#!/usr/bin/env python3
"""
测试CVEDataCollector类的API请求
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(os.path.join(current_dir, 'src'))
sys.path.append(os.path.join(project_root, 'src', 'data_collection'))

def test_collector():
    """测试CVEDataCollector类"""
    
    # 导入CVEDataCollector
    from src.main_collector import CVEDataCollector
    
    print("创建CVEDataCollector实例...")
    collector = CVEDataCollector()
    
    print("测试单个批次的CVE数据获取...")
    start_date = '2022-01-01T00:00:00.000'
    end_date = '2022-04-30T23:59:59.999'
    
    print(f"获取批次数据: {start_date} 到 {end_date}")
    
    try:
        cves = collector.get_nvd_cves_by_date_range(start_date, end_date)
        print(f"✅ 成功获取 {len(cves)} 个CVE")
        
        if cves:
            first_cve = cves[0]
            cve_id = first_cve.get('cve', {}).get('id', 'Unknown')
            print(f"第一个CVE ID: {cve_id}")
        
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_collector()

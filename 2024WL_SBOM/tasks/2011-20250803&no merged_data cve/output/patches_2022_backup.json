[{"unified_id": "CVE-2021-45930_qt_qtsvg_qt_qtsvg_a3b753c2", "cve_id": "CVE-2021-45930", "patch_type": "cross_branch", "projects": {"source": {"name": "qt/qtsvg", "repo": "https://github.com/qt/qtsvg", "language": "C"}, "target": {"name": "qt/qtsvg", "repo": "https://github.com/qt/qtsvg", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "36cfd9efb9b22b891adee9c48d30202289cfa620"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a3b753c2d077313fc9eb93af547051b956e383fc"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-45930", "year": 2021, "cwe_types": ["CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-10-25T12:17:55Z", "target_commit_date": "2021-10-25T12:17:55Z", "source_files_changed": ["src/svg/qsvghandler.cpp"], "target_files_changed": ["src/svg/qsvghandler.cpp"], "source_commit_url": "https://github.com/qt/qtsvg/commit/36cfd9efb9b22b891adee9c48d30202289cfa620", "target_commit_url": "https://github.com/qt/qtsvg/commit/a3b753c2d077313fc9eb93af547051b956e383fc", "backporting_type": "cross_branch", "source_repo": "https://github.com/qt/qtsvg", "target_repo": "https://github.com/qt/qtsvg", "backporting_analysis": {"source_repo": "https://github.com/qt/qtsvg", "target_repo": "https://github.com/qt/qtsvg", "source_commit": "36cfd9efb9b22b891adee9c48d30202289cfa620", "target_commit": "a3b753c2d077313fc9eb93af547051b956e383fc", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-45942_AcademySoftwareFoundation_openexr_AcademySoftwareFoundation_openexr_11cad77d", "cve_id": "CVE-2021-45942", "patch_type": "cross_branch", "projects": {"source": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}, "target": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "db217f29dfb24f6b4b5100c24ac5e7490e1c57d0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "11cad77da87c4fa2aab7d58dd5339e254db7937e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-45942", "year": 2021, "cwe_types": ["CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-11-18T20:00:21Z", "target_commit_date": "2021-12-02T21:06:35Z", "source_files_changed": ["src/lib/OpenEXR/ImfDeepScanLineInputFile.cpp"], "target_files_changed": ["src/lib/OpenEXR/ImfCompositeDeepScanLine.cpp"], "source_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/db217f29dfb24f6b4b5100c24ac5e7490e1c57d0", "target_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/11cad77da87c4fa2aab7d58dd5339e254db7937e", "backporting_type": "cross_branch", "source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "backporting_analysis": {"source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "source_commit": "db217f29dfb24f6b4b5100c24ac5e7490e1c57d0", "target_commit": "11cad77da87c4fa2aab7d58dd5339e254db7937e", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-45943_OSGeo_gdal_OSGeo_gdal_1ca6a3e5", "cve_id": "CVE-2021-45943", "patch_type": "cross_branch", "projects": {"source": {"name": "OSGeo/gdal", "repo": "https://github.com/OSGeo/gdal", "language": "C"}, "target": {"name": "OSGeo/gdal", "repo": "https://github.com/OSGeo/gdal", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "93913a849dc1d217a40dbf9d6e6a3a23c42b61a6"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1ca6a3e5168c200763fa46d8aa7e698d0b757e7e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-45943", "year": 2021, "cwe_types": ["CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-12-04T19:52:42Z", "target_commit_date": "2021-12-04T23:26:39Z", "source_files_changed": [], "target_files_changed": ["frmts/pcidsk/sdk/segment/cpcidskbinarysegment.cpp"], "source_commit_url": "https://github.com/OSGeo/gdal/commit/93913a849dc1d217a40dbf9d6e6a3a23c42b61a6", "target_commit_url": "https://github.com/OSGeo/gdal/commit/1ca6a3e5168c200763fa46d8aa7e698d0b757e7e", "backporting_type": "cross_branch", "source_repo": "https://github.com/OSGeo/gdal", "target_repo": "https://github.com/OSGeo/gdal", "backporting_analysis": {"source_repo": "https://github.com/OSGeo/gdal", "target_repo": "https://github.com/OSGeo/gdal", "source_commit": "93913a849dc1d217a40dbf9d6e6a3a23c42b61a6", "target_commit": "1ca6a3e5168c200763fa46d8aa7e698d0b757e7e", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-43860_flatpak_flatpak_flatpak_flatpak_65cbfac9", "cve_id": "CVE-2021-43860", "patch_type": "cross_branch", "projects": {"source": {"name": "flatpak/flatpak", "repo": "https://github.com/flatpak/flatpak", "language": "C"}, "target": {"name": "flatpak/flatpak", "repo": "https://github.com/flatpak/flatpak", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ba818f504c926baaf6e362be8159cfacf994310e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "65cbfac982cb1c83993a9e19aa424daee8e9f042"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-43860", "year": 2021, "cwe_types": ["CWE-269", "CWE-276"], "bug_type": "CWE-269", "source_commit_date": "2021-12-24T00:30:17Z", "target_commit_date": "2022-01-12T10:00:56Z", "source_files_changed": ["common/flatpak-dir.c", "common/flatpak-transaction.c", "common/flatpak-utils.c"], "target_files_changed": ["common/flatpak-dir.c"], "source_commit_url": "https://github.com/flatpak/flatpak/commit/ba818f504c926baaf6e362be8159cfacf994310e", "target_commit_url": "https://github.com/flatpak/flatpak/commit/65cbfac982cb1c83993a9e19aa424daee8e9f042", "backporting_type": "cross_branch", "source_repo": "https://github.com/flatpak/flatpak", "target_repo": "https://github.com/flatpak/flatpak", "backporting_analysis": {"source_repo": "https://github.com/flatpak/flatpak", "target_repo": "https://github.com/flatpak/flatpak", "source_commit": "ba818f504c926baaf6e362be8159cfacf994310e", "target_commit": "65cbfac982cb1c83993a9e19aa424daee8e9f042", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23567_tensorflow_tensorflow_tensorflow_tensorflow_e952a89b", "cve_id": "CVE-2022-23567", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1b54cadd19391b60b6fcccd8d076426f7221d5e8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e952a89b7026b98fe8cbe626514a93ed68b7c510"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23567", "year": 2022, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2021-12-10T17:46:39Z", "target_commit_date": "2021-12-10T17:46:48Z", "source_files_changed": ["tensorflow/core/kernels/sparse_dense_binary_op_shared.cc"], "target_files_changed": ["tensorflow/core/kernels/sparse_dense_binary_op_shared.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/1b54cadd19391b60b6fcccd8d076426f7221d5e8", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/e952a89b7026b98fe8cbe626514a93ed68b7c510", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "1b54cadd19391b60b6fcccd8d076426f7221d5e8", "target_commit": "e952a89b7026b98fe8cbe626514a93ed68b7c510", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23568_tensorflow_tensorflow_tensorflow_tensorflow_a68f6806", "cve_id": "CVE-2022-23568", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b51b82fe65ebace4475e3c54eb089c18a4403f1c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a68f68061e263a88321c104a6c911fe5598050a8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23568", "year": 2022, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2021-12-09T22:32:48Z", "target_commit_date": "2021-12-10T00:17:26Z", "source_files_changed": ["tensorflow/core/kernels/sparse_tensors_map_ops.cc"], "target_files_changed": ["tensorflow/core/kernels/sparse_tensors_map_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/b51b82fe65ebace4475e3c54eb089c18a4403f1c", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a68f68061e263a88321c104a6c911fe5598050a8", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "b51b82fe65ebace4475e3c54eb089c18a4403f1c", "target_commit": "a68f68061e263a88321c104a6c911fe5598050a8", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-21740_tensorflow_tensorflow_tensorflow_tensorflow_adbbabdb", "cve_id": "CVE-2022-21740", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2b7100d6cdff36aa21010a82269bc05a6d1cc74a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "adbbabdb0d3abb3cdeac69e38a96de1d678b24b3"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-21740", "year": 2022, "cwe_types": ["CWE-787", "CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-12-08T03:36:18Z", "target_commit_date": "2021-12-08T03:44:33Z", "source_files_changed": ["tensorflow/core/kernels/count_ops.cc"], "target_files_changed": ["tensorflow/core/kernels/count_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/2b7100d6cdff36aa21010a82269bc05a6d1cc74a", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/adbbabdb0d3abb3cdeac69e38a96de1d678b24b3", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "2b7100d6cdff36aa21010a82269bc05a6d1cc74a", "target_commit": "adbbabdb0d3abb3cdeac69e38a96de1d678b24b3", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23559_tensorflow_tensorflow_tensorflow_tensorflow_a4e401da", "cve_id": "CVE-2022-23559", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f19be71717c497723ba0cea0379e84f061a75e01"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a4e401da71458d253b05e41f28637b65baf64be4"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23559", "year": 2022, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2021-12-16T22:32:07Z", "target_commit_date": "2021-12-21T21:06:51Z", "source_files_changed": ["tensorflow/lite/core/subgraph.cc", "tensorflow/lite/util.cc", "tensorflow/lite/util.h", "tensorflow/lite/util_test.cc"], "target_files_changed": ["tensorflow/lite/kernels/embedding_lookup_sparse.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/f19be71717c497723ba0cea0379e84f061a75e01", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a4e401da71458d253b05e41f28637b65baf64be4", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "f19be71717c497723ba0cea0379e84f061a75e01", "target_commit": "a4e401da71458d253b05e41f28637b65baf64be4", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23562_tensorflow_tensorflow_tensorflow_tensorflow_f0147751", "cve_id": "CVE-2022-23562", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f0147751fd5d2ff23251149ebad9af9f03010732"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23562", "year": 2022, "cwe_types": ["CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2021-08-28T17:15:23Z", "target_commit_date": "2021-12-17T03:26:39Z", "source_files_changed": [], "target_files_changed": ["tensorflow/core/kernels/sequence_ops.cc", "tensorflow/core/ops/math_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/f0147751fd5d2ff23251149ebad9af9f03010732", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3", "target_commit": "f0147751fd5d2ff23251149ebad9af9f03010732", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23581_tensorflow_tensorflow_tensorflow_tensorflow_24065551", "cve_id": "CVE-2022-23581", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ebc1a2ffe5a7573d905e99bd0ee3568ee07c12c1"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "240655511cd3e701155f944a972db71b6c0b1bb6"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23581", "year": 2022, "cwe_types": ["CWE-617"], "bug_type": "CWE-617", "source_commit_date": "2021-11-11T16:58:30Z", "target_commit_date": "2021-11-11T17:24:31Z", "source_files_changed": ["tensorflow/core/grappler/optimizers/constant_folding.cc", "tensorflow/core/grappler/optimizers/constant_folding.h"], "target_files_changed": ["tensorflow/core/grappler/optimizers/constant_folding.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/ebc1a2ffe5a7573d905e99bd0ee3568ee07c12c1", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/240655511cd3e701155f944a972db71b6c0b1bb6", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "ebc1a2ffe5a7573d905e99bd0ee3568ee07c12c1", "target_commit": "240655511cd3e701155f944a972db71b6c0b1bb6", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23586_tensorflow_tensorflow_tensorflow_tensorflow_dcc21c7b", "cve_id": "CVE-2022-23586", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3d89911481ba6ebe8c88c1c0b595412121e6c645"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "dcc21c7bc972b10b6fb95c2fb0f4ab5a59680ec2"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23586", "year": 2022, "cwe_types": ["CWE-617"], "bug_type": "CWE-617", "source_commit_date": "2021-11-12T16:12:05Z", "target_commit_date": "2021-11-12T16:19:38Z", "source_files_changed": ["tensorflow/core/framework/function.cc"], "target_files_changed": ["tensorflow/core/framework/function.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/3d89911481ba6ebe8c88c1c0b595412121e6c645", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/dcc21c7bc972b10b6fb95c2fb0f4ab5a59680ec2", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "3d89911481ba6ebe8c88c1c0b595412121e6c645", "target_commit": "dcc21c7bc972b10b6fb95c2fb0f4ab5a59680ec2", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23589_tensorflow_tensorflow_tensorflow_tensorflow_045deec1", "cve_id": "CVE-2022-23589", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "0a365c029e437be0349c31f8d4c9926b69fa3fa1"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "045deec1cbdebb27d817008ad5df94d96a08b1bf"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23589", "year": 2022, "cwe_types": ["CWE-476"], "bug_type": "NULL pointer dereference", "source_commit_date": "2021-11-13T18:05:59Z", "target_commit_date": "2021-11-13T18:12:22Z", "source_files_changed": ["tensorflow/core/grappler/optimizers/constant_folding.cc"], "target_files_changed": ["tensorflow/core/grappler/mutable_graph_view.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/0a365c029e437be0349c31f8d4c9926b69fa3fa1", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/045deec1cbdebb27d817008ad5df94d96a08b1bf", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "0a365c029e437be0349c31f8d4c9926b69fa3fa1", "target_commit": "045deec1cbdebb27d817008ad5df94d96a08b1bf", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-46389_ruven_iipsrv_ruven_iipsrv_882925b2", "cve_id": "CVE-2021-46389", "patch_type": "cross_branch", "projects": {"source": {"name": "ruven/iipsrv", "repo": "https://github.com/ruven/iipsrv", "language": "C"}, "target": {"name": "ruven/iipsrv", "repo": "https://github.com/ruven/iipsrv", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4ed59265fbbd636dc2fbbf325f8ea37ed300a6d9"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "882925b295a80ec992063deffc2a3b0d803c3195"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-46389", "year": 2021, "cwe_types": ["CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2022-01-14T22:22:09Z", "target_commit_date": "2022-01-16T13:34:07Z", "source_files_changed": ["src/JTL.cc", "src/SPECTRA.cc"], "target_files_changed": ["src/KakaduImage.cc", "src/OpenJPEGImage.cc", "src/RawTile.h", "src/TileManager.cc", "src/Transforms.cc"], "source_commit_url": "https://github.com/ruven/iipsrv/commit/4ed59265fbbd636dc2fbbf325f8ea37ed300a6d9", "target_commit_url": "https://github.com/ruven/iipsrv/commit/882925b295a80ec992063deffc2a3b0d803c3195", "backporting_type": "cross_branch", "source_repo": "https://github.com/ruven/iipsrv", "target_repo": "https://github.com/ruven/iipsrv", "backporting_analysis": {"source_repo": "https://github.com/ruven/iipsrv", "target_repo": "https://github.com/ruven/iipsrv", "source_commit": "4ed59265fbbd636dc2fbbf325f8ea37ed300a6d9", "target_commit": "882925b295a80ec992063deffc2a3b0d803c3195", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-25939_arangodb_arangodb_arangodb_arangodb_d7b35a68", "cve_id": "CVE-2021-25939", "patch_type": "cross_branch", "projects": {"source": {"name": "arangodb/arangodb", "repo": "https://github.com/arangodb/arangodb", "language": "C"}, "target": {"name": "arangodb/arangodb", "repo": "https://github.com/arangodb/arangodb", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d9b7f019d2435f107b19a59190bf9cc27d5f34dd"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d7b35a6884c6b2802d34d79fb2a79fb2c9ec2175"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-25939", "year": 2021, "cwe_types": ["CWE-918", "CWE-918"], "bug_type": "CWE-918", "source_commit_date": "2021-12-14T10:24:01Z", "target_commit_date": "2021-12-30T03:34:00Z", "source_files_changed": ["arangod/GeneralServer/ServerSecurityFeature.cpp", "arangod/GeneralServer/ServerSecurityFeature.h", "arangod/V8Server/v8-actions.cpp"], "target_files_changed": ["arangod/GeneralServer/ServerSecurityFeature.cpp", "arangod/GeneralServer/ServerSecurityFeature.h", "arangod/V8Server/v8-actions.cpp"], "source_commit_url": "https://github.com/arangodb/arangodb/commit/d9b7f019d2435f107b19a59190bf9cc27d5f34dd", "target_commit_url": "https://github.com/arangodb/arangodb/commit/d7b35a6884c6b2802d34d79fb2a79fb2c9ec2175", "backporting_type": "cross_branch", "source_repo": "https://github.com/arangodb/arangodb", "target_repo": "https://github.com/arangodb/arangodb", "backporting_analysis": {"source_repo": "https://github.com/arangodb/arangodb", "target_repo": "https://github.com/arangodb/arangodb", "source_commit": "d9b7f019d2435f107b19a59190bf9cc27d5f34dd", "target_commit": "d7b35a6884c6b2802d34d79fb2a79fb2c9ec2175", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-25297_drogonframework_drogon_drogonframework_drogon_3c785326", "cve_id": "CVE-2022-25297", "patch_type": "cross_branch", "projects": {"source": {"name": "drogonframework/drogon", "repo": "https://github.com/drogonframework/drogon", "language": "C"}, "target": {"name": "drogonframework/drogon", "repo": "https://github.com/drogonframework/drogon", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "49f61448f0463fce68032c53e03bf6f85b7f3655"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3c785326c63a34aa1799a639ae185bc9453cb447"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-25297", "year": 2022, "cwe_types": ["CWE-552"], "bug_type": "CWE-552", "source_commit_date": "2022-02-08T16:13:35Z", "target_commit_date": "2022-02-11T15:54:42Z", "source_files_changed": [], "target_files_changed": ["lib/src/HttpFileImpl.cc", "lib/tests/unittests/HttpFileTest.cc"], "source_commit_url": "https://github.com/drogonframework/drogon/commit/49f61448f0463fce68032c53e03bf6f85b7f3655", "target_commit_url": "https://github.com/drogonframework/drogon/commit/3c785326c63a34aa1799a639ae185bc9453cb447", "backporting_type": "cross_branch", "source_repo": "https://github.com/drogonframework/drogon", "target_repo": "https://github.com/drogonframework/drogon", "backporting_analysis": {"source_repo": "https://github.com/drogonframework/drogon", "target_repo": "https://github.com/drogonframework/drogon", "source_commit": "49f61448f0463fce68032c53e03bf6f85b7f3655", "target_commit": "3c785326c63a34aa1799a639ae185bc9453cb447", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-25643_kenny<PERSON><PERSON>en_seatd_kenny<PERSON><PERSON>en_seatd_7cffe079", "cve_id": "CVE-2022-25643", "patch_type": "cross_branch", "projects": {"source": {"name": "kennylevinsen/seatd", "repo": "https://github.com/kennylevinsen/seatd", "language": "C"}, "target": {"name": "kennylevinsen/seatd", "repo": "https://github.com/kennylevinsen/seatd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "10658dc5439db429af0088295a051c53925a4416"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7cffe0797fdb17a9c08922339465b1b187394335"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-25643", "year": 2022, "cwe_types": ["CWE-668"], "bug_type": "CWE-668", "source_commit_date": "2022-02-21T10:46:48Z", "target_commit_date": "2022-02-21T10:46:48Z", "source_files_changed": ["seatd-launch/seatd-launch.c"], "target_files_changed": ["seatd-launch/seatd-launch.c"], "source_commit_url": "https://github.com/kennylevinsen/seatd/commit/10658dc5439db429af0088295a051c53925a4416", "target_commit_url": "https://github.com/kennylevinsen/seatd/commit/7cffe0797fdb17a9c08922339465b1b187394335", "backporting_type": "cross_branch", "source_repo": "https://github.com/kennylevinsen/seatd", "target_repo": "https://github.com/kennylevinsen/seatd", "backporting_analysis": {"source_repo": "https://github.com/kennylevinsen/seatd", "target_repo": "https://github.com/kennylevinsen/seatd", "source_commit": "10658dc5439db429af0088295a051c53925a4416", "target_commit": "7cffe0797fdb17a9c08922339465b1b187394335", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-3623_ste<PERSON><PERSON>_libtpms_stefan<PERSON>_libtpms_2e6173c", "cve_id": "CVE-2021-3623", "patch_type": "cross_branch", "projects": {"source": {"name": "ste<PERSON><PERSON>/libtpms", "repo": "https://github.com/stefanberger/libtpms", "language": "C"}, "target": {"name": "ste<PERSON><PERSON>/libtpms", "repo": "https://github.com/stefanberger/libtpms", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2f30d62"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2e6173c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-3623", "year": 2021, "cwe_types": ["CWE-787", "CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-06-21T18:04:34Z", "target_commit_date": "2021-06-24T00:54:44Z", "source_files_changed": ["src/tpm2/NVMarshal.c", "src/tpm2/Unmarshal.c"], "target_files_changed": ["src/tpm2/Unmarshal.c"], "source_commit_url": "https://github.com/stefanberger/libtpms/commit/2f30d62", "target_commit_url": "https://github.com/stefanberger/libtpms/commit/2e6173c", "backporting_type": "cross_branch", "source_repo": "https://github.com/stefanberger/libtpms", "target_repo": "https://github.com/stefanberger/libtpms", "backporting_analysis": {"source_repo": "https://github.com/stefanberger/libtpms", "target_repo": "https://github.com/stefanberger/libtpms", "source_commit": "2f30d62", "target_commit": "2e6173c", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-4076_latchset_tang_latchset_tang_e82459fd", "cve_id": "CVE-2021-4076", "patch_type": "cross_branch", "projects": {"source": {"name": "latchset/tang", "repo": "https://github.com/latchset/tang", "language": "C"}, "target": {"name": "latchset/tang", "repo": "https://github.com/latchset/tang", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3503270a0b85e16ccd4a6f078a65d62d192df127"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e82459fda10f0630c3414ed2afbc6320bb9ea7c9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-4076", "year": 2021, "cwe_types": ["CWE-200"], "bug_type": "information leakage", "source_commit_date": "2021-12-07T10:26:27Z", "target_commit_date": "2021-12-14T11:18:41Z", "source_files_changed": [], "target_files_changed": ["src/keys.c"], "source_commit_url": "https://github.com/latchset/tang/commit/3503270a0b85e16ccd4a6f078a65d62d192df127", "target_commit_url": "https://github.com/latchset/tang/commit/e82459fda10f0630c3414ed2afbc6320bb9ea7c9", "backporting_type": "cross_branch", "source_repo": "https://github.com/latchset/tang", "target_repo": "https://github.com/latchset/tang", "backporting_analysis": {"source_repo": "https://github.com/latchset/tang", "target_repo": "https://github.com/latchset/tang", "source_commit": "3503270a0b85e16ccd4a6f078a65d62d192df127", "target_commit": "e82459fda10f0630c3414ed2afbc6320bb9ea7c9", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-3737_python_cpython_python_cpython_61720e83", "cve_id": "CVE-2021-3737", "patch_type": "cross_branch", "projects": {"source": {"name": "python/cpython", "repo": "https://github.com/python/cpython", "language": "C"}, "target": {"name": "python/cpython", "repo": "https://github.com/python/cpython", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "859cd6e609d9f9275f9e5437ff8ce21c3490e3dc"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "61720e83a1f1e76620a618981ec1cbc6524b32fb"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-3737", "year": 2021, "cwe_types": ["CWE-835", "CWE-400", "CWE-835"], "bug_type": "CWE-835", "source_commit_date": "2021-05-05T21:55:37Z", "target_commit_date": "2021-06-03T03:09:41Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/python/cpython/commit/859cd6e609d9f9275f9e5437ff8ce21c3490e3dc", "target_commit_url": "https://github.com/python/cpython/commit/61720e83a1f1e76620a618981ec1cbc6524b32fb", "backporting_type": "cross_branch", "source_repo": "https://github.com/python/cpython", "target_repo": "https://github.com/python/cpython", "backporting_analysis": {"source_repo": "https://github.com/python/cpython", "target_repo": "https://github.com/python/cpython", "source_commit": "859cd6e609d9f9275f9e5437ff8ce21c3490e3dc", "target_commit": "61720e83a1f1e76620a618981ec1cbc6524b32fb", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-24755_bareos_bareos_bareos_bareos_632b32d3", "cve_id": "CVE-2022-24755", "patch_type": "cross_branch", "projects": {"source": {"name": "bareos/bareos", "repo": "https://github.com/bareos/bareos", "language": "C"}, "target": {"name": "bareos/bareos", "repo": "https://github.com/bareos/bareos", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ad64a0714017ec27a044bedc706340fce58a1d37"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "632b32d32060a229d40562d9b363e04187c71d8c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-24755", "year": 2022, "cwe_types": ["CWE-863"], "bug_type": "CWE-863", "source_commit_date": "2022-03-10T15:49:24Z", "target_commit_date": "2022-03-11T14:32:11Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/bareos/bareos/commit/ad64a0714017ec27a044bedc706340fce58a1d37", "target_commit_url": "https://github.com/bareos/bareos/commit/632b32d32060a229d40562d9b363e04187c71d8c", "backporting_type": "cross_branch", "source_repo": "https://github.com/bareos/bareos", "target_repo": "https://github.com/bareos/bareos", "backporting_analysis": {"source_repo": "https://github.com/bareos/bareos", "target_repo": "https://github.com/bareos/bareos", "source_commit": "ad64a0714017ec27a044bedc706340fce58a1d37", "target_commit": "632b32d32060a229d40562d9b363e04187c71d8c", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-24756_bareos_bareos_bareos_bareos_632b32d3", "cve_id": "CVE-2022-24756", "patch_type": "cross_branch", "projects": {"source": {"name": "bareos/bareos", "repo": "https://github.com/bareos/bareos", "language": "C"}, "target": {"name": "bareos/bareos", "repo": "https://github.com/bareos/bareos", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ad64a0714017ec27a044bedc706340fce58a1d37"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "632b32d32060a229d40562d9b363e04187c71d8c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-24756", "year": 2022, "cwe_types": ["CWE-401"], "bug_type": "CWE-401", "source_commit_date": "2022-03-10T15:49:24Z", "target_commit_date": "2022-03-11T14:32:11Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/bareos/bareos/commit/ad64a0714017ec27a044bedc706340fce58a1d37", "target_commit_url": "https://github.com/bareos/bareos/commit/632b32d32060a229d40562d9b363e04187c71d8c", "backporting_type": "cross_branch", "source_repo": "https://github.com/bareos/bareos", "target_repo": "https://github.com/bareos/bareos", "backporting_analysis": {"source_repo": "https://github.com/bareos/bareos", "target_repo": "https://github.com/bareos/bareos", "source_commit": "ad64a0714017ec27a044bedc706340fce58a1d37", "target_commit": "632b32d32060a229d40562d9b363e04187c71d8c", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-21718_electron_electron_electron_electron_cebc2046", "cve_id": "CVE-2022-21718", "patch_type": "cross_branch", "projects": {"source": {"name": "electron/electron", "repo": "https://github.com/electron/electron", "language": "C"}, "target": {"name": "electron/electron", "repo": "https://github.com/electron/electron", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5f162614c074204db497a022fe9b799c6a00c6a5"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cebc20466edfb4e0af0bb2c7cc24d4a29f8e48a6"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-21718", "year": 2022, "cwe_types": ["CWE-668", "CWE-862"], "bug_type": "CWE-668", "source_commit_date": "2021-12-14T23:33:02Z", "target_commit_date": "2021-12-17T21:38:15Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/electron/electron/commit/5f162614c074204db497a022fe9b799c6a00c6a5", "target_commit_url": "https://github.com/electron/electron/commit/cebc20466edfb4e0af0bb2c7cc24d4a29f8e48a6", "backporting_type": "cross_branch", "source_repo": "https://github.com/electron/electron", "target_repo": "https://github.com/electron/electron", "backporting_analysis": {"source_repo": "https://github.com/electron/electron", "target_repo": "https://github.com/electron/electron", "source_commit": "5f162614c074204db497a022fe9b799c6a00c6a5", "target_commit": "cebc20466edfb4e0af0bb2c7cc24d4a29f8e48a6", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-26530_swaywm_swaylock_swaywm_swaylock_58a15cf0", "cve_id": "CVE-2022-26530", "patch_type": "cross_branch", "projects": {"source": {"name": "swaywm/swaylock", "repo": "https://github.com/swaywm/swaylock", "language": "C"}, "target": {"name": "swaywm/swaylock", "repo": "https://github.com/swaywm/swaylock", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1d1c75b6316d21933069a9d201f966d84099f6ca"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "58a15cf0cd070ccd4b479ff515012886f7dbd54a"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-26530", "year": 2022, "cwe_types": [], "bug_type": "unknown", "source_commit_date": "2021-12-20T16:25:44Z", "target_commit_date": "2021-12-20T16:25:44Z", "source_files_changed": ["include/swaylock.h", "main.c"], "target_files_changed": [], "source_commit_url": "https://github.com/swaywm/swaylock/commit/1d1c75b6316d21933069a9d201f966d84099f6ca", "target_commit_url": "https://github.com/swaywm/swaylock/commit/58a15cf0cd070ccd4b479ff515012886f7dbd54a", "backporting_type": "cross_branch", "source_repo": "https://github.com/swaywm/swaylock", "target_repo": "https://github.com/swaywm/swaylock", "backporting_analysis": {"source_repo": "https://github.com/swaywm/swaylock", "target_repo": "https://github.com/swaywm/swaylock", "source_commit": "1d1c75b6316d21933069a9d201f966d84099f6ca", "target_commit": "58a15cf0cd070ccd4b479ff515012886f7dbd54a", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-42781_OpenSC_OpenSC_OpenSC_OpenSC_cae5c71f", "cve_id": "CVE-2021-42781", "patch_type": "cross_branch", "projects": {"source": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}, "target": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "17d8980c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cae5c71f"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-42781", "year": 2021, "cwe_types": ["CWE-119", "CWE-787"], "bug_type": "memory overflow", "source_commit_date": "2021-02-03T20:46:15Z", "target_commit_date": "2021-04-06T10:45:24Z", "source_files_changed": ["src/libopensc/pkcs15-oberthur.c"], "target_files_changed": ["src/libopensc/pkcs15-oberthur.c"], "source_commit_url": "https://github.com/OpenSC/OpenSC/commit/17d8980c", "target_commit_url": "https://github.com/OpenSC/OpenSC/commit/cae5c71f", "backporting_type": "cross_branch", "source_repo": "https://github.com/OpenSC/OpenSC", "target_repo": "https://github.com/OpenSC/OpenSC", "backporting_analysis": {"source_repo": "https://github.com/OpenSC/OpenSC", "target_repo": "https://github.com/OpenSC/OpenSC", "source_commit": "17d8980c", "target_commit": "cae5c71f", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-42782_OpenSC_OpenSC_OpenSC_OpenSC_456ac566", "cve_id": "CVE-2021-42782", "patch_type": "cross_version", "projects": {"source": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}, "target": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "78cdab94"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "456ac566"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-42782", "year": 2021, "cwe_types": ["CWE-119", "CWE-787"], "bug_type": "memory overflow", "source_commit_date": "2020-11-30T16:43:03Z", "target_commit_date": "2021-07-14T16:15:10Z", "source_files_changed": ["src/libopensc/pkcs15-tcos.c"], "target_files_changed": ["src/libopensc/card-piv.c"], "source_commit_url": "https://github.com/OpenSC/OpenSC/commit/78cdab94", "target_commit_url": "https://github.com/OpenSC/OpenSC/commit/456ac566", "backporting_type": "cross_version", "source_repo": "https://github.com/OpenSC/OpenSC", "target_repo": "https://github.com/OpenSC/OpenSC", "backporting_analysis": {"source_repo": "https://github.com/OpenSC/OpenSC", "target_repo": "https://github.com/OpenSC/OpenSC", "source_commit": "78cdab94", "target_commit": "456ac566", "analysis_result": "cross_version"}}}, {"unified_id": "CVE-2022-24883_FreeRDP_FreeRDP_FreeRDP_FreeRDP_6f473b27", "cve_id": "CVE-2022-24883", "patch_type": "cross_branch", "projects": {"source": {"name": "FreeRDP/FreeRDP", "repo": "https://github.com/FreeRDP/FreeRDP", "language": "C"}, "target": {"name": "FreeRDP/FreeRDP", "repo": "https://github.com/FreeRDP/FreeRDP", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4661492e5a617199457c8074bad22f766a116cdc"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6f473b273a4b6f0cb6aca32b95e22fd0de88e144"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-24883", "year": 2022, "cwe_types": ["CWE-287", "CWE-287"], "bug_type": "CWE-287", "source_commit_date": "2022-04-22T12:42:11Z", "target_commit_date": "2022-04-22T12:42:11Z", "source_files_changed": ["winpr/libwinpr/sspi/NTLM/ntlm_compute.c"], "target_files_changed": ["winpr/libwinpr/sspi/NTLM/ntlm_compute.c"], "source_commit_url": "https://github.com/FreeRDP/FreeRDP/commit/4661492e5a617199457c8074bad22f766a116cdc", "target_commit_url": "https://github.com/FreeRDP/FreeRDP/commit/6f473b273a4b6f0cb6aca32b95e22fd0de88e144", "backporting_type": "cross_branch", "source_repo": "https://github.com/FreeRDP/FreeRDP", "target_repo": "https://github.com/FreeRDP/FreeRDP", "backporting_analysis": {"source_repo": "https://github.com/FreeRDP/FreeRDP", "target_repo": "https://github.com/FreeRDP/FreeRDP", "source_commit": "4661492e5a617199457c8074bad22f766a116cdc", "target_commit": "6f473b273a4b6f0cb6aca32b95e22fd0de88e144", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29869_piastry_cifs-utils_piastry_cifs-utils_3d67d09b", "cve_id": "CVE-2022-29869", "patch_type": "cross_branch", "projects": {"source": {"name": "piastry/cifs-utils", "repo": "https://github.com/piastry/cifs-utils", "language": "C"}, "target": {"name": "piastry/cifs-utils", "repo": "https://github.com/piastry/cifs-utils", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "8acc963a2e7e9d63fe1f2e7f73f5a03f83d9c379"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3d67d09b4a73c85ac34c739a6936c456e80be038"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29869", "year": 2022, "cwe_types": ["CWE-532"], "bug_type": "CWE-532", "source_commit_date": "2022-03-19T17:41:15Z", "target_commit_date": "2022-03-19T17:41:15Z", "source_files_changed": ["mount.cifs.c"], "target_files_changed": [], "source_commit_url": "https://github.com/piastry/cifs-utils/commit/8acc963a2e7e9d63fe1f2e7f73f5a03f83d9c379", "target_commit_url": "https://github.com/piastry/cifs-utils/commit/3d67d09b4a73c85ac34c739a6936c456e80be038", "backporting_type": "cross_branch", "source_repo": "https://github.com/piastry/cifs-utils", "target_repo": "https://github.com/piastry/cifs-utils", "backporting_analysis": {"source_repo": "https://github.com/piastry/cifs-utils", "target_repo": "https://github.com/piastry/cifs-utils", "source_commit": "8acc963a2e7e9d63fe1f2e7f73f5a03f83d9c379", "target_commit": "3d67d09b4a73c85ac34c739a6936c456e80be038", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-21144_libxmljs_libxmljs_libxmljs_libxmljs_2501807b", "cve_id": "CVE-2022-21144", "patch_type": "cross_branch", "projects": {"source": {"name": "libxmljs/libxmljs", "repo": "https://github.com/libxmljs/libxmljs", "language": "C"}, "target": {"name": "libxmljs/libxmljs", "repo": "https://github.com/libxmljs/libxmljs", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "99ff82fbb046935ef4b9d3cd1d53161c1ca9ee19"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2501807bde9b38cfaed06d1e140487516d91379d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-21144", "year": 2022, "cwe_types": ["CWE-20"], "bug_type": "CWE-20", "source_commit_date": "2022-02-16T19:12:24Z", "target_commit_date": "2022-03-30T15:48:41Z", "source_files_changed": [], "target_files_changed": ["src/xml_document.cc"], "source_commit_url": "https://github.com/libxmljs/libxmljs/commit/99ff82fbb046935ef4b9d3cd1d53161c1ca9ee19", "target_commit_url": "https://github.com/libxmljs/libxmljs/commit/2501807bde9b38cfaed06d1e140487516d91379d", "backporting_type": "cross_branch", "source_repo": "https://github.com/libxmljs/libxmljs", "target_repo": "https://github.com/libxmljs/libxmljs", "backporting_analysis": {"source_repo": "https://github.com/libxmljs/libxmljs", "target_repo": "https://github.com/libxmljs/libxmljs", "source_commit": "99ff82fbb046935ef4b9d3cd1d53161c1ca9ee19", "target_commit": "2501807bde9b38cfaed06d1e140487516d91379d", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-24884_freifunk-gluon_ecdsautils_freifunk-gluon_ecdsautils_39b6d0a7", "cve_id": "CVE-2022-24884", "patch_type": "cross_branch", "projects": {"source": {"name": "freifunk-gluon/ecdsautils", "repo": "https://github.com/freifunk-gluon/ecdsautils", "language": "C"}, "target": {"name": "freifunk-gluon/ecdsautils", "repo": "https://github.com/freifunk-gluon/ecdsautils", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1d4b091abdf15ad7b2312535b5b95ad70f6dbd08"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "39b6d0a77414fd41614953a0e185c4eefa2f88ad"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-24884", "year": 2022, "cwe_types": ["CWE-347", "CWE-347"], "bug_type": "CWE-347", "source_commit_date": "2022-04-20T20:04:07Z", "target_commit_date": "2022-05-05T16:01:14Z", "source_files_changed": ["src/lib/ecdsa.c"], "target_files_changed": ["src/lib/ecdsa.c"], "source_commit_url": "https://github.com/freifunk-gluon/ecdsautils/commit/1d4b091abdf15ad7b2312535b5b95ad70f6dbd08", "target_commit_url": "https://github.com/freifunk-gluon/ecdsautils/commit/39b6d0a77414fd41614953a0e185c4eefa2f88ad", "backporting_type": "cross_branch", "source_repo": "https://github.com/freifunk-gluon/ecdsautils", "target_repo": "https://github.com/freifunk-gluon/ecdsautils", "backporting_analysis": {"source_repo": "https://github.com/freifunk-gluon/ecdsautils", "target_repo": "https://github.com/freifunk-gluon/ecdsautils", "source_commit": "1d4b091abdf15ad7b2312535b5b95ad70f6dbd08", "target_commit": "39b6d0a77414fd41614953a0e185c4eefa2f88ad", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-28463_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_e6ea5876", "cve_id": "CVE-2022-28463", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ca3654ebf7a439dc736f56f083c9aa98e4464b7f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e6ea5876e0228165ee3abc6e959aa174cee06680"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-28463", "year": 2022, "cwe_types": ["CWE-120"], "bug_type": "CWE-120", "source_commit_date": "2022-03-26T13:26:57Z", "target_commit_date": "2022-03-26T13:27:36Z", "source_files_changed": ["coders/cin.c"], "target_files_changed": ["coders/cin.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/ca3654ebf7a439dc736f56f083c9aa98e4464b7f", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/e6ea5876e0228165ee3abc6e959aa174cee06680", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "ca3654ebf7a439dc736f56f083c9aa98e4464b7f", "target_commit": "e6ea5876e0228165ee3abc6e959aa174cee06680", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-1586_PCRE2Project_pcre2_PCRE2Project_pcre2_d4fa336f", "cve_id": "CVE-2022-1586", "patch_type": "cross_branch", "projects": {"source": {"name": "PCRE2Project/pcre2", "repo": "https://github.com/PCRE2Project/pcre2", "language": "C"}, "target": {"name": "PCRE2Project/pcre2", "repo": "https://github.com/PCRE2Project/pcre2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "50a51cb7e67268e6ad417eb07c9de9bfea5cc55a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d4fa336fbcc388f89095b184ba6d99422cfc676c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-1586", "year": 2022, "cwe_types": ["CWE-125", "CWE-125"], "bug_type": "CWE-125", "source_commit_date": "2022-03-23T07:53:25Z", "target_commit_date": "2022-03-24T05:34:42Z", "source_files_changed": ["src/pcre2_jit_compile.c", "src/pcre2_jit_test.c"], "target_files_changed": ["src/pcre2_jit_compile.c"], "source_commit_url": "https://github.com/PCRE2Project/pcre2/commit/50a51cb7e67268e6ad417eb07c9de9bfea5cc55a%2C", "target_commit_url": "https://github.com/PCRE2Project/pcre2/commit/d4fa336fbcc388f89095b184ba6d99422cfc676c", "backporting_type": "cross_branch", "source_repo": "https://github.com/PCRE2Project/pcre2", "target_repo": "https://github.com/PCRE2Project/pcre2", "backporting_analysis": {"source_repo": "https://github.com/PCRE2Project/pcre2", "target_repo": "https://github.com/PCRE2Project/pcre2", "source_commit": "50a51cb7e67268e6ad417eb07c9de9bfea5cc55a", "target_commit": "d4fa336fbcc388f89095b184ba6d99422cfc676c", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29181_sparklemotion_nokogiri_sparklemotion_nokogiri_db05ba9a", "cve_id": "CVE-2022-29181", "patch_type": "cross_branch", "projects": {"source": {"name": "sparklemotion/nokogiri", "repo": "https://github.com/sparklemotion/nokogiri", "language": "C"}, "target": {"name": "sparklemotion/nokogiri", "repo": "https://github.com/sparklemotion/nokogiri", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "83cc451c3f29df397caa890afc3b714eae6ab8f7"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "db05ba9a1bd4b90aa6c76742cf6102a7c7297267"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29181", "year": 2022, "cwe_types": ["CWE-241", "CWE-843"], "bug_type": "CWE-241", "source_commit_date": "2022-05-07T01:57:41Z", "target_commit_date": "2022-05-07T01:57:41Z", "source_files_changed": ["ext/nokogiri/html4_sax_parser_context.c", "ext/nokogiri/xml_sax_parser_context.c"], "target_files_changed": ["ext/nokogiri/html4_sax_parser_context.c", "ext/nokogiri/xml_sax_parser_context.c"], "source_commit_url": "https://github.com/sparklemotion/nokogiri/commit/83cc451c3f29df397caa890afc3b714eae6ab8f7", "target_commit_url": "https://github.com/sparklemotion/nokogiri/commit/db05ba9a1bd4b90aa6c76742cf6102a7c7297267", "backporting_type": "cross_branch", "source_repo": "https://github.com/sparklemotion/nokogiri", "target_repo": "https://github.com/sparklemotion/nokogiri", "backporting_analysis": {"source_repo": "https://github.com/sparklemotion/nokogiri", "target_repo": "https://github.com/sparklemotion/nokogiri", "source_commit": "83cc451c3f29df397caa890afc3b714eae6ab8f7", "target_commit": "db05ba9a1bd4b90aa6c76742cf6102a7c7297267", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29204_tensorflow_tensorflow_tensorflow_tensorflow_20cb1872", "cve_id": "CVE-2022-29204", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "84563f265f28b3c36a15335c8b005d405260e943"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "20cb18724b0bf6c09071a3f53434c4eec53cc147"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29204", "year": 2022, "cwe_types": ["CWE-20", "CWE-191", "CWE-20"], "bug_type": "CWE-20", "source_commit_date": "2022-04-15T17:34:16Z", "target_commit_date": "2022-04-20T19:05:26Z", "source_files_changed": ["tensorflow/core/kernels/unsorted_segment_join_op.cc"], "target_files_changed": ["tensorflow/core/kernels/unsorted_segment_join_op.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/84563f265f28b3c36a15335c8b005d405260e943", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/20cb18724b0bf6c09071a3f53434c4eec53cc147", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "84563f265f28b3c36a15335c8b005d405260e943", "target_commit": "20cb18724b0bf6c09071a3f53434c4eec53cc147", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29209_tensorflow_tensorflow_tensorflow_tensorflow_b917181c", "cve_id": "CVE-2022-29209", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d73521b7603f10e3029a2f1cd5067ca985738fc8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b917181c29b50cb83399ba41f4d938dc369109a1"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29209", "year": 2022, "cwe_types": ["CWE-843"], "bug_type": "CWE-843", "source_commit_date": "2022-04-26T07:30:22Z", "target_commit_date": "2022-04-28T21:41:18Z", "source_files_changed": [], "target_files_changed": ["tensorflow/core/platform/default/logging.h"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/d73521b7603f10e3029a2f1cd5067ca985738fc8", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/b917181c29b50cb83399ba41f4d938dc369109a1", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "d73521b7603f10e3029a2f1cd5067ca985738fc8", "target_commit": "b917181c29b50cb83399ba41f4d938dc369109a1", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29213_tensorflow_tensorflow_tensorflow_tensorflow_0a8a781e", "cve_id": "CVE-2022-29213", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f4348ad6ea69c82a6e310b1755de0e11ba739f5a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "0a8a781e597b18ead006d19b7d23d0a369e9ad73"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29213", "year": 2022, "cwe_types": ["CWE-20", "CWE-617"], "bug_type": "CWE-20", "source_commit_date": "2022-03-17T15:07:40Z", "target_commit_date": "2022-03-23T14:55:17Z", "source_files_changed": [], "target_files_changed": ["tensorflow/core/kernels/fft_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/f4348ad6ea69c82a6e310b1755de0e11ba739f5a", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/0a8a781e597b18ead006d19b7d23d0a369e9ad73", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "f4348ad6ea69c82a6e310b1755de0e11ba739f5a", "target_commit": "0a8a781e597b18ead006d19b7d23d0a369e9ad73", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29242_gost-engine_engine_gost-engine_engine_b2b4d629", "cve_id": "CVE-2022-29242", "patch_type": "cross_branch", "projects": {"source": {"name": "gost-engine/engine", "repo": "https://github.com/gost-engine/engine", "language": "C"}, "target": {"name": "gost-engine/engine", "repo": "https://github.com/gost-engine/engine", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c6655a0b620a3e31f085cc906f8073fe81b2fad3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b2b4d629f100eaee9f5942a106b1ccefe85b8808"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29242", "year": 2022, "cwe_types": ["CWE-120"], "bug_type": "CWE-120", "source_commit_date": "2022-05-20T16:13:50Z", "target_commit_date": "2022-05-21T18:20:20Z", "source_files_changed": ["e_gost_err.c", "e_gost_err.h", "gost_ec_keyx.c"], "target_files_changed": ["gost_ec_keyx.c"], "source_commit_url": "https://github.com/gost-engine/engine/commit/c6655a0b620a3e31f085cc906f8073fe81b2fad3", "target_commit_url": "https://github.com/gost-engine/engine/commit/b2b4d629f100eaee9f5942a106b1ccefe85b8808", "backporting_type": "cross_branch", "source_repo": "https://github.com/gost-engine/engine", "target_repo": "https://github.com/gost-engine/engine", "backporting_analysis": {"source_repo": "https://github.com/gost-engine/engine", "target_repo": "https://github.com/gost-engine/engine", "source_commit": "c6655a0b620a3e31f085cc906f8073fe81b2fad3", "target_commit": "b2b4d629f100eaee9f5942a106b1ccefe85b8808", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-31623_MariaDB_server_MariaDB_server_f1e9e0b6", "cve_id": "CVE-2022-31623", "patch_type": "cross_branch", "projects": {"source": {"name": "MariaDB/server", "repo": "https://github.com/MariaDB/server", "language": "C"}, "target": {"name": "MariaDB/server", "repo": "https://github.com/MariaDB/server", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7c30bc38a588b22b01f11130cfe99e7f36accf94"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f1e9e0b6436c26d89d3a226cad14d9d211847b79"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-31623", "year": 2022, "cwe_types": ["CWE-667"], "bug_type": "CWE-667", "source_commit_date": "2021-10-26T22:42:45Z", "target_commit_date": "2021-11-09T05:45:07Z", "source_files_changed": ["extra/mariabackup/ds_compress.cc"], "target_files_changed": [], "source_commit_url": "https://github.com/MariaDB/server/commit/7c30bc38a588b22b01f11130cfe99e7f36accf94", "target_commit_url": "https://github.com/MariaDB/server/commit/f1e9e0b6436c26d89d3a226cad14d9d211847b79", "backporting_type": "cross_branch", "source_repo": "https://github.com/MariaDB/server", "target_repo": "https://github.com/MariaDB/server", "backporting_analysis": {"source_repo": "https://github.com/MariaDB/server", "target_repo": "https://github.com/MariaDB/server", "source_commit": "7c30bc38a588b22b01f11130cfe99e7f36accf94", "target_commit": "f1e9e0b6436c26d89d3a226cad14d9d211847b79", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29694_unicorn-engine_unicorn_unicorn-engine_unicorn_3d3deac5", "cve_id": "CVE-2022-29694", "patch_type": "cross_branch", "projects": {"source": {"name": "unicorn-engine/unicorn", "repo": "https://github.com/unicorn-engine/unicorn", "language": "C"}, "target": {"name": "unicorn-engine/unicorn", "repo": "https://github.com/unicorn-engine/unicorn", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "31389e59457f304be3809f9679f91a42daa7ebaa"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3d3deac5e6d38602b689c4fef5dac004f07a2e63"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29694", "year": 2022, "cwe_types": ["CWE-476"], "bug_type": "NULL pointer dereference", "source_commit_date": "2022-04-14T10:06:58Z", "target_commit_date": "2022-04-16T17:17:41Z", "source_files_changed": [], "target_files_changed": ["qemu/exec.c", "qemu/include/qemu/atomic.h", "qemu/include/qemu/rcu_queue.h", "qemu/softmmu/memory.c", "tests/unit/test_mem.c"], "source_commit_url": "https://github.com/unicorn-engine/unicorn/commit/31389e59457f304be3809f9679f91a42daa7ebaa", "target_commit_url": "https://github.com/unicorn-engine/unicorn/commit/3d3deac5e6d38602b689c4fef5dac004f07a2e63", "backporting_type": "cross_branch", "source_repo": "https://github.com/unicorn-engine/unicorn", "target_repo": "https://github.com/unicorn-engine/unicorn", "backporting_analysis": {"source_repo": "https://github.com/unicorn-engine/unicorn", "target_repo": "https://github.com/unicorn-engine/unicorn", "source_commit": "31389e59457f304be3809f9679f91a42daa7ebaa", "target_commit": "3d3deac5e6d38602b689c4fef5dac004f07a2e63", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-32545_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_450949ed", "cve_id": "CVE-2022-32545", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9c9a84cec4ab28ee0b57c2b9266d6fbe68183512"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "450949ed017f009b399c937cf362f0058eacc5fa"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-32545", "year": 2022, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2022-03-19T10:50:38Z", "target_commit_date": "2022-03-19T11:01:57Z", "source_files_changed": ["coders/psd.c"], "target_files_changed": ["coders/emf.c", "coders/psd.c", "magick/widget.c", "wand/animate.c", "wand/display.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/9c9a84cec4ab28ee0b57c2b9266d6fbe68183512", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/450949ed017f009b399c937cf362f0058eacc5fa", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "9c9a84cec4ab28ee0b57c2b9266d6fbe68183512", "target_commit": "450949ed017f009b399c937cf362f0058eacc5fa", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-32546_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_29c8abce", "cve_id": "CVE-2022-32546", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f221ea0fa3171f0f4fdf74ac9d81b203b9534c23"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "29c8abce0da56b536542f76a9ddfebdaab5b2943"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-32546", "year": 2022, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2022-03-24T15:34:47Z", "target_commit_date": "2022-03-24T15:38:59Z", "source_files_changed": ["coders/pcl.c"], "target_files_changed": ["coders/pcl.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/f221ea0fa3171f0f4fdf74ac9d81b203b9534c23", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/29c8abce0da56b536542f76a9ddfebdaab5b2943", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "f221ea0fa3171f0f4fdf74ac9d81b203b9534c23", "target_commit": "29c8abce0da56b536542f76a9ddfebdaab5b2943", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-32547_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_dc070da8", "cve_id": "CVE-2022-32547", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eac8ce4d873f28bb6a46aa3a662fb196b49b95d0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "dc070da861a015d3c97488fdcca6063b44d47a7b"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-32547", "year": 2022, "cwe_types": ["CWE-704"], "bug_type": "CWE-704", "source_commit_date": "2022-04-09T12:34:03Z", "target_commit_date": "2022-04-09T12:40:54Z", "source_files_changed": ["MagickCore/property.c"], "target_files_changed": ["magick/property.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/eac8ce4d873f28bb6a46aa3a662fb196b49b95d0", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/dc070da861a015d3c97488fdcca6063b44d47a7b", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "eac8ce4d873f28bb6a46aa3a662fb196b49b95d0", "target_commit": "dc070da861a015d3c97488fdcca6063b44d47a7b", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-33105_redis_redis_redis_redis_4a7a4e42", "cve_id": "CVE-2022-33105", "patch_type": "cross_branch", "projects": {"source": {"name": "redis/redis", "repo": "https://github.com/redis/redis", "language": "C"}, "target": {"name": "redis/redis", "repo": "https://github.com/redis/redis", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4b262182d95f286222039ee52fc420eb1f9bb955"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4a7a4e42db8ff757cdf3f4a824f66426036034ef"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-33105", "year": 2022, "cwe_types": ["CWE-401"], "bug_type": "CWE-401", "source_commit_date": "2022-05-15T02:38:26Z", "target_commit_date": "2022-05-22T09:15:26Z", "source_files_changed": [], "target_files_changed": ["src/t_stream.c"], "source_commit_url": "https://github.com/redis/redis/commit/4b262182d95f286222039ee52fc420eb1f9bb955", "target_commit_url": "https://github.com/redis/redis/commit/4a7a4e42db8ff757cdf3f4a824f66426036034ef", "backporting_type": "cross_branch", "source_repo": "https://github.com/redis/redis", "target_repo": "https://github.com/redis/redis", "backporting_analysis": {"source_repo": "https://github.com/redis/redis", "target_repo": "https://github.com/redis/redis", "source_commit": "4b262182d95f286222039ee52fc420eb1f9bb955", "target_commit": "4a7a4e42db8ff757cdf3f4a824f66426036034ef", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-35414_qemu_qemu_qemu_qemu_3517fb72", "cve_id": "CVE-2022-35414", "patch_type": "cross_branch", "projects": {"source": {"name": "qemu/qemu", "repo": "https://github.com/qemu/qemu", "language": "C"}, "target": {"name": "qemu/qemu", "repo": "https://github.com/qemu/qemu", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "418ade7849ce7641c0f7333718caf5091a02fd4c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3517fb726741c109cae7995f9ea46f0cab6187d6"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-35414", "year": 2022, "cwe_types": ["CWE-908"], "bug_type": "CWE-908", "source_commit_date": "2022-06-21T15:38:29Z", "target_commit_date": "2022-07-05T07:09:50Z", "source_files_changed": ["softmmu/physmem.c"], "target_files_changed": ["target/loongarch/cpu.c"], "source_commit_url": "https://github.com/qemu/qemu/commit/418ade7849ce7641c0f7333718caf5091a02fd4c", "target_commit_url": "https://github.com/qemu/qemu/commit/3517fb726741c109cae7995f9ea46f0cab6187d6#diff-83c563ed6330dc5d49876f1116e7518b5c16654bbc6e9b4ea8e28f5833d576fcR482.aa", "backporting_type": "cross_branch", "source_repo": "https://github.com/qemu/qemu", "target_repo": "https://github.com/qemu/qemu", "backporting_analysis": {"source_repo": "https://github.com/qemu/qemu", "target_repo": "https://github.com/qemu/qemu", "source_commit": "418ade7849ce7641c0f7333718caf5091a02fd4c", "target_commit": "3517fb726741c109cae7995f9ea46f0cab6187d6", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-34033_michaelrsweet_htmldoc_michaelrsweet_htmldoc_ee778252", "cve_id": "CVE-2022-34033", "patch_type": "cross_branch", "projects": {"source": {"name": "michaelrsweet/htmldoc", "repo": "https://github.com/michaelrsweet/htmldoc", "language": "C"}, "target": {"name": "michaelrsweet/htmldoc", "repo": "https://github.com/michaelrsweet/htmldoc", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a0014be47d614220db111b360fb6170ef6f3937e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ee778252faebb721afba5a081dd6ad7eaf20eef3"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-34033", "year": 2022, "cwe_types": ["CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-05-07T10:37:23Z", "target_commit_date": "2021-05-07T10:42:42Z", "source_files_changed": ["htmldoc/htmllib.cxx", "htmldoc/testhtml.cxx"], "target_files_changed": ["htmldoc/html.cxx", "htmldoc/htmllib.cxx", "htmldoc/htmlsep.cxx"], "source_commit_url": "https://github.com/michaelrsweet/htmldoc/commit/a0014be47d614220db111b360fb6170ef6f3937e", "target_commit_url": "https://github.com/michaelrsweet/htmldoc/commit/ee778252faebb721afba5a081dd6ad7eaf20eef3", "backporting_type": "cross_branch", "source_repo": "https://github.com/michaelrsweet/htmldoc", "target_repo": "https://github.com/michaelrsweet/htmldoc", "backporting_analysis": {"source_repo": "https://github.com/michaelrsweet/htmldoc", "target_repo": "https://github.com/michaelrsweet/htmldoc", "source_commit": "a0014be47d614220db111b360fb6170ef6f3937e", "target_commit": "ee778252faebb721afba5a081dd6ad7eaf20eef3", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-34035_michaelrsweet_htmldoc_michaelrsweet_htmldoc_ee778252", "cve_id": "CVE-2022-34035", "patch_type": "cross_branch", "projects": {"source": {"name": "michaelrsweet/htmldoc", "repo": "https://github.com/michaelrsweet/htmldoc", "language": "C"}, "target": {"name": "michaelrsweet/htmldoc", "repo": "https://github.com/michaelrsweet/htmldoc", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a0014be47d614220db111b360fb6170ef6f3937e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ee778252faebb721afba5a081dd6ad7eaf20eef3"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-34035", "year": 2022, "cwe_types": ["CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2021-05-07T10:37:23Z", "target_commit_date": "2021-05-07T10:42:42Z", "source_files_changed": ["htmldoc/htmllib.cxx", "htmldoc/testhtml.cxx"], "target_files_changed": ["htmldoc/html.cxx", "htmldoc/htmllib.cxx", "htmldoc/htmlsep.cxx"], "source_commit_url": "https://github.com/michaelrsweet/htmldoc/commit/a0014be47d614220db111b360fb6170ef6f3937e", "target_commit_url": "https://github.com/michaelrsweet/htmldoc/commit/ee778252faebb721afba5a081dd6ad7eaf20eef3", "backporting_type": "cross_branch", "source_repo": "https://github.com/michaelrsweet/htmldoc", "target_repo": "https://github.com/michaelrsweet/htmldoc", "backporting_analysis": {"source_repo": "https://github.com/michaelrsweet/htmldoc", "target_repo": "https://github.com/michaelrsweet/htmldoc", "source_commit": "a0014be47d614220db111b360fb6170ef6f3937e", "target_commit": "ee778252faebb721afba5a081dd6ad7eaf20eef3", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-2522_vim_vim_vim_vim_5fa9f23a", "cve_id": "CVE-2022-2522", "patch_type": "cross_branch", "projects": {"source": {"name": "vim/vim", "repo": "https://github.com/vim/vim", "language": "C"}, "target": {"name": "vim/vim", "repo": "https://github.com/vim/vim", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b9e717367c395490149495cf375911b5d9de889e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5fa9f23a63651a8abdb074b4fc2ec9b1adc6b089"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-2522", "year": 2022, "cwe_types": ["CWE-122"], "bug_type": "heap overflow", "source_commit_date": "2022-07-23T05:53:08Z", "target_commit_date": "2022-07-23T08:06:48Z", "source_files_changed": ["src/insexpand.c", "src/version.c"], "target_files_changed": ["src/autocmd.c", "src/version.c", "src/window.c"], "source_commit_url": "https://github.com/vim/vim/commit/b9e717367c395490149495cf375911b5d9de889e", "target_commit_url": "https://github.com/vim/vim/commit/5fa9f23a63651a8abdb074b4fc2ec9b1adc6b089", "backporting_type": "cross_branch", "source_repo": "https://github.com/vim/vim", "target_repo": "https://github.com/vim/vim", "backporting_analysis": {"source_repo": "https://github.com/vim/vim", "target_repo": "https://github.com/vim/vim", "source_commit": "b9e717367c395490149495cf375911b5d9de889e", "target_commit": "5fa9f23a63651a8abdb074b4fc2ec9b1adc6b089", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-37434_madler_zlib_madler_zlib_1eb7682f", "cve_id": "CVE-2022-37434", "patch_type": "cross_branch", "projects": {"source": {"name": "madler/zlib", "repo": "https://github.com/madler/zlib", "language": "C"}, "target": {"name": "madler/zlib", "repo": "https://github.com/madler/zlib", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eff308af425b67093bab25f80f1ae950166bece1"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1eb7682f845ac9e9bf9ae35bbfb3bad5dacbd91d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-37434", "year": 2022, "cwe_types": ["CWE-787", "CWE-120"], "bug_type": "CWE-787", "source_commit_date": "2022-07-30T22:51:11Z", "target_commit_date": "2022-08-08T17:50:09Z", "source_files_changed": ["inflate.c"], "target_files_changed": ["inflate.c"], "source_commit_url": "https://github.com/madler/zlib/commit/eff308af425b67093bab25f80f1ae950166bece1", "target_commit_url": "https://github.com/madler/zlib/commit/1eb7682f845ac9e9bf9ae35bbfb3bad5dacbd91d", "backporting_type": "cross_branch", "source_repo": "https://github.com/madler/zlib", "target_repo": "https://github.com/madler/zlib", "backporting_analysis": {"source_repo": "https://github.com/madler/zlib", "target_repo": "https://github.com/madler/zlib", "source_commit": "eff308af425b67093bab25f80f1ae950166bece1", "target_commit": "1eb7682f845ac9e9bf9ae35bbfb3bad5dacbd91d", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2020-27795_radareorg_radare2_radareorg_radare2_4d381168", "cve_id": "CVE-2020-27795", "patch_type": "cross_branch", "projects": {"source": {"name": "radareorg/radare2", "repo": "https://github.com/radareorg/radare2", "language": "C"}, "target": {"name": "radareorg/radare2", "repo": "https://github.com/radareorg/radare2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a6cd3f203c9e47543c7a0552974ebbb0a54f8188"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4d3811681a80f92a53e795f6a64c4b0fc2c8dd22"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-27795", "year": 2020, "cwe_types": ["CWE-908", "CWE-908"], "bug_type": "CWE-908", "source_commit_date": "2020-03-16T14:30:18Z", "target_commit_date": "2020-03-16T16:03:22Z", "source_files_changed": [], "target_files_changed": ["libr/core/cmd_anal.c"], "source_commit_url": "https://github.com/radareorg/radare2/commit/a6cd3f203c9e47543c7a0552974ebbb0a54f8188", "target_commit_url": "https://github.com/radareorg/radare2/commit/4d3811681a80f92a53e795f6a64c4b0fc2c8dd22", "backporting_type": "cross_branch", "source_repo": "https://github.com/radareorg/radare2", "target_repo": "https://github.com/radareorg/radare2", "backporting_analysis": {"source_repo": "https://github.com/radareorg/radare2", "target_repo": "https://github.com/radareorg/radare2", "source_commit": "a6cd3f203c9e47543c7a0552974ebbb0a54f8188", "target_commit": "4d3811681a80f92a53e795f6a64c4b0fc2c8dd22", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-3521_rpm-software-management_rpm_rpm-software-management_rpm_e233fb84", "cve_id": "CVE-2021-3521", "patch_type": "cross_branch", "projects": {"source": {"name": "rpm-software-management/rpm", "repo": "https://github.com/rpm-software-management/rpm", "language": "C"}, "target": {"name": "rpm-software-management/rpm", "repo": "https://github.com/rpm-software-management/rpm", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bd36c5dc9fb6d90c46fbfed8c2d67516fc571ec8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e233fb844adda74a5199057d1fd7fa20d994564d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-3521", "year": 2021, "cwe_types": ["CWE-347", "CWE-347"], "bug_type": "CWE-347", "source_commit_date": "2021-09-30T06:59:30Z", "target_commit_date": "2021-09-30T06:59:30Z", "source_files_changed": ["rpmio/rpmpgp.c"], "target_files_changed": [], "source_commit_url": "https://github.com/rpm-software-management/rpm/commit/bd36c5dc9fb6d90c46fbfed8c2d67516fc571ec8", "target_commit_url": "https://github.com/rpm-software-management/rpm/commit/e233fb844adda74a5199057d1fd7fa20d994564d", "backporting_type": "cross_branch", "source_repo": "https://github.com/rpm-software-management/rpm", "target_repo": "https://github.com/rpm-software-management/rpm", "backporting_analysis": {"source_repo": "https://github.com/rpm-software-management/rpm", "target_repo": "https://github.com/rpm-software-management/rpm", "source_commit": "bd36c5dc9fb6d90c46fbfed8c2d67516fc571ec8", "target_commit": "e233fb844adda74a5199057d1fd7fa20d994564d", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-28861_python_cpython_python_cpython_8563d4a7", "cve_id": "CVE-2021-28861", "patch_type": "cross_version", "projects": {"source": {"name": "python/cpython", "repo": "https://github.com/python/cpython", "language": "C"}, "target": {"name": "python/cpython", "repo": "https://github.com/python/cpython", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "42eb552059eb1ec9df7f71c7c8dd8e6597640a93"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "8563d4a74d7ee0abca7dc3910c9a2dac4e08ce65"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-28861", "year": 2021, "cwe_types": ["CWE-601"], "bug_type": "CWE-601", "source_commit_date": "2021-03-13T20:01:06Z", "target_commit_date": "2022-06-16T20:34:29Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/python/cpython/commit/42eb552059eb1ec9df7f71c7c8dd8e6597640a93", "target_commit_url": "https://github.com/python/cpython/commit/8563d4a74d7ee0abca7dc3910c9a2dac4e08ce65", "backporting_type": "cross_version", "source_repo": "https://github.com/python/cpython", "target_repo": "https://github.com/python/cpython", "backporting_analysis": {"source_repo": "https://github.com/python/cpython", "target_repo": "https://github.com/python/cpython", "source_commit": "42eb552059eb1ec9df7f71c7c8dd8e6597640a93", "target_commit": "8563d4a74d7ee0abca7dc3910c9a2dac4e08ce65", "analysis_result": "cross_version"}}}, {"unified_id": "CVE-2022-25761_open62541_open62541_open62541_open62541_cd04b650", "cve_id": "CVE-2022-25761", "patch_type": "cross_branch", "projects": {"source": {"name": "open62541/open62541", "repo": "https://github.com/open62541/open62541", "language": "C"}, "target": {"name": "open62541/open62541", "repo": "https://github.com/open62541/open62541", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b79db1ac78146fc06b0b8435773d3967de2d659c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cd04b6506a293b0d2f35e66bb2ae859c2c44adee"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-25761", "year": 2022, "cwe_types": ["CWE-770"], "bug_type": "CWE-770", "source_commit_date": "2022-06-04T10:32:41Z", "target_commit_date": "2022-06-04T10:32:41Z", "source_files_changed": ["plugins/ua_config_default.c", "tests/check_securechannel.c"], "target_files_changed": [], "source_commit_url": "https://github.com/open62541/open62541/commit/b79db1ac78146fc06b0b8435773d3967de2d659c", "target_commit_url": "https://github.com/open62541/open62541/commit/cd04b6506a293b0d2f35e66bb2ae859c2c44adee", "backporting_type": "cross_branch", "source_repo": "https://github.com/open62541/open62541", "target_repo": "https://github.com/open62541/open62541", "backporting_analysis": {"source_repo": "https://github.com/open62541/open62541", "target_repo": "https://github.com/open62541/open62541", "source_commit": "b79db1ac78146fc06b0b8435773d3967de2d659c", "target_commit": "cd04b6506a293b0d2f35e66bb2ae859c2c44adee", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-20298_AcademySoftwareFoundation_openexr_AcademySoftwareFoundation_openexr_85fd638a", "cve_id": "CVE-2021-20298", "patch_type": "cross_branch", "projects": {"source": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}, "target": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bb90c30a5acebb2f65a097e3be57ef15d9db4e81"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "85fd638ae0d5fa132434f4cbf32590261c1dba97"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-20298", "year": 2021, "cwe_types": ["CWE-400", "CWE-787"], "bug_type": "denial of service", "source_commit_date": "2020-10-06T23:17:59Z", "target_commit_date": "2020-10-07T01:38:59Z", "source_files_changed": [], "target_files_changed": ["OpenEXR/IlmImf/ImfB44Compressor.cpp"], "source_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/bb90c30a5acebb2f65a097e3be57ef15d9db4e81", "target_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/85fd638ae0d5fa132434f4cbf32590261c1dba97", "backporting_type": "cross_branch", "source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "backporting_analysis": {"source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "source_commit": "bb90c30a5acebb2f65a097e3be57ef15d9db4e81", "target_commit": "85fd638ae0d5fa132434f4cbf32590261c1dba97", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-20304_AcademySoftwareFoundation_openexr_AcademySoftwareFoundation_openexr_51a92d67", "cve_id": "CVE-2021-20304", "patch_type": "cross_branch", "projects": {"source": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}, "target": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "04bac6d7aff0070cb0ff74153068f54fe77e3258"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "51a92d67f53c08230734e74564c807043cbfe41e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-20304", "year": 2021, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2020-10-10T03:33:37Z", "target_commit_date": "2020-10-12T19:03:57Z", "source_files_changed": [], "target_files_changed": ["OpenEXR/IlmImf/ImfHuf.cpp", "OpenEXR/IlmImfTest/testHuf.cpp"], "source_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/04bac6d7aff0070cb0ff74153068f54fe77e3258", "target_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/51a92d67f53c08230734e74564c807043cbfe41e", "backporting_type": "cross_branch", "source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "backporting_analysis": {"source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "source_commit": "04bac6d7aff0070cb0ff74153068f54fe77e3258", "target_commit": "51a92d67f53c08230734e74564c807043cbfe41e", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-3798_opencryptoki_opencryptoki_opencryptoki_opencryptoki_814e4d0a", "cve_id": "CVE-2021-3798", "patch_type": "cross_branch", "projects": {"source": {"name": "opencryptoki/opencryptoki", "repo": "https://github.com/opencryptoki/opencryptoki", "language": "C"}, "target": {"name": "opencryptoki/opencryptoki", "repo": "https://github.com/opencryptoki/opencryptoki", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4e3b43c3d8844402c04a66b55c6c940f965109f0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "814e4d0ab9a72b536e8f6694af4e26280e0e96bd"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-3798", "year": 2021, "cwe_types": ["CWE-200"], "bug_type": "information leakage", "source_commit_date": "2021-05-03T08:05:07Z", "target_commit_date": "2021-05-03T08:05:07Z", "source_files_changed": ["usr/lib/soft_stdll/soft_specific.c"], "target_files_changed": [], "source_commit_url": "https://github.com/opencryptoki/opencryptoki/commit/4e3b43c3d8844402c04a66b55c6c940f965109f0", "target_commit_url": "https://github.com/opencryptoki/opencryptoki/commit/814e4d0ab9a72b536e8f6694af4e26280e0e96bd", "backporting_type": "cross_branch", "source_repo": "https://github.com/opencryptoki/opencryptoki", "target_repo": "https://github.com/opencryptoki/opencryptoki", "backporting_analysis": {"source_repo": "https://github.com/opencryptoki/opencryptoki", "target_repo": "https://github.com/opencryptoki/opencryptoki", "source_commit": "4e3b43c3d8844402c04a66b55c6c940f965109f0", "target_commit": "814e4d0ab9a72b536e8f6694af4e26280e0e96bd", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-20224_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_553054c1", "cve_id": "CVE-2021-20224", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9583e97bfe0a80ebf321e05399a9e6490747f397"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "553054c1cb1e4e05ec86237afef76a32cd7c464d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-20224", "year": 2021, "cwe_types": ["CWE-190", "CWE-190"], "bug_type": "integer overflow", "source_commit_date": "2021-01-06T15:39:26Z", "target_commit_date": "2021-01-06T23:34:31Z", "source_files_changed": [], "target_files_changed": ["magick/quantum-export.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/9583e97bfe0a80ebf321e05399a9e6490747f397", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/553054c1cb1e4e05ec86237afef76a32cd7c464d", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "9583e97bfe0a80ebf321e05399a9e6490747f397", "target_commit": "553054c1cb1e4e05ec86237afef76a32cd7c464d", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2021-35938_rpm-software-management_rpm_rpm-software-management_rpm_ea87a936", "cve_id": "CVE-2021-35938", "patch_type": "cross_branch", "projects": {"source": {"name": "rpm-software-management/rpm", "repo": "https://github.com/rpm-software-management/rpm", "language": "C"}, "target": {"name": "rpm-software-management/rpm", "repo": "https://github.com/rpm-software-management/rpm", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "25a435e90844ea98fe5eb7bef22c1aecf3a9c033"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ea87a93685dfbe682dcc6c9d44f2150e238787a0"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-35938", "year": 2021, "cwe_types": ["CWE-59", "CWE-59"], "bug_type": "CWE-59", "source_commit_date": "2022-02-14T12:29:33Z", "target_commit_date": "2022-02-15T11:15:03Z", "source_files_changed": ["lib/fsm.c"], "target_files_changed": [], "source_commit_url": "https://github.com/rpm-software-management/rpm/commit/25a435e90844ea98fe5eb7bef22c1aecf3a9c033", "target_commit_url": "https://github.com/rpm-software-management/rpm/commit/ea87a93685dfbe682dcc6c9d44f2150e238787a0", "backporting_type": "cross_branch", "source_repo": "https://github.com/rpm-software-management/rpm", "target_repo": "https://github.com/rpm-software-management/rpm", "backporting_analysis": {"source_repo": "https://github.com/rpm-software-management/rpm", "target_repo": "https://github.com/rpm-software-management/rpm", "source_commit": "25a435e90844ea98fe5eb7bef22c1aecf3a9c033", "target_commit": "ea87a93685dfbe682dcc6c9d44f2150e238787a0", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-35939_rpm-software-management_rpm_rpm-software-management_rpm_ea87a936", "cve_id": "CVE-2021-35939", "patch_type": "cross_branch", "projects": {"source": {"name": "rpm-software-management/rpm", "repo": "https://github.com/rpm-software-management/rpm", "language": "C"}, "target": {"name": "rpm-software-management/rpm", "repo": "https://github.com/rpm-software-management/rpm", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "96ec957e281220f8e137a2d5eb23b83a6377d556"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ea87a93685dfbe682dcc6c9d44f2150e238787a0"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-35939", "year": 2021, "cwe_types": ["CWE-59", "CWE-59"], "bug_type": "CWE-59", "source_commit_date": "2022-02-10T12:32:43Z", "target_commit_date": "2022-02-15T11:15:03Z", "source_files_changed": ["lib/fsm.c"], "target_files_changed": [], "source_commit_url": "https://github.com/rpm-software-management/rpm/commit/96ec957e281220f8e137a2d5eb23b83a6377d556", "target_commit_url": "https://github.com/rpm-software-management/rpm/commit/ea87a93685dfbe682dcc6c9d44f2150e238787a0", "backporting_type": "cross_branch", "source_repo": "https://github.com/rpm-software-management/rpm", "target_repo": "https://github.com/rpm-software-management/rpm", "backporting_analysis": {"source_repo": "https://github.com/rpm-software-management/rpm", "target_repo": "https://github.com/rpm-software-management/rpm", "source_commit": "96ec957e281220f8e137a2d5eb23b83a6377d556", "target_commit": "ea87a93685dfbe682dcc6c9d44f2150e238787a0", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-3574_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_c6ad94fb", "cve_id": "CVE-2021-3574", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cd7f9fb7751b0d59d5a74b12d971155caad5a792"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c6ad94fbb7b280f39c2fbbdc1c140e51b1b466e9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-3574", "year": 2021, "cwe_types": ["CWE-401", "CWE-401"], "bug_type": "CWE-401", "source_commit_date": "2021-04-14T01:41:34Z", "target_commit_date": "2021-04-14T01:41:43Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/cd7f9fb7751b0d59d5a74b12d971155caad5a792", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/c6ad94fbb7b280f39c2fbbdc1c140e51b1b466e9", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "cd7f9fb7751b0d59d5a74b12d971155caad5a792", "target_commit": "c6ad94fbb7b280f39c2fbbdc1c140e51b1b466e9", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-0496_openscad_openscad_openscad_openscad_00a46929", "cve_id": "CVE-2022-0496", "patch_type": "cross_branch", "projects": {"source": {"name": "openscad/openscad", "repo": "https://github.com/openscad/openscad", "language": "C"}, "target": {"name": "openscad/openscad", "repo": "https://github.com/openscad/openscad", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "770e3234cbfe66edbc0333f796b46d36a74aa652"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "00a4692989c4e2f191525f73f24ad8727bacdf41"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-0496", "year": 2022, "cwe_types": ["CWE-119", "CWE-125"], "bug_type": "memory overflow", "source_commit_date": "2022-01-16T03:40:09Z", "target_commit_date": "2022-02-05T17:38:31Z", "source_files_changed": ["src/dxfdata.cc"], "target_files_changed": ["src/dxfdata.cc"], "source_commit_url": "https://github.com/openscad/openscad/commit/770e3234cbfe66edbc0333f796b46d36a74aa652", "target_commit_url": "https://github.com/openscad/openscad/commit/00a4692989c4e2f191525f73f24ad8727bacdf41", "backporting_type": "cross_branch", "source_repo": "https://github.com/openscad/openscad", "target_repo": "https://github.com/openscad/openscad", "backporting_analysis": {"source_repo": "https://github.com/openscad/openscad", "target_repo": "https://github.com/openscad/openscad", "source_commit": "770e3234cbfe66edbc0333f796b46d36a74aa652", "target_commit": "00a4692989c4e2f191525f73f24ad8727bacdf41", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-1115_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_c8718305", "cve_id": "CVE-2022-1115", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1f860f52bd8d58737ad883072203391096b30b51"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c8718305f120293d8bf13724f12eed885d830b09"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-1115", "year": 2022, "cwe_types": ["CWE-119", "CWE-787"], "bug_type": "memory overflow", "source_commit_date": "2022-03-23T00:11:27Z", "target_commit_date": "2022-03-23T00:11:47Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/1f860f52bd8d58737ad883072203391096b30b51", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/c8718305f120293d8bf13724f12eed885d830b09", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "1f860f52bd8d58737ad883072203391096b30b51", "target_commit": "c8718305f120293d8bf13724f12eed885d830b09", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-1325_GreycLab_CImg_GreycLab_CImg_37cf0c1e", "cve_id": "CVE-2022-1325", "patch_type": "cross_branch", "projects": {"source": {"name": "GreycLab/CImg", "repo": "https://github.com/GreycLab/CImg", "language": "C"}, "target": {"name": "GreycLab/CImg", "repo": "https://github.com/GreycLab/CImg", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "619cb58dd90b4e03ac68286c70ed98acbefd1c90"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "37cf0c1e5eeafb5b759c1a36423eb3dae27dbee8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-1325", "year": 2022, "cwe_types": ["CWE-400", "CWE-770"], "bug_type": "denial of service", "source_commit_date": "2022-04-07T10:43:39Z", "target_commit_date": "2022-04-25T17:32:11Z", "source_files_changed": ["CImg.h"], "target_files_changed": [], "source_commit_url": "https://github.com/GreycLab/CImg/commit/619cb58dd90b4e03ac68286c70ed98acbefd1c90", "target_commit_url": "https://github.com/GreycLab/CImg/commit/37cf0c1e5eeafb5b759c1a36423eb3dae27dbee8", "backporting_type": "cross_branch", "source_repo": "https://github.com/GreycLab/CImg", "target_repo": "https://github.com/GreycLab/CImg", "backporting_analysis": {"source_repo": "https://github.com/GreycLab/CImg", "target_repo": "https://github.com/GreycLab/CImg", "source_commit": "619cb58dd90b4e03ac68286c70ed98acbefd1c90", "target_commit": "37cf0c1e5eeafb5b759c1a36423eb3dae27dbee8", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-36044_rizinorg_rizin_rizinorg_rizin_07b43bc8", "cve_id": "CVE-2022-36044", "patch_type": "cross_branch", "projects": {"source": {"name": "rizinorg/rizin", "repo": "https://github.com/rizinorg/rizin", "language": "C"}, "target": {"name": "rizinorg/rizin", "repo": "https://github.com/rizinorg/rizin", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "05bbd147caccc60162d6fba9baaaf24befa281cd"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "07b43bc8aa1ffebd9b68d60624c9610cf7e460c7"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-36044", "year": 2022, "cwe_types": ["CWE-787"], "bug_type": "CWE-787", "source_commit_date": "2022-08-17T12:29:37Z", "target_commit_date": "2022-08-19T21:01:10Z", "source_files_changed": ["librz/bin/format/luac/luac_bin.c", "librz/bin/format/luac/luac_common.h", "librz/bin/p/bin_luac.c"], "target_files_changed": ["librz/bin/bobj.c", "librz/bin/format/luac/luac_bin.c"], "source_commit_url": "https://github.com/rizinorg/rizin/commit/05bbd147caccc60162d6fba9baaaf24befa281cd", "target_commit_url": "https://github.com/rizinorg/rizin/commit/07b43bc8aa1ffebd9b68d60624c9610cf7e460c7", "backporting_type": "cross_branch", "source_repo": "https://github.com/rizinorg/rizin", "target_repo": "https://github.com/rizinorg/rizin", "backporting_analysis": {"source_repo": "https://github.com/rizinorg/rizin", "target_repo": "https://github.com/rizinorg/rizin", "source_commit": "05bbd147caccc60162d6fba9baaaf24befa281cd", "target_commit": "07b43bc8aa1ffebd9b68d60624c9610cf7e460c7", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-3170_torvalds_linux_torvalds_linux_5934d9a0", "cve_id": "CVE-2022-3170", "patch_type": "cross_branch", "projects": {"source": {"name": "torvalds/linux", "repo": "https://github.com/torvalds/linux", "language": "C"}, "target": {"name": "torvalds/linux", "repo": "https://github.com/torvalds/linux", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6ab55ec0a938c7f943a4edba3d6514f775983887"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5934d9a0383619c14df91af8fd76261dc3de2f5f"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-3170", "year": 2022, "cwe_types": ["CWE-125", "CWE-125"], "bug_type": "CWE-125", "source_commit_date": "2022-08-24T08:16:54Z", "target_commit_date": "2022-08-26T15:01:10Z", "source_files_changed": ["sound/core/control.c"], "target_files_changed": ["sound/core/control.c"], "source_commit_url": "https://github.com/torvalds/linux/commit/6ab55ec0a938c7f943a4edba3d6514f775983887", "target_commit_url": "https://github.com/torvalds/linux/commit/5934d9a0383619c14df91af8fd76261dc3de2f5f", "backporting_type": "cross_branch", "source_repo": "https://github.com/torvalds/linux", "target_repo": "https://github.com/torvalds/linux", "backporting_analysis": {"source_repo": "https://github.com/torvalds/linux", "target_repo": "https://github.com/torvalds/linux", "source_commit": "6ab55ec0a938c7f943a4edba3d6514f775983887", "target_commit": "5934d9a0383619c14df91af8fd76261dc3de2f5f", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-40674_libexpat_libexpat_libexpat_libexpat_721169ee", "cve_id": "CVE-2022-40674", "patch_type": "cross_branch", "projects": {"source": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}, "target": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4a32da87e931ba54393d465bb77c40b5c33d343b"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "721169eeca509ba93be6c73d0abe1caf5d218b9c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-40674", "year": 2022, "cwe_types": ["CWE-416", "CWE-416"], "bug_type": "use-after-free", "source_commit_date": "2022-08-17T17:26:18Z", "target_commit_date": "2022-09-11T18:38:09Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/libexpat/libexpat/commit/4a32da87e931ba54393d465bb77c40b5c33d343b", "target_commit_url": "https://github.com/libexpat/libexpat/commit/721169eeca509ba93be6c73d0abe1caf5d218b9c", "backporting_type": "cross_branch", "source_repo": "https://github.com/libexpat/libexpat", "target_repo": "https://github.com/libexpat/libexpat", "backporting_analysis": {"source_repo": "https://github.com/libexpat/libexpat", "target_repo": "https://github.com/libexpat/libexpat", "source_commit": "4a32da87e931ba54393d465bb77c40b5c33d343b", "target_commit": "721169eeca509ba93be6c73d0abe1caf5d218b9c", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-36014_tensorflow_tensorflow_tensorflow_tensorflow_a0f0b9a2", "cve_id": "CVE-2022-36014", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3a754740d5414e362512ee981eefba41561a63a6"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a0f0b9a21c9270930457095092f558fbad4c03e5"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-36014", "year": 2022, "cwe_types": ["CWE-476"], "bug_type": "NULL pointer dereference", "source_commit_date": "2022-05-20T03:00:39Z", "target_commit_date": "2022-06-08T17:27:21Z", "source_files_changed": ["tensorflow/core/ir/importexport/graphdef_import.cc"], "target_files_changed": ["tensorflow/core/ir/importexport/graphdef_import.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/3a754740d5414e362512ee981eefba41561a63a6", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a0f0b9a21c9270930457095092f558fbad4c03e5", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "3a754740d5414e362512ee981eefba41561a63a6", "target_commit": "a0f0b9a21c9270930457095092f558fbad4c03e5", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-3213_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_30ccf9a0", "cve_id": "CVE-2022-3213", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1aea203eb36409ce6903b9e41fe7cb70030e8750"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "30ccf9a0da1f47161b5935a95be854fe84e6c2a2"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-3213", "year": 2022, "cwe_types": ["CWE-119", "CWE-787"], "bug_type": "memory overflow", "source_commit_date": "2022-08-27T12:38:18Z", "target_commit_date": "2022-08-27T12:38:57Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/1aea203eb36409ce6903b9e41fe7cb70030e8750", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/30ccf9a0da1f47161b5935a95be854fe84e6c2a2", "backporting_type": "cross_repo", "source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "1aea203eb36409ce6903b9e41fe7cb70030e8750", "target_commit": "30ccf9a0da1f47161b5935a95be854fe84e6c2a2", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-37032_FRRouting_frr_FRRouting_frr_ff6db102", "cve_id": "CVE-2022-37032", "patch_type": "cross_version", "projects": {"source": {"name": "FRRouting/frr", "repo": "https://github.com/FRRouting/frr", "language": "C"}, "target": {"name": "FRRouting/frr", "repo": "https://github.com/FRRouting/frr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6d58272b4cf96f0daa846210dd2104877900f921"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ff6db1027f8f36df657ff2e5ea167773752537ed"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-37032", "year": 2022, "cwe_types": ["CWE-125"], "bug_type": "CWE-125", "source_commit_date": "2007-08-06T15:21:45Z", "target_commit_date": "2022-07-21T12:11:58Z", "source_files_changed": ["bgpd/bgp_debug.h", "bgpd/bgp_open.c", "bgpd/bgp_open.h", "bgpd/bgp_packet.c", "bgpd/bgp_vty.c", "lib/log.c"], "target_files_changed": ["bgpd/bgp_packet.c"], "source_commit_url": "https://github.com/FRRouting/frr/commit/6d58272b4cf96f0daa846210dd2104877900f921", "target_commit_url": "https://github.com/FRRouting/frr/commit/ff6db1027f8f36df657ff2e5ea167773752537ed", "backporting_type": "cross_version", "source_repo": "https://github.com/FRRouting/frr", "target_repo": "https://github.com/FRRouting/frr", "backporting_analysis": {"source_repo": "https://github.com/FRRouting/frr", "target_repo": "https://github.com/FRRouting/frr", "source_commit": "6d58272b4cf96f0daa846210dd2104877900f921", "target_commit": "ff6db1027f8f36df657ff2e5ea167773752537ed", "analysis_result": "cross_version"}}}, {"unified_id": "CVE-2022-3676_eclipse_omr_eclipse-openj9_openj9_6dbf777f", "cve_id": "CVE-2022-3676", "patch_type": "cross_repo", "projects": {"source": {"name": "eclipse/omr", "repo": "https://github.com/eclipse/omr", "language": "C"}, "target": {"name": "eclipse-openj9/openj9", "repo": "https://github.com/eclipse-openj9/openj9", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f5ab765943c192e25ac8ea8d341f88e6ed9f90d2"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6dbf777ff3e147208b76ce40cecb75c529a34c4c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-3676", "year": 2022, "cwe_types": ["CWE-20", "CWE-843", "CWE-843"], "bug_type": "CWE-20", "source_commit_date": "2022-03-11T00:34:10Z", "target_commit_date": "2022-09-20T17:38:32Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/eclipse/omr/commit/f5ab765943c192e25ac8ea8d341f88e6ed9f90d2", "target_commit_url": "https://github.com/eclipse-openj9/openj9/commit/6dbf777ff3e147208b76ce40cecb75c529a34c4c", "backporting_type": "cross_repo", "source_repo": "https://github.com/eclipse/omr", "target_repo": "https://github.com/eclipse-openj9/openj9", "backporting_analysis": {"source_repo": "https://github.com/eclipse/omr", "target_repo": "https://github.com/eclipse-openj9/openj9", "source_commit": "f5ab765943c192e25ac8ea8d341f88e6ed9f90d2", "target_commit": "6dbf777ff3e147208b76ce40cecb75c529a34c4c", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-43680_libexpat_libexpat_libexpat_libexpat_eedc5f6d", "cve_id": "CVE-2022-43680", "patch_type": "cross_branch", "projects": {"source": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}, "target": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "05c6788b364816f3c584f47e9819119138d151b0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eedc5f6de8e219130032c8ff2ff17580e18bd0c1"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-43680", "year": 2022, "cwe_types": ["CWE-416", "CWE-416"], "bug_type": "use-after-free", "source_commit_date": "2022-07-25T08:12:19Z", "target_commit_date": "2022-09-21T01:32:26Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/libexpat/libexpat/commit/05c6788b364816f3c584f47e9819119138d151b0", "target_commit_url": "https://github.com/libexpat/libexpat/commit/eedc5f6de8e219130032c8ff2ff17580e18bd0c1", "backporting_type": "cross_branch", "source_repo": "https://github.com/libexpat/libexpat", "target_repo": "https://github.com/libexpat/libexpat", "backporting_analysis": {"source_repo": "https://github.com/libexpat/libexpat", "target_repo": "https://github.com/libexpat/libexpat", "source_commit": "05c6788b364816f3c584f47e9819119138d151b0", "target_commit": "eedc5f6de8e219130032c8ff2ff17580e18bd0c1", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-25892_juli<PERSON><PERSON><PERSON>_MuhammaraJS_julian<PERSON><PERSON>_MuhammaraJS_90b278d0", "cve_id": "CVE-2022-25892", "patch_type": "cross_branch", "projects": {"source": {"name": "juli<PERSON><PERSON><PERSON>/MuhammaraJS", "repo": "https://github.com/julianhille/MuhammaraJS", "language": "C"}, "target": {"name": "juli<PERSON><PERSON><PERSON>/MuhammaraJS", "repo": "https://github.com/julianhille/MuhammaraJS", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1890fb555eaf171db79b73fdc3ea543bbd63c002"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "90b278d09f16062d93a4160ef0a54d449d739c51"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-25892", "year": 2022, "cwe_types": [], "bug_type": "unknown", "source_commit_date": "2022-10-22T17:31:05Z", "target_commit_date": "2022-10-22T17:33:35Z", "source_files_changed": ["src/deps/PDFWriter/PDFParser.cpp"], "target_files_changed": ["src/deps/PDFWriter/PDFParser.cpp"], "source_commit_url": "https://github.com/julianhille/MuhammaraJS/commit/1890fb555eaf171db79b73fdc3ea543bbd63c002", "target_commit_url": "https://github.com/julianhille/MuhammaraJS/commit/90b278d09f16062d93a4160ef0a54d449d739c51", "backporting_type": "cross_branch", "source_repo": "https://github.com/julianhille/MuhammaraJS", "target_repo": "https://github.com/julianhille/MuhammaraJS", "backporting_analysis": {"source_repo": "https://github.com/julianhille/MuhammaraJS", "target_repo": "https://github.com/julianhille/MuhammaraJS", "source_commit": "1890fb555eaf171db79b73fdc3ea543bbd63c002", "target_commit": "90b278d09f16062d93a4160ef0a54d449d739c51", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-3821_systemd_systemd_systemd_systemd_8d2d0895", "cve_id": "CVE-2022-3821", "patch_type": "cross_branch", "projects": {"source": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}, "target": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9102c625a673a3246d7e73d8737f3494446bad4e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "8d2d0895229cfbe39c1c5c16e61e426812a72e8b"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-3821", "year": 2022, "cwe_types": ["CWE-193"], "bug_type": "CWE-193", "source_commit_date": "2022-07-07T09:27:02Z", "target_commit_date": "2022-07-07T09:27:02Z", "source_files_changed": ["src/basic/time-util.c", "src/test/test-time-util.c"], "target_files_changed": [], "source_commit_url": "https://github.com/systemd/systemd/commit/9102c625a673a3246d7e73d8737f3494446bad4e", "target_commit_url": "https://github.com/systemd/systemd/commit/8d2d0895229cfbe39c1c5c16e61e426812a72e8b", "backporting_type": "cross_branch", "source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "backporting_analysis": {"source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "source_commit": "9102c625a673a3246d7e73d8737f3494446bad4e", "target_commit": "8d2d0895229cfbe39c1c5c16e61e426812a72e8b", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-3959_drogonframework_drogon_drogonframework_drogon_c0d48da9", "cve_id": "CVE-2022-3959", "patch_type": "cross_branch", "projects": {"source": {"name": "drogonframework/drogon", "repo": "https://github.com/drogonframework/drogon", "language": "C"}, "target": {"name": "drogonframework/drogon", "repo": "https://github.com/drogonframework/drogon", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "120cc0ea21711476c3f3f3152935b177ac4e7102"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c0d48da99f66aaada17bcd28b07741cac8697647"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-3959", "year": 2022, "cwe_types": ["CWE-330", "CWE-330"], "bug_type": "CWE-330", "source_commit_date": "2022-11-09T16:24:08Z", "target_commit_date": "2022-11-10T05:41:21Z", "source_files_changed": [], "target_files_changed": ["lib/inc/drogon/HttpRequest.h", "lib/inc/drogon/HttpResponse.h", "lib/inc/drogon/utils/Utilities.h", "lib/src/HttpRequestImpl.h", "lib/src/HttpResponseImpl.h", "lib/src/Utilities.cc"], "source_commit_url": "https://github.com/drogonframework/drogon/commit/120cc0ea21711476c3f3f3152935b177ac4e7102", "target_commit_url": "https://github.com/drogonframework/drogon/commit/c0d48da99f66aaada17bcd28b07741cac8697647", "backporting_type": "cross_branch", "source_repo": "https://github.com/drogonframework/drogon", "target_repo": "https://github.com/drogonframework/drogon", "backporting_analysis": {"source_repo": "https://github.com/drogonframework/drogon", "target_repo": "https://github.com/drogonframework/drogon", "source_commit": "120cc0ea21711476c3f3f3152935b177ac4e7102", "target_commit": "c0d48da99f66aaada17bcd28b07741cac8697647", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-41909_tensorflow_tensorflow_tensorflow_tensorflow_bf594d08", "cve_id": "CVE-2022-41909", "patch_type": "cross_branch", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "660ce5a89eb6766834bdc303d2ab3902aef99d3d"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bf594d08d377dc6a3354d9fdb494b32d45f91971"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-41909", "year": 2022, "cwe_types": ["CWE-20", "CWE-476"], "bug_type": "CWE-20", "source_commit_date": "2022-09-14T21:54:10Z", "target_commit_date": "2022-09-15T17:18:37Z", "source_files_changed": ["tensorflow/core/kernels/composite_tensor_ops.cc"], "target_files_changed": ["tensorflow/core/kernels/composite_tensor_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/660ce5a89eb6766834bdc303d2ab3902aef99d3d", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/bf594d08d377dc6a3354d9fdb494b32d45f91971", "backporting_type": "cross_branch", "source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "660ce5a89eb6766834bdc303d2ab3902aef99d3d", "target_commit": "bf594d08d377dc6a3354d9fdb494b32d45f91971", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-4066_david<PERSON><PERSON>_onion_davidmoreno_onion_de8ea938", "cve_id": "CVE-2022-4066", "patch_type": "cross_branch", "projects": {"source": {"name": "davidmoreno/onion", "repo": "https://github.com/davidmoreno/onion", "language": "C"}, "target": {"name": "davidmoreno/onion", "repo": "https://github.com/davidmoreno/onion", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "89cc1c0bdd9511563b0de4d713e57ebce6ebe6f3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "de8ea938342b36c28024fd8393ebc27b8442a161"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-4066", "year": 2022, "cwe_types": ["CWE-404"], "bug_type": "CWE-404", "source_commit_date": "2022-09-05T19:12:57Z", "target_commit_date": "2022-09-05T20:06:52Z", "source_files_changed": [], "target_files_changed": ["src/onion/low.h", "src/onion/response.c"], "source_commit_url": "https://github.com/davidmoreno/onion/commit/89cc1c0bdd9511563b0de4d713e57ebce6ebe6f3", "target_commit_url": "https://github.com/davidmoreno/onion/commit/de8ea938342b36c28024fd8393ebc27b8442a161", "backporting_type": "cross_branch", "source_repo": "https://github.com/davidmoreno/onion", "target_repo": "https://github.com/davidmoreno/onion", "backporting_analysis": {"source_repo": "https://github.com/davidmoreno/onion", "target_repo": "https://github.com/davidmoreno/onion", "source_commit": "89cc1c0bdd9511563b0de4d713e57ebce6ebe6f3", "target_commit": "de8ea938342b36c28024fd8393ebc27b8442a161", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-45866_PierreLvx_qpress_EvgeniyPatlan_qpress_ddb31209", "cve_id": "CVE-2022-45866", "patch_type": "cross_repo", "projects": {"source": {"name": "PierreLvx/qpress", "repo": "https://github.com/PierreLvx/qpress", "language": "C"}, "target": {"name": "EvgeniyPatlan/qpress", "repo": "https://github.com/EvgeniyPatlan/qpress", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "02a79a793f56e86e2014a606647b158b246811e3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ddb312090ebd5794e81bc6fb1dfb4e79eda48761"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-45866", "year": 2022, "cwe_types": ["CWE-22", "CWE-22"], "bug_type": "CWE-22", "source_commit_date": "2021-07-14T07:35:58Z", "target_commit_date": "2022-08-19T21:33:18Z", "source_files_changed": [], "target_files_changed": ["qpress.cpp"], "source_commit_url": "https://github.com/PierreLvx/qpress/commit/02a79a793f56e86e2014a606647b158b246811e3", "target_commit_url": "https://github.com/EvgeniyPatlan/qpress/commit/ddb312090ebd5794e81bc6fb1dfb4e79eda48761", "backporting_type": "cross_repo", "source_repo": "https://github.com/PierreLvx/qpress", "target_repo": "https://github.com/EvgeniyPatlan/qpress", "backporting_analysis": {"source_repo": "https://github.com/PierreLvx/qpress", "target_repo": "https://github.com/EvgeniyPatlan/qpress", "source_commit": "02a79a793f56e86e2014a606647b158b246811e3", "target_commit": "ddb312090ebd5794e81bc6fb1dfb4e79eda48761", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-45873_systemd_systemd_systemd_systemd_076b807b", "cve_id": "CVE-2022-45873", "patch_type": "cross_branch", "projects": {"source": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}, "target": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "de76643b3fbd49dbd3713e59d59e206fc28cc3f0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "076b807be472630692c5348c60d0c2b7b28ad437"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-45873", "year": 2022, "cwe_types": ["CWE-400", "CWE-400"], "bug_type": "denial of service", "source_commit_date": "2022-09-30T12:14:33Z", "target_commit_date": "2022-10-18T16:23:53Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/systemd/systemd/commit/de76643b3fbd49dbd3713e59d59e206fc28cc3f0", "target_commit_url": "https://github.com/systemd/systemd/commit/076b807be472630692c5348c60d0c2b7b28ad437", "backporting_type": "cross_branch", "source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "backporting_analysis": {"source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "source_commit": "de76643b3fbd49dbd3713e59d59e206fc28cc3f0", "target_commit": "076b807be472630692c5348c60d0c2b7b28ad437", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-45909_drachtio_drachtio-server_drachtio_drachtio-server_a63d0185", "cve_id": "CVE-2022-45909", "patch_type": "cross_branch", "projects": {"source": {"name": "drachtio/drachtio-server", "repo": "https://github.com/drachtio/drachtio-server", "language": "C"}, "target": {"name": "drachtio/drachtio-server", "repo": "https://github.com/drachtio/drachtio-server", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "270b952738682c5f5e252482dbe219793aa6dc55"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a63d01854987d9fd846cdc9265af38ee9eb72490"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-45909", "year": 2022, "cwe_types": ["CWE-125", "CWE-125"], "bug_type": "CWE-125", "source_commit_date": "2022-11-06T15:53:48Z", "target_commit_date": "2022-11-06T16:27:55Z", "source_files_changed": [], "target_files_changed": ["src/drachtio.cpp", "src/sip-dialog-controller.cpp"], "source_commit_url": "https://github.com/drachtio/drachtio-server/commit/270b952738682c5f5e252482dbe219793aa6dc55", "target_commit_url": "https://github.com/drachtio/drachtio-server/commit/a63d01854987d9fd846cdc9265af38ee9eb72490", "backporting_type": "cross_branch", "source_repo": "https://github.com/drachtio/drachtio-server", "target_repo": "https://github.com/drachtio/drachtio-server", "backporting_analysis": {"source_repo": "https://github.com/drachtio/drachtio-server", "target_repo": "https://github.com/drachtio/drachtio-server", "source_commit": "270b952738682c5f5e252482dbe219793aa6dc55", "target_commit": "a63d01854987d9fd846cdc9265af38ee9eb72490", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-41957_juli<PERSON><PERSON><PERSON>_MuhammaraJS_juli<PERSON><PERSON><PERSON>_MuhammaraJS_bb64937c", "cve_id": "CVE-2022-41957", "patch_type": "cross_branch", "projects": {"source": {"name": "juli<PERSON><PERSON><PERSON>/MuhammaraJS", "repo": "https://github.com/julianhille/MuhammaraJS", "language": "C"}, "target": {"name": "juli<PERSON><PERSON><PERSON>/MuhammaraJS", "repo": "https://github.com/julianhille/MuhammaraJS", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6ce062318e33cce633fbcadcf8e6886215e3c648"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bb64937c9f80cbae720a38ae0efd43df8c5bc251"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-41957", "year": 2022, "cwe_types": ["CWE-690"], "bug_type": "CWE-690", "source_commit_date": "2022-11-16T07:10:27Z", "target_commit_date": "2022-11-24T08:43:37Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/julianhille/MuhammaraJS/commit/6ce062318e33cce633fbcadcf8e6886215e3c648", "target_commit_url": "https://github.com/julianhille/MuhammaraJS/commit/bb64937c9f80cbae720a38ae0efd43df8c5bc251", "backporting_type": "cross_branch", "source_repo": "https://github.com/julianhille/MuhammaraJS", "target_repo": "https://github.com/julianhille/MuhammaraJS", "backporting_analysis": {"source_repo": "https://github.com/julianhille/MuhammaraJS", "target_repo": "https://github.com/julianhille/MuhammaraJS", "source_commit": "6ce062318e33cce633fbcadcf8e6886215e3c648", "target_commit": "bb64937c9f80cbae720a38ae0efd43df8c5bc251", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23476_sparklemotion_nokogiri_sparklemotion_nokogiri_85410e38", "cve_id": "CVE-2022-23476", "patch_type": "cross_branch", "projects": {"source": {"name": "sparklemotion/nokogiri", "repo": "https://github.com/sparklemotion/nokogiri", "language": "C"}, "target": {"name": "sparklemotion/nokogiri", "repo": "https://github.com/sparklemotion/nokogiri", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9fe0761c47c0d4270d1a5220cfd25de080350d50"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "85410e38410f670cbbc8c5b00d07b843caee88ce"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23476", "year": 2022, "cwe_types": ["CWE-252", "CWE-476"], "bug_type": "CWE-252", "source_commit_date": "2022-12-06T06:51:37Z", "target_commit_date": "2022-12-07T21:25:07Z", "source_files_changed": ["ext/nokogiri/xml_reader.c"], "target_files_changed": ["ext/nokogiri/xml_reader.c"], "source_commit_url": "https://github.com/sparklemotion/nokogiri/commit/9fe0761c47c0d4270d1a5220cfd25de080350d50", "target_commit_url": "https://github.com/sparklemotion/nokogiri/commit/85410e38410f670cbbc8c5b00d07b843caee88ce", "backporting_type": "cross_branch", "source_repo": "https://github.com/sparklemotion/nokogiri", "target_repo": "https://github.com/sparklemotion/nokogiri", "backporting_analysis": {"source_repo": "https://github.com/sparklemotion/nokogiri", "target_repo": "https://github.com/sparklemotion/nokogiri", "source_commit": "9fe0761c47c0d4270d1a5220cfd25de080350d50", "target_commit": "85410e38410f670cbbc8c5b00d07b843caee88ce", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-47517_davehorton_sofia-sip_davehorton_sofia-sip_bfc79d85", "cve_id": "CVE-2022-47517", "patch_type": "cross_branch", "projects": {"source": {"name": "dave<PERSON>ton/sofia-sip", "repo": "https://github.com/davehorton/sofia-sip", "language": "C"}, "target": {"name": "dave<PERSON>ton/sofia-sip", "repo": "https://github.com/davehorton/sofia-sip", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "22c1bd191f0acbf11f0c0fbea1845d9bf9dcd47e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bfc79d85c8f3a4798a3305fb98f5a11c11d0d29f"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-47517", "year": 2022, "cwe_types": ["CWE-193", "CWE-193"], "bug_type": "CWE-193", "source_commit_date": "2022-11-28T00:47:24Z", "target_commit_date": "2022-11-28T01:06:46Z", "source_files_changed": ["libsofia-sip-ua/url/url.c"], "target_files_changed": ["libsofia-sip-ua/url/url.c"], "source_commit_url": "https://github.com/davehorton/sofia-sip/commit/22c1bd191f0acbf11f0c0fbea1845d9bf9dcd47e", "target_commit_url": "https://github.com/davehorton/sofia-sip/commit/bfc79d85c8f3a4798a3305fb98f5a11c11d0d29f", "backporting_type": "cross_branch", "source_repo": "https://github.com/davehorton/sofia-sip", "target_repo": "https://github.com/davehorton/sofia-sip", "backporting_analysis": {"source_repo": "https://github.com/davehorton/sofia-sip", "target_repo": "https://github.com/davehorton/sofia-sip", "source_commit": "22c1bd191f0acbf11f0c0fbea1845d9bf9dcd47e", "target_commit": "bfc79d85c8f3a4798a3305fb98f5a11c11d0d29f", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2020-36619_EliasOenal_multimon-ng_EliasOenal_multimon-ng_50025846", "cve_id": "CVE-2020-36619", "patch_type": "cross_branch", "projects": {"source": {"name": "EliasOenal/multimon-ng", "repo": "https://github.com/EliasOenal/multimon-ng", "language": "C"}, "target": {"name": "EliasOenal/multimon-ng", "repo": "https://github.com/EliasOenal/multimon-ng", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e5a51c508ef952e81a6da25b43034dd1ed023c07"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "500258464f7fc8ee04e9b70b0f0727320f7bde1e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-36619", "year": 2020, "cwe_types": ["CWE-119", "CWE-134"], "bug_type": "memory overflow", "source_commit_date": "2020-05-31T22:24:00Z", "target_commit_date": "2020-05-31T22:28:18Z", "source_files_changed": ["demod_flex.c"], "target_files_changed": [], "source_commit_url": "https://github.com/EliasOenal/multimon-ng/commit/e5a51c508ef952e81a6da25b43034dd1ed023c07", "target_commit_url": "https://github.com/EliasOenal/multimon-ng/commit/500258464f7fc8ee04e9b70b0f0727320f7bde1e", "backporting_type": "cross_branch", "source_repo": "https://github.com/EliasOenal/multimon-ng", "target_repo": "https://github.com/EliasOenal/multimon-ng", "backporting_analysis": {"source_repo": "https://github.com/EliasOenal/multimon-ng", "target_repo": "https://github.com/EliasOenal/multimon-ng", "source_commit": "e5a51c508ef952e81a6da25b43034dd1ed023c07", "target_commit": "500258464f7fc8ee04e9b70b0f0727320f7bde1e", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-47932_brave_brave-core_brave_brave-core_e7330966", "cve_id": "CVE-2022-47932", "patch_type": "cross_branch", "projects": {"source": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}, "target": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "498ade8c00de9a3393698deee17f7e89b4d9ac39"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e73309665508c17e48a67e302d3ab02a38d3ef50"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-47932", "year": 2022, "cwe_types": ["CWE-400"], "bug_type": "denial of service", "source_commit_date": "2022-07-18T07:48:25Z", "target_commit_date": "2022-07-19T14:04:56Z", "source_files_changed": [], "target_files_changed": ["browser/net/ipfs_redirect_network_delegate_helper.cc", "browser/net/ipfs_redirect_network_delegate_helper_unittest.cc"], "source_commit_url": "https://github.com/brave/brave-core/commit/498ade8c00de9a3393698deee17f7e89b4d9ac39", "target_commit_url": "https://github.com/brave/brave-core/commit/e73309665508c17e48a67e302d3ab02a38d3ef50", "backporting_type": "cross_branch", "source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "backporting_analysis": {"source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "source_commit": "498ade8c00de9a3393698deee17f7e89b4d9ac39", "target_commit": "e73309665508c17e48a67e302d3ab02a38d3ef50", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-47933_brave_brave-core_brave_brave-core_7ef8cb2f", "cve_id": "CVE-2022-47933", "patch_type": "cross_branch", "projects": {"source": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}, "target": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "0fb80b9ff5a040e291dffb8fba2fe3129bd6de43"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7ef8cb2f232abdf59ec9c3c99a086a14b972bc56"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-47933", "year": 2022, "cwe_types": ["CWE-755", "CWE-755"], "bug_type": "CWE-755", "source_commit_date": "2022-06-28T14:39:07Z", "target_commit_date": "2022-07-02T06:18:47Z", "source_files_changed": [], "target_files_changed": ["browser/net/ipfs_redirect_network_delegate_helper.cc", "browser/net/ipfs_redirect_network_delegate_helper_unittest.cc"], "source_commit_url": "https://github.com/brave/brave-core/commit/0fb80b9ff5a040e291dffb8fba2fe3129bd6de43", "target_commit_url": "https://github.com/brave/brave-core/commit/7ef8cb2f232abdf59ec9c3c99a086a14b972bc56", "backporting_type": "cross_branch", "source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "backporting_analysis": {"source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "source_commit": "0fb80b9ff5a040e291dffb8fba2fe3129bd6de43", "target_commit": "7ef8cb2f232abdf59ec9c3c99a086a14b972bc56", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-47934_brave_brave-core_brave_brave-core_82d8e390", "cve_id": "CVE-2022-47934", "patch_type": "cross_branch", "projects": {"source": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}, "target": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "73304545a3e1f7897114258867996dd02d26fc51"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "82d8e39043e691e0492519126437275511ee87e8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-47934", "year": 2022, "cwe_types": ["CWE-400"], "bug_type": "denial of service", "source_commit_date": "2022-08-01T14:29:44Z", "target_commit_date": "2022-08-08T19:22:25Z", "source_files_changed": [], "target_files_changed": ["browser/brave_content_browser_client.cc", "browser/ipfs/ipfs_subframe_navigation_throttle.cc", "browser/ipfs/ipfs_subframe_navigation_throttle.h", "browser/ipfs/test/ipfs_service_browsertest.cc", "browser/net/ipfs_redirect_network_delegate_helper.cc", "browser/net/ipfs_redirect_network_delegate_helper_unittest.cc"], "source_commit_url": "https://github.com/brave/brave-core/commit/73304545a3e1f7897114258867996dd02d26fc51", "target_commit_url": "https://github.com/brave/brave-core/commit/82d8e39043e691e0492519126437275511ee87e8", "backporting_type": "cross_branch", "source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "backporting_analysis": {"source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "source_commit": "73304545a3e1f7897114258867996dd02d26fc51", "target_commit": "82d8e39043e691e0492519126437275511ee87e8", "analysis_result": "cross_branch"}}}]
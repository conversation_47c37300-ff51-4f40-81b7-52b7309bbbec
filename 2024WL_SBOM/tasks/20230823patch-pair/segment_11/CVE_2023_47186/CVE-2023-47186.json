{"cve_id": "CVE-2023-47186", "published_date": "2023-11-06T12:15:08.760", "last_modified_date": "2024-11-21T08:29:55.293", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Kadence WP Kadence WooCommerce Email Designer plugin <= 1.5.11 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Kadence WP Kadence WooCommerce Email Designer en versiones &lt;= 1.5.11."}], "references": [{"url": "https://patchstack.com/database/vulnerability/kadence-woocommerce-email-designer/wordpress-kadence-woocommerce-email-designer-plugin-1-5-11-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
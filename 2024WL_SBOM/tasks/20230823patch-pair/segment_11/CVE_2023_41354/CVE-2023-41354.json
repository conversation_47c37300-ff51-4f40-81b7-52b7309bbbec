{"cve_id": "CVE-2023-41354", "published_date": "2023-11-03T06:15:07.527", "last_modified_date": "2024-11-21T08:21:08.270", "descriptions": [{"lang": "en", "value": "Chunghwa Telecom NOKIA G-040W-Q Firewall function does not block ICMP TIMESTAMP requests by default, an unauthenticated remote attacker can exploit this vulnerability by sending a crafted package, resulting in partially sensitive information exposed to an actor."}, {"lang": "es", "value": "La función Chunghwa Telecom NOKIA G-040W-Q Firewall no bloquea las solicitudes ICMP TIMESTAMP de forma predeterminada; un atacante remoto no autenticado puede explotar esta vulnerabilidad enviando un paquete manipulado, lo que resulta en información parcialmente confidencial expuesta a un actor."}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7504-c6a5e-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-5354", "published_date": "2023-11-06T21:15:09.790", "last_modified_date": "2025-02-26T22:15:13.653", "descriptions": [{"lang": "en", "value": "The Awesome Support WordPress plugin before 6.1.5 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin."}, {"lang": "es", "value": "El complemento Awesome Support de WordPress anterior a 6.1.5 no sanitiza ni escapa un parámetro antes de devolverlo a la página, lo que genera un Cross-Site Scripting (XSS) Reflejado que podría usarse contra usuarios con privilegios elevados, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/aa380524-031d-4e49-9d0b-96e62d54557f", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
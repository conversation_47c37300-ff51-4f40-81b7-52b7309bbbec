{"cve_id": "CVE-2023-39299", "published_date": "2023-11-03T17:15:08.900", "last_modified_date": "2024-11-21T08:15:06.383", "descriptions": [{"lang": "en", "value": "A path traversal vulnerability has been reported to affect Music Station. If exploited, the vulnerability could allow users to read the contents of unexpected files and expose sensitive data via a network.\n\nWe have already fixed the vulnerability in the following versions:\nMusic Station 4.8.11 and later\nMusic Station 5.1.16 and later\nMusic Station 5.3.23 and later\n"}, {"lang": "es", "value": "Se ha informado que una vulnerabilidad de path traversal que afecta a Music Station. Si se explota, la vulnerabilidad podría permitir a los usuarios leer el contenido de archivos inesperados y exponer datos confidenciales a través de una red. Ya hemos solucionado la vulnerabilidad en las siguientes versiones: Music Station 4.8.11 y posteriores Music Station 5.1.16 y posteriores Music Station 5.3.23 y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-61", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
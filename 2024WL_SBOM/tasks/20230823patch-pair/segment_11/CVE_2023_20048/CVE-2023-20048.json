{"cve_id": "CVE-2023-20048", "published_date": "2023-11-01T18:15:08.907", "last_modified_date": "2024-11-26T16:09:02.407", "descriptions": [{"lang": "en", "value": "A vulnerability in the web services interface of Cisco Firepower Management Center (FMC) Software could allow an authenticated, remote attacker to execute certain unauthorized configuration commands on a Firepower Threat Defense (FTD) device that is managed by the FMC Software. This vulnerability is due to insufficient authorization of configuration commands that are sent through the web service interface. An attacker could exploit this vulnerability by authenticating to the FMC web services interface and sending a crafted HTTP request to an affected device. A successful exploit could allow the attacker to execute certain configuration commands on the targeted FTD device. To successfully exploit this vulnerability, an attacker would need valid credentials on the FMC Software."}, {"lang": "es", "value": "Una vulnerabilidad en la interfaz de servicios web del software Cisco Firepower Management Center (FMC) podría permitir que un atacante remoto autenticado ejecute ciertos comandos de configuración no autorizados en un dispositivo Firepower Threat Defense (FTD) administrado por el software FMC. Esta vulnerabilidad se debe a una autorización insuficiente de los comandos de configuración que se envían a través de la interfaz del servicio web. Un atacante podría aprovechar esta vulnerabilidad autenticándose en la interfaz de servicios web de FMC y enviando una solicitud HTTP manipulada a un dispositivo afectado. Un exploit exitoso podría permitir al atacante ejecutar ciertos comandos de configuración en el dispositivo FTD objetivo. Para explotar con éxito esta vulnerabilidad, un atacante necesitaría credenciales válidas en el software FMC."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-fmc-cmd-inj-29MP49hN", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-32837", "published_date": "2023-11-06T04:15:07.973", "last_modified_date": "2025-04-25T16:15:25.410", "descriptions": [{"lang": "en", "value": "In video, there is a possible out of bounds write due to a missing bounds check. This could lead to local escalation of privilege with no additional execution privileges needed. User interaction is not needed for exploitation. Patch ID: ALPS08235273; Issue ID: ALPS08250357."}, {"lang": "es", "value": "En el vídeo, hay una posible escritura fuera de los límites debido a una comprobación de los límites faltantes. Esto podría conducir a una escalada local de privilegios sin necesidad de permisos de ejecución adicionales. La interacción del usuario no es necesaria para la explotación. ID de parche: ALPS08235273; ID del problema: ALPS08250357."}], "references": [{"url": "http://packetstormsecurity.com/files/175665/mtk-jpeg-Driver-Out-Of-Bounds-Read-Write.html", "source": "<EMAIL>", "tags": []}, {"url": "https://corp.mediatek.com/product-security-bulletin/November-2023", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
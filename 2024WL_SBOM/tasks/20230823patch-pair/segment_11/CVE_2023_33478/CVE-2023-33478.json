{"cve_id": "CVE-2023-33478", "published_date": "2023-11-07T15:15:10.630", "last_modified_date": "2024-11-21T08:05:37.057", "descriptions": [{"lang": "en", "value": "RemoteClinic 2.0 has a SQL injection vulnerability in the ID parameter of /medicines/stocks.php."}, {"lang": "es", "value": "RemoteClinic 2.0 tiene una vulnerabilidad de inyección SQL en el parámetro ID de /medicines/stocks.php."}], "references": [{"url": "https://github.com/remoteclinic/RemoteClinic/issues/22", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}]}
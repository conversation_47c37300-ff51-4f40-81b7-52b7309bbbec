{"cve_id": "CVE-2023-31017", "published_date": "2023-11-02T19:15:41.033", "last_modified_date": "2024-11-21T08:01:15.250", "descriptions": [{"lang": "en", "value": "NVIDIA GPU Display Driver for Windows contains a vulnerability where an attacker may be able to write arbitrary data to privileged locations by using reparse points. A successful exploit of this vulnerability may lead to code execution, denial of service, escalation of privileges, information disclosure, or data tampering."}, {"lang": "es", "value": "NVIDIA GPU Display Driver para Windows contiene una vulnerabilidad en la que un atacante puede escribir datos arbitrarios en ubicaciones privilegiadas mediante el uso de puntos de análisis. Una explotación exitosa de esta vulnerabilidad puede provocar la ejecución de código, denegación de servicio, escalada de privilegios, divulgación de información o manipulación de datos."}], "references": [{"url": "https://nvidia.custhelp.com/app/answers/detail/a_id/5491", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
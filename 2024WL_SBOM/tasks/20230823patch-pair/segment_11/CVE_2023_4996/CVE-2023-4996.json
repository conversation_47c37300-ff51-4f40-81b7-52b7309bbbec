{"cve_id": "CVE-2023-4996", "published_date": "2023-11-06T11:15:09.593", "last_modified_date": "2024-11-21T08:36:25.640", "descriptions": [{"lang": "en", "value": "Netskope was made aware of a security vulnerability in its NSClient product for version 100 & prior where a malicious non-admin user can disable the Netskope client by using a specially-crafted package. The root cause of the problem was a user control code when called by a Windows ServiceController did not validate the permissions associated with the user before executing the user control code. This user control code had permissions to terminate the NSClient service. \n"}, {"lang": "es", "value": "Netskope fue informado de una vulnerabilidad de seguridad en su producto NSClient para la versión 100 y anteriores donde un usuario malintencionado que no sea administrador puede desactivar el cliente Netskope mediante el uso de un paquete especialmente manipulado. La causa principal del problema fue que un código de control de usuario cuando lo llamaba un ServiceController de Windows no validaba los permisos asociados con el usuario antes de ejecutar el código de control de usuario. Este código de control de usuario tenía permisos para finalizar el servicio NSClient."}], "references": [{"url": "https://www.netskope.com/company/security-compliance-and-assurance/security-advisories-and-disclosures/netskope-security-advisory-nskpsa-2023-003", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
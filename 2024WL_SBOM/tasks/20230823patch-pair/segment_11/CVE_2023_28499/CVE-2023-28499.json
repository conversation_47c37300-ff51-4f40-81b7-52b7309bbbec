{"cve_id": "CVE-2023-28499", "published_date": "2023-11-07T18:15:08.457", "last_modified_date": "2024-11-21T07:55:14.317", "descriptions": [{"lang": "en", "value": "Auth. (author+) Stored Cross-Site Scripting (XSS) vulnerability in simonpedge Slide Anything – Responsive Content / HTML Slider and Carousel plugin <= 2.4.9 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Almacenado Autenticado (con permisos de admin o superiores) en el complemento simonpedge Slide Anything – Responsive Content / HTML Slider and Carousel en versiones &lt;= 2.4.9."}], "references": [{"url": "https://patchstack.com/database/vulnerability/slide-anything/wordpress-slide-anything-plugin-2-4-7-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
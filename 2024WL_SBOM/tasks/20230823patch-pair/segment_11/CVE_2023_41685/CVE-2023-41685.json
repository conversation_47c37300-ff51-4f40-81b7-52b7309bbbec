{"cve_id": "CVE-2023-41685", "published_date": "2023-11-06T09:15:08.367", "last_modified_date": "2024-11-21T08:21:29.137", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in ilGhera Woocommerce Support System allows SQL Injection.This issue affects Woocommerce Support System: from n/a through 1.2.1.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en ilGhera Woocommerce Support System permite la inyección de SQL. Este problema afecta Woocommerce Support System: desde n/a hasta 1.2.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wc-support-system/wordpress-woocommerce-support-system-plugin-1-2-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
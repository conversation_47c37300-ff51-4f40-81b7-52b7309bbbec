{"cve_id": "CVE-2023-42802", "published_date": "2023-11-02T14:15:11.440", "last_modified_date": "2024-11-21T08:23:11.213", "descriptions": [{"lang": "en", "value": "GLPI is a free asset and IT management software package. Starting in version 10.0.7 and prior to version 10.0.10, an unverified object instantiation allows one to upload malicious PHP files to unwanted directories. Depending on web server configuration and available system libraries, malicious PHP files can then be executed through a web server request. Version 10.0.10 fixes this issue. As a workaround, remove write access on `/ajax` and `/front` files to the web server."}, {"lang": "es", "value": "GLPI es un paquete de software gratuito de gestión de activos y TI. A partir de la versión 10.0.7 y antes de la versión 10.0.10, la creación de instancias de un objeto no verificado permite cargar archivos PHP maliciosos en directorios no deseados. Dependiendo de la configuración del servidor web y de las librerías del sistema disponibles, se pueden ejecutar archivos PHP maliciosos mediante una solicitud de servidor web. La versión 10.0.10 soluciona este problema. Como workaround, elimine el acceso de escritura en los archivos `/ajax` y `/front` al servidor web."}], "references": [{"url": "https://github.com/glpi-project/glpi/releases/tag/10.0.10", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/glpi-project/glpi/security/advisories/GHSA-rrh2-x4ch-pq3m", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-1194", "published_date": "2023-11-03T08:15:07.490", "last_modified_date": "2025-03-20T17:01:03.870", "descriptions": [{"lang": "en", "value": "An out-of-bounds (OOB) memory read flaw was found in parse_lease_state in the KSMBD implementation of the in-kernel samba server and CIFS in the Linux kernel. When an attacker sends the CREATE command with a malformed payload to KSMBD, due to a missing check of `NameOffset` in the `parse_lease_state()` function, the `create_context` object can access invalid memory."}, {"lang": "es", "value": "Se encontró una falla de lectura de memoria Out-Of-Bounds (OOB) en parse_lease_state en la implementación KSMBD del servidor samba en el kernel y CIFS en el kernel de Linux. Cuando un atacante envía el comando CREATE con un payload mal formada a KSMBD, debido a una verificación faltante de `NameOffset` en la función `parse_lease_state()`, el objeto `create_context` puede acceder a memoria no válida."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-1194", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2154176", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231221-0006/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.spinics.net/lists/stable-commits/msg303065.html", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}
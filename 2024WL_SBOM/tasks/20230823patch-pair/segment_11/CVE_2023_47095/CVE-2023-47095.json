{"cve_id": "CVE-2023-47095", "published_date": "2023-11-01T00:15:09.423", "last_modified_date": "2024-11-21T08:29:45.580", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting (XSS) vulnerability in the Custom fields of Edit Virtual Server under System Customization in Virtualmin 7.7 allows remote attackers to inject arbitrary web script or HTML via the Batch Label field while details of Virtual Server."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en Custom fields de Edit Virtual Server bajo System Customization en Virtualmin 7.7 permite a atacantes remotos inyectar script web o HTML arbitrario a través del campo Etiqueta de Lote mientras se muestran detalles del Servidor Virtual."}], "references": [{"url": "https://github.com/pavanughade43/Virtualmin-7.7/blob/main/CVE-2023-47095", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
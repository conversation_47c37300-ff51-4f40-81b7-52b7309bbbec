{"cve_id": "CVE-2023-46448", "published_date": "2023-11-01T22:15:08.730", "last_modified_date": "2024-11-21T08:28:31.983", "descriptions": [{"lang": "en", "value": "Reflected Cross-Site Scripting (XSS) vulnerability in dmpop Mejiro Commit Versions Prior To 3096393 allows attackers to run arbitrary code via crafted string in metadata of uploaded images."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Reflejada en dmpop Mejiro Commit Versions anteriores a 3096393 permite a los atacantes ejecutar código arbitrario a través de una cadena manipulada en metadatos de imágenes cargadas."}], "references": [{"url": "https://blog.0xzon.dev/2023-10-15-Mejiro-Reflected-XSS-Via-Remote-File-Inclusion-CVE-2023-46448/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/dmpop/mejiro/commit/309639339f5816408865902befe8c90cb6862537", "source": "<EMAIL>", "tags": ["Patch"]}]}
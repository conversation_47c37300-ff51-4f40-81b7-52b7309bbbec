{"cve_id": "CVE-2023-5860", "published_date": "2023-11-02T12:15:09.720", "last_modified_date": "2024-11-21T08:42:39.100", "descriptions": [{"lang": "en", "value": "The Icons Font Loader plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the upload function in all versions up to, and including, 1.1.2. This makes it possible for authenticated attackers, with administrator-level access and above, to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento Icons Font Loader para WordPress es vulnerable a cargas de archivos arbitrarias debido a la falta de validación del tipo de archivo en la función de carga en todas las versiones hasta la 1.1.2 incluida. Esto hace posible que atacantes autenticados, con acceso de nivel de administrador y superior, carguen archivos arbitrarios en el servidor del sitio afectado, lo que puede hacer posible la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/2987296/icons-font-loader", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/12a9fbe8-445a-478a-b6ce-cd669ccb6a2d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
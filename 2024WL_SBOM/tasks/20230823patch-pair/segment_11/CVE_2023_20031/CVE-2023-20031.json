{"cve_id": "CVE-2023-20031", "published_date": "2023-11-01T18:15:08.770", "last_modified_date": "2024-11-21T07:40:23.797", "descriptions": [{"lang": "en", "value": "A vulnerability in the SSL/TLS certificate handling of Snort 3 Detection Engine integration with Cisco Firepower Threat Defense (FTD) Software could allow an unauthenticated, remote attacker to cause the Snort 3 detection engine to restart. This vulnerability is due to a logic error that occurs when an SSL/TLS certificate that is under load is accessed when it is initiating an SSL connection. Under specific, time-based constraints, an attacker could exploit this vulnerability by sending a high rate of SSL/TLS connection requests to be inspected by the Snort 3 detection engine on an affected device. A successful exploit could allow the attacker to cause the Snort 3 detection engine to reload, resulting in either a bypass or a denial of service (DoS) condition, depending on device configuration. The Snort detection engine will restart automatically. No manual intervention is required."}, {"lang": "es", "value": "Una vulnerabilidad en el manejo de certificados SSL/TLS de la integración del motor de detección Snort 3 con el software Cisco Firepower Threat Defense (FTD) podría permitir que un atacante remoto no autenticado provoque que el motor de detección Snort 3 se reinicie. Esta vulnerabilidad se debe a un error lógico que ocurre cuando se accede a un certificado SSL/TLS que está bajo carga cuando se inicia una conexión SSL. Bajo limitaciones específicas basadas en el tiempo, un atacante podría aprovechar esta vulnerabilidad enviando una alta tasa de solicitudes de conexión SSL/TLS para ser inspeccionadas por el motor de detección Snort 3 en un dispositivo afectado. Un exploit exitoso podría permitir al atacante hacer que el motor de detección de Snort 3 se recargue, lo que resultaría en una condición de omisión o Denegación de Servicio (DoS), dependiendo de la configuración del dispositivo. El motor de detección de Snort se reiniciará automáticamente. No se requiere intervención manual."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftd-snort3-8U4HHxH8", "source": "<EMAIL>", "tags": ["Mitigation", "Vendor Advisory"]}]}
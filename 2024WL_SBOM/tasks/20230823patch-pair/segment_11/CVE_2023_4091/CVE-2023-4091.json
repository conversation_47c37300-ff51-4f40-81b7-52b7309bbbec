{"cve_id": "CVE-2023-4091", "published_date": "2023-11-03T08:15:08.197", "last_modified_date": "2024-11-21T08:34:22.283", "descriptions": [{"lang": "en", "value": "A vulnerability was discovered in Samba, where the flaw allows SMB clients to truncate files, even with read-only permissions when the Samba VFS module \"acl_xattr\" is configured with \"acl_xattr:ignore system acls = yes\". The SMB protocol allows opening files when the client requests read-only access but then implicitly truncates the opened file to 0 bytes if the client specifies a separate OVERWRITE create disposition request. The issue arises in configurations that bypass kernel file system permissions checks, relying solely on Samba's permissions."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad en Samba, donde la falla permite a los clientes SMB truncar archivos, incluso con permisos de solo lectura cuando el módulo Samba VFS \"acl_xattr\" está configurado con \"acl_xattr:ignore system acls = yes\". El protocolo SMB permite abrir archivos cuando el cliente solicita acceso de solo lectura, pero luego trunca implícitamente el archivo abierto a 0 bytes si el cliente especifica una solicitud de disposición de creación de SOBRESCRITURA separada. El problema surge en configuraciones que omiten las comprobaciones de permisos del sistema de archivos del kernel y dependen únicamente de los permisos de Samba."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6209", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6744", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7371", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7408", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7464", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7467", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-4091", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2241882", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://bugzilla.samba.org/show_bug.cgi?id=15439", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://www.samba.org/samba/security/CVE-2023-4091.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2024/04/msg00015.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/ZUMVALLFFDFC53JZMUWA6HPD7HUGAP5I/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231124-0002/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
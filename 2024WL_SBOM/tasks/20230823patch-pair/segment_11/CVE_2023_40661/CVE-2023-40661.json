{"cve_id": "CVE-2023-40661", "published_date": "2023-11-06T17:15:11.830", "last_modified_date": "2024-11-21T08:19:55.537", "descriptions": [{"lang": "en", "value": "Several memory vulnerabilities were identified within the OpenSC packages, particularly in the card enrollment process using pkcs15-init when a user or administrator enrolls cards. To take advantage of these flaws, an attacker must have physical access to the computer system and employ a custom-crafted USB device or smart card to manipulate responses to APDUs. This manipulation can potentially allow \r\ncompromise key generation, certificate loading, and other card management operations during enrollment."}, {"lang": "es", "value": "Se identificaron varias vulnerabilidades de memoria dentro de los paquetes OpenSC, particularmente en el proceso de inscripción de tarjetas usando pkcs15-init cuando un usuario o administrador registra tarjetas. Para aprovechar estas fallas, un atacante debe tener acceso físico al sistema informático y emplear un dispositivo USB o una tarjeta inteligente hechos a medida para manipular las respuestas a las APDU. Esta manipulación puede permitir potencialmente comprometer la generación de claves, la carga de certificados y otras operaciones de administración de tarjetas durante la inscripción."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:7876", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7879", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-40661", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2240913", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/OpenSC/OpenSC/issues/2792#issuecomment-1674806651", "source": "<EMAIL>", "tags": ["VDB Entry"]}, {"url": "https://github.com/OpenSC/OpenSC/releases/tag/0.24.0-rc1", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/OpenSC/OpenSC/wiki/OpenSC-security-advisories", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2023/12/13/3", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2023/11/msg00024.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/3CPQOMCDWFRBMEFR5VK4N5MMXXU42ODE/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/GLYEFIBBA37TK3UNMZN5NOJ7IWCIXLQP/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
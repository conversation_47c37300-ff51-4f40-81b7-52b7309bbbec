{"cve_id": "CVE-2023-42553", "published_date": "2023-11-07T08:15:23.367", "last_modified_date": "2025-03-06T16:15:43.477", "descriptions": [{"lang": "en", "value": "Improper authorization verification vulnerability in Samsung Email prior to version ******** allows attackers to read sandbox data of email."}, {"lang": "es", "value": "Una vulnerabilidad de verificación de autorización inadecuada en Samsung Email anterior a la versión ******** permite a los atacantes leer datos de la zona de pruebas del correo electrónico."}], "references": [{"url": "https://security.samsungmobile.com/serviceWeb.smsb?year=2023&month=11", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
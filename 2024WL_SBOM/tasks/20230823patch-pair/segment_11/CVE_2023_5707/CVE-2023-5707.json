{"cve_id": "CVE-2023-5707", "published_date": "2023-11-03T13:15:08.870", "last_modified_date": "2024-11-21T08:42:19.530", "descriptions": [{"lang": "en", "value": "The SEO Slider plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'slider' shortcode and post meta in all versions up to, and including, 1.1.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento SEO Slider para WordPress es vulnerable a Cross-Site Scripting almacenado a través del código abreviado 'control deslizante' del complemento y meta de publicación en todas las versiones hasta la 1.1.0 incluida debido a una sanitización de entrada insuficiente y a un escape de salida en los atributos proporcionados por el usuario. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/seo-slider/trunk/includes/shortcode.php?rev=2367856#L68", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://plugins.trac.wordpress.org/browser/seo-slider/trunk/includes/shortcode.php?rev=2367856#L71", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://plugins.trac.wordpress.org/changeset/2987802/seo-slider#file3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/32bc88a7-93ed-4d67-9383-b6d935a0df4d?source=cve", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}]}
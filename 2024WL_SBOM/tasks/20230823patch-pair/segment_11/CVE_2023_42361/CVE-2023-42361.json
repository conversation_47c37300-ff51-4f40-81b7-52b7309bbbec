{"cve_id": "CVE-2023-42361", "published_date": "2023-11-07T22:15:11.167", "last_modified_date": "2024-11-21T08:22:28.087", "descriptions": [{"lang": "en", "value": "Local File Inclusion vulnerability in Midori-global Better PDF Exporter for Jira Server and Jira Data Center v.10.3.0 and before allows an attacker to view arbitrary files and cause other impacts via use of crafted image during PDF export."}, {"lang": "es", "value": "Vulnerabilidad de inclusión de archivos locales en Midori-global Better PDF Exporter para Jira Server y Jira Data Center v.10.3.0 y anteriores permite a un atacante ver archivos arbitrarios y causar otros impactos mediante el uso de imágenes manipuladas durante la exportación de PDF."}], "references": [{"url": "https://gccybermonks.com/posts/pdfjira/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://marketplace.atlassian.com/apps/5167/better-pdf-exporter-for-jira?tab=versions&hosting=datacenter", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://marketplace.atlassian.com/apps/5167/better-pdf-exporter-for-jira?tab=versions&hosting=server", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2023-4842", "published_date": "2023-11-07T12:15:12.287", "last_modified_date": "2024-11-21T08:36:05.230", "descriptions": [{"lang": "en", "value": "The Social Sharing Plugin - Social Warfare plugin for WordPress is vulnerable to Stored Cross-Site Scripting via 'social_warfare' shortcode in versions up to, and including, 4.4.3 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Social Sharing Plugin - Social Warfare para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode 'social_warfare' en versiones hasta la 4.4.3 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/social-warfare/tags/4.4.1/lib/buttons-panel/SWP_Buttons_Panel_Trait.php#L304", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/social-warfare/tags/4.4.1/lib/buttons-panel/SWP_Buttons_Panel_Trait.php#L877", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/2982662/social-warfare#file0", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/8f5b9aff-0833-4887-ae59-df5bc88c7f91?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
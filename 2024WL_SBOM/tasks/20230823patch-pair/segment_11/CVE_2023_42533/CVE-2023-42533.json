{"cve_id": "CVE-2023-42533", "published_date": "2023-11-07T08:15:16.467", "last_modified_date": "2024-11-21T08:22:44.057", "descriptions": [{"lang": "en", "value": "Improper Input Validation with USB Gadget Interface prior to SMR Nov-2023 Release 1 allows a physical attacker to execute arbitrary code in Kernel."}, {"lang": "es", "value": "La validación de entrada incorrecta con USB Gadget Interface anterior a SMR Nov-2023 Release 1 permite a un atacante físico ejecutar código arbitrario en el Kernel."}], "references": [{"url": "https://security.samsungmobile.com/securityUpdate.smsb?year=2023&month=11", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
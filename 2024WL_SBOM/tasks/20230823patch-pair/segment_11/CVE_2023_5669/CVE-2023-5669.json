{"cve_id": "CVE-2023-5669", "published_date": "2023-11-07T12:15:13.617", "last_modified_date": "2024-11-21T08:42:14.390", "descriptions": [{"lang": "en", "value": "The Featured Image Caption plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's shortcode and post meta in all versions up to, and including, 0.8.10 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Featured Image Caption para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode y meta de publicación del complemento en todas las versiones hasta la 0.8.10 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/featured-image-caption/trunk/classes/MetaBox.php?rev=2300545#L91", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://plugins.trac.wordpress.org/browser/featured-image-caption/trunk/classes/MetaBox.php?rev=2300545#L92", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=2998182%40featured-image-caption%2Ftrunk&old=2486227%40featured-image-caption%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0c43a88c-6374-414f-97ae-26ba15d75cdc?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-46808", "published_date": "2023-11-03T13:15:08.310", "last_modified_date": "2024-11-21T07:31:05.117", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Repute Infosystems ARMember armember-membership allows SQL Injection.This issue affects ARMember: from n/a through 3.4.11.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL ('Inyección SQL') en Repute Infosystems ARMember armember-membership permite la inyección SQL. Este problema afecta a ARMember: desde n/a hasta 3.4.11."}], "references": [{"url": "https://patchstack.com/database/vulnerability/armember-membership/wordpress-armember-3-4-11-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
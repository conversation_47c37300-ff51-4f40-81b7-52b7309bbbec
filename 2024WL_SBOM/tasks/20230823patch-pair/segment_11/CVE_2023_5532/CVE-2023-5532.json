{"cve_id": "CVE-2023-5532", "published_date": "2023-11-07T11:15:11.560", "last_modified_date": "2024-11-21T08:41:57.180", "descriptions": [{"lang": "en", "value": "The ImageMapper plugin for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 1.2.6. This is due to missing or incorrect nonce validation on the 'imgmap_save_area_title' function. This makes it possible for unauthenticated attackers to update the post title and inject malicious JavaScript via a forged request, granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento ImageMapper para WordPress es vulnerable a la Cross-Site Request Forgery (CSRF) en versiones hasta la 1.2.6 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función 'imgmap_save_area_title'. Esto hace posible que atacantes no autenticados actualicen el título de la publicación e inyecten JavaScript malicioso a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/imagemapper/tags/1.2.6/imagemapper.php#L894", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/bbb67f02-87e8-4ca3-8a9d-6663a700ab5b?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
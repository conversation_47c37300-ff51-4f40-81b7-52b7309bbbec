{"cve_id": "CVE-2023-38549", "published_date": "2023-11-07T07:15:09.187", "last_modified_date": "2024-11-21T08:13:48.593", "descriptions": [{"lang": "en", "value": "A vulnerability in Veeam ONE allows an unprivileged user who has access to the Veeam ONE Web Client the ability to acquire the NTLM hash of the account used by the Veeam ONE Reporting Service. Note: The criticality of this vulnerability is reduced as it requires interaction by a user with the Veeam ONE Administrator role."}, {"lang": "es", "value": "Una vulnerabilidad en Veeam ONE permite a un usuario sin privilegios que tiene acceso al cliente web Veeam ONE la capacidad de adquirir el hash NTLM de la cuenta utilizada por Veeam ONE Reporting Service. Nota: La criticidad de esta vulnerabilidad se reduce ya que requiere la interacción de un usuario con el rol de administrador de Veeam ONE."}], "references": [{"url": "https://www.veeam.com/kb4508", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}
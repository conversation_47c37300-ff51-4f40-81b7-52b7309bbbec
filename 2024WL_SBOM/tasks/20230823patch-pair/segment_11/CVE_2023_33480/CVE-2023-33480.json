{"cve_id": "CVE-2023-33480", "published_date": "2023-11-07T15:15:10.743", "last_modified_date": "2024-11-21T08:05:37.510", "descriptions": [{"lang": "en", "value": "RemoteClinic 2.0 contains a critical vulnerability chain that can be exploited by a remote attacker with low-privileged user credentials to create admin users, escalate privileges, and execute arbitrary code on the target system via a PHP shell. The vulnerabilities are caused by a lack of input validation and access control in the staff/register.php endpoint and the edit-my-profile.php page. By sending a series of specially crafted requests to the RemoteClinic application, an attacker can create admin users with more privileges than their own, upload a PHP file containing arbitrary code, and execute arbitrary commands via the PHP shell."}, {"lang": "es", "value": "RemoteClinic 2.0 contiene una cadena de vulnerabilidad crítica que puede ser explotada por un atacante remoto con credenciales de usuario con pocos privilegios para crear usuarios administradores, escalar privilegios y ejecutar código arbitrario en el sistema de destino a través de un shell PHP. Las vulnerabilidades se deben a la falta de validación de entradas y control de acceso en el endpoint staff/register.php y en la página edit-my-profile.php. Al enviar una serie de solicitudes especialmente manipuladas a la aplicación RemoteClinic, un atacante puede crear usuarios administradores con más privilegios que los suyos, cargar un archivo PHP que contiene código arbitrario y ejecutar comandos arbitrarios a través del shell PHP."}], "references": [{"url": "https://github.com/remoteclinic/RemoteClinic/issues/24", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-45347", "published_date": "2023-11-02T15:15:08.747", "last_modified_date": "2024-11-21T08:26:46.660", "descriptions": [{"lang": "en", "value": "Online Food Ordering System v1.0 is vulnerable to multiple Unauthenticated SQL Injection vulnerabilities. The '*_verified' parameter of the routers/user-router.php resource does not validate the characters received and they are sent unfiltered to the database.\n\n"}, {"lang": "es", "value": "Online Food Ordering System v1.0 es afectado por múltiples vulnerabilidades de inyección SQL no autenticada. El parámetro '*_verified' del recurso routers/user-router.php no valida los caracteres recibidos y se envían sin filtrar a la base de datos."}], "references": [{"url": "https://fluidattacks.com/advisories/hann", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://projectworlds.in/", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2023-27605", "published_date": "2023-11-06T09:15:07.717", "last_modified_date": "2024-11-21T07:53:15.047", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Sajjad <PERSON>in WP Reroute Email allows SQL Injection.This issue affects WP Reroute Email: from n/a through 1.4.6.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en <PERSON>j<PERSON>d <PERSON>in WP Reroute Email permite la inyección SQL. Este problema afecta a WP Reroute Email: desde n/a hasta 1.4.6."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-reroute-email/wordpress-wp-reroute-email-plugin-1-4-6-admin-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46823", "published_date": "2023-11-06T10:15:08.263", "last_modified_date": "2025-02-26T22:15:11.500", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Avirtum ImageLinks Interactive Image Builder for WordPress allows SQL Injection.This issue affects ImageLinks Interactive Image Builder for WordPress: from n/a through 1.5.4.\n\n"}, {"lang": "es", "value": "La neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Avirtum ImageLinks Interactive Image Builder para WordPress permite la inyección SQL. Este problema afecta a ImageLinks Interactive Image Builder para WordPress: desde n/a hasta 1.5.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/imagelinks-interactive-image-builder-lite/wordpress-imagelinks-interactive-image-builder-for-wordpress-plugin-1-5-4-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-42542", "published_date": "2023-11-07T08:15:20.220", "last_modified_date": "2025-03-06T16:15:43.343", "descriptions": [{"lang": "en", "value": "Improper access control vulnerability in Samsung Push Service prior to 3.4.10 allows local attackers to get register ID to identify the device."}, {"lang": "es", "value": "Una vulnerabilidad de control de acceso inadecuado en Samsung Push Service anterior a 3.4.10 permite a atacantes locales obtener una identificación de registro para identificar el dispositivo."}], "references": [{"url": "https://security.samsungmobile.com/serviceWeb.smsb?year=2023&month=11", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
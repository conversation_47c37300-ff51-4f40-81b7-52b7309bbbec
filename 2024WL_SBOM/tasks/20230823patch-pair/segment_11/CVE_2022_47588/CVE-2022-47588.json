{"cve_id": "CVE-2022-47588", "published_date": "2023-11-03T12:15:08.490", "last_modified_date": "2024-11-21T07:32:13.093", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Tips and Tricks HQ, <PERSON> Simple Photo Gallery simple-photo-gallery allows SQL Injection.This issue affects Simple Photo Gallery: from n/a through v1.8.1.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL ('Inyección SQL') en Tips and Tricks HQ, <PERSON> Simple Photo Gallery simple-photo-gallery permite la inyección de SQL. Este problema afecta a Simple Photo Gallery: desde n/a hasta v1 .8.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/simple-photo-gallery/wordpress-simple-photo-gallery-plugin-v1-8-1-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
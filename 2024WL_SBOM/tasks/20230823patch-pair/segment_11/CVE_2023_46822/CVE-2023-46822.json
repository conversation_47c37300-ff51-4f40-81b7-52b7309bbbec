{"cve_id": "CVE-2023-46822", "published_date": "2023-11-06T10:15:08.200", "last_modified_date": "2024-11-21T08:29:23.107", "descriptions": [{"lang": "en", "value": "Unauth. Reflected Cross-Site Scripting') vulnerability in Visser Labs Store Exporter for WooCommerce – Export Products, Export Orders, Export Subscriptions, and More plugin <= 2.7.2 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Reflejada No Autenticada en Visser Labs Store Exporter para WooCommerce: en los complementos Export Products, Export Orders, Export Subscriptions, and More en versiones &lt;= 2.7.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/woocommerce-exporter/wordpress-store-exporter-for-woocommerce-plugin-2-7-2-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46737", "published_date": "2023-11-07T18:15:09.283", "last_modified_date": "2024-11-21T08:29:11.780", "descriptions": [{"lang": "en", "value": "Cosign is a sigstore signing tool for OCI containers. Cosign is susceptible to a denial of service by an attacker controlled registry. An attacker who controls a remote registry can return a high number of attestations and/or signatures to <PERSON><PERSON> and cause <PERSON><PERSON> to enter a long loop resulting in an endless data attack. The root cause is that <PERSON><PERSON> loops through all attestations fetched from the remote registry in pkg/cosign.FetchAttestations. The attacker needs to compromise the registry or make a request to a registry they control. When doing so, the attacker must return a high number of attestations in the response to Cosign. The result will be that the attacker can cause <PERSON><PERSON> to go into a long or infinite loop that will prevent other users from verifying their data. In Kyvernos case, an attacker whose privileges are limited to making requests to the cluster can make a request with an image reference to their own registry, trigger the infinite loop and deny other users from completing their admission requests. Alternatively, the attacker can obtain control of the registry used by an organization and return a high number of attestations instead the expected number of attestations. The issue can be mitigated rather simply by setting a limit to the limit of attestations that <PERSON><PERSON> will loop through. The limit does not need to be high to be within the vast majority of use cases and still prevent the endless data attack. This issue has been patched in version 2.2.1 and users are advised to upgrade."}, {"lang": "es", "value": "Cosign es una herramienta de firma de sigstore para contenedores OCI. Cosign es susceptible a una denegación de servicio por parte de un registro controlado por un atacante. Un atacante que controla un registro remoto puede devolver una gran cantidad de certificaciones y/o firmas a Cosign y hacer que Cosign entre en un bucle largo que resulte en un ataque de datos interminable. La causa principal es que Cosign recorre todas las certificaciones obtenidas del registro remoto en pkg/cosign.FetchAttestations. El atacante necesita comprometer el registro o realizar una solicitud a un registro que controla. Al hacerlo, el atacante debe devolver una gran cantidad de certificaciones en la respuesta a Cosign. El resultado será que el atacante puede hacer que Cosign entre en un bucle largo o infinito que impedirá que otros usuarios verifiquen sus datos. En el caso de Kyvernos, un atacante cuyos privilegios se limitan a realizar solicitudes al clúster puede realizar una solicitud con una referencia de imagen a su propio registro, desencadenar el bucle infinito y negar que otros usuarios completen sus solicitudes de admisión. Alternativamente, el atacante puede obtener el control del registro utilizado por una organización y devolver una gran cantidad de certificaciones en lugar de la cantidad esperada de certificaciones. El problema se puede mitigar simplemente estableciendo un límite al límite de certificaciones que recorrerá Cosign. No es necesario que el límite sea alto para estar dentro de la gran mayoría de los casos de uso y aun así evitar ataques interminables a los datos. Este problema se solucionó en la versión 2.2.1 y se recomienda a los usuarios que actualicen."}], "references": [{"url": "https://github.com/sigstore/cosign/commit/8ac891ff0e29ddc67965423bee8f826219c6eb0f", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/sigstore/cosign/security/advisories/GHSA-vfp6-jrw2-99g9", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
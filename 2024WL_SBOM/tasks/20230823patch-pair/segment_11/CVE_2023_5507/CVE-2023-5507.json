{"cve_id": "CVE-2023-5507", "published_date": "2023-11-07T11:15:11.367", "last_modified_date": "2024-11-21T08:41:54.647", "descriptions": [{"lang": "en", "value": "The ImageMapper plugin for WordPress is vulnerable to Stored Cross-Site Scripting via 'imagemap' shortcode in versions up to, and including, 1.2.6 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento ImageMapper para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode 'imagemap' en versiones hasta la 1.2.6 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/imagemapper/tags/1.2.6/imagemapper.php#L402", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a6e687e9-6ffe-4457-8d57-3c03f657eb74?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-5824", "published_date": "2023-11-03T08:15:08.270", "last_modified_date": "2024-11-21T08:42:34.053", "descriptions": [{"lang": "en", "value": "A flaw was found in Squid. The limits applied for validation of HTTP response headers are applied before caching. However, Squid may grow a cached HTTP response header beyond the configured maximum size, causing a stall or crash of the worker process when a large header is retrieved from the disk cache, resulting in a denial of service."}, {"lang": "es", "value": "Squid es vulnerable a ataques de Denegación de Servicio contra clientes HTTP y HTTPS debido a un error en el manejo inadecuado de elementos estructurales."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:7465", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7668", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:0072", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:0397", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:0771", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:0772", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:0773", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:1153", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-5824", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2245914", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/squid-cache/squid/security/advisories/GHSA-543m-w2m2-g255", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
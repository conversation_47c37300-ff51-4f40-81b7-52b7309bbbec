{"cve_id": "CVE-2023-4197", "published_date": "2023-11-01T08:15:07.333", "last_modified_date": "2024-11-21T08:34:36.177", "descriptions": [{"lang": "en", "value": "Improper input validation in Dolibarr ERP CRM <= v18.0.1 fails to strip certain PHP code from user-supplied input when creating a Website, allowing an attacker to inject and evaluate arbitrary PHP code."}, {"lang": "es", "value": "La validación de entrada incorrecta en Dolibarr ERP CRM  versiones &lt;= 18.0.1 no elimina cierto código PHP de la entrada proporcionada por el usuario al crear un sitio web, lo que permite a un atacante inyectar y evaluar código PHP arbitrario."}], "references": [{"url": "https://github.com/Dolibarr/dolibarr/commit/0ed6a63fb06be88be5a4f8bcdee83185eee4087e", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://starlabs.sg/advisories/23/23-4197", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
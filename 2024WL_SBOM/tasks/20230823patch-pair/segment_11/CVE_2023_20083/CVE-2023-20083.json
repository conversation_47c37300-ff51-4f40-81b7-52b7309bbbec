{"cve_id": "CVE-2023-20083", "published_date": "2023-11-01T18:15:09.143", "last_modified_date": "2024-11-21T07:40:31.057", "descriptions": [{"lang": "en", "value": "A vulnerability in ICMPv6 inspection when configured with the Snort 2 detection engine for Cisco Firepower Threat Defense (FTD) Software could allow an unauthenticated, remote attacker to cause the CPU of an affected device to spike to 100 percent, which could stop all traffic processing and result in a denial of service (DoS) condition. FTD management traffic is not affected by this vulnerability. This vulnerability is due to improper error checking when parsing fields within the ICMPv6 header. An attacker could exploit this vulnerability by sending a crafted ICMPv6 packet through an affected device. A successful exploit could allow the attacker to cause the device to exhaust CPU resources and stop processing traffic, resulting in a DoS condition. Note: To recover from the DoS condition, the Snort 2 Detection Engine or the Cisco FTD device may need to be restarted."}, {"lang": "es", "value": "Una vulnerabilidad en la inspección ICMPv6 cuando se configura con el motor de detección Snort 2 para el software Cisco Firepower Threat Defense (FTD) podría permitir que un atacante remoto no autenticado haga que la CPU de un dispositivo afectado aumente al 100 por ciento, lo que podría detener todo el procesamiento del tráfico y resultar en una condición de Denegación de Servicio (DoS). El tráfico de gestión de FTD no se ve afectado por esta vulnerabilidad. Esta vulnerabilidad se debe a una comprobación incorrecta de errores al analizar campos dentro del encabezado ICMPv6. Un atacante podría aprovechar esta vulnerabilidad enviando un paquete ICMPv6 manipulado a través de un dispositivo afectado. Un exploit exitoso podría permitir al atacante hacer que el dispositivo agote los recursos de la CPU y deje de procesar el tráfico, lo que resultaría en una condición DoS. Nota: Para recuperarse de la condición DoS, es posible que sea necesario reiniciar el motor de detección Snort 2 o el dispositivo Cisco FTD."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftd-icmpv6-dos-4eMkLuN", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-42655", "published_date": "2023-11-01T10:15:10.657", "last_modified_date": "2024-11-21T08:22:53.740", "descriptions": [{"lang": "en", "value": "In sim service, there is a possible way to write permission usage records of an app due to a missing permission check. This could lead to local escalation of privilege with System execution privileges needed"}, {"lang": "es", "value": "En el servicio de simulación, existe una forma posible de escribir registros de uso de permisos de una aplicación debido a que falta una verificación de permisos. Esto podría llevar a una escalada local de privilegios con permisos de ejecución de System necesarios."}], "references": [{"url": "https://www.unisoc.com/en_us/secy/announcementDetail/https://www.unisoc.com/en_us/secy/announcementDetail/1719615756246777857", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
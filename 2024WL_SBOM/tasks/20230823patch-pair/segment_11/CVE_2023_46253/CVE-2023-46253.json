{"cve_id": "CVE-2023-46253", "published_date": "2023-11-07T19:15:12.213", "last_modified_date": "2024-11-21T08:28:10.573", "descriptions": [{"lang": "en", "value": "Squidex is an open source headless CMS and content management hub. Affected versions are subject to an arbitrary file write vulnerability in the backup restore feature which allows an authenticated attacker to gain remote code execution (RCE). Squidex allows users with the `squidex.admin.restore` permission to create and restore backups. Part of these backups are the assets uploaded to an App. For each asset, the backup zip archive contains a `.asset` file with the actual content of the asset as well as a related `AssetCreatedEventV2` event, which is stored in a JSON file. Amongst other things, the JSON file contains the event type (`AssetCreatedEventV2`), the ID of the asset (`46c05041-9588-4179-b5eb-ddfcd9463e1e`), its filename (`test.txt`), and its file version (`0`). When a backup with this event is restored, the `BackupAssets.ReadAssetAsync` method is responsible for re-creating the asset. For this purpose, it determines the name of the `.asset` file in the zip archive, reads its content, and stores the content in the filestore. When the asset is stored in the filestore via the UploadAsync method, the assetId and fileVersion are passed as arguments. These are further passed to the method GetFileName, which determines the filename where the asset should be stored. The assetId is inserted into the filename without any sanitization and an attacker with squidex.admin.restore privileges to run arbitrary operating system commands on the underlying server (RCE)."}, {"lang": "es", "value": "Squidex es un centro de gestión de contenidos y CMS headless de código abierto. Las versiones afectadas están sujetas a una vulnerabilidad de escritura de archivos arbitraria en la función de restauración de copias de seguridad que permite a un atacante autenticado obtener la ejecución remota de código (RCE). Squidex permite a los usuarios con el permiso `squidex.admin.restore` crear y restaurar copias de seguridad. Parte de estas copias de seguridad son los activos cargados en una aplicación. Para cada activo, el archivo zip de respaldo contiene un archivo `.asset` con el contenido real del activo, así como un evento `AssetCreatedEventV2` relacionado, que se almacena en un archivo JSON. Entre otras cosas, el archivo JSON contiene el tipo de evento (`AssetCreatedEventV2`), el ID del activo (`46c05041-9588-4179-b5eb-ddfcd9463e1e`), su nombre de archivo (`test.txt`) y su versión del archivo. (`0`). Cuando se restaura una copia de seguridad con este evento, el método `BackupAssets.ReadAssetAsync` es responsable de recrear el activo. Para ello, determina el nombre del archivo `.asset` en el archivo zip, lee su contenido y lo almacena en el almacén de archivos. Cuando el activo se almacena en el almacén de archivos mediante el método UploadAsync, el ID del activo y la versión del archivo se pasan como argumentos. Estos se pasan al método GetFileName, que determina el nombre del archivo donde se debe almacenar el activo. El assetId se inserta en el nombre del archivo sin ningún tipo de sanitización y un atacante con privilegios squidex.admin.restore para ejecutar comandos arbitrarios del sistema operativo en el servidor subyacente (RCE)."}], "references": [{"url": "https://github.com/Squidex/squidex/security/advisories/GHSA-phqq-8g7v-3pg5", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
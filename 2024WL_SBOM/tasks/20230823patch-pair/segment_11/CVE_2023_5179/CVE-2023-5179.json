{"cve_id": "CVE-2023-5179", "published_date": "2023-11-07T16:15:29.550", "last_modified_date": "2024-11-21T08:41:14.720", "descriptions": [{"lang": "en", "value": "An issue was discovered in Open Design Alliance Drawings SDK before 2024.10. A corrupted value for the start of MiniFat sector in a crafted DGN file leads to an out-of-bounds read. This can allow attackers to cause a crash, potentially enabling a denial-of-service attack (Crash, Exit, or Restart) or possible code execution. \n\n\n\n\n"}, {"lang": "es", "value": "Se descubrió un problema en Open Design Alliance Drawings SDK antes de la versión 2024.10. Un valor dañado para el inicio del sector MiniFat en un archivo DGN manipulado genera una lectura fuera de los límites. Esto puede permitir a los atacantes provocar un bloqueo, lo que podría permitir un ataque de Denegación de Servicio (DoS) (bloqueo, salida o reinicio) o una posible ejecución de código."}], "references": [{"url": "https://www.opendesign.com/security-advisories", "source": "8a9629cb-c5e7-4d2a-a894-111e8039b7ea", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-1715", "published_date": "2023-11-01T10:15:09.113", "last_modified_date": "2024-11-21T07:39:45.313", "descriptions": [{"lang": "en", "value": "\nA logic error when using mb_strpos() to check for potential XSS payload in Bitrix24 22.0.300 allows attackers to bypass XSS sanitisation via placing HTML tags at the begining of the payload.\n\n\n\n\n\n"}, {"lang": "es", "value": "Un error lógico al usar mb_strpos() para verificar un posible payload XSS en Bitrix24 22.0.300 permite a los atacantes evitar la sanitización XSS colocando etiquetas HTML al comienzo del payload."}], "references": [{"url": "https://starlabs.sg/advisories/23/23-1715/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
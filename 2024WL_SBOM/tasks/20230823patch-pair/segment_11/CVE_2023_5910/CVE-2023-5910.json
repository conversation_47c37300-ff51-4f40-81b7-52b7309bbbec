{"cve_id": "CVE-2023-5910", "published_date": "2023-11-02T00:15:23.373", "last_modified_date": "2024-11-21T08:42:45.393", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PopojiCMS 2.0.1 and classified as problematic. This issue affects some unknown processing of the file install.php of the component Web Config. The manipulation of the argument Site Title with the input <script>alert(1)</script> leads to cross site scripting. The attack may be initiated remotely. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used. The identifier VDB-244229 was assigned to this vulnerability. NOTE: The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en PopojiCMS 2.0.1 y clasificada como problemática. Este problema afecta un procesamiento desconocido del archivo install.php del componente Web Config. La manipulación del argumento Título del sitio con la entrada  conduce a cross site scripting. El ataque puede iniciarse de forma remota. La complejidad de un ataque es bastante alta. Se sabe que la explotación es difícil. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-244229. NOTA: Se contactó primeramente con el proveedor sobre esta divulgación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/hujiahua1997/popojicms2.0.1-Storage-xss-exists/blob/main/image-20231020213521150-16978089243571.png", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://github.com/hujiahua1997/popojicms2.0.1-Storage-xss-exists/blob/main/popojicms2.0.1-Storage-xss-exists.md", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://vuldb.com/?ctiid.244229", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.244229", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-41616", "published_date": "2023-11-07T18:15:07.780", "last_modified_date": "2024-11-21T07:23:30.157", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Ka<PERSON>ik <PERSON>hiya Export Users Data CSV.This issue affects Export Users Data CSV: from n/a through 2.1.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Kaushik Kalathiya Export Users Data CSV. Este problema afecta a Export Users Data CSV: desde n/a hasta 2.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/export-users-data-csv/wordpress-export-users-data-csv-plugin-2-1-auth-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
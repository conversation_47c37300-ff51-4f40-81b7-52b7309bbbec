{"cve_id": "CVE-2023-41425", "published_date": "2023-11-07T16:15:28.867", "last_modified_date": "2025-04-24T19:15:45.347", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerability in Wonder CMS v.3.2.0 thru v.3.4.2 allows a remote attacker to execute arbitrary code via a crafted script uploaded to the installModule component."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting en Wonder CMS v.3.2.0 a v.3.4.2 permite a un atacante remoto ejecutar código arbitrario a través de un script manipulado y cargado en el componente installModule."}], "references": [{"url": "http://wondercms.com", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://gist.github.com/prodigiousMind/fc69a79629c4ba9ee88a7ad526043413", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://packetstorm.news/files/id/190575/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://www.exploit-db.com/exploits/52271", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
{"cve_id": "CVE-2023-47097", "published_date": "2023-11-01T00:15:09.507", "last_modified_date": "2024-11-21T08:29:45.870", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting (XSS) vulnerability in the Server Template under System Setting in Virtualmin 7.7 allows remote attackers to inject arbitrary web script or HTML via the Template name field while creating server templates."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en Server Template bajo System Setting en Virtualmin 7.7 permite a atacantes remotos inyectar script web o HTML arbitrario a través del campo nombre de la Plantilla mientras crean plantillas de servidor."}], "references": [{"url": "https://github.com/pavanughade43/Virtualmin-7.7/blob/main/CVE-2023-47097", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
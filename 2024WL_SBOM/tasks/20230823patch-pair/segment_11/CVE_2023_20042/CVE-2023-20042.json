{"cve_id": "CVE-2023-20042", "published_date": "2023-11-01T18:15:08.843", "last_modified_date": "2024-11-21T07:40:25.200", "descriptions": [{"lang": "en", "value": "A vulnerability in the AnyConnect SSL VPN feature of Cisco Adaptive Security Appliance (ASA) Software and Cisco Firepower Threat Defense (FTD) Software could allow an unauthenticated, remote attacker to cause a denial of service (DoS) condition on an affected device. This vulnerability is due to an implementation error within the SSL/TLS session handling process that can prevent the release of a session handler under specific conditions. An attacker could exploit this vulnerability by sending crafted SSL/TLS traffic to an affected device, increasing the probability of session handler leaks. A successful exploit could allow the attacker to eventually deplete the available session handler pool, preventing new sessions from being established and causing a DoS condition."}, {"lang": "es", "value": "Una vulnerabilidad en la función AnyConnect SSL VPN del software Cisco Adaptive Security Appliance (ASA) y el software Cisco Firepower Threat Defense (FTD) podría permitir que un atacante remoto no autenticado cause una condición de Denegación de Servicio (DoS) en un dispositivo afectado. Esta vulnerabilidad se debe a un error de implementación dentro del proceso de manejo de sesiones SSL/TLS que puede impedir la liberación de un controlador de sesión en condiciones específicas. Un atacante podría aprovechar esta vulnerabilidad enviando tráfico SSL/TLS manipulado a un dispositivo afectado, lo que aumenta la probabilidad de fugas del controlador de sesión. Un exploit exitoso podría permitir al atacante agotar eventualmente el grupo de controladores de sesiones disponible, impidiendo que se establezcan nuevas sesiones y provocando una condición DoS."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-asaftd-ssl-dos-kxG8mpUA", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
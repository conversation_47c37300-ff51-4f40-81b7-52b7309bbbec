{"cve_id": "CVE-2023-5819", "published_date": "2023-11-07T20:15:09.483", "last_modified_date": "2024-11-21T08:42:33.437", "descriptions": [{"lang": "en", "value": "The Amazonify plugin for WordPress is vulnerable to Stored Cross-Site Scripting via admin settings in all versions up to, and including, 0.8.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled. However, please note that this can also be combined with CVE-2023-5818 for CSRF to XSS."}, {"lang": "es", "value": "El complemento Amazonify para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través de la configuración de administrador en todas las versiones hasta la 0.8.1 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con permisos de nivel de administrador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. Esto solo afecta a las instalaciones multisitio y a las instalaciones en las que se ha deshabilitado unfiltered_html. Sin embargo, tenga en cuenta que esto también se puede combinar con CVE-2023-5818 para CSRF a XSS."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/amazonify/trunk/amazonify.php#L142", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/amazonify/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/41adfb58-d79f-40a3-8a7e-f3f08f64659f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
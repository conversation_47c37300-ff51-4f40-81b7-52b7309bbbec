{"cve_id": "CVE-2023-41355", "published_date": "2023-11-03T06:15:07.630", "last_modified_date": "2024-11-21T08:21:08.403", "descriptions": [{"lang": "en", "value": "Chunghwa Telecom NOKIA G-040W-Q Firewall function has a vulnerability of input validation for ICMP redirect messages. An unauthenticated remote attacker can exploit this vulnerability by sending a crafted package to modify the network routing table, resulting in a denial of service or sensitive information leaking."}, {"lang": "es", "value": "La función Chunghwa Telecom NOKIA G-040W-Q Firewall tiene una vulnerabilidad de validación de entrada para mensajes de redireccionamiento ICMP. Un atacante remoto no autenticado puede aprovechar esta vulnerabilidad enviando un paquete manipulado para modificar la tabla de enrutamiento de la red, lo que resulta en una denegación de servicio o una filtración de información confidencial."}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7505-a0c94-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
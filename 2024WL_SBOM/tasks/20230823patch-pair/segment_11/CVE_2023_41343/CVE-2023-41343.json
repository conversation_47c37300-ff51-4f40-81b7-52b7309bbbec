{"cve_id": "CVE-2023-41343", "published_date": "2023-11-03T05:15:29.583", "last_modified_date": "2024-11-21T08:21:06.760", "descriptions": [{"lang": "en", "value": "Rogic No-Code Database Builder's file uploading function has insufficient filtering for special characters. A remote attacker with regular user privilege can inject JavaScript to perform XSS (Stored Cross-Site Scripting) attack."}, {"lang": "es", "value": "La función de carga de archivos de Rogic No-Code Database Builder tiene un filtrado insuficiente para caracteres especiales. Un atacante remoto con privilegios de usuario normal puede inyectar JavaScript para realizar un ataque XSS (Cross-Site Scripting Almacenado)."}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7509-5b734-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
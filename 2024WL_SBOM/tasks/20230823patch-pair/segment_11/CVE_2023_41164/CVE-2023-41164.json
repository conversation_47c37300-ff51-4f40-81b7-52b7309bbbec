{"cve_id": "CVE-2023-41164", "published_date": "2023-11-03T05:15:29.447", "last_modified_date": "2024-11-21T08:20:42.437", "descriptions": [{"lang": "en", "value": "In Django 3.2 before 3.2.21, 4.1 before 4.1.11, and 4.2 before 4.2.5, django.utils.encoding.uri_to_iri() is subject to a potential DoS (denial of service) attack via certain inputs with a very large number of Unicode characters."}, {"lang": "es", "value": "En Django 3.2 anterior a 3.2.21, 4.1 anterior a 4.1.11 y 4.2 anterior a 4.2.5, django.utils.encoding.uri_to_iri() está sujeto a un posible ataque DoS (denegación de servicio) a través de ciertas entradas con un número muy grande de caracteres Unicode."}], "references": [{"url": "https://docs.djangoproject.com/en/4.2/releases/security/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://groups.google.com/forum/#%21forum/django-announce", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/HJFRPUHDYJHBH3KYHSPGULQM4JN7BMSU/", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ZQJOMNRMVPCN5WMIZ7YSX5LQ7IR2NY4D/", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231214-0002/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.djangoproject.com/weblog/2023/sep/04/security-releases/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-39283", "published_date": "2023-11-02T22:15:09.070", "last_modified_date": "2024-11-21T08:15:03.690", "descriptions": [{"lang": "en", "value": "An SMM memory corruption vulnerability in the SMM driver (SMRAM write) in CsmInt10HookSmm in Insyde InsydeH2O with kernel 5.0 through 5.5 allows attackers to send arbitrary data to SMM which could lead to privilege escalation."}, {"lang": "es", "value": "Una vulnerabilidad de corrupción de memoria SMM en el controlador SMM (SMRAM write) en CsmInt10HookSmm en Insyde InsydeH2O con kernel 5.0 a 5.5 permite a atacantes enviar datos arbitrarios a SMM, lo que podría conducir a una escalada de privilegios."}], "references": [{"url": "https://www.insyde.com/security-pledge", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.insyde.com/security-pledge/**********", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-5709", "published_date": "2023-11-07T12:15:13.923", "last_modified_date": "2024-11-21T08:42:19.777", "descriptions": [{"lang": "en", "value": "The WD WidgetTwitter plugin for WordPress is vulnerable to SQL Injection via the plugin's shortcode in versions up to, and including, 1.0.9 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query. This makes it possible for authenticated attackers with contributor-level and above permissions to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento WD WidgetTwitter para WordPress es vulnerable a la inyección SQL a través del shortcode del complemento en versiones hasta la 1.0.9 incluida debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores agreguen consultas SQL adicionales a consultas ya existentes que pueden usarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/widget-twitter/trunk/twitter.php?rev=2212825#L161", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/86cdbfec-b1af-48ec-ae70-f97768694e44?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-5678", "published_date": "2023-11-06T16:15:42.670", "last_modified_date": "2024-11-21T08:42:15.573", "descriptions": [{"lang": "en", "value": "Issue summary: Generating excessively long X9.42 DH keys or checking\nexcessively long X9.42 DH keys or parameters may be very slow.\n\nImpact summary: Applications that use the functions DH_generate_key() to\ngenerate an X9.42 DH key may experience long delays.  Likewise, applications\nthat use DH_check_pub_key(), DH_check_pub_key_ex() or EVP_PKEY_public_check()\nto check an X9.42 DH key or X9.42 DH parameters may experience long delays.\nWhere the key or parameters that are being checked have been obtained from\nan untrusted source this may lead to a Denial of Service.\n\nWhile DH_check() performs all the necessary checks (as of CVE-2023-3817),\nDH_check_pub_key() doesn't make any of these checks, and is therefore\nvulnerable for excessively large P and Q parameters.\n\nLikewise, while DH_generate_key() performs a check for an excessively large\nP, it doesn't check for an excessively large Q.\n\nAn application that calls DH_generate_key() or DH_check_pub_key() and\nsupplies a key or parameters obtained from an untrusted source could be\nvulnerable to a Denial of Service attack.\n\nDH_generate_key() and DH_check_pub_key() are also called by a number of\nother OpenSSL functions.  An application calling any of those other\nfunctions may similarly be affected.  The other functions affected by this\nare DH_check_pub_key_ex(), EVP_PKEY_public_check(), and EVP_PKEY_generate().\n\nAlso vulnerable are the OpenSSL pkey command line application when using the\n\"-pubcheck\" option, as well as the OpenSSL genpkey command line application.\n\nThe OpenSSL SSL/TLS implementation is not affected by this issue.\n\nThe OpenSSL 3.0 and 3.1 FIPS providers are not affected by this issue."}, {"lang": "es", "value": "Resumen del problema: generar claves X9.42 DH excesivamente largas o comprobar claves o parámetros X9.42 DH excesivamente largos puede ser muy lento. Resumen de impacto: las aplicaciones que utilizan las funciones DH_generate_key() para generar una clave DH X9.42 pueden experimentar grandes retrasos. Del mismo modo, las aplicaciones que utilizan DH_check_pub_key(), DH_check_pub_key_ex() o EVP_PKEY_public_check() para comprobar una clave X9.42 DH o parámetros X9.42 DH pueden experimentar grandes retrasos. Cuando la clave o los parámetros que se están verificando se obtuvieron de una fuente que no es confiable, esto puede dar lugar a una Denegación de Servicio. Mientras que DH_check() realiza todas las comprobaciones necesarias (a partir de CVE-2023-3817), DH_check_pub_key() no realiza ninguna de estas comprobaciones y, por lo tanto, es vulnerable a parámetros P y Q excesivamente grandes. Del mismo modo, aunque DH_generate_key() realiza una verificación de una P excesivamente grande, no verifica una Q excesivamente grande. Una aplicación que llama a DH_generate_key() o DH_check_pub_key() y proporciona una clave o parámetros obtenidos de una fuente que no es de confianza podría ser vulnerable a un ataque de denegación de servicio. DH_generate_key() y DH_check_pub_key() también son llamados por otras funciones de OpenSSL. Una aplicación que llame a cualquiera de esas otras funciones también puede verse afectada. Las otras funciones afectadas por esto son DH_check_pub_key_ex(), EVP_PKEY_public_check() y EVP_PKEY_generate(). También son vulnerables la aplicación de línea de comandos OpenSSL pkey cuando se utiliza la opción \"-pubcheck\", así como la aplicación de línea de comandos OpenSSL genpkey. La implementación de OpenSSL SSL/TLS no se ve afectada por este problema. Los proveedores FIPS OpenSSL 3.0 y 3.1 no se ven afectados por este problema."}], "references": [{"url": "https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=34efaef6c103d636ab507a0cc34dca4d3aecc055", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=710fee740904b6290fef0dd5536fbcedbc38ff0c", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=db925ae2e65d0d925adef429afc37f75bd1c2017", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}, {"url": "https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=ddeb4b6c6d527e54ce9a99cba785c0f7776e54b6", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}, {"url": "https://www.openssl.org/news/secadv/20231106.txt", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2024/03/11/1", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231130-0010/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
{"cve_id": "CVE-2023-45055", "published_date": "2023-11-06T09:15:08.553", "last_modified_date": "2024-11-21T08:26:17.867", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in InspireUI MStore API allows SQL Injection.This issue affects MStore API: from n/a through 4.0.6.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en la API MStore de InspireUI permite la inyección SQL. Este problema afecta a la API MStore: desde n/a hasta 4.0.6."}], "references": [{"url": "https://patchstack.com/database/vulnerability/mstore-api/wordpress-mstore-api-plugin-4-0-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
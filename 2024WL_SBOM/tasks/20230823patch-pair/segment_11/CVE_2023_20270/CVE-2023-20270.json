{"cve_id": "CVE-2023-20270", "published_date": "2023-11-01T17:15:11.783", "last_modified_date": "2024-11-21T07:41:02.670", "descriptions": [{"lang": "en", "value": "A vulnerability in the interaction between the Server Message Block (SMB) protocol preprocessor and the Snort 3 detection engine for Cisco Firepower Threat Defense (FTD) Software could allow an unauthenticated, remote attacker to bypass the configured policies or cause a denial of service (DoS) condition on an affected device. This vulnerability is due to improper error-checking when the Snort 3 detection engine is processing SMB traffic. An attacker could exploit this vulnerability by sending a crafted SMB packet stream through an affected device. A successful exploit could allow the attacker to cause the Snort process to reload, resulting in a DoS condition."}, {"lang": "es", "value": "Una vulnerabilidad en la interacción entre el preprocesador del protocolo Server Message Block (SMB) y el motor de detección Snort 3 para el software Cisco Firepower Threat Defense (FTD) podría permitir que un atacante remoto no autenticado omita las políticas configuradas o provoque una Denegación de Servicio (DoS) condición en un dispositivo afectado. Esta vulnerabilidad se debe a una verificación de errores incorrecta cuando el motor de detección de Snort 3 procesa el tráfico SMB. Un atacante podría aprovechar esta vulnerabilidad enviando un flujo de paquetes SMB manipulado a través de un dispositivo afectado. Un exploit exitoso podría permitir al atacante hacer que el proceso Snort se recargue, lo que resultaría en una condición DoS."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftd-smbsnort3-dos-pfOjOYUV", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
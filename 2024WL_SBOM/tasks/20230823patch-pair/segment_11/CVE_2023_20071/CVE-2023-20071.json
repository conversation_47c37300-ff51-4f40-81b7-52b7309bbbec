{"cve_id": "CVE-2023-20071", "published_date": "2023-11-01T18:15:09.090", "last_modified_date": "2024-11-21T07:40:29.093", "descriptions": [{"lang": "en", "value": "Multiple Cisco products are affected by a vulnerability in the Snort detection engine that could allow an unauthenticated, remote attacker to bypass the configured policies on an affected system. This vulnerability is due to a flaw in the FTP module of the Snort detection engine. An attacker could exploit this vulnerability by sending crafted FTP traffic through an affected device. A successful exploit could allow the attacker to bypass FTP inspection and deliver a malicious payload."}, {"lang": "es", "value": "Varios productos de Cisco se ven afectados por una vulnerabilidad en el motor de detección Snort que podría permitir que un atacante remoto no autenticado omitir las políticas configuradas en un sistema afectado. Esta vulnerabilidad se debe a una falla en el módulo FTP del motor de detección de Snort. Un atacante podría aprovechar esta vulnerabilidad enviando tráfico FTP manipulado a través de un dispositivo afectado. Un exploit exitoso podría permitir al atacante omitir la inspección de FTP y entregar un payload maliciosa."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-snort-ftd-zXYtnjOM", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
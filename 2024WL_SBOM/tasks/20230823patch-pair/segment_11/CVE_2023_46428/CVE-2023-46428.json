{"cve_id": "CVE-2023-46428", "published_date": "2023-11-01T21:15:08.733", "last_modified_date": "2024-11-21T08:28:30.990", "descriptions": [{"lang": "en", "value": "An arbitrary file upload vulnerability in HadSky v7.12.10 allows attackers to execute arbitrary code via a crafted file."}, {"lang": "es", "value": "Una vulnerabilidad de carga de archivos arbitrarios en HadSky v7.12.10 permite a los atacantes ejecutar código arbitrario a través de un archivo manipulado."}], "references": [{"url": "https://github.com/fenglon/CVE/blob/main/analyse.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
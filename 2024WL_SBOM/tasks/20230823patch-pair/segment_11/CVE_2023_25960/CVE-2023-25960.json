{"cve_id": "CVE-2023-25960", "published_date": "2023-11-03T13:15:08.570", "last_modified_date": "2024-11-21T07:50:31.400", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Zendrop Zendrop – Global Dropshipping zendrop-dropshipping-and-fulfillment allows SQL Injection.This issue affects Zendrop – Global Dropshipping: from n/a through 1.0.0.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL ('Inyección SQL') en Zendrop Zendrop – Global Dropshipping zendrop-dropshipping-and-fulfillment permite la inyección SQL. Este problema afecta a Zendrop – Global Dropshipping: desde n/a hasta 1.0.0 ."}], "references": [{"url": "https://patchstack.com/database/vulnerability/zendrop-dropshipping-and-fulfillment/wordpress-zendrop-global-dropshipping-plugin-1-0-0-arbitrary-code-execution?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
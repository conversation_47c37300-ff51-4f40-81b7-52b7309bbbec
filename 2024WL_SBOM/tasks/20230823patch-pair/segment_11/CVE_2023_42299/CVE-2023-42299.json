{"cve_id": "CVE-2023-42299", "published_date": "2023-11-02T22:15:09.113", "last_modified_date": "2024-11-21T08:22:25.097", "descriptions": [{"lang": "en", "value": "Buffer Overflow vulnerability in OpenImageIO oiio v.******** allows a remote attacker to execute arbitrary code and cause a denial of service via the read_subimage_data function."}, {"lang": "es", "value": "Vulnerabilidad de desbordamiento de búfer en OpenImageIO oiio v.******** permite a un atacante remoto ejecutar código arbitrario y provocar una denegación de servicio a través de la función read_subimage_data."}], "references": [{"url": "https://github.com/OpenImageIO/oiio/issues/3840", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch", "Vendor Advisory"]}]}
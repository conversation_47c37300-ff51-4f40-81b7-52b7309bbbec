{"cve_id": "CVE-2023-46482", "published_date": "2023-11-01T19:15:45.393", "last_modified_date": "2024-11-21T08:28:34.620", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in wuzhicms v.4.1.0 allows a remote attacker to execute arbitrary code via the Database Backup Functionality in the coreframe/app/database/admin/index.php component."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en wuzhicms v.4.1.0 permite a un atacante remoto ejecutar código arbitrario a través de la funcionalidad de copia de seguridad de la base de datos en el componente coreframe/app/database/admin/index.php."}], "references": [{"url": "https://github.com/XTo-o1/PHP/blob/main/wuzhicms/WUZHI%20CMS%20v4.1.0%20SQL%20Injection%20Vulnerability%20in%20Database%20Backup%20Functionality.md", "source": "<EMAIL>", "tags": ["Exploit"]}]}
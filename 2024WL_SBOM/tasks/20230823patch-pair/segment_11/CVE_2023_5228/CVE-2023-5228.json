{"cve_id": "CVE-2023-5228", "published_date": "2023-11-06T21:15:09.660", "last_modified_date": "2025-02-26T22:15:13.333", "descriptions": [{"lang": "en", "value": "The User Registration WordPress plugin before ******* does not sanitize and escape some of its settings, which could allow high-privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento User Registration de WordPress anterior a ******* no sanitiza ni escapa a algunas de sus configuraciones, lo que podría permitir a usuarios con altos privilegios, como el administrador, realizar ataques de Cross-Site Scripting (XSS) Almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración multisitio)."}], "references": [{"url": "https://wpscan.com/vulnerability/50ae7008-46f0-4f89-ae98-65dcabe4ef09", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2019-25156", "published_date": "2023-11-07T06:15:07.783", "last_modified_date": "2024-11-21T04:39:59.717", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in dstar2018 Agency up to 61. Affected by this vulnerability is an unknown functionality of the file search.php. The manipulation of the argument QSType/QuickSearch leads to cross site scripting. The attack can be launched remotely. The patch is named 975b56953efabb434519d9feefcc53685fb8d0ab. It is recommended to apply a patch to fix this issue. The associated identifier of this vulnerability is VDB-244495."}, {"lang": "es", "value": "Una vulnerabilidad ha sido encontrada en dstar2018 Agency hasta 61 y clasificada como problemática. Una función desconocida del archivo search.php es afectada por esta vulnerabilidad. La manipulación del argumento QSType/QuickSearch conduce a Cross-Site Scripting (XSS). El ataque se puede lanzar de forma remota. El parche se llama 975b56953efabb434519d9feefcc53685fb8d0ab. Se recomienda aplicar un parche para solucionar este problema. El identificador asociado de esta vulnerabilidad es VDB-244495."}], "references": [{"url": "https://github.com/dstar2018/agency-code-repo/commit/975b56953efabb434519d9feefcc53685fb8d0ab", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://vuldb.com/?ctiid.244495", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://vuldb.com/?id.244495", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
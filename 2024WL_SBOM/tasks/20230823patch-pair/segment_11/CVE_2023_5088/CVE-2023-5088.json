{"cve_id": "CVE-2023-5088", "published_date": "2023-11-03T14:15:08.560", "last_modified_date": "2024-11-21T08:41:02.500", "descriptions": [{"lang": "en", "value": "A bug in QEMU could cause a guest I/O operation otherwise addressed to an arbitrary disk offset to be targeted to offset 0 instead (potentially overwriting the VM's boot code). This could be used, for example, by L2 guests with a virtual disk (vdiskL2) stored on a virtual disk of an L1 (vdiskL1) hypervisor to read and/or write data to LBA 0 of vdiskL1, potentially gaining control of L1 at its next reboot."}, {"lang": "es", "value": "Un error en QEMU podría causar que una operación de E/S de invitado que de otro modo estaría dirigida a un desplazamiento de disco arbitrario se dirija al desplazamiento 0 (potencialmente sobrescribiendo el código de arranque de la VM). Esto podría ser utilizado, por ejemplo, por invitados L2 con un disco virtual (vdiskL2) almacenado en un disco virtual de un hipervisor L1 (vdiskL1) para leer y/o escribir datos en el LBA 0 de vdiskL1, obteniendo potencialmente el control de L1 en su próximo reinicio."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2024:2135", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:2962", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-5088", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2247283", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://lore.kernel.org/all/<EMAIL>/T/", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}, {"url": "https://lists.debian.org/debian-lts-announce/2024/03/msg00012.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231208-0005/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
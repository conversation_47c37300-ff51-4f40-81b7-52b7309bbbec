{"cve_id": "CVE-2023-4043", "published_date": "2023-11-03T09:15:13.600", "last_modified_date": "2024-11-21T08:34:17.207", "descriptions": [{"lang": "en", "value": "In Eclipse Parsson before versions 1.1.4 and 1.0.5, Parsing JSON from untrusted sources can lead malicious actors to exploit the fact that the built-in support for parsing numbers with large scale in Java has a number of edge cases where the input text of a number can lead to much larger processing time than one would expect.\n\n\nTo mitigate the risk, parsson put in place a size limit for the numbers as well as their scale.\n\n\n"}, {"lang": "es", "value": "En Eclipse Parsson antes de las versiones 1.1.4 y 1.0.5, el Parsing JSON de fuentes no confiables puede llevar a actores maliciosos a explotar el hecho de que el soporte integrado para analizar números a gran escala en Java tiene varios casos extremos en los que el texto de entrada de un número puede llevar a un tiempo de procesamiento mucho mayor de lo que cabría esperar. Para mitigar el riesgo, parsson estableció un límite de tamaño para los números y su escala."}], "references": [{"url": "https://github.com/eclipse-ee4j/parsson/pull/100", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://gitlab.eclipse.org/security/vulnerability-reports/-/issues/13", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}
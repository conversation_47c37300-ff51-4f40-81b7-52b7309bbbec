{"cve_id": "CVE-2023-3889", "published_date": "2023-11-07T16:15:28.637", "last_modified_date": "2025-03-24T18:15:18.077", "descriptions": [{"lang": "en", "value": "A local non-privileged user can make improper GPU memory processing operations. If the operations are carefully prepared, then they could be used to gain access to already freed memory.\n\n"}, {"lang": "es", "value": "Un usuario local sin privilegios puede realizar operaciones de procesamiento de memoria de GPU incorrectas. Si las operaciones se preparan cuidadosamente, podrían usarse para obtener acceso a la memoria ya liberada."}], "references": [{"url": "https://developer.arm.com/Arm%20Security%20Center/Mali%20GPU%20Driver%20Vulnerabilities", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
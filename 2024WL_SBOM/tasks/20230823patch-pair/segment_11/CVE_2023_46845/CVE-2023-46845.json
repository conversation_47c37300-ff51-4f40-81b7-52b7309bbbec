{"cve_id": "CVE-2023-46845", "published_date": "2023-11-07T08:15:24.257", "last_modified_date": "2024-11-21T08:29:24.673", "descriptions": [{"lang": "en", "value": "EC-CUBE 3 series (3.0.0 to 3.0.18-p6) and 4 series (4.0.0 to 4.0.6-p3, 4.1.0 to 4.1.2-p2, and 4.2.0 to 4.2.2) contain an arbitrary code execution vulnerability due to improper settings of the template engine Twig included in the product. As a result, arbitrary code may be executed on the server where the product is running by a user with an administrative privilege."}, {"lang": "es", "value": "EC-CUBE series 3 (3.0.0 a 3.0.18-p6) y 4 (4.0.0 a 4.0.6-p3, 4.1.0 a 4.1.2-p2 y 4.2.0 a 4.2.2) contienen una vulnerabilidad de ejecución de código arbitrario debido a una configuración incorrecta del motor de plantillas Twig incluido en el producto. Como resultado, un usuario con privilegios administrativos puede ejecutar código arbitrario en el servidor donde se ejecuta el producto."}], "references": [{"url": "https://jvn.jp/en/jp/JVN29195731/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.ec-cube.net/info/weakness/20231026/index.php", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Vendor Advisory"]}, {"url": "https://www.ec-cube.net/info/weakness/20231026/index_3.php", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Vendor Advisory"]}, {"url": "https://www.ec-cube.net/info/weakness/20231026/index_40.php", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Vendor Advisory"]}]}
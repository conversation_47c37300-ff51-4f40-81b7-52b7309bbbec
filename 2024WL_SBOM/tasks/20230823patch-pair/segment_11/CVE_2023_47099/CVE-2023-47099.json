{"cve_id": "CVE-2023-47099", "published_date": "2023-11-01T00:15:09.583", "last_modified_date": "2024-11-21T08:29:46.157", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting (XSS) vulnerability in the Create Virtual Server in Virtualmin 7.7 allows remote attackers to inject arbitrary web script or HTML via Description field while creating the Virtual server."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en Create Virtual Server en Virtualmin 7.7 permite a atacantes remotos inyectar script web o HTML arbitrario a través del campo Descripción mientras crean el servidor Virtual."}], "references": [{"url": "https://github.com/pavanughade43/Virtualmin-7.7/blob/main/CVE-2023-47099", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46243", "published_date": "2023-11-07T20:15:08.370", "last_modified_date": "2024-11-21T08:28:09.173", "descriptions": [{"lang": "en", "value": "XWiki Platform is a generic wiki platform offering runtime services for applications built on top of it. In affected versions it's possible for a user to execute any content with the right of an existing document's content author, provided the user have edit right on it. A crafted URL of the form ` /xwiki/bin/edit//?content=%7B%7Bgroovy%7D%7Dprintln%28%22Hello+from+Groovy%21%22%29%7B%7B%2Fgroovy%7D%7D&xpage=view` can be used to execute arbitrary groovy code on the server. This vulnerability has been patched in XWiki versions 14.10.6 and 15.2RC1. Users are advised to update. There are no known workarounds for this issue. "}, {"lang": "es", "value": "XWiki Platform es una plataforma wiki genérica que ofrece servicios de ejecución para aplicaciones creadas sobre ella. En las versiones afectadas, es posible que un usuario ejecute cualquier contenido con el derecho del autor del contenido de un documento existente, siempre que el usuario tenga derecho de edición sobre él. Una URL manipulada con el formato ` /xwiki/bin/edit//?content=%7B%7Bgroovy%7D%7Dprintln%28%22Hello+from+Groovy%21%22%29%7B%7B%2Fgroovy%7D%7D&amp;xpage=view` se puede utilizar para ejecutar código arbitrario en el servidor. Esta vulnerabilidad ha sido parcheada en las versiones 14.10.6 y 15.2RC1 de XWiki. Se recomienda a los usuarios que actualicen. No se conocen workarounds para este problema."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/a0e6ca083b36be6f183b9af33ae735c1e02010f4", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-g2qq-c5j9-5w5w", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-20385", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}]}
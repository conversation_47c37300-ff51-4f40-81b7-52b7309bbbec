{"cve_id": "CVE-2023-3972", "published_date": "2023-11-01T16:15:08.517", "last_modified_date": "2024-11-21T08:18:25.853", "descriptions": [{"lang": "en", "value": "A vulnerability was found in insights-client. This security issue occurs because of insecure file operations or unsafe handling of temporary files and directories that lead to local privilege escalation. Before the insights-client has been registered on the system by root, an unprivileged local user or attacker could create the /var/tmp/insights-client directory (owning the directory with read, write, and execute permissions) on the system. After the insights-client is registered by root, an attacker could then control the directory content that insights are using by putting malicious scripts into it and executing arbitrary code as root (trivially bypassing SELinux protections because insights processes are allowed to disable SELinux system-wide)."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en insights-client. Este problema de seguridad se produce debido a operaciones de archivos inseguras o al manejo inseguro de archivos y directorios temporales que conducen a una escalada de privilegios locales. Antes de que el usuario root registre el cliente de insights en el sistema, un usuario local sin privilegios o un atacante podría crear el directorio /var/tmp/insights-client (que posee el directorio con permisos de lectura, escritura y ejecución) en el sistema. Después de que el cliente de Insights esté registrado como root, un atacante podría controlar el contenido del directorio que utiliza Insights colocando scripts maliciosos en él y ejecutando código arbitrario como root (evitando trivialmente las protecciones de SELinux porque los procesos de Insights pueden desactivar SELinux en todo el sistema)."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6264", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6282", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6283", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6284", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6795", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6796", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6798", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6811", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-3972", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2227027", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://github.com/RedHatInsights/insights-core/pull/3878", "source": "<EMAIL>", "tags": ["Patch"]}]}
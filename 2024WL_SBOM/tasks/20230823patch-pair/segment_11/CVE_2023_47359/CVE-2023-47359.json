{"cve_id": "CVE-2023-47359", "published_date": "2023-11-07T16:15:29.140", "last_modified_date": "2024-11-21T08:30:11.663", "descriptions": [{"lang": "en", "value": "Videolan VLC prior to version 3.0.20 contains an incorrect offset read that leads to a Heap-Based Buffer Overflow in function GetPacket() and results in a memory corruption."}, {"lang": "es", "value": "Videolan VLC anterior a la versión 3.0.20 contiene una lectura de desplazamiento incorrecta que provoca un desbordamiento del búfer en la función GetPacket() y provoca daños en la memoria."}], "references": [{"url": "https://0xariana.github.io/blog/real_bugs/vlc/mms", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2023/11/msg00034.html", "source": "<EMAIL>", "tags": []}]}
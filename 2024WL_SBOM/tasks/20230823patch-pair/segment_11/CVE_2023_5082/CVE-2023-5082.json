{"cve_id": "CVE-2023-5082", "published_date": "2023-11-06T21:15:09.517", "last_modified_date": "2025-02-26T22:15:13.003", "descriptions": [{"lang": "en", "value": "The History Log by click5 WordPress plugin before 1.0.13 does not properly sanitise and escape a parameter before using it in a SQL statement, leading to a SQL injection exploitable by admin users when using the Smash Balloon Social Photo Feed plugin alongside it."}, {"lang": "es", "value": "El complemento History Log by click5 para Wordpress anterior a 1.0.13 no sanitiza ni escapa adecuadamente un parámetro antes de usarlo en una declaración SQL, lo que genera una inyección de SQL explotable por los usuarios administradores cuando usan el complemento Smash Balloon Social Photo Feed junto con él."}], "references": [{"url": "https://wpscan.com/vulnerability/13a196ba-49c7-4575-9a49-3ef9eb2348f3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
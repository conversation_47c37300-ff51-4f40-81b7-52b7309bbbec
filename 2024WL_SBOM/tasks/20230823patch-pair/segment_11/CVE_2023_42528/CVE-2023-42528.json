{"cve_id": "CVE-2023-42528", "published_date": "2023-11-07T08:15:13.257", "last_modified_date": "2024-11-21T08:22:43.340", "descriptions": [{"lang": "en", "value": "Improper Input Validation vulnerability in ProcessNvBuffering of libsec-ril prior to SMR Nov-2023 Release 1 allows local attacker to execute arbitrary code."}, {"lang": "es", "value": "Vulnerabilidad de validación de entrada incorrecta en ProcessNvBuffering de libsec-ril anterior a SMR Nov-2023 Release 1 permite a un atacante local ejecutar código arbitrario."}], "references": [{"url": "https://security.samsungmobile.com/securityUpdate.smsb?year=2023&month=11", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-46851", "published_date": "2023-11-07T09:15:07.313", "last_modified_date": "2024-11-21T08:29:25.597", "descriptions": [{"lang": "en", "value": "Allura Discussion and Allura Forum importing does not restrict URL values specified in attachments. Project administrators can run these imports, which could cause Allura to read local files and expose them.  Exposing internal files then can lead to other exploits, like session hijacking, or remote code execution.\n\nThis issue affects Apache Allura from 1.0.1 through 1.15.0.\n\nUsers are recommended to upgrade to version 1.16.0, which fixes the issue.  If you are unable to upgrade, set \"disable_entry_points.allura.importers = forge-tracker, forge-discussion\" in your .ini config file.\n\n"}, {"lang": "es", "value": "La importación de Allura Discussion y Allura Forum no restringe los valores de URL especificados en los archivos adjuntos. Los administradores de proyectos pueden ejecutar estas importaciones, lo que podría hacer que Allura lea archivos locales y los exponga. La exposición de archivos internos puede dar lugar a otros exploits, como el secuestro de sesión o la ejecución remota de código. Este problema afecta a Apache Allura desde la versión 1.0.1 hasta la 1.15.0. Se recomienda a los usuarios actualizar a la versión 1.16.0, que soluciona el problema. Si no puede actualizar, configure \"disable_entry_points.allura.importers = forge-tracker, forge-discussion\" en su archivo de configuración .ini."}], "references": [{"url": "https://allura.apache.org/posts/2023-allura-1.16.0.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://lists.apache.org/thread/hqk0vltl7qgrq215zgwjfoj0khbov0gx", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-46728", "published_date": "2023-11-06T18:15:08.637", "last_modified_date": "2024-11-21T08:29:10.403", "descriptions": [{"lang": "en", "value": "Squid is a caching proxy for the Web supporting HTTP, HTTPS, FTP, and more. Due to a NULL pointer dereference bug Squid is vulnerable to a Denial of Service attack against Squid's Gopher gateway. The gopher protocol is always available and enabled in Squid prior to Squid 6.0.1. Responses triggering this bug are possible to be received from any gopher server, even those without malicious intent. Gopher support has been removed in Squid version 6.0.1. Users are advised to upgrade. Users unable to upgrade should reject all gopher URL requests."}, {"lang": "es", "value": "Squid es un proxy de almacenamiento en caché para la Web que admite HTTP, HTTPS, FTP y más. Debido a un bug de desreferencia de NULL pointer, Squid es vulnerable a un ataque de Denegación de Servicio contra la puerta de enlace Gopher de Squid. El protocolo Gopher siempre está disponible y habilitado en Squid antes de Squid 6.0.1. Es posible recibir respuestas que desencadenen este error desde cualquier servidor Gopher, incluso aquellos sin intenciones maliciosas. La compatibilidad con Gopher se eliminó en la versión 6.0.1 de Squid. Se recomienda a los usuarios que actualicen. Los usuarios que no puedan actualizar deben rechazar todas las solicitudes de URL de Gopher."}], "references": [{"url": "https://github.com/squid-cache/squid/commit/6ea12e8fb590ac6959e9356a81aa3370576568c3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/squid-cache/squid/security/advisories/GHSA-cg5h-v6vc-w33f", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/A5QASTMCUSUEW3UOMKHZJB3FTONWSRXS/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/MEV66D3PAAY6K7TWDT3WZBLCPLASFJDC/", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2023-47185", "published_date": "2023-11-06T11:15:09.497", "last_modified_date": "2024-11-21T08:29:55.177", "descriptions": [{"lang": "en", "value": "Unauth. Stored Cross-Site Scripting (XSS) vulnerability in gVectors Team Comments — wpDiscuz plugin <= 7.6.11 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Almacenada No Autenticada en engVectors Team Comments en el complemento wpDiscuz en versiones &lt;= 7.6.11."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wpdiscuz/wordpress-wpdiscuz-plugin-7-6-11-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46731", "published_date": "2023-11-06T19:15:09.307", "last_modified_date": "2024-11-21T08:29:10.913", "descriptions": [{"lang": "en", "value": "XWiki Platform is a generic wiki platform offering runtime services for applications built on top of it. XWiki doesn't properly escape the section URL parameter that is used in the code for displaying administration sections. This allows any user with read access to the document `XWiki.AdminSheet` (by default, everyone including unauthenticated users) to execute code including Groovy code. This impacts the confidentiality, integrity and availability of the whole XWiki instance. This vulnerability has been patched in XWiki 14.10.14, 15.6 RC1 and 15.5.1. Users are advised to upgrade. Users unablr to upgrade may apply the fix in commit `fec8e0e53f9` manually. Alternatively, to protect against attacks from unauthenticated users, view right for guests can be removed from this document (it is only needed for space and wiki admins)."}, {"lang": "es", "value": "XWiki Platform es una plataforma wiki genérica que ofrece servicios de ejecución para aplicaciones creadas sobre ella. XWiki no escapa correctamente al parámetro URL de la sección que se utiliza en el código para mostrar las secciones de administración. Esto permite que cualquier usuario con acceso de lectura al documento `XWiki.AdminSheet` (de forma predeterminada, todos, incluidos los usuarios no autenticados) ejecute código, incluido el código Groovy. Esto afecta la confidencialidad, integridad y disponibilidad de toda la instancia de XWiki. Esta vulnerabilidad ha sido parcheada en XWiki 14.10.14, 15.6 RC1 y 15.5.1. Se recomienda a los usuarios que actualicen. Los usuarios que no puedan actualizar pueden aplicar la solución en el commit `fec8e0e53f9` manualmente. Alternativamente, para protegerse contra ataques de usuarios no autenticados, se puede eliminar de este documento el derecho de visualización para invitados (solo es necesario para los administradores de espacio y wiki)."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/fec8e0e53f9fa2c3f1e568cc15b0e972727c803a", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/commit/fec8e0e53f9fa2c3f1e568cc15b0e972727c803a#diff-6271f9be501f30b2ba55459eb451aee3413d34171ba8198a77c865306d174e23", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-62pr-qqf7-hh89", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-21110", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-43982", "published_date": "2023-11-03T05:15:30.093", "last_modified_date": "2024-11-21T08:25:02.497", "descriptions": [{"lang": "en", "value": "Bon Presta boninstagramcarousel between v5.2.1 to v7.0.0 was discovered to contain a Server-Side Request Forgery (SSRF) via the url parameter at insta_parser.php. This vulnerability allows attackers to use the vulnerable website as proxy to attack other websites or exfiltrate data via a HTTP call."}, {"lang": "es", "value": "Se descubrió que Bon Presta boninstagramcarousel entre v5.2.1 y v7.0.0 contenía Server-Side Request Forgery (SSRF) a través del parámetro url en insta_parser.php. Esta vulnerabilidad permite a los atacantes utilizar el sitio web vulnerable como proxy para atacar otros sitios web o extraer datos mediante una llamada HTTP."}], "references": [{"url": "https://security.friendsofpresta.org/modules/2023/11/02/boninstagramcarousel.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
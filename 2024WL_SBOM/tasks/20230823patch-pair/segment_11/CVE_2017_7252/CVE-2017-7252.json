{"cve_id": "CVE-2017-7252", "published_date": "2023-11-03T01:15:07.777", "last_modified_date": "2024-11-21T03:31:28.793", "descriptions": [{"lang": "en", "value": "bcrypt password hashing in Botan before 2.1.0 does not correctly handle passwords with a length between 57 and 72 characters, which makes it easier for attackers to determine the cleartext password."}, {"lang": "es", "value": "El hash de contraseñas de bcrypt en Botan anterior a 2.1.0 no maneja correctamente las contraseñas con una longitud de entre 57 y 72 caracteres, lo que facilita a los atacantes determinar la contraseña en texto plano."}], "references": [{"url": "https://botan.randombit.net/security.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.suse.com/show_bug.cgi?id=1034591", "source": "<EMAIL>", "tags": ["Issue Tracking"]}]}
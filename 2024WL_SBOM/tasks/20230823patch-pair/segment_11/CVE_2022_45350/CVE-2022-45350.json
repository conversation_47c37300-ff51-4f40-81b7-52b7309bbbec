{"cve_id": "CVE-2022-45350", "published_date": "2023-11-07T15:15:09.730", "last_modified_date": "2024-11-21T07:29:04.867", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Pär Thernström Simple History – user activity log, audit tool.This issue affects Simple History – user activity log, audit tool: from n/a through 3.3.1.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Pär Thernström Simple History – user activity log, audit tool. Este problema afecta Simple History: registro de actividad del usuario, herramienta de auditoría: desde n/a hasta 3.3.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/simple-history/wordpress-simple-history-plugin-3-3-1-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
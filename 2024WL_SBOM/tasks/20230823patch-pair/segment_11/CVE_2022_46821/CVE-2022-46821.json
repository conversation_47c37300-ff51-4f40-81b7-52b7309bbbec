{"cve_id": "CVE-2022-46821", "published_date": "2023-11-07T17:15:09.270", "last_modified_date": "2024-11-21T07:31:06.900", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Jackmail & Sarbacane Emails & Newsletters with Jackmail.This issue affects Emails & Newsletters with Jackmail: from n/a through 1.2.22.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Jackmail &amp; Sarbacane Emails &amp; Newsletters with Jack<PERSON>. Este problema afecta a Emails &amp; Newsletters with <PERSON><PERSON>: desde n/a hasta 1.2.22."}], "references": [{"url": "https://patchstack.com/database/vulnerability/jackmail-newsletters/wordpress-emails-newsletters-with-jackmail-plugin-1-2-22-csv-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
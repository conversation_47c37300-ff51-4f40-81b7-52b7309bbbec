{"cve_id": "CVE-2023-1192", "published_date": "2023-11-01T20:15:08.597", "last_modified_date": "2024-11-21T07:38:38.570", "descriptions": [{"lang": "en", "value": "A use-after-free flaw was found in smb2_is_status_io_timeout() in CIFS in the Linux Kernel. After CIFS transfers response data to a system call, there are still local variable points to the memory region, and if the system call frees it faster than CIFS uses it, CIFS will access a free memory region, leading to a denial of service."}, {"lang": "es", "value": "Se encontró una falla de use-after-free en smb2_is_status_io_timeout() en CIFS en el kernel de Linux. Después de que CIFS transfiere datos de respuesta a una llamada al sistema, todavía hay puntos variables locales en la región de memoria, y si la llamada al sistema la libera más rápido de lo que CIFS la usa, CIFS accederá a una región de memoria libre, lo que provocará una denegación de servicio."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-1192", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2154178", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/commit/?id=d527f51331cace562393a8038d870b3e9916686f", "source": "<EMAIL>", "tags": []}]}
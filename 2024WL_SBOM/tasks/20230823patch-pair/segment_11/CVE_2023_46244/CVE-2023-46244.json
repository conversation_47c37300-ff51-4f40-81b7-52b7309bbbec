{"cve_id": "CVE-2023-46244", "published_date": "2023-11-07T19:15:10.923", "last_modified_date": "2024-11-21T08:28:09.307", "descriptions": [{"lang": "en", "value": "XWiki Platform is a generic wiki platform offering runtime services for applications built on top of it. In affected versions it's possible for a user to write a script in which any velocity content is executed with the right of any other document content author. Since this API require programming right and the user does not have it, the expected result is `$doc.document.authors.contentAuthor` (not executed script), unfortunately with the security vulnerability it is possible for the attacker to get `XWiki.superadmin` which shows that the title was executed with the right of the unmodified document. This has been patched in XWiki versions 14.10.7 and 15.2RC1. Users are advised to upgrade. There are no known workarounds for this vulnerability."}, {"lang": "es", "value": "XWiki Platform es una plataforma wiki genérica que ofrece servicios de ejecución para aplicaciones creadas sobre ella. En las versiones afectadas, es posible que un usuario escriba un script en el que se ejecute cualquier contenido de velocidad con el derecho de cualquier otro autor del contenido del documento. Dado que esta API requiere derechos de programación y el usuario no los tiene, el resultado esperado es `$doc.document.authors.contentAuthor` (script no ejecutado), desafortunadamente, con la vulnerabilidad de seguridad, es posible que el atacante obtenga `XWiki.superadmin` que muestra que el título fue ejecutado con el derecho del documento no modificado. Esto ha sido parcheado en las versiones 14.10.7 y 15.2RC1 de XWiki. Se recomienda a los usuarios que actualicen. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/11a9170dfe63e59f4066db67f84dbfce4ed619c6", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/commit/41d7dca2d30084966ca6a7ee537f39ee8354a7e3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-rmxw-c48h-2vf5", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-20624", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-20625", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}]}
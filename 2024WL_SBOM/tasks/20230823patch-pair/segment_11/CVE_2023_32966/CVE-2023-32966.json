{"cve_id": "CVE-2023-32966", "published_date": "2023-11-07T18:15:08.620", "last_modified_date": "2024-11-21T08:04:18.410", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in CRUDLab Jazz Popups leads to Stored XSS.This issue affects Jazz Popups: from n/a through 1.8.7.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en CRUDLab Jazz Popups conduce XSS Almacenado. Este problema afecta a Jazz Popups: desde n/a hasta 1.8.7."}], "references": [{"url": "https://patchstack.com/database/vulnerability/jazz-popups/wordpress-jazz-popups-plugin-1-8-7-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
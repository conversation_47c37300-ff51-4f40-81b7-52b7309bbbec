{"cve_id": "CVE-2023-47235", "published_date": "2023-11-03T21:15:17.470", "last_modified_date": "2024-11-21T08:30:00.550", "descriptions": [{"lang": "en", "value": "An issue was discovered in FRRouting FRR through 9.0.1. A crash can occur when a malformed BGP UPDATE message with an EOR is processed, because the presence of EOR does not lead to a treat-as-withdraw outcome."}, {"lang": "es", "value": "Se descubrió un problema en FRRouting FRR hasta 9.0.1. <PERSON><PERSON><PERSON> ocu<PERSON>r una caída cuando se procesa un mensaje malformado de BGP UPDATE con un EOR, porque la presencia de un EOR no conduce a un resultado de treat-as-withdraw."}], "references": [{"url": "https://github.com/FRRouting/frr/pull/14716/commits/6814f2e0138a6ea5e1f83bdd9085d9a77999900b", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://lists.debian.org/debian-lts-announce/2024/04/msg00019.html", "source": "<EMAIL>", "tags": []}]}
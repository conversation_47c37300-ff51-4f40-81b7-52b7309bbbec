{"cve_id": "CVE-2023-5918", "published_date": "2023-11-02T12:15:09.800", "last_modified_date": "2024-11-21T08:42:46.543", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in SourceCodester Visitor Management System 1.0. Affected is an unknown function of the file manage_user.php. The manipulation of the argument id leads to sql injection. It is possible to launch the attack remotely. The identifier of this vulnerability is VDB-244308."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Visitor Management System 1.0 y clasificada como crítica. Una función desconocida del archivo manage_user.php es afectada por esta vulnerabilidad. La manipulación del argumento id conduce a la inyección de SQL. Es posible lanzar el ataque de forma remota. El identificador de esta vulnerabilidad es VDB-244308."}], "references": [{"url": "https://github.com/Castle1984/CveRecord/blob/main/Sql_apply.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.244308", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.244308", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
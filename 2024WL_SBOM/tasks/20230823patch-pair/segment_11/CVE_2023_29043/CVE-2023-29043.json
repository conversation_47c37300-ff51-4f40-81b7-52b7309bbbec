{"cve_id": "CVE-2023-29043", "published_date": "2023-11-02T14:15:11.017", "last_modified_date": "2024-11-21T07:56:26.147", "descriptions": [{"lang": "en", "value": "Presentations may contain references to images, which are user-controlled, and could include malicious script code that is being processed when editing a document. Script code embedded in malicious documents could be executed in the context of the user editing the document when performing certain actions, like copying content. The relevant attribute does now get encoded to avoid the possibility of executing script code. No publicly available exploits are known.\n\n"}, {"lang": "es", "value": "Las presentaciones pueden contener referencias a imágenes controladas por el usuario y podrían incluir código de script malicioso que se procesa al editar un documento. El código de script incorporado en documentos maliciosos podría ejecutarse en el contexto en el que el usuario edita el documento al realizar determinadas acciones, como copiar contenido. El atributo relevante ahora se codifica para evitar la posibilidad de ejecutar código de script. No se conocen exploits disponibles públicamente."}], "references": [{"url": "https://documentation.open-xchange.com/appsuite/security/advisories/csaf/2023/oxas-adv-2023-0004.json", "source": "<EMAIL>", "tags": []}, {"url": "https://software.open-xchange.com/products/appsuite/doc/Release_Notes_for_Patch_Release_6243_7.10.6_2023-08-01.pdf", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
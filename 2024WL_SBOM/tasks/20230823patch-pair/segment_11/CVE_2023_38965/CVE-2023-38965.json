{"cve_id": "CVE-2023-38965", "published_date": "2023-11-03T05:15:29.400", "last_modified_date": "2024-11-21T08:14:32.650", "descriptions": [{"lang": "en", "value": "Lost and Found Information System 1.0 allows account takeover via username and password to a /classes/Users.php?f=save URI."}, {"lang": "es", "value": "Lost and Found Information System 1.0 permite la toma de control de cuentas mediante nombre de usuario y contraseña en un /classes/Users.php?f=save URI."}], "references": [{"url": "http://packetstormsecurity.com/files/175077/Lost-And-Found-Information-System-1.0-Insecure-Direct-Object-Reference.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}, {"url": "https://github.com/Or4ngm4n/vulnreability-code-review-php/blob/main/Lost%20and%20Found%20Information%20System%20v1.0.txt", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
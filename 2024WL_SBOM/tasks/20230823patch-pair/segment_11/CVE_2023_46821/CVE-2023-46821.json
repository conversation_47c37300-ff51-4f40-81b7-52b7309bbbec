{"cve_id": "CVE-2023-46821", "published_date": "2023-11-06T10:15:08.130", "last_modified_date": "2024-11-21T08:29:22.927", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Milan Petrovic GD Security Headers allows auth. (admin+) SQL Injection.This issue affects GD Security Headers: from n/a through 1.7.\n\n"}, {"lang": "es", "value": "La neutralización inadecuada de los elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en Milan Petrovic GD Security Headers permite la autenticación. (con permisos de admin o superiores) Inyección SQL. Este problema afecta GD Security Headers: desde n/a hasta 1.7."}], "references": [{"url": "https://patchstack.com/database/vulnerability/gd-security-headers/wordpress-gd-security-headers-plugin-1-7-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
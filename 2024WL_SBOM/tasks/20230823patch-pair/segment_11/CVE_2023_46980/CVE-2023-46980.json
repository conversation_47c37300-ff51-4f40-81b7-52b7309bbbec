{"cve_id": "CVE-2023-46980", "published_date": "2023-11-03T16:15:31.103", "last_modified_date": "2024-11-21T08:29:35.433", "descriptions": [{"lang": "en", "value": "An issue in Best Courier Management System v.1.0 allows a remote attacker to execute arbitrary code and escalate privileges via a crafted script to the userID parameter."}, {"lang": "es", "value": "Un problema en Best Courier Management System v.1.0 permite a un atacante remoto ejecutar código arbitrario y escalar privilegios a través de un script manipulado al parámetro ID de usuario."}], "references": [{"url": "https://github.com/sajaljat/CVE-2023-46980/tree/main", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://youtu.be/3Mz2lSElg7Y", "source": "<EMAIL>", "tags": ["Exploit"]}]}
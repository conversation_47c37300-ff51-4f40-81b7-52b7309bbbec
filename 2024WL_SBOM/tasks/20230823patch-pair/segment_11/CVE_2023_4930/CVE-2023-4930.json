{"cve_id": "CVE-2023-4930", "published_date": "2023-11-06T21:15:09.233", "last_modified_date": "2025-02-26T22:15:12.833", "descriptions": [{"lang": "en", "value": "The Front End PM WordPress plugin before 11.4.3 does not block listing the contents of the directories where it stores attachments to private messages, allowing unauthenticated visitors to list and download private attachments if the autoindex feature of the web server is enabled."}, {"lang": "es", "value": "El complemento Front End PM para WordPress anterior a 11.4.3 no bloquea la lista de contenidos de los directorios donde almacena archivos adjuntos a mensajes privados, lo que permite a los visitantes no autenticados enumerar y descargar archivos adjuntos privados si la función de autoindexación del servidor web está habilitada."}], "references": [{"url": "https://wpscan.com/vulnerability/c73b3276-e6f1-4f22-a888-025e5d0504f2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
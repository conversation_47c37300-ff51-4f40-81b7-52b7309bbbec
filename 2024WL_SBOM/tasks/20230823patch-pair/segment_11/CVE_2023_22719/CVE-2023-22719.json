{"cve_id": "CVE-2023-22719", "published_date": "2023-11-07T16:15:28.140", "last_modified_date": "2024-11-21T07:45:17.280", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in GiveWP.This issue affects GiveWP: from n/a through 2.25.1.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en GiveWP. Este problema afecta a GiveWP: desde n/a hasta 2.25.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/give/wordpress-givewp-plugin-2-25-1-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
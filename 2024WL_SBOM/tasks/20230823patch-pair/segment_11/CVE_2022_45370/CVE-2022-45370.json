{"cve_id": "CVE-2022-45370", "published_date": "2023-11-07T17:15:08.387", "last_modified_date": "2025-02-19T22:15:10.240", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in WebToffee WordPress Comments Import & Export.This issue affects WordPress Comments Import & Export: from n/a through 2.3.1.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en WebToffee WordPress Comments Import &amp; Export. Este problema afecta a WordPress Comments Import &amp; Export: desde n/a hasta 2.3.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/comments-import-export-woocommerce/wordpress-wordpress-comments-import-export-plugin-2-3-1-csv-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
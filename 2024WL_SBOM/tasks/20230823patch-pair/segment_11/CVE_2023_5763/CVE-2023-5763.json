{"cve_id": "CVE-2023-5763", "published_date": "2023-11-03T07:15:14.617", "last_modified_date": "2024-11-21T08:42:26.270", "descriptions": [{"lang": "en", "value": "In Eclipse Glassfish 5 or 6, running with old versions of JDK (lower than 6u211, or < 7u201, or < 8u191), allows remote attackers to load malicious code on the server via access to insecure ORB listeners.\n"}, {"lang": "es", "value": "En Eclipse Glassfish 5 o 6, ejecutado con versiones antiguas de JDK (inferiores a 6u211, o &lt; 7u201, o &lt; 8u191), permite a atacantes remotos cargar código malicioso en el servidor mediante el acceso a oyentes ORB inseguros."}], "references": [{"url": "https://gitlab.eclipse.org/security/cve-assignement/-/issues/14", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://glassfish.org/docs/latest/security-guide.html#securing-glassfish-server", "source": "<EMAIL>", "tags": ["Product"]}]}
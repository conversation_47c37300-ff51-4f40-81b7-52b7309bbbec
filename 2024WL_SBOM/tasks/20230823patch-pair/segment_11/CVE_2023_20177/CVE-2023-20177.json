{"cve_id": "CVE-2023-20177", "published_date": "2023-11-01T17:15:11.287", "last_modified_date": "2024-11-21T07:40:45.160", "descriptions": [{"lang": "en", "value": "A vulnerability in the SSL file policy implementation of Cisco Firepower Threat Defense (FTD) Software that occurs when the SSL/TLS connection is configured with a URL Category and the Snort 3 detection engine could allow an unauthenticated, remote attacker to cause the Snort 3 detection engine to unexpectedly restart. This vulnerability exists because a logic error occurs when a Snort 3 detection engine inspects an SSL/TLS connection that has either a URL Category configured on the SSL file policy or a URL Category configured on an access control policy with TLS server identity discovery enabled. Under specific, time-based constraints, an attacker could exploit this vulnerability by sending a crafted SSL/TLS connection through an affected device. A successful exploit could allow the attacker to trigger an unexpected reload of the Snort 3 detection engine, resulting in either a bypass or denial of service (DoS) condition, depending on device configuration. The Snort 3 detection engine will restart automatically. No manual intervention is required."}, {"lang": "es", "value": "Una vulnerabilidad en la implementación de la política de archivos SSL del software Cisco Firepower Threat Defense (FTD) que ocurre cuando la conexión SSL/TLS está configurada con una categoría de URL y el motor de detección Snort 3 podría permitir que un atacante remoto no autenticado provoque la detección de Snort 3. el motor se reinicie inesperadamente. Esta vulnerabilidad existe porque se produce un error lógico cuando un motor de detección Snort 3 inspecciona una conexión SSL/TLS que tiene una categoría de URL configurada en la política de archivos SSL o una categoría de URL configurada en una política de control de acceso con el descubrimiento de identidad del servidor TLS habilitado. Bajo limitaciones específicas basadas en el tiempo, un atacante podría aprovechar esta vulnerabilidad enviando una conexión SSL/TLS manipulada a través de un dispositivo afectado. Un exploit exitoso podría permitir al atacante activar una recarga inesperada del motor de detección de Snort 3, lo que resultaría en una condición de omisión o Denegación de Servicio (DoS), según la configuración del dispositivo. El motor de detección de Snort 3 se reiniciará automáticamente. No se requiere intervención manual."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-sa-ftd-snort3-urldos-OccFQTeX", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
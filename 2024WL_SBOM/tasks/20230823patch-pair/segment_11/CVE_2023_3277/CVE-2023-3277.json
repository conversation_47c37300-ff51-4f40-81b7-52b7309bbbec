{"cve_id": "CVE-2023-3277", "published_date": "2023-11-03T12:15:08.650", "last_modified_date": "2024-11-21T08:16:55.077", "descriptions": [{"lang": "en", "value": "The MStore API plugin for WordPress is vulnerable to Unauthorized Account Access and Privilege Escalation in versions up to, and including, 4.10.7 due to improper implementation of the Apple login feature. This allows unauthenticated attackers to log in as any user as long as they know the user's email address. We are disclosing this issue as the developer has not yet released a patch, but continues to release updates and we escalated this issue to the plugin's team 30 days ago."}, {"lang": "es", "value": "El complemento API de MStore para WordPress es vulnerable al acceso no autorizado a cuentas y a la escalada de privilegios en versiones hasta la 4.10.7 incluida debido a una implementación incorrecta de la función de inicio de sesión de Apple. Esto permite a atacantes no autenticados iniciar sesión como cualquier usuario siempre que conozcan la dirección de correo electrónico del usuario. Estamos divulgando este problema porque el desarrollador aún no ha lanzado un parche, pero continúa lanzando actualizaciones y escalamos este problema al equipo del complemento hace 30 días."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/mstore-api/trunk/controllers/flutter-user.php#L821", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1c7c0c35-5f44-488f-9fe1-269ea4a73854?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
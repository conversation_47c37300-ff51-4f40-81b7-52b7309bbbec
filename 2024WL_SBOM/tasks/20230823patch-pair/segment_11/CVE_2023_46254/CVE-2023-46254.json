{"cve_id": "CVE-2023-46254", "published_date": "2023-11-06T19:15:09.230", "last_modified_date": "2024-11-21T08:28:10.743", "descriptions": [{"lang": "en", "value": "capsule-proxy is a reverse proxy for Capsule kubernetes multi-tenancy framework. A bug in the RoleBinding reflector used by `capsule-proxy` gives ServiceAccount tenant owners the right to list Namespaces of other tenants backed by the same owner kind and name. For example consider two tenants `solar` and `wind`. Tenant `solar`, owned by a ServiceAccount named `tenant-owner` in the Namespace `solar`. Tenant `wind`, owned by a ServiceAccount named `tenant-owner` in the Namespace `wind`. The Tenant owner `solar` would be able to list the namespaces of the Tenant `wind` and vice-versa, although this is not correct. The bug introduces an exfiltration vulnerability since allows the listing of Namespace resources of other Tenants, although just in some specific conditions: 1. `capsule-proxy` runs with the `--disable-caching=false` (default value: `false`) and 2. Tenant owners are ServiceAccount, with the same resource name, but in different Namespaces. This vulnerability doesn't allow any privilege escalation on the outer tenant Namespace-scoped resources, since the Kubernetes RBAC is enforcing this. This issue has been addressed in version 0.4.5. Users are advised to upgrade. There are no known workarounds for this vulnerability."}, {"lang": "es", "value": "Capsule-proxy es un proxy inverso para el framework multi-tenancy de Capsule Kubernetes. Un bug en el reflector RoleBinding utilizado por `capsule-proxy` otorga a los propietarios de tenants de ServiceAccount el derecho de enumerar espacios de nombres de otros tenants respaldados por el mismo tipo y nombre de propietario. Por ejemplo, considere dos tenant \"\"\"\"solar\"\"\"\" y \"\"\"\"wind\"\"\"\". El tenant \"\"\"\"solar\"\"\"\", propiedad de una cuenta de servicio denominada \"\"\"\"tenant-owner\"\"\"\" en el espacio de nombres \"\"\"\"solar\"\"\"\". El tenant \"\"\"\"wind\"\"\"\", propiedad de una cuenta de servicio denominada \"\"\"\"tenant-owner\"\"\"\" en el espacio de nombres \"\"\"\"wind\"\"\"\". El propietario del inquilino \"\"\"\"solar\"\"\"\" podría enumerar los espacios de nombres del inquilino \"\"\"\"wind\"\"\"\" y viceversa, aunque esto no es correcto. El error introduce una vulnerabilidad de exfiltración ya que permite enumerar los recursos del espacio de nombres de otros tenant, aunque solo en algunas condiciones específicas:\n1. `capsule-proxy` se ejecuta con `--disable-caching=false` (valor predeterminado: `false` ) y \n2. Los propietarios de los tenant son ServiceAccount, con el mismo nombre de recurso, pero en diferentes espacios de nombres.\nEsta vulnerabilidad no permite ninguna escalada de privilegios en los recursos con ámbito de espacio de nombres del inquilino externo, ya que Kubernetes RBAC lo está aplicando. \nEste problema se solucionó en la versión 0.4.5. Se recomienda a los usuarios que actualicen. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/projectcapsule/capsule-proxy/commit/615202f7b02eaec7681336bd63daed1f39ae00c5", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/projectcapsule/capsule-proxy/security/advisories/GHSA-6758-979h-249x", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-3397", "published_date": "2023-11-01T20:15:08.737", "last_modified_date": "2024-11-21T08:17:11.077", "descriptions": [{"lang": "en", "value": "A race condition occurred between the functions lmLogClose and txEnd in JFS, in the Linux Kernel, executed in different threads. This flaw allows a local attacker with normal user privileges to crash the system or leak internal kernel information."}, {"lang": "es", "value": "Se produjo una condición de ejecución entre las funciones lmLogClose y txEnd en JFS, en el Kernel de Linux, ejecutadas en diferentes hilos. Esta falla permite que un atacante local con privilegios de usuario normales bloquee el sistema o filtre información interna del kernel."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-3397", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2217271", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://www.spinics.net/lists/kernel/msg4788636.html", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}
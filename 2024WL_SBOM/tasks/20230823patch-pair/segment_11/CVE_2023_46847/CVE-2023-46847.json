{"cve_id": "CVE-2023-46847", "published_date": "2023-11-03T08:15:08.023", "last_modified_date": "2024-11-21T08:29:25.000", "descriptions": [{"lang": "en", "value": "Squid is vulnerable to a Denial of Service,  where a remote attacker can perform buffer overflow attack by writing up to 2 MB of arbitrary data to heap memory when Squid is configured to accept HTTP Digest Authentication."}, {"lang": "es", "value": "Squid es vulnerable a una Denegación de Servicio, donde un atacante remoto puede realizar un ataque de desbordamiento de búfer escribiendo hasta 2 MB de datos arbitrarios en la memoria acumulada cuando Squid está configurado para aceptar la autenticación implícita HTTP."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6266", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6267", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6268", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6748", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6801", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6803", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6804", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6805", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6810", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6882", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6884", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7213", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7576", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7578", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-46847", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2245916", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://github.com/squid-cache/squid/security/advisories/GHSA-phqj-m8gv-cq4g", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2024/01/msg00003.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
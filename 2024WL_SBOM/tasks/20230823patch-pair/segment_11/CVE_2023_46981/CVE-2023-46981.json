{"cve_id": "CVE-2023-46981", "published_date": "2023-11-05T00:15:08.580", "last_modified_date": "2024-11-21T08:29:35.663", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in Novel-Plus v.4.2.0 allows a remote attacker to execute arbitrary code via a crafted script to the sort parameter in /common/log/list."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en Novel-Plus v.4.2.0 permite a un atacante remoto ejecutar código arbitrario a través de un script manipulado en el parámetro sort en /common/log/list."}], "references": [{"url": "https://github.com/JunFengDeng/Cve-List/blob/main/novel-plus/20231027/vuln/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2022-3172", "published_date": "2023-11-03T20:15:08.550", "last_modified_date": "2025-02-13T17:15:42.960", "descriptions": [{"lang": "en", "value": "A security issue was discovered in kube-apiserver that allows an \naggregated API server to redirect client traffic to any URL.  This could\n lead to the client performing unexpected actions as well as forwarding \nthe client's API server credentials to third parties."}, {"lang": "es", "value": "Se descubrió un problema de seguridad en kube-apiserver que permite que un servidor API agregado redirija el tráfico del cliente a cualquier URL. Esto podría llevar a que el cliente realice acciones inesperadas, así como a que reenvíe las credenciales del servidor API del cliente a terceros."}], "references": [{"url": "https://github.com/kubernetes/kubernetes/issues/112513", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://groups.google.com/g/kubernetes-security-announce/c/_aLzYMpPRak", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "https://security.netapp.com/advisory/ntap-20231221-0005/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2023-46846", "published_date": "2023-11-03T08:15:07.953", "last_modified_date": "2024-12-18T01:15:06.010", "descriptions": [{"lang": "en", "value": "SQUID is vulnerable to HTTP request smuggling, caused by chunked decoder lenience, allows a remote attacker to perform Request/Response smuggling past firewall and frontend security systems."}, {"lang": "es", "value": "SQUID es vulnerable al contrabando de solicitudes HTTP, causado por la indulgencia de los decodificadores fragmentados, lo que permite a un atacante remoto realizar el contrabando de solicitudes/respuestas a través del firewall y los sistemas de seguridad frontales."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6266", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6267", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6268", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6748", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6801", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6803", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6804", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6810", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7213", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2024:11049", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-46846", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2245910", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://github.com/squid-cache/squid/security/advisories/GHSA-j83v-w3p4-5cqh", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2024/01/msg00003.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2024/01/msg00008.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
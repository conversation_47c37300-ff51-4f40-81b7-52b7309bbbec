{"cve_id": "CVE-2023-46998", "published_date": "2023-11-07T05:15:13.657", "last_modified_date": "2024-11-21T08:29:36.720", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerability in BootBox Bootbox.js v.3.2 through 6.0 allows a remote attacker to execute arbitrary code via a crafted payload to alert(), confirm(), prompt() functions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) en BootBox Bootbox.js v.3.2 a 6.0 permite a un atacante remoto ejecutar código arbitrario a través de un payload manipulado para las funciones alert(), confirm() y prompt()."}], "references": [{"url": "https://github.com/bootboxjs/bootbox/issues/661", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/soy-oreocato/CVE-2023-46998/", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-40453", "published_date": "2023-11-07T04:20:15.030", "last_modified_date": "2024-11-21T08:19:29.920", "descriptions": [{"lang": "en", "value": "Docker Machine through 0.16.2 allows an attacker, who has control of a worker node, to provide crafted version data, which might potentially trick an administrator into performing an unsafe action (via escape sequence injection), or might have a data size that causes a denial of service to a bastion node. NOTE: This vulnerability only affects products that are no longer supported by the maintainer."}, {"lang": "es", "value": "Docker Machine hasta 0.16.2 permite a un atacante, que tiene control de un nodo trabajador, proporcionar datos de versión manipulados, lo que podría engañar a un administrador para que realice una acción insegura (mediante inyección de secuencia de escape), o podría tener un tamaño de datos que cause una Denegación de Servicio (DoS) a un nodo bastión. NOTA: Esta vulnerabilidad solo afecta a productos que ya no son compatibles con el fabricante."}], "references": [{"url": "https://github.com/docker/machine/releases", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://hackerone.com/reports/1916285", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vin01.github.io/piptagole/docker/security/gitlab/docker-machine/2023/07/07/docker-machine-attack-surface.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2023-29047", "published_date": "2023-11-02T14:15:11.280", "last_modified_date": "2024-11-21T07:56:26.670", "descriptions": [{"lang": "en", "value": "Imageconverter API endpoints provided methods that were not sufficiently validating and sanitizing client input, allowing to inject arbitrary SQL statements. An attacker with access to the adjacent network and potentially API credentials, could read and modify database content which is accessible to the imageconverter SQL user account. None No publicly available exploits are known.\n\n"}, {"lang": "es", "value": "Los endpoints de la API Imageconverter proporcionaban métodos que no validaban ni sanitizaban correctamente la entrada del cliente, lo que permitía inyectar declaraciones SQL arbitrarias. Un atacante con acceso a la red adyacente y potencialmente credenciales API podría leer y modificar el contenido de la base de datos al que puede acceder la cuenta de usuario SQL de imageconverter. Ninguno No se conocen exploits disponibles públicamente."}], "references": [{"url": "https://documentation.open-xchange.com/appsuite/security/advisories/csaf/2023/oxas-adv-2023-0004.json", "source": "<EMAIL>", "tags": []}, {"url": "https://software.open-xchange.com/products/appsuite/doc/Release_Notes_for_Patch_Release_6243_7.10.6_2023-08-01.pdf", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
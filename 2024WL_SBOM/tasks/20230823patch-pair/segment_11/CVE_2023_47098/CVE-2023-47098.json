{"cve_id": "CVE-2023-47098", "published_date": "2023-11-01T00:15:09.547", "last_modified_date": "2024-11-21T08:29:46.013", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting (XSS) vulnerability in the Manage Extra Admins under Administration Options in Virtualmin 7.7 allows remote attackers to inject arbitrary web script or HTML via the real name or description field."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en Manage Extra Admins bajo Administration Options en Virtualmin 7.7 permite a atacantes remotos inyectar script web o HTML arbitrarion a través del nombre real o el campo de descripción."}], "references": [{"url": "https://github.com/pavanughade43/Virtualmin-7.7/blob/main/CVE-2023-47098", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
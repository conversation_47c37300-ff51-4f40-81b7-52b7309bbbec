{"cve_id": "CVE-2023-5660", "published_date": "2023-11-07T12:15:13.293", "last_modified_date": "2024-11-21T08:42:13.240", "descriptions": [{"lang": "en", "value": "The SendPress Newsletters plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's shortcode(s) in all versions up to, and including, ********* due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento SendPress Newsletters para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través de los shortcodes del complemento en todas las versiones hasta la ********* incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/sendpress/tags/*********/classes/sc/class-sendpress-sc-unsubscribe-form.php#L57", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/cbce42a0-29a7-40df-973c-1fe7338f6c94?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
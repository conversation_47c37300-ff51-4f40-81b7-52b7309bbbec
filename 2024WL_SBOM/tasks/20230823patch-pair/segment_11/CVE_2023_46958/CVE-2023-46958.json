{"cve_id": "CVE-2023-46958", "published_date": "2023-11-02T22:15:09.250", "last_modified_date": "2024-11-21T08:29:33.393", "descriptions": [{"lang": "en", "value": "An issue in lmxcms v.1.41 allows a remote attacker to execute arbitrary code via a crafted script to the admin.php file."}, {"lang": "es", "value": "Un problema en lmxcms v.1.41 permite a un atacante remoto ejecutar código arbitrario a través de un script manipulado en el archivo admin.php."}], "references": [{"url": "http://lmxcms.com", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "http://www.lmxcms.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://gist.github.com/durian5201314/6507d1057c62f4bf93e740a631617434", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
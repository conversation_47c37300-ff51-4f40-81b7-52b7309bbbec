{"cve_id": "CVE-2023-45827", "published_date": "2023-11-06T18:15:08.467", "last_modified_date": "2024-11-21T08:27:26.363", "descriptions": [{"lang": "en", "value": "Dot diver is a lightweight, powerful, and dependency-free TypeScript utility library that provides types and functions to work with object paths in dot notation. In versions prior to 1.0.2 there is a Prototype Pollution vulnerability in the `setByPath` function which can leads to remote code execution (RCE). This issue has been addressed in commit `98daf567` which has been included in release 1.0.2. Users are advised to upgrade. There are no known workarounds to this vulnerability.\n"}, {"lang": "es", "value": "Dot diver es una librería de utilidades TypeScript liviana, potente y sin dependencias que proporciona tipos y funciones para trabajar con rutas de objetos en notación de puntos. En versiones anteriores a la 1.0.2 hay una vulnerabilidad de contaminación de prototipo en la función `setByPath` que puede conducir a la Ejecución Remota de Código (RCE). Este problema se solucionó en el commit `98daf567` que se incluyó en la versión 1.0.2. Se recomienda a los usuarios que actualicen. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/clickbar/dot-diver/commit/98daf567390d816fd378ec998eefe2e97f293d5a", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/clickbar/dot-diver/security/advisories/GHSA-9w5f-mw3p-pj47", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
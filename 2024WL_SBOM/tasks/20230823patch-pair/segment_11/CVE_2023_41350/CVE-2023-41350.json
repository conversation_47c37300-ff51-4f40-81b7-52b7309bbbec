{"cve_id": "CVE-2023-41350", "published_date": "2023-11-03T05:15:29.930", "last_modified_date": "2024-11-21T08:21:07.740", "descriptions": [{"lang": "en", "value": "Chunghwa Telecom NOKIA G-040W-Q has a vulnerability of insufficient measures to prevent multiple failed authentication attempts. An unauthenticated remote attacker can execute a crafted Javascript to expose captcha in page, making it very easy for bots to bypass the captcha check and more susceptible to brute force attacks."}, {"lang": "es", "value": "Chunghwa Telecom NOKIA G-040W-Q tiene una vulnerabilidad de medidas insuficientes para evitar múltiples intentos fallidos de autenticación. Un atacante remoto no autenticado puede ejecutar un Javascript manipulado para exponer el captcha en la página, lo que hace que sea muy fácil para los bots omitir la verificación del captcha y hacerlos más susceptibles a ataques de fuerza bruta."}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7500-0c544-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
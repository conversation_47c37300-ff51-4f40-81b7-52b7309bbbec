{"cve_id": "CVE-2023-47510", "published_date": "2023-11-07T10:15:08.273", "last_modified_date": "2024-11-21T08:30:21.883", "descriptions": [{"lang": "en", "value": "Unauth. Reflected Cross-Site Scripting (XSS) vulnerability in WPSolutions-HQ WPDBSpringClean plugin <= 1.6 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Reflejada No Autenticada en el complemento WPSolutions-HQ WPDBSpringClean en versiones &lt;= 1.6."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wpdbspringclean/wordpress-wpdbspringclean-plugin-1-6-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
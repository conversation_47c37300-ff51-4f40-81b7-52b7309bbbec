{"cve_id": "CVE-2023-4956", "published_date": "2023-11-07T20:15:08.970", "last_modified_date": "2024-11-21T08:36:20.217", "descriptions": [{"lang": "en", "value": "A flaw was found in Quay. Clickjacking is when an attacker uses multiple transparent or opaque layers to trick a user into clicking on a button or link on another page when they intend to click on the top-level page. During the pentest, it has been detected that the config-editor page is vulnerable to clickjacking. This flaw allows an attacker to trick an administrator user into clicking on buttons on the config-editor panel, possibly reconfiguring some parts of the Quay instance."}, {"lang": "es", "value": "Se encontró una falla en Quay. El secuestro de clicks se produce cuando un atacante utiliza múltiples capas transparentes u opacas para engañar a un usuario para que haga clic en un botón o enlace en otra página cuando pretende hacer click en la página de nivel superior. Durante el pentest, se detectó que la página del editor de configuración es vulnerable al secuestro de clicks. Esta falla permite a un atacante engañar a un usuario administrador para que haga click en los botones del panel del editor de configuración, posiblemente reconfigurando algunas partes de la instancia de Quay."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-4956", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2238886", "source": "<EMAIL>", "tags": ["Issue Tracking"]}]}
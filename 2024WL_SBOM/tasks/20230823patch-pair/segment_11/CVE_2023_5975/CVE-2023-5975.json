{"cve_id": "CVE-2023-5975", "published_date": "2023-11-07T11:15:12.117", "last_modified_date": "2024-11-21T08:42:54.140", "descriptions": [{"lang": "en", "value": "The ImageMapper plugin for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 1.2.6. This is due to missing or incorrect nonce validation on multiple functions. This makes it possible for unauthenticated attackers to update the plugin settings via a forged request, granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento ImageMapper para WordPress es vulnerable a la Cross-Site Request Forgery (CSRF) en versiones hasta la 1.2.6 incluida. Esto se debe a una validación nonce faltante o incorrecta en múltiples funciones. Esto hace posible que atacantes no autenticados actualicen la configuración del complemento mediante una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/imagemapper/tags/1.2.6/imagemapper.php#L904", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/browser/imagemapper/tags/1.2.6/imagemapper.php#L916", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/browser/imagemapper/tags/1.2.6/imagemapper.php#L939", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/browser/imagemapper/tags/1.2.6/imagemapper.php#L958", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a128018b-f19b-4b18-a53c-cf1310d3d0e7?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
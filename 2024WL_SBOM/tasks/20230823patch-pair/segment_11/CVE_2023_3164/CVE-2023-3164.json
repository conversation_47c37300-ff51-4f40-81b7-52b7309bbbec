{"cve_id": "CVE-2023-3164", "published_date": "2023-11-02T12:15:09.543", "last_modified_date": "2024-11-21T08:16:36.097", "descriptions": [{"lang": "en", "value": "A heap-buffer-overflow vulnerability was found in LibTIFF, in extractImageSection() at tools/tiffcrop.c:7916 and tools/tiffcrop.c:7801. This flaw allows attackers to cause a denial of service via a crafted tiff file."}, {"lang": "es", "value": "Se encontró un error de lectura fuera de los límites en el paquete gawk de buildin.c. Este problema puede provocar un bloqueo y podría utilizarse para leer información confidencial."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-3164", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2213531", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://gitlab.com/libtiff/libtiff/-/issues/542", "source": "<EMAIL>", "tags": ["Issue Tracking"]}]}
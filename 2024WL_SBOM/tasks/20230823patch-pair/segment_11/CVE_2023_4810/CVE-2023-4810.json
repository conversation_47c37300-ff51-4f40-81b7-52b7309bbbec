{"cve_id": "CVE-2023-4810", "published_date": "2023-11-06T21:15:08.900", "last_modified_date": "2025-02-26T22:15:12.473", "descriptions": [{"lang": "en", "value": "The Responsive Pricing Table WordPress plugin before 5.1.8 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)"}, {"lang": "es", "value": "El complemento Responsive Pricing Table de WordPress anterior a 5.1.8 no sanitiza ni escapa a algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting (XSS) Almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración multisitio)."}], "references": [{"url": "https://portswigger.net/web-security/cross-site-scripting/stored", "source": "<EMAIL>", "tags": ["Technical Description", "Third Party Advisory"]}, {"url": "https://wpscan.com/vulnerability/dfde5436-dd5c-4c70-a9c2-3cb85cc99c0a", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
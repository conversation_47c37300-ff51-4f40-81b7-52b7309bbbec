{"cve_id": "CVE-2023-43885", "published_date": "2023-11-07T08:15:24.090", "last_modified_date": "2024-11-21T08:24:57.510", "descriptions": [{"lang": "en", "value": "Missing error handling in the HTTP server component of Tenda RX9 Pro Firmware V22.03.02.20 allows authenticated attackers to arbitrarily lock the device."}, {"lang": "es", "value": "La falta de manejo de errores en el componente del servidor HTTP del Tenda RX9 Pro Firmware V22.03.02.20 permite a atacantes autenticados bloquear arbitrariamente el dispositivo."}], "references": [{"url": "https://blog.rtlcopymemory.com/tenda-rx9-pro/", "source": "<EMAIL>", "tags": ["Exploit", "Technical Description", "Third Party Advisory"]}]}
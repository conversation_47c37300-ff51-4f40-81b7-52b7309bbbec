{"cve_id": "CVE-2023-5606", "published_date": "2023-11-02T09:15:08.507", "last_modified_date": "2025-05-12T15:09:58.850", "descriptions": [{"lang": "en", "value": "The ChatBot for WordPress is vulnerable to Stored Cross-Site Scripting via the FAQ Builder in versions 4.8.6 through 4.9.6 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled. NOTE: This vulnerability is a re-introduction of CVE-2023-4253."}, {"lang": "es", "value": "ChatBot para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de FAQ Builder en las versiones 4.8.6 a 4.9.6 debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con permisos de nivel de administrador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. Esto solo afecta a las instalaciones multisitio y a las instalaciones en las que se ha deshabilitado unfiltered_html. NOTA: Esta vulnerabilidad es una reintroducción de CVE-2023-4253."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=2987335%40chatbot%2Ftrunk&old=2986133%40chatbot%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fc305c48-8337-42b7-ad61-61aea8018def?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
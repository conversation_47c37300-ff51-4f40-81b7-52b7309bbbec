{"cve_id": "CVE-2023-20063", "published_date": "2023-11-01T18:15:08.967", "last_modified_date": "2024-11-26T16:09:02.407", "descriptions": [{"lang": "en", "value": "A vulnerability in the inter-device communication mechanisms between devices that are running Cisco Firepower Threat Defense (FTD) Software and devices that are running Cisco Firepower Management (FMC) Software could allow an authenticated, local attacker to execute arbitrary commands with root permissions on the underlying operating system of an affected device.\r\n\r\nThis vulnerability is due to insufficient validation of user-supplied input. An attacker could exploit this vulnerability by accessing the expert mode of an affected device and submitting specific commands to a connected system. A successful exploit could allow the attacker to execute arbitrary code in the context of an FMC device if the attacker has administrative privileges on an associated FTD device. Alternatively, a successful exploit could allow the attacker to execute arbitrary code in the context of an FTD device if the attacker has administrative privileges on an associated FMC device."}, {"lang": "es", "value": "Una vulnerabilidad en los mecanismos de comunicación entre dispositivos entre los dispositivos que ejecutan el software Cisco Firepower Threat Defense (FTD) y los dispositivos que ejecutan el software Cisco Firepower Management (FMC) podría permitir que un atacante local autenticado ejecute comandos arbitrarios con permisos de root en el sistema operativo subyacente de un dispositivo afectado. Esta vulnerabilidad se debe a una validación insuficiente de la entrada proporcionada por el usuario. Un atacante podría aprovechar esta vulnerabilidad accediendo al modo experto de un dispositivo afectado y enviando comandos específicos a un sistema conectado. Un exploit exitoso podría permitir al atacante ejecutar código arbitrario en el contexto de un dispositivo FMC si el atacante tiene privilegios administrativos en un dispositivo FTD asociado. Alternativamente, un exploit exitoso podría permitir al atacante ejecutar código arbitrario en el contexto de un dispositivo FTD si el atacante tiene privilegios administrativos en un dispositivo FMC asociado."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftd-fmc-code-inj-wSHrgz8L", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-20255", "published_date": "2023-11-01T18:15:09.700", "last_modified_date": "2024-11-21T07:41:00.387", "descriptions": [{"lang": "en", "value": "A vulnerability in an API of the Web Bridge feature of Cisco Meeting Server could allow an unauthenticated, remote attacker to cause a denial of service (DoS) condition. This vulnerability is due to insufficient validation of HTTP requests. An attacker could exploit this vulnerability by sending crafted HTTP packets to an affected device. A successful exploit could allow the attacker to cause a partial availability condition, which could cause ongoing video calls to be dropped due to the invalid packets reaching the Web Bridge."}, {"lang": "es", "value": "Una vulnerabilidad en una API de la función Web Bridge de Cisco Meeting Server podría permitir que un atacante remoto no autenticado provoque una condición de Denegación de Servicio (DoS). Esta vulnerabilidad se debe a una validación insuficiente de las solicitudes HTTP. Un atacante podría aprovechar esta vulnerabilidad enviando paquetes HTTP manipulados a un dispositivo afectado. Un exploit exitoso podría permitir que el atacante cause una condición de disponibilidad parcial, lo que podría causar que las video llamadas en curso se interrumpan debido a que los paquetes no válidos llegan al Web Bridge."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-cms-segfault-G6ES4Ve8", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
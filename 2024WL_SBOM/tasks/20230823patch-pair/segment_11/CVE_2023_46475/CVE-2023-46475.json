{"cve_id": "CVE-2023-46475", "published_date": "2023-11-02T13:15:08.413", "last_modified_date": "2024-11-21T08:28:33.980", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting vulnerability was discovered in ZenTao 18.3 where a user can create a project, and in the name field of the project, they can inject malicious JavaScript code."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de Cross-Site Scripting Almacenada en ZenTao 18.3 donde un usuario puede crear un proyecto y, en el campo de nombre del proyecto, puede inyectar código JavaScript malicioso."}], "references": [{"url": "https://github.com/easysoft/zentaopms", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/elementalSec/CVE-Disclosures/blob/main/ZentaoPMS/CVE-2023-46475/CVE-2023-46475%20-%20Cross-Site%20Scripting%20%28Stored%29.md", "source": "<EMAIL>", "tags": ["Exploit"]}]}
{"cve_id": "CVE-2023-46252", "published_date": "2023-11-07T19:15:11.573", "last_modified_date": "2024-11-21T08:28:10.417", "descriptions": [{"lang": "en", "value": "Squidex is an open source headless CMS and content management hub. Affected versions are missing origin verification in a postMessage handler which introduces a Cross-Site Scripting (XSS) vulnerability. The editor-sdk.js file defines three different class-like functions, which employ a global message event listener: SquidexSidebar, SquidexWidget, and SquidexFormField. The registered event listener takes some action based on the type of the received message. For example, when the SquidexFormField receives a message with the type valueChanged, the value property is updated. The SquidexFormField class is for example used in the editor-editorjs.html file, which can be accessed via the public wwwroot folder. It uses the onValueChanged method to register a callback function, which passes the value provided from the message event to the editor.render. Passing an attacker-controlled value to this function introduces a Cross-Site Scripting (XSS) vulnerability."}, {"lang": "es", "value": "Squidex es un centro de gestión de contenidos y CMS headless de código abierto. A las versiones afectadas les falta la verificación de origen en un controlador postMessage, lo que introduce una vulnerabilidad de Cross-Site Scripting (XSS). El archivo editor-sdk.js define tres funciones similares a clases diferentes, que emplean un detector de eventos de mensajes global: SquidexSidebar, SquidexWidget y SquidexFormField. El detector de eventos registrado realiza alguna acción según el tipo de mensaje recibido. Por ejemplo, cuando SquidexFormField recibe un mensaje con el tipo valueChanged, la propiedad del valor se actualiza. La clase SquidexFormField se utiliza, por ejemplo, en el archivo editor-editorjs.html, al que se puede acceder a través de la carpeta pública wwwroot. Utiliza el método onValueChanged para registrar una función de devolución de llamada, que pasa el valor proporcionado por el evento del mensaje al editor.render. Pasar un valor controlado por un atacante a esta función introduce una vulnerabilidad de Cross-Site Scripting (XSS)."}], "references": [{"url": "https://github.com/Squidex/squidex/security/advisories/GHSA-7q4f-fprr-5jw8", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
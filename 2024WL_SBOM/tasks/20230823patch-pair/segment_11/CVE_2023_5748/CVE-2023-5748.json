{"cve_id": "CVE-2023-5748", "published_date": "2023-11-07T04:24:19.670", "last_modified_date": "2024-11-21T08:42:24.430", "descriptions": [{"lang": "en", "value": "Buffer copy without checking size of input ('Classic Buffer Overflow') vulnerability in cgi component in Synology SSL VPN Client before 1.4.7-0687 allows local users to conduct denial-of-service attacks via unspecified vectors."}, {"lang": "es", "value": "La vulnerabilidad de copia de búfer sin comprobar el tamaño de la entrada ('Desbordamiento de búfer clásico') en el componente cgi en Synology SSL VPN Client anterior a 1.4.7-0687 permite a los usuarios locales realizar ataques de denegación de servicio a través de vectores no especificados."}], "references": [{"url": "https://www.synology.com/en-global/security/advisory/Synology_SA_23_12", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
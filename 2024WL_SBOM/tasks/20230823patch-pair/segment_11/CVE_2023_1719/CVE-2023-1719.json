{"cve_id": "CVE-2023-1719", "published_date": "2023-11-01T10:15:09.373", "last_modified_date": "2024-11-21T07:39:45.817", "descriptions": [{"lang": "en", "value": "Global variable extraction in bitrix/modules/main/tools.php in Bitrix24 22.0.300 allows unauthenticated remote attackers to (1) enumerate attachments on the server and (2) execute arbitrary JavaScript code in the victim's browser, and possibly execute arbitrary PHP code on the server if the victim has administrator privilege, via overwriting uninitialised variables."}, {"lang": "es", "value": "La extracción de variables globales en bitrix/modules/main/tools.php en Bitrix24 22.0.300 permite a atacantes remotos no autenticados (1) enumerar archivos adjuntos en el servidor y (2) ejecutar código JavaScript arbitrario en el navegador de la víctima, y posiblemente ejecutar código PHP arbitrario en el servidor si la víctima tiene privilegios de administrador, sobrescribiendo variables no inicializadas."}], "references": [{"url": "https://starlabs.sg/advisories/23/23-1719/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2023-33481", "published_date": "2023-11-07T15:15:10.793", "last_modified_date": "2024-11-21T08:05:37.747", "descriptions": [{"lang": "en", "value": "RemoteClinic 2.0 is vulnerable to a time-based blind SQL injection attack in the 'start' GET parameter of patients/index.php."}, {"lang": "es", "value": "RemoteClinic 2.0 es vulnerable a un ataque de inyección blind SQL basado en tiempo en el parámetro GET 'start' de patients/index.php."}], "references": [{"url": "https://github.com/remoteclinic/RemoteClinic/issues/25", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}]}
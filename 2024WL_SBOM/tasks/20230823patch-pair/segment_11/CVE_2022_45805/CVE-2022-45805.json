{"cve_id": "CVE-2022-45805", "published_date": "2023-11-03T13:15:08.227", "last_modified_date": "2024-11-21T07:29:45.210", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Paytm Paytm Payment Gateway paytm-payments allows SQL Injection.This issue affects Paytm Payment Gateway: from n/a through 2.7.3.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Paytm Paytm Payment Gateway paytm-paids permite la inyección SQL. Este problema afecta a Paytm Payment Gateway: desde n/a hasta 2.7.3."}], "references": [{"url": "https://patchstack.com/database/vulnerability/paytm-payments/wordpress-paytm-payment-gateway-plugin-2-7-3-auth-sql-injection-sqli-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
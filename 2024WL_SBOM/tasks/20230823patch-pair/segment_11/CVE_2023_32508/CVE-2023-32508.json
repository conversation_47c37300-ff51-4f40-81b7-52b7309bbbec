{"cve_id": "CVE-2023-32508", "published_date": "2023-11-03T17:15:08.693", "last_modified_date": "2024-11-21T08:03:30.027", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in <PERSON> Order Your Posts Manually allows SQL Injection.This issue affects Order Your Posts Manually: from n/a through 2.2.5.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en <PERSON> Order Your Posts Manually permite la inyección SQL. Este problema afecta Order Your Posts Manually: desde n/a hasta 2.2.5."}], "references": [{"url": "https://patchstack.com/database/vulnerability/order-your-posts-manually/wordpress-order-your-posts-manually-plugin-2-2-5-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
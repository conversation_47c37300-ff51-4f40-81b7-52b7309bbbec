{"cve_id": "CVE-2023-5567", "published_date": "2023-11-07T12:15:12.810", "last_modified_date": "2024-11-21T08:42:01.960", "descriptions": [{"lang": "en", "value": "The QR Code Tag plugin for WordPress is vulnerable to Stored Cross-Site Scripting via 'qrcodetag' shortcode in versions up to, and including, 1.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento QR Code Tag para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode 'qrcodetag' en versiones hasta la 1.0 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/qr-code-tag/trunk/lib/qrct/QrctWp.php?rev=1705525#L369", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/be004002-a3ac-46e9-b0c1-258f05f97b2a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
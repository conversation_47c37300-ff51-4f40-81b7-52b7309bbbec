{"cve_id": "CVE-2023-40609", "published_date": "2023-11-06T09:15:08.307", "last_modified_date": "2024-11-21T08:19:49.270", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Aiyaz, maheshpatel Contact form 7 Custom validation allows SQL Injection.This issue affects Contact form 7 Custom validation: from n/a through 1.1.3.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en la validación Aiyaz, maheshpatel Contact form 7 Custom personalizada permite la inyección de SQL. Este problema afecta la validación de Contact form 7 Custom: desde n/a hasta 1.1.3."}], "references": [{"url": "https://patchstack.com/database/vulnerability/cf7-field-validation/wordpress-contact-form-7-custom-validation-plugin-1-1-3-unauth-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
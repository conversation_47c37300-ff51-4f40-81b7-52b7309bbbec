{"cve_id": "CVE-2023-3961", "published_date": "2023-11-03T13:15:08.723", "last_modified_date": "2024-11-21T08:18:24.390", "descriptions": [{"lang": "en", "value": "A path traversal vulnerability was identified in Samba when processing client pipe names connecting to Unix domain sockets within a private directory. Samba typically uses this mechanism to connect SMB clients to remote procedure call (RPC) services like SAMR LSA or SPOOLSS, which Samba initiates on demand. However, due to inadequate sanitization of incoming client pipe names, allowing a client to send a pipe name containing Unix directory traversal characters (../). This could result in SMB clients connecting as root to Unix domain sockets outside the private directory. If an attacker or client managed to send a pipe name resolving to an external service using an existing Unix domain socket, it could potentially lead to unauthorized access to the service and consequential adverse events, including compromise or service crashes."}, {"lang": "es", "value": "Se identificó una vulnerabilidad de path traversal en Samba al procesar nombres de canalizaciones de clientes que se conectan a sockets de dominio Unix dentro de un directorio privado. Samba normalmente usa este mecanismo para conectar clientes SMB a servicios de Remote Procedure Call (RPC) como SAMR LSA o SPOOLSS, que Samba inicia bajo demanda. Sin embargo, debido a una sanitización inadecuada de los nombres de canalización del cliente entrante, se permite que un cliente envíe un nombre de canalización que contenga caracteres transversales del directorio Unix (../). Esto podría provocar que los clientes SMB se conecten como root a sockets de dominio Unix fuera del directorio privado. Si un atacante o cliente lograra enviar un nombre de canalización a un servicio externo utilizando un socket de dominio Unix existente, podría provocar un acceso no autorizado al servicio y los consiguientes eventos adversos, incluido el compromiso o la caída del servicio."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6209", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6744", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7371", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7408", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7464", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7467", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-3961", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2241881", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://bugzilla.samba.org/show_bug.cgi?id=15422", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://www.samba.org/samba/security/CVE-2023-3961.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/ZUMVALLFFDFC53JZMUWA6HPD7HUGAP5I/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231124-0002/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
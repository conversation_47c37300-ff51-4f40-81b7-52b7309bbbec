{"cve_id": "CVE-2022-45373", "published_date": "2023-11-06T08:15:21.547", "last_modified_date": "2024-11-21T07:29:08.157", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in <PERSON>, VeronaLabs Slimstat Analytics allows SQL Injection.This issue affects Slimstat Analytics: from n/a through 5.0.4.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en <PERSON>, VeronaLabs Slimstat Analytics permite la inyección SQL. Este problema afecta a Slimstat Analytics: desde n/a hasta 5.0.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-slimstat/wordpress-slimstat-analytics-plugin-5-0-4-sql-injection-sqli-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
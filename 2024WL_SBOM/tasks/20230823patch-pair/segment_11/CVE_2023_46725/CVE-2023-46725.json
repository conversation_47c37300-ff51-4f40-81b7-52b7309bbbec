{"cve_id": "CVE-2023-46725", "published_date": "2023-11-02T15:15:08.847", "last_modified_date": "2024-11-21T08:29:09.810", "descriptions": [{"lang": "en", "value": "FoodCoopShop is open source software for food coops and local shops. Versions starting with 3.2.0 prior to 3.6.1 are vulnerable to server-side request forgery. In the Network module, a manufacturer account can use the `/api/updateProducts.json` endpoint to make the server send a request to an arbitrary host. This means that the server can be used as a proxy into the internal network where the server is. Furthermore, the checks on a valid image are not adequate, leading to a time of check time of use issue. For example, by using a custom server that returns 200 on HEAD requests, then return a valid image on first GET request and then a 302 redirect to final target on second GET request, the server will copy whatever file is at the redirect destination, making this a full SSRF. Version 3.6.1 fixes this vulnerability."}, {"lang": "es", "value": "FoodCoopShop es un software de código abierto para cooperativas de alimentos y tiendas locales. Las versiones que comienzan con 3.2.0 anteriores a 3.6.1 son vulnerables a server-side request forgery. En el módulo de Network, una cuenta de fabricante puede usar el endpoint `/api/updateProducts.json` para hacer que el servidor envíe una solicitud a un host arbitrario. Esto significa que el servidor se puede utilizar como proxy en la red interna donde se encuentra el servidor. Además, las comprobaciones de una imagen válida no son adecuadas, lo que genera un problema de tiempo de verificación de uso. Por ejemplo, al usar un servidor personalizado que devuelve 200 en solicitudes HEAD, luego devuelve una imagen válida en la primera solicitud GET y luego una redirección 302 al destino final en la segunda solicitud GET, el servidor copiará cualquier archivo que esté en el destino de la redirección, haciendo Esta es una SSRF completa. La versión 3.6.1 corrige esta vulnerabilidad."}], "references": [{"url": "https://github.com/foodcoopshop/foodcoopshop/commit/0d5bec5c4c22e1affe7fd321a30e3f3a4d99e808", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/foodcoopshop/foodcoopshop/pull/972", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/foodcoopshop/foodcoopshop/security/advisories/GHSA-jhww-fx2j-3rf7", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://pastebin.com/8K5Brwbq", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}
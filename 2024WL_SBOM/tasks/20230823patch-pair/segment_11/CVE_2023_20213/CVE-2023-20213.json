{"cve_id": "CVE-2023-20213", "published_date": "2023-11-01T17:15:11.500", "last_modified_date": "2024-11-21T07:40:54.080", "descriptions": [{"lang": "en", "value": "A vulnerability in the CDP processing feature of Cisco ISE could allow an unauthenticated, adjacent attacker to cause a denial of service (DoS) condition of the CDP process on an affected device. This vulnerability is due to insufficient bounds checking when an affected device processes CDP traffic. An attacker could exploit this vulnerability by sending crafted CDP traffic to the device. A successful exploit could cause the CDP process to crash, impacting neighbor discovery and the ability of Cisco ISE to determine the reachability of remote devices. After a crash, the CDP process must be manually restarted using the cdp enable command in interface configuration mode."}, {"lang": "es", "value": "Una vulnerabilidad en la función de procesamiento CDP de Cisco ISE podría permitir que un atacante adyacente no autenticado cause una condición de Denegación de Servicio (DoS) del proceso CDP en un dispositivo afectado. Esta vulnerabilidad se debe a una verificación de los límites insuficiente cuando un dispositivo afectado procesa el tráfico CDP. Un atacante podría aprovechar esta vulnerabilidad enviando tráfico CDP manipulado al dispositivo. Un exploit exitoso podría provocar que el proceso CDP falle, lo que afectaría el descubrimiento de vecinos y la capacidad de Cisco ISE para determinar la accesibilidad de los dispositivos remotos. Después de una falla, el proceso CDP se debe reiniciar manualmente usando el comando cdp enable en el modo de configuración de interfaz."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ise-file-upload-FceLP4xs", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2021-4430", "published_date": "2023-11-06T08:15:21.343", "last_modified_date": "2024-11-21T06:37:42.570", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic has been found in Ortus Solutions ColdBox Elixir 3.1.6. This affects an unknown part of the file src/defaultConfig.js of the component ENV Variable Handler. The manipulation leads to information disclosure. Upgrading to version 3.1.7 is able to address this issue. The identifier of the patch is a3aa62daea2e44c76d08d1eac63768cd928cd69e. It is recommended to upgrade the affected component. The identifier VDB-244485 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad ha sido encontrada en Ortus Solutions ColdBox Elixir 3.1.6 y clasificada como problemática. Una parte desconocida del archivo src/defaultConfig.js del componente ENV Variable Handler afecta a una parte desconocida. La manipulación conduce a la divulgación de información. La actualización a la versión 3.1.7 puede solucionar este problema. El identificador del parche es a3aa62daea2e44c76d08d1eac63768cd928cd69e. Se recomienda actualizar el componente afectado. A esta vulnerabilidad se le asignó el identificador VDB-244485."}], "references": [{"url": "https://github.com/Ortus-Solutions/coldbox-elixir/commit/a3aa62daea2e44c76d08d1eac63768cd928cd69e", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/Ortus-Solutions/coldbox-elixir/releases/tag/v3.1.7", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://vuldb.com/?ctiid.244485", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.244485", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}]}
{"cve_id": "CVE-2023-47004", "published_date": "2023-11-06T22:15:08.043", "last_modified_date": "2025-04-29T20:15:24.890", "descriptions": [{"lang": "en", "value": "Buffer Overflow vulnerability in Redis RedisGraph v.2.x through v.2.12.8 and fixed in v.2.12.9 allows an attacker to execute arbitrary code via the code logic after valid authentication."}, {"lang": "es", "value": "La vulnerabilidad de desbordamiento del búfer en Redis RedisGraph v.2.x a v.2.12.8 y corregida en v.2.12.9 permite a un atacante ejecutar código arbitrario a través de la lógica del código después de una autenticación válida."}], "references": [{"url": "https://github.com/RedisGraph/RedisGraph/issues/3178", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-44025", "published_date": "2023-11-01T22:15:08.597", "last_modified_date": "2024-11-21T08:25:09.093", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in addify Addifyfreegifts v.1.0.2 and before allows a remote attacker to execute arbitrary code via a crafted script to the getrulebyid function in the AddifyfreegiftsModel.php component."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en addify Addifyfreegifts v.1.0.2 y anteriores permite a un atacante remoto ejecutar código arbitrario a través de un script manipulado para la función getrulebyid en el componente AddifyfreegiftsModel.php."}], "references": [{"url": "https://security.friendsofpresta.org/modules/2023/10/31/addifyfreegifts.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46730", "published_date": "2023-11-07T18:15:08.973", "last_modified_date": "2024-11-21T08:29:10.773", "descriptions": [{"lang": "en", "value": "Group-Office is an enterprise CRM and groupware tool. In affected versions there is full Server-Side Request Forgery (SSRF) vulnerability in the /api/upload.php endpoint. The /api/upload.php endpoint does not filter URLs which allows a malicious user to cause the server to make resource requests to untrusted domains. Note that protocols like file:// can also be used to access the server disk. The request result (on success) can then be retrieved using /api/download.php. This issue has been addressed in versions 6.8.15, 6.7.54, and 6.6.177. Users are advised to upgrade. There are no known workarounds for this vulnerability."}, {"lang": "es", "value": "Group-Office es una herramienta de software colaborativo y CRM empresarial. En las versiones afectadas hay una vulnerabilidad completa de Server-Side Request Forgery (SSRF) en el endpoint /api/upload.php. El endpoint /api/upload.php no filtra las URL, lo que permite que un usuario malintencionado haga que el servidor realice solicitudes de recursos a dominios que no son de confianza. Tenga en cuenta que protocolos como file:// también se pueden utilizar para acceder al disco del servidor. El resultado de la solicitud (en caso de éxito) se puede recuperar usando /api/download.php. Este problema se solucionó en las versiones 6.8.15, 6.7.54 y 6.6.177. Se recomienda a los usuarios que actualicen. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/Intermesh/groupoffice/commit/99205535e8cec6592fd7f1469837926f27c72d50", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/Intermesh/groupoffice/security/advisories/GHSA-vw6c-h82w-mvfv", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
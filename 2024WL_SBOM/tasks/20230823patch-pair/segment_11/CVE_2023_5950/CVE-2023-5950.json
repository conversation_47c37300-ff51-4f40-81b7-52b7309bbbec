{"cve_id": "CVE-2023-5950", "published_date": "2023-11-06T15:15:14.857", "last_modified_date": "2024-11-21T08:42:50.700", "descriptions": [{"lang": "en", "value": "Rapid7 Velociraptor versions prior to 0.7.0-4 suffer from a reflected cross site scripting vulnerability. This vulnerability allows attackers to inject JS into the error path, potentially leading to unauthorized execution of scripts within a user's web browser. This vulnerability is fixed in version 0.7.0-04 and a patch is available to download. Patches are also available for version 0.6.9 (0.6.9-1).\n\n"}, {"lang": "es", "value": "Las versiones de Rapid7 Velociraptor anteriores a 0.7.0-4 sufren de una vulnerabilidad de cross site scripting. Esta vulnerabilidad permite a los atacantes inyectar JS en la ruta del error, lo que podría provocar la ejecución no autorizada de scripts dentro del navegador web de un usuario. Esta vulnerabilidad se solucionó en la versión 0.7.0-04 y hay un parche disponible para descargar. También hay parches disponibles para la versión 0.6.9 (0.6.9-1)."}], "references": [{"url": "https://github.com/Velocidex/velociraptor/releases/tag/v0.7.0", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
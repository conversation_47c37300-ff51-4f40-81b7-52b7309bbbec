{"cve_id": "CVE-2023-46501", "published_date": "2023-11-07T18:15:08.930", "last_modified_date": "2024-11-21T08:28:36.950", "descriptions": [{"lang": "en", "value": "An issue in BoltWire v.6.03 allows a remote attacker to obtain sensitive information via a crafted payload to the view and change admin password function."}, {"lang": "es", "value": "Un problema en BoltWire v.6.03 permite a un atacante remoto obtener información confidencial a través de un payload manipulado para la función de ver y cambiar la contraseña de administrador."}], "references": [{"url": "https://github.com/Cyber-Wo0dy/CVE-2023-46501", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/Cyber-Wo0dy/report/blob/main/boltwire/v6.03/boltwire_improper_access_control", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
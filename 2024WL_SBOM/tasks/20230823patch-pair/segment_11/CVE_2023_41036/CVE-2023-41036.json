{"cve_id": "CVE-2023-41036", "published_date": "2023-11-07T04:20:50.427", "last_modified_date": "2024-11-21T08:20:25.713", "descriptions": [{"lang": "en", "value": "<PERSON>vi<PERSON> is a text editor for MacOS. Prior to version 178, Macvim makes use of an insecure interprocess communication (IPC) mechanism which could lead to a privilege escalation. Distributed objects are a concept introduced by Apple which allow one program to vend an interface to another program. What is not made clear in the documentation is that this service can vend this interface to any other program on the machine. The impact of exploitation is a privilege escalation to root - this is likely to affect anyone who is not careful about the software they download and use MacVim to edit files that would require root privileges. Version 178 contains a fix for this issue."}, {"lang": "es", "value": "Macvim es un editor de texto para MacOS. Antes de la versión 178, Macvim utiliza un mecanismo de Insecure Interprocess Communication (IPC) que podría provocar una escalada de privilegios. Los objetos distribuidos son un concepto introducido por Apple que permite que un programa proporcione una interfaz a otro programa. Lo que no queda claro en la documentación es que este servicio puede vender esta interfaz a cualquier otro programa en la máquina. El impacto de la explotación es una escalada de privilegios a root; esto probablemente afectará a cualquiera que no tenga cuidado con el software que descarga y use MacVim para editar archivos que requerirían privilegios de root. La versión 178 contiene una solución para este problema."}], "references": [{"url": "https://github.com/macvim-dev/macvim/blob/d9de087dddadbfd82fcb5dc9734380a2f829bd0a/src/MacVim/MMAppController.h#L28", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/macvim-dev/macvim/blob/d9de087dddadbfd82fcb5dc9734380a2f829bd0a/src/MacVim/MMBackend.h", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/macvim-dev/macvim/commit/399b43e9e1dbf656a1780e87344f4d3c875e4cda", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/macvim-dev/macvim/security/advisories/GHSA-9jgj-jfwg-99fv", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
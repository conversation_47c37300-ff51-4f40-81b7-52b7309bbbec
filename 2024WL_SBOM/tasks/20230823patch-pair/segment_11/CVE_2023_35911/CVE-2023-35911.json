{"cve_id": "CVE-2023-35911", "published_date": "2023-11-06T09:15:07.947", "last_modified_date": "2025-02-26T22:15:10.810", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Creative Solutions Contact Form Generator : Creative form builder for WordPress allows SQL Injection.This issue affects Contact Form Generator : Creative form builder for WordPress: from n/a through 2.6.0.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en Creative Solutions Contact Form Generator: el creador de formularios creativos para WordPress permite la inyección SQL. Este problema afecta al Contact Form Generator: creador de formularios creativos para WordPress: de n/a hasta 2.6.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/contact-form-generator/wordpress-contact-form-generator-plugin-2-6-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-4154", "published_date": "2023-11-07T20:15:08.683", "last_modified_date": "2024-11-21T08:34:29.827", "descriptions": [{"lang": "en", "value": "A design flaw was found in Samba's DirSync control implementation, which exposes passwords and secrets in Active Directory to privileged users and Read-Only Domain Controllers (RODCs). This flaw allows RODCs and users possessing the GET_CHANGES right to access all attributes, including sensitive secrets and passwords. Even in a default setup, RODC DC accounts, which should only replicate some passwords, can gain access to all domain secrets, including the vital krbtgt, effectively eliminating the RODC / DC distinction. Furthermore, the vulnerability fails to account for error conditions (fail open), like out-of-memory situations, potentially granting access to secret attributes, even under low-privileged attacker influence."}, {"lang": "es", "value": "Se encontró una falla de diseño en la implementación del control DirSync de Samba, que expone contraseñas y secretos en Active Directory a usuarios privilegiados y Controladores de Dominio de Solo Lectura (RODC). Esta falla permite a los RODC y a los usuarios que poseen el derecho GET_CHANGES acceder a todos los atributos, incluidos secretos y contraseñas confidenciales. Incluso en una configuración predeterminada, las cuentas RODC DC, que solo deberían replicar algunas contraseñas, pueden obtener acceso a todos los secretos del dominio, incluido el vital krbtgt, eliminando efectivamente la distinción RODC/DC. Además, la vulnerabilidad no tiene en cuenta las condiciones de error (fallo de apertura), como situaciones de falta de memoria, lo que potencialmente otorga acceso a atributos secretos, incluso bajo la influencia de un atacante con pocos privilegios."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-4154", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2241883", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://bugzilla.samba.org/show_bug.cgi?id=15424", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.samba.org/samba/security/CVE-2023-4154.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
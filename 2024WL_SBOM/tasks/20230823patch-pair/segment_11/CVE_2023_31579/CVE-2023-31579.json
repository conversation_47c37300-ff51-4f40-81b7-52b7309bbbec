{"cve_id": "CVE-2023-31579", "published_date": "2023-11-02T22:15:08.640", "last_modified_date": "2024-11-21T08:02:02.700", "descriptions": [{"lang": "en", "value": "<PERSON><PERSON><PERSON> before v3.8.1 was discovered to use a hardcoded cryptographic key when creating and verifying a Json Web Token. This vulnerability allows attackers to authenticate to the application via a crafted JWT token."}, {"lang": "es", "value": "Se descubrió que Dromara Lamp-Cloud anterior a v3.8.1 utiliza una clave criptográfica codificada al crear y verificar un Token Web Json. Esta vulnerabilidad permite a los atacantes autenticarse en la aplicación mediante un token JWT manipulado."}], "references": [{"url": "https://github.com/dromara/lamp-cloud/issues/183", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://github.com/xubowenW/JWTissues/blob/main/lamp%20issue.md", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-31020", "published_date": "2023-11-02T19:15:41.247", "last_modified_date": "2024-11-21T08:01:15.670", "descriptions": [{"lang": "en", "value": "NVIDIA GPU Display Driver for Windows contains a vulnerability in the kernel mode layer, where an unprivileged regular user can cause improper access control, which may lead to denial of service or data tampering."}, {"lang": "es", "value": "NVIDIA GPU Display Driver para Windows contiene una vulnerabilidad en la capa del modo kernel, donde un usuario normal sin privilegios puede provocar un control de acceso inadecuado, lo que puede provocar denegación de servicio o manipulación de datos."}], "references": [{"url": "https://nvidia.custhelp.com/app/answers/detail/a_id/5491", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
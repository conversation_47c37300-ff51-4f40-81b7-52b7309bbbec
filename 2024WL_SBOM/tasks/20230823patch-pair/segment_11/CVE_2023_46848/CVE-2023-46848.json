{"cve_id": "CVE-2023-46848", "published_date": "2023-11-03T08:15:08.117", "last_modified_date": "2024-11-21T08:29:25.190", "descriptions": [{"lang": "en", "value": "Squid is vulnerable to Denial of Service,  where a remote attacker can perform DoS by sending ftp:// URLs in HTTP Request messages or constructing ftp:// URLs from FTP Native input."}, {"lang": "es", "value": "Squid es vulnerable a la Denegación de Servicio, donde un atacante remoto puede realizar DoS enviando URL ftp:// en mensajes de solicitud HTTP o construyendo URL ftp:// a partir de una entrada nativa FTP."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6266", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6268", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6748", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-46848", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2245919", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/squid-cache/squid/security/advisories/GHSA-2g3c-pg7q-g59w", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
{"cve_id": "CVE-2023-20267", "published_date": "2023-11-01T18:15:09.810", "last_modified_date": "2024-11-21T07:41:02.150", "descriptions": [{"lang": "en", "value": "A vulnerability in the IP geolocation rules of Snort 3 could allow an unauthenticated, remote attacker to potentially bypass IP address restrictions. This vulnerability exists because the configuration for IP geolocation rules is not parsed properly. An attacker could exploit this vulnerability by spoofing an IP address until they bypass the restriction. A successful exploit could allow the attacker to bypass location-based IP address restrictions."}, {"lang": "es", "value": "Una vulnerabilidad en las reglas de geolocalización de IP de Snort 3 podría permitir que un atacante remoto no autenticado potencialmente evite las restricciones de direcciones IP. Esta vulnerabilidad existe porque la configuración de las reglas de geolocalización de IP no se analiza correctamente. Un atacante podría aprovechar esta vulnerabilidad falsificando una dirección IP hasta omitir la restricción. Un exploit exitoso podría permitir al atacante omitir las restricciones de direcciones IP basadas en la ubicación."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftdsnort3sip-bypass-LMz2ThKn", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
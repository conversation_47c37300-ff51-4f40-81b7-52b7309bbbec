{"cve_id": "CVE-2023-36677", "published_date": "2023-11-03T23:15:08.417", "last_modified_date": "2024-11-21T08:10:20.430", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Smartypants SP Project & Document Manager allows SQL Injection.This issue affects SP Project & Document Manager: from n/a through 4.67.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Smartypants SP Project &amp; Document Manager permite la inyección SQL. Este problema afecta a SP Project &amp; Document Manager: desde n/a hasta 4.67."}], "references": [{"url": "https://patchstack.com/database/vulnerability/sp-client-document-manager/wordpress-sp-project-document-manager-plugin-4-67-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-44954", "published_date": "2023-11-01T23:15:07.900", "last_modified_date": "2024-11-21T08:26:09.330", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerability in BigTree CMS v.4.5.7 allows a remote attacker to execute arbitrary code via the ID parameter in the Developer Settings functions."}, {"lang": "es", "value": "Una vulnerabilidad de  Cross Site Scripting en BigTree CMS v.4.5.7 permite a un atacante remoto ejecutar código arbitrario a través del parámetro ID en las funciones de configuración del desarrollador."}], "references": [{"url": "https://github.com/<PERSON><PERSON>-<PERSON>/BigTree_CMS-Stored_XSS-Developer_Settings/blob/main/README.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.bigtreecms.org/download/core/", "source": "<EMAIL>", "tags": ["Product"]}]}
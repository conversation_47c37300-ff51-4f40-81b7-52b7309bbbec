{"cve_id": "CVE-2023-20244", "published_date": "2023-11-01T17:15:11.577", "last_modified_date": "2024-11-21T07:40:58.750", "descriptions": [{"lang": "en", "value": "A vulnerability in the internal packet processing of Cisco Firepower Threat Defense (FTD) Software for Cisco Firepower 2100 Series Firewalls could allow an unauthenticated, remote attacker to cause a denial of service (DoS) condition on an affected device. This vulnerability is due to improper handling of certain packets when they are sent to the inspection engine. An attacker could exploit this vulnerability by sending a series of crafted packets to an affected device. A successful exploit could allow the attacker to deplete all 9,472 byte blocks on the device, resulting in traffic loss across the device or an unexpected reload of the device. If the device does not reload on its own, a manual reload of the device would be required to recover from this state."}, {"lang": "es", "value": "Una vulnerabilidad en el procesamiento interno de paquetes del software Cisco Firepower Threat Defense (FTD) para los firewalls Cisco Firepower serie 2100 podría permitir que un atacante remoto no autenticado cause una condición de Denegación de Servicio (DoS) en un dispositivo afectado. Esta vulnerabilidad se debe al manejo inadecuado de ciertos paquetes cuando se envían al motor de inspección. Un atacante podría aprovechar esta vulnerabilidad enviando una serie de paquetes manipulados a un dispositivo afectado. Un exploit exitoso podría permitir al atacante agotar los bloques de 9472 bytes del dispositivo, lo que provocaría una pérdida de tráfico en el dispositivo o una recarga inesperada del mismo. Si el dispositivo no se recarga por sí solo, será necesaria una recarga manual del dispositivo para recuperarse de este estado."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftd-intrusion-dos-DfT7wyGC", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
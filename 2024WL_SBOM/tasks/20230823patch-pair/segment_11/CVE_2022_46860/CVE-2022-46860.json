{"cve_id": "CVE-2022-46860", "published_date": "2023-11-06T08:15:21.690", "last_modified_date": "2024-11-21T07:31:11.490", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in KaizenCoders Short URL allows SQL Injection.This issue affects Short URL: from n/a through 1.6.4.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en KaizenCoders Short URL permite la inyección SQL. Este problema afecta Short URL: desde n/a hasta 1.6.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/shorten-url/wordpress-short-url-plugin-1-6-4-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
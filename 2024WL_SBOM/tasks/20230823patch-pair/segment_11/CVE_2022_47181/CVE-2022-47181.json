{"cve_id": "CVE-2022-47181", "published_date": "2023-11-07T18:15:08.290", "last_modified_date": "2025-02-19T22:15:11.130", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in wpexpertsio Email Templates Customizer and Designer for WordPress and WooCommerce email-templates allows Cross Site Request Forgery.This issue affects Email Templates Customizer and Designer for WordPress and WooCommerce: from n/a through 1.4.2.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en wpexpertsio Email Templates Customizer and Designer para WordPress y WooCommerce permite Cross-Site Request Forgery (CSRF). Este problema afecta Email Templates Customizer and Designer for WordPress and WooCommerce: desde n/a hasta 1.4.2 ."}], "references": [{"url": "https://patchstack.com/database/vulnerability/email-templates/wordpress-email-templates-plugin-1-4-2-cross-site-request-forgery-csrf?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
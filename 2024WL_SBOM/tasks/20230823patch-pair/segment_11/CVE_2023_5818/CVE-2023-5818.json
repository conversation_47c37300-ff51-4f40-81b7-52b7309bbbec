{"cve_id": "CVE-2023-5818", "published_date": "2023-11-07T20:15:09.297", "last_modified_date": "2024-11-21T08:42:33.313", "descriptions": [{"lang": "en", "value": "The Amazonify plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 0.8.1. This is due to missing or incorrect nonce validation on the amazonifyOptionsPage() function. This makes it possible for unauthenticated attackers to update the plugins settings, including the Amazon Tracking ID, via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Amazonify para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en todas las versiones hasta la 0.8.1 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función amazonifyOptionsPage(). Esto hace posible que atacantes no autenticados actualicen la configuración de los complementos, incluido el ID de seguimiento de Amazon, a través de una solicitud falsificada, ya que pueden engañar a un administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/amazonify/trunk/amazonify.php#L142", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/33f3c466-bdeb-402f-bf34-bc703f35e1e2?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-5090", "published_date": "2023-11-06T11:15:09.670", "last_modified_date": "2024-11-21T08:41:02.793", "descriptions": [{"lang": "en", "value": "A flaw was found in KVM. An improper check in svm_set_x2apic_msr_interception() may allow direct access to host x2apic msrs when the guest resets its apic, potentially leading to a denial of service condition."}, {"lang": "es", "value": "Se encontró una falla en KVM. Una verificación incorrecta en svm_set_x2apic_msr_interception() puede permitir el acceso directo al host x2apic msrs cuando el invitado restablece su apic, lo que podría provocar una condición de denegación de servicio."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2024:2758", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:3854", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:3855", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:4211", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:4352", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-5090", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2248122", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}]}
{"cve_id": "CVE-2023-23369", "published_date": "2023-11-03T17:15:08.327", "last_modified_date": "2024-11-21T07:46:02.830", "descriptions": [{"lang": "en", "value": "An OS command injection vulnerability has been reported to affect several QNAP operating system versions. If exploited, the vulnerability could allow users to execute commands via a network.\n\nWe have already fixed the vulnerability in the following versions:\nMultimedia Console 2.1.2 ( 2023/05/04 ) and later\nMultimedia Console 1.4.8 ( 2023/05/05 ) and later\nQTS 5.1.0.2399 build 20230515 and later\nQTS 4.3.6.2441 build 20230621 and later\nQTS 4.3.4.2451 build 20230621 and later\nQTS 4.3.3.2420 build 20230621 and later\nQTS 4.2.6 build 20230621 and later\nMedia Streaming add-on 500.1.1.2 ( 2023/06/12 ) and later\nMedia Streaming add-on 500.0.0.11 ( 2023/06/16 ) and later\n"}, {"lang": "es", "value": "Se ha informado que una vulnerabilidad de inyección de comandos del sistema operativo afecta a varias versiones del sistema operativo QNAP. Si se explota, la vulnerabilidad podría permitir a los usuarios ejecutar comandos a través de una red. Ya hemos solucionado la vulnerabilidad en las siguientes versiones: Multimedia Console 2.1.2 ( 2023/05/04 ) y posteriores Multimedia Console 1.4.8 ( 2023/05/05 ) y posteriores QTS 5.1.0.2399 build 20230515 y posteriores QTS 4.3.6.2441 build 20230621 y posteriores QTS 4.3.4.2451 build 20230621 y posteriores QTS 4.3.3.2420 build 20230621 y posteriores QTS 4.2.6 build 20230621 y posteriores Media Streaming add-on 500.1.1.2 ( 2023/06/12 ) y posteriores Media Streaming add-on 500.0.0.11 ( 2023/06/16 ) y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-35", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
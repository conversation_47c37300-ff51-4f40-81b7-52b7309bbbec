{"cve_id": "CVE-2022-47428", "published_date": "2023-11-06T08:15:21.830", "last_modified_date": "2024-11-21T07:31:56.490", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in WpDevArt Booking calendar, Appointment Booking System allows SQL Injection.This issue affects Booking calendar, Appointment Booking System: from n/a through 3.2.7.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en WpDevArt Booking calendar, Appointment Booking System permite la inyección de SQL. Este problema afecta Booking calendar, Appointment Booking System: desde n/a hasta 3.2.7."}], "references": [{"url": "https://patchstack.com/database/vulnerability/booking-calendar/wordpress-booking-calendar-appointment-booking-system-plugin-3-2-6-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-38391", "published_date": "2023-11-04T00:15:08.730", "last_modified_date": "2024-11-21T08:13:28.047", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Themesgrove Onepage Builder allows SQL Injection.This issue affects Onepage Builder: from n/a through 2.4.1.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Themesgrove Onepage Builder permite la inyección SQL. Este problema afecta a Onepage Builder: desde n/a hasta 2.4.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/tx-onepager/wordpress-onepage-builder-easiest-landing-page-builder-for-wordpress-plugin-2-4-1-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
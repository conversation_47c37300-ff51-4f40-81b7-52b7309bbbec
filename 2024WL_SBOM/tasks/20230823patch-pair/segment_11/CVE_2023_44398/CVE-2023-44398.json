{"cve_id": "CVE-2023-44398", "published_date": "2023-11-06T18:15:08.380", "last_modified_date": "2024-11-21T08:25:49.320", "descriptions": [{"lang": "en", "value": "Exiv2 is a C++ library and a command-line utility to read, write, delete and modify Exif, IPTC, XMP and ICC image metadata. An out-of-bounds write was found in Exiv2 version v0.28.0. The vulnerable function, `BmffImage::brotliUncompress`, is new in v0.28.0, so earlier versions of Exiv2 are _not_ affected. The out-of-bounds write is triggered when Exiv2 is used to read the metadata of a crafted image file. An attacker could potentially exploit the vulnerability to gain code execution, if they can trick the victim into running Exiv2 on a crafted image file. This bug is fixed in version v0.28.1. Users are advised to upgrade. There are no known workarounds for this vulnerability."}, {"lang": "es", "value": "Exiv2 es una librería de C++ y una utilidad de línea de comandos para leer, escribir, eliminar y modificar metadatos de imágenes Exif, IPTC, XMP e ICC. Se encontró una escritura fuera de los límites en la versión v0.28.0 de Exiv2. La función vulnerable, `BmffImage::brotliUncompress`, es nueva en v0.28.0, por lo que las versiones anteriores de Exiv2 no se ven afectadas. La escritura fuera de los límites se activa cuando se utiliza Exiv2 para leer los metadatos de un archivo de imagen manipulado. Un atacante podría explotar la vulnerabilidad para obtener la ejecución del código, si puede engañar a la víctima para que ejecute Exiv2 en un archivo de imagen manipulado. Este error se solucionó en la versión v0.28.1. Se recomienda a los usuarios que actualicen. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/Exiv2/exiv2/commit/e884a0955359107f4031c74a07406df7e99929a5", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/Exiv2/exiv2/security/advisories/GHSA-hrw9-ggg3-3r4r", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://security.gentoo.org/glsa/202312-06", "source": "<EMAIL>", "tags": []}]}
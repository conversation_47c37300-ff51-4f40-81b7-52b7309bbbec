{"cve_id": "CVE-2023-41344", "published_date": "2023-11-03T07:15:14.190", "last_modified_date": "2024-11-21T08:21:06.913", "descriptions": [{"lang": "en", "value": "NCSIST ManageEngine Mobile Device Manager(MDM) APP's special function has a path traversal vulnerability. An unauthenticated remote attacker can exploit this vulnerability to bypass authentication and read arbitrary system files."}, {"lang": "es", "value": "La función especial de la aplicación NCSIST ManageEngine Mobile Device Manager (MDM) tiene una vulnerabilidad de path traversal. Un atacante remoto no autenticado puede aprovechar esta vulnerabilidad para omitir la autenticación y leer archivos arbitrarios del sistema."}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7507-55b28-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
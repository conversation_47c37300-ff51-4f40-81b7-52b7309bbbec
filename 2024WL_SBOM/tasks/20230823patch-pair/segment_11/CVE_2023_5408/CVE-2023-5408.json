{"cve_id": "CVE-2023-5408", "published_date": "2023-11-02T03:15:10.230", "last_modified_date": "2024-11-21T08:41:42.800", "descriptions": [{"lang": "en", "value": "A privilege escalation flaw was found in the node restriction admission plugin of the kubernetes api server of OpenShift. A remote attacker who modifies the node role label could steer workloads from the control plane and etcd nodes onto different worker nodes and gain broader access to the cluster."}, {"lang": "es", "value": "Se encontró una falla de escalada de privilegios en el complemento de admisión de restricción de nodos del servidor API de Kubernetes de OpenShift. Un atacante remoto que modifique la etiqueta de función del nodo podría dirigir cargas de trabajo desde el plano de control y los nodos etcd a diferentes nodos trabajadores y obtener un acceso más amplio al clúster."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:5006", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6130", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6842", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7479", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-5408", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2242173", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://github.com/openshift/kubernetes/pull/1736", "source": "<EMAIL>", "tags": ["Issue Tracking"]}]}
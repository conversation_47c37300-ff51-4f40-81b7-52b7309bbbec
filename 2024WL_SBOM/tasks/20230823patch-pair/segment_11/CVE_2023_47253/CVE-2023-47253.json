{"cve_id": "CVE-2023-47253", "published_date": "2023-11-06T06:15:40.957", "last_modified_date": "2025-07-07T18:50:25.087", "descriptions": [{"lang": "en", "value": "Qualitor through 8.20 allows remote attackers to execute arbitrary code via PHP code in the html/ad/adpesquisasql/request/processVariavel.php gridValoresPopHidden parameter."}, {"lang": "es", "value": "Qualitor hasta 8.20 permite a atacantes remotos ejecutar código arbitrario mediante código PHP en el parámetro html/ad/adpesquisasql/request/processVariavel.php gridValoresPopHidden."}], "references": [{"url": "https://openxp.xpsec.co/blog/cve-2023-47253", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.linkedin.com/in/hairrison-wenning-4631a4124/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.linkedin.com/in/xvinicius/", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://www.qualitor.com.br/official-security-advisory-cve-2023-47253", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.qualitor.com.br/qualitor-8-20", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}]}
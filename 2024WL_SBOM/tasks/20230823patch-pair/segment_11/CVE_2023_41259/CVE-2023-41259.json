{"cve_id": "CVE-2023-41259", "published_date": "2023-11-03T05:15:29.490", "last_modified_date": "2024-11-21T08:20:55.950", "descriptions": [{"lang": "en", "value": "Best Practical Request Tracker (RT) before 4.4.7 and 5.x before 5.0.5 allows Information Disclosure via fake or spoofed RT email headers in an email message or a mail-gateway REST API call."}, {"lang": "es", "value": "Best Practical Request Tracker (RT) anterior a 4.4.7 y 5.x anterior a 5.0.5 permite la divulgación de información a través de encabezados de correo electrónico RT falsos o falsificados en un mensaje de correo electrónico o una llamada API REST de puerta de enlace de correo."}], "references": [{"url": "https://docs.bestpractical.com/release-notes/rt/4.4.7", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://docs.bestpractical.com/release-notes/rt/5.0.5", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://docs.bestpractical.com/release-notes/rt/index.html", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
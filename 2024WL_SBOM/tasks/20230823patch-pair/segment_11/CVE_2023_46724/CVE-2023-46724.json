{"cve_id": "CVE-2023-46724", "published_date": "2023-11-01T20:15:08.800", "last_modified_date": "2025-02-13T18:15:36.657", "descriptions": [{"lang": "en", "value": "Squid is a caching proxy for the Web. Due to an Improper Validation of Specified Index bug, Squid versions ******* through 5.9 and 6.0 prior to 6.4 compiled using `--with-openssl` are vulnerable to a Denial of Service attack against SSL Certificate validation. This problem allows a remote server to perform Denial of Service against Squid Proxy by initiating a TLS Handshake with a specially crafted SSL Certificate in a server certificate chain. This attack is limited to HTTPS and SSL-Bump. This bug is fixed in Squid version 6.4. In addition, patches addressing this problem for the stable releases can be found in Squid's patch archives. Those who you use a prepackaged version of Squid should refer to the package vendor for availability information on updated packages."}, {"lang": "es", "value": "Squid es un proxy de almacenamiento en caché para la Web. Debido a un error de validación incorrecta del índice especificado, las versiones de Squid ******* a 5.9 y 6.0 anteriores a 6.4 compiladas usando `--with-openssl` son vulnerables a un ataque de Denegación de Servicio contra la validación del certificado SSL. Este problema permite que un servidor remoto realice una denegación de servicio contra Squid Proxy iniciando un protocolo de enlace TLS con un certificado SSL especialmente manipulado en una cadena de certificados de servidor. Este ataque se limita a HTTPS y SSL-Bump. Este error se solucionó en la versión 6.4 de Squid. Además, los parches que solucionan este problema para las versiones estables se pueden encontrar en los archivos de parches de Squid. Aquellos que utilicen una versión empaquetada de Squid deben consultar al proveedor del paquete para obtener información sobre la disponibilidad de paquetes actualizados."}], "references": [{"url": "http://www.squid-cache.org/Versions/v5/SQUID-2023_4.patch", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}, {"url": "http://www.squid-cache.org/Versions/v6/SQUID-2023_4.patch", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}, {"url": "https://github.com/squid-cache/squid/commit/b70f864940225dfe69f9f653f948e787f99c3810", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/squid-cache/squid/security/advisories/GHSA-73m6-jm96-c6r3", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/A5QASTMCUSUEW3UOMKHZJB3FTONWSRXS/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/MEV66D3PAAY6K7TWDT3WZBLCPLASFJDC/", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "<EMAIL>", "tags": []}]}
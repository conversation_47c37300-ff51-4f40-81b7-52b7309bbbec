{"cve_id": "CVE-2023-20264", "published_date": "2023-11-01T18:15:09.757", "last_modified_date": "2024-11-21T07:41:01.730", "descriptions": [{"lang": "en", "value": "A vulnerability in the implementation of Security Assertion Markup Language (SAML) 2.0 single sign-on (SSO) for remote access VPN in Cisco Adaptive Security Appliance (ASA) Software and Cisco Firepower Threat Defense (FTD) Software could allow an unauthenticated, remote attacker to intercept the SAML assertion of a user who is authenticating to a remote access VPN session. This vulnerability is due to insufficient validation of the login URL. An attacker could exploit this vulnerability by persuading a user to access a site that is under the control of the attacker, allowing the attacker to modify the login URL. A successful exploit could allow the attacker to intercept a successful SAML assertion and use that assertion to establish a remote access VPN session toward the affected device with the identity and permissions of the hijacked user, resulting in access to the protected network."}, {"lang": "es", "value": "Una vulnerabilidad en la implementación de Security Assertion Markup Language (SAML) 2.0 de Single Sign-oOn (SSO) para VPN de acceso remoto en el software Cisco Adaptive Security Appliance (ASA) y el software Cisco Firepower Threat Defense (FTD) podría permitir que un atacante remoto no autenticado intercepte la aserción SAML de un usuario que se está autenticando en una sesión VPN de acceso remoto. Esta vulnerabilidad se debe a una validación insuficiente de la URL de inicio de sesión. Un atacante podría aprovechar esta vulnerabilidad persuadiendo a un usuario para que acceda a un sitio que está bajo el control del atacante, permitiéndole modificar la URL de inicio de sesión. Un exploit exitoso podría permitir al atacante interceptar una aserción SAML exitosa y usar esa aserción para establecer una sesión VPN de acceso remoto hacia el dispositivo afectado con la identidad y los permisos del usuario secuestrado, lo que resultaría en acceso a la red protegida."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-asaftd-saml-hijack-ttuQfyz", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
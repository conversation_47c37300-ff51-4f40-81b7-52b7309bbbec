{"cve_id": "CVE-2023-34383", "published_date": "2023-11-03T12:15:08.583", "last_modified_date": "2024-11-21T08:07:08.700", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in weDevs WP Project Manager wedevs-project-manager allows SQL Injection.This issue affects WP Project Manager: from n/a through 2.6.0.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL ('Inyección SQL') en weDevs WP Project Manager wedevs-project-manager permite la inyección SQL. Este problema afecta a WP Project Manager: desde n/a hasta 2.6.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wedevs-project-manager/wordpress-wp-project-manager-task-team-and-project-management-plugin-featuring-kanban-board-and-gantt-charts-plugin-2-6-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
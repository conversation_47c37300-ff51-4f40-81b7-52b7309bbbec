{"cve_id": "CVE-2022-42882", "published_date": "2023-11-07T18:15:07.943", "last_modified_date": "2024-11-21T07:25:31.427", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Shambix Simple CSV/XLS Exporter.This issue affects Simple CSV/XLS Exporter: from n/a through 1.5.8.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Shambix Simple CSV/XLS Exporter. Este problema afecta a Simple CSV/XLS Exporter: desde n/a hasta 1.5.8."}], "references": [{"url": "https://patchstack.com/database/vulnerability/simple-csv-xls-exporter/wordpress-simple-csv-xls-exporter-plugin-1-5-8-authenticated-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
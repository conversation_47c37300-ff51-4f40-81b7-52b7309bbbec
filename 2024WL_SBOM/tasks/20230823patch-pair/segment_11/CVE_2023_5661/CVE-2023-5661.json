{"cve_id": "CVE-2023-5661", "published_date": "2023-11-07T12:15:13.447", "last_modified_date": "2024-11-21T08:42:13.353", "descriptions": [{"lang": "en", "value": "The Social Feed plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'socialfeed' shortcode in all versions up to, and including, ******* due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with author-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Social Feed para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode 'socialfeed' del complemento en todas las versiones hasta la ******* incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de autor y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/add-facebook/tags/*******/public/templates/default/template.php#L417", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/8b145772-624e-4af0-9156-03c483bf8381?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-36527", "published_date": "2023-11-07T16:15:28.530", "last_modified_date": "2024-11-21T08:09:52.407", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in BestWebSoft Post to CSV by BestWebSoft.This issue affects Post to CSV by BestWebSoft: from n/a through 1.4.0.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en BestWebSoft Post to CSV by BestWebSoft. Este problema afecta a Post to CSV by BestWebSoft: desde n/a hasta 1.4.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/post-to-csv/wordpress-post-to-csv-by-bestwebsoft-plugin-1-4-0-csv-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
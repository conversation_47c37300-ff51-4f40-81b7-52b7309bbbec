{"cve_id": "CVE-2023-41723", "published_date": "2023-11-07T07:15:10.420", "last_modified_date": "2024-11-21T08:21:32.890", "descriptions": [{"lang": "en", "value": "A vulnerability in Veeam ONE allows a user with the Veeam ONE Read-Only User role to view the Dashboard Schedule. Note: The criticality of this vulnerability is reduced because the user with the Read-Only role is only able to view the schedule and cannot make changes."}, {"lang": "es", "value": "Una vulnerabilidad en Veeam ONE permite a un usuario con el rol de Veeam ONE Read-Only User ver la Programación del Panel. Nota: La gravedad de esta vulnerabilidad se reduce porque el usuario con el rol de Read-Only solo puede ver la programación y no puede realizar cambios."}], "references": [{"url": "https://www.veeam.com/kb4508", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-5969", "published_date": "2023-11-06T16:15:42.987", "last_modified_date": "2024-11-21T08:42:53.270", "descriptions": [{"lang": "en", "value": "Mattermost fails to properly sanitize the request to /api/v4/redirect_location allowing an attacker, sending a specially crafted request to /api/v4/redirect_location, to fill up the memory due to caching large items.\n\n"}, {"lang": "es", "value": "Mattermost no puede sanitizar adecuadamente la solicitud a /api/v4/redirect_location, lo que permite que un atacante envíe una solicitud especialmente manipulada a /api/v4/redirect_location para llenar la memoria debido al almacenamiento en caché de elementos grandes."}], "references": [{"url": "https://mattermost.com/security-updates", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-25990", "published_date": "2023-11-03T17:15:08.553", "last_modified_date": "2024-11-21T07:50:34.830", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Themeum Tutor LMS allows SQL Injection.This issue affects Tutor LMS: from n/a through 2.1.10.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Themeum Tutor LMS permite la inyección SQL. Este problema afecta a Tutor LMS: desde n/a hasta 2.1.10."}], "references": [{"url": "https://patchstack.com/database/vulnerability/tutor/wordpress-tutor-lms-plugin-2-1-10-multiple-tutor-instructor-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-46818", "published_date": "2023-11-03T16:15:30.930", "last_modified_date": "2024-11-21T07:31:06.450", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Gopi Ramasamy Email posts to subscribers allows SQL Injection.This issue affects Email posts to subscribers: from n/a through 6.2.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en las publicaciones Gopi Ramasamy Email a los suscriptores permite la inyección de SQL. Este problema afecta las publicaciones de correo electrónico a los suscriptores: desde n/a hasta 6.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/email-posts-to-subscribers/wordpress-email-posts-to-subscribers-plugin-6-2-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
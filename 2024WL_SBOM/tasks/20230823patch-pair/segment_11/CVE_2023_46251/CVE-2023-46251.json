{"cve_id": "CVE-2023-46251", "published_date": "2023-11-06T18:15:08.547", "last_modified_date": "2024-11-21T08:28:10.273", "descriptions": [{"lang": "en", "value": " MyBB is a free and open source forum software. Custom MyCode (BBCode) for the visual editor (_SCEditor_) doesn't escape input properly when rendering HTML, resulting in a DOM-based XSS vulnerability. This weakness can be exploited by pointing a victim to a page where the visual editor is active (e.g. as a post or Private Message) and operates on a maliciously crafted MyCode message. This may occur on pages where message content is pre-filled using a GET/POST parameter, or on reply pages where a previously saved malicious message is quoted. The impact is be mitigated when: 1. the visual editor is disabled globally (_Admin CP → Configuration → Settings → Clickable Smilies and BB Code: [Clickable MyCode Editor](https://github.com/mybb/mybb/blob/mybb_1836/install/resources/settings.xml#L2087-L2094)_ is set to _Off_), or 2. the visual editor is disabled for individual user accounts (_User CP → Your Profile → Edit Options_: _Show the MyCode formatting options on the posting pages_ checkbox is not checked). MyBB 1.8.37 resolves this issue with the commit `6dcaf0b4d`. Users are advised to upgrade. Users unable to upgrade may mitigate the impact without upgrading MyBB by changing the following setting (_Admin CP → Configuration → Settings_):\n- _Clickable Smilies and BB Code → [Clickable MyCode Editor](https://github.com/mybb/mybb/blob/mybb_1836/install/resources/settings.xml#L2087-L2094)_: _Off_. Similarly, individual MyBB forum users are able to disable the visual editor by diabling the account option (_User CP → Your Profile → Edit Options_) _Show the MyCode formatting options on the posting pages_."}, {"lang": "es", "value": "MyBB es un software de foro gratuito y de código abierto. El MyCode personalizado (BBCode) para el editor visual (_SCEditor_) no sanitiza la entrada correctamente al representar HTML, lo que genera una vulnerabilidad XSS basada en DOM. Esta debilidad se puede explotar dirigiendo a la víctima a una página donde el editor visual está activo (por ejemplo, como una publicación o mensaje privado) y opera con un mensaje MyCode creado con fines malintencionados. Esto puede ocurrir en páginas donde el contenido del mensaje se completa previamente mediante un parámetro GET/POST, o en páginas de respuesta donde se cita un mensaje malicioso previamente guardado. El impacto se mitiga cuando: \n1. el editor visual está deshabilitado globalmente (_Admin CP ? Configuration ? Settings ? Clickable Smilies and BB Code: [Clickable MyCode Editor](https://github.com/mybb/mybb/blob/mybb_1836/install/resources/settings.xml#L2087-L2094)_ is set to _Off_), o \n2. el editor visual está deshabilitado para cuentas de usuario individuales (_User CP ? Your Profile ? Edit Options_: _Show the MyCode formatting options on the posting pages_ checkbox is not checked).\nMyBB 1.8.37 resuelve este problema con el commit `6dcaf0b4d`. Se recomienda a los usuarios que actualicen. Los usuarios que no puedan actualizar pueden mitigar el impacto sin actualizar MyBB cambiando la siguiente configuración (_Admin CP ? Configuration ? Settings_): - _Clickable Smilies and BB Code ? [Clickable MyCode Editor](https://github.com/mybb/mybb/blob/mybb_1836/install/resources/settings.xml#L2087-L2094)_: _Off_. \nDe manera similar, los usuarios individuales del foro MyBB pueden desactivar el editor visual marcando la opción de cuenta (_User CP ? Your Profile ? Edit Options_)_Show the MyCode formatting options on the posting pages_."}], "references": [{"url": "https://github.com/mybb/mybb/commit/6dcaf0b4db6254f1833fe8dae295d9ddc2219276", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/mybb/mybb/security/advisories/GHSA-wj33-q7vj-9fr8", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://mybb.com/versions/1.8.37/", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
{"cve_id": "CVE-2023-46695", "published_date": "2023-11-02T06:15:08.000", "last_modified_date": "2024-11-21T08:29:05.987", "descriptions": [{"lang": "en", "value": "An issue was discovered in Django 3.2 before 3.2.23, 4.1 before 4.1.13, and 4.2 before 4.2.7. The NFKC normalization is slow on Windows. As a consequence, django.contrib.auth.forms.UsernameField is subject to a potential DoS (denial of service) attack via certain inputs with a very large number of Unicode characters."}, {"lang": "es", "value": "Se descubrió un problema en Django 3.2 anterior a 3.2.23, 4.1 anterior a 4.1.13 y 4.2 anterior a 4.2.7. La normalización de NFKC es lenta en Windows. Como consecuencia, django.contrib.auth.forms.UsernameField está sujeto a un potencial ataque DoS (denegación de servicio) a través de ciertas entradas con una gran cantidad de caracteres Unicode."}], "references": [{"url": "https://docs.djangoproject.com/en/4.2/releases/security/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://groups.google.com/forum/#%21forum/django-announce", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://security.netapp.com/advisory/ntap-20231214-0001/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.djangoproject.com/weblog/2023/nov/01/security-releases/", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}
{"cve_id": "CVE-2022-46849", "published_date": "2023-11-06T08:15:21.623", "last_modified_date": "2024-11-21T07:31:09.973", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Weblizar Coming Soon Page – Responsive Coming Soon & Maintenance Mode allows SQL Injection.This issue affects Coming Soon Page – Responsive Coming Soon & Maintenance Mode: from n/a through 1.5.9.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('Inyección SQL') en Weblizar Coming Soon Page – Responsive Coming Soon &amp; Maintenance Mode permite la inyección de SQL. Este problema afecta Coming Soon Page – Responsive Coming Soon &amp; Maintenance Mode: desde n/ a hasta 1.5.9."}], "references": [{"url": "https://patchstack.com/database/vulnerability/responsive-coming-soon-page/wordpress-coming-soon-page-plugin-1-5-8-sql-injection-sqli-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
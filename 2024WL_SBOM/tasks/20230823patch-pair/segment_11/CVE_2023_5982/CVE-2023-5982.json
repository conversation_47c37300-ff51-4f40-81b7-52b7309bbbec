{"cve_id": "CVE-2023-5982", "published_date": "2023-11-07T21:15:14.193", "last_modified_date": "2024-11-21T08:42:55.020", "descriptions": [{"lang": "en", "value": "The UpdraftPlus: WordPress Backup & Migration Plugin plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.23.10. This is due to a lack of nonce validation and insufficient validation of the instance_id on the 'updraftmethod-googledrive-auth' action used to update Google Drive remote storage location. This makes it possible for unauthenticated attackers to modify the Google Drive location that backups are sent to via a forged request granted they can trick a site administrator into performing an action such as clicking on a link. This can make it possible for attackers to receive backups for a site which may contain sensitive information."}, {"lang": "es", "value": "El complemento UpdraftPlus: WordPress Backup &amp; Migration Plugin para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en todas las versiones hasta la 1.23.10 incluida. Esto se debe a una falta de validación nonce y a una validación insuficiente del instance_id en la acción 'updraftmethod-googledrive-auth' utilizada para actualizar la ubicación de almacenamiento remoto de Google Drive. Esto hace posible que atacantes no autenticados modifiquen la ubicación de Google Drive a la que se envían las copias de seguridad mediante una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace. Esto puede hacer posible que los atacantes reciban copias de seguridad de un sitio que puede contener información confidencial."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/2989669/updraftplus/tags/1.23.11/class-updraftplus.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e1be11c5-0a44-4816-b6bf-d330cb51dbf3?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
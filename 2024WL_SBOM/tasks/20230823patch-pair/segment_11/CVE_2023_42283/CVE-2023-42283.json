{"cve_id": "CVE-2023-42283", "published_date": "2023-11-07T08:15:12.343", "last_modified_date": "2024-11-21T08:22:24.383", "descriptions": [{"lang": "en", "value": "Blind SQL injection in api_id parameter in Tyk Gateway version 5.0.3 allows attacker to access and dump the database via a crafted SQL query."}, {"lang": "es", "value": "La inyección de Blind SQL en el parámetro api_id en Tyk Gateway versión 5.0.3 permite a un atacante acceder y volcar la base de datos mediante una consulta SQL manipulada."}], "references": [{"url": "https://github.com/andreysanyuk/CVE-2023-42283", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
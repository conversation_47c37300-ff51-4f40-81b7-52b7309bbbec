{"cve_id": "CVE-2023-5577", "published_date": "2023-11-07T12:15:12.977", "last_modified_date": "2024-11-21T08:42:03.197", "descriptions": [{"lang": "en", "value": "The Bitly's plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'wpbitly' shortcode in all versions up to, and including, 2.7.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Bitly para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode 'wpbitly' del complemento en todas las versiones hasta la 2.7.1 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-bitly/trunk/includes/class-wp-bitly-shortlink.php?rev=2767772#L238", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/31522e54-f260-46d0-8d57-2d46af7d3450?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
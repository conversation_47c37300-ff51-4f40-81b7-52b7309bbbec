{"cve_id": "CVE-2023-20195", "published_date": "2023-11-01T17:15:11.357", "last_modified_date": "2024-11-21T07:40:48.303", "descriptions": [{"lang": "en", "value": "Two vulnerabilities in Cisco ISE could allow an authenticated, remote attacker to upload arbitrary files to an affected device. To exploit these vulnerabilities, an attacker must have valid Administrator credentials on the affected device. These vulnerabilities are due to improper validation of files that are uploaded to the web-based management interface. An attacker could exploit these vulnerabilities by uploading a crafted file to an affected device. A successful exploit could allow the attacker to store malicious files in specific directories on the device. The attacker could later use those files to conduct additional attacks, including executing arbitrary code on the affected device with root privileges."}, {"lang": "es", "value": "Dos vulnerabilidades en Cisco ISE podrían permitir que un atacante remoto autenticado cargue archivos arbitrarios en un dispositivo afectado. Para aprovechar estas vulnerabilidades, un atacante debe tener credenciales de administrador válidas en el dispositivo afectado. Estas vulnerabilidades se deben a una validación inadecuada de los archivos que se cargan en la interfaz de administración basada en web. Un atacante podría aprovechar estas vulnerabilidades cargando un archivo manipulado en un dispositivo afectado. Un exploit exitoso podría permitir al atacante almacenar archivos maliciosos en directorios específicos del dispositivo. Posteriormente, el atacante podría utilizar esos archivos para realizar ataques adicionales, incluida la ejecución de código arbitrario en el dispositivo afectado con privilegios de root."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ise-file-upload-FceLP4xs", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-23368", "published_date": "2023-11-03T17:15:08.210", "last_modified_date": "2024-11-21T07:46:02.677", "descriptions": [{"lang": "en", "value": "An OS command injection vulnerability has been reported to affect several QNAP operating system versions. If exploited, the vulnerability could allow users to execute commands via a network.\n\nWe have already fixed the vulnerability in the following versions:\nQTS 5.0.1.2376 build 20230421 and later\nQTS 4.5.4.2374 build 20230416 and later\nQuTS hero h5.0.1.2376 build 20230421 and later\nQuTS hero h4.5.4.2374 build 20230417 and later\nQuTScloud c5.0.1.2374 and later\n"}, {"lang": "es", "value": "Se ha informado que una vulnerabilidad de inyección de comandos del sistema operativo afecta a varias versiones del sistema operativo QNAP. Si se explota, la vulnerabilidad podría permitir a los usuarios ejecutar comandos a través de una red. Ya hemos solucionado la vulnerabilidad en las siguientes versiones: QTS 5.0.1.2376 compilación 20230421 y posteriores QTS 4.5.4.2374 compilación 20230416 y posteriores QuTS hero h5.0.1.2376 compilación 20230421 y posteriores QuTS hero h4.5.4.2374 compilación 20230417 y posteriores QuTScloud c5.0.1.2374 y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-31", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
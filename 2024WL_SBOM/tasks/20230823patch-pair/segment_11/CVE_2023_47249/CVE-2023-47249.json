{"cve_id": "CVE-2023-47249", "published_date": "2023-11-05T00:15:08.627", "last_modified_date": "2024-11-21T08:30:02.607", "descriptions": [{"lang": "en", "value": "In International Color Consortium DemoIccMAX 79ecb74, a CIccXmlArrayType:::ParseText function (for unsigned short) in IccUtilXml.cpp in libIccXML.a has an out-of-bounds read."}, {"lang": "es", "value": "En International Color Consortium DemoIccMAX 79ecb74, una función CIccXmlArrayType:::ParseText (para abreviatura sin firmar) en IccUtilXml.cpp en libIccXML.a tiene una lectura fuera de los límites."}], "references": [{"url": "https://github.com/InternationalColorConsortium/DemoIccMAX/issues/54", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46744", "published_date": "2023-11-07T18:15:09.600", "last_modified_date": "2024-11-21T08:29:12.707", "descriptions": [{"lang": "en", "value": "Squidex is an open source headless CMS and content management hub. In affected versions a stored Cross-Site Scripting (XSS) vulnerability enables privilege escalation of authenticated users. The SVG element filtering mechanism intended to stop XSS attacks through uploaded SVG images, is insufficient resulting to stored XSS attacks. Squidex allows the CMS contributors to be granted the permission of uploading an SVG asset. When the asset is uploaded, a filtering mechanism is performed to validate that the SVG does not contain malicious code. The validation logic consists of traversing the HTML nodes in the DOM. In order for the validation to succeed, 2 conditions must be met: 1. No HTML tags included in a \"blacklist\" called \"InvalidSvgElements\" are present. This list only contains the element \"script\". and 2. No attributes of HTML tags begin with \"on\" (i.e. onerror, onclick) (line 65). If either of the 2 conditions is not satisfied, validation fails and the file/asset is not uploaded. However it is possible to bypass the above filtering mechanism and execute arbitrary JavaScript code by introducing other HTML elements such as an <iframe> element with a \"src\" attribute containing a \"javascript:\" value. Authenticated adversaries with the \"assets.create\" permission, can leverage this vulnerability to upload a malicious SVG as an asset, targeting any registered user that will attempt to open/view the asset through the Squidex CMS."}, {"lang": "es", "value": "Squidex es un centro de gestión de contenidos y CMS headless de código abierto. En las versiones afectadas, una vulnerabilidad de Cross-Site Scripting (XSS) Almacenado permite la escalada de privilegios de los usuarios autenticados. El mecanismo de filtrado de elementos SVG destinado a detener los ataques XSS a través de imágenes SVG cargadas es insuficiente, lo que resulta en ataques XSS almacenados. Squidex permite a los contribuyentes de CMS obtener permiso para cargar un activo SVG. Cuando se carga el activo, se realiza un mecanismo de filtrado para validar que el SVG no contenga código malicioso. La lógica de validación consiste en recorrer los nodos HTML en el DOM. Para que la validación se realice correctamente, se deben cumplir 2 condiciones: 1. No hay etiquetas HTML incluidas en una \"lista negra\" llamada \"InvalidSvgElements\". Esta lista sólo contiene el elemento \"script\". y 2. Ningún atributo de las etiquetas HTML comienza con \"on\" (es decir, onerror, onclick) (línea 65). Si alguna de las 2 condiciones no se cumple, la validación falla y el archivo/activo no se carga. Sin embargo, es posible omitir el mecanismo de filtrado anterior y ejecutar código JavaScript arbitrario introduciendo otros elementos HTML como un elemento  con un atributo \"src\" que contenga un valor \"javascript:\". Los adversarios autenticados con el permiso \"assets.create\" pueden aprovechar esta vulnerabilidad para cargar un SVG malicioso como activo, dirigido a cualquier usuario registrado que intente abrir/ver el activo a través del CMS Squidex."}], "references": [{"url": "https://github.com/Squidex/squidex/security/advisories/GHSA-xfr4-qg2v-7v5m", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
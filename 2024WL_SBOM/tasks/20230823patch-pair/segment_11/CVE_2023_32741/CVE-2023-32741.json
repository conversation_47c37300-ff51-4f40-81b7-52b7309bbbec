{"cve_id": "CVE-2023-32741", "published_date": "2023-11-04T00:15:08.573", "last_modified_date": "2025-02-13T17:16:33.090", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in IT Path Solutions PVT LTD Contact Form to Any API allows SQL Injection.This issue affects Contact Form to Any API: from n/a through 1.1.2."}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en IT Path Solutions PVT LTD El formulario de contacto para cualquier API permite la inyección de SQL. Este problema afecta el formulario de contacto para cualquier API: desde n/a hasta 1.1.2."}], "references": [{"url": "http://packetstormsecurity.com/files/175654/WordPress-Contact-Form-To-Any-API-1.1.2-SQL-Injection.html", "source": "<EMAIL>", "tags": []}, {"url": "https://patchstack.com/database/vulnerability/contact-form-to-any-api/wordpress-contact-form-to-any-api-plugin-1-1-2-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-28748", "published_date": "2023-11-06T09:15:07.790", "last_modified_date": "2024-11-21T07:55:55.623", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in biztechc Copy or Move Comments allows SQL Injection.This issue affects Copy or Move Comments: from n/a through 5.0.4.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Biztechc Copy or Move Comments permite la inyección SQL. Este problema afecta Copy or Move Comments: desde n/a hasta 5.0.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/copy-or-move-comments/wordpress-copy-or-move-comments-plugin-5-0-4-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-47182", "published_date": "2023-11-06T10:15:08.470", "last_modified_date": "2024-11-21T08:29:54.870", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) leading to a Stored Cross-Site Scripting (XSS) vulnerability in Nazmul Hossain Nihal Login Screen Manager plugin <= 3.5.2 versions."}, {"lang": "es", "value": "Cross-Site Request Forgery (CSRF) conduce a una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en el complemento Nazmul Hossain <PERSON>gin Screen Manager en versiones &lt;= 3.5.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/login-screen-manager/wordpress-login-screen-manager-plugin-3-5-2-unauth-stored-cross-site-scripting-xss-via-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
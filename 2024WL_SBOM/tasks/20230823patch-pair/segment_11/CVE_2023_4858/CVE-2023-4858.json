{"cve_id": "CVE-2023-4858", "published_date": "2023-11-06T21:15:08.980", "last_modified_date": "2025-02-26T22:15:12.660", "descriptions": [{"lang": "en", "value": "The Simple Table Manager WordPress plugin through 1.5.6 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Simple Table Manager para WordPress hasta la versión 1.5.6 no sanitiza ni escapa a algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting (XSS) Almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración multisitio)."}], "references": [{"url": "https://github.com/nightcloudos/bug_report/blob/main/vendors/poc2.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://wpscan.com/vulnerability/ef8029e0-9282-401a-a77d-10b6656adaa6", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2023-20175", "published_date": "2023-11-01T18:15:09.330", "last_modified_date": "2024-11-21T07:40:44.887", "descriptions": [{"lang": "en", "value": "A vulnerability in a specific Cisco ISE CLI command could allow an authenticated, local attacker to perform command injection attacks on the underlying operating system and elevate privileges to root. To exploit this vulnerability, an attacker must have valid Read-only-level privileges or higher on the affected device. This vulnerability is due to insufficient validation of user-supplied input. An attacker could exploit this vulnerability by submitting a crafted CLI command. A successful exploit could allow the attacker to elevate privileges to root."}, {"lang": "es", "value": "Una vulnerabilidad en un comando específico de Cisco ISE CLI podría permitir que un atacante local autenticado realice ataques de inyección de comandos en el sistema operativo subyacente y eleve los privilegios a root. Para aprovechar esta vulnerabilidad, un atacante debe tener privilegios válidos de nivel de solo lectura o superior en el dispositivo afectado. Esta vulnerabilidad se debe a una validación insuficiente de la entrada proporcionada por el usuario. Un atacante podría aprovechar esta vulnerabilidad enviando un comando CLI manipulado. Un exploit exitoso podría permitir al atacante elevar los privilegios a root."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ise-injection-QeXegrCw", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
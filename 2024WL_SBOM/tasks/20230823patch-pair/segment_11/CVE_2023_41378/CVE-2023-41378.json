{"cve_id": "CVE-2023-41378", "published_date": "2023-11-06T16:15:42.273", "last_modified_date": "2024-11-21T08:21:11.240", "descriptions": [{"lang": "en", "value": "In certain conditions for Calico Typha (v3.26.2, v3.25.1 and below), and Calico Enterprise Typha (v3.17.1, v3.16.3, v3.15.3 and below), a client TLS handshake can block the Calico Typha server indefinitely, resulting in denial of service. The TLS Handshake() call is performed inside the main server handle for loop without any timeout allowing an unclean TLS handshake to block the main loop indefinitely while other connections will be idle waiting for that handshake to finish.\n"}, {"lang": "es", "value": "En determinadas condiciones para Calico Typha (v3.26.2, v3.25.1 y anteriores) y Calico Enterprise Typha (v3.17.1, v3.16.3, v3.15.3 y siguientes), un protocolo de enlace TLS del cliente puede bloquear el servidor Calico Typha indefinidamente, resultando en denegación de servicio. La llamada TLS Handshake() se realiza dentro del bucle for del identificador del servidor principal sin ningún tiempo de espera, lo que permite que un protocolo de enlace TLS sucio bloquee el bucle principal indefinidamente mientras que otras conexiones estarán inactivas esperando a que finalice ese protocolo de enlace."}], "references": [{"url": "https://github.com/projectcalico/calico/pull/7908", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/projectcalico/calico/pull/7993", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://www.tigera.io/security-bulletins-tta-2023-001/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
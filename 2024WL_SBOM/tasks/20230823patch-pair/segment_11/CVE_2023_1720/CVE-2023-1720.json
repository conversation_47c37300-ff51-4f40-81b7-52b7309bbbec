{"cve_id": "CVE-2023-1720", "published_date": "2023-11-01T10:15:09.440", "last_modified_date": "2024-11-21T07:39:45.950", "descriptions": [{"lang": "en", "value": "Lack of mime type response header in Bitrix24 22.0.300 allows authenticated remote attackers to execute arbitrary JavaScript code in the victim's browser, and possibly execute arbitrary PHP code on the server if the victim has administrator privilege, via uploading a crafted HTML file through /desktop_app/file.ajax.php?action=uploadfile."}, {"lang": "es", "value": "La falta de un encabezado de respuesta de tipo mime en Bitrix24 22.0.300 permite a atacantes remotos autenticados ejecutar código JavaScript arbitrario en el navegador de la víctima, y posiblemente ejecutar código PHP arbitrario en el servidor si la víctima tiene privilegios de administrador, cargando un archivo HTML manipulado a través de /desktop_app /file.ajax.php?action=uploadfile."}], "references": [{"url": "https://starlabs.sg/advisories/23/23-1720/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
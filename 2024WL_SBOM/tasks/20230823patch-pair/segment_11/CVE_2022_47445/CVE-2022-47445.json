{"cve_id": "CVE-2022-47445", "published_date": "2023-11-03T13:15:08.503", "last_modified_date": "2024-11-21T07:31:58.547", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Web-X Be POPIA Compliant be-popia-compliant allows SQL Injection.This issue affects Be POPIA Compliant: from n/a through 1.2.0.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en Web-X Be POPIA Compliant be-popia-compliant permite la inyección SQL. Este problema afecta Be POPIA Compliant: desde n/a hasta 1.2.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/be-popia-compliant/wordpress-be-popia-compliant-plugin-1-2-0-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
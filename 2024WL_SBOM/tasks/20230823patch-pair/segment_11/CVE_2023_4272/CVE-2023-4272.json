{"cve_id": "CVE-2023-4272", "published_date": "2023-11-07T16:15:29.250", "last_modified_date": "2024-11-21T08:34:45.880", "descriptions": [{"lang": "en", "value": "A local non-privileged user can make GPU processing operations that expose sensitive data from previously freed memory. \n\n"}, {"lang": "es", "value": "Un usuario local sin privilegios puede realizar operaciones de procesamiento de GPU que expongan datos confidenciales de la memoria previamente liberada."}], "references": [{"url": "https://developer.arm.com/Arm%20Security%20Center/Mali%20GPU%20Driver%20Vulnerabilities", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
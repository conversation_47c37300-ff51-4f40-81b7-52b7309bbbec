{"cve_id": "CVE-2023-39301", "published_date": "2023-11-03T17:15:08.987", "last_modified_date": "2024-11-21T08:15:06.693", "descriptions": [{"lang": "en", "value": "A server-side request forgery (SSRF) vulnerability has been reported to affect several QNAP operating system versions. If exploited, the vulnerability could allow authenticated users to read application data via a network.\n\nWe have already fixed the vulnerability in the following versions:\nQTS 5.0.1.2514 build 20230906 and later\nQTS 5.1.1.2491 build 20230815 and later\nQuTS hero h5.0.1.2515 build 20230907 and later\nQuTS hero h5.1.1.2488 build 20230812 and later\nQuTScloud c5.1.0.2498 and later\n"}, {"lang": "es", "value": "Se ha informado que una vulnerabilidad de Server-Side Request Forgery (SSRF) afecta a varias versiones del sistema operativo QNAP. Si se explota, la vulnerabilidad podría permitir a los usuarios autenticados leer datos de aplicaciones a través de una red. Ya hemos solucionado la vulnerabilidad en las siguientes versiones: QTS 5.0.1.2514 compilación 20230906 y posteriores QTS 5.1.1.2491 compilación 20230815 y posteriores QuTS hero h5.0.1.2515 compilación 20230907 y posteriores QuTS hero h5.1.1.2488 compilación 20230812 y posteriores QuTScloud c5.1.0.2498 y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-51", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
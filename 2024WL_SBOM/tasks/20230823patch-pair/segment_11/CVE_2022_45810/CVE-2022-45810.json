{"cve_id": "CVE-2022-45810", "published_date": "2023-11-07T17:15:08.537", "last_modified_date": "2025-02-19T22:15:10.500", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Icegram Icegram Express – Email Marketing, Newsletters and Automation for WordPress & WooCommerce.This issue affects Icegram Express – Email Marketing, Newsletters and Automation for WordPress & WooCommerce: from n/a through 5.5.2.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Icegram Icegram Express – Email Marketing, Newsletters and Automation for WordPress &amp; WooCommerce. Este problema afecta a Icegram Express – Email Marketing, Newsletters and Automation for WordPress &amp; WooCommerce: desde n/a hasta 5.5. 2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/email-subscribers/wordpress-icegram-express-email-subscribers-newsletters-and-marketing-automation-plugin-plugin-5-5-2-csv-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
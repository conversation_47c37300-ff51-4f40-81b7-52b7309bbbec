{"cve_id": "CVE-2023-1716", "published_date": "2023-11-01T10:15:09.183", "last_modified_date": "2024-11-21T07:39:45.437", "descriptions": [{"lang": "en", "value": "\nCross-site scripting (XSS) vulnerability in Invoice Edit Page in Bitrix24 22.0.300 allows attackers to execute arbitrary JavaScript code in the victim's browser, and possibly execute arbitrary PHP code on the server if the victim has administrator privilege.\n\n\n\n\n\n"}, {"lang": "es", "value": "Vulnerabilidad de  Cross-site scripting (XSS) en la página de edición de facturas en Bitrix24 22.0.300 permite a los atacantes ejecutar código JavaScript arbitrario en el navegador de la víctima y posiblemente ejecutar código PHP arbitrario en el servidor si la víctima tiene privilegios de administrador."}], "references": [{"url": "https://starlabs.sg/advisories/23/23-1716/", "source": "<EMAIL>", "tags": ["Broken Link", "Exploit"]}]}
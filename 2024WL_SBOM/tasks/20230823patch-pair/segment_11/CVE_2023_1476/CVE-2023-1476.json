{"cve_id": "CVE-2023-1476", "published_date": "2023-11-03T09:15:13.393", "last_modified_date": "2024-11-21T07:39:16.030", "descriptions": [{"lang": "en", "value": "A use-after-free flaw was found in the Linux kernel’s mm/mremap memory address space accounting source code. This issue occurs due to a race condition between rmap walk and mremap, allowing a local user to crash the system or potentially escalate their privileges on the system."}, {"lang": "es", "value": "Se encontró una falla de use-after-free en el código fuente de contabilidad del espacio de direcciones de memoria mm/mremap del kernel de Linux. Este problema ocurre debido a una condición de ejecución entre rmap walk y mremap, lo que permite a un usuario local bloquear el sistema o potencialmente aumentar sus privilegios en el sistema."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:1659", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-1476", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2176035", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/commit/?id=97113eb39fa7972722ff490b947d8af023e1f6a2", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}
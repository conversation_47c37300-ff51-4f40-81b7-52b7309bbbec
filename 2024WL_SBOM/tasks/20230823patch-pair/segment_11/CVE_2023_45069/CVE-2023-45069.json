{"cve_id": "CVE-2023-45069", "published_date": "2023-11-06T09:15:08.617", "last_modified_date": "2025-02-26T22:15:11.063", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Video Gallery by Total-Soft Video Gallery – Best WordPress YouTube Gallery Plugin allows SQL Injection.This issue affects Video Gallery – Best WordPress YouTube Gallery Plugin: from n/a through 2.1.3.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en Video Gallery de Total-Soft Video Gallery - Best WordPress YouTube Gallery Plugin permite la inyección de SQL. Este problema afecta a Video Gallery – Best WordPress YouTube Gallery Plugin para WordPress desde n /a hasta 2.1.3."}], "references": [{"url": "https://patchstack.com/database/vulnerability/gallery-videos/wordpress-gallery-video-plugin-2-0-2-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-34179", "published_date": "2023-11-03T17:15:08.760", "last_modified_date": "2024-11-21T08:06:43.140", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Groundhogg Inc. Groundhogg allows SQL Injection.This issue affects Groundhogg: from n/a through 2.7.11.\n\n"}, {"lang": "es", "value": "Neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Groundhogg Inc. Groundhogg permite la inyección SQL. Este problema afecta a Groundhogg: desde n/a hasta 2.7.11."}], "references": [{"url": "https://patchstack.com/database/vulnerability/groundhogg/wordpress-groundhogg-plugin-2-7-10-3-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
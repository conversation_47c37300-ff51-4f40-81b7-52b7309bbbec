{"cve_id": "CVE-2023-5601", "published_date": "2023-11-06T21:15:10.063", "last_modified_date": "2025-03-25T20:15:20.667", "descriptions": [{"lang": "en", "value": "The WooCommerce Ninja Forms Product Add-ons WordPress plugin before 1.7.1 does not validate the file to be uploaded, allowing any unauthenticated users to upload arbitrary files to the server, leading to RCE."}, {"lang": "es", "value": "El complemento WooCommerce Ninja Forms Product Add-ons para WordPress anterior a 1.7.1 no valida el archivo que se va a cargar, lo que permite que cualquier usuario no autenticado cargue archivos arbitrarios en el servidor, lo que lleva a RCE."}], "references": [{"url": "https://wpscan.com/vulnerability/0035ec5e-d405-4eb7-8fe4-29dd0c71e4bc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
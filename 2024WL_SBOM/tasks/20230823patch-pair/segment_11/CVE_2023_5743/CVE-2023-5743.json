{"cve_id": "CVE-2023-5743", "published_date": "2023-11-07T11:15:11.933", "last_modified_date": "2024-11-21T08:42:23.800", "descriptions": [{"lang": "en", "value": "The Telephone Number Linker plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'telnumlink' shortcode in all versions up to, and including, 1.2 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Telephone Number Linker para WordPress es vulnerable a las Cross-Site Scripting (XSS) Almacenado a través del shortcode 'telnumlink' del complemento en todas las versiones hasta la 1.2 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/telephone-number-linker/tags/1.2/telnumlinker.php#L34", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/browser/telephone-number-linker/tags/1.2/telnumlinker.php#L36", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/06424d9f-0064-4101-b819-688489a18eee?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
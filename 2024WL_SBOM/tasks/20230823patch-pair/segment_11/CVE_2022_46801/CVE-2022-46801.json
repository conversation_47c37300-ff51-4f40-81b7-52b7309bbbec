{"cve_id": "CVE-2022-46801", "published_date": "2023-11-07T17:15:08.693", "last_modified_date": "2024-11-21T07:31:04.240", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Paul Ryley Site Reviews.This issue affects Site Reviews: from n/a through 6.2.0.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Paul <PERSON>y Site Reviews. Este problema afecta a Site Reviews: desde n/a hasta 6.2.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/site-reviews/wordpress-site-reviews-plugin-6-2-0-unauth-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
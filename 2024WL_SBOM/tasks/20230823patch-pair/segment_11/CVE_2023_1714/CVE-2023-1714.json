{"cve_id": "CVE-2023-1714", "published_date": "2023-11-01T10:15:09.050", "last_modified_date": "2024-11-21T07:39:45.173", "descriptions": [{"lang": "en", "value": "Unsafe variable extraction in bitrix/modules/main/classes/general/user_options.php in Bitrix24 22.0.300 allows remote authenticated attackers to execute arbitrary code via (1) appending arbitrary content to existing PHP files or (2) PHAR deserialization."}, {"lang": "es", "value": "La extracción de variables inseguras en bitrix/modules/main/classes/general/user_options.php en Bitrix24 22.0.300 permite a atacantes remotos autenticados ejecutar código arbitrario mediante (1) añadiendo contenido arbitrario a archivos PHP existentes o (2) deserialización PHAR."}], "references": [{"url": "https://starlabs.sg/advisories/23/23-1714/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
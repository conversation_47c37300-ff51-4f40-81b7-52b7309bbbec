{"cve_id": "CVE-2023-40207", "published_date": "2023-11-06T09:15:08.237", "last_modified_date": "2024-11-21T08:18:59.910", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in RedNao Donations Made Easy – Smart Donations allows SQL Injection.This issue affects Donations Made Easy – Smart Donations: from n/a through 4.0.12.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL ('Inyección SQL') en RedNao Donations Made Easy – Smart Donations permite la inyección de SQL. Este problema afecta a Donations Made Easy – Smart Donations: desde n/a hasta 4.0.12."}], "references": [{"url": "https://patchstack.com/database/vulnerability/smart-donations/wordpress-donations-made-easy-smart-donations-plugin-4-0-12-sql-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
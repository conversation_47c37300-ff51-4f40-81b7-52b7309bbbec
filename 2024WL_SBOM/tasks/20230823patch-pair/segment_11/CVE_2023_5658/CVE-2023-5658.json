{"cve_id": "CVE-2023-5658", "published_date": "2023-11-07T11:15:11.737", "last_modified_date": "2024-11-21T08:42:13.003", "descriptions": [{"lang": "en", "value": "The WP MapIt plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'wp_mapit' shortcode in all versions up to, and including, 2.7.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WP MapIt para WordPress es vulnerable a las Cross-Site Scripting (XSS) Almacenado a través del shortcode 'wp_mapit' del complemento en todas las versiones hasta la 2.7.1 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-mapit/tags/2.7.1/wp_mapit/classes/class.wp_mapit_map.php#L235", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/7ef6f598-e1a7-4036-9485-1aad0416349a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
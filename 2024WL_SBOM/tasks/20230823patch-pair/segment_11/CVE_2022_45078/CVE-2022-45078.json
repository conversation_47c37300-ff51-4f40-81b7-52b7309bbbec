{"cve_id": "CVE-2022-45078", "published_date": "2023-11-07T17:15:07.897", "last_modified_date": "2024-11-21T07:28:44.240", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Solwin Infotech User Blocker.This issue affects User Blocker: from n/a through 1.5.5.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en Solwin Infotech User Blocker. Este problema afecta a User Blocker: desde n/a hasta 1.5.5."}], "references": [{"url": "https://patchstack.com/database/vulnerability/user-blocker/wordpress-user-blocker-plugin-1-5-5-auth-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
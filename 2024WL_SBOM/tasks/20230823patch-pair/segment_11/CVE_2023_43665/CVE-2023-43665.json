{"cve_id": "CVE-2023-43665", "published_date": "2023-11-03T05:15:30.047", "last_modified_date": "2024-11-21T08:24:34.663", "descriptions": [{"lang": "en", "value": "In Django 3.2 before 3.2.22, 4.1 before 4.1.12, and 4.2 before 4.2.6, the django.utils.text.Truncator chars() and words() methods (when used with html=True) are subject to a potential DoS (denial of service) attack via certain inputs with very long, potentially malformed HTML text. The chars() and words() methods are used to implement the truncatechars_html and truncatewords_html template filters, which are thus also vulnerable. NOTE: this issue exists because of an incomplete fix for CVE-2019-14232."}, {"lang": "es", "value": "En Django 3.2 anterior a 3.2.22, 4.1 anterior a 4.1.12 y 4.2 anterior a 4.2.6, los métodos django.utils.text.Truncator chars() y palabras() (cuando se usan con html=True) están sujetos a un potencial Ataque DoS (denegación de servicio) a través de ciertas entradas con texto HTML muy largo y potencialmente mal formado. Los métodos chars() y palabras() se utilizan para implementar los filtros de plantilla truncatechars_html y truncatewords_html, que por tanto también son vulnerables. NOTA: este problema existe debido a una solución incompleta para CVE-2019-14232."}], "references": [{"url": "http://www.openwall.com/lists/oss-security/2024/03/04/1", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.djangoproject.com/en/4.2/releases/security/", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://groups.google.com/forum/#%21forum/django-announce", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/HJFRPUHDYJHBH3KYHSPGULQM4JN7BMSU/", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/ZQJOMNRMVPCN5WMIZ7YSX5LQ7IR2NY4D/", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231221-0001/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.djangoproject.com/weblog/2023/oct/04/security-releases/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
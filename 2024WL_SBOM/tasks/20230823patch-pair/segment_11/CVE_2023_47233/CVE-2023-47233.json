{"cve_id": "CVE-2023-47233", "published_date": "2023-11-03T21:15:17.360", "last_modified_date": "2025-03-06T16:15:43.747", "descriptions": [{"lang": "en", "value": "The brcm80211 component in the Linux kernel through 6.5.10 has a brcmf_cfg80211_detach use-after-free in the device unplugging (disconnect the USB by hotplug) code. For physically proximate attackers with local access, this \"could be exploited in a real world scenario.\" This is related to brcmf_cfg80211_escan_timeout_worker in drivers/net/wireless/broadcom/brcm80211/brcmfmac/cfg80211.c."}, {"lang": "es", "value": "El componente brcm80211 en el kernel de Linux hasta 6.5.10 tiene un código brcmf_cfg80211_detach use after free en el código de desconexión del dispositivo (desconectar el USB mediante conexión en caliente). Para los atacantes físicamente próximos con acceso local, esto \"podría explotarse en un escenario del mundo real\". Esto está relacionado con brcmf_cfg80211_escan_timeout_worker en drivers/net/wireless/broadcom/brcm80211/brcmfmac/cfg80211.c."}], "references": [{"url": "https://bugzilla.suse.com/show_bug.cgi?id=1216702", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://git.kernel.org/cgit/linux/kernel/git/torvalds/linux.git/commit/?id=0f7352557a35ab7888bc7831411ec8a3cbe20d78", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2024/06/msg00017.html", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2024/06/msg00020.html", "source": "<EMAIL>", "tags": []}, {"url": "https://lore.kernel.org/all/20231104054709.716585-1-zyytlz.wz%40163.com/", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}, {"url": "https://marc.info/?l=linux-kernel&m=169907678011243&w=2", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}
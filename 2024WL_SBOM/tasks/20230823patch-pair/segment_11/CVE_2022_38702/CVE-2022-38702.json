{"cve_id": "CVE-2022-38702", "published_date": "2023-11-07T18:15:07.583", "last_modified_date": "2024-11-21T07:16:57.680", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in Nakashima Masahiro WP CSV Exporter.This issue affects WP CSV Exporter: from n/a through 2.0.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de archivo CSV en Na<PERSON>hima <PERSON> WP CSV Exporter. Este problema afecta a WP CSV Exporter: desde n/a hasta 2.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-csv-exporter/wordpress-wp-csv-exporter-plugin-1-3-6-authenticated-csv-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
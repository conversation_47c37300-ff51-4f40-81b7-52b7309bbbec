{"cve_id": "CVE-2023-5605", "published_date": "2023-11-06T21:15:10.137", "last_modified_date": "2024-11-21T08:42:06.630", "descriptions": [{"lang": "en", "value": "The URL Shortify WordPress plugin before ******* does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)"}, {"lang": "es", "value": "El complemento URL Shortify de WordPress hasta la versión 1.7.8 no sanitiza ni escapa a algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting (XSS) Almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración multisitio)."}], "references": [{"url": "https://wpscan.com/vulnerability/9ec03ef0-0c04-4517-b761-df87af722a64", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
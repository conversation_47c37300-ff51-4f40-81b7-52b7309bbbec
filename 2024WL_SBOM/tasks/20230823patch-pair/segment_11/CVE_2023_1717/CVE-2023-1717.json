{"cve_id": "CVE-2023-1717", "published_date": "2023-11-01T10:15:09.243", "last_modified_date": "2024-11-21T07:39:45.560", "descriptions": [{"lang": "en", "value": "\nPrototype pollution in bitrix/templates/bitrix24/components/bitrix/menu/left_vertical/script.js in Bitrix24 22.0.300 allows remote attackers to execute arbitrary JavaScript code in the victim’s browser, and possibly execute arbitrary PHP code on the server if the victim has administrator privilege, via polluting `__proto__[tag]` and `__proto__[text]`.\n\n\n\n\n\n"}, {"lang": "es", "value": "La contaminación del prototipo en bitrix/templates/bitrix24/components/bitrix/menu/left_vertical/script.js en Bitrix24 22.0.300 permite a atacantes remotos ejecutar código JavaScript arbitrario en el navegador de la víctima, y posiblemente ejecutar código PHP arbitrario en el servidor si la víctima tiene privilegios de administrador, mediante la contaminación `__proto__[tag]` y `__proto__[text]`."}], "references": [{"url": "https://starlabs.sg/advisories/23/23-1717/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
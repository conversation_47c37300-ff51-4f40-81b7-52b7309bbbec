{"cve_id": "CVE-2023-39345", "published_date": "2023-11-06T19:15:09.027", "last_modified_date": "2024-11-21T08:15:12.060", "descriptions": [{"lang": "en", "value": "strapi is an open-source headless CMS. Versions prior to 4.13.1 did not properly restrict write access to fielded marked as private in the user registration endpoint. As such malicious users may be able to errantly modify their user records. This issue has been addressed in version 4.13.1. Users are advised to upgrade. There are no known workarounds for this vulnerability."}, {"lang": "es", "value": "Strapi es un CMS sin cabeza de código abierto. Las versiones anteriores a la 4.13.1 no restringían adecuadamente el acceso de escritura a los campos marcados como privados en el endpoint de registro de usuario. Como tal, los usuarios malintencionados pueden modificar erróneamente sus registros de usuario. Este problema se solucionó en la versión 4.13.1. Se recomienda a los usuarios que actualicen. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/strapi/strapi/security/advisories/GHSA-gc7p-j5xm-xxh2", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
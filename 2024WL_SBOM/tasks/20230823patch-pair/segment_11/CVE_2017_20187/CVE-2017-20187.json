{"cve_id": "CVE-2017-20187", "published_date": "2023-11-05T21:15:09.190", "last_modified_date": "2024-11-21T03:22:50.353", "descriptions": [{"lang": "en", "value": "** UNSUPPORTED WHEN ASSIGNED ** A vulnerability was found in Magnesium-PHP up to 0.3.0. It has been classified as problematic. Affected is the function formatEmailString of the file src/Magnesium/Message/Base.php. The manipulation of the argument email/name leads to injection. Upgrading to version 0.3.1 is able to address this issue. The patch is identified as 500d340e1f6421007413cc08a8383475221c2604. It is recommended to upgrade the affected component. VDB-244482 is the identifier assigned to this vulnerability. NOTE: This vulnerability only affects products that are no longer supported by the maintainer."}, {"lang": "es", "value": "** NO SOPORTADO CUANDO ESTÁ ASIGNADO ** ** NO SOPORTADO CUANDO ESTÁ ASIGNADO ** Se encontró una vulnerabilidad en Magnesium-PHP hasta 0.3.0. Ha sido clasificada como problemática. La función formatEmailString del archivo src/Magnesium/Message/Base.php es afectada por la vulnerabilidad. La manipulación del argumento correo electrónico/nombre conduce a la inyección. La actualización a la versión 0.3.1 puede solucionar este problema. El parche se identifica como 500d340e1f6421007413cc08a8383475221c2604. Se recomienda actualizar el componente afectado. VDB-244482 es el identificador asignado a esta vulnerabilidad. NOTA: Esta vulnerabilidad solo afecta a productos que ya no son compatibles con el mantenedor."}], "references": [{"url": "https://github.com/floriangaerber/Magnesium-PHP/commit/500d340e1f6421007413cc08a8383475221c2604", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/floriangaerber/Magnesium-PHP/releases/tag/v0.3.1", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://vuldb.com/?ctiid.244482", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://vuldb.com/?id.244482", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
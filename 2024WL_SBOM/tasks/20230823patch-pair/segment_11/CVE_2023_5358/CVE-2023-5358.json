{"cve_id": "CVE-2023-5358", "published_date": "2023-11-01T18:15:09.883", "last_modified_date": "2024-11-21T08:41:36.373", "descriptions": [{"lang": "en", "value": "Improper access control in Report log filters feature in Devolutions Server 2023.2.10.0 and earlier allows attackers to retrieve logs from vaults or entries they are not allowed to access via the report request url query parameters."}, {"lang": "es", "value": "El control de acceso inadecuado en la función de filtros de registro de informes en Devolutions Server 2023.2.10.0 y versiones anteriores permite a los atacantes recuperar registros de bóvedas o entradas a las que no pueden acceder a través de los parámetros de consulta de la URL de solicitud de informe."}], "references": [{"url": "https://devolutions.net/security/advisories/DEVO-2023-0019/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-46781", "published_date": "2023-11-06T12:15:08.700", "last_modified_date": "2024-11-21T08:29:18.550", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Roland Murg Current Menu Item for Custom Post Types plugin <= 1.5 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en <PERSON> Menu Item para el complemento Custom Post Types en versiones &lt;= 1.5."}], "references": [{"url": "https://patchstack.com/database/vulnerability/current-menu-item-for-custom-post-types/wordpress-current-menu-item-for-custom-post-types-plugin-1-5-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
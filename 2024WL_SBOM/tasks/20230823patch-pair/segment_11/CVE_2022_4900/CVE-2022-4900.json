{"cve_id": "CVE-2022-4900", "published_date": "2023-11-02T16:15:08.700", "last_modified_date": "2025-03-20T17:01:07.010", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHP where setting the environment variable PHP_CLI_SERVER_WORKERS to a large value leads to a heap buffer overflow."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHP donde establecer la variable de entorno PHP_CLI_SERVER_WORKERS en un valor grande provoca un desbordamiento del búfer del heap."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2022-4900", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2179880", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://security.netapp.com/advisory/ntap-20231130-0008/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
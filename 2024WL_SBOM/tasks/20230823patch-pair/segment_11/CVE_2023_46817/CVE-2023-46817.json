{"cve_id": "CVE-2023-46817", "published_date": "2023-11-03T05:15:30.867", "last_modified_date": "2024-11-21T08:29:22.147", "descriptions": [{"lang": "en", "value": "An issue was discovered in phpFox before 4.8.14. The url request parameter passed to the /core/redirect route is not properly sanitized before being used in a call to the unserialize() PHP function. This can be exploited by remote, unauthenticated attackers to inject arbitrary PHP objects into the application scope, allowing them to perform a variety of attacks, such as executing arbitrary PHP code."}, {"lang": "es", "value": "Se descubrió un problema en phpFox antes de la versión 4.8.14. El parámetro de solicitud de URL pasado a la ruta /core/redirect no se sanitiza adecuadamente antes de usarse en una llamada a la función PHP unserialize(). Esto puede ser aprovechado por atacantes remotos no autenticados para inyectar objetos PHP arbitrarios en el ámbito de la aplicación, lo que les permite realizar una variedad de ataques, como ejecutar código PHP arbitrario."}], "references": [{"url": "http://seclists.org/fulldisclosure/2023/Oct/30", "source": "<EMAIL>", "tags": ["Exploit", "Mailing List", "Third Party Advisory"]}, {"url": "https://docs.phpfox.com/display/FOX4MAN/phpFox+4.8.14", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://karmainsecurity.com/KIS-2023-12", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://karmainsecurity.com/pocs/CVE-2023-46817.php", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.phpfox.com/blog/", "source": "<EMAIL>", "tags": ["Product"]}]}
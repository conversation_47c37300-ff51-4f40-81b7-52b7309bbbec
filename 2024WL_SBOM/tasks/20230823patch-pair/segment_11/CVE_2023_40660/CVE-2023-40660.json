{"cve_id": "CVE-2023-40660", "published_date": "2023-11-06T17:15:11.757", "last_modified_date": "2024-12-04T08:15:04.043", "descriptions": [{"lang": "en", "value": "A flaw was found in OpenSC packages that allow a potential PIN bypass. When a token/card is authenticated by one process, it can perform cryptographic operations in other processes when an empty zero-length pin is passed. This issue poses a security risk, particularly for OS logon/screen unlock and for small, permanently connected tokens to computers. Additionally, the token can internally track login status. This flaw allows an attacker to gain unauthorized access, carry out malicious actions, or compromise the system without the user's awareness."}, {"lang": "es", "value": "Se encontró una falla en los paquetes OpenSC que permiten una posible omisión del PIN. Cuando un token/tarjeta es autenticado por un proceso, puede realizar operaciones criptográficas en otros procesos cuando se pasa un pin vacío de longitud cero. Este problema plantea un riesgo de seguridad, particularmente para el inicio de sesión/desbloqueo de pantalla del sistema operativo y para tokens pequeños conectados permanentemente a las maquinas. Además, el token puede rastrear internamente el estado de inicio de sesión. Esta falla permite que un atacante obtenga acceso no autorizado, lleve a cabo acciones maliciosas o comprometa el sistema sin que el usuario se de cuenta."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:7876", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7879", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-40660", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2240912", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/OpenSC/OpenSC/issues/2792#issuecomment-1674806651", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/OpenSC/OpenSC/releases/tag/0.24.0-rc1", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/OpenSC/OpenSC/wiki/OpenSC-security-advisories", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2023/12/13/2", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2023/11/msg00024.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/3CPQOMCDWFRBMEFR5VK4N5MMXXU42ODE/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/GLYEFIBBA37TK3UNMZN5NOJ7IWCIXLQP/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
{"cve_id": "CVE-2022-46809", "published_date": "2023-11-07T17:15:09.123", "last_modified_date": "2024-11-21T07:31:05.313", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Formula Elements in a CSV File vulnerability in WPDeveloper ReviewX – Multi-criteria Rating & Reviews for WooCommerce.This issue affects ReviewX – Multi-criteria Rating & Reviews for WooCommerce: from n/a through 1.6.7.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos de fórmula en una vulnerabilidad de CSV File en WPDeveloper ReviewX – Multi-criteria Rating &amp; Reviews for WooCommerce. Este problema afecta ReviewX – Multi-criteria Rating &amp; Reviews for WooCommerce: desde n/a hasta 1.6.7."}], "references": [{"url": "https://patchstack.com/database/vulnerability/reviewx/wordpress-reviewx-plugin-1-6-6-csv-injection?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
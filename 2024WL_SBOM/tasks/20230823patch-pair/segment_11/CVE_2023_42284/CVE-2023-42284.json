{"cve_id": "CVE-2023-42284", "published_date": "2023-11-07T08:15:12.577", "last_modified_date": "2024-11-21T08:22:24.537", "descriptions": [{"lang": "en", "value": "Blind SQL injection in api_version parameter in Tyk Gateway version 5.0.3 allows attacker to access and dump the database via a crafted SQL query."}, {"lang": "es", "value": "La inyección de Blind SQL en el parámetro api_version en Tyk Gateway versión 5.0.3 permite al atacante acceder y volcar la base de datos mediante una consulta SQL manipulada."}], "references": [{"url": "https://github.com/andreysanyuk/CVE-2023-42284", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
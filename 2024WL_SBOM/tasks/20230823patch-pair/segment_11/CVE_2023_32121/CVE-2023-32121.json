{"cve_id": "CVE-2023-32121", "published_date": "2023-11-03T17:15:08.620", "last_modified_date": "2025-02-19T22:15:17.883", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Highfivery LLC Zero Spam for WordPress allows SQL Injection.This issue affects Zero Spam for WordPress: from n/a through 5.4.4.\n\n"}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando SQL (\"Inyección SQL\") en Highfivery LLC Zero Spam para WordPress permite la inyección SQL. Este problema afecta a Zero Spam for WordPress: desde n/a hasta 5.4.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/zero-spam/wordpress-zero-spam-for-wordpress-plugin-5-4-4-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
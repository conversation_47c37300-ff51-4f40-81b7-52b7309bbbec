{"cve_id": "CVE-2021-4431", "published_date": "2023-11-07T11:15:10.070", "last_modified_date": "2024-11-21T06:37:42.740", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic has been found in msyk FMDataAPI up to 22. Affected is an unknown function of the file FMDataAPI_Sample.php. The manipulation leads to cross site scripting. It is possible to launch the attack remotely. Upgrading to version 23 is able to address this issue. The patch is identified as 3bd1709a8f7b1720529bf5dfc9855ad609f436cf. It is recommended to upgrade the affected component. VDB-244494 is the identifier assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad ha sido encontrada en msyk FMDataAPI hasta 22 y clasificada como problemática. Una función desconocida del archivo FMDataAPI_Sample.php es afectada por esta vulnerabilidad. La manipulación conduce a Cross-Site Scripting (XSS). Es posible lanzar el ataque de forma remota. La actualización a la versión 23 puede solucionar este problema. El parche se identifica como 3bd1709a8f7b1720529bf5dfc9855ad609f436cf. Se recomienda actualizar el componente afectado. VDB-244494 es el identificador asignado a esta vulnerabilidad."}], "references": [{"url": "https://github.com/msyk/FMDataAPI/commit/3bd1709a8f7b1720529bf5dfc9855ad609f436cf", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/msyk/FMDataAPI/pull/54", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://github.com/msyk/FMDataAPI/releases/tag/23", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://vuldb.com/?ctiid.244494", "source": "<EMAIL>", "tags": ["Permissions Required", "Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?id.244494", "source": "<EMAIL>", "tags": ["Permissions Required", "Third Party Advisory", "VDB Entry"]}]}
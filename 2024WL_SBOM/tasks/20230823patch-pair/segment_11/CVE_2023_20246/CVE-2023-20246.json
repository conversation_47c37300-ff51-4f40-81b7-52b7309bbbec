{"cve_id": "CVE-2023-20246", "published_date": "2023-11-01T18:15:09.583", "last_modified_date": "2024-11-21T07:40:59.063", "descriptions": [{"lang": "en", "value": "Multiple Cisco products are affected by a vulnerability in Snort access control policies that could allow an unauthenticated, remote attacker to bypass the configured policies on an affected system. \r\n\r This vulnerability is due to a logic error that occurs when the access control policies are being populated. An attacker could exploit this vulnerability by establishing a connection to an affected device. A successful exploit could allow the attacker to bypass configured access control rules on the affected system."}, {"lang": "es", "value": "Varios productos de Cisco se ven afectados por una vulnerabilidad en las políticas de control de acceso de Snort que podría permitir que un atacante remoto no autenticado eluda las políticas configuradas en un sistema afectado. Esta vulnerabilidad se debe a un error lógico que ocurre cuando se completan las políticas de control de acceso. Un atacante podría aprovechar esta vulnerabilidad estableciendo una conexión con un dispositivo afectado. Un exploit exitoso podría permitir al atacante omitir las reglas de control de acceso configuradas en el sistema afectado."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ftd-snort3acp-bypass-3bdR2BEh", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-0436", "published_date": "2023-11-07T12:15:08.067", "last_modified_date": "2024-11-21T07:37:10.633", "descriptions": [{"lang": "en", "value": "The affected versions of MongoDB Atlas Kubernetes Operator may print sensitive information like GCP service account keys and API integration secrets while DEBUG mode logging is enabled. This issue affects MongoDB Atlas Kubernetes Operator versions: 1.5.0, 1.6.0, 1.6.1, 1.7.0.\n\nPlease note that this is reported on an EOL version of the product, and users are advised to upgrade to the latest supported version.\nRequired Configuration: \n\nDEBUG logging is not enabled by default, and must be configured by the end-user. To check the log-level of the Operator, review the flags passed in your deployment configuration (eg.  https://github.com/mongodb/mongodb-atlas-kubernetes/blob/main/config/manager/manager.yaml#L27 https://github.com/mongodb/mongodb-atlas-kubernetes/blob/main/config/manager/manager.yaml#L27 )\n\n"}, {"lang": "es", "value": "Las versiones afectadas de MongoDB Atlas Kubernetes Operator pueden imprimir información confidencial, como claves de cuenta de servicio de GCP y secretos de integración de API, mientras el registro en modo DEBUG está habilitado. Este problema afecta a las versiones de MongoDB Atlas Kubernetes Operador: 1.5.0, 1.6.0, 1.6.1, 1.7.0. Tenga en cuenta que esto se informa en una versión EOL del producto y se recomienda a los usuarios que actualicen a la última versión compatible. Configuración requerida: el registro DEBUG no está habilitado de forma predeterminada y debe configurarlo el usuario final. Para verificar el nivel de registro del Operador, revise los indicadores pasados en su configuración de implementación (por ejemplo,  https://github.com/mongodb/mongodb-atlas-kubernetes/blob/main/config/manager/manager.yaml#L27 https://github.com/mongodb/mongodb-atlas-kubernetes/blob/main/config/manager/manager.yaml#L27)"}], "references": [{"url": "https://github.com/mongodb/mongodb-atlas-kubernetes/releases/tag/v1.7.1", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
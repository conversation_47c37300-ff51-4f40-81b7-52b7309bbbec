{"cve_id": "CVE-2022-46859", "published_date": "2023-11-03T13:15:08.373", "last_modified_date": "2024-11-21T07:31:11.300", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Spiffy Plugins Spiffy Calendar spiffy-calendar allows SQL Injection.This issue affects Spiffy Calendar: from n/a through 4.9.1.\n\n"}, {"lang": "es", "value": "Neutralización inadecuada de elementos especiales utilizados en una vulnerabilidad de comando SQL ('inyección SQL') en Spiffy Plugins Spiffy Calendar spiffy-calendar permite la inyección SQL. Este problema afecta a Spiffy Calendar: desde n/a hasta 4.9.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/spiffy-calendar/wordpress-spiffy-calendar-plugin-4-9-1-auth-sql-injection-sqli-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-46001", "published_date": "2023-11-07T22:15:11.473", "last_modified_date": "2024-11-21T08:27:43.813", "descriptions": [{"lang": "en", "value": "Buffer Overflow vulnerability in gpac MP4Box v.2.3-DEV-rev573-g201320819-master allows a local attacker to cause a denial of service via the gpac/src/isomedia/isom_read.c:2807:51 function in gf_isom_get_user_data."}, {"lang": "es", "value": "Vulnerabilidad de desbordamiento del búfer en gpac MP4Box v.2.3-DEV-rev573-g201320819-master permite a un atacante local provocar una Denegación de Servicio (DoS) a través de la función gpac/src/isomedia/isom_read.c:2807:51 en gf_isom_get_user_data."}], "references": [{"url": "https://github.com/gpac/gpac/commit/e79b0cf7e72404750630bc01340e999f3940dbc4", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/gpac/gpac/issues/2629", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}]}
{"cve_id": "CVE-2023-5530", "published_date": "2023-11-06T21:15:10.003", "last_modified_date": "2024-11-21T08:41:56.960", "descriptions": [{"lang": "en", "value": "The Ninja Forms Contact Form WordPress plugin before 3.6.34 does not sanitize and escape its label fields, which could allow high privilege users such as admin to perform Stored XSS attacks. Only users with the unfiltered_html capability can perform this, and such users are already allowed to use J<PERSON> in posts/comments etc however the vendor acknowledged and fixed the issue"}, {"lang": "es", "value": "El complemento Ninja Forms Contact Form para WordPress anterior a 3.6.34 no sanitiza ni escapa de sus campos de etiqueta, lo que podría permitir a usuarios con altos privilegios, como el administrador, realizar ataques XSS almacenados. Solo los usuarios con la capacidad unfiltered_html pueden realizar esto, y dichos usuarios ya pueden usar JS en publicaciones/comentarios, etc. Sin embargo, el proveedor reconoció y solucionó el problema."}], "references": [{"url": "https://ninjaforms.com/blog/saturday-drive-x-edition/", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://wpscan.com/vulnerability/a642f313-cc3e-4d75-b207-1dceb6a7fbae", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
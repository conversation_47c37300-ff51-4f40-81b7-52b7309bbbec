{"cve_id": "CVE-2023-26455", "published_date": "2023-11-02T14:15:10.873", "last_modified_date": "2024-11-21T07:51:30.080", "descriptions": [{"lang": "en", "value": "RMI was not requiring authentication when calling ChronosRMIService:setEventOrganizer. Attackers with local or adjacent network access could abuse the RMI service to modify calendar items using RMI. RMI access is restricted to localhost by default. The interface has been updated to require authenticated requests. No publicly available exploits are known.\n\n"}, {"lang": "es", "value": "RMI no requería autenticación al llamar a ChronosRMIService:setEventOrganizer. Los atacantes con acceso a la red local o adyacente podrían abusar del servicio RMI para modificar elementos del calendario utilizando RMI. El acceso RMI está restringido a localhost de forma predeterminada. La interfaz se ha actualizada para requerir solicitudes autenticadas. No se conocen exploits disponibles públicamente."}], "references": [{"url": "https://documentation.open-xchange.com/appsuite/security/advisories/csaf/2023/oxas-adv-2023-0004.json", "source": "<EMAIL>", "tags": []}, {"url": "https://software.open-xchange.com/products/appsuite/doc/Release_Notes_for_Patch_Release_6243_7.10.6_2023-08-01.pdf", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
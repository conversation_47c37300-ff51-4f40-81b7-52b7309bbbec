{"cve_id": "CVE-2023-29045", "published_date": "2023-11-02T14:15:11.153", "last_modified_date": "2024-11-21T07:56:26.413", "descriptions": [{"lang": "en", "value": "Documents operations, in this case \"drawing\", could be manipulated to contain invalid data types, possibly script code. Script code could be injected to an operation that would be executed for users that are actively collaborating on the same document. Operation data exchanged between collaborating parties does now gets checked for validity to avoid code execution. No publicly available exploits are known.\n\n"}, {"lang": "es", "value": "Las operaciones de documentos, en este caso \"drawing\", podrían manipularse para contener tipos de datos no válidos, posiblemente código de script. Se podría inyectar código de script en una operación que se ejecutaría para los usuarios que colaboran activamente en el mismo documento. Ahora se verifica la validez de los datos de operación intercambiados entre partes colaboradoras para evitar la ejecución de código. No se conocen exploits disponibles públicamente."}], "references": [{"url": "https://documentation.open-xchange.com/appsuite/security/advisories/csaf/2023/oxas-adv-2023-0004.json", "source": "<EMAIL>", "tags": []}, {"url": "https://software.open-xchange.com/products/appsuite/doc/Release_Notes_for_Patch_Release_6243_7.10.6_2023-08-01.pdf", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
{"cve_id": "CVE-2022-48192", "published_date": "2023-11-06T20:15:07.650", "last_modified_date": "2024-11-21T07:32:57.163", "descriptions": [{"lang": "en", "value": "Cross-site Scripting vulnerability in Softing smartLink SW-HT before 1.30, which allows an attacker to execute a dynamic script (JavaScript, VBScript) in the context of the application."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) en Softing smartLink SW-HT anterior a la 1.30, que permite a un atacante ejecutar un script dinámico (JavaScript, VBScript) en el contexto de la aplicación."}], "references": [{"url": "https://industrial.softing.com/fileadmin/psirt/downloads/syt-2022-11.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://industrial.softing.com/fileadmin/psirt/downloads/syt-2022-11.json", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-2621", "published_date": "2023-11-01T03:15:07.790", "last_modified_date": "2024-11-21T07:58:56.940", "descriptions": [{"lang": "en", "value": "\nThe McFeeder server (distributed as part of SSW package), is susceptible to an arbitrary file write vulnerability on the MAIN computer\nsystem. This vulnerability stems from the use of an outdated version of a third-party library, which is used to extract archives uploaded to McFeeder server. An authenticated malicious client can\nexploit this vulnerability by uploading a crafted ZIP archive via the\nnetwork to McF<PERSON><PERSON>’s service endpoint.\n\n"}, {"lang": "es", "value": "El servidor M<PERSON><PERSON><PERSON>er (distribuido como parte del paquete SSW) es susceptible a una vulnerabilidad de escritura de archivos arbitraria en el sistema informático PRINCIPAL. Esta vulnerabilidad se debe al uso de una versión desactualizada de una librería de terceros, que se utiliza para extraer archivos cargados en el servidor McFeeder. Un cliente malicioso autenticado puede aprovechar esta vulnerabilidad cargando un archivo ZIP manipulado a través de la red en el endpoint del servicio de M<PERSON><PERSON><PERSON>er."}], "references": [{"url": "https://publisher.hitachienergy.com/preview?DocumentId=8DBD000177&languageCode=en&Preview=true", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
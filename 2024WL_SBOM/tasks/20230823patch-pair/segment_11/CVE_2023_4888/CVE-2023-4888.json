{"cve_id": "CVE-2023-4888", "published_date": "2023-11-07T12:15:12.457", "last_modified_date": "2024-11-21T08:36:11.627", "descriptions": [{"lang": "en", "value": "The Simple Like Page Plugin plugin for WordPress is vulnerable to Stored Cross-Site Scripting via 'sfp-page-plugin' shortcode in versions up to, and including, 1.5.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Simple Like Page Plugin para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenados a través del shortcode 'sfp-page-plugin' en versiones hasta la 1.5.1 incluida debido a una sanitización de entrada y a un escape de salida en los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/simple-facebook-plugin/trunk/views/view-page-plugin.php?rev=2083359#L37", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/simple-facebook-plugin/trunk/views/view-page-plugin.php?rev=2083359#L38", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/simple-facebook-plugin/trunk/views/view-page-plugin.php?rev=2083359#L39", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/2988694/simple-facebook-plugin#file17", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f81df26f-4390-4626-8539-367a52f8a027?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
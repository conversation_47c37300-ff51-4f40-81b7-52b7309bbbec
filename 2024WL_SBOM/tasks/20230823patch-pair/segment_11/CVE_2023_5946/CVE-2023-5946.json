{"cve_id": "CVE-2023-5946", "published_date": "2023-11-03T14:15:08.893", "last_modified_date": "2024-11-21T08:42:50.300", "descriptions": [{"lang": "en", "value": "The Digirisk plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the 'current_group_id' parameter in version ******* due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Digirisk para WordPress es vulnerable a Reflected Cross-Site Scripting a través del parámetro 'current_group_id' en la versión ******* debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes no autenticados inyecten scripts web arbitrarios en páginas que se ejecutan si logran engañar a un usuario para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/1428184/digirisk/trunk/modules/society/controller/group.controller.01.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d41355ed-77d0-48b3-bbb3-4cc3b4df4b2a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-4295", "published_date": "2023-11-07T16:15:29.340", "last_modified_date": "2025-03-24T16:15:17.463", "descriptions": [{"lang": "en", "value": "A local non-privileged user can make improper GPU memory processing operations to gain access to already freed memory."}, {"lang": "es", "value": "Un usuario local sin privilegios puede realizar operaciones inadecuadas de procesamiento de la memoria de la GPU para obtener acceso a la memoria ya liberada."}], "references": [{"url": "http://packetstormsecurity.com/files/176109/Arm-Mali-CSF-Overflow-Use-After-Free.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://developer.arm.com/Arm%20Security%20Center/Mali%20GPU%20Driver%20Vulnerabilities", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
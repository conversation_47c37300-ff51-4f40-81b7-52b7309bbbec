{"cve_id": "CVE-2023-20155", "published_date": "2023-11-01T17:15:11.217", "last_modified_date": "2024-11-26T16:09:02.407", "descriptions": [{"lang": "en", "value": "A vulnerability in a logging API in Cisco Firepower Management Center (FMC) Software could allow an unauthenticated, remote attacker to cause the device to become unresponsive or trigger an unexpected reload. This vulnerability could also allow an attacker with valid user credentials, but not Administrator privileges, to view a system log file that they would not normally have access to. This vulnerability is due to a lack of rate-limiting of requests that are sent to a specific API that is related to an FMC log. An attacker could exploit this vulnerability by sending a high rate of HTTP requests to the API. A successful exploit could allow the attacker to cause a denial of service (DoS) condition due to the FMC CPU spiking to 100 percent utilization or to the device reloading. CPU utilization would return to normal if the attack traffic was stopped before an unexpected reload was triggered."}, {"lang": "es", "value": "Una vulnerabilidad en una API de registro en el software Cisco Firepower Management Center (FMC) podría permitir que un atacante remoto no autenticado haga que el dispositivo deje de responder o desencadene una recarga inesperada. Esta vulnerabilidad también podría permitir que un atacante con credenciales de usuario válidas, pero sin privilegios de administrador, vea un archivo de registro del sistema al que normalmente no tendría acceso. Esta vulnerabilidad se debe a la falta de limitación de la velocidad de las solicitudes que se envían a una API específica relacionada con un registro FMC. Un atacante podría aprovechar esta vulnerabilidad enviando una alta tasa de solicitudes HTTP a la API. Un exploit exitoso podría permitir al atacante causar una condición de Denegación de Servicio (DoS) debido a que la CPU del FMC alcanza el 100 por ciento de utilización o a que el dispositivo se recarga. La utilización de la CPU volvería a la normalidad si el tráfico de ataque se detuviera antes de que se desencadenara una recarga inesperada."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-fmc-logview-dos-AYJdeX55", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
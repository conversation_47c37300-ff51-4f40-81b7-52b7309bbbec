{"cve_id": "CVE-2023-26453", "published_date": "2023-11-02T14:15:10.737", "last_modified_date": "2024-11-21T07:51:29.680", "descriptions": [{"lang": "en", "value": "Requests to cache an image could be abused to include SQL queries that would be executed unchecked. Exploiting this vulnerability requires at least access to adjacent networks of the imageconverter service, which is not exposed to public networks by default. Arbitrary SQL statements could be executed in the context of the services database user account. API requests are now properly checked for valid content and attempts to circumvent this check are being logged as error. No publicly available exploits are known.\n\n"}, {"lang": "es", "value": "Se podría abusar de las solicitudes para almacenar en caché una imagen para incluir consultas SQL que se ejecutarían sin comprobar. Explotar esta vulnerabilidad requiere al menos acceso a redes adyacentes del servicio de conversión de imágenes, que no está expuesto a redes públicas de forma predeterminada. Se podrían ejecutar sentencias SQL Arbitrarias en el contexto de la cuenta de usuario de la base de datos de servicios. Las solicitudes de API ahora se verifican correctamente para detectar contenido válido y los intentos de omitir esta verificación se registran como errores. No se conocen exploits disponibles públicamente."}], "references": [{"url": "https://documentation.open-xchange.com/appsuite/security/advisories/csaf/2023/oxas-adv-2023-0004.json", "source": "<EMAIL>", "tags": []}, {"url": "https://software.open-xchange.com/products/appsuite/doc/Release_Notes_for_Patch_Release_6243_7.10.6_2023-08-01.pdf", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
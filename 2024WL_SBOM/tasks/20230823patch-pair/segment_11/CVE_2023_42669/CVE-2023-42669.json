{"cve_id": "CVE-2023-42669", "published_date": "2023-11-06T07:15:09.137", "last_modified_date": "2024-11-21T08:22:55.443", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Samba's \"rpcecho\" development server, a non-Windows RPC server used to test Samba's DCE/RPC stack elements. This vulnerability stems from an RPC function that can be blocked indefinitely. The issue arises because the \"rpcecho\" service operates with only one worker in the main RPC task, allowing calls to the \"rpcecho\" server to be blocked for a specified time, causing service disruptions. This disruption is triggered by a \"sleep()\" call in the \"dcesrv_echo_TestSleep()\" function under specific conditions. Authenticated users or attackers can exploit this vulnerability to make calls to the \"rpcecho\" server, requesting it to block for a specified duration, effectively disrupting most services and leading to a complete denial of service on the AD DC. The DoS affects all other services as \"rpcecho\" runs in the main RPC task."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en el servidor de desarrollo \"rpcecho\" de Samba, un servidor RPC que no es de Windows utilizado para probar los elementos de la pila DCE/RPC de Samba. Esta vulnerabilidad se debe a una función RPC que puede bloquearse indefinidamente. El problema surge porque el servicio \"rpcecho\" opera con un solo trabajador en la tarea principal de RPC, lo que permite bloquear las llamadas al servidor \"rpcecho\" durante un tiempo específico, lo que provoca interrupciones en el servicio. Esta interrupción se desencadena mediante una llamada \"sleep()\" en la función \"dcesrv_echo_TestSleep()\" bajo condiciones específicas. Los usuarios autenticados o los atacantes pueden aprovechar esta vulnerabilidad para realizar llamadas al servidor \"rpcecho\", solicitándole que se bloquee durante un período específico, interrumpiendo efectivamente la mayoría de los servicios y provocando una denegación completa de servicio en AD DC. La DoS afecta a todos los demás servicios ya que \"rpcecho\" se ejecuta en la tarea principal de RPC."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:6209", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:6744", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:7371", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7408", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7464", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:7467", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-42669", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2241884", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://bugzilla.samba.org/show_bug.cgi?id=15474", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://www.samba.org/samba/security/CVE-2023-42669.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231124-0002/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
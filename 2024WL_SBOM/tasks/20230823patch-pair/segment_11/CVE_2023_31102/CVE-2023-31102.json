{"cve_id": "CVE-2023-31102", "published_date": "2023-11-03T04:15:20.793", "last_modified_date": "2024-11-21T08:01:25.310", "descriptions": [{"lang": "en", "value": "Ppmd7.c in 7-Zip before 23.00 allows an integer underflow and invalid read operation via a crafted 7Z archive."}, {"lang": "es", "value": "7-<PERSON><PERSON> hasta 22.01 en Linux permite un desbordamiento de números enteros y la ejecución de código a través de un archivo 7Z manipulado."}], "references": [{"url": "https://ds-security.com/post/integer-overflow-in-7-zip-cve-2023-31102/", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231110-0007/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://sourceforge.net/p/sevenzip/discussion/45797/thread/713c8a8269/", "source": "<EMAIL>", "tags": ["Issue Tracking", "Release Notes"]}, {"url": "https://www.7-zip.org/download.html", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.zerodayinitiative.com/advisories/ZDI-23-1165/", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
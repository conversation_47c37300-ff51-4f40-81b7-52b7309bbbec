{"cve_id": "CVE-2023-5703", "published_date": "2023-11-07T12:15:13.770", "last_modified_date": "2024-11-21T08:42:18.967", "descriptions": [{"lang": "en", "value": "The Gift Up Gift Cards for WordPress and WooCommerce plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'giftup' shortcode in all versions up to, and including, 2.20.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Gift Up Gift Cards para WordPress y WooCommerce para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del shortcode  'giftup' del complemento en todas las versiones hasta la 2.20.1 incluida debido a una sanitización de entrada y a un escape de salida de los atributos proporcionados por el usuario insuficientes. Esto hace posible que atacantes autenticados con permisos de nivel de colaborador y superiores inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/gift-up/tags/2.20.1/view/giftup-checkout.php#L46", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://plugins.trac.wordpress.org/browser/gift-up/tags/2.20.1/view/giftup-checkout.php#L48", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://plugins.trac.wordpress.org/changeset/2989802/gift-up#file3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4e498706-3dbe-4c48-9c0d-0d90677aba0d?source=cve", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}]}
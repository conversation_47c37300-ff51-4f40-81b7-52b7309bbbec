{"cve_id": "CVE-2023-46404", "published_date": "2023-11-03T16:15:31.050", "last_modified_date": "2024-11-21T08:28:26.833", "descriptions": [{"lang": "en", "value": "PCRS <= 3.11 (d0de1e) “Questions” page and “Code editor” page are vulnerable to remote code execution (RCE) by escaping Python sandboxing."}, {"lang": "es", "value": "PCRS en versiones &lt;= 3.11 (d0de1e) La página “Questions” y la página “Code editor” son vulnerables a la Ejecución Remota de Código (RCE) al escapar de la sandbox de Python."}], "references": [{"url": "https://bitbucket.org/utmandrew/pcrs/commits/5f18bcbb383b7d73f7a8b399cc52b23597d752ae", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/windecks/CVE-2023-46404", "source": "<EMAIL>", "tags": ["Exploit", "Mitigation", "Patch", "Third Party Advisory"]}]}
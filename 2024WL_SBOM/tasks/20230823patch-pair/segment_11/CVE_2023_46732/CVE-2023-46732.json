{"cve_id": "CVE-2023-46732", "published_date": "2023-11-06T19:15:09.397", "last_modified_date": "2024-11-21T08:29:11.057", "descriptions": [{"lang": "en", "value": "XWiki Platform is a generic wiki platform offering runtime services for applications built on top of it. XWiki is vulnerable to reflected cross-site scripting (RXSS) via the `rev` parameter that is used in the content of the content menu without escaping. If an attacker can convince a user to visit a link with a crafted parameter, this allows the attacker to execute arbitrary actions in the name of the user, including remote code (Groovy) execution in the case of a user with programming right, compromising the confidentiality, integrity and availability of the whole XWiki installation. This has been patched in XWiki 15.6 RC1, 15.5.1 and 14.10.14. The patch in commit `04e325d57` can be manually applied without upgrading (or restarting) the instance. Users are advised to upgrade or to manually apply the patch. There are no known workarounds for this vulnerability."}, {"lang": "es", "value": "XWiki Platform es una plataforma wiki genérica que ofrece servicios de ejecución para aplicaciones creadas sobre ella. XWiki es vulnerable a Reflected Cross-Site Scripting (RXSS) a través del parámetro \"rev\" que se utiliza en el contenido del menú de contenido sin escapar. Si un atacante puede convencer a un usuario para que visite un enlace con un parámetro manipulado, esto le permitirá ejecutar acciones arbitrarias en nombre del usuario, incluida la ejecución remota de código (Groovy) en el caso de un usuario con derechos de programación, comprometiendo la confidencialidad, integridad y disponibilidad de toda la instalación de XWiki. Esto ha sido parcheado en XWiki 15.6 RC1, 15.5.1 y 14.10.14. El parche en el commit `04e325d57` se puede aplicar manualmente sin actualizar (o reiniciar) la instancia. Se recomienda a los usuarios que actualicen o apliquen manualmente el parche. No se conocen workarounds para esta vulnerabilidad."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/04e325d57d4bcb6ab79bddcafbb19032474c2a55", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-j9rc-w3wv-fv62", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-21095", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}]}
{"cve_id": "CVE-2023-26456", "published_date": "2023-11-02T14:15:10.940", "last_modified_date": "2024-11-21T07:51:30.270", "descriptions": [{"lang": "en", "value": "Users were able to set an arbitrary \"product name\" for OX Guard. The chosen value was not sufficiently sanitized before processing it at the user interface, allowing for indirect cross-site scripting attacks. Accounts that were temporarily taken over could be configured to trigger persistent code execution, allowing an attacker to build a foothold. Sanitization is in place for product names now. No publicly available exploits are known.\n\n"}, {"lang": "es", "value": "Los usuarios pudieron establecer un \"nombre de producto\" arbitrario para OX Guard. El valor elegido no se sanitizo lo suficiente antes de procesarlo en la interfaz de usuario, lo que permitió ataques indirectos de Cross Site Scripting. Las cuentas que fueron tomadas temporalmente podrían configurarse para desencadenar la ejecución de código persistente, lo que permitiría a un atacante establecerse. La sanitización ya está implementada para los nombres de los productos. No se conocen exploits disponibles públicamente."}], "references": [{"url": "https://documentation.open-xchange.com/appsuite/security/advisories/csaf/2023/oxas-adv-2023-0004.json", "source": "<EMAIL>", "tags": []}, {"url": "https://software.open-xchange.com/products/appsuite/doc/Release_Notes_for_Patch_Release_6243_7.10.6_2023-08-01.pdf", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
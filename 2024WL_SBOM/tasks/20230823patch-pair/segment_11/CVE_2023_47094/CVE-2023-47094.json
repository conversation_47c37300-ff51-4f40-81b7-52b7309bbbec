{"cve_id": "CVE-2023-47094", "published_date": "2023-11-01T00:15:09.380", "last_modified_date": "2024-11-21T08:29:45.430", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting (XSS) vulnerability in the Account Plans tab of System Settings in Virtualmin 7.7 allows remote attackers to inject arbitrary web script or HTML via the Plan name field while editing Account plan details."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en Account Plans pestaña de System Settings en Virtualmin 7.7 permite a atacantes remotos inyectar script web o HTML arbitrario a través del campo nombre del Plan mientras editan los detalles del plan de Cuenta."}], "references": [{"url": "https://github.com/pavanughade43/Virtualmin-7.7/blob/main/CVE-2023-47094", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
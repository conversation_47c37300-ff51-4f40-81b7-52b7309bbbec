{"cve_id": "CVE-2025-47281", "published_date": "2025-07-23T21:15:26.397", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Kyverno is a policy engine designed for cloud native platform engineering teams. In versions 1.14.1 and below, a Denial of Service (DoS) vulnerability exists due to improper handling of JMESPath variable substitutions. Attackers with permissions to create or update Kyverno policies can craft expressions using the {{@}} variable combined with a pipe and an invalid JMESPath function (e.g., {{@ | non_existent_function }}). This leads to a nil value being substituted into the policy structure. Subsequent processing by internal functions, specifically getValueAsStringMap, which expect string values, results in a panic due to a type assertion failure (interface {} is nil, not string). This crashes Kyverno worker threads in the admission controller and causes continuous crashes of the reports controller pod. This is fixed in version 1.14.2."}], "references": [{"url": "https://github.com/kyverno/kyverno/commit/cbd7d4ca24de1c55396fc3295e9fc3215832be7c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kyverno/kyverno/security/advisories/GHSA-r5p3-955p-5ggq", "source": "<EMAIL>", "tags": []}]}
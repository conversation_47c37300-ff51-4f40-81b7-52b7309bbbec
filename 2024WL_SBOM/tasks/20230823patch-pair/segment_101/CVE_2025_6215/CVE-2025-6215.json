{"cve_id": "CVE-2025-6215", "published_date": "2025-07-23T03:15:25.290", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The Omnishop plugin for WordPress is vulnerable to Unauthenticated Registration Bypass in all versions up to, and including, 1.0.9. Its /users/register endpoint is exposed to the public (permission_callback always returns true) and invokes wp_create_user() unconditionally,  ignoring the site’s users_can_register option and any nonce or CAPTCHA checks. This makes it possible for unauthenticated attackers to create arbitrary user accounts (customer) on sites where registrations should be closed."}, {"lang": "es", "value": "El complemento Omnishop para WordPress es vulnerable a la omisión de registro no autenticado en todas las versiones hasta la 1.0.9 incluida. Su endpoint /users/register está expuesto al público (permission_callback siempre devuelve verdadero) e invoca wp_create_user() incondicionalmente, ignorando la opción users_can_register del sitio y cualquier verificación de nonce o CAPTCHA. Esto permite a atacantes no autenticados crear cuentas de usuario arbitrarias (clientes) en sitios donde los registros deberían estar cerrados."}], "references": [{"url": "https://wordpress.org/plugins/omnishop/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/12d465d2-cd89-476e-b70a-743151a23053?source=cve", "source": "<EMAIL>", "tags": []}]}
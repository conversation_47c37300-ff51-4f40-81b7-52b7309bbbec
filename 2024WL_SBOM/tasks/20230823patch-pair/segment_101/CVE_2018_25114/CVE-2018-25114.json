{"cve_id": "CVE-2018-25114", "published_date": "2025-07-23T14:15:32.447", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A remote code execution vulnerability exists within osCommerce Online Merchant version ******* due to insecure default configuration and missing authentication in the installer workflow. By default, the /install/ directory remains accessible after installation. An unauthenticated attacker can invoke install_4.php, submit crafted POST data, and inject arbitrary PHP code into the configure.php file. When the application later includes this file, the injected payload is executed, resulting in full server-side compromise."}], "references": [{"url": "https://raw.githubusercontent.com/rapid7/metasploit-framework/master/modules/exploits/multi/http/oscommerce_installer_unauth_code_exec.rb", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/44374", "source": "<EMAIL>", "tags": []}, {"url": "https://www.oscommerce.com/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/oscommerce-installer-unauth-config-file-injection-php-code-execution", "source": "<EMAIL>", "tags": []}]}
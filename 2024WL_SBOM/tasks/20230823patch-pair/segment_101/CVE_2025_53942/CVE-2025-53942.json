{"cve_id": "CVE-2025-53942", "published_date": "2025-07-23T21:15:26.777", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "authentik is an open-source Identity Provider that emphasizes flexibility and versatility, with support for a wide set of protocols. In versions 2025.4.4 and earlier, as well as versions 2025.6.0-rc1 through 2025.6.3, deactivated users who registered through OAuth/SAML or linked their accounts to OAuth/SAML providers can still retain partial access to the system despite their accounts being deactivated. They end up in a half-authenticated state where they cannot access the API but crucially they can authorize applications if they know the URL of the application. To workaround this issue, developers can add an expression policy to the user login stage on the respective authentication flow with the expression of return request.context[\"pending_user\"].is_active. This modification ensures that the return statement only activates the user login stage when the user is active. This issue is fixed in versions authentik 2025.4.4 and 2025.6.4."}], "references": [{"url": "https://github.com/goauthentik/authentik/commit/7a4c6b9b50f8b837133a7a1fd2cb9b7f18a145cd", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goauthentik/authentik/commit/c3629d12bfe3d32d3dc8f85c0ee1f087a55dde8f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goauthentik/authentik/commit/ce3f9e3763c1778bf3a16b98c95d10f4091436ab", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goauthentik/authentik/security/advisories/GHSA-9g4j-v8w5-7x42", "source": "<EMAIL>", "tags": []}]}
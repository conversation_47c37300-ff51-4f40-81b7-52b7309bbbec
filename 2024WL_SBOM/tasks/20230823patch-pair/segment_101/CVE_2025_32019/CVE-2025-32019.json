{"cve_id": "CVE-2025-32019", "published_date": "2025-07-23T21:15:26.037", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Harbor is an open source trusted cloud native registry project that stores, signs, and scans content. Versions 2.11.2 and below, as well as versions 2.12.0-rc1 and 2.13.0-rc1, contain a vulnerability where the markdown field in the info tab page can be exploited to inject XSS code. This is fixed in versions 2.11.3 and 2.12.3."}], "references": [{"url": "https://github.com/goharbor/harbor/commit/76c2c5f7cfd9edb356cbb373889a59cc3217a058", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goharbor/harbor/commit/a13a16383a41a8e20f524593cb290dc52f86f088", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goharbor/harbor/commit/f019430872118852f83f96cac9c587b89052d1e5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goharbor/harbor/security/advisories/GHSA-f9vc-vf3r-pqqq", "source": "<EMAIL>", "tags": []}]}
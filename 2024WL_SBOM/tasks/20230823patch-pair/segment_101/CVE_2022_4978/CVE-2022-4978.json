{"cve_id": "CVE-2022-4978", "published_date": "2025-07-23T14:15:32.640", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Remote Control Server, maintained by <PERSON><PERSON><PERSON><PERSON>, ******** allows unauthenticated remote code execution when authentication is disabled, which is the default configuration. The server exposes a custom UDP-based control protocol that accepts remote keyboard input events without verification. An attacker on the same network can issue a sequence of keystroke commands to launch a system shell and execute arbitrary commands, resulting in full system compromise."}], "references": [{"url": "https://raw.githubusercontent.com/rapid7/metasploit-framework/master/modules/exploits/windows/misc/remote_control_collection_rce.rb", "source": "<EMAIL>", "tags": []}, {"url": "https://remote-control-collection.com/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/steppschuh-remote-control-server-unauth-rce", "source": "<EMAIL>", "tags": []}]}
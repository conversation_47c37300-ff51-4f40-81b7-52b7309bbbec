{"cve_id": "CVE-2025-8020", "published_date": "2025-07-23T05:15:30.590", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "All versions of the package private-ip are vulnerable to Server-Side Request Forgery (SSRF) where an attacker can provide an IP or hostname that resolves to a multicast IP address (*********/4) which is not included as part of the private IP ranges in the package's source code."}, {"lang": "es", "value": "Todas las versiones del paquete private-ip son vulnerables a server-side request forgery (SSRF), donde un atacante puede proporcionar una IP o un nombre de host que se resuelve en una dirección IP de multidifusión (*********/4) que no está incluida como parte de los rangos de IP privados en el código fuente del paquete."}], "references": [{"url": "https://gist.github.com/lirantal/ed18a4493ca9fe4429957c79454a9df1", "source": "<EMAIL>", "tags": []}, {"url": "https://security.snyk.io/vuln/SNYK-JS-PRIVATEIP-9510757", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-46686", "published_date": "2025-07-23T19:15:33.133", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Redis through 8.0.3 allows memory consumption via a multi-bulk command composed of many bulks, sent by an authenticated user. This occurs because the server allocates memory for the command arguments of every bulk, even when the command is skipped because of insufficient permissions. NOTE: this is disputed by the Supplier because abuse of the commands network protocol is not a violation of the Redis Security Model."}], "references": [{"url": "https://github.com/io-no/CVE-Reports/issues/1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/redis/redis", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/redis/redis/security/advisories/GHSA-2r7g-8hpc-rpq9", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-6261", "published_date": "2025-07-23T03:15:25.443", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The Fleetwire Fleet Management plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's fleetwire_list shortcode in all versions up to, and including, 1.0.19 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Fleetwire Fleet Management para WordPress es vulnerable a cross-site scripting almacenado a través del shortcode \"fleetwire_list\" del complemento en todas las versiones hasta la 1.0.19 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wordpress.org/plugins/fleetwire-fleet-management/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/7593b8b5-36c0-4c68-b1f2-d505fafc3328?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-6214", "published_date": "2025-07-23T03:15:25.130", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The Omnishop plugin for WordPress is vulnerable to Cross-Site Request Forgery on its /users/delete REST route in all versions up to, and including, 1.0.9. The route’s permission_callback only verifies that the requester is logged in, but fails to require any nonce or other proof of intent. This makes it possible for unauthenticated attackers to delete arbitrary user accounts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Omnishop para WordPress es vulnerable a Cross-Site Request Forgery en su ruta REST /users/delete en todas las versiones hasta la 1.0.9 incluida. El permiso_callback de la ruta solo verifica que el solicitante haya iniciado sesión, pero no requiere ningún nonce ni ninguna otra prueba de intención. Esto permite a atacantes no autenticados eliminar cuentas de usuario arbitrarias mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://wordpress.org/plugins/omnishop/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e7c936b8-3132-45e1-92ed-32ecdc9cbb1e?source=cve", "source": "<EMAIL>", "tags": []}]}
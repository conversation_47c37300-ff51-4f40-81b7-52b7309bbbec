{"cve_id": "CVE-2025-41684", "published_date": "2025-07-23T09:15:25.747", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "An authenticated remote attacker can execute arbitrary commands with root privileges on affected devices due to lack of improper sanitizing of user input in the Main Web Interface (endpoint tls_iotgen_setting)."}], "references": [{"url": "https://certvde.com/de/advisories/VDE-2025-052", "source": "<EMAIL>", "tags": []}]}
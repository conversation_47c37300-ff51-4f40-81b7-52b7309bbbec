{"cve_id": "CVE-2025-8022", "published_date": "2025-07-23T05:15:30.933", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Versions of the package bun after 0.0.12 are vulnerable to Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection') in the $ shell API due to improper neutralization of user input. An attacker can exploit this by providing specially crafted input that includes command-line arguments or shell metacharacters, leading to unintended command execution.\r\r**Note:** This issue relates to the widely known and actively developed 'Bun' JavaScript runtime. The bun package on NPM at versions 0.0.12 and below belongs to a different and older project that happened to claim the 'bun' name in the past."}, {"lang": "es", "value": "Todas las versiones de package bun son vulnerables a la neutralización incorrecta de elementos especiales utilizados en un comando del sistema operativo ('Inyección de comandos del sistema operativo') en la API $shell debido a la neutralización incorrecta de la entrada del usuario. Un atacante puede aprovechar esto proporcionando una entrada especialmente manipulada que incluya argumentos de la línea de comandos o metacaracteres de shell, lo que provoca la ejecución no deseada de comandos."}], "references": [{"url": "https://gist.github.com/lirantal/9780d664037f29d5277d7b2bc569d213", "source": "<EMAIL>", "tags": []}, {"url": "https://security.snyk.io/vuln/SNYK-JS-BUN-9510752", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-8069", "published_date": "2025-07-23T16:15:29.243", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "During the AWS Client VPN client installation on Windows devices, the install process references the C:\\usr\\local\\windows-x86_64-openssl-localbuild\\ssl directory location to fetch the OpenSSL configuration file. As a result, a non-admin user could place arbitrary code in the configuration file. If an admin user starts the AWS Client VPN client installation process, that code could be executed with root-level privileges. This issue does not affect Linux or Mac devices. \n\nWe recommend users discontinue any new installations of AWS Client VPN on Windows prior to version 5.2.2."}], "references": [{"url": "https://aws.amazon.com/security/security-bulletins/AWS-2025-014/", "source": "ff89ba41-3aa1-4d27-914a-91399e9639e5", "tags": []}]}
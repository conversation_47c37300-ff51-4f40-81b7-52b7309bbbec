{"cve_id": "CVE-2025-46099", "published_date": "2025-07-23T14:15:33.490", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "In Pluck CMS 4.7.20-dev, an authenticated attacker can upload or create a crafted PHP file under the albums module directory and access it via the module routing logic in albums.site.php, resulting in arbitrary command execution through a GET parameter."}], "references": [{"url": "http://pluck.com", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/0xC4J/CVE-Lists/blob/main/CVE-2025-46099/CVE-2025-46099.md", "source": "<EMAIL>", "tags": []}]}
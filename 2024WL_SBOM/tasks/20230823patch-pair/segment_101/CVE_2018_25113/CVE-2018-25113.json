{"cve_id": "CVE-2018-25113", "published_date": "2025-07-23T14:15:32.300", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "An unauthenticated path traversal vulnerability exists in Dicoogle PACS Web Server version 2.5.0 and possibly earlier. The vulnerability allows remote attackers to read arbitrary files on the underlying system by sending a crafted request to the /exportFile endpoint using the UID parameter. Successful exploitation can reveal sensitive files accessible by the web server user."}], "references": [{"url": "https://raw.githubusercontent.com/rapid7/metasploit-framework/master/modules/auxiliary/scanner/http/dicoogle_traversal.rb", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/45007", "source": "<EMAIL>", "tags": []}, {"url": "https://www.fortiguard.com/encyclopedia/ips/46527/dicoogle-pacs-web-server-directory-traversal", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/dicoogle-pacs-web-server-path-traversal", "source": "<EMAIL>", "tags": []}]}
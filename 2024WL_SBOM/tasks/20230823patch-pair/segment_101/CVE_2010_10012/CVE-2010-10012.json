{"cve_id": "CVE-2010-10012", "published_date": "2025-07-23T14:15:31.017", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A path traversal vulnerability exists in httpdasm version 0.92, a lightweight Windows HTTP server, that allows unauthenticated attackers to read arbitrary files on the host system. By sending a specially crafted GET request containing a sequence of URL-encoded backslashes and directory traversal patterns, an attacker can escape the web root and access sensitive files outside of the intended directory."}], "references": [{"url": "https://raw.githubusercontent.com/rapid7/metasploit-framework/master/modules/auxiliary/scanner/http/httpdasm_directory_traversal.rb", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/15861", "source": "<EMAIL>", "tags": []}, {"url": "https://www.japheth.de/httpdASM.html", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/httpasm-path-traversal", "source": "<EMAIL>", "tags": []}]}
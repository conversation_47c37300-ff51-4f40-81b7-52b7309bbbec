{"cve_id": "CVE-2025-2634", "published_date": "2025-07-23T16:15:25.620", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Out of bounds read vulnerability due to improper bounds checking in NI LabVIEW in fontmgr may result in information disclosure or arbitrary code execution.  Successful exploitation requires an attacker to get a user to open a specially crafted VI.  This vulnerability affects NI LabVIEW 2025 Q1 and prior versions."}], "references": [{"url": "https://www.ni.com/en/support/security/available-critical-and-security-updates-for-ni-software/out-of-bounds-read-vulnerabilities-in-ni-labview.html", "source": "<EMAIL>", "tags": []}]}
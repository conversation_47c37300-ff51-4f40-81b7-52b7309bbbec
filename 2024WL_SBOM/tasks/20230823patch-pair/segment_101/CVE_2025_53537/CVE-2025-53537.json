{"cve_id": "CVE-2025-53537", "published_date": "2025-07-23T21:15:26.613", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "LibHTP is a security-aware parser for the HTTP protocol and its related bits and pieces. In versions 0.5.50 and below, there is a traffic-induced memory leak that can starve the process of memory, leading to loss of visibility. To workaround this issue, set `suricata.yaml app-layer.protocols.http.libhtp.default-config.lzma-enabled` to false. This issue is fixed in version 0.5.51."}], "references": [{"url": "https://github.com/OISF/libhtp/commit/9037ea35110a0d97be5cedf8d31fb4cd9a38c7a7", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/OISF/libhtp/security/advisories/GHSA-v3qq-h8mh-vph7", "source": "<EMAIL>", "tags": []}]}
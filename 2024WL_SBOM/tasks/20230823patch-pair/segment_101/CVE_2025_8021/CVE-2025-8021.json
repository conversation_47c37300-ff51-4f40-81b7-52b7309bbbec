{"cve_id": "CVE-2025-8021", "published_date": "2025-07-23T05:15:30.763", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "All versions of the package files-bucket-server are vulnerable to Directory Traversal where an attacker can traverse the file system and access files outside of the intended directory."}, {"lang": "es", "value": "Todas las versiones del package files-bucket-server son vulnerables a Directory Traversal, donde un atacante puede atravesar el sistema de archivos y acceder a archivos fuera del directorio previsto."}], "references": [{"url": "https://gist.github.com/lirantal/1f833a7d445e8cfbdcb3e75022954b35%23path-traversal-vulnerability-in-files-bucket-server", "source": "<EMAIL>", "tags": []}, {"url": "https://security.snyk.io/vuln/SNYK-JS-FILESBUCKETSERVER-9510944", "source": "<EMAIL>", "tags": []}]}
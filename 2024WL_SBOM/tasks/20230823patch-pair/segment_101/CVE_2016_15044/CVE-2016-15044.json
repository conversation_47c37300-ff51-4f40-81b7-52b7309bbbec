{"cve_id": "CVE-2016-15044", "published_date": "2025-07-23T22:15:24.053", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A remote code execution vulnerability exists in Kaltura versions prior to 11.1.0-2 due to unsafe deserialization of user-controlled data within the keditorservices module. An unauthenticated remote attacker can exploit this issue by sending a specially crafted serialized PHP object in the kdata GET parameter to the redirectWidgetCmd endpoint. Successful exploitation leads to execution of arbitrary PHP code in the context of the web server process."}], "references": [{"url": "https://raw.githubusercontent.com/rapid7/metasploit-framework/master/modules/exploits/linux/http/kaltura_unserialize_rce.rb", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/39563", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/40404", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/kaltura-php-object-injection-rce", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-54365", "published_date": "2025-07-23T23:15:24.050", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "fastapi-guard is a security library for FastAPI that provides middleware to control IPs, log requests, detect penetration attempts and more. In version 3.0.1, the regular expression patched to mitigate the ReDoS vulnerability by limiting the length of string fails to catch inputs that exceed this limit. This type of patch fails to detect cases in which the string representing the attributes of a <script> tag exceeds 100 characters. As a result, most of the regex patterns present in version 3.0.1 can be bypassed. This is fixed in version 3.0.2."}], "references": [{"url": "https://github.com/rennf93/fastapi-guard/commit/0829292c322d33dc14ab00c5451c5c138148035a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/rennf93/fastapi-guard/commit/d9d50e8130b7b434cdc1b001b8cfd03a06729f7f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/rennf93/fastapi-guard/security/advisories/GHSA-rrf6-pxg8-684g", "source": "<EMAIL>", "tags": []}]}
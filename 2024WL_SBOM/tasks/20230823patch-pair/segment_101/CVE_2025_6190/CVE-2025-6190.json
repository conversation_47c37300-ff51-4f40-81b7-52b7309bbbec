{"cve_id": "CVE-2025-6190", "published_date": "2025-07-23T03:15:24.963", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The Realty Portal – Agent plugin for WordPress is vulnerable to Privilege Escalation due to missing authorization within the rp_user_profile() AJAX handler in versions 0.1.0 through 0.3.9. The handler reads the client-supplied meta key and value pairs from $_POST and passes them directly to update_user_meta() without restricting to a safe whitelist. This makes it possible for authenticated attackers, with Subscriber-level access and above, to overwrite the wp_capabilities meta and grant themselves the administrator role."}, {"lang": "es", "value": "El complemento Realty Portal – Agent para WordPress es vulnerable a la escalada de privilegios debido a la falta de autorización en el controlador AJAX rp_user_profile() en las versiones 0.1.0 a 0.3.9. Este controlador lee los pares de metaclave y valor proporcionados por el cliente desde $_POST y los pasa directamente a update_user_meta() sin restringirlos a una lista blanca segura. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, sobrescribir el metadato wp_capabilities y otorgarse el rol de administrador."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/realty-portal-agent/trunk/includes/class-agent-process.php#L494", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/realty-portal-agent/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b3adfe9e-ebdf-4a50-b60f-03a606a84ec0?source=cve", "source": "<EMAIL>", "tags": []}]}
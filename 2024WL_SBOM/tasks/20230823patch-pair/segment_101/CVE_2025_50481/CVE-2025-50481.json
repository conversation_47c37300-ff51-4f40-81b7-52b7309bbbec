{"cve_id": "CVE-2025-50481", "published_date": "2025-07-23T16:15:26.723", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A cross-site scripting (XSS) vulnerability in the component /blog/blogpost/add of Mezzanine CMS v6.1.0 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into a blog post."}], "references": [{"url": "https://github.com/kevinpdicks/Mezzanine-CMS-6.1.0-XSS", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/stephenmcd/mezzanine", "source": "<EMAIL>", "tags": []}]}
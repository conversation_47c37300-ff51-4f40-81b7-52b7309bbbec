{"cve_id": "CVE-2025-54139", "published_date": "2025-07-23T00:15:25.737", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "HAX CMS allows users to manage their microsite universe with a NodeJS or PHP backend. In haxcms-nodejs versions 11.0.12 and below and in haxcms-php versions 11.0.7 and below, all pages within the HAX CMS application do not contain headers to prevent other websites from loading the site within an iframe. This applies to both the CMS and generated sites. An unauthenticated attacker can load the standalone login page or other sensitive functionality within an iframe, performing a UI redressing attack (clickjacking). This can be used to perform social engineering attacks to attempt to coerce users into performing unintended actions within the HAX CMS application. This is fixed in haxcms-nodejs version 11.0.13 and haxcms-php 11.0.8."}, {"lang": "es", "value": "HAX CMS permite a los usuarios gestionar su universo de micrositios con un backend NodeJS o PHP. En las versiones 11.0.12 y anteriores de haxcms-nodejs y 11.0.7 y anteriores de haxcms-php, ninguna página de la aplicación HAX CMS contiene encabezados para impedir que otros sitios web la carguen dentro de un iframe. Esto aplica tanto al CMS como a los sitios generados. Un atacante no autenticado puede cargar la página de inicio de sesión independiente u otra funcionalidad sensible dentro de un iframe, realizando un ataque de corrección de la interfaz de usuario (clickjacking). Esto puede utilizarse para realizar ataques de ingeniería social que intenten obligar a los usuarios a realizar acciones no deseadas dentro de la aplicación HAX CMS. Esto se ha corregido en las versiones 11.0.13 y 11.0.8 de haxcms-nodejs y 11.0.8 de haxcms-php."}], "references": [{"url": "https://github.com/haxtheweb/haxcms-nodejs/commit/777f9a7ff9675a160496f350d766df1f1f9b9b99", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/haxtheweb/haxcms-php/commit/708dc8518928fe307044e67bff8b0f397cfdd606", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/haxtheweb/issues/security/advisories/GHSA-54vw-f4xf-f92j", "source": "<EMAIL>", "tags": []}]}
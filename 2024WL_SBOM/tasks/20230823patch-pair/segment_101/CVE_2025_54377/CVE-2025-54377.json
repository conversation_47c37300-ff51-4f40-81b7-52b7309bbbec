{"cve_id": "CVE-2025-54377", "published_date": "2025-07-23T21:15:27.060", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "Roo Code is an AI-powered autonomous coding agent that lives in users' editors. In versions 3.23.18 and below, RooCode does not validate line breaks (\\n) in its command input, allowing potential bypass of the allow-list mechanism. The project appears to lack parsing or validation logic to prevent multi-line command injection. When commands are evaluated for execution, only the first line or token may be considered, enabling attackers to smuggle additional commands in subsequent lines. This is fixed in version 3.23.19."}], "references": [{"url": "https://github.com/RooCodeInc/Roo-Code/commit/9d434c2db9b20eb5c78b698cb2b0037cd2074534", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RooCodeInc/Roo-Code/security/advisories/GHSA-p278-52x9-cffx", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-6054", "published_date": "2025-07-23T03:15:24.800", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The YANewsflash plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.0.3. This is due to missing or incorrect nonce validation on the 'yanewsflash/yanewsflash.php' page. This makes it possible for unauthenticated attackers to update settings and inject malicious web scripts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento YANewsflash para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.0.3 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la página 'yanewsflash/yanewsflash.php'. Esto permite que atacantes no autenticados actualicen la configuración e inyecten scripts web maliciosos mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://wordpress.org/plugins/yanewsflash/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/171fe5db-0b43-47ba-b215-87ce9d7b5095?source=cve", "source": "<EMAIL>", "tags": []}]}
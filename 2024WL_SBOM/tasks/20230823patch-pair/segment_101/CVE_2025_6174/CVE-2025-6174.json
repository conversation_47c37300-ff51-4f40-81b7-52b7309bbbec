{"cve_id": "CVE-2025-6174", "published_date": "2025-07-23T06:15:28.223", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The Qwizcards | online quizzes and flashcards WordPress plugin through 3.9.4 does not sanitise and escape the \"_stylesheet\" parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin or any other user."}, {"lang": "es", "value": "El complemento Qwizcards | online quizzes and flashcards para Wordpress hasta la versión 3.9.4 no depura ni escapa el parámetro \"_stylesheet\" antes de mostrarlo nuevamente en la página, lo que genera Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios altos, como el administrador o cualquier otro usuario."}], "references": [{"url": "https://wpscan.com/vulnerability/ff827f67-712e-4ab6-b6aa-7f5e6ff1283a/", "source": "<EMAIL>", "tags": []}]}
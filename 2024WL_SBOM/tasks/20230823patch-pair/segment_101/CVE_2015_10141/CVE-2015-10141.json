{"cve_id": "CVE-2015-10141", "published_date": "2025-07-23T14:15:31.763", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "An unauthenticated OS command injection vulnerability exists within Xdebug versions 2.5.5 and earlier, a PHP debugging extension developed by <PERSON><PERSON>. When remote debugging is enabled, Xdebug listens on port 9000 and accepts debugger protocol commands without authentication. An attacker can send a crafted eval command over this interface to execute arbitrary PHP code, which may invoke system-level functions such as system() or passthru(). This results in full compromise of the host under the privileges of the web server user."}], "references": [{"url": "http://web.archive.org/web/20231226215418/https://paper.seebug.org/397/", "source": "<EMAIL>", "tags": []}, {"url": "https://kirtixs.com/blog/2015/11/13/xpwn-exploiting-xdebug-enabled-servers/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/44568", "source": "<EMAIL>", "tags": []}, {"url": "https://www.fortiguard.com/encyclopedia/ips/46000", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/xdebug-remote-debugger-unauth-os-command-execution", "source": "<EMAIL>", "tags": []}, {"url": "https://xdebug.org/", "source": "<EMAIL>", "tags": []}]}
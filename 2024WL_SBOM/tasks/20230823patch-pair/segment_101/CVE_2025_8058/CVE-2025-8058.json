{"cve_id": "CVE-2025-8058", "published_date": "2025-07-23T20:15:27.747", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The regcomp function in the GNU C library version from 2.4 to 2.41 is \nsubject to a double free if some previous allocation fails. It can be \naccomplished either by a malloc failure or by using an interposed malloc\n that injects random malloc failures. The double free can allow buffer \nmanipulation depending of how the regex is constructed. This issue \naffects all architectures and ABIs supported by the GNU C library."}], "references": [{"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=33185", "source": "3ff69d7a-14f2-4f67-a097-88dee7810d18", "tags": []}, {"url": "https://sourceware.org/git/?p=glibc.git;a=commit;h=3ff17af18c38727b88d9115e536c069e6b5d601f", "source": "3ff69d7a-14f2-4f67-a097-88dee7810d18", "tags": []}]}
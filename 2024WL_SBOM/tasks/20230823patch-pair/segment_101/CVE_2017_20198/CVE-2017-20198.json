{"cve_id": "CVE-2017-20198", "published_date": "2025-07-23T14:15:32.140", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The Marathon UI in DC/OS < 1.9.0 allows unauthenticated users to deploy arbitrary Docker containers. Due to improper restriction of volume mount configurations, attackers can deploy a container that mounts the host's root filesystem (/) with read/write privileges. When using a malicious Docker image, the attacker can write to /etc/cron.d/ on the host, achieving arbitrary code execution with root privileges. This impacts any system where the Docker daemon honors Marathon container configurations without policy enforcement."}], "references": [{"url": "https://dcos.io/", "source": "<EMAIL>", "tags": []}, {"url": "https://raw.githubusercontent.com/rapid7/metasploit-framework/master/modules/exploits/linux/http/dcos_marathon.rb", "source": "<EMAIL>", "tags": []}, {"url": "https://web.archive.org/web/20230609134421/https://warroom.rsmus.com/dcos-marathon-compromise/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.exploit-db.com/exploits/42134", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vulncheck.com/advisories/dcos-marathon-docker-mount-abuse-rce", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-6018", "published_date": "2025-07-23T15:15:34.810", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A Local Privilege Escalation (LPE) vulnerability has been discovered in pam-config within Linux Pluggable Authentication Modules (PAM). This flaw allows an unprivileged local attacker (for example, a user logged in via SSH) to obtain the elevated privileges normally reserved for a physically present, \"allow_active\" user. The highest risk is that the attacker can then perform all allow_active yes Polkit actions, which are typically restricted to console users, potentially gaining unauthorized control over system configurations, services, or other sensitive operations."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2025-6018", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2372693", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.suse.com/show_bug.cgi?id=1243226", "source": "<EMAIL>", "tags": []}, {"url": "https://cdn2.qualys.com/2025/06/17/suse15-pam-udisks-lpe.txt", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2023-53030", "published_date": "2025-03-27T17:15:52.750", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocteontx2-pf: Avoid use of GFP_KERNEL in atomic context\n\nUsing GFP_KERNEL in preemption disable context, causing below warning\nwhen CONFIG_DEBUG_ATOMIC_SLEEP is enabled.\n\n[   32.542271] BUG: sleeping function called from invalid context at include/linux/sched/mm.h:274\n[   32.550883] in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 1, name: swapper/0\n[   32.558707] preempt_count: 1, expected: 0\n[   32.562710] RCU nest depth: 0, expected: 0\n[   32.566800] CPU: 3 PID: 1 Comm: swapper/0 Tainted: G        W          6.2.0-rc2-00269-gae9dcb91c606 #7\n[   32.576188] Hardware name: Marvell CN106XX board (DT)\n[   32.581232] Call trace:\n[   32.583670]  dump_backtrace.part.0+0xe0/0xf0\n[   32.587937]  show_stack+0x18/0x30\n[   32.591245]  dump_stack_lvl+0x68/0x84\n[   32.594900]  dump_stack+0x18/0x34\n[   32.598206]  __might_resched+0x12c/0x160\n[   32.602122]  __might_sleep+0x48/0xa0\n[   32.605689]  __kmem_cache_alloc_node+0x2b8/0x2e0\n[   32.610301]  __kmalloc+0x58/0x190\n[   32.613610]  otx2_sq_aura_pool_init+0x1a8/0x314\n[   32.618134]  otx2_open+0x1d4/0x9d0\n\nTo avoid use of GFP_ATOMIC for memory allocation, disable preemption\nafter all memory allocation is done."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: octeontx2-pf: Evitar el uso de GFP_KERNEL en un contexto atómico. El uso de GFP_KERNEL en el contexto de deshabilitación de preempción provoca la siguiente advertencia cuando CONFIG_DEBUG_ATOMIC_SLEEP está habilitado. [ 32.542271] ERROR: función de suspensión llamada desde contexto no válido en include/linux/sched/mm.h:274 [ 32.550883] in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 1, name: swapper/0 [ 32.558707] preempt_count: 1, esperado: 0 [ 32.562710] Profundidad de anidamiento de RCU: 0, esperado: 0 [ 32.566800] CPU: 3 PID: 1 Comm: swapper/0 Tainted: GW 6.2.0-rc2-00269-gae9dcb91c606 #7 [ 32.576188] Nombre del hardware: Placa Marvell CN106XX (DT) [ 32.581232] Rastreo de llamadas: [ 32.583670] dump_backtrace.part.0+0xe0/0xf0 [ 32.587937] show_stack+0x18/0x30 [ 32.591245] dump_stack_lvl+0x68/0x84 [ 32.594900] dump_stack+0x18/0x34 [ 32.598206] __might_resched+0x12c/0x160 [ 32.602122] __might_sleep+0x48/0xa0 [ 32.605689] __kmem_cache_alloc_node+0x2b8/0x2e0 [ 32.610301] __kmalloc+0x58/0x190 [ 32.613610] otx2_sq_aura_pool_init+0x1a8/0x314 [ 32.618134] otx2_open+0x1d4/0x9d0 Para evitar el uso de GFP_ATOMIC para la asignación de memoria, deshabilite la preempción una vez que se haya realizado toda la asignación de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/1eb57b87f106c90cee6b2a56a10f2e29c7a25f3e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2827c4eb429db64befdca11362e2b1c5f524f6ba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/87b93b678e95c7d93fe6a55b0e0fbda26d8c7760", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
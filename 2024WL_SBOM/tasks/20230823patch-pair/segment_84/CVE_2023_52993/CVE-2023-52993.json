{"cve_id": "CVE-2023-52993", "published_date": "2025-03-27T17:15:46.820", "last_modified_date": "2025-04-15T14:32:23.007", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nx86/i8259: Mark legacy PIC interrupts with IRQ_LEVE<PERSON>\n\nBa<PERSON> reported that after triggering a crash the subsequent crash-kernel\nfails to boot about half of the time. It triggers a NULL pointer\ndereference in the periodic tick code.\n\nThis happens because the legacy timer interrupt (IRQ0) is resent in\nsoftware which happens in soft interrupt (tasklet) context. In this context\nget_irq_regs() returns NULL which leads to the NULL pointer dereference.\n\nThe reason for the resend is a spurious APIC interrupt on the IRQ0 vector\nwhich is captured and leads to a resend when the legacy timer interrupt is\nenabled. This is wrong because the legacy PIC interrupts are level\ntriggered and therefore should never be resent in software, but nothing\never sets the IRQ_LEVEL flag on those interrupts, so the core code does not\nknow about their trigger type.\n\nEnsure that IRQ_LEVEL is set when the legacy PCI interrupts are set up."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: x86/i8259: Marcar interrupciones PIC heredadas con IRQ_LEVEL. Baoquan informó que, tras desencadenar un fallo, el kernel posterior al fallo no arranca aproximadamente la mitad de las veces. Esto desencadena una desreferencia de puntero NULL en el código de tick periódico. Esto sucede porque la interrupción del temporizador heredada (IRQ0) se reenvía por software, lo que ocurre en el contexto de una interrupción suave (tasklet). En este contexto, get_irq_regs() devuelve NULL, lo que provoca la desreferencia de puntero NULL. El motivo del reenvío es una interrupción APIC espuria en el vector IRQ0, que se captura y provoca un reenvío cuando se habilita la interrupción del temporizador heredada. Esto es incorrecto porque las interrupciones PIC heredadas se activan por nivel y, por lo tanto, nunca deberían reenviarse por software; sin embargo, nada activa el indicador IRQ_LEVEL en esas interrupciones, por lo que el código principal desconoce su tipo de activación. Asegúrese de que IRQ_LEVEL esté activado al configurar las interrupciones PCI heredadas."}], "references": [{"url": "https://git.kernel.org/stable/c/0b08201158f177aab469e356b4d6af24fdd118df", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/137f1b47da5f58805da42c1b7811e28c1e353f39", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/496975d1a2937f4baadf3d985991b13fc4fc4f27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5fa55950729d0762a787451dc52862c3f850f859", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/744fe9be9665227335539b7a77ece8d9ff62b6c0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8770cd9d7c14aa99c255a0d08186f0be953e1638", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e284c273dbb4c1ed68d4204bff94d0b10e4a90f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
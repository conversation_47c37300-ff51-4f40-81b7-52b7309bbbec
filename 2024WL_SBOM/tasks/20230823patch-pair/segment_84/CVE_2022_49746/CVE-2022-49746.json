{"cve_id": "CVE-2022-49746", "published_date": "2025-03-27T17:15:39.343", "last_modified_date": "2025-04-14T20:27:15.143", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndmaengine: imx-sdma: Fix a possible memory leak in sdma_transfer_init\n\nIf the function sdma_load_context() fails, the sdma_desc will be\nfreed, but the allocated desc->bd is forgot to be freed.\n\nWe already met the sdma_load_context() failure case and the log as\nbelow:\n[ 450.699064] imx-sdma 30bd0000.dma-controller: Timeout waiting for CH0 ready\n...\n\nIn this case, the desc->bd will not be freed without this change."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dmaengine: imx-sdma: Se corrige una posible fuga de memoria en sdma_transfer_init. Si la función sdma_load_context() falla, se libera sdma_desc, pero se olvida liberar el desc-&gt;bd asignado. Ya se ha detectado el fallo de sdma_load_context() y el registro es el siguiente: [450.699064] imx-sdma 30bd0000.dma-controller: Tiempo de espera agotado para que CH0 esté listo... En este caso, el desc-&gt;bd no se liberará sin este cambio."}], "references": [{"url": "https://git.kernel.org/stable/c/1417f59ac0b02130ee56c0c50794b9b257be3d17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/43acd767bd90c5d4172ce7fee5d9007a9a08dea9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/80ee99e52936b2c04cc37b17a14b2ae2f9d282ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bd0050b7ffa87c7b260d563646af612f4112a778", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ce4745a6b8016fae74c95dcd457d4ceef7d98af1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/dbe634ce824329d8f14079c3e9f8f11670894bec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
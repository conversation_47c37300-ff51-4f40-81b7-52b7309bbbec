{"cve_id": "CVE-2022-49759", "published_date": "2025-03-27T17:15:41.163", "last_modified_date": "2025-04-15T14:51:51.090", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nVMCI: Use threaded irqs instead of tasklets\n\nThe vmci_dispatch_dgs() tasklet function calls vmci_read_data()\nwhich uses wait_event() resulting in invalid sleep in an atomic\ncontext (and therefore potentially in a deadlock).\n\nUse threaded irqs to fix this issue and completely remove usage\nof tasklets.\n\n[   20.264639] BUG: sleeping function called from invalid context at drivers/misc/vmw_vmci/vmci_guest.c:145\n[   20.264643] in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 762, name: vmtoolsd\n[   20.264645] preempt_count: 101, expected: 0\n[   20.264646] RCU nest depth: 0, expected: 0\n[   20.264647] 1 lock held by vmtoolsd/762:\n[   20.264648]  #0: ffff0000874ae440 (sk_lock-AF_VSOCK){+.+.}-{0:0}, at: vsock_connect+0x60/0x330 [vsock]\n[   20.264658] Preemption disabled at:\n[   20.264659] [<ffff80000151d7d8>] vmci_send_datagram+0x44/0xa0 [vmw_vmci]\n[   20.264665] CPU: 0 PID: 762 Comm: vmtoolsd Not tainted 5.19.0-0.rc8.20220727git39c3c396f813.60.fc37.aarch64 #1\n[   20.264667] Hardware name: VMware, Inc. VBSA/VBSA, BIOS VEFI 12/31/2020\n[   20.264668] Call trace:\n[   20.264669]  dump_backtrace+0xc4/0x130\n[   20.264672]  show_stack+0x24/0x80\n[   20.264673]  dump_stack_lvl+0x88/0xb4\n[   20.264676]  dump_stack+0x18/0x34\n[   20.264677]  __might_resched+0x1a0/0x280\n[   20.264679]  __might_sleep+0x58/0x90\n[   20.264681]  vmci_read_data+0x74/0x120 [vmw_vmci]\n[   20.264683]  vmci_dispatch_dgs+0x64/0x204 [vmw_vmci]\n[   20.264686]  tasklet_action_common.constprop.0+0x13c/0x150\n[   20.264688]  tasklet_action+0x40/0x50\n[   20.264689]  __do_softirq+0x23c/0x6b4\n[   20.264690]  __irq_exit_rcu+0x104/0x214\n[   20.264691]  irq_exit_rcu+0x1c/0x50\n[   20.264693]  el1_interrupt+0x38/0x6c\n[   20.264695]  el1h_64_irq_handler+0x18/0x24\n[   20.264696]  el1h_64_irq+0x68/0x6c\n[   20.264697]  preempt_count_sub+0xa4/0xe0\n[   20.264698]  _raw_spin_unlock_irqrestore+0x64/0xb0\n[   20.264701]  vmci_send_datagram+0x7c/0xa0 [vmw_vmci]\n[   20.264703]  vmci_datagram_dispatch+0x84/0x100 [vmw_vmci]\n[   20.264706]  vmci_datagram_send+0x2c/0x40 [vmw_vmci]\n[   20.264709]  vmci_transport_send_control_pkt+0xb8/0x120 [vmw_vsock_vmci_transport]\n[   20.264711]  vmci_transport_connect+0x40/0x7c [vmw_vsock_vmci_transport]\n[   20.264713]  vsock_connect+0x278/0x330 [vsock]\n[   20.264715]  __sys_connect_file+0x8c/0xc0\n[   20.264718]  __sys_connect+0x84/0xb4\n[   20.264720]  __arm64_sys_connect+0x2c/0x3c\n[   20.264721]  invoke_syscall+0x78/0x100\n[   20.264723]  el0_svc_common.constprop.0+0x68/0x124\n[   20.264724]  do_el0_svc+0x38/0x4c\n[   20.264725]  el0_svc+0x60/0x180\n[   20.264726]  el0t_64_sync_handler+0x11c/0x150\n[   20.264728]  el0t_64_sync+0x190/0x194"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: VMCI: Uso de IRQs con subprocesos en lugar de tasklets. La función de tasklet vmci_dispatch_dgs() llama a vmci_read_data(), que utiliza wait_event(), lo que resulta en una suspensión no válida en un contexto atómico (y, por lo tanto, potencialmente en un interbloqueo). Utilice IRQs con subprocesos para solucionar este problema y eliminar por completo el uso de tasklets. [ 20.264639] ERROR: función de suspensión llamada desde un contexto no válido en drivers/misc/vmw_vmci/vmci_guest.c:145 [ 20.264643] in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 762, name: vmtoolsd [ 20.264645] preempt_count: 101, esperado: 0 [ 20.264646] Profundidad de anidamiento de RCU: 0, esperado: 0 [ 20.264647] 1 bloqueo retenido por vmtoolsd/762: [ 20.264648] #0: ffff0000874ae440 (sk_lock-AF_VSOCK){+.+.}-{0:0}, en: vsock_connect+0x60/0x330 [vsock] [ 20.264658] Preempción deshabilitada en: [ 20.264659] [] vmci_send_datagram+0x44/0xa0 [vmw_vmci] [ 20.264665] CPU: 0 PID: 762 Comm: vmtoolsd No contaminado 5.19.0-0.rc8.20220727git39c3c396f813.60.fc37.aarch64 #1 [ 20.264667] Nombre del hardware: VMware, Inc. VBSA/VBSA, BIOS VEFI 31/12/2020 [ 20.264668] Rastreo de llamadas: [ 20.264669] dump_backtrace+0xc4/0x130 [ 20.264672] show_stack+0x24/0x80 [ 20.264673] dump_stack_lvl+0x88/0xb4 [ 20.264676] dump_stack+0x18/0x34 [ 20.264677] __might_resched+0x1a0/0x280 [ 20.264679] __might_sleep+0x58/0x90 [ 20.264681] vmci_read_data+0x74/0x120 [vmw_vmci] [ 20.264683] vmci_dispatch_dgs+0x64/0x204 [vmw_vmci] [ 20.264686] tasklet_action_common.constprop.0+0x13c/0x150 [ 20.264688] tasklet_action+0x40/0x50 [ 20.264689] __do_softirq+0x23c/0x6b4 [ 20.264690] __irq_exit_rcu+0x104/0x214 [ 20.264691] irq_exit_rcu+0x1c/0x50 [ 20.264693] el1_interrupt+0x38/0x6c [ 20.264695] el1h_64_irq_handler+0x18/0x24 [ 20.264696] el1h_64_irq+0x68/0x6c [ 20.264697] preempt_count_sub+0xa4/0xe0 [ 20.264698] _raw_spin_unlock_irqrestore+0x64/0xb0 [ 20.264701] vmci_send_datagram+0x7c/0xa0 [vmw_vmci] [ 20.264703] vmci_datagram_dispatch+0x84/0x100 [vmw_vmci] [ 20.264706] vmci_datagram_send+0x2c/0x40 [vmw_vmci] [ 20.264709] vmci_transport_send_control_pkt+0xb8/0x120 [vmw_vsock_vmci_transport] [ 20.264711] vmci_transport_connect+0x40/0x7c [vmw_vsock_vmci_transport] [ 20.264713] vsock_connect+0x278/0x330 [vsock] [ 20.264715] __sys_connect_file+0x8c/0xc0 [ 20.264718] __sys_connect+0x84/0xb4 [ 20.264720] __arm64_sys_connect+0x2c/0x3c [ 20.264721] invocar_syscall+0x78/0x100 [ 20.264723] el0_svc_common.constprop.0+0x68/0x124 [ 20.264724] do_el0_svc+0x38/0x4c [ 20.264725] el0_svc+0x60/0x180 [ 20.264726] el0t_64_sync_handler+0x11c/0x150 [ 20.264728] el0t_64_sync+0x190/0x194"}], "references": [{"url": "https://git.kernel.org/stable/c/3daed6345d5880464f46adab871d208e1baa2f3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/548ea9dd5e01b0ecf53d2563004c80abd636743d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
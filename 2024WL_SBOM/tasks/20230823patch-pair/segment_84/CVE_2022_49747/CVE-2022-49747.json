{"cve_id": "CVE-2022-49747", "published_date": "2025-03-27T17:15:39.477", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nerofs/zmap.c: Fix incorrect offset calculation\n\nEffective offset to add to length was being incorrectly calculated,\nwhich resulted in iomap->length being set to 0, triggering a WARN_ON\nin iomap_iter_done().\n\nFix that, and describe it in comments.\n\nThis was reported as a crash by syzbot under an issue about a warning\nencountered in iomap_iter_done(), but unrelated to erofs.\n\nC reproducer: https://syzkaller.appspot.com/text?tag=ReproC&x=1037a6b2880000\nKernel config: https://syzkaller.appspot.com/text?tag=KernelConfig&x=e2021a61197ebe02\nDashboard link: https://syzkaller.appspot.com/bug?extid=a8e049cd3abd342936b6"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: erofs/zmap.c: Se corrigió el cálculo incorrecto del desplazamiento. El desplazamiento efectivo para añadir a length se calculaba incorrectamente, lo que provocaba que iomap-&gt;length se estableciera en 0, lo que activaba un WARN_ON en iomap_iter_done(). Se corrigió y se describió en los comentarios. syzbot reportó esto como un fallo en un problema relacionado con una advertencia encontrada en iomap_iter_done(), pero no relacionada con erofs. Reproductor C: https://syzkaller.appspot.com/text?tag=ReproC&amp;x=1037a6b2880000 Configuración del kernel: https://syzkaller.appspot.com/text?tag=KernelConfig&amp;x=e2021a61197ebe02 Enlace del panel: https://syzkaller.appspot.com/bug?extid=a8e049cd3abd342936b6"}], "references": [{"url": "https://git.kernel.org/stable/c/2144859229c1e74f52d3ea067338d314a83a8afb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6acd87d50998ef0afafc441613aeaf5a8f5c9eff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9f31d8c889d9a4e47bfcc6c4537d0c9f89fe582c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
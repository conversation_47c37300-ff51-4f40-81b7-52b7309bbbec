{"cve_id": "CVE-2022-49748", "published_date": "2025-03-27T17:15:39.597", "last_modified_date": "2025-04-14T20:27:28.673", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nperf/x86/amd: fix potential integer overflow on shift of a int\n\nThe left shift of int 32 bit integer constant 1 is evaluated using 32 bit\narithmetic and then passed as a 64 bit function argument. In the case where\ni is 32 or more this can lead to an overflow.  Avoid this by shifting\nusing the BIT_ULL macro instead."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: perf/x86/amd: se corrige un posible desbordamiento de enteros al desplazar un entero. El desplazamiento a la izquierda de la constante entera de 32 bits 1 se evalúa mediante aritmética de 32 bits y se pasa como argumento de función de 64 bits. Si i es 32 o más, esto puede provocar un desbordamiento. Para evitarlo, utilice la macro BIT_ULL para desplazar."}], "references": [{"url": "https://git.kernel.org/stable/c/08245672cdc6505550d1a5020603b0a8d4a6dcc7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/14cc13e433e1067557435b1adbf05608d7d47a93", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a4d01fb87ece45d4164fd725890211ccf9a307a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f84c9b72fb200633774704d8020f769c88a4b249", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fbf7b0e4cef3b5470b610f14fb9faa5ee7f63954", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
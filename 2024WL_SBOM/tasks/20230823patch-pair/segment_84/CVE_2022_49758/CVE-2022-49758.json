{"cve_id": "CVE-2022-49758", "published_date": "2025-03-27T17:15:41.043", "last_modified_date": "2025-04-15T14:51:37.410", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nreset: uniphier-glue: Fix possible null-ptr-deref\n\nIt will cause null-ptr-deref when resource_size(res) invoked,\nif platform_get_resource() returns NULL."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: reset: uniphier-glue: corrige posible null-ptr-deref Provocará null-ptr-deref cuando se invoque resource_size(res), si platform_get_resource() devuelve NULL."}], "references": [{"url": "https://git.kernel.org/stable/c/3a2390c6777e3f6662980c6cfc25cafe9e4fef98", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/633bad3dc81ce2aa561f704ec091e49eb647bd0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/95de286200b2a046da01c4aeba02ae9220d68ca4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
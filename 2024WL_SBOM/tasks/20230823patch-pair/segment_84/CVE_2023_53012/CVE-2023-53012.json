{"cve_id": "CVE-2023-53012", "published_date": "2025-03-27T17:15:50.290", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nthermal: core: call put_device() only after device_register() fails\n\nput_device() shouldn't be called before a prior call to\ndevice_register(). __thermal_cooling_device_register() doesn't follow\nthat properly and needs fixing. Also\nthermal_cooling_device_destroy_sysfs() is getting called unnecessarily\non few error paths.\n\nFix all this by placing the calls at the right place.\n\nBased on initial work done by <PERSON>."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: thermal: core: llamar a put_device() solo después de que device_register() falle. No se debe llamar a put_device() antes de una llamada previa a device_register(). __thermal_cooling_device_register() no sigue esto correctamente y necesita solución. Además, se llama a thermal_cooling_device_destroy_sysfs() innecesariamente en algunas rutas de error. Para solucionar esto, coloque las llamadas en el lugar correcto. Basado en el trabajo inicial de <PERSON>."}], "references": [{"url": "https://git.kernel.org/stable/c/2846a7412f6246fd5171f51011bf76dfebcec0ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c54b7bc8a31ce0f7cc7f8deef05067df414f1d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a7d736cc3c6cb0d7498bbfb56515d414e35e9510", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52998", "published_date": "2025-03-27T17:15:48.560", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: fec: Use page_pool_put_full_page when freeing rx buffers\n\nThe page_pool_release_page was used when freeing rx buffers, and this\nfunction just unmaps the page (if mapped) and does not recycle the page.\nSo after hundreds of down/up the eth0, the system will out of memory.\nFor more details, please refer to the following reproduce steps and\nbug logs. To solve this issue and refer to the doc of page pool, the\npage_pool_put_full_page should be used to replace page_pool_release_page.\nBecause this API will try to recycle the page if the page refcnt equal to\n1. After testing 20000 times, the issue can not be reproduced anymore\n(about testing 391 times the issue will occur on i.MX8MN-EVK before).\n\nReproduce steps:\nCreate the test script and run the script. The script content is as\nfollows:\nLOOPS=20000\ni=1\nwhile [ $i -le $LOOPS ]\ndo\n    echo \"TINFO:ENET $curface up and down test $i times\"\n    org_macaddr=$(cat /sys/class/net/eth0/address)\n    ifconfig eth0 down\n    ifconfig eth0  hw ether $org_macaddr up\n    i=$(expr $i + 1)\ndone\nsleep 5\nif cat /sys/class/net/eth0/operstate | grep 'up';then\n    echo \"TEST PASS\"\nelse\n    echo \"TEST FAIL\"\nfi\n\nBug detail logs:\nTINFO:ENET  up and down test 391 times\n[  850.471205] Qualcomm Atheros AR8031/AR8033 30be0000.ethernet-1:00: attached PHY driver (mii_bus:phy_addr=30be0000.ethernet-1:00, irq=POLL)\n[  853.535318] IPv6: ADDRCONF(NETDEV_CHANGE): eth0: link becomes ready\n[  853.541694] fec 30be0000.ethernet eth0: Link is Up - 1Gbps/Full - flow control rx/tx\n[  870.590531] page_pool_release_retry() stalled pool shutdown 199 inflight 60 sec\n[  931.006557] page_pool_release_retry() stalled pool shutdown 199 inflight 120 sec\nTINFO:ENET  up and down test 392 times\n[  991.426544] page_pool_release_retry() stalled pool shutdown 192 inflight 181 sec\n[ 1051.838531] page_pool_release_retry() stalled pool shutdown 170 inflight 241 sec\n[ 1093.751217] Qualcomm Atheros AR8031/AR8033 30be0000.ethernet-1:00: attached PHY driver (mii_bus:phy_addr=30be0000.ethernet-1:00, irq=POLL)\n[ 1096.446520] page_pool_release_retry() stalled pool shutdown 308 inflight 60 sec\n[ 1096.831245] fec 30be0000.ethernet eth0: Link is Up - 1Gbps/Full - flow control rx/tx\n[ 1096.839092] IPv6: ADDRCONF(NETDEV_CHANGE): eth0: link becomes ready\n[ 1112.254526] page_pool_release_retry() stalled pool shutdown 103 inflight 302 sec\n[ 1156.862533] page_pool_release_retry() stalled pool shutdown 308 inflight 120 sec\n[ 1172.674516] page_pool_release_retry() stalled pool shutdown 103 inflight 362 sec\n[ 1217.278532] page_pool_release_retry() stalled pool shutdown 308 inflight 181 sec\nTINFO:ENET  up and down test 393 times\n[ 1233.086535] page_pool_release_retry() stalled pool shutdown 103 inflight 422 sec\n[ 1277.698513] page_pool_release_retry() stalled pool shutdown 308 inflight 241 sec\n[ 1293.502525] page_pool_release_retry() stalled pool shutdown 86 inflight 483 sec\n[ 1338.110518] page_pool_release_retry() stalled pool shutdown 308 inflight 302 sec\n[ 1353.918540] page_pool_release_retry() stalled pool shutdown 32 inflight 543 sec\n[ 1361.179205] Qualcomm Atheros AR8031/AR8033 30be0000.ethernet-1:00: attached PHY driver (mii_bus:phy_addr=30be0000.ethernet-1:00, irq=POLL)\n[ 1364.255298] fec 30be0000.ethernet eth0: Link is Up - 1Gbps/Full - flow control rx/tx\n[ 1364.263189] IPv6: ADDRCONF(NETDEV_CHANGE): eth0: link becomes ready\n[ 1371.998532] page_pool_release_retry() stalled pool shutdown 310 inflight 60 sec\n[ 1398.530542] page_pool_release_retry() stalled pool shutdown 308 inflight 362 sec\n[ 1414.334539] page_pool_release_retry() stalled pool shutdown 16 inflight 604 sec\n[ 1432.414520] page_pool_release_retry() stalled pool shutdown 310 inflight 120 sec\n[ 1458.942523] page_pool_release_retry() stalled pool shutdown 308 inflight 422 sec\n[ 1474.750521] page_pool_release_retry() stalled pool shutdown 16 inflight 664 sec\nTINFO:ENET  up and down test 394 times\n[ 1492.8305\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: fec: Usar page_pool_put_full_page al liberar búferes rx Se usó page_pool_release_page al liberar búferes rx, y esta función solo desasigna la página (si está asignada) y no la recicla. Entonces, después de cientos de subidas y bajadas de eth0, el sistema se quedará sin memoria. Para obtener más detalles, consulte los siguientes pasos de reproducción y los registros de errores. Para resolver este problema y consultar la documentación del grupo de páginas, se debe usar page_pool_put_full_page para reemplazar page_pool_release_page. Porque esta API intentará reciclar la página si la referencia de la página es igual a 1. Después de probar 20000 veces, el problema ya no se puede reproducir (aproximadamente 391 veces que se probó el problema en i.MX8MN-EVK antes). Pasos de reproducción: Cree el script de prueba y ejecútelo. El contenido del script es el siguiente: LOOPS=20000 i=1 while [ $i -le $LOOPS ] do echo \"TINFO:ENET $curface up and down test $i times\" org_macaddr=$(cat /sys/class/net/eth0/address) ifconfig eth0 down ifconfig eth0 hw ether $org_macaddr up i=$(expr $i + 1) done sleep 5 if cat /sys/class/net/eth0/operstate | grep 'up';then echo \"TEST PASS\" else echo \"TEST FAIL\" fi Registros de detalles de errores: TINFO: prueba de ENET activada y desactivada 391 veces [850.471205] Qualcomm Atheros AR8031/AR8033 30be0000.ethernet-1:00: controlador PHY conectado (mii_bus:phy_addr=30be0000.ethernet-1:00, irq=POLL) [853.535318] IPv6: ADDRCONF(NETDEV_CHANGE): eth0: el enlace está listo [853.541694] fec 30be0000.ethernet eth0: el enlace está activo - 1 Gbps/completo - control de flujo rx/tx [870.590531] page_pool_release_retry() apagado del grupo estancado 199 durante el vuelo 60 s [931.006557] page_pool_release_retry() apagado del grupo detenido 199 durante el vuelo 120 s TINFO:ENET prueba de subida y bajada 392 veces [991.426544] page_pool_release_retry() apagado del grupo detenido 192 durante el vuelo 181 s [1051.838531] page_pool_release_retry() apagado del grupo detenido 170 durante el vuelo 241 s [1093.751217] Qualcomm Atheros AR8031/AR8033 30be0000.ethernet-1:00: controlador PHY conectado (mii_bus:phy_addr=30be0000.ethernet-1:00, irq=POLL) [1096.446520] page_pool_release_retry() apagado de grupo estancado 308 en vuelo 60 seg [ 1096.831245] fec 30be0000.ethernet eth0: Enlace activo - 1 Gbps/completo - control de flujo rx/tx [ 1096.839092] IPv6: ADDRCONF(NETDEV_CHANGE): eth0: enlace listo [ 1112.254526] page_pool_release_retry() apagado de grupo estancado 103 en vuelo 302 seg [ 1156.862533] page_pool_release_retry() apagado de grupo estancado 308 en vuelo 120 seg [ 1172.674516] page_pool_release_retry() apagado de grupo estancado 103 en vuelo 362 seg [ 1217.278532] page_pool_release_retry() detuvo el apagado del grupo 308 durante el vuelo 181 s TINFO:ENET prueba de subida y bajada 393 veces [ 1233.086535] page_pool_release_retry() detuvo el apagado del grupo 103 durante el vuelo 422 s [ 1277.698513] page_pool_release_retry() detuvo el apagado del grupo 308 durante el vuelo 241 s [ 1293.502525] page_pool_release_retry() detuvo el apagado del grupo 86 durante el vuelo 483 s [ 1338.110518] page_pool_release_retry() detuvo el apagado del grupo 308 durante el vuelo 302 s [ 1353.918540] page_pool_release_retry() detuvo el apagado del grupo 32 durante el vuelo 543 seg [1361.179205] Qualcomm Atheros AR8031/AR8033 30be0000.ethernet-1:00: controlador PHY conectado (mii_bus:phy_addr=30be0000.ethernet-1:00, irq=POLL) [1364.255298] fec 30be0000.ethernet eth0: el enlace está activo - 1 Gbps/completo - control de flujo rx/tx [1364.263189] IPv6: ADDRCONF(NETDEV_CHANGE): eth0: el enlace está listo [1371.998532] page_pool_release_retry() apagado del grupo detenido 310 en vuelo 60 seg [1398.530542] page_pool_release_retry() apagado del grupo detenido 308 en vuelo 362 seg [1414.334539] page_pool_release_retry() apagado del grupo detenido 16 en vuelo 604 seg [1432.414520] page_pool_release_retry() apagado del grupo  ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/554484a34e985a307756ee4794e60be31e3db2e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e38553bdc377e3e7a6caa9dd9770d8b644d8dac3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
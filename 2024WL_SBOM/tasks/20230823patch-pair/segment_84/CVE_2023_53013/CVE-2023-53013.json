{"cve_id": "CVE-2023-53013", "published_date": "2025-03-27T17:15:50.423", "last_modified_date": "2025-04-14T20:51:49.353", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nptdma: pt_core_execute_cmd() should use spinlock\n\nThe interrupt handler (pt_core_irq_handler()) of the ptdma\ndriver can be called from interrupt context. The code flow\nin this function can lead down to pt_core_execute_cmd() which\nwill attempt to grab a mutex, which is not appropriate in\ninterrupt context and ultimately leads to a kernel panic.\nThe fix here changes this mutex to a spinlock, which has\nbeen verified to resolve the issue."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ptdma: pt_core_execute_cmd() debería usar un bloqueo de giro. El manejador de interrupciones (pt_core_irq_handler()) del controlador ptdma puede llamarse desde el contexto de interrupción. El flujo de código en esta función puede derivar en pt_core_execute_cmd(), que intentará obtener un mutex, lo cual no es apropiado en el contexto de interrupción y, en última instancia, provoca un pánico del kernel. Esta corrección convierte este mutex en un bloqueo de giro, lo cual se ha verificado para resolver el problema."}], "references": [{"url": "https://git.kernel.org/stable/c/13ba563c2c8055ba8a637c9f70bb833b43cb4207", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/95e5fda3b5f9ed8239b145da3fa01e641cf5d53c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ed0d8f731e0bf1bb12a7a37698ac613db20e2794", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
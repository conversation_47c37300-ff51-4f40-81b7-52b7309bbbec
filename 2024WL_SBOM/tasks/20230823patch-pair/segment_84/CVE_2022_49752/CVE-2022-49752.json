{"cve_id": "CVE-2022-49752", "published_date": "2025-03-27T17:15:40.110", "last_modified_date": "2025-04-14T20:16:40.710", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndevice property: fix of node refcount leak in fwnode_graph_get_next_endpoint()\n\nThe 'parent' returned by fwnode_graph_get_port_parent()\nwith refcount incremented when 'prev' is not NULL, it\nneeds be put when finish using it.\n\nBecause the parent is const, introduce a new variable to\nstore the returned fwnode, then put it before returning\nfrom fwnode_graph_get_next_endpoint()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: propiedad del dispositivo: corrección de la fuga de recuento de referencias de nodos en fwnode_graph_get_next_endpoint(). El valor de \"parent\" devuelto por fwnode_graph_get_port_parent(), con un recuento de referencias incrementado cuando \"prev\" no es NULL, debe añadirse al finalizar su uso. Dado que \"parent\" es constante, se introduce una nueva variable para almacenar el fwnode devuelto y se añade antes de que fwnode_graph_get_next_endpoint() lo devuelva."}], "references": [{"url": "https://git.kernel.org/stable/c/39af728649b05e88a2b40e714feeee6451c3f18e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7701a4bd45c11f9a289d8f262fad05705a012339", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e75485fc589ec729cc182aa9b41dfb6c15ae6f6e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-52977", "published_date": "2025-03-27T17:15:44.793", "last_modified_date": "2025-04-15T14:49:08.120", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: openvswitch: fix flow memory leak in ovs_flow_cmd_new\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> reports a memory leak of new_flow in ovs_flow_cmd_new() as it is\nnot freed when an allocation of a key fails.\n\nBUG: memory leak\nunreferenced object 0xffff888116668000 (size 632):\n  comm \"syz-executor231\", pid 1090, jiffies 4294844701 (age 18.871s)\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n  backtrace:\n    [<00000000defa3494>] kmem_cache_zalloc include/linux/slab.h:654 [inline]\n    [<00000000defa3494>] ovs_flow_alloc+0x19/0x180 net/openvswitch/flow_table.c:77\n    [<00000000c67d8873>] ovs_flow_cmd_new+0x1de/0xd40 net/openvswitch/datapath.c:957\n    [<0000000010a539a8>] genl_family_rcv_msg_doit+0x22d/0x330 net/netlink/genetlink.c:739\n    [<00000000dff3302d>] genl_family_rcv_msg net/netlink/genetlink.c:783 [inline]\n    [<00000000dff3302d>] genl_rcv_msg+0x328/0x590 net/netlink/genetlink.c:800\n    [<000000000286dd87>] netlink_rcv_skb+0x153/0x430 net/netlink/af_netlink.c:2515\n    [<0000000061fed410>] genl_rcv+0x24/0x40 net/netlink/genetlink.c:811\n    [<000000009dc0f111>] netlink_unicast_kernel net/netlink/af_netlink.c:1313 [inline]\n    [<000000009dc0f111>] netlink_unicast+0x545/0x7f0 net/netlink/af_netlink.c:1339\n    [<000000004a5ee816>] netlink_sendmsg+0x8e7/0xde0 net/netlink/af_netlink.c:1934\n    [<00000000482b476f>] sock_sendmsg_nosec net/socket.c:651 [inline]\n    [<00000000482b476f>] sock_sendmsg+0x152/0x190 net/socket.c:671\n    [<00000000698574ba>] ____sys_sendmsg+0x70a/0x870 net/socket.c:2356\n    [<00000000d28d9e11>] ___sys_sendmsg+0xf3/0x170 net/socket.c:2410\n    [<0000000083ba9120>] __sys_sendmsg+0xe5/0x1b0 net/socket.c:2439\n    [<00000000c00628f8>] do_syscall_64+0x30/0x40 arch/x86/entry/common.c:46\n    [<000000004abfdcf4>] entry_SYSCALL_64_after_hwframe+0x61/0xc6\n\nTo fix this the patch rearranges the goto labels to reflect the order of\nobject allocations and adds appropriate goto statements on the error\npaths.\n\nFound by Linux Verification Center (linuxtesting.org) with Syzkaller."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: openvswitch: corrige pérdida de memoria de flujo en ovs_flow_cmd_new Syzkaller informa una pérdida de memoria de new_flow en ovs_flow_cmd_new() ya que no se libera cuando falla la asignación de una clave. ERROR: Fuga de memoria, objeto no referenciado 0xffff888116668000 (tamaño 632): comunicación \"syz-executor231\", pid 1090, jiffies 4294844701 (edad 18.871 s) volcado hexadecimal (primeros 32 bytes): 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ seguimiento inverso: [&lt;00000000defa3494&gt;] kmem_cache_zalloc include/linux/slab.h:654 [en línea] [&lt;00000000defa3494&gt;] ovs_flow_alloc+0x19/0x180 net/openvswitch/flow_table.c:77 [&lt;00000000c67d8873&gt;] ovs_flow_cmd_new+0x1de/0xd40 net/openvswitch/datapath.c:957 [&lt;0000000010a539a8&gt;] genl_family_rcv_msg_doit+0x22d/0x330 net/netlink/genetlink.c:739 [&lt;00000000dff3302d&gt;] genl_family_rcv_msg net/netlink/genetlink.c:783 [en línea] [&lt;00000000dff3302d&gt;] genl_rcv_msg+0x328/0x590 net/netlink/genetlink.c:800 [&lt;000000000286dd87&gt;] netlink_rcv_skb+0x153/0x430 net/netlink/af_netlink.c:2515 [&lt;0000000061fed410&gt;] genl_rcv+0x24/0x40 net/netlink/genetlink.c:811 [&lt;000000009dc0f111&gt;] netlink_unicast_kernel net/netlink/af_netlink.c:1313 [en línea] [&lt;000000009dc0f111&gt;] netlink_unicast+0x545/0x7f0 net/netlink/af_netlink.c:1339 [&lt;000000004a5ee816&gt;] netlink_sendmsg+0x8e7/0xde0 net/netlink/af_netlink.c:1934 [&lt;00000000482b476f&gt;] sock_sendmsg_nosec net/socket.c:651 [en línea] [&lt;00000000482b476f&gt;] sock_sendmsg+0x152/0x190 net/socket.c:671 [&lt;00000000698574ba&gt;] ____sys_sendmsg+0x70a/0x870 net/socket.c:2356 [&lt;00000000d28d9e11&gt;] ___sys_sendmsg+0xf3/0x170 net/socket.c:2410 [&lt;0000000083ba9120&gt;] __sys_sendmsg+0xe5/0x1b0 net/socket.c:2439 [&lt;00000000c00628f8&gt;] do_syscall_64+0x30/0x40 arch/x86/entry/common.c:46 [&lt;000000004abfdcf4&gt;] entry_SYSCALL_64_after_hwframe+0x61/0xc6 Para solucionar esto, el parche reorganiza las etiquetas goto para reflejar el orden de asignación de objetos y añade instrucciones goto adecuadas en las rutas de error. Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con Syzkaller."}], "references": [{"url": "https://git.kernel.org/stable/c/0c598aed445eb45b0ee7ba405f7ece99ee349c30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1ac653cf886cdfc082708c82dc6ac6115cebd2ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/70154489f531587996f3e9d7cceeee65cff0001d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/70d40674a549d498bd63d5432acf46205da1534b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/af4e720bc00a2653f7b9df21755b9978b3d7f386", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ed6c5e8caf55778500202775167e8ccdb1a030cb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f423c2efd51d7eb1d143c2be7eea233241d9bbbf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
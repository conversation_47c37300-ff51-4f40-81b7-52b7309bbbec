{"cve_id": "CVE-2022-49744", "published_date": "2025-03-27T17:15:39.093", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/uffd: fix pte marker when fork() without fork event\n\nPatch series \"mm: Fixes on pte markers\".\n\nPatch 1 resolves the syzkiller report from Pengfei.\n\nPatch 2 further harden pte markers when used with the recent swapin error\nmarkers.  The major case is we should persist a swapin error marker after\nfork(), so child shouldn't read a corrupted page.\n\n\nThis patch (of 2):\n\nWhen fork(), dst_vma is not guaranteed to have VM_UFFD_WP even if src may\nhave it and has pte marker installed.  The warning is improper along with\nthe comment.  The right thing is to inherit the pte marker when needed, or\nkeep the dst pte empty.\n\nA vague guess is this happened by an accident when there's the prior patch\nto introduce src/dst vma into this helper during the uffd-wp feature got\ndeveloped and I probably messed up in the rebase, since if we replace\ndst_vma with src_vma the warning & comment it all makes sense too.\n\nHugetlb did exactly the right here (copy_hugetlb_page_range()).  Fix the\ngeneral path.\n\nReproducer:\n\nhttps://github.com/xupengfe/syzkaller_logs/blob/main/221208_115556_copy_page_range/repro.c\n\nBugzilla report: https://bugzilla.kernel.org/show_bug.cgi?id=216808"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/uffd: corrección del marcador pte cuando fork() no tiene evento fork. Serie de parches \"mm: Correcciones en marcadores pte\". El parche 1 resuelve el informe de syzkiller de Pengfei. El parche 2 refuerza aún más los marcadores pte cuando se usan con los recientes marcadores de error de intercambio. El caso principal es que debemos conservar un marcador de error de intercambio después de fork(), para que el elemento secundario no lea una página dañada. Este parche (de 2): Al ejecutar fork(), no se garantiza que dst_vma tenga VM_UFFD_WP, incluso si src lo tiene y tiene instalado el marcador pte. La advertencia y el comentario son incorrectos. Lo correcto es heredar el marcador pte cuando sea necesario o dejar vacío el marcador pte de dst. Una vaga suposición es que esto ocurrió accidentalmente durante el parche anterior para introducir src/dst vma en este ayudante durante el desarrollo de la función uffd-wp, y probablemente cometí un error al rebasar, ya que si reemplazamos dst_vma con src_vma, la advertencia y el comentario también cobran sentido. Hugetlb hizo exactamente lo correcto aquí (copy_hugetlb_page_range()). Corrija la ruta general. Reproductor: https://github.com/xupengfe/syzkaller_logs/blob/main/221208_115556_copy_page_range/repro.c. Informe de Bugzilla: https://bugzilla.kernel.org/show_bug.cgi?id=216808"}], "references": [{"url": "https://git.kernel.org/stable/c/2d11727655bf931776fb541f5862daf04bd5bf02", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/49d6d7fb631345b0f2957a7c4be24ad63903150f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
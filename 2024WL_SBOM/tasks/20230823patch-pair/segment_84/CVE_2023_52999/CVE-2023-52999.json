{"cve_id": "CVE-2023-52999", "published_date": "2025-03-27T17:15:48.683", "last_modified_date": "2025-04-01T15:39:31.593", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: fix UaF in netns ops registration error path\n\nIf net_assign_generic() fails, the current error path in ops_init() tries\nto clear the gen pointer slot. Anyway, in such error path, the gen pointer\nitself has not been modified yet, and the existing and accessed one is\nsmaller than the accessed index, causing an out-of-bounds error:\n\n BUG: KASAN: slab-out-of-bounds in ops_init+0x2de/0x320\n Write of size 8 at addr ffff888109124978 by task modprobe/1018\n\n CPU: 2 PID: 1018 Comm: modprobe Not tainted 6.2.0-rc2.mptcp_ae5ac65fbed5+ #1641\n Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.1-2.fc37 04/01/2014\n Call Trace:\n  <TASK>\n  dump_stack_lvl+0x6a/0x9f\n  print_address_description.constprop.0+0x86/0x2b5\n  print_report+0x11b/0x1fb\n  kasan_report+0x87/0xc0\n  ops_init+0x2de/0x320\n  register_pernet_operations+0x2e4/0x750\n  register_pernet_subsys+0x24/0x40\n  tcf_register_action+0x9f/0x560\n  do_one_initcall+0xf9/0x570\n  do_init_module+0x190/0x650\n  load_module+0x1fa5/0x23c0\n  __do_sys_finit_module+0x10d/0x1b0\n  do_syscall_64+0x58/0x80\n  entry_SYSCALL_64_after_hwframe+0x72/0xdc\n RIP: 0033:0x7f42518f778d\n Code: 00 c3 66 2e 0f 1f 84 00 00 00 00 00 90 f3 0f 1e fa 48 89 f8 48 89 f7 48\n       89 d6 48 89 ca 4d 89 c2 4d 89 c8 4c 8b 4c 24 08 0f 05 <48> 3d 01 f0 ff\n       ff 73 01 c3 48 8b 0d cb 56 2c 00 f7 d8 64 89 01 48\n RSP: 002b:00007fff96869688 EFLAGS: 00000246 ORIG_RAX: 0000000000000139\n RAX: ffffffffffffffda RBX: 00005568ef7f7c90 RCX: 00007f42518f778d\n RDX: 0000000000000000 RSI: 00005568ef41d796 RDI: 0000000000000003\n RBP: 00005568ef41d796 R08: 0000000000000000 R09: 0000000000000000\n R10: 0000000000000003 R11: 0000000000000246 R12: 0000000000000000\n R13: 00005568ef7f7d30 R14: 0000000000040000 R15: 0000000000000000\n  </TASK>\n\nThis change addresses the issue by skipping the gen pointer\nde-reference in the mentioned error-path.\n\nFound by code inspection and verified with explicit error injection\non a kasan-enabled kernel."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: corrige UaF en la ruta de error de registro de operaciones de netns Si net_assign_generic() falla, la ruta de error actual en ops_init() intenta borrar la ranura del puntero gen. De todas formas, en dicha ruta de error, el puntero gen en sí no se ha modificado aún, y el existente y al que se ha accedido es más pequeño que el índice accedido, lo que provoca un error fuera de los límites: BUG: KASAN: slab-out-of-bounds en ops_init+0x2de/0x320 Escritura de tamaño 8 en la dirección ffff888109124978 por la tarea modprobe/1018 CPU: 2 PID: 1018 Comm: modprobe No contaminado 6.2.0-rc2.mptcp_ae5ac65fbed5+ #1641 Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.1-2.fc37 04/01/2014 Rastreo de llamadas:   dump_stack_lvl+0x6a/0x9f print_address_description.constprop.0+0x86/0x2b5 print_report+0x11b/0x1fb kasan_report+0x87/0xc0 ops_init+0x2de/0x320 register_pernet_operations+0x2e4/0x750 register_pernet_subsys+0x24/0x40 tcf_register_action+0x9f/0x560 do_one_initcall+0xf9/0x570 do_init_module+0x190/0x650 load_module+0x1fa5/0x23c0 __do_sys_finit_module+0x10d/0x1b0 do_syscall_64+0x58/0x80 entry_SYSCALL_64_after_hwframe+0x72/0xdc RIP: 0033:0x7f42518f778d Code: 00 c3 66 2e 0f 1f 84 00 00 00 00 00 90 f3 0f 1e fa 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 4c 8b 4c 24 08 0f 05 &lt;48&gt; 3d 01 f0 ff ff 73 01 c3 48 8b 0d cb 56 2c 00 f7 d8 64 89 01 48 RSP: 002b:00007fff96869688 EFLAGS: 00000246 ORIG_RAX: 0000000000000139 RAX: ffffffffffffffda RBX: 00005568ef7f7c90 RCX: 00007f42518f778d RDX: 0000000000000000 RSI: 00005568ef41d796 RDI: 0000000000000003 RBP: 00005568ef41d796 R08: 0000000000000000 R09: 0000000000000000 R10: 0000000000000003 R11: 0000000000000246 R12: 0000000000000000 R13: 00005568ef7f7d30 R14: 0000000000040000 R15: 0000000000000000  Este cambio soluciona el problema omitiendo la desreferencia del puntero gen en la ruta de error mencionada. Se detectó mediante inspección de código y se verificó con inyección de error explícita en un kernel con kasan habilitado."}], "references": [{"url": "https://git.kernel.org/stable/c/12075708f2e77ee6a9f8bb2cf512c38be3099794", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/66689a72ba73575e76d4f6a8748d3fa2690ec1c4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/71ab9c3e2253619136c31c89dbb2c69305cc89b1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ad0dfe9bcf0d78e699c7efb64c90ed062dc48bea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d4c008f3b7f7d4ffd311eb2dae5e75b3cbddacd0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ddd49cbbd4c1ceb38032018b589b44208e54f55e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
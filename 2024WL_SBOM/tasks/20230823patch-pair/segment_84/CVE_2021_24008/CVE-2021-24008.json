{"cve_id": "CVE-2021-24008", "published_date": "2025-03-28T11:15:36.620", "last_modified_date": "2025-07-24T19:57:26.330", "descriptions": [{"lang": "en", "value": "An exposure of sensitive system information to an unauthorized control sphere vulnerability [CWE-497] in FortiDDoS version 5.4.0, version 5.3.2 and below, version 5.2.0, version 5.1.0, version 5.0.0, version 4.7.0, version 4.6.0, version 4.5.0, version 4.4.2 and below, FortiDDoS-CM version 5.3.0, version 5.2.0, version 5.1.0, version 5.0.0, version 4.7.0, FortiVoice version 6.0.6 and below, FortiRecorder version 6.0.3 and below and FortiMail version 6.4.1 and below, version 6.2.4 and below, version 6.0.9 and below may allow a remote, unauthenticated attacker to obtain potentially sensitive software-version information by reading a JavaScript file."}, {"lang": "es", "value": "Una exposición de información sensible del sistema a una vulnerabilidad de esfera de control no autorizada [CWE-497] en FortiDDoS versión 5.4.0, versión 5.3.2 y anteriores, versión 5.2.0, versión 5.1.0, versión 5.0.0, versión 4.7.0, versión 4.6.0, versión 4.5.0, versión 4.4.2 y anteriores, FortiDDoS-CM versión 5.3.0, versión 5.2.0, versión 5.1.0, versión 5.0.0, versión 4.7.0, FortiVoice versión 6.0.6 y anteriores, FortiRecorder versión 6.0.3 y anteriores y FortiMail versión 6.4.1 y anteriores, versión 6.2.4 y anteriores, versión 6.0.9 y anteriores puede permitir que un atacante remoto no autenticado obtenga información potencialmente sensible de la versión del software leyendo un archivo JavaScript."}], "references": [{"url": "https://fortiguard.fortinet.com/psirt/FG-IR-20-105", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
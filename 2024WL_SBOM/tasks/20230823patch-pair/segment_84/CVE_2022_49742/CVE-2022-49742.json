{"cve_id": "CVE-2022-49742", "published_date": "2025-03-27T17:15:38.847", "last_modified_date": "2025-04-14T20:27:03.027", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nf2fs: initialize locks earlier in f2fs_fill_super()\n\nsyzbot is reporting lockdep warning at f2fs_handle_error() [1], for\nspin_lock(&sbi->error_lock) is called before spin_lock_init() is called.\nFor safe locking in error handling, move initialization of locks (and\nobvious structures) in f2fs_fill_super() to immediately after memory\nallocation."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: f2fs: inicializar bloqueos antes en f2fs_fill_super(). syzbot reporta una advertencia de bloqueo en f2fs_handle_error() [1], ya que se llama a spin_lock(&amp;sbi-&gt;error_lock) antes de que se llame a spin_lock_init(). Para un bloqueo seguro en la gestión de errores, se debe trasladar la inicialización de bloqueos (y estructuras obvias) en f2fs_fill_super() inmediatamente después de la asignación de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/92b4cf5b48955a4bdd15fe4e2067db8ebd87f04c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ddeff03bb33810fcf2f0c18e03d099cf0aacda62", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
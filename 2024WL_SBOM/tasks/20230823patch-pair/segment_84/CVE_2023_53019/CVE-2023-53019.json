{"cve_id": "CVE-2023-53019", "published_date": "2025-03-27T17:15:51.330", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: mdio: validate parameter addr in mdiobus_get_phy()\n\nThe caller may pass any value as addr, what may result in an out-of-bounds\naccess to array mdio_map. One existing case is stmmac_init_phy() that\nmay pass -1 as addr. Therefore validate addr before using it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: mdio: validar el parámetro addr en mdiobus_get_phy(). El llamador puede pasar cualquier valor como addr, lo que puede resultar en un acceso fuera de límites a la matriz mdio_map. Un caso existente es stmmac_init_phy(), que puede pasar -1 como addr. Por lo tanto, valide addr antes de usarlo."}], "references": [{"url": "https://git.kernel.org/stable/c/1d80c259dfbadefa61b7ea334dfce5cb57f8c72f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4bc5f1f6bc94e695dfd912122af96e7115a0ddb8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7879626296e6ffd838ae0f2af1ab49ee46354973", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/867dbe784c5010a466f00a7d1467c1c5ea569c75", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8a7b9560a3a8eb8724888c426e05926752f73aa0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad67de330d83e8078372b52af18ffe8d39e26c85", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c431a3d642593bbdb99e8a9e3eed608b730db6f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
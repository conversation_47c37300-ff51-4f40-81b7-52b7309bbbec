{"cve_id": "CVE-2023-53033", "published_date": "2025-03-27T17:15:53.120", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetfilter: nft_payload: incorrect arithmetics when fetching VLAN header bits\n\nIf the offset + length goes over the ethernet + vlan header, then the\nlength is adjusted to copy the bytes that are within the boundaries of\nthe vlan_ethhdr scratchpad area. The remaining bytes beyond ethernet +\nvlan header are copied directly from the skbuff data area.\n\nFix incorrect arithmetic operator: subtract, not add, the size of the\nvlan header in case of double-tagged packets to adjust the length\naccordingly to address CVE-2023-0179."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netfilter: nft_payload: aritmética incorrecta al obtener bits del encabezado VLAN. Si el desplazamiento + longitud sobrepasa el encabezado Ethernet + VLAN, esta se ajusta para copiar los bytes dentro de los límites del área de borrador vlan_ethhdr. Los bytes restantes más allá del encabezado Ethernet + VLAN se copian directamente del área de datos skbuff. Se corrige el operador aritmético incorrecto: restar, no sumar, el tamaño del encabezado VLAN en el caso de paquetes con doble etiqueta para ajustar la longitud según la solución CVE-2023-0179."}], "references": [{"url": "https://git.kernel.org/stable/c/550efeff989b041f3746118c0ddd863c39ddc1aa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/696e1a48b1a1b01edad542a1ef293665864a4dd0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76ef74d4a379faa451003621a84e3498044e7aa3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a8acfe2c6fb99f9375a9325807a179cd8c32e6e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
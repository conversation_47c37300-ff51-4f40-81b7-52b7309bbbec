{"cve_id": "CVE-2023-52985", "published_date": "2025-03-27T17:15:45.810", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64: dts: imx8mm-verdin: Do not power down eth-phy\n\nCurrently if suspending using either freeze or memory state, the fec\ndriver tries to power down the phy which leads to crash of the kernel\nand non-responsible kernel with the following call trace:\n\n[   24.839889 ] Call trace:\n[   24.839892 ]  phy_error+0x18/0x60\n[   24.839898 ]  kszphy_handle_interrupt+0x6c/0x80\n[   24.839903 ]  phy_interrupt+0x20/0x2c\n[   24.839909 ]  irq_thread_fn+0x30/0xa0\n[   24.839919 ]  irq_thread+0x178/0x2c0\n[   24.839925 ]  kthread+0x154/0x160\n[   24.839932 ]  ret_from_fork+0x10/0x20\n\nSince there is currently no functionality in the phy subsystem to power\ndown phys let's just disable the feature of powering-down the ethernet\nphy."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64: dts: imx8mm-verdin: No apague eth-phy Actualmente, si se suspende utilizando el estado de congelación o de memoria, el controlador fec intenta apagar el phy, lo que provoca un bloqueo del kernel y un kernel no responsable con el siguiente seguimiento de llamada: [ 24.839889 ] Seguimiento de llamada: [ 24.839892 ] phy_error+0x18/0x60 [ 24.839898 ] kszphy_handle_interrupt+0x6c/0x80 [ 24.839903 ] phy_interrupt+0x20/0x2c [ 24.839909 ] irq_thread_fn+0x30/0xa0 [ 24.839919 ] irq_thread+0x178/0x2c0 [ 24.839925 ] kthread+0x154/0x160 [ 24.839932 ] ret_from_fork+0x10/0x20 Dado que actualmente no hay ninguna funcionalidad en el subsistema phy para apagar phys, deshabilitemos la función de apagar el phy Ethernet."}], "references": [{"url": "https://git.kernel.org/stable/c/0bdd5a7b517f16fdffc444be6516c45788548d08", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39c95d0c357d7ef76aea958c1bece6b24f9b2e7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
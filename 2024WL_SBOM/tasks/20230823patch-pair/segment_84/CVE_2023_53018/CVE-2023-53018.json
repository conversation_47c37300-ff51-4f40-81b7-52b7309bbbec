{"cve_id": "CVE-2023-53018", "published_date": "2025-03-27T17:15:51.223", "last_modified_date": "2025-04-15T19:41:43.143", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: hci_conn: Fix memory leaks\n\nWhen hci_cmd_sync_queue() failed in hci_le_terminate_big() or\nhci_le_big_terminate(), the memory pointed by variable d is not freed,\nwhich will cause memory leak. Add release process to error path."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: hci_conn: Se corrigen fugas de memoria. Cuando hci_cmd_sync_queue() falla en hci_le_terminate_big() o hci_le_big_terminate(), la memoria apuntada por la variable d no se libera, lo que provoca una fuga de memoria. Se añade el proceso de liberación a la ruta de error."}], "references": [{"url": "https://git.kernel.org/stable/c/3aa21311f36d8a2730c7ccef37235e951f23927b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f51a825b9f730a782aa768454906b4468e67b667", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
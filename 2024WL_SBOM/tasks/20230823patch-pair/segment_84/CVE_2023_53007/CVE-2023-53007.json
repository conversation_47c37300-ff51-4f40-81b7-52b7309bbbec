{"cve_id": "CVE-2023-53007", "published_date": "2025-03-27T17:15:49.670", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntracing: Make sure trace_printk() can output as soon as it can be used\n\nCurrently trace_printk() can be used as soon as early_trace_init() is\ncalled from start_kernel(). But if a crash happens, and\n\"ftrace_dump_on_oops\" is set on the kernel command line, all you get will\nbe:\n\n  [    0.456075]   <idle>-0         0dN.2. 347519us : Unknown type 6\n  [    0.456075]   <idle>-0         0dN.2. 353141us : Unknown type 6\n  [    0.456075]   <idle>-0         0dN.2. 358684us : Unknown type 6\n\nThis is because the trace_printk() event (type 6) hasn't been registered\nyet. That gets done via an early_initcall(), which may be early, but not\nearly enough.\n\nInstead of registering the trace_printk() event (and other ftrace events,\nwhich are not trace events) via an early_initcall(), have them registered at\nthe same time that trace_printk() can be used. This way, if there is a\ncrash before early_initcall(), then the trace_printk()s will actually be\nuseful."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rastreo: Asegúrese de que trace_printk() pueda generar resultados tan pronto como sea posible. Actualmente, trace_printk() puede usarse tan pronto como se llame a early_trace_init() desde start_kernel(). Sin embargo, si se produce un fallo y se configura \"ftrace_dump_on_oops\" en la línea de comandos del kernel, solo se obtendrá: [ 0.456075] -0 0dN.2.347519us : Tipo desconocido 6 [ 0.456075] -0 0dN.2.353141us : Tipo desconocido 6 [ 0.456075] -0 0dN.2.358684us : Tipo desconocido 6. Esto se debe a que el evento trace_printk() (tipo 6) aún no se ha registrado. Esto se realiza mediante una llamada early_initcall(), que puede ser temprana, pero no lo suficiente. En lugar de registrar el evento trace_printk() (y otros eventos ftrace, que no son eventos de seguimiento) mediante una llamada early_initcall(), regístrelos al mismo tiempo que trace_printk(). De esta forma, si se produce un fallo antes de la llamada early_initcall(), las llamadas trace_printk() serán realmente útiles."}], "references": [{"url": "https://git.kernel.org/stable/c/198c83963f6335ca6d690cff067679560f2a3a22", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3bb06eb6e9acf7c4a3e1b5bc87aed398ff8e2253", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76b2390fdc80c0a8300e5da5b6b62d201b6fe9ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b0af180514edea6c83dc9a299d9f383009c99f25", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b94d7c7654356860dd7719120c7d15ba38b6162a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de3930a4883ddad2244efd6d349013294c62c75c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f97eb0ab066133483a65c93eb894748de2f6b598", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
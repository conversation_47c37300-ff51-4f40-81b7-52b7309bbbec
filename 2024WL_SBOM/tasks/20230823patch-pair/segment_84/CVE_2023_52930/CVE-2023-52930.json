{"cve_id": "CVE-2023-52930", "published_date": "2025-03-27T17:15:42.477", "last_modified_date": "2025-04-15T16:01:17.677", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915: Fix potential bit_17 double-free\n\nA userspace with multiple threads racing I915_GEM_SET_TILING to set the\ntiling to I915_TILING_NONE could trigger a double free of the bit_17\nbitmask.  (Or conversely leak memory on the transition to tiled.)  Move\nallocation/free'ing of the bitmask within the section protected by the\nobj lock.\n\n[tursulin: Correct fixes tag and added cc stable.]\n(cherry picked from commit 10e0cbaaf1104f449d695c80bcacf930dcd3c42e)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915: Se corrige la posible doble liberación de bit_17. Un espacio de usuario con varios subprocesos que compiten con I915_GEM_SET_TILING para establecer el mosaico en I915_TILING_NONE podría provocar una doble liberación de la máscara de bits bit_17. (O, por el contrario, una fuga de memoria al cambiar a mosaico). Se ha trasladado la asignación/liberación de la máscara de bits dentro de la sección protegida por el bloqueo obj. [tursulin: Se corrige la etiqueta de correcciones y se ha añadido la versión estable de cc.] (Seleccionado del commit 10e0cbaaf1104f449d695c80bcacf930dcd3c42e)"}], "references": [{"url": "https://git.kernel.org/stable/c/0769f997a7b6d5cb8336db0b4ec3d2d311b8097c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7057a8f126f14f14b040faecfa220fd27c6c2f85", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b591abac78e25269b12e3d7170c99463f8c5cb02", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e3ebc3e23bd9028a8a9a26cbc5985f99be445f65", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53029", "published_date": "2025-03-27T17:15:52.627", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocteontx2-pf: Fix the use of GFP_KERNEL in atomic context on rt\n\nThe commit 4af1b64f80fb (\"octeontx2-pf: Fix lmtst ID used in aura\nfree\") uses the get/put_cpu() to protect the usage of percpu pointer\nin ->aura_freeptr() callback, but it also unnecessarily disable the\npreemption for the blockable memory allocation. The commit 87b93b678e95\n(\"octeontx2-pf: Avoid use of GFP_KERNEL in atomic context\") tried to\nfix these sleep inside atomic warnings. But it only fix the one for\nthe non-rt kernel. For the rt kernel, we still get the similar warnings\nlike below.\n  BUG: sleeping function called from invalid context at kernel/locking/spinlock_rt.c:46\n  in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 1, name: swapper/0\n  preempt_count: 1, expected: 0\n  RCU nest depth: 0, expected: 0\n  3 locks held by swapper/0/1:\n   #0: ffff800009fc5fe8 (rtnl_mutex){+.+.}-{3:3}, at: rtnl_lock+0x24/0x30\n   #1: ffff000100c276c0 (&mbox->lock){+.+.}-{3:3}, at: otx2_init_hw_resources+0x8c/0x3a4\n   #2: ffffffbfef6537e0 (&cpu_rcache->lock){+.+.}-{2:2}, at: alloc_iova_fast+0x1ac/0x2ac\n  Preemption disabled at:\n  [<ffff800008b1908c>] otx2_rq_aura_pool_init+0x14c/0x284\n  CPU: 20 PID: 1 Comm: swapper/0 Tainted: G        W          6.2.0-rc3-rt1-yocto-preempt-rt #1\n  Hardware name: Marvell OcteonTX CN96XX board (DT)\n  Call trace:\n   dump_backtrace.part.0+0xe8/0xf4\n   show_stack+0x20/0x30\n   dump_stack_lvl+0x9c/0xd8\n   dump_stack+0x18/0x34\n   __might_resched+0x188/0x224\n   rt_spin_lock+0x64/0x110\n   alloc_iova_fast+0x1ac/0x2ac\n   iommu_dma_alloc_iova+0xd4/0x110\n   __iommu_dma_map+0x80/0x144\n   iommu_dma_map_page+0xe8/0x260\n   dma_map_page_attrs+0xb4/0xc0\n   __otx2_alloc_rbuf+0x90/0x150\n   otx2_rq_aura_pool_init+0x1c8/0x284\n   otx2_init_hw_resources+0xe4/0x3a4\n   otx2_open+0xf0/0x610\n   __dev_open+0x104/0x224\n   __dev_change_flags+0x1e4/0x274\n   dev_change_flags+0x2c/0x7c\n   ic_open_devs+0x124/0x2f8\n   ip_auto_config+0x180/0x42c\n   do_one_initcall+0x90/0x4dc\n   do_basic_setup+0x10c/0x14c\n   kernel_init_freeable+0x10c/0x13c\n   kernel_init+0x2c/0x140\n   ret_from_fork+0x10/0x20\n\nOf course, we can shuffle the get/put_cpu() to only wrap the invocation\nof ->aura_freeptr() as what commit 87b93b678e95 does. But there are only\ntwo ->aura_freeptr() callbacks, otx2_aura_freeptr() and\ncn10k_aura_freeptr(). There is no usage of perpcu variable in the\notx2_aura_freeptr() at all, so the get/put_cpu() seems redundant to it.\nWe can move the get/put_cpu() into the corresponding callback which\nreally has the percpu variable usage and avoid the sprinkling of\nget/put_cpu() in several places."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: octeontx2-pf: Corrección del uso de GFP_KERNEL en contexto atómico en rt. El commit 4af1b64f80fb (\"octeontx2-pf: Corrección del ID de lmtst usado en aura free\") utiliza get/put_cpu() para proteger el uso del puntero percpu en la retrollamada -&gt;aura_freeptr(), pero también deshabilita innecesariamente la preempción para la asignación de memoria bloqueable. El commit 87b93b678e95 (\"octeontx2-pf: Evitar el uso de GFP_KERNEL en contexto atómico\") intentó corregir estas advertencias de suspensión dentro de contexto atómico. Sin embargo, solo corrigió la del kernel no rt. Para el kernel rt, aún recibimos advertencias similares a las que se muestran a continuación. ERROR: función inactiva llamada desde contexto no válido en kernel/locking/spinlock_rt.c:46 in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 1, name: swapper/0 preempt_count: 1, expected: 0 Profundidad de anidamiento de RCU: 0, expected: 0 3 bloqueos mantenidos por swapper/0/1: #0: ffff800009fc5fe8 (rtnl_mutex){+.+.}-{3:3}, at: rtnl_lock+0x24/0x30 #1: ffff000100c276c0 (&amp;mbox-&gt;lock){+.+.}-{3:3}, at: otx2_init_hw_resources+0x8c/0x3a4 #2: ffffffbfef6537e0 (&amp;cpu_rcache-&gt;lock){+.+.}-{2:2}, en: alloc_iova_fast+0x1ac/0x2ac Preempción deshabilitada en: [] otx2_rq_aura_pool_init+0x14c/0x284 CPU: 20 PID: 1 Comm: swapper/0 Contaminado: GW 6.2.0-rc3-rt1-yocto-preempt-rt #1 Nombre del hardware: Placa Marvell OcteonTX CN96XX (DT) Rastreo de llamadas: dump_backtrace.part.0+0xe8/0xf4 show_stack+0x20/0x30 dump_stack_lvl+0x9c/0xd8 dump_stack+0x18/0x34 __might_resched+0x188/0x224 rt_spin_lock+0x64/0x110 alloc_iova_fast+0x1ac/0x2ac iommu_dma_alloc_iova+0xd4/0x110 __iommu_dma_map+0x80/0x144 iommu_dma_map_page+0xe8/0x260 dma_map_page_attrs+0xb4/0xc0 __otx2_alloc_rbuf+0x90/0x150 otx2_rq_aura_pool_init+0x1c8/0x284 otx2_init_hw_resources+0xe4/0x3a4 otx2_open+0xf0/0x610 __dev_open+0x104/0x224 __dev_change_flags+0x1e4/0x274 dev_change_flags+0x2c/0x7c ic_open_devs+0x124/0x2f8 ip_auto_config+0x180/0x42c do_one_initcall+0x90/0x4dc do_basic_setup+0x10c/0x14c kernel_init_freeable+0x10c/0x13c kernel_init+0x2c/0x140 ret_from_fork+0x10/0x20 Por supuesto, podemos barajar el get/put_cpu() para solo envolver la invocación de -&gt;aura_freeptr() como lo hace el commit 87b93b678e95. Pero solo hay dos devoluciones de llamada -&gt;aura_freeptr(): otx2_aura_freeptr() y cn10k_aura_freeptr(). La variable perpcu no se usa en absoluto en otx2_aura_freeptr(), por lo que get/put_cpu() parece redundante. Podemos mover get/put_cpu() a la devolución de llamada correspondiente que sí usa la variable percpu y evitar así la dispersión de get/put_cpu() en varios lugares."}], "references": [{"url": "https://git.kernel.org/stable/c/29e9c67bf3271067735c188e95cf3631ecd64d58", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/55ba18dc62deff5910c0fa64486dea1ff20832ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/659518e013d6bd562bb0f1d2d9f99d0ac54720e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
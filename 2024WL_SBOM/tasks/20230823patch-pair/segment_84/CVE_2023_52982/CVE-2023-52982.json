{"cve_id": "CVE-2023-52982", "published_date": "2025-03-27T17:15:45.437", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfscache: Use wait_on_bit() to wait for the freeing of relinquished volume\n\nThe freeing of relinquished volume will wake up the pending volume\nacquisition by using wake_up_bit(), however it is mismatched with\nwait_var_event() used in fscache_wait_on_volume_collision() and it will\nnever wake up the waiter in the wait-queue because these two functions\noperate on different wait-queues.\n\nAccording to the implementation in fscache_wait_on_volume_collision(),\nif the wake-up of pending acquisition is delayed longer than 20 seconds\n(e.g., due to the delay of on-demand fd closing), the first\nwait_var_event_timeout() will timeout and the following wait_var_event()\nwill hang forever as shown below:\n\n FS-Cache: Potential volume collision new=00000024 old=00000022\n ......\n INFO: task mount:1148 blocked for more than 122 seconds.\n       Not tainted 6.1.0-rc6+ #1\n task:mount           state:D stack:0     pid:1148  ppid:1\n Call Trace:\n  <TASK>\n  __schedule+0x2f6/0xb80\n  schedule+0x67/0xe0\n  fscache_wait_on_volume_collision.cold+0x80/0x82\n  __fscache_acquire_volume+0x40d/0x4e0\n  erofs_fscache_register_volume+0x51/0xe0 [erofs]\n  erofs_fscache_register_fs+0x19c/0x240 [erofs]\n  erofs_fc_fill_super+0x746/0xaf0 [erofs]\n  vfs_get_super+0x7d/0x100\n  get_tree_nodev+0x16/0x20\n  erofs_fc_get_tree+0x20/0x30 [erofs]\n  vfs_get_tree+0x24/0xb0\n  path_mount+0x2fa/0xa90\n  do_mount+0x7c/0xa0\n  __x64_sys_mount+0x8b/0xe0\n  do_syscall_64+0x30/0x60\n  entry_SYSCALL_64_after_hwframe+0x46/0xb0\n\nConsidering that wake_up_bit() is more selective, so fix it by using\nwait_on_bit() instead of wait_var_event() to wait for the freeing of\nrelinquished volume. In addition because waitqueue_active() is used in\nwake_up_bit() and clear_bit() doesn't imply any memory barrier, use\nclear_and_wake_up_bit() to add the missing memory barrier between\ncursor->flags and waitqueue_active()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: fscache: utilice wait_on_bit() para esperar la liberación del volumen cedido. La liberación del volumen cedido activará la adquisición de volumen pendiente mediante wake_up_bit(), sin embargo no coincide con wait_var_event() utilizado en fscache_wait_on_volume_collision() y nunca activará al que espera en la cola de espera porque estas dos funciones operan en colas de espera diferentes. Según la implementación en fscache_wait_on_volume_collision(), si la activación de una adquisición pendiente se demora más de 20 segundos (por ejemplo, debido a la demora en el cierre del fd a pedido), el primer wait_var_event_timeout() expirará y el siguiente wait_var_event() se colgará para siempre como se muestra a continuación: FS-Cache: Potencial colisión de volumen nuevo=00000024 antiguo=00000022 ...... INFORMACIÓN: montaje de tarea: 1148 bloqueado durante más de 122 segundos. No contaminado 6.1.0-rc6+ #1 task:mount state:D stack:0 pid:1148 ppid:1 Call Trace:  __schedule+0x2f6/0xb80 schedule+0x67/0xe0 fscache_wait_on_volume_collision.cold+0x80/0x82 __fscache_acquire_volume+0x40d/0x4e0 erofs_fscache_register_volume+0x51/0xe0 [erofs] erofs_fscache_register_fs+0x19c/0x240 [erofs] erofs_fc_fill_super+0x746/0xaf0 [erofs] vfs_get_super+0x7d/0x100 get_tree_nodev+0x16/0x20 erofs_fc_get_tree+0x20/0x30 [erofs] vfs_get_tree+0x24/0xb0 path_mount+0x2fa/0xa90 do_mount+0x7c/0xa0 __x64_sys_mount+0x8b/0xe0 do_syscall_64+0x30/0x60 entry_SYSCALL_64_after_hwframe+0x46/0xb0 Considering that wake_up_bit() es más selectivo, corríjalo utilizando wait_on_bit() en lugar de wait_var_event() para esperar la liberación del volumen cedido. Además, debido a que waitqueue_active() se usa en wake_up_bit() y clear_bit() no implica ninguna barrera de memoria, use clear_and_wake_up_bit() para agregar la barrera de memoria faltante entre cursor-&gt;flags y waitqueue_active()."}], "references": [{"url": "https://git.kernel.org/stable/c/3be069f42a7b79d3149194f21cdf24bf23864cac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8226e37d82f43657da34dd770e2b38f20242ada7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
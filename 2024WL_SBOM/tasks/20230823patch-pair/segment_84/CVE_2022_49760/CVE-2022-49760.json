{"cve_id": "CVE-2022-49760", "published_date": "2025-03-27T17:15:41.280", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/hugetlb: fix PTE marker handling in hugetlb_change_protection()\n\nPatch series \"mm/hugetlb: uffd-wp fixes for hugetlb_change_protection()\".\n\nPlaying with virtio-mem and background snapshots (using uffd-wp) on\nhugetlb in QEMU, I managed to trigger a VM_BUG_ON().  Looking into the\ndetails, hugetlb_change_protection() seems to not handle uffd-wp correctly\nin all cases.\n\nPatch #1 fixes my test case.  I don't have reproducers for patch #2, as it\nrequires running into migration entries.\n\nI did not yet check in detail yet if !hugetlb code requires similar care.\n\n\nThis patch (of 2):\n\nThere are two problematic cases when stumbling over a PTE marker in\nhugetlb_change_protection():\n\n(1) We protect an uffd-wp PTE marker a second time using uffd-wp: we will\n    end up in the \"!huge_pte_none(pte)\" case and mess up the PTE marker.\n\n(2) We unprotect a uffd-wp PTE marker: we will similarly end up in the\n    \"!huge_pte_none(pte)\" case even though we cleared the PTE, because\n    the \"pte\" variable is stale. We'll mess up the PTE marker.\n\nFor example, if we later stumble over such a \"wrongly modified\" PTE marker,\nwe'll treat it like a present PTE that maps some garbage page.\n\nThis can, for example, be triggered by mapping a memfd backed by huge\npages, registering uffd-wp, uffd-wp'ing an unmapped page and (a)\nuffd-wp'ing it a second time; or (b) uffd-unprotecting it; or (c)\nunregistering uffd-wp. Then, ff we trigger fallocate(FALLOC_FL_PUNCH_HOLE)\non that file range, we will run into a VM_BUG_ON:\n\n[  195.039560] page:00000000ba1f2987 refcount:1 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x0\n[  195.039565] flags: 0x7ffffc0001000(reserved|node=0|zone=0|lastcpupid=0x1fffff)\n[  195.039568] raw: 0007ffffc0001000 ffffe742c0000008 ffffe742c0000008 0000000000000000\n[  195.039569] raw: 0000000000000000 0000000000000000 00000001ffffffff 0000000000000000\n[  195.039569] page dumped because: VM_BUG_ON_PAGE(compound && !PageHead(page))\n[  195.039573] ------------[ cut here ]------------\n[  195.039574] kernel BUG at mm/rmap.c:1346!\n[  195.039579] invalid opcode: 0000 [#1] PREEMPT SMP NOPTI\n[  195.039581] CPU: 7 PID: 4777 Comm: qemu-system-x86 Not tainted 6.0.12-200.fc36.x86_64 #1\n[  195.039583] Hardware name: LENOVO 20WNS1F81N/20WNS1F81N, BIOS N35ET50W (1.50 ) 09/15/2022\n[  195.039584] RIP: 0010:page_remove_rmap+0x45b/0x550\n[  195.039588] Code: [...]\n[  195.039589] RSP: 0018:ffffbc03c3633ba8 EFLAGS: 00010292\n[  195.039591] RAX: 0000000000000040 RBX: ffffe742c0000000 RCX: 0000000000000000\n[  195.039592] RDX: 0000000000000002 RSI: ffffffff8e7aac1a RDI: 00000000ffffffff\n[  195.039592] RBP: 0000000000000001 R08: 0000000000000000 R09: ffffbc03c3633a08\n[  195.039593] R10: 0000000000000003 R11: ffffffff8f146328 R12: ffff9b04c42754b0\n[  195.039594] R13: ffffffff8fcc6328 R14: ffffbc03c3633c80 R15: ffff9b0484ab9100\n[  195.039595] FS:  00007fc7aaf68640(0000) GS:ffff9b0bbf7c0000(0000) knlGS:0000000000000000\n[  195.039596] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[  195.039597] CR2: 000055d402c49110 CR3: 0000000159392003 CR4: 0000000000772ee0\n[  195.039598] PKRU: 55555554\n[  195.039599] Call Trace:\n[  195.039600]  <TASK>\n[  195.039602]  __unmap_hugepage_range+0x33b/0x7d0\n[  195.039605]  unmap_hugepage_range+0x55/0x70\n[  195.039608]  hugetlb_vmdelete_list+0x77/0xa0\n[  195.039611]  hugetlbfs_fallocate+0x410/0x550\n[  195.039612]  ? _raw_spin_unlock_irqrestore+0x23/0x40\n[  195.039616]  vfs_fallocate+0x12e/0x360\n[  195.039618]  __x64_sys_fallocate+0x40/0x70\n[  195.039620]  do_syscall_64+0x58/0x80\n[  195.039623]  ? syscall_exit_to_user_mode+0x17/0x40\n[  195.039624]  ? do_syscall_64+0x67/0x80\n[  195.039626]  entry_SYSCALL_64_after_hwframe+0x63/0xcd\n[  195.039628] RIP: 0033:0x7fc7b590651f\n[  195.039653] Code: [...]\n[  195.039654] RSP: 002b:00007fc7aaf66e70 EFLAGS: 00000293 ORIG_RAX: 000000000000011d\n[  195.039655] RAX: ffffffffffffffda RBX: 0000558ef4b7f370 RCX: 00007fc7b590651f\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/hugetlb: corrección de la gestión de marcadores PTE en hugetlb_change_protection(). Serie de parches \"mm/hugetlb: corrección de uffd-wp para hugetlb_change_protection()\". Al experimentar con virtio-mem e instantáneas en segundo plano (usando uffd-wp) en hugetlb en QEMU, logré activar un error VM_BUG_ON(). Al analizar los detalles, hugetlb_change_protection() parece no manejar uffd-wp correctamente en todos los casos. El parche n.° 1 corrige mi caso de prueba. No tengo reproductores para el parche n.° 2, ya que requiere la ejecución de entradas de migración. Aún no he comprobado en detalle si el código de !hugetlb requiere un cuidado similar. Este parche (de 2): Hay dos casos problemáticos al encontrar un marcador PTE en hugetlb_change_protection(): (1) Protegemos un marcador PTE uffd-wp por segunda vez usando uffd-wp: terminaremos en el caso \"!huge_pte_none(pte)\" y dañaremos el marcador PTE. (2) Desprotegemos un marcador PTE uffd-wp: terminaremos en el caso \"!huge_pte_none(pte)\" aunque hayamos borrado el PTE, porque la variable \"pte\" está obsoleta. Dañaremos el marcador PTE. Por ejemplo, si posteriormente encontramos un marcador PTE \"modificado incorrectamente\", lo trataremos como un PTE existente que asigna una página basura. Esto se puede activar, por ejemplo, al mapear un memfd respaldado por páginas grandes, registrar uffd-wp, hacer uffd-wp en una página no mapeada y (a) hacer uffd-wp una segunda vez; o (b) desprotegerla con uffd; o (c) anular el registro de uffd-wp. Luego, si activamos fallocate(FALLOC_FL_PUNCH_HOLE) en ese rango de archivos, nos encontraremos con un VM_BUG_ON: [ 195.039560] page:00000000ba1f2987 refcount:1 mapcount:0 mapping:000000000000000 index:0x0 pfn:0x0 [ 195.039565] flags: 0x7ffffc0001000(reserved|node=0|zone=0|lastcpupid=0x1fffff) [ 195.039568] raw: 0007ffffc0001000 ffffe742c0000008 ffffe742c0000008 000000000000000 [ 195.039569] raw: 0000000000000000 000000000000000 0000000000000 00000001ffffffff 0000000000000000 [ 195.039569] página volcada porque: VM_BUG_ON_PAGE(compound &amp;&amp; !PageHead(page)) [ 195.039573] ------------[ cortar aquí ]------------ [ 195.039574] ¡ERROR del kernel en mm/rmap.c:1346! [ 195.039579] Código de operación no válido: 0000 [#1] PREEMPT SMP NOPTI [ 195.039581] CPU: 7 PID: 4777 Comm: qemu-system-x86 No contaminado 6.0.12-200.fc36.x86_64 #1 [ 195.039583] Nombre del hardware: LENOVO 20WNS1F81N/20WNS1F81N, BIOS N35ET50W (1.50) 15/09/2022 [ 195.039584] RIP: 0010:page_remove_rmap+0x45b/0x550 [ 195.039588] Código: [...] [ 195.039589] RSP: 0018:ffffbc03c3633ba8 EFLAGS: 00010292 [ 195.039591] RAX: 0000000000000040 RBX: ffffe742c0000000 RCX: 0000000000000000 [ 195.039592] RDX: 0000000000000002 RSI: ffffffff8e7aac1a RDI: 00000000ffffffff [ 195.039592] RBP: 00000000000000001 R08: 0000000000000000 R09: ffffbc03c3633a08 [ 195.039593] R10: 0000000000000003 R11: ffffffff8f146328 R12: ffff9b04c42754b0 [ 195.039594] R13: ffffffff8fcc6328 R14: ffffbc03c3633c80 R15: ffff9b0484ab9100 [ 195.039595] FS: 00007fc7aaf68640(0000) GS:ffff9b0bbf7c0000(0000) knlGS:0000000000000000 [ 195.039596] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [ 195.039597] CR2: 000055d402c49110 CR3: 0000000159392003 CR4: 0000000000772ee0 [ 195.039598] PKRU: 55555554 [ 195.039599] Rastreo de llamadas: [ 195.039600]  [ 195.039602] __unmap_hugepage_range+0x33b/0x7d0 [ 195.039605] unmap_hugepage_range+0x55/0x70 [ 195.039608] hugetlb_vmdelete_list+0x77/0xa0 [ 195.039611] hugetlbfs_fallocate+0x410/0x550 [ 195.039612] ? _raw_spin_unlock_irqrestore+0x23/0x40 [ 195.039616] vfs_fallocate+0x12e/0x360 [ 195.039618] __x64_sys_fallocate+0x40/0x70 [ 195.039620] hacer_syscall_64+0x58/0x80 [ 195.039623] ? syscall_exit_to_user_mode+0x17/0x40 [ 195.039624] ? do_syscall_64+0x67/0x80 [ 195.039626] entry_SYSCALL_64_after_hwframe+0x63/0xcd [ 195.039628] RIP: 0033:0x7fc7b590651f [ 195.039653] Código: [...] [ 195.039654] RSP: 002b:00007fc7aaf66e70 EFLAGS: 00000293 ORIG_RAX: 000000000000011d [ 195.039655] RAX: ffffffffffffffda ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/0e678153f5be7e6c8d28835f5a678618da4b7a9c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6062c992e912df1eedad52cf64efb3d48e8d35c5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
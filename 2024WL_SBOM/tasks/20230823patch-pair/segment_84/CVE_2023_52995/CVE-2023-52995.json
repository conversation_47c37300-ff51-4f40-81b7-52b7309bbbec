{"cve_id": "CVE-2023-52995", "published_date": "2025-03-27T17:15:48.153", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv/kprobe: Fix instruction simulation of JALR\n\nSet kprobe at 'jalr 1140(ra)' of vfs_write results in the following\ncrash:\n\n[   32.092235] Unable to handle kernel access to user memory without uaccess routines at virtual address 00aaaaaad77b1170\n[   32.093115] Oops [#1]\n[   32.093251] Modules linked in:\n[   32.093626] CPU: 0 PID: 135 Comm: ftracetest Not tainted 6.2.0-rc2-00013-gb0aa5e5df0cb-dirty #16\n[   32.093985] Hardware name: riscv-virtio,qemu (DT)\n[   32.094280] epc : ksys_read+0x88/0xd6\n[   32.094855]  ra : ksys_read+0xc0/0xd6\n[   32.095016] epc : ffffffff801cda80 ra : ffffffff801cdab8 sp : ff20000000d7bdc0\n[   32.095227]  gp : ffffffff80f14000 tp : ff60000080f9cb40 t0 : ffffffff80f13e80\n[   32.095500]  t1 : ffffffff8000c29c t2 : ffffffff800dbc54 s0 : ff20000000d7be60\n[   32.095716]  s1 : 0000000000000000 a0 : ffffffff805a64ae a1 : ffffffff80a83708\n[   32.095921]  a2 : ffffffff80f160a0 a3 : 0000000000000000 a4 : f229b0afdb165300\n[   32.096171]  a5 : f229b0afdb165300 a6 : ffffffff80eeebd0 a7 : 00000000000003ff\n[   32.096411]  s2 : ff6000007ff76800 s3 : fffffffffffffff7 s4 : 00aaaaaad77b1170\n[   32.096638]  s5 : ffffffff80f160a0 s6 : ff6000007ff76800 s7 : 0000000000000030\n[   32.096865]  s8 : 00ffffffc3d97be0 s9 : 0000000000000007 s10: 00aaaaaad77c9410\n[   32.097092]  s11: 0000000000000000 t3 : ffffffff80f13e48 t4 : ffffffff8000c29c\n[   32.097317]  t5 : ffffffff8000c29c t6 : ffffffff800dbc54\n[   32.097505] status: 0000000200000120 badaddr: 00aaaaaad77b1170 cause: 000000000000000d\n[   32.098011] [<ffffffff801cdb72>] ksys_write+0x6c/0xd6\n[   32.098222] [<ffffffff801cdc06>] sys_write+0x2a/0x38\n[   32.098405] [<ffffffff80003c76>] ret_from_syscall+0x0/0x2\n\nSince the rs1 and rd might be the same one, such as 'jalr 1140(ra)',\nhence it requires obtaining the target address from rs1 followed by\nupdating rd.\n\n[Palmer: Pick Guo's cleanup]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv/kprobe: Se corrige la simulación de instrucción de JALR. Establecer kprobe en 'jalr 1140(ra)' de vfs_write da como resultado el siguiente fallo: [32.092235] No se puede controlar el acceso del kernel a la memoria del usuario sin rutinas uaccess en la dirección virtual 00aaaaaad77b1170 [32.093115] Ups [#1] [32.093251] Módulos vinculados en: [32.093626] CPU: 0 PID: 135 Comm: ftracetest No contaminado 6.2.0-rc2-00013-gb0aa5e5df0cb-dirty #16 [32.093985] Nombre del hardware: riscv-virtio,qemu (DT) [ [32.094280] epc : ksys_read+0x88/0xd6 [ 32.094855] ra : ksys_read+0xc0/0xd6 [ 32.095016] epc : ffffffff801cda80 ra : ffffffff801cdab8 sp : ff20000000d7bdc0 [ 32.095227] gp : ffffffff80f14000 tp : ff60000080f9cb40 t0 : ffffffff80f13e80 [ 32.095500] t1 : ffffffff8000c29c t2 : ffffffff800dbc54 s0 : ff20000000d7be60 [32.095716] s1: 0000000000000000 a0: ffffffff805a64ae a1: ffffffff80a83708 [32.095921] a2: ffffffff80f160a0 a3: 0000000000000000 a4: f229b0afdb165300 [32.096171] a5: f229b0afdb165300 a6: ffffffff80eeebd0 a7: 00000000000003ff [32.096411] s2: ff6000007ff76800 s3: ffffffffffffff7 s4: 00aaaaaad77b1170 [32.096638] s5: ffffffff80f160a0 s6: ff6000007ff76800 s7: 00000000000000030 [32.096865] s8: 00ffffffc3d97be0 s9: 0000000000000007 s10: 00aaaaaad77c9410 [32.097092] s11: 0000000000000000 t3: ffffffff80f13e48 t4 : ffffffff8000c29c [ 32.097317] t5 : ffffffff8000c29c t6 : ffffffff800dbc54 [ 32.097505] estado: 0000000200000120 dirección incorrecta: 00aaaaaad77b1170 causa: 000000000000000d [ 32.098011] [] ksys_write+0x6c/0xd6 [ 32.098222] [] sys_write+0x2a/0x38 [ 32.098405] [] ret_from_syscall+0x0/0x2 Dado que rs1 y rd podrían ser el mismo, como 'jalr 1140(ra)', se requiere obtener la dirección de destino de rs1 y luego actualizar rd. [Palmer: Limpieza de Pick Guo]"}], "references": [{"url": "https://git.kernel.org/stable/c/614471b7f7cd28a2c96ab9c90b37471c82258ffb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ca0254998be4d74cf6add70ccfab0d2dbd362a10", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f4c8fc775fcbc9e9047b22671c55ca18f9a127d4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52991", "published_date": "2025-03-27T17:15:46.540", "last_modified_date": "2025-04-15T14:32:09.260", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: fix NULL pointer in skb_segment_list\n\nCommit 3a1296a38d0c (\"net: Support GRO/GSO fraglist chaining.\")\nintroduced UDP listifyed GRO. The segmentation relies on frag_list being\nuntouched when passing through the network stack. This assumption can be\nbroken sometimes, where frag_list itself gets pulled into linear area,\nleaving frag_list being NULL. When this happens it can trigger\nfollowing NULL pointer dereference, and panic the kernel. Reverse the\ntest condition should fix it.\n\n[19185.577801][    C1] BUG: kernel NULL pointer dereference, address:\n...\n[19185.663775][    C1] RIP: 0010:skb_segment_list+0x1cc/0x390\n...\n[19185.834644][    C1] Call Trace:\n[19185.841730][    C1]  <TASK>\n[19185.848563][    C1]  __udp_gso_segment+0x33e/0x510\n[19185.857370][    C1]  inet_gso_segment+0x15b/0x3e0\n[19185.866059][    C1]  skb_mac_gso_segment+0x97/0x110\n[19185.874939][    C1]  __skb_gso_segment+0xb2/0x160\n[19185.883646][    C1]  udp_queue_rcv_skb+0xc3/0x1d0\n[19185.892319][    C1]  udp_unicast_rcv_skb+0x75/0x90\n[19185.900979][    C1]  ip_protocol_deliver_rcu+0xd2/0x200\n[19185.910003][    C1]  ip_local_deliver_finish+0x44/0x60\n[19185.918757][    C1]  __netif_receive_skb_one_core+0x8b/0xa0\n[19185.927834][    C1]  process_backlog+0x88/0x130\n[19185.935840][    C1]  __napi_poll+0x27/0x150\n[19185.943447][    C1]  net_rx_action+0x27e/0x5f0\n[19185.951331][    C1]  ? mlx5_cq_tasklet_cb+0x70/0x160 [mlx5_core]\n[19185.960848][    C1]  __do_softirq+0xbc/0x25d\n[19185.968607][    C1]  irq_exit_rcu+0x83/0xb0\n[19185.976247][    C1]  common_interrupt+0x43/0xa0\n[19185.984235][    C1]  asm_common_interrupt+0x22/0x40\n...\n[19186.094106][    C1]  </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: corrección de puntero nulo en skb_segment_list. El commit 3a1296a38d0c (\"net: Compatibilidad con encadenamiento de fraglist GRO/GSO\") introdujo GRO con lista UDP. La segmentación depende de que frag_list permanezca intacto al pasar por la pila de red. Esta suposición puede fallar a veces, ya que frag_list se arrastra al área lineal, dejando frag_list como nulo. Cuando esto sucede, puede desencadenar la consiguiente desreferencia de puntero nulo y generar un pánico en el kernel. Revertir la condición de prueba debería solucionarlo. [19185.577801][ C1] ERROR: desreferencia de puntero NULL del núcleo, dirección: ... [19185.663775][ C1] RIP: 0010:skb_segment_list+0x1cc/0x390 ... [19185.834644][ C1] Rastre<PERSON> de ll<PERSON>: [19185.841730][ C1]  [19185.848563][ C1] __udp_gso_segment+0x33e/0x510 [19185.857370][ C1] inet_gso_segment+0x15b/0x3e0 [19185.866059][ C1] skb_mac_gso_segment+0x97/0x110 [19185.874939][ C1] __skb_gso_segment+0xb2/0x160 [19185.883646][ C1] udp_queue_rcv_skb+0xc3/0x1d0 [19185.892319][ C1] udp_unicast_rcv_skb+0x75/0x90 [19185.900979][ C1] ip_protocol_deliver_rcu+0xd2/0x200 [19185.910003][ C1] ip_local_deliver_finish+0x44/0x60 [19185.918757][ C1] __netif_receive_skb_one_core+0x8b/0xa0 [19185.927834][ C1] registro_de_proceso+0x88/0x130 [19185.935840][ C1] __napi_poll+0x27/0x150 [19185.943447][ C1] acción_de_rx_net+0x27e/0x5f0 [19185.951331][ C1] ? mlx5_cq_tasklet_cb+0x70/0x160 [mlx5_core] [19185.960848][ C1] __do_softirq+0xbc/0x25d [19185.968607][ C1] irq_exit_rcu+0x83/0xb0 [19185.976247][ C1] common_interrupt+0x43/0xa0 [19185.984235][ C1] asm_common_interrupt+0x22/0x40 ... [19186.094106][ C1] "}], "references": [{"url": "https://git.kernel.org/stable/c/046de74f9af92ae9ffce75fa22a1795223f4fb54", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6446369fb9f083ce032448c5047da08e298b22e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/876e8ca8366735a604bac86ff7e2732fc9d85d2d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/888dad6f3e85e3b2f8389bd6478f181efc72534d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
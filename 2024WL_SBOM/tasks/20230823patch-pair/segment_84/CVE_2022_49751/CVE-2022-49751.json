{"cve_id": "CVE-2022-49751", "published_date": "2025-03-27T17:15:39.970", "last_modified_date": "2025-04-14T20:16:24.670", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nw1: fix WARNING after calling w1_process()\n\nI got the following WARNING message while removing driver(ds2482):\n\n------------[ cut here ]------------\ndo not call blocking ops when !TASK_RUNNING; state=1 set at [<000000002d50bfb6>] w1_process+0x9e/0x1d0 [wire]\nWARNING: CPU: 0 PID: 262 at kernel/sched/core.c:9817 __might_sleep+0x98/0xa0\nCPU: 0 PID: 262 Comm: w1_bus_master1 Tainted: G                 N 6.1.0-rc3+ #307\nRIP: 0010:__might_sleep+0x98/0xa0\nCall Trace:\n exit_signals+0x6c/0x550\n do_exit+0x2b4/0x17e0\n kthread_exit+0x52/0x60\n kthread+0x16d/0x1e0\n ret_from_fork+0x1f/0x30\n\nThe state of task is set to TASK_INTERRUPTIBLE in loop in w1_process(),\nset it to TASK_RUNNING when it breaks out of the loop to avoid the\nwarning."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: w1: corregir ADVERTENCIA después de llamar a w1_process() Recibí el siguiente mensaje de ADVERTENCIA al eliminar el controlador (ds2482): ------------[ cortar aquí ]------------ no llamar a operaciones de bloqueo cuando !TASK_RUNNING; estado=1 establecido en [&lt;000000002d50bfb6&gt;] w1_process+0x9e/0x1d0 [wire] ADVERTENCIA: CPU: 0 PID: 262 en kernel/sched/core.c:9817 __might_sleep+0x98/0xa0 CPU: 0 PID: 262 Comm: w1_bus_master1 Contaminado: GN 6.1.0-rc3+ #307 RIP: 0010:__might_sleep+0x98/0xa0 Rastreo de llamadas: exit_signals+0x6c/0x550 do_exit+0x2b4/0x17e0 kthread_exit+0x52/0x60 kthread+0x16d/0x1e0 ret_from_fork+0x1f/0x30 El estado de la tarea está establecido en TASK_INTERRUPTIBLE en bucle en w1_process(), configúrelo en TASK_RUNNING cuando salga del bucle para evitar la advertencia."}], "references": [{"url": "https://git.kernel.org/stable/c/190b5c3bbd5df685bb1063bda048831d72b8f1d4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/216f35db6ec6a667cd9db4838d657c1d2f4684da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/276052159ba94d4d9f5b453fb4707d6798c6b845", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/36225a7c72e9e3e1ce4001b6ce72849f5c9a2d3b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/89c62cee5d4d65ac75d99b5f986f7f94290e888f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bccd6df4c177b1ad766f16565ccc298653d027d0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cfc7462ff824ed6718ed0272ee9aae88e20d469a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53031", "published_date": "2025-03-27T17:15:52.870", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\npowerpc/imc-pmu: Fix use of mutex in IRQs disabled section\n\nCurrent imc-pmu code triggers a WARNING with CONFIG_DEBUG_ATOMIC_SLEEP\nand CONFIG_PROVE_LOCKING enabled, while running a thread_imc event.\n\nCommand to trigger the warning:\n  # perf stat -e thread_imc/CPM_CS_FROM_L4_MEM_X_DPTEG/ sleep 5\n\n   Performance counter stats for 'sleep 5':\n\n                   0      thread_imc/CPM_CS_FROM_L4_MEM_X_DPTEG/\n\n         5.002117947 seconds time elapsed\n\n         0.000131000 seconds user\n         0.001063000 seconds sys\n\nBelow is snippet of the warning in dmesg:\n\n  BUG: sleeping function called from invalid context at kernel/locking/mutex.c:580\n  in_atomic(): 1, irqs_disabled(): 1, non_block: 0, pid: 2869, name: perf-exec\n  preempt_count: 2, expected: 0\n  4 locks held by perf-exec/2869:\n   #0: c00000004325c540 (&sig->cred_guard_mutex){+.+.}-{3:3}, at: bprm_execve+0x64/0xa90\n   #1: c00000004325c5d8 (&sig->exec_update_lock){++++}-{3:3}, at: begin_new_exec+0x460/0xef0\n   #2: c0000003fa99d4e0 (&cpuctx_lock){-...}-{2:2}, at: perf_event_exec+0x290/0x510\n   #3: c000000017ab8418 (&ctx->lock){....}-{2:2}, at: perf_event_exec+0x29c/0x510\n  irq event stamp: 4806\n  hardirqs last  enabled at (4805): [<c000000000f65b94>] _raw_spin_unlock_irqrestore+0x94/0xd0\n  hardirqs last disabled at (4806): [<c0000000003fae44>] perf_event_exec+0x394/0x510\n  softirqs last  enabled at (0): [<c00000000013c404>] copy_process+0xc34/0x1ff0\n  softirqs last disabled at (0): [<0000000000000000>] 0x0\n  CPU: 36 PID: 2869 Comm: perf-exec Not tainted 6.2.0-rc2-00011-g1247637727f2 #61\n  Hardware name: 8375-42A POWER9 0x4e1202 opal:v7.0-16-g9b85f7d961 PowerNV\n  Call Trace:\n    dump_stack_lvl+0x98/0xe0 (unreliable)\n    __might_resched+0x2f8/0x310\n    __mutex_lock+0x6c/0x13f0\n    thread_imc_event_add+0xf4/0x1b0\n    event_sched_in+0xe0/0x210\n    merge_sched_in+0x1f0/0x600\n    visit_groups_merge.isra.92.constprop.166+0x2bc/0x6c0\n    ctx_flexible_sched_in+0xcc/0x140\n    ctx_sched_in+0x20c/0x2a0\n    ctx_resched+0x104/0x1c0\n    perf_event_exec+0x340/0x510\n    begin_new_exec+0x730/0xef0\n    load_elf_binary+0x3f8/0x1e10\n  ...\n  do not call blocking ops when !TASK_RUNNING; state=2001 set at [<00000000fd63e7cf>] do_nanosleep+0x60/0x1a0\n  WARNING: CPU: 36 PID: 2869 at kernel/sched/core.c:9912 __might_sleep+0x9c/0xb0\n  CPU: 36 PID: 2869 Comm: sleep Tainted: G        W          6.2.0-rc2-00011-g1247637727f2 #61\n  Hardware name: 8375-42A POWER9 0x4e1202 opal:v7.0-16-g9b85f7d961 PowerNV\n  NIP:  c000000000194a1c LR: c000000000194a18 CTR: c000000000a78670\n  REGS: c00000004d2134e0 TRAP: 0700   Tainted: G        W           (6.2.0-rc2-00011-g1247637727f2)\n  MSR:  9000000000021033 <SF,HV,ME,IR,DR,RI,LE>  CR: 48002824  XER: 00000000\n  CFAR: c00000000013fb64 IRQMASK: 1\n\nThe above warning triggered because the current imc-pmu code uses mutex\nlock in interrupt disabled sections. The function mutex_lock()\ninternally calls __might_resched(), which will check if IRQs are\ndisabled and in case IRQs are disabled, it will trigger the warning.\n\nFix the issue by changing the mutex lock to spinlock.\n\n[mpe: Fix comments, trim oops in change log, add reported-by tags]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: powerpc/imc-pmu: Se corrige el uso de mutex en la sección deshabilitada de IRQ. El código imc-pmu actual activa una ADVERTENCIA con CONFIG_DEBUG_ATOMIC_SLEEP y CONFIG_PROVE_LOCKING habilitados, mientras se ejecuta un evento thread_imc. Comando para activar la advertencia: # perf stat -e thread_imc/CPM_CS_FROM_L4_MEM_X_DPTEG/ sleep 5 Estadísticas del contador de rendimiento para 'sleep 5': 0 thread_imc/CPM_CS_FROM_L4_MEM_X_DPTEG/ 5.002117947 segundos tiempo transcurrido 0.000131000 segundos usuario 0.001063000 segundos sys A continuación se muestra un fragmento de la advertencia en dmesg: ERROR: función de suspensión llamada desde un contexto no válido en kernel/locking/mutex.c:580 in_atomic(): 1, irqs_disabled(): 1, non_block: 0, pid: 2869, name: perf-exec preempt_count: 2, expected: 0 4 bloqueos mantenidos por perf-exec/2869: #0: c00000004325c540 (&amp;sig-&gt;cred_guard_mutex){+.+.}-{3:3}, en: bprm_execve+0x64/0xa90 #1: c00000004325c5d8 (&amp;sig-&gt;exec_update_lock){++++}-{3:3}, en: begin_new_exec+0x460/0xef0 #2: c0000003fa99d4e0 (&amp;cpuctx_lock){-...}-{2:2}, en: perf_event_exec+0x290/0x510 #3: c000000017ab8418 (&amp;ctx-&gt;lock){....}-{2:2}, en: perf_event_exec+0x29c/0x510 marca de evento de irq: 4806 hardirqs habilitados por última vez en (4805): [] _raw_spin_unlock_irqrestore+0x94/0xd0 hardirqs deshabilitados por última vez en (4806): [] perf_event_exec+0x394/0x510 softirqs habilitados por última vez en (0): [] copy_process+0xc34/0x1ff0 softirqs deshabilitados por última vez en (0): [&lt;000000000000000&gt;] 0x0 CPU: 36 PID: 2869 Comm: perf-exec No contaminado 6.2.0-rc2-00011-g1247637727f2 #61 Nombre del hardware: 8375-42A POWER9 0x4e1202 opal:v7.0-16-g9b85f7d961 Rastreo de llamadas de PowerNV: dump_stack_lvl+0x98/0xe0 (no confiable) __might_resched+0x2f8/0x310 __mutex_lock+0x6c/0x13f0 thread_imc_event_add+0xf4/0x1b0 event_sched_in+0xe0/0x210 merge_sched_in+0x1f0/0x600 visit_groups_merge.isra.92.constprop.166+0x2bc/0x6c0 ctx_flexible_sched_in+0xcc/0x140 ctx_sched_in+0x20c/0x2a0 ctx_resched+0x104/0x1c0 perf_event_exec+0x340/0x510 begin_new_exec+0x730/0xef0 load_elf_binary+0x3f8/0x1e10 ... no llamar a operaciones de bloqueo cuando !TASK_RUNNING; estado=2001 establecido en [&lt;00000000fd63e7cf&gt;] do_nanosleep+0x60/0x1a0 ADVERTENCIA: CPU: 36 PID: 2869 en kernel/sched/core.c:9912 __might_sleep+0x9c/0xb0 CPU: 36 PID: 2869 Comm: sleep Contaminado: GW 6.2.0-rc2-00011-g1247637727f2 #61 Nombre del hardware: 8375-42A POWER9 0x4e1202 opal:v7.0-16-g9b85f7d961 PowerNV NIP: c000000000194a1c LR: c000000000194a18 CTR: c000000000a78670 REGS: c00000004d2134e0 TRAP: 0700 Tainted: GW (6.2.0-rc2-00011-g1247637727f2) MSR: 9000000000021033  CR: 48002824 XER: 00000000 CFAR: c00000000013fb64 IRQMASK: 1 La advertencia anterior se activó porque el código imc-pmu actual usa bloqueo mutex en secciones con interrupciones deshabilitadas. La función mutex_lock() llama internamente a __might_resched(), que verificará si las IRQ están deshabilitadas y, en caso de que lo estén, activará la advertencia. Solucione el problema cambiando el bloqueo mutex a spinlock. [mpe: Corregir comentarios, eliminar errores en el registro de cambios, agregar etiquetas reportadas por]"}], "references": [{"url": "https://git.kernel.org/stable/c/424bcb570cb320d1d15238cd4c933522b90f78fa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76d588dddc459fefa1da96e0a081a397c5c8e216", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8cbeb60320ac45a8240b561c8ef466b86c34dedc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a90d339f1f66be4a946769b565668e2bd0686dfa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d0c6d2a31026102d4738b47a610bed4401b9834f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52997", "published_date": "2025-03-27T17:15:48.433", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipv4: prevent potential spectre v1 gadget in ip_metrics_convert()\n\nif (!type)\n\t\tcontinue;\n\tif (type > RTAX_MAX)\n\t\treturn -EINVAL;\n\t...\n\tmetrics[type - 1] = val;\n\n@type being used as an array index, we need to prevent\ncpu speculation or risk leaking kernel memory content."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipv4: evitar un potencial gadget spectre v1 en ip_metrics_convert() if (!type) continue; if (type &gt; RTAX_MAX) return -EINVAL; ... metrics[type - 1] = val; @type se utiliza como un índice de matriz, debemos evitar la especulación de la CPU o el riesgo de filtrar el contenido de la memoria del kernel."}], "references": [{"url": "https://git.kernel.org/stable/c/1d1d63b612801b3f0a39b7d4467cad0abd60e5c8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/34c6142f0df9cd75cba5a7aa9df0960d2854b415", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6850fe301d015a7d2012d1de8caf43dafb7cc2f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/746db9ec1e672eee13965625ddac0d97e16fa20c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d50e7348b44f1e046121ff5be01b7fb6978a1149", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ef050cf5fb70d995a0d03244e25179b7c66a924a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
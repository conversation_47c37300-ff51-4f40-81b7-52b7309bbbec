{"cve_id": "CVE-2023-52942", "published_date": "2025-03-27T17:15:44.163", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncgroup/cpuset: Fix wrong check in update_parent_subparts_cpumask()\n\nIt was found that the check to see if a partition could use up all\nthe cpus from the parent cpuset in update_parent_subparts_cpumask()\nwas incorrect. As a result, it is possible to leave parent with no\neffective cpu left even if there are tasks in the parent cpuset. This\ncan lead to system panic as reported in [1].\n\nFix this probem by updating the check to fail the enabling the partition\nif parent's effective_cpus is a subset of the child's cpus_allowed.\n\nAlso record the error code when an error happens in update_prstate()\nand add a test case where parent partition and child have the same cpu\nlist and parent has task. Enabling partition in the child will fail in\nthis case.\n\n[1] https://www.spinics.net/lists/cgroups/msg36254.html"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cgroup/cpuset: Corrección de una comprobación incorrecta en update_parent_subparts_cpumask(). Se detectó que la comprobación para ver si una partición podía usar todas las CPU del conjunto de CPU principal en update_parent_subparts_cpumask() era incorrecta. Como resultado, es posible dejar la partición principal sin CPU efectiva, incluso si hay tareas en el conjunto de CPU principal. Esto puede provocar un pánico del sistema, como se informa en [1]. Corrija este problema actualizando la comprobación para que falle al habilitar la partición si el valor de CPU efectivas de la partición principal es un subconjunto del valor de CPU permitidas de la partición secundaria. También registre el código de error cuando se produce un error en update_prstate() y agregue un caso de prueba donde la partición principal y la secundaria tengan la misma lista de CPU y la partición principal tenga una tarea. En este caso, la habilitación de la partición en la secundaria fallará. [1] https://www.spinics.net/lists/cgroups/msg36254.html"}], "references": [{"url": "https://git.kernel.org/stable/c/a2ab7f2cf5ef8f0c6212a246e681d1fe358cec1f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e5ae8803847b80fe9d744a3174abe2b7bfed222a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49738", "published_date": "2025-03-27T17:15:38.340", "last_modified_date": "2025-04-14T20:38:36.153", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nf2fs: fix to do sanity check on i_extra_isize in is_alive()\n\ns<PERSON>z<PERSON> found a f2fs bug:\n\nBUG: KASAN: slab-out-of-bounds in data_blkaddr fs/f2fs/f2fs.h:2891 [inline]\nBUG: KASAN: slab-out-of-bounds in is_alive fs/f2fs/gc.c:1117 [inline]\nBUG: KASAN: slab-out-of-bounds in gc_data_segment fs/f2fs/gc.c:1520 [inline]\nBUG: KASAN: slab-out-of-bounds in do_garbage_collect+0x386a/0x3df0 fs/f2fs/gc.c:1734\nRead of size 4 at addr ffff888076557568 by task kworker/u4:3/52\n\nCPU: 1 PID: 52 Comm: kworker/u4:3 Not tainted 6.1.0-rc4-syzkaller-00362-gfef7fd48922d #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 10/26/2022\nWorkqueue: writeback wb_workfn (flush-7:0)\nCall Trace:\n<TASK>\n__dump_stack lib/dump_stack.c:88 [inline]\ndump_stack_lvl+0xcd/0x134 lib/dump_stack.c:106\nprint_address_description mm/kasan/report.c:284 [inline]\nprint_report+0x15e/0x45d mm/kasan/report.c:395\nkasan_report+0xbb/0x1f0 mm/kasan/report.c:495\ndata_blkaddr fs/f2fs/f2fs.h:2891 [inline]\nis_alive fs/f2fs/gc.c:1117 [inline]\ngc_data_segment fs/f2fs/gc.c:1520 [inline]\ndo_garbage_collect+0x386a/0x3df0 fs/f2fs/gc.c:1734\nf2fs_gc+0x88c/0x20a0 fs/f2fs/gc.c:1831\nf2fs_balance_fs+0x544/0x6b0 fs/f2fs/segment.c:410\nf2fs_write_inode+0x57e/0xe20 fs/f2fs/inode.c:753\nwrite_inode fs/fs-writeback.c:1440 [inline]\n__writeback_single_inode+0xcfc/0x1440 fs/fs-writeback.c:1652\nwriteback_sb_inodes+0x54d/0xf90 fs/fs-writeback.c:1870\nwb_writeback+0x2c5/0xd70 fs/fs-writeback.c:2044\nwb_do_writeback fs/fs-writeback.c:2187 [inline]\nwb_workfn+0x2dc/0x12f0 fs/fs-writeback.c:2227\nprocess_one_work+0x9bf/0x1710 kernel/workqueue.c:2289\nworker_thread+0x665/0x1080 kernel/workqueue.c:2436\nkthread+0x2e4/0x3a0 kernel/kthread.c:376\nret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:306\n\nThe root cause is that we forgot to do sanity check on .i_extra_isize\nin below path, result in accessing invalid address later, fix it.\n- gc_data_segment\n - is_alive\n  - data_blkaddr\n   - offset_in_addr"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: f2fs: corrección para realizar una comprobación de cordura en i_extra_isize en is_alive() syzbot encontró un error de f2fs: ERROR: KASAN: slab-out-of-bounds en data_blkaddr fs/f2fs/f2fs.h:2891 [en línea] ERROR: KASAN: slab-out-of-bounds en is_alive fs/f2fs/gc.c:1117 [en línea] ERROR: KASAN: slab-out-of-bounds en gc_data_segment fs/f2fs/gc.c:1520 [en línea] ERROR: KASAN: slab-out-of-bounds en do_garbage_collect+0x386a/0x3df0 fs/f2fs/gc.c:1734 Lectura de tamaño 4 en la dirección ffff888076557568 por la tarea kworker/u4:3/52 CPU: 1 PID: 52 Comm: kworker/u4:3 No contaminado 6.1.0-rc4-syzkaller-00362-gfef7fd48922d #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 26/10/2022 Cola de trabajo: escritura diferida wb_workfn (flush-7:0) Rastreo de llamadas: __dump_stack lib/dump_stack.c:88 [inline] dump_stack_lvl+0xcd/0x134 lib/dump_stack.c:106 print_address_description mm/kasan/report.c:284 [inline] print_report+0x15e/0x45d mm/kasan/report.c:395 kasan_report+0xbb/0x1f0 mm/kasan/report.c:495 data_blkaddr fs/f2fs/f2fs.h:2891 [inline] is_alive fs/f2fs/gc.c:1117 [inline] gc_data_segment fs/f2fs/gc.c:1520 [inline] do_garbage_collect+0x386a/0x3df0 fs/f2fs/gc.c:1734 f2fs_gc+0x88c/0x20a0 fs/f2fs/gc.c:1831 f2fs_balance_fs+0x544/0x6b0 fs/f2fs/segment.c:410 f2fs_write_inode+0x57e/0xe20 fs/f2fs/inode.c:753 write_inode fs/fs-writeback.c:1440 [inline] __writeback_single_inode+0xcfc/0x1440 fs/fs-writeback.c:1652 writeback_sb_inodes+0x54d/0xf90 fs/fs-writeback.c:1870 wb_writeback+0x2c5/0xd70 fs/fs-writeback.c:2044 wb_do_writeback fs/fs-writeback.c:2187 [inline] wb_workfn+0x2dc/0x12f0 fs/fs-writeback.c:2227 process_one_work+0x9bf/0x1710 kernel/workqueue.c:2289 worker_thread+0x665/0x1080 kernel/workqueue.c:2436 kthread+0x2e4/0x3a0 kernel/kthread.c:376 ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:306 La causa raíz es que olvidamos hacer una comprobación de cordura en .i_extra_isize en la siguiente ruta, lo que da como resultado el acceso a una dirección no válida más tarde, corríjalo. - gc_data_segment - is_alive - data_blkaddr - offset_in_addr"}], "references": [{"url": "https://git.kernel.org/stable/c/5b25035fb888cb2f78bf0b9c9f95b1dc54480d36", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/914e38f02a490dafd980ff0f39cccedc074deb29", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/97ccfffcc061e54ce87e4a51a40e2e9cb0b7076a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d3b7b4afd6b2c344eabf9cc26b8bfa903c164c7c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e5142a4935c1f15841d06047b8130078fc4d7b8f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
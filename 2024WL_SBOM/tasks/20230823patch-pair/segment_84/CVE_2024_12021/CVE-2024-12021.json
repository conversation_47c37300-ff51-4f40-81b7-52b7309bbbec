{"cve_id": "CVE-2024-12021", "published_date": "2025-03-31T14:15:18.303", "last_modified_date": "2025-04-01T20:26:30.593", "descriptions": [{"lang": "en", "value": "Coverity versions prior to 2024.9.0 are vulnerable to stored cross-site scripting (XSS) in various administrative interfaces. The impact of exploitation may result in the compromise of local accounts managed by the Coverity platform as well as other standard impacts resulting from cross-site scripting."}, {"lang": "es", "value": "Las versiones de Coverity anteriores a la 2024.9.0 son vulnerables a ataques de cross-site scripting (XSS) almacenado en diversas interfaces administrativas. El impacto de esta explotación puede comprometer las cuentas locales administradas por la plataforma Coverity, así como otros impactos habituales derivados de los ataques de cross-site scripting."}], "references": [{"url": "https://community.blackduck.com/s/article/Black-Duck-Product-Security-Advisory-CVE-2024-12021", "source": "<EMAIL>", "tags": []}]}
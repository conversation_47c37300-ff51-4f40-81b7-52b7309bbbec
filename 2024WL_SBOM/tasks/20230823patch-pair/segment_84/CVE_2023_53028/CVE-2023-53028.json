{"cve_id": "CVE-2023-53028", "published_date": "2025-03-27T17:15:52.507", "last_modified_date": "2025-04-15T19:42:15.790", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRevert \"wifi: mac80211: fix memory leak in ieee80211_if_add()\"\n\nThis reverts commit 13e5afd3d773c6fc6ca2b89027befaaaa1ea7293.\n\nieee80211_if_free() is already called from free_netdev(ndev)\nbecause ndev->priv_destructor == ieee80211_if_free\n\nsyzbot reported:\n\ngeneral protection fault, probably for non-canonical address 0xdffffc0000000004: 0000 [#1] PREEMPT SMP KASAN\nKASAN: null-ptr-deref in range [0x0000000000000020-0x0000000000000027]\nCPU: 0 PID: 10041 Comm: syz-executor.0 Not tainted 6.2.0-rc2-syzkaller-00388-g55b98837e37d #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 10/26/2022\nRIP: 0010:pcpu_get_page_chunk mm/percpu.c:262 [inline]\nRIP: 0010:pcpu_chunk_addr_search mm/percpu.c:1619 [inline]\nRIP: 0010:free_percpu mm/percpu.c:2271 [inline]\nRIP: 0010:free_percpu+0x186/0x10f0 mm/percpu.c:2254\nCode: 80 3c 02 00 0f 85 f5 0e 00 00 48 8b 3b 48 01 ef e8 cf b3 0b 00 48 ba 00 00 00 00 00 fc ff df 48 8d 78 20 48 89 f9 48 c1 e9 03 <80> 3c 11 00 0f 85 3b 0e 00 00 48 8b 58 20 48 b8 00 00 00 00 00 fc\nRSP: 0018:ffffc90004ba7068 EFLAGS: 00010002\nRAX: 0000000000000000 RBX: ffff88823ffe2b80 RCX: 0000000000000004\nRDX: dffffc0000000000 RSI: ffffffff81c1f4e7 RDI: 0000000000000020\nRBP: ffffe8fffe8fc220 R08: 0000000000000005 R09: 0000000000000000\nR10: 0000000000000000 R11: 1ffffffff2179ab2 R12: ffff8880b983d000\nR13: 0000000000000003 R14: 0000607f450fc220 R15: ffff88823ffe2988\nFS: 00007fcb349de700(0000) GS:ffff8880b9800000(0000) knlGS:0000000000000000\nCS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 0000001b32220000 CR3: 000000004914f000 CR4: 00000000003506f0\nDR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000\nDR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400\nCall Trace:\n<TASK>\nnetdev_run_todo+0x6bf/0x1100 net/core/dev.c:10352\nieee80211_register_hw+0x2663/0x4040 net/mac80211/main.c:1411\nmac80211_hwsim_new_radio+0x2537/0x4d80 drivers/net/wireless/mac80211_hwsim.c:4583\nhwsim_new_radio_nl+0xa09/0x10f0 drivers/net/wireless/mac80211_hwsim.c:5176\ngenl_family_rcv_msg_doit.isra.0+0x1e6/0x2d0 net/netlink/genetlink.c:968\ngenl_family_rcv_msg net/netlink/genetlink.c:1048 [inline]\ngenl_rcv_msg+0x4ff/0x7e0 net/netlink/genetlink.c:1065\nnetlink_rcv_skb+0x165/0x440 net/netlink/af_netlink.c:2564\ngenl_rcv+0x28/0x40 net/netlink/genetlink.c:1076\nnetlink_unicast_kernel net/netlink/af_netlink.c:1330 [inline]\nnetlink_unicast+0x547/0x7f0 net/netlink/af_netlink.c:1356\nnetlink_sendmsg+0x91b/0xe10 net/netlink/af_netlink.c:1932\nsock_sendmsg_nosec net/socket.c:714 [inline]\nsock_sendmsg+0xd3/0x120 net/socket.c:734\n____sys_sendmsg+0x712/0x8c0 net/socket.c:2476\n___sys_sendmsg+0x110/0x1b0 net/socket.c:2530\n__sys_sendmsg+0xf7/0x1c0 net/socket.c:2559\ndo_syscall_x64 arch/x86/entry/common.c:50 [inline]\ndo_syscall_64+0x39/0xb0 arch/x86/entry/common.c:80\nentry_SYSCALL_64_after_hwframe+0x63/0xcd"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Revertir \"wifi: mac80211: corregir pérdida de memoria en ieee80211_if_add()\" Esto revierte el commit 13e5afd3d773c6fc6ca2b89027befaaaa1ea7293. ieee80211_if_free() ya se llama desde free_netdev(ndev) porque ndev-&gt;priv_destructor == ieee80211_if_free syzbot informó: error de protección general, probablemente para la dirección no canónica 0xdffffc0000000004: 0000 [#1] PREEMPT SMP KASAN KASAN: null-ptr-deref en el rango [0x000000000000020-0x0000000000000027] CPU: 0 PID: 10041 Comm: syz-executor.0 No contaminado 6.2.0-rc2-syzkaller-00388-g55b98837e37d #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 26/10/2022 RIP: 0010:pcpu_get_page_chunk mm/percpu.c:262 [en línea] RIP: 0010:pcpu_chunk_addr_search mm/percpu.c:1619 [en línea] RIP: 0010:free_percpu mm/percpu.c:2271 [en línea] RIP: 0010:free_percpu+0x186/0x10f0 mm/percpu.c:2254 Código: 80 3c 02 00 0f 85 f5 0e 00 00 48 8b 3b 48 01 ef e8 cf b3 0b 00 48 ba 00 00 00 00 00 fc ff df 48 8d 78 20 48 89 f9 48 c1 e9 03 &lt;80&gt; 3c 11 00 0f 85 3b 0e 00 00 48 8b 58 20 48 b8 00 00 00 00 00 fc RSP: 0018:ffffc90004ba7068 EFLAGS: 00010002 RAX: 000000000000000 RBX: ffff88823ffe2b80 RCX: 0000000000000004 RDX: dffffc0000000000 RSI: ffffffff81c1f4e7 RDI: 0000000000000020 RBP: ffffe8fffe8fc220 R08: 0000000000000005 R09: 00000000000000000 R10: 0000000000000000 R11: 1ffffffff2179ab2 R12: ffff8880b983d000 R13: 000000000000003 R14: 0000607f450fc220 R15: ffff88823ffe2988 FS: 00007fcb349de700(0000) GS:ffff8880b9800000(0000) knlGS:000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 0000001b32220000 CR3: 000000004914f000 CR4: 000000000003506f0 DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000 DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400 Seguimiento de llamadas:  netdev_run_todo+0x6bf/0x1100 net/core/dev.c:10352 ieee80211_register_hw+0x2663/0x4040 net/mac80211/main.c:1411 mac80211_hwsim_new_radio+0x2537/0x4d80 drivers/net/wireless/mac80211_hwsim.c:4583 hwsim_new_radio_nl+0xa09/0x10f0 drivers/net/wireless/mac80211_hwsim.c:5176 genl_family_rcv_msg_doit.isra.0+0x1e6/0x2d0 net/netlink/genetlink.c:968 genl_family_rcv_msg net/netlink/genetlink.c:1048 [en línea] genl_rcv_msg+0x4ff/0x7e0 net/netlink/genetlink.c:1065 netlink_rcv_skb+0x165/0x440 net/netlink/af_netlink.c:2564 genl_rcv+0x28/0x40 net/netlink/genetlink.c:1076 netlink_unicast_kernel net/netlink/af_netlink.c:1330 [en línea] netlink_unicast+0x547/0x7f0 net/netlink/af_netlink.c:1356 netlink_sendmsg+0x91b/0xe10 net/netlink/af_netlink.c:1932 sock_sendmsg_nosec net/socket.c:714 [en línea] sock_sendmsg+0xd3/0x120 net/socket.c:734 ____sys_sendmsg+0x712/0x8c0 net/socket.c:2476 ___sys_sendmsg+0x110/0x1b0 net/socket.c:2530 __sys_sendmsg+0xf7/0x1c0 net/socket.c:2559 do_syscall_x64 arch/x86/entry/common.c:50 [en línea] do_syscall_64+0x39/0xb0 arch/x86/entry/common.c:80 entry_SYSCALL_64_after_hwframe+0x63/0xcd"}], "references": [{"url": "https://git.kernel.org/stable/c/71e5cd1018d345e649e63f74a56c1897f99db7e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/80f8a66dede0a4b4e9e846765a97809c6fe49ce5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/982c8b1e95c088f5d8f65967ec25be66e961401c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/effecd8d116d3d3a28b4f628e61bba8d318fdfcf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
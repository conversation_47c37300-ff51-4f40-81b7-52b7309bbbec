{"cve_id": "CVE-2023-53003", "published_date": "2025-03-27T17:15:49.170", "last_modified_date": "2025-04-01T15:39:21.640", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nEDAC/qcom: Do not pass llcc_driv_data as edac_device_ctl_info's pvt_info\n\nThe memory for llcc_driv_data is allocated by the LLCC driver. But when\nit is passed as the private driver info to the EDAC core, it will get freed\nduring the qcom_edac driver release. So when the qcom_edac driver gets probed\nagain, it will try to use the freed data leading to the use-after-free bug.\n\nHence, do not pass llcc_driv_data as pvt_info but rather reference it\nusing the platform_data pointer in the qcom_edac driver."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: EDAC/qcom: No pasar llcc_driv_data como pvt_info de edac_device_ctl_info. El controlador LLCC asigna memoria para llcc_driv_data. Sin embargo, al pasarla como información privada del controlador al núcleo de EDAC, se libera durante la liberación del controlador qcom_edac. Por lo tanto, al volver a probar el controlador qcom_edac, intentará usar los datos liberados, lo que provoca el error de use-after-free. Por lo tanto, no pasar llcc_driv_data como pvt_info, sino referenciarlo mediante el puntero platform_data en el controlador qcom_edac."}], "references": [{"url": "https://git.kernel.org/stable/c/66e10d5f399629ef7877304d9ba2b35d0474e7eb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6f0351d0c311951b8b3064db91e61841e85b2b96", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/76d9ebb7f0bc10fbc78b6d576751552edf743968", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/977c6ba624f24ae20cf0faee871257a39348d4a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bff5243bd32661cf9ce66f6d9210fc8f89bda145", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
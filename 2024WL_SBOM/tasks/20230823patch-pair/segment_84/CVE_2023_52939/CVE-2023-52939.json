{"cve_id": "CVE-2023-52939", "published_date": "2025-03-27T17:15:43.803", "last_modified_date": "2025-04-15T14:46:06.930", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm: memcg: fix NULL pointer in mem_cgroup_track_foreign_dirty_slowpath()\n\nAs commit 18365225f044 (\"hwpoison, memcg: forcibly uncharge LRU pages\"),\nhwpoison will forcibly uncharg a LRU hwpoisoned page, the folio_memcg\ncould be NULl, then, mem_cgroup_track_foreign_dirty_slowpath() could\noccurs a NULL pointer dereference, let's do not record the foreign\nwritebacks for folio memcg is null in mem_cgroup_track_foreign_dirty() to\nfix it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm: memcg: corregir puntero NULL en mem_cgroup_track_foreign_dirty_slowpath() Como confirmación 18365225f044 (\"hwpoison, memcg: descargar a la fuerza páginas LRU\"), hwpoison descargará a la fuerza una página LRU hwpoisoned, folio_memcg podría ser NUL, luego, mem_cgroup_track_foreign_dirty_slowpath() podría producir una desreferencia de puntero NULL, no registremos las reescrituras externas para folio memcg es nulo en mem_cgroup_track_foreign_dirty() para solucionarlo."}], "references": [{"url": "https://git.kernel.org/stable/c/ac86f547ca1002aec2ef66b9e64d03f45bbbfbb9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b79ba5953f6fdc5559389ad415620bffc24f024b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2022-49741", "published_date": "2025-03-27T17:15:38.720", "last_modified_date": "2025-04-14T20:26:41.627", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfbdev: smscufx: fix error handling code in ufx_usb_probe\n\nThe current error handling code in ufx_usb_probe have many unmatching\nissues, e.g., missing ufx_free_usb_list, destroy_modedb label should\nonly include framebuffer_release, fb_dealloc_cmap only matches\nfb_alloc_cmap.\n\nMy local syzkaller reports a memory leak bug:\n\nmemory leak in ufx_usb_probe\n\nBUG: memory leak\nunreferenced object 0xffff88802f879580 (size 128):\n  comm \"kworker/0:7\", pid 17416, jiffies 4295067474 (age 46.710s)\n  hex dump (first 32 bytes):\n    80 21 7c 2e 80 88 ff ff 18 d0 d0 0c 80 88 ff ff  .!|.............\n    00 d0 d0 0c 80 88 ff ff e0 ff ff ff 0f 00 00 00  ................\n  backtrace:\n    [<ffffffff814c99a0>] kmalloc_trace+0x20/0x90 mm/slab_common.c:1045\n    [<ffffffff824d219c>] kmalloc include/linux/slab.h:553 [inline]\n    [<ffffffff824d219c>] kzalloc include/linux/slab.h:689 [inline]\n    [<ffffffff824d219c>] ufx_alloc_urb_list drivers/video/fbdev/smscufx.c:1873 [inline]\n    [<ffffffff824d219c>] ufx_usb_probe+0x11c/0x15a0 drivers/video/fbdev/smscufx.c:1655\n    [<ffffffff82d17927>] usb_probe_interface+0x177/0x370 drivers/usb/core/driver.c:396\n    [<ffffffff82712f0d>] call_driver_probe drivers/base/dd.c:560 [inline]\n    [<ffffffff82712f0d>] really_probe+0x12d/0x390 drivers/base/dd.c:639\n    [<ffffffff8271322f>] __driver_probe_device+0xbf/0x140 drivers/base/dd.c:778\n    [<ffffffff827132da>] driver_probe_device+0x2a/0x120 drivers/base/dd.c:808\n    [<ffffffff82713c27>] __device_attach_driver+0xf7/0x150 drivers/base/dd.c:936\n    [<ffffffff82710137>] bus_for_each_drv+0xb7/0x100 drivers/base/bus.c:427\n    [<ffffffff827136b5>] __device_attach+0x105/0x2d0 drivers/base/dd.c:1008\n    [<ffffffff82711d36>] bus_probe_device+0xc6/0xe0 drivers/base/bus.c:487\n    [<ffffffff8270e242>] device_add+0x642/0xdc0 drivers/base/core.c:3517\n    [<ffffffff82d14d5f>] usb_set_configuration+0x8ef/0xb80 drivers/usb/core/message.c:2170\n    [<ffffffff82d2576c>] usb_generic_driver_probe+0x8c/0xc0 drivers/usb/core/generic.c:238\n    [<ffffffff82d16ffc>] usb_probe_device+0x5c/0x140 drivers/usb/core/driver.c:293\n    [<ffffffff82712f0d>] call_driver_probe drivers/base/dd.c:560 [inline]\n    [<ffffffff82712f0d>] really_probe+0x12d/0x390 drivers/base/dd.c:639\n    [<ffffffff8271322f>] __driver_probe_device+0xbf/0x140 drivers/base/dd.c:778\n\nFix this bug by rewriting the error handling code in ufx_usb_probe."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: fbdev: smscufx: corrige el código de gestión de errores en ufx_usb_probe El código de manejo de errores actual en ufx_usb_probe tiene muchos problemas que no coinciden, por ejemplo, falta ufx_free_usb_list, la etiqueta destroy_modedb solo debe incluir framebuffer_release, fb_dealloc_cmap solo coincide con fb_alloc_cmap. Mi syzkaller local informa de un error de pérdida de memoria: pérdida de memoria en ufx_usb_probe ERROR: pérdida de memoria objeto no referenciado 0xffff88802f879580 (tamaño 128): comm \"kworker/0:7\", pid 17416, jiffies 4295067474 (edad 46.710s) volcado hexadecimal (primeros 32 bytes): 80 21 7c 2e 80 88 ff ff 18 d0 d0 0c 80 88 ff ff .!|............. 00 d0 d0 0c 80 88 ff ff e0 ff ff ff 0f 00 00 00 ................ backtrace: [] kmalloc_trace+0x20/0x90 mm/slab_common.c:1045 [] kmalloc include/linux/slab.h:553 [en línea] [] kzalloc include/linux/slab.h:689 [en línea] [] ufx_alloc_urb_list drivers/video/fbdev/smscufx.c:1873 [en línea] [] ufx_usb_probe+0x11c/0x15a0 drivers/video/fbdev/smscufx.c:1655 [] usb_probe_interface+0x177/0x370 drivers/usb/core/driver.c:396 [] sonda_de_controlador_de_llamada controladores/base/dd.c:560 [en línea] [] sonda_realmente+0x12d/0x390 controladores/base/dd.c:639 [] dispositivo_sonda_de_controlador+0xbf/0x140 controladores/base/dd.c:778 [] dispositivo_sonda_de_controlador+0x2a/0x120 controladores/base/dd.c:808 [] controlador_adjunto_de_dispositivo+0xf7/0x150 controladores/base/dd.c:936 [] bus_para_cada_unidad+0xb7/0x100 controladores/base/bus.c:427 [] __adjunto_dispositivo+0x105/0x2d0 controladores/base/dd.c:1008 [] dispositivo_sonda_bus+0xc6/0xe0 controladores/base/bus.c:487 [] adición_dispositivo+0x642/0xdc0 controladores/base/núcleo.c:3517 [] configuración_establecida_usb+0x8ef/0xb80 controladores/usb/núcleo/mensaje.c:2170 [] usb_generic_driver_probe+0x8c/0xc0 drivers/usb/core/generic.c:238 [] usb_probe_device+0x5c/0x140 drivers/usb/core/driver.c:293 [] call_driver_probe drivers/base/dd.c:560 [en línea] [] really_probe+0x12d/0x390 drivers/base/dd.c:639 [] __driver_probe_device+0xbf/0x140 drivers/base/dd.c:778 Corrija este error reescribiendo el código de gestión de errores en ufx_usb_probe."}], "references": [{"url": "https://git.kernel.org/stable/c/1b4c08844628dfc8d72d3f51b657f2a5e63b7b4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3931014367ef31d26af65386a4ca496f50f0cfdf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3b3d3127f5b4291ae4caaf50f7b66089ad600480", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/64fa364ad3245508d393e16ed4886f92d7eb423c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b76449ee75e21acfe9fa4c653d8598f191ed7d68", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
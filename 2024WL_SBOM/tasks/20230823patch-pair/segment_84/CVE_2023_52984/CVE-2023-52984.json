{"cve_id": "CVE-2023-52984", "published_date": "2025-03-27T17:15:45.677", "last_modified_date": "2025-04-15T14:14:43.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: phy: dp83822: Fix null pointer access on DP83825/DP83826 devices\n\nThe probe() function is only used for the DP83822 PHY, leaving the\nprivate data pointer uninitialized for the smaller DP83825/26 models.\nWhile all uses of the private data structure are hidden in 82822 specific\ncallbacks, configuring the interrupt is shared across all models.\nThis causes a NULL pointer dereference on the smaller PHYs as it accesses\nthe private data unchecked. Verifying the pointer avoids that."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: phy: dp83822: Se corrige el acceso a puntero nulo en dispositivos DP83825/DP83826. La función probe() solo se utiliza para la estructura física (PHY) DP83822, lo que deja el puntero de datos privados sin inicializar para los modelos DP83825/26 más pequeños. Si bien todos los usos de la estructura de datos privados están ocultos en las devoluciones de llamada específicas de 82822, la configuración de la interrupción se comparte entre todos los modelos. Esto provoca una desreferencia de puntero nulo en las estructuras físicas (PHY) más pequeñas, ya que accede a los datos privados sin verificar. Verificar el puntero evita esto."}], "references": [{"url": "https://git.kernel.org/stable/c/2cd1e9c013ec56421c58921b1ddf1d2d53bd47fa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/362a2f5531dc0e5b0b5b3e3a541000dbffa75461", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/422ae7d9c7221e8d4c8526d0f54106307d69d2dc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/78901b10522cdf6badf24acf65a892637596bccc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
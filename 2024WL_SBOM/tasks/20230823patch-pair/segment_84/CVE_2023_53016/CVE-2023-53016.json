{"cve_id": "CVE-2023-53016", "published_date": "2025-03-27T17:15:50.983", "last_modified_date": "2025-04-15T19:41:27.160", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: Fix possible deadlock in rfcomm_sk_state_change\n\nsyzbot reports a possible deadlock in rfcomm_sk_state_change [1].\nWhile rfcomm_sock_connect acquires the sk lock and waits for\nthe rfcomm lock, rfcomm_sock_release could have the rfcomm\nlock and hit a deadlock for acquiring the sk lock.\nHere's a simplified flow:\n\nrfcomm_sock_connect:\n  lock_sock(sk)\n  rfcomm_dlc_open:\n    rfcomm_lock()\n\nrfcomm_sock_release:\n  rfcomm_sock_shutdown:\n    rfcomm_lock()\n    __rfcomm_dlc_close:\n        rfcomm_k_state_change:\n\t  lock_sock(sk)\n\nThis patch drops the sk lock before calling rfcomm_dlc_open to\navoid the possible deadlock and holds sk's reference count to\nprevent use-after-free after rfcomm_dlc_open completes."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: Se soluciona un posible bloqueo en rfcomm_sk_state_change syzbot informa de un posible bloqueo en rfcomm_sk_state_change [1]. Mientras rfcomm_sock_connect adquiere el bloqueo sk y espera el bloqueo rfcomm, rfcomm_sock_release podría tener el bloqueo rfcomm y llegar a un bloqueo para adquirir el bloqueo sk. Aquí hay un flujo simplificado: rfcomm_sock_connect: lock_sock(sk) rfcomm_dlc_open: rfcomm_lock() rfcomm_sock_release: rfcomm_sock_shutdown: rfcomm_lock() __rfcomm_dlc_close: rfcomm_k_state_change: lock_sock(sk) Este parche elimina el bloqueo de sk antes de llamar a rfcomm_dlc_open para evitar el posible bloqueo y mantiene el recuento de referencia de sk para evitar el use-after-free después de que se completa rfcomm_dlc_open."}], "references": [{"url": "https://git.kernel.org/stable/c/17511bd84871f4a6106cb335616e086880313f3f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1d80d57ffcb55488f0ec0b77928d4f82d16b6a90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/98aec50ff7f60cc6f2d6a4396b475c547e58b04d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-52933", "published_date": "2025-03-27T17:15:43.063", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nSquashfs: fix handling and sanity checking of xattr_ids count\n\nA Sysbot [1] corrupted filesystem exposes two flaws in the handling and\nsanity checking of the xattr_ids count in the filesystem.  Both of these\nflaws cause computation overflow due to incorrect typing.\n\nIn the corrupted filesystem the xattr_ids value is 4294967071, which\nstored in a signed variable becomes the negative number -225.\n\nFlaw 1 (64-bit systems only):\n\nThe signed integer xattr_ids variable causes sign extension.\n\nThis causes variable overflow in the SQUASHFS_XATTR_*(A) macros.  The\nvariable is first multiplied by sizeof(struct squashfs_xattr_id) where the\ntype of the sizeof operator is \"unsigned long\".\n\nOn a 64-bit system this is 64-bits in size, and causes the negative number\nto be sign extended and widened to 64-bits and then become unsigned.  This\nproduces the very large number 18446744073709548016 or 2^64 - 3600.  This\nnumber when rounded up by SQUASHFS_METADATA_SIZE - 1 (8191 bytes) and\ndivided by SQUASHFS_METADATA_SIZE overflows and produces a length of 0\n(stored in len).\n\nFlaw 2 (32-bit systems only):\n\nOn a 32-bit system the integer variable is not widened by the unsigned\nlong type of the sizeof operator (32-bits), and the signedness of the\nvariable has no effect due it always being treated as unsigned.\n\nThe above corrupted xattr_ids value of 4294967071, when multiplied\noverflows and produces the number 4294963696 or 2^32 - 3400.  This number\nwhen rounded up by SQUASHFS_METADATA_SIZE - 1 (8191 bytes) and divided by\nSQUASHFS_METADATA_SIZE overflows again and produces a length of 0.\n\nThe effect of the 0 length computation:\n\nIn conjunction with the corrupted xattr_ids field, the filesystem also has\na corrupted xattr_table_start value, where it matches the end of\nfilesystem value of 850.\n\nThis causes the following sanity check code to fail because the\nincorrectly computed len of 0 matches the incorrect size of the table\nreported by the superblock (0 bytes).\n\n    len = SQUASHFS_XATTR_BLOCK_BYTES(*xattr_ids);\n    indexes = SQUASHFS_XATTR_BLOCKS(*xattr_ids);\n\n    /*\n     * The computed size of the index table (len bytes) should exactly\n     * match the table start and end points\n    */\n    start = table_start + sizeof(*id_table);\n    end = msblk->bytes_used;\n\n    if (len != (end - start))\n            return ERR_PTR(-EINVAL);\n\nChanging the xattr_ids variable to be \"usigned int\" fixes the flaw on a\n64-bit system.  This relies on the fact the computation is widened by the\nunsigned long type of the sizeof operator.\n\nCasting the variable to u64 in the above macro fixes this flaw on a 32-bit\nsystem.\n\nIt also means 64-bit systems do not implicitly rely on the type of the\nsizeof operator to widen the computation.\n\n[1] https://lore.kernel.org/lkml/<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Squashfs: corrección de la gestión y la comprobación de la validez del recuento de xattr_ids. Un sistema de archivos dañado por un bot de sistema [1] expone dos fallos en el manejo y la comprobación de la validez del recuento de xattr_ids. Ambos fallos provocan un desbordamiento de cálculo debido a una escritura incorrecta. En el sistema de archivos dañado, el valor de xattr_ids es 4294967071, que, almacenado en una variable con signo, se convierte en el número negativo -225. Fallo 1 (solo sistemas de 64 bits): La variable xattr_ids, un entero con signo, provoca la extensión del signo. Esto provoca un desbordamiento de variable en las macros SQUASHFS_XATTR_*(A). La variable se multiplica primero por sizeof(struct squashfs_xattr_id), donde el tipo del operador sizeof es \"unsigned long\". En un sistema de 64 bits, este valor tiene un tamaño de 64 bits y provoca que el número negativo se amplíe con signo y se ensanche a 64 bits, quedando así sin signo. Esto produce el gran número 18446744073709548016 o 2^64 - 3600. Este número, al redondearse por SQUASHFS_METADATA_SIZE - 1 (8191 bytes) y dividirse por SQUASHFS_METADATA_SIZE, se desborda y produce una longitud de 0 (almacenada en len). Fallo 2 (solo en sistemas de 32 bits): En un sistema de 32 bits, la variable entera no se amplía con el tipo \"long sin signo\" del operador sizeof (32 bits), y el signo de la variable no tiene efecto, ya que siempre se trata como sin signo. El valor xattr_ids dañado anterior de 4294967071, cuando se multiplica se desborda y produce el número 4294963696 o 2^32 - 3400. Este número cuando se redondea por SQUASHFS_METADATA_SIZE - 1 (8191 bytes) y se divide por SQUASHFS_METADATA_SIZE se desborda nuevamente y produce una longitud de 0. El efecto del cálculo de la longitud 0: junto con el campo xattr_ids dañado, el sistema de archivos también tiene un valor xattr_table_start dañado, donde coincide con el valor de fin del sistema de archivos de 850. Esto hace que el siguiente código de verificación de cordura falle porque el len incorrectamente calculado de 0 coincide con el tamaño incorrecto de la tabla informada por el superbloque (0 bytes). len = SQUASHFS_XATTR_BLOCK_BYTES(*xattr_ids); índices = SQUASHFS_XATTR_BLOCKS(*xattr_ids); /* * El tamaño calculado de la tabla de índices (len bytes) debe coincidir exactamente con los puntos de inicio y fin de la tabla. */ inicio = tabla_inicio + tamaño_de(*id_tabla); fin = msblk-&gt;bytes_usados; si (len != (fin - inicio)) devolver ERR_PTR(-EINVAL); Cambiar la variable xattr_ids a \"usigned int\" corrige el fallo en sistemas de 64 bits. Esto se debe a que el cálculo se amplía con el tipo \"unsigned long\" del operador \"sizeof\". Convertir la variable a \"u64\" en la macro anterior corrige este fallo en sistemas de 32 bits. Esto también significa que los sistemas de 64 bits no dependen implícitamente del tipo del operador \"sizeof\" para ampliar el cálculo. [1] https://lore.kernel.org/lkml/<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/1369322c1de52c7b9b988b95c9903110a4566778", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5c4d4a83bf1a862d80c1efff1c6e3ce33b501e2e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7fe583c9bec10cd4b76231c51b37f3e4ca646e01", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/997bed0f3cde78a3e639d624985bf4a95cf767e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a7da7d01ac5ce9b369a1ac70e1197999cc6c9686", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b38c3e9e0adc01956cc3e5a52e4d3f92f79d88e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f65c4bbbd682b0877b669828b4e033b8d5d0a2dc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
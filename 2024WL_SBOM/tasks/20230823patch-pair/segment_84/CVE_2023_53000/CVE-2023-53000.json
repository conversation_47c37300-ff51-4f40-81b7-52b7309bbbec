{"cve_id": "CVE-2023-53000", "published_date": "2025-03-27T17:15:48.810", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetlink: prevent potential spectre v1 gadgets\n\nMost netlink attributes are parsed and validated from\n__nla_validate_parse() or validate_nla()\n\n    u16 type = nla_type(nla);\n\n    if (type == 0 || type > maxtype) {\n        /* error or continue */\n    }\n\n@type is then used as an array index and can be used\nas a Spectre v1 gadget.\n\narray_index_nospec() can be used to prevent leaking\ncontent of kernel memory to malicious users.\n\nThis should take care of vast majority of netlink uses,\nbut an audit is needed to take care of others where\nvalidation is not yet centralized in core netlink functions."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netlink: evitar posibles gadgets de Spectre v1 La mayoría de los atributos de netlink se analizan y validan desde __nla_validate_parse() o validate_nla() u16 type = nla_type(nla); if (type == 0 || type &gt; maxtype) { /* error or continue */ } @type se usa entonces como un índice de matriz y puede usarse como un gadget de Spectre v1. array_index_nospec() puede usarse para evitar la filtración de contenido de la memoria del kernel a usuarios maliciosos. Esto debería solucionar la gran mayoría de los usos de netlink, pero se necesita una auditoría para encargarse de otros donde la validación aún no está centralizada en las funciones principales de netlink."}], "references": [{"url": "https://git.kernel.org/stable/c/3e5082b1c66c7783fbcd79b5b178573230e528ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/41b74e95f297ac360ca7ed6bf200100717cb6c45", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/539ca5dcbc91134bbe2c45677811c31d8b030d2d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/992e4ff7116a77968039277b5d6aaa535c2f2184", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f0950402e8c76e7dcb08563f1b4e8000fbc62455", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52987", "published_date": "2025-03-27T17:15:46.050", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: SOF: ipc4-mtrace: prevent underflow in sof_ipc4_priority_mask_dfs_write()\n\nThe \"id\" comes from the user.  Change the type to unsigned to prevent\nan array underflow."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: SOF: ipc4-mtrace: previene el desbordamiento por debajo de la capacidad en sof_ipc4_priority_mask_dfs_write(). El \"id\" proviene del usuario. Cambie el tipo a unsigned para evitar un desbordamiento por debajo de la capacidad de la matriz."}], "references": [{"url": "https://git.kernel.org/stable/c/d52f34784e4e2f6e77671a9f104d8a69a3b5d24c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ea57680af47587397f5005d7758022441ed66d54", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
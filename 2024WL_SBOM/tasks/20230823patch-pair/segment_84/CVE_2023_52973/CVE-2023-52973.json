{"cve_id": "CVE-2023-52973", "published_date": "2025-03-27T17:15:44.283", "last_modified_date": "2025-04-01T15:40:21.587", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nvc_screen: move load of struct vc_data pointer in vcs_read() to avoid UAF\n\nAfter a call to console_unlock() in vcs_read() the vc_data struct can be\nfreed by vc_deallocate(). Because of that, the struct vc_data pointer\nload must be done at the top of while loop in vcs_read() to avoid a UAF\nwhen vcs_size() is called.\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> reported a UAF in vcs_size().\n\nBUG: KASAN: use-after-free in vcs_size (drivers/tty/vt/vc_screen.c:215)\nRead of size 4 at addr ffff8881137479a8 by task 4a005ed81e27e65/1537\n\nCPU: 0 PID: 1537 Comm: 4a005ed81e27e65 Not tainted 6.2.0-rc5 #1\nHardware name: Red Hat KVM, BIOS 1.15.0-2.module\nCall Trace:\n  <TASK>\n__asan_report_load4_noabort (mm/kasan/report_generic.c:350)\nvcs_size (drivers/tty/vt/vc_screen.c:215)\nvcs_read (drivers/tty/vt/vc_screen.c:415)\nvfs_read (fs/read_write.c:468 fs/read_write.c:450)\n...\n  </TASK>\n\nAllocated by task 1191:\n...\nkmalloc_trace (mm/slab_common.c:1069)\nvc_allocate (./include/linux/slab.h:580 ./include/linux/slab.h:720\n     drivers/tty/vt/vt.c:1128 drivers/tty/vt/vt.c:1108)\ncon_install (drivers/tty/vt/vt.c:3383)\ntty_init_dev (drivers/tty/tty_io.c:1301 drivers/tty/tty_io.c:1413\n     drivers/tty/tty_io.c:1390)\ntty_open (drivers/tty/tty_io.c:2080 drivers/tty/tty_io.c:2126)\nchrdev_open (fs/char_dev.c:415)\ndo_dentry_open (fs/open.c:883)\nvfs_open (fs/open.c:1014)\n...\n\nFreed by task 1548:\n...\nkfree (mm/slab_common.c:1021)\nvc_port_destruct (drivers/tty/vt/vt.c:1094)\ntty_port_destructor (drivers/tty/tty_port.c:296)\ntty_port_put (drivers/tty/tty_port.c:312)\nvt_disallocate_all (drivers/tty/vt/vt_ioctl.c:662 (discriminator 2))\nvt_ioctl (drivers/tty/vt/vt_ioctl.c:903)\ntty_ioctl (drivers/tty/tty_io.c:2776)\n...\n\nThe buggy address belongs to the object at ffff888113747800\n  which belongs to the cache kmalloc-1k of size 1024\nThe buggy address is located 424 bytes inside of\n  1024-byte region [ffff888113747800, ffff888113747c00)\n\nThe buggy address belongs to the physical page:\npage:00000000b3fe6c7c refcount:1 mapcount:0 mapping:0000000000000000\n     index:0x0 pfn:0x113740\nhead:00000000b3fe6c7c order:3 compound_mapcount:0 subpages_mapcount:0\n     compound_pincount:0\nanon flags: 0x17ffffc0010200(slab|head|node=0|zone=2|lastcpupid=0x1fffff)\nraw: 0017ffffc0010200 ffff888100042dc0 0000000000000000 dead000000000001\nraw: 0000000000000000 0000000000100010 00000001ffffffff 0000000000000000\npage dumped because: kasan: bad access detected\n\nMemory state around the buggy address:\n  ffff888113747880: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n  ffff888113747900: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n> ffff888113747980: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n                                   ^\n  ffff888113747a00: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n  ffff888113747a80: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n==================================================================\nDisabling lock debugging due to kernel taint"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: vc_screen: se desplaza la carga del puntero struct vc_data en vcs_read() para evitar un UAF. Tras una llamada a console_unlock() en vcs_read(), la estructura vc_data puede liberarse mediante vc_deallocate(). Por ello, la carga del puntero struct vc_data debe realizarse al principio del bucle while en vcs_read() para evitar un UAF al llamar a vcs_size(). Syzkaller reportó un UAF en vcs_size(). ERROR: KASAN: use-after-free in vcs_size (drivers/tty/vt/vc_screen.c:215) Read of size 4 at addr ffff8881137479a8 by task 4a005ed81e27e65/1537 CPU: 0 PID: 1537 Comm: 4a005ed81e27e65 Not tainted 6.2.0-rc5 #1 Hardware name: Red Hat KVM, BIOS 1.15.0-2.module Call Trace:  __asan_report_load4_noabort (mm/kasan/report_generic.c:350) vcs_size (drivers/tty/vt/vc_screen.c:215) vcs_read (drivers/tty/vt/vc_screen.c:415) vfs_read (fs/read_write.c:468 fs/read_write.c:450) ...  Allocated by task 1191: ... kmalloc_trace (mm/slab_common.c:1069) vc_allocate (./include/linux/slab.h:580 ./include/linux/slab.h:720 drivers/tty/vt/vt.c:1128 drivers/tty/vt/vt.c:1108) con_install (drivers/tty/vt/vt.c:3383) tty_init_dev (drivers/tty/tty_io.c:1301 drivers/tty/tty_io.c:1413 drivers/tty/tty_io.c:1390) tty_open (drivers/tty/tty_io.c:2080 drivers/tty/tty_io.c:2126) chrdev_open (fs/char_dev.c:415) do_dentry_open (fs/open.c:883) vfs_open (fs/open.c:1014) ... Freed by task 1548: ... kfree (mm/slab_common.c:1021) vc_port_destruct (drivers/tty/vt/vt.c:1094) tty_port_destructor (drivers/tty/tty_port.c:296) tty_port_put (drivers/tty/tty_port.c:312) vt_disallocate_all (drivers/tty/vt/vt_ioctl.c:662 (discriminator 2)) vt_ioctl (drivers/tty/vt/vt_ioctl.c:903) tty_ioctl (drivers/tty/tty_io.c:2776) ... The buggy address belongs to the object at ffff888113747800 which belongs to the cache kmalloc-1k of size 1024 The buggy address is located 424 bytes inside of 1024-byte region [ffff888113747800, ffff888113747c00) The buggy address belongs to the physical page: page:00000000b3fe6c7c refcount:1 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x113740 head:00000000b3fe6c7c order:3 compound_mapcount:0 subpages_mapcount:0 compound_pincount:0 anon flags: 0x17ffffc0010200(slab|head|node=0|zone=2|lastcpupid=0x1fffff) raw: 0017ffffc0010200 ffff888100042dc0 0000000000000000 dead000000000001 raw: 0000000000000000 0000000000100010 00000001ffffffff 0000000000000000 página volcada porque: kasan: mal acceso detectado Estado de memoria alrededor de la dirección con errores: ffff888113747880: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ffff888113747900: fb fb fb fb fb fb fb fb fb fb fb fb fb fb &gt; ffff888113747980: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ^ ffff888113747a00: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ffff888113747a80: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ================================================================== Deshabilitar la depuración de bloqueo debido a una contaminación del kernel"}], "references": [{"url": "https://git.kernel.org/stable/c/226fae124b2dac217ea5436060d623ff3385bc34", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/55515d7d8743b71b80bfe68e89eb9d92630626ab", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6332f52f44b9776568bf3c0b714ddfb0bb175e78", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8506f16aae9daf354e3732bcfd447e2a97f023df", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/af79ea9a2443016f64d8fd8d72020cc874f0e066", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d0332cbf53dad06a22189cc341391237f4ea6d9f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fc9e27f3ba083534b8bbf72ab0f5c810ffdc7d18", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
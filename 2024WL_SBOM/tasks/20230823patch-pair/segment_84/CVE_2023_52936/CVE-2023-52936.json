{"cve_id": "CVE-2023-52936", "published_date": "2025-03-27T17:15:43.450", "last_modified_date": "2025-04-15T16:00:40.953", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nkernel/irq/irqdomain.c: fix memory leak with using debugfs_lookup()\n\nWhen calling debugfs_lookup() the result must have dput() called on it,\notherwise the memory will leak over time.  To make things simpler, just\ncall debugfs_lookup_and_remove() instead which handles all of the logic\nat once."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: kernel/irq/irqdomain.c: se corrige la pérdida de memoria mediante el uso de debugfs_lookup(). Al llamar a debugfs_lookup(), se debe ejecutar dput() en el resultado; de lo contrario, la pérdida de memoria se producirá con el tiempo. Para simplificar, simplemente llame a debugfs_lookup_and_remove(), que gestiona toda la lógica a la vez."}], "references": [{"url": "https://git.kernel.org/stable/c/066ecbf1a53eb0b92b10c8df7808666be6ea5681", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cf1c917bf1c761a557b26410024e90057646c049", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d83d7ed260283560700d4034a80baad46620481b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
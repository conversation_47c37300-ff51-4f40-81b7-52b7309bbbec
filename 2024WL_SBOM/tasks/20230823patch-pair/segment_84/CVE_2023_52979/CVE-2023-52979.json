{"cve_id": "CVE-2023-52979", "published_date": "2025-03-27T17:15:45.057", "last_modified_date": "2025-06-25T20:59:20.347", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsquashfs: harden sanity check in squashfs_read_xattr_id_table\n\nWhile mounting a corrupted filesystem, a signed integer '*xattr_ids' can\nbecome less than zero.  This leads to the incorrect computation of 'len'\nand 'indexes' values which can cause null-ptr-deref in copy_bio_to_actor()\nor out-of-bounds accesses in the next sanity checks inside\nsquashfs_read_xattr_id_table().\n\nFound by Linux Verification Center (linuxtesting.org) with <PERSON>yzkaller."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: squashfs: endurecimiento de la comprobación de seguridad en squashfs_read_xattr_id_table. Al montar un sistema de archivos dañado, un entero con signo '*xattr_ids' puede ser menor que cero. Esto provoca el cálculo incorrecto de los valores de 'len' e 'indexes', lo que puede causar un valor nulo de referencia de referencia (ptr) en copy_bio_to_actor() o accesos fuera de límites en las siguientes comprobaciones de seguridad dentro de squashfs_read_xattr_id_table(). Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con Syzkaller."}], "references": [{"url": "https://git.kernel.org/stable/c/29e774dcb27116c06b9c57b1f1f14a1623738989", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}, {"url": "https://git.kernel.org/stable/c/72e544b1b28325fe78a4687b980871a7e4101f76", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}, {"url": "https://git.kernel.org/stable/c/b30a74f83265c24d1d0842c6c3928cd2e775a3fb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}, {"url": "https://git.kernel.org/stable/c/b7398efe24a965cf3937b716c0b1011c201c5d6e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}, {"url": "https://git.kernel.org/stable/c/cf5d6612092408157db6bb500c70bf6d67c40fbc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}, {"url": "https://git.kernel.org/stable/c/db76fc535fbdfbf29fd0b93e49627537ad794c8c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}, {"url": "https://git.kernel.org/stable/c/de2785aa3448d1ee7be3ab47fd4a873025f1b3d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Not Applicable"]}]}
{"cve_id": "CVE-2023-52986", "published_date": "2025-03-27T17:15:45.930", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf, sockmap: Check for any of tcp_bpf_prots when cloning a listener\n\nA listening socket linked to a sockmap has its sk_prot overridden. It\npoints to one of the struct proto variants in tcp_bpf_prots. The variant\ndepends on the socket's family and which sockmap programs are attached.\n\nA child socket cloned from a TCP listener initially inherits their sk_prot.\nBut before cloning is finished, we restore the child's proto to the\nlistener's original non-tcp_bpf_prots one. This happens in\ntcp_create_openreq_child -> tcp_bpf_clone.\n\nToday, in tcp_bpf_clone we detect if the child's proto should be restored\nby checking only for the TCP_BPF_BASE proto variant. This is not\ncorrect. The sk_prot of listening socket linked to a sockmap can point to\nto any variant in tcp_bpf_prots.\n\nIf the listeners sk_prot happens to be not the TCP_BPF_BASE variant, then\nthe child socket unintentionally is left if the inherited sk_prot by\ntcp_bpf_clone.\n\nThis leads to issues like infinite recursion on close [1], because the\nchild state is otherwise not set up for use with tcp_bpf_prot operations.\n\nAdjust the check in tcp_bpf_clone to detect all of tcp_bpf_prots variants.\n\nNote that it wouldn't be sufficient to check the socket state when\noverriding the sk_prot in tcp_bpf_update_proto in order to always use the\nTCP_BPF_BASE variant for listening sockets. Since commit\nb8b8315e39ff (\"bpf, sockmap: Remove unhash handler for BPF sockmap usage\")\nit is possible for a socket to transition to TCP_LISTEN state while already\nlinked to a sockmap, e.g. connect() -> insert into map ->\nconnect(AF_UNSPEC) -> listen().\n\n[1]: https://lore.kernel.org/all/<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf, sockmap: Comprueba si hay algún tcp_bpf_prots al clonar un oyente Un socket que escucha vinculado a un sockmap tiene su sk_prot anulado. Apunta a una de las variantes de struct proto en tcp_bpf_prots. La variante depende de la familia del socket y de los programas sockmap que estén adjuntos. Un socket hijo clonado de un oyente TCP hereda inicialmente su sk_prot. Pero antes de que finalice la clonación, restauramos el proto del hijo al original del oyente que no es tcp_bpf_prots. Esto sucede en tcp_create_openreq_child -&gt; tcp_bpf_clone. Hoy, en tcp_bpf_clone detectamos si el proto del hijo debe restaurarse comprobando solo la variante del proto TCP_BPF_BASE. Esto no es correcto. El sk_prot del socket de escucha vinculado a un mapa de socks puede apuntar a cualquier variante de tcp_bpf_prots. Si el sk_prot del socket de escucha no es la variante TCP_BPF_BASE, el socket hijo se abandona involuntariamente si el sk_prot heredado por tcp_bpf_clone lo impide. Esto genera problemas como la recursión infinita al cerrar [1], ya que el estado del hijo no está configurado para su uso con operaciones de tcp_bpf_prot. Ajuste la comprobación en tcp_bpf_clone para detectar todas las variantes de tcp_bpf_prots. Tenga en cuenta que no sería suficiente comprobar el estado del socket al sobrescribir el sk_prot en tcp_bpf_update_proto para usar siempre la variante TCP_BPF_BASE para los sockets de escucha. Desde el commit b8b8315e39ff (\"bpf, sockmap: eliminar el controlador unhash para el uso de sockmap BPF\"), es posible que un socket pase al estado TCP_LISTEN mientras ya está vinculado a un sockmap, por ejemplo, connect() -&gt; insert into map -&gt; connect(AF_UNSPEC) -&gt; listen(). [1]: https://lore.kernel.org/all/<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/12b0ec7c6953e1602957926439e5297095d7d065", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9bd6074e1872d22190a8da30e796cbf937d334f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c681d7a4ed3d360de0574f4d6b7305a8de8dc54f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ddce1e091757d0259107c6c0c7262df201de2b66", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-11847", "published_date": "2025-03-26T06:15:28.000", "last_modified_date": "2025-06-25T20:43:39.567", "descriptions": [{"lang": "en", "value": "The wp-svg-upload WordPress plugin through 1.0.0 does not sanitize SVG file contents, which enables users with at least the author role to SVG with malicious JavaScript to conduct Stored XSS attacks."}, {"lang": "es", "value": "El complemento wp-svg-upload de WordPress hasta la versión 1.0.0 no depura el contenido de los archivos SVG, lo que permite a los usuarios con al menos el rol de autor de SVG con JavaScript malicioso realizar ataques XSS almacenado."}], "references": [{"url": "https://wpscan.com/vulnerability/f57ecff2-0cff-40c7-b6e4-5b162b847d65/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2021-4454", "published_date": "2025-03-27T17:15:35.820", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncan: j1939: fix errant WARN_ON_ONCE in j1939_session_deactivate\n\nThe conclusion \"j1939_session_deactivate() should be called with a\nsession ref-count of at least 2\" is incorrect. In some concurrent\nscenarios, j1939_session_deactivate can be called with the session\nref-count less than 2. But there is not any problem because it\nwill check the session active state before session putting in\nj1939_session_deactivate_locked().\n\nHere is the concurrent scenario of the problem reported by syzbot\nand my reproduction log.\n\n        cpu0                            cpu1\n                                j1939_xtp_rx_eoma\nj1939_xtp_rx_abort_one\n                                j1939_session_get_by_addr [kref == 2]\nj1939_session_get_by_addr [kref == 3]\nj1939_session_deactivate [kref == 2]\nj1939_session_put [kref == 1]\n\t\t\t\tj1939_session_completed\n\t\t\t\tj1939_session_deactivate\n\t\t\t\tWARN_ON_ONCE(kref < 2)\n\n=====================================================\nWARNING: CPU: 1 PID: 21 at net/can/j1939/transport.c:1088 j1939_session_deactivate+0x5f/0x70\nCPU: 1 PID: 21 Comm: ksoftirqd/1 Not tainted 5.14.0-rc7+ #32\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS 1.13.0-1ubuntu1 04/01/2014\nRIP: 0010:j1939_session_deactivate+0x5f/0x70\nCall Trace:\n j1939_session_deactivate_activate_next+0x11/0x28\n j1939_xtp_rx_eoma+0x12a/0x180\n j1939_tp_recv+0x4a2/0x510\n j1939_can_recv+0x226/0x380\n can_rcv_filter+0xf8/0x220\n can_receive+0x102/0x220\n ? process_backlog+0xf0/0x2c0\n can_rcv+0x53/0xf0\n __netif_receive_skb_one_core+0x67/0x90\n ? process_backlog+0x97/0x2c0\n __netif_receive_skb+0x22/0x80"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: can: j1939: corrección del error WARN_ON_ONCE en j1939_session_deactivate. La conclusión \"j1939_session_deactivate() debe llamarse con un recuento de referencias de sesión de al menos 2\" es incorrecta. En algunos escenarios concurrentes, se puede llamar a j1939_session_deactivate con un recuento de referencias de sesión inferior a 2. Sin embargo, no hay problema, ya que comprueba el estado activo de la sesión antes de que se active j1939_session_deactivate_locked(). A continuación, se muestra el escenario concurrente del problema reportado por syzbot y mi registro de reproducción. cpu0 cpu1 j1939_xtp_rx_eoma j1939_xtp_rx_abort_one j1939_session_get_by_addr [kref == 2] j1939_session_get_by_addr [kref == 3] j1939_session_deactivate [kref == 2] j1939_session_put [kref == 1] j1939_session_completed j1939_session_deactivate WARN_ON_ONCE(kref &lt; 2)  ======================================================= ADVERTENCIA: CPU: 1 PID: 21 at net/can/j1939/transport.c:1088 j1939_session_deactivate+0x5f/0x70 CPU: 1 PID: 21 Comm: ksoftirqd/1 Not tainted 5.14.0-rc7+ #32 Hardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS 1.13.0-1ubuntu1 04/01/2014 RIP: 0010:j1939_session_deactivate+0x5f/0x70 Call Trace: j1939_session_deactivate_activate_next+0x11/0x28 j1939_xtp_rx_eoma+0x12a/0x180 j1939_tp_recv+0x4a2/0x510 j1939_can_recv+0x226/0x380 can_rcv_filter+0xf8/0x220 can_receive+0x102/0x220 ? process_backlog+0xf0/0x2c0 can_rcv+0x53/0xf0 __netif_receive_skb_one_core+0x67/0x90 ? process_backlog+0x97/0x2c0 __netif_receive_skb+0x22/0x80 "}], "references": [{"url": "https://git.kernel.org/stable/c/1740a1e45eee65099a92fb502e1e67e63aad277d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6950df42a03c9ac9290503ced3f371199cb68fa9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ab896775f98ff54b68512f345eed178bf961084", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b6d44072117bba057d50f7a2f96e5d070c65926d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d0553680f94c49bbe0e39eb50d033ba563b4212d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52994", "published_date": "2025-03-27T17:15:47.053", "last_modified_date": "2025-04-15T14:32:39.637", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nacpi: Fix suspend with Xen PV\n\nCommit f1e525009493 (\"x86/boot: Skip realmode init code when running as\nXen PV guest\") missed one code path accessing real_mode_header, leading\nto dereferencing NULL when suspending the system under Xen:\n\n    [  348.284004] PM: suspend entry (deep)\n    [  348.289532] Filesystems sync: 0.005 seconds\n    [  348.291545] Freezing user space processes ... (elapsed 0.000 seconds) done.\n    [  348.292457] OOM killer disabled.\n    [  348.292462] Freezing remaining freezable tasks ... (elapsed 0.104 seconds) done.\n    [  348.396612] printk: Suspending console(s) (use no_console_suspend to debug)\n    [  348.749228] PM: suspend devices took 0.352 seconds\n    [  348.769713] ACPI: EC: interrupt blocked\n    [  348.816077] BUG: kernel NULL pointer dereference, address: 000000000000001c\n    [  348.816080] #PF: supervisor read access in kernel mode\n    [  348.816081] #PF: error_code(0x0000) - not-present page\n    [  348.816083] PGD 0 P4D 0\n    [  348.816086] Oops: 0000 [#1] PREEMPT SMP NOPTI\n    [  348.816089] CPU: 0 PID: 6764 Comm: systemd-sleep Not tainted 6.1.3-1.fc32.qubes.x86_64 #1\n    [  348.816092] Hardware name: Star Labs StarBook/StarBook, BIOS 8.01 07/03/2022\n    [  348.816093] RIP: e030:acpi_get_wakeup_address+0xc/0x20\n\nFix that by adding an optional acpi callback allowing to skip setting\nthe wakeup address, as in the Xen PV case this will be handled by the\nhypervisor anyway."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: acpi: Fix suspend with Xen PV Commit f1e525009493 (\"x86/boot: Skip realmode init code when running as Xen PV guest\") omitió una ruta de código que accedía a real_mode_header, lo que provocó la desreferenciación de NULL al suspender el sistema bajo Xen: [348.284004] PM: entrada de suspensión (profunda) [348.289532] Sincronización de sistemas de archivos: 0,005 segundos [348.291545] Congelación de procesos de espacio de usuario... (transcurridos 0,000 segundos) hecho. [348.292457] OOM killer deshabilitado. [348.292462] Congelación de tareas congelables restantes... (transcurridos 0,104 segundos) hecho. [ 348.396612] printk: Suspendiendo consola(s) (use no_console_suspend para depurar) [ 348.749228] PM: suspender dispositivos tomó 0.352 segundos [ 348.769713] ACPI: EC: interrupción bloqueada [ 348.816077] ERROR: desreferencia de puntero NULL del kernel, dirección: 000000000000001c [ 348.816080] #PF: acceso de lectura del supervisor en modo kernel [ 348.816081] #PF: error_code(0x0000) - página no presente [ 348.816083] PGD 0 P4D 0 [ 348.816086] Oops: 0000 [#1] PREEMPT SMP NOPTI [ 348.816089] CPU: 0 PID: 6764 Comm: systemd-sleep No contaminado 6.1.3-1.fc32.qubes.x86_64 #1 [ 348.816092] Nombre del hardware: Star Labs StarBook/StarBook, BIOS 8.01 07/03/2022 [ 348.816093] RIP: e030:acpi_get_wakeup_address+0xc/0x20 Corrija esto agregando una devolución de llamada acpi opcional que permita omitir la configuración de la dirección de activación, ya que en el caso de Xen PV esto lo manejará el hipervisor de todos modos."}], "references": [{"url": "https://git.kernel.org/stable/c/b96903b7fc8c82ddfd92df4cdd83db3e567da0a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fe0ba8c23f9a35b0307eb662f16dd3a75fcdae41", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
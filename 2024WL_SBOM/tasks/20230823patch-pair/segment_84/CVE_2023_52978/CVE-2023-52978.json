{"cve_id": "CVE-2023-52978", "published_date": "2025-03-27T17:15:44.923", "last_modified_date": "2025-04-15T14:49:22.897", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv: kprobe: Fixup kernel panic when probing an illegal position\n\nThe kernel would panic when probed for an illegal position. eg:\n\n(CONFIG_RISCV_ISA_C=n)\n\necho 'p:hello kernel_clone+0x16 a0=%a0' >> kprobe_events\necho 1 > events/kprobes/hello/enable\ncat trace\n\n<PERSON><PERSON> panic - not syncing: stack-protector: Kernel stack\nis corrupted in: __do_sys_newfstatat+0xb8/0xb8\nCPU: 0 PID: 111 Comm: sh Not tainted\n6.2.0-rc1-00027-g2d398fe49a4d #490\nHardware name: riscv-virtio,qemu (DT)\nCall Trace:\n[<ffffffff80007268>] dump_backtrace+0x38/0x48\n[<ffffffff80c5e83c>] show_stack+0x50/0x68\n[<ffffffff80c6da28>] dump_stack_lvl+0x60/0x84\n[<ffffffff80c6da6c>] dump_stack+0x20/0x30\n[<ffffffff80c5ecf4>] panic+0x160/0x374\n[<ffffffff80c6db94>] generic_handle_arch_irq+0x0/0xa8\n[<ffffffff802deeb0>] sys_newstat+0x0/0x30\n[<ffffffff800158c0>] sys_clone+0x20/0x30\n[<ffffffff800039e8>] ret_from_syscall+0x0/0x4\n---[ end Kernel panic - not syncing: stack-protector:\nKernel stack is corrupted in: __do_sys_newfstatat+0xb8/0xb8 ]---\n\nThat is because the kprobe's ebreak instruction broke the kernel's\noriginal code. The user should guarantee the correction of the probe\nposition, but it couldn't make the kernel panic.\n\nThis patch adds arch_check_kprobe in arch_prepare_kprobe to prevent an\nillegal position (Such as the middle of an instruction)."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv: kprobe: Corrección del pánico del kernel al sondear una posición ilegal El kernel entraría en pánico cuando se sondeara una posición ilegal. p. ej.: (CONFIG_RISCV_ISA_C=n) echo 'p:hello kernel_clone+0x16 a0=%a0' &gt;&gt; kprobe_events echo 1 &gt; events/kprobes/hello/enable cat trace Kern<PERSON> panic - not syncing: stack-protector: Kernel stack is corrupted in: __do_sys_newfstatat+0xb8/0xb8 CPU: 0 PID: 111 Comm: sh Not tainted 6.2.0-rc1-00027-g2d398fe49a4d #490 Hardware name: riscv-virtio,qemu (DT) Call Trace: [] dump_backtrace+0x38/0x48 [] show_stack+0x50/0x68 [] dump_stack_lvl+0x60/0x84 [] dump_stack+0x20/0x30 [] panic+0x160/0x374 [] generic_handle_arch_irq+0x0/0xa8 [] sys_newstat+0x0/0x30 [] sys_clone+0x20/0x30 [] ret_from_syscall+0x0/0x4 ---[ end Kernel panic - not syncing: stack-protector: Kernel stack is corrupted in: __do_sys_newfstatat+0xb8/0xb8 ]--- Esto se debe a que la instrucción ebreak de kprobe rompió el código original del núcleo. El usuario debería garantizar la corrección de la posición de la sonda, pero no pudo provocar un pánico en el núcleo. Este parche añade arch_check_kprobe en arch_prepare_kprobe para evitar una posición ilegal (como la mitad de una instrucción)."}], "references": [{"url": "https://git.kernel.org/stable/c/04a73558209554da17f46490ec4faaaf1b2bab68", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/12316538b1d193064109ce1a28fc9bacd43950de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/87f48c7ccc73afc78630530d9af51f458f58cab8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
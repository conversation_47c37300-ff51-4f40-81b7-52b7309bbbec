{"cve_id": "CVE-2023-53021", "published_date": "2025-03-27T17:15:51.580", "last_modified_date": "2025-04-01T15:40:10.120", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/sched: sch_taprio: fix possible use-after-free\n\nsyzbot reported a nasty crash [1] in net_tx_action() which\nmade little sense until we got a repro.\n\nThis repro installs a taprio qdisc, but providing an\ninvalid TCA_RATE attribute.\n\nqdisc_create() has to destroy the just initialized\ntaprio qdisc, and taprio_destroy() is called.\n\nHowever, the hrtimer used by tap<PERSON> had already fired,\ntherefore advance_sched() called __netif_schedule().\n\nThen net_tx_action was trying to use a destroyed qdisc.\n\nWe can not undo the __netif_schedule(), so we must wait\nuntil one cpu serviced the qdisc before we can proceed.\n\nMany thanks to <PERSON> for his help.\n\n[1]\nBUG: KMSAN: uninit-value in queued_spin_trylock include/asm-generic/qspinlock.h:94 [inline]\nBUG: KMSAN: uninit-value in do_raw_spin_trylock include/linux/spinlock.h:191 [inline]\nBUG: KMSAN: uninit-value in __raw_spin_trylock include/linux/spinlock_api_smp.h:89 [inline]\nBUG: KMSAN: uninit-value in _raw_spin_trylock+0x92/0xa0 kernel/locking/spinlock.c:138\n queued_spin_trylock include/asm-generic/qspinlock.h:94 [inline]\n do_raw_spin_trylock include/linux/spinlock.h:191 [inline]\n __raw_spin_trylock include/linux/spinlock_api_smp.h:89 [inline]\n _raw_spin_trylock+0x92/0xa0 kernel/locking/spinlock.c:138\n spin_trylock include/linux/spinlock.h:359 [inline]\n qdisc_run_begin include/net/sch_generic.h:187 [inline]\n qdisc_run+0xee/0x540 include/net/pkt_sched.h:125\n net_tx_action+0x77c/0x9a0 net/core/dev.c:5086\n __do_softirq+0x1cc/0x7fb kernel/softirq.c:571\n run_ksoftirqd+0x2c/0x50 kernel/softirq.c:934\n smpboot_thread_fn+0x554/0x9f0 kernel/smpboot.c:164\n kthread+0x31b/0x430 kernel/kthread.c:376\n ret_from_fork+0x1f/0x30\n\nUninit was created at:\n slab_post_alloc_hook mm/slab.h:732 [inline]\n slab_alloc_node mm/slub.c:3258 [inline]\n __kmalloc_node_track_caller+0x814/0x1250 mm/slub.c:4970\n kmalloc_reserve net/core/skbuff.c:358 [inline]\n __alloc_skb+0x346/0xcf0 net/core/skbuff.c:430\n alloc_skb include/linux/skbuff.h:1257 [inline]\n nlmsg_new include/net/netlink.h:953 [inline]\n netlink_ack+0x5f3/0x12b0 net/netlink/af_netlink.c:2436\n netlink_rcv_skb+0x55d/0x6c0 net/netlink/af_netlink.c:2507\n rtnetlink_rcv+0x30/0x40 net/core/rtnetlink.c:6108\n netlink_unicast_kernel net/netlink/af_netlink.c:1319 [inline]\n netlink_unicast+0xf3b/0x1270 net/netlink/af_netlink.c:1345\n netlink_sendmsg+0x1288/0x1440 net/netlink/af_netlink.c:1921\n sock_sendmsg_nosec net/socket.c:714 [inline]\n sock_sendmsg net/socket.c:734 [inline]\n ____sys_sendmsg+0xabc/0xe90 net/socket.c:2482\n ___sys_sendmsg+0x2a1/0x3f0 net/socket.c:2536\n __sys_sendmsg net/socket.c:2565 [inline]\n __do_sys_sendmsg net/socket.c:2574 [inline]\n __se_sys_sendmsg net/socket.c:2572 [inline]\n __x64_sys_sendmsg+0x367/0x540 net/socket.c:2572\n do_syscall_x64 arch/x86/entry/common.c:50 [inline]\n do_syscall_64+0x3d/0xb0 arch/x86/entry/common.c:80\n entry_SYSCALL_64_after_hwframe+0x63/0xcd\n\nCPU: 0 PID: 13 Comm: ksoftirqd/0 Not tainted 6.0.0-rc2-syzkaller-47461-gac3859c02d7f #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 07/22/2022"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/sched: sch_taprio: corrección de posible error de use-after-free syzbot reportó un fallo grave [1] en net_tx_action() que no tenía sentido hasta que se reprodujo. Esta reproducción instala una qdisc de Taprio, pero proporciona un atributo TCA_RATE no válido. qdisc_create() debe destruir la qdisc de Taprio recién inicializada, y se llama a taprio_destroy(). Sin embargo, el temporizador hr usado por Taprio ya se había ejecutado, por lo que advance_sched() llamó a __netif_schedule(). Entonces, net_tx_action intentaba usar una qdisc destruida. No podemos deshacer __netif_schedule(), por lo que debemos esperar a que una CPU haya dado servicio a la qdisc antes de continuar. Muchas gracias a <PERSON> por su ayuda. [1] ERROR: KMSAN: valor no inicializado en queued_spin_trylock include/asm-generic/qspinlock.h:94 [en línea] ERROR: KMSAN: valor no inicializado en do_raw_spin_trylock include/linux/spinlock.h:191 [en línea] ERROR: KMSAN: valor no inicializado en __raw_spin_trylock include/linux/spinlock_api_smp.h:89 [en línea] ERROR: KMSAN: valor no inicializado en _raw_spin_trylock+0x92/0xa0 kernel/locking/spinlock.c:138 queued_spin_trylock include/asm-generic/qspinlock.h:94 [en línea] do_raw_spin_trylock include/linux/spinlock.h:191 [en línea] __raw_spin_trylock incluir/linux/spinlock_api_smp.h:89 [en línea] _raw_spin_trylock+0x92/0xa0 kernel/locking/spinlock.c:138 spin_trylock incluir/linux/spinlock.h:359 [en línea] qdisc_run_begin incluir/net/sch_generic.h:187 [en línea] qdisc_run+0xee/0x540 incluir/net/pkt_sched.h:125 net_tx_action+0x77c/0x9a0 net/core/dev.c:5086 __do_softirq+0x1cc/0x7fb kernel/softirq.c:571 run_ksoftirqd+0x2c/0x50 kernel/softirq.c:934 smpboot_thread_fn+0x554/0x9f0 kernel/smpboot.c:164 kthread+0x31b/0x430 kernel/kthread.c:376 ret_from_fork+0x1f/0x30 Uninit se creó en: slab_post_alloc_hook mm/slab.h:732 [en línea] slab_alloc_node mm/slub.c:3258 [en línea] __kmalloc_node_track_caller+0x814/0x1250 mm/slub.c:4970 kmalloc_reserve net/core/skbuff.c:358 [en línea] __alloc_skb+0x346/0xcf0 net/core/skbuff.c:430 alloc_skb include/linux/skbuff.h:1257 [en línea] nlmsg_new include/net/netlink.h:953 [en línea] netlink_ack+0x5f3/0x12b0 net/netlink/af_netlink.c:2436 netlink_rcv_skb+0x55d/0x6c0 net/netlink/af_netlink.c:2507 rtnetlink_rcv+0x30/0x40 net/core/rtnetlink.c:6108 netlink_unicast_kernel net/netlink/af_netlink.c:1319 [en línea] netlink_unicast+0xf3b/0x1270 net/netlink/af_netlink.c:1345 netlink_sendmsg+0x1288/0x1440 net/netlink/af_netlink.c:1921 sock_sendmsg_nosec net/socket.c:714 [en línea] sock_sendmsg net/socket.c:734 [en línea] ____sys_sendmsg+0xabc/0xe90 net/socket.c:2482 ___sys_sendmsg+0x2a1/0x3f0 net/socket.c:2536 __sys_sendmsg net/socket.c:2565 [en línea] __do_sys_sendmsg net/socket.c:2574 [en línea] __se_sys_sendmsg net/socket.c:2572 [en línea] __x64_sys_sendmsg+0x367/0x540 net/socket.c:2572 do_syscall_x64 arch/x86/entry/common.c:50 [en línea] do_syscall_64+0x3d/0xb0 arch/x86/entry/common.c:80 entry_SYSCALL_64_after_hwframe+0x63/0xcd CPU: 0 PID: 13 Comm: ksoftirqd/0 No contaminado 6.0.0-rc2-syzkaller-47461-gac3859c02d7f #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 22/07/2022"}], "references": [{"url": "https://git.kernel.org/stable/c/1200388a0b1c3c6fda48d4d2143db8f7e4ef5348", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3a415d59c1dbec9d772dbfab2d2520d98360caae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c53acbf2facfdfabdc6e6984a1a38f5d38b606a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c60fe70078d6e515f424cb868d07e00411b27fbc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d3b2d2820a005e43855fa71b80c4a4b194201c60", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
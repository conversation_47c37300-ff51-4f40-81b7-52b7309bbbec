{"cve_id": "CVE-2023-52932", "published_date": "2025-03-27T17:15:42.930", "last_modified_date": "2025-04-15T16:00:32.137", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/swapfile: add cond_resched() in get_swap_pages()\n\nThe softlockup still occurs in get_swap_pages() under memory pressure.  64\nCPU cores, 64GB memory, and 28 zram devices, the disksize of each zram\ndevice is 50MB with same priority as si.  Use the stress-ng tool to\nincrease memory pressure, causing the system to oom frequently.\n\nThe plist_for_each_entry_safe() loops in get_swap_pages() could reach tens\nof thousands of times to find available space (extreme case:\ncond_resched() is not called in scan_swap_map_slots()).  Let's add\ncond_resched() into get_swap_pages() when failed to find available space\nto avoid softlockup."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/swapfile: añadir cond_resched() en get_swap_pages() El bloqueo suave todavía ocurre en get_swap_pages() bajo presión de memoria. 64 núcleos de CPU, 64 GB de memoria y 28 dispositivos zram, el tamaño del disco de cada dispositivo zram es de 50 MB con la misma prioridad que si. Utilice la herramienta stress-ng para aumentar la presión de memoria, lo que hace que el sistema se sobrecargue con frecuencia. Los bucles plist_for_each_entry_safe() en get_swap_pages() podrían alcanzar decenas de miles de veces para encontrar espacio disponible (caso extremo: cond_resched() no se llama en scan_swap_map_slots()). Agreguemos cond_resched() a get_swap_pages() cuando no se pueda encontrar espacio disponible para evitar el bloqueo suave."}], "references": [{"url": "https://git.kernel.org/stable/c/29f0349c5c76b627fe06b87d4b13fa03a6ce8e64", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/30187be29052bba9203b0ae2bdd815e0bc2faaab", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/387217b97e99699c34e6d95ce2b91b327fcd853e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/49178d4d61e78aed8c837dfeea8a450700f196e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5dbe1ebd56470d03b78fc31491a9e4d433106ef2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7717fc1a12f88701573f9ed897cc4f6699c661e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d49c85a1913385eed46dd16a25ad0928253767f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
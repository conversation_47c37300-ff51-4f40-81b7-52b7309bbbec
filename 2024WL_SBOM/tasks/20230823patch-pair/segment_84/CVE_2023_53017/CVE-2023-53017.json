{"cve_id": "CVE-2023-53017", "published_date": "2025-03-27T17:15:51.103", "last_modified_date": "2025-04-15T19:41:36.207", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: hci_sync: fix memory leak in hci_update_adv_data()\n\nWhen hci_cmd_sync_queue() failed in hci_update_adv_data(), inst_ptr is\nnot freed, which will cause memory leak, convert to use ERR_PTR/PTR_ERR\nto pass the instance to callback so no memory needs to be allocated."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: hci_sync: corrige pérdida de memoria en hci_update_adv_data() Cuando hci_cmd_sync_queue() falla en hci_update_adv_data(), inst_ptr no se libera, lo que provocará una pérdida de memoria. Convierta para usar ERR_PTR/PTR_ERR para pasar la instancia a la devolución de llamada, de modo que no sea necesario asignar memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/1ed8b37cbaf14574c779064ef1372af62e8ba6aa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8ac6043bd3e5b58d30f50737aedc2e58e8087ad5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
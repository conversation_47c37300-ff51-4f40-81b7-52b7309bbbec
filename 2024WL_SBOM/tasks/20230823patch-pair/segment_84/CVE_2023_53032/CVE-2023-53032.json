{"cve_id": "CVE-2023-53032", "published_date": "2025-03-27T17:15:52.997", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetfilter: ipset: Fix overflow before widen in the bitmap_ip_create() function.\n\nWhen first_ip is 0, last_ip is 0xFFFFFFFF, and netmask is 31, the value of\nan arithmetic expression 2 << (netmask - mask_bits - 1) is subject\nto overflow due to a failure casting operands to a larger data type\nbefore performing the arithmetic.\n\nNote that it's harmless since the value will be checked at the next step.\n\nFound by InfoTeCS on behalf of Linux Verification Center\n(linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netfilter: ipset: Se corrige el desbordamiento antes de la función \"widen\" en la función bitmap_ip_create(). Cuando first_ip es 0, last_ip es 0xFFFFFFFF y netmask es 31, el valor de una expresión aritmética 2 &lt;&lt; (netmask - mask_bits - 1) está sujeto a desbordamiento debido a un error al convertir operandos a un tipo de dato mayor antes de realizar la operación aritmética. Tenga en cuenta que esto es inofensivo, ya que el valor se comprobará en el siguiente paso. Encontrado por InfoTeCS en nombre del Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/4e6a70fd840400e3a2e784a6673968a3eb2431c0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/511cf17b2447fc41cfef8d71936e1fa53e395c1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ea4b476cea1b7d461d16dda25ca3c7e616e2d15", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dfd834ccc1b88bbbab81b9046a3a539dd0c2d14f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e137d9bb26bd85ce07323a38e38ceb0b160db841", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e88865876d47c790be0d5e23973499d75d034364", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/feefb33eefa166fc3e0fd17547b0bc0cb3baced9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
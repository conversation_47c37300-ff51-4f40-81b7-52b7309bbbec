{"cve_id": "CVE-2023-53006", "published_date": "2025-03-27T17:15:49.543", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncifs: Fix oops due to uncleared server->smbd_conn in reconnect\n\nIn smbd_destroy(), clear the server->smbd_conn pointer after freeing the\nsmbd_connection struct that it points to so that reconnection doesn't get\nconfused."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cifs: soluciona el error debido a un servidor-&gt;smbd_conn sin borrar en la reconexión. En smbd_destroy(), borra el puntero server-&gt;smbd_conn después de liberar la estructura smbd_connection a la que apunta para que la reconexión no se confunda."}], "references": [{"url": "https://git.kernel.org/stable/c/4b83bc6f87eedab4599b0123e572a422689444be", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5109607a4ece7cd8536172bf7549eb4dce1f3576", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/91be54849d5392050f5b847b42bd5e6221551ac8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a9640c0b268405f2540e8203a545e930ea88bb7d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b7ab9161cf5ddc42a288edf9d1a61f3bdffe17c7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e037baee16e0b9ace7e730888fcae9cec11daff2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53011", "published_date": "2025-03-27T17:15:50.157", "last_modified_date": "2025-04-14T20:51:37.577", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: stmmac: enable all safety features by default\n\nIn the original implementation of dwmac5\ncommit 8bf993a5877e (\"net: stmmac: Add support for DWMAC5 and implement Safety Features\")\nall safety features were enabled by default.\n\nLater it seems some implementations didn't have support for all the\nfeatures, so in\ncommit 5ac712dcdfef (\"net: stmmac: enable platform specific safety features\")\nthe safety_feat_cfg structure was added to the callback and defined for\nsome platforms to selectively enable these safety features.\n\nThe problem is that only certain platforms were given that software\nsupport. If the automotive safety package bit is set in the hardware\nfeatures register the safety feature callback is called for the platform,\nand for platforms that didn't get a safety_feat_cfg defined this results\nin the following NULL pointer dereference:\n\n[    7.933303] Call trace:\n[    7.935812]  dwmac5_safety_feat_config+0x20/0x170 [stmmac]\n[    7.941455]  __stmmac_open+0x16c/0x474 [stmmac]\n[    7.946117]  stmmac_open+0x38/0x70 [stmmac]\n[    7.950414]  __dev_open+0x100/0x1dc\n[    7.954006]  __dev_change_flags+0x18c/0x204\n[    7.958297]  dev_change_flags+0x24/0x6c\n[    7.962237]  do_setlink+0x2b8/0xfa4\n[    7.965827]  __rtnl_newlink+0x4ec/0x840\n[    7.969766]  rtnl_newlink+0x50/0x80\n[    7.973353]  rtnetlink_rcv_msg+0x12c/0x374\n[    7.977557]  netlink_rcv_skb+0x5c/0x130\n[    7.981500]  rtnetlink_rcv+0x18/0x2c\n[    7.985172]  netlink_unicast+0x2e8/0x340\n[    7.989197]  netlink_sendmsg+0x1a8/0x420\n[    7.993222]  ____sys_sendmsg+0x218/0x280\n[    7.997249]  ___sys_sendmsg+0xac/0x100\n[    8.001103]  __sys_sendmsg+0x84/0xe0\n[    8.004776]  __arm64_sys_sendmsg+0x24/0x30\n[    8.008983]  invoke_syscall+0x48/0x114\n[    8.012840]  el0_svc_common.constprop.0+0xcc/0xec\n[    8.017665]  do_el0_svc+0x38/0xb0\n[    8.021071]  el0_svc+0x2c/0x84\n[    8.024212]  el0t_64_sync_handler+0xf4/0x120\n[    8.028598]  el0t_64_sync+0x190/0x194\n\nGo back to the original behavior, if the automotive safety package\nis found to be supported in hardware enable all the features unless\nsafety_feat_cfg is passed in saying this particular platform only\nsupports a subset of the features."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: stmmac: habilitar todas las funciones de seguridad por defecto. En la implementación original del commit 8bf993a5877e de dwmac5 (\"net: stmmac: Añadir soporte para DWMAC5 e implementar funciones de seguridad\"), todas las funciones de seguridad estaban habilitadas por defecto. Posteriormente, parece que algunas implementaciones no eran compatibles con todas las funciones, por lo que en el commit 5ac712dcdfef (\"net: stmmac: habilitar funciones de seguridad específicas de la plataforma\") se añadió la estructura safety_feat_cfg a la devolución de llamada y se definió para algunas plataformas con el fin de habilitar selectivamente estas funciones de seguridad. El problema radica en que solo ciertas plataformas recibieron este soporte de software. Si el bit del paquete de seguridad automotriz está configurado en el registro de características de hardware, se llama a la devolución de llamada de la característica de seguridad para la plataforma y, para las plataformas que no tienen un safety_feat_cfg definido, esto da como resultado la siguiente desreferencia de puntero NULL: [7.933303] Rastreo de llamada: [ 7.935812] dwmac5_safety_feat_config+0x20/0x170 [stmmac] [ 7.941455] __stmmac_open+0x16c/0x474 [stmmac] [ 7.946117] stmmac_open+0x38/0x70 [stmmac] [ 7.950414] __dev_open+0x100/0x1dc [ 7.954006] __dev_change_flags+0x18c/0x204 [ 7.958297] dev_change_flags+0x24/0x6c [ 7.962237] do_setlink+0x2b8/0xfa4 [ 7.965827] __rtnl_newlink+0x4ec/0x840 [ 7.969766] rtnl_newlink+0x50/0x80 [ 7.973353] rtnetlink_rcv_msg+0x12c/0x374 [ 7.977557] netlink_rcv_skb+0x5c/0x130 [ 7.981500] rtnetlink_rcv+0x18/0x2c [ 7.985172] netlink_unicast+0x2e8/0x340 [ 7.989197] netlink_sendmsg+0x1a8/0x420 [ 7.993222] ____sys_sendmsg+0x218/0x280 [ 7.997249] ___sys_sendmsg+0xac/0x100 [ 8.001103] __sys_sendmsg+0x84/0xe0 [ 8.004776] __arm64_sys_sendmsg+0x24/0x30 [ 8.008983] invoke_syscall+0x48/0x114 [ 8.012840] el0_svc_common.constprop.0+0xcc/0xec [ 8.017665] do_el0_svc+0x38/0xb0 [ 8.021071] el0_svc+0x2c/0x84 [ 8.024212] el0t_64_sync_handler+0xf4/0x120 [ 8.028598] el0t_64_sync+0x190/0x194 Regrese al comportamiento original, si se descubre que el paquete de seguridad automotriz es compatible con el hardware, habilite todas las funciones a menos que se pase safety_feat_cfg indicando que esta plataforma en particular solo admite un subconjunto de las funciones."}], "references": [{"url": "https://git.kernel.org/stable/c/120b8e527e07c65de7f2b9018dcd9d17e66f2427", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/aebf7e62708ba706ee7bf484c9023b15c214e92a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fdfc76a116b5e9d3e98e6c96fe83b42d011d21d4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
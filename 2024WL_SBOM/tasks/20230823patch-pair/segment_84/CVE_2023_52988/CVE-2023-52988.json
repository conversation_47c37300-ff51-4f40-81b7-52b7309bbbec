{"cve_id": "CVE-2023-52988", "published_date": "2025-03-27T17:15:46.170", "last_modified_date": "2025-04-15T14:15:02.110", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nALSA: hda/via: Avoid potential array out-of-bound in add_secret_dac_path()\n\nsnd_hda_get_connections() can return a negative error code.\nIt may lead to accessing 'conn' array at a negative index.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ALSA: hda/via: Evitar una posible matriz fuera de los límites en add_secret_dac_path(). snd_hda_get_connections() puede devolver un código de error negativo. Esto puede provocar el acceso a la matriz 'conn' con un índice negativo. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/1b9256c96220bcdba287eeeb90e7c910c77f8c46", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2b557fa635e7487f638c0f030c305870839eeda2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/437e50ef6290ac835d526d0e45f466a0aa69ba1b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6e1f586ddec48d71016b81acf68ba9f49ca54db8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b9cee506da2b7920b5ea02ccd8e78a907d0ee7aa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d6870f3800dbb212ae8433183ee82f566d067c6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f011360ad234a07cb6fbcc720fff646a93a9f0d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
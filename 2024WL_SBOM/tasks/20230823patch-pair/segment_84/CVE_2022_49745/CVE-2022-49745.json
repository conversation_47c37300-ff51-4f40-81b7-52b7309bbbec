{"cve_id": "CVE-2022-49745", "published_date": "2025-03-27T17:15:39.220", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfpga: m10bmc-sec: Fix probe rollback\n\nHandle probe error rollbacks properly to avoid leaks."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: fpga: m10bmc-sec: Corregir reversión de errores de sonda gestionar adecuadamente las reversiones de errores de sonda para evitar fugas."}], "references": [{"url": "https://git.kernel.org/stable/c/60ce26d10e5850f33cc76fce52f5377045e75a15", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/74cff472d3d66db13b5ef64f40dfa42383f71ff7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
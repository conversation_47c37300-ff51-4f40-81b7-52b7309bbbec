{"cve_id": "CVE-2023-52928", "published_date": "2025-03-27T17:15:42.230", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: Skip invalid kfunc call in backtrack_insn\n\nThe verifier skips invalid kfunc call in check_kfunc_call(), which\nwould be captured in fixup_kfunc_call() if such insn is not eliminated\nby dead code elimination. However, this can lead to the following\nwarning in backtrack_insn(), also see [1]:\n\n  ------------[ cut here ]------------\n  verifier backtracking bug\n  WARNING: CPU: 6 PID: 8646 at kernel/bpf/verifier.c:2756 backtrack_insn\n  kernel/bpf/verifier.c:2756\n\t__mark_chain_precision kernel/bpf/verifier.c:3065\n\tmark_chain_precision kernel/bpf/verifier.c:3165\n\tadjust_reg_min_max_vals kernel/bpf/verifier.c:10715\n\tcheck_alu_op kernel/bpf/verifier.c:10928\n\tdo_check kernel/bpf/verifier.c:13821 [inline]\n\tdo_check_common kernel/bpf/verifier.c:16289\n  [...]\n\nSo make backtracking conservative with this by returning ENOTSUPP.\n\n  [1] https://lore.kernel.org/bpf/CACkBjsaXNceR8ZjkLG=dT3P=4A8SBsg0Z5h5PWLryF5=ghKq=<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: Omite llamada kfunc no válida en backtrack_insn El verificador omite llamada kfunc no válida en check_kfunc_call(), que se capturaría en fixup_kfunc_call() si dicha insn no se elimina mediante la eliminación de código muerto. Sin embargo, esto puede generar la siguiente advertencia en backtrack_insn(), vea también [1]: ------------[ cortar aquí ]------------ error de retroceso del verificador ADVERTENCIA: CPU: 6 PID: 8646 en kernel/bpf/verifier.c:2756 backtrack_insn kernel/bpf/verifier.c:2756 __mark_chain_precision kernel/bpf/verifier.c:3065 mark_chain_precision kernel/bpf/verifier.c:3165 adjust_reg_min_max_vals kernel/bpf/verifier.c:10715 check_alu_op kernel/bpf/verifier.c:10928 do_check kernel/bpf/verifier.c:13821 [en línea] do_check_common kernel/bpf/verifier.c:16289 [...] Así que haga que el retroceso sea conservador con esto devolviendo ENOTSUPP. [1] https://lore.kernel.org/bpf/CACkBjsaXNceR8ZjkLG=dT3P=4A8SBsg0Z5h5PWLryF5=ghKq=<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/6e2fac197de2c4c041bdd8982cffb104689113f1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/74eec8266f37aff609db6a2f2b093e56a11c28c4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3178e8a434b58678d99257c0387810a24042fb6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52980", "published_date": "2025-03-27T17:15:45.183", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblock: ublk: extending queue_size to fix overflow\n\nWhen validating drafted SPDK ublk target, in a case that\nassigning large queue depth to multiqueue ublk device,\nublk target would run into a weird incorrect state. During\nrounds of review and debug, An overflow bug was found\nin ublk driver.\n\nIn ublk_cmd.h, UBLK_MAX_QUEUE_DEPTH is 4096 which means\neach ublk queue depth can be set as large as 4096. But\nwhen setting qd for a ublk device,\nsizeof(struct ublk_queue) + depth * sizeof(struct ublk_io)\nwill be larger than 65535 if qd is larger than 2728.\nThen queue_size is overflowed, and ublk_get_queue()\nreferences a wrong pointer position. The wrong content of\nublk_queue elements will lead to out-of-bounds memory\naccess.\n\nExtend queue_size in ublk_device as \"unsigned int\"."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bloque: ublk: extensión de queue_size para corregir el desbordamiento Al validar el borrador del objetivo ublk de SPDK, en un caso en el que se asigna una gran profundidad de cola al dispositivo ublk de múltiples colas, el objetivo ublk se ejecutaría en un estado incorrecto extraño. Durante las rondas de revisión y depuración, se encontró un error de desbordamiento en el controlador ublk. En ublk_cmd.h, UBLK_MAX_QUEUE_DEPTH es 4096, lo que significa que cada profundidad de cola ublk se puede configurar tan grande como 4096. Pero al configurar qd para un dispositivo ublk, sizeof(struct ublk_queue) + profundidad * sizeof(struct ublk_io) será mayor que 65535 si qd es mayor que 2728. Entonces queue_size se desborda y ublk_get_queue() hace referencia a una posición de puntero incorrecta. El contenido incorrecto de los elementos ublk_queue provocará un acceso a memoria fuera de los límites. Extienda queue_size en ublk_device como \"unsigned int\"."}], "references": [{"url": "https://git.kernel.org/stable/c/29baef789c838bd5c02f50c88adbbc6b955aaf61", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee1e3fe4b4579f856997190a00ea4db0307b4332", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
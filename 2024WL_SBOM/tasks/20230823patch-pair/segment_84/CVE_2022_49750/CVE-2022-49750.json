{"cve_id": "CVE-2022-49750", "published_date": "2025-03-27T17:15:39.850", "last_modified_date": "2025-04-14T20:16:13.217", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncpufreq: CPPC: Add u64 casts to avoid overflowing\n\nThe fields of the _CPC object are unsigned 32-bits values.\nTo avoid overflows while using _CPC's values, add 'u64' casts."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cpufreq: CPPC: Añadir conversiones u64 para evitar desbordamientos. Los campos del objeto _CPC son valores de 32 bits sin signo. Para evitar desbordamientos al usar los valores de _CPC, añadir conversiones 'u64'."}], "references": [{"url": "https://git.kernel.org/stable/c/7d596bbc66a52ff2c7a83d7e0ee840cb07e2a045", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f5f94b9c8b805d87ff185caf9779c3a4d07819e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
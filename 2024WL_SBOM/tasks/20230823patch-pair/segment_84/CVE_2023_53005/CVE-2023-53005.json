{"cve_id": "CVE-2023-53005", "published_date": "2025-03-27T17:15:49.420", "last_modified_date": "2025-04-14T20:52:39.980", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntrace_events_hist: add check for return value of 'create_hist_field'\n\nFunction 'create_hist_field' is called recursively at\ntrace_events_hist.c:1954 and can return NULL-value that's why we have\nto check it to avoid null pointer dereference.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: trace_events_hist: se ha añadido una comprobación del valor de retorno de 'create_hist_field'. La función 'create_hist_field' se llama recursivamente en trace_events_hist.c:1954 y puede devolver un valor nulo. <PERSON>r el<PERSON>, es necesario comprobarla para evitar la desreferencia de punteros nulos. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/31b2414abeaa6de0490e85164badc6dcb1bb8ec9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/592ba7116fa620425725ff0972691f352ba3caf6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/886aa449235f478e262bbd5dcdee6ed6bc202949", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8b152e9150d07a885f95e1fd401fc81af202d9a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b4e7e81b4fdfcf457daee6b7a61769f62198d840", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d2d1ada58e7cc100b8d7d6b082d19321ba4a700a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53023", "published_date": "2025-03-27T17:15:51.847", "last_modified_date": "2025-04-01T15:39:10.740", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: nfc: Fix use-after-free in local_cleanup()\n\nFix a use-after-free that occurs in kfree_skb() called from\nlocal_cleanup(). This could happen when killing nfc daemon (e.g. neard)\nafter detaching an nfc device.\nWhen detaching an nfc device, local_cleanup() called from\nnfc_llcp_unregister_device() frees local->rx_pending and decreases\nlocal->ref by kref_put() in nfc_llcp_local_put().\nIn the terminating process, nfc daemon releases all sockets and it leads\nto decreasing local->ref. After the last release of local->ref,\nlocal_cleanup() called from local_release() frees local->rx_pending\nagain, which leads to the bug.\n\nSetting local->rx_pending to NULL in local_cleanup() could prevent\nuse-after-free when local_cleanup() is called twice.\n\nFound by a modified version of syzkaller.\n\nBUG: KASAN: use-after-free in kfree_skb()\n\nCall Trace:\ndump_stack_lvl (lib/dump_stack.c:106)\nprint_address_description.constprop.0.cold (mm/kasan/report.c:306)\nkasan_check_range (mm/kasan/generic.c:189)\nkfree_skb (net/core/skbuff.c:955)\nlocal_cleanup (net/nfc/llcp_core.c:159)\nnfc_llcp_local_put.part.0 (net/nfc/llcp_core.c:172)\nnfc_llcp_local_put (net/nfc/llcp_core.c:181)\nllcp_sock_destruct (net/nfc/llcp_sock.c:959)\n__sk_destruct (net/core/sock.c:2133)\nsk_destruct (net/core/sock.c:2181)\n__sk_free (net/core/sock.c:2192)\nsk_free (net/core/sock.c:2203)\nllcp_sock_release (net/nfc/llcp_sock.c:646)\n__sock_release (net/socket.c:650)\nsock_close (net/socket.c:1365)\n__fput (fs/file_table.c:306)\ntask_work_run (kernel/task_work.c:179)\nptrace_notify (kernel/signal.c:2354)\nsyscall_exit_to_user_mode_prepare (kernel/entry/common.c:278)\nsyscall_exit_to_user_mode (kernel/entry/common.c:296)\ndo_syscall_64 (arch/x86/entry/common.c:86)\nentry_SYSCALL_64_after_hwframe (arch/x86/entry/entry_64.S:106)\n\nAllocated by task 4719:\nkasan_save_stack (mm/kasan/common.c:45)\n__kasan_slab_alloc (mm/kasan/common.c:325)\nslab_post_alloc_hook (mm/slab.h:766)\nkmem_cache_alloc_node (mm/slub.c:3497)\n__alloc_skb (net/core/skbuff.c:552)\npn533_recv_response (drivers/nfc/pn533/usb.c:65)\n__usb_hcd_giveback_urb (drivers/usb/core/hcd.c:1671)\nusb_giveback_urb_bh (drivers/usb/core/hcd.c:1704)\ntasklet_action_common.isra.0 (kernel/softirq.c:797)\n__do_softirq (kernel/softirq.c:571)\n\nFreed by task 1901:\nkasan_save_stack (mm/kasan/common.c:45)\nkasan_set_track (mm/kasan/common.c:52)\nkasan_save_free_info (mm/kasan/genericdd.c:518)\n__kasan_slab_free (mm/kasan/common.c:236)\nkmem_cache_free (mm/slub.c:3809)\nkfree_skbmem (net/core/skbuff.c:874)\nkfree_skb (net/core/skbuff.c:931)\nlocal_cleanup (net/nfc/llcp_core.c:159)\nnfc_llcp_unregister_device (net/nfc/llcp_core.c:1617)\nnfc_unregister_device (net/nfc/core.c:1179)\npn53x_unregister_nfc (drivers/nfc/pn533/pn533.c:2846)\npn533_usb_disconnect (drivers/nfc/pn533/usb.c:579)\nusb_unbind_interface (drivers/usb/core/driver.c:458)\ndevice_release_driver_internal (drivers/base/dd.c:1279)\nbus_remove_device (drivers/base/bus.c:529)\ndevice_del (drivers/base/core.c:3665)\nusb_disable_device (drivers/usb/core/message.c:1420)\nusb_disconnect (drivers/usb/core.c:2261)\nhub_event (drivers/usb/core/hub.c:5833)\nprocess_one_work (arch/x86/include/asm/jump_label.h:27 include/linux/jump_label.h:212 include/trace/events/workqueue.h:108 kernel/workqueue.c:2281)\nworker_thread (include/linux/list.h:282 kernel/workqueue.c:2423)\nkthread (kernel/kthread.c:319)\nret_from_fork (arch/x86/entry/entry_64.S:301)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: nfc: Se corrige use-after-free en local_cleanup() Corrige un use-after-free que ocurre en kfree_skb() llamado desde local_cleanup(). Esto podría suceder al matar el demonio nfc (p. ej., neard) después de desconectar un dispositivo nfc. Al desconectar un dispositivo nfc, local_cleanup() llamado desde nfc_llcp_unregister_device() libera local-&gt;rx_pending y disminuye local-&gt;ref por kref_put() en nfc_llcp_local_put(). En el proceso de terminación, el demonio nfc libera todos los sockets y esto lleva a la disminución de local-&gt;ref. Después de la última liberación de local-&gt;ref, local_cleanup() llamado desde local_release() libera local-&gt;rx_pending de nuevo, lo que conduce al error. Establecer local-&gt;rx_pending como NULL en local_cleanup() podría impedir el use-after-free al llamar a local_cleanup() dos veces. Encontrado por una versión modificada de syzkaller. ERROR: KASAN: use-after-free en el seguimiento de llamadas kfree_skb(): dump_stack_lvl (lib/dump_stack.c:106) print_address_description.constprop.0.cold (mm/kasan/report.c:306) kasan_check_range (mm/kasan/generic.c:189) kfree_skb (net/core/skbuff.c:955) local_cleanup (net/nfc/llcp_core.c:159) nfc_llcp_local_put.part.0 (net/nfc/llcp_core.c:172) nfc_llcp_local_put (net/nfc/llcp_core.c:181) llcp_sock_destruct (net/nfc/llcp_sock.c:959) __sk_destruct (net/core/sock.c:2133) sk_destruct (net/core/sock.c:2181) __sk_free (net/core/sock.c:2192) sk_free (net/core/sock.c:2203) llcp_sock_release (net/nfc/llcp_sock.c:646) __sock_release (net/socket.c:650) sock_close (net/socket.c:1365) __fput (fs/file_table.c:306) task_work_run (kernel/task_work.c:179) ptrace_notify (kernel/signal.c:2354) syscall_exit_to_user_mode_prepare (kernel/entry/common.c:278) syscall_exit_to_user_mode (kernel/entry/common.c:296) do_syscall_64 (arch/x86/entry/common.c:86) entry_SYSCALL_64_after_hwframe (arch/x86/entry/entry_64.S:106) Asignado por la tarea 4719: kasan_save_stack (mm/kasan/common.c:45) __kasan_slab_alloc (mm/kasan/common.c:325) slab_post_alloc_hook (mm/slab.h:766) kmem_cache_alloc_node (mm/slub.c:3497) __alloc_skb (net/core/skbuff.c:552) pn533_recv_response (drivers/nfc/pn533/usb.c:65) __usb_hcd_giveback_urb (controladores/usb/core/hcd.c:1671) usb_giveback_urb_bh (controladores/usb/core/hcd.c:1704) tasklet_action_common.isra.0 (kernel/softirq.c:797) __do_softirq (kernel/softirq.c:571) Liberado por la tarea 1901: kasan_save_stack (mm/kasan/common.c:45) kasan_set_track (mm/kasan/common.c:52) kasan_save_free_info (mm/kasan/genericdd.c:518) __kasan_slab_free (mm/kasan/common.c:236) kmem_cache_free (mm/slub.c:3809) kfree_skbmem (net/core/skbuff.c:874) kfree_skb (net/core/skbuff.c:931) limpieza local (net/nfc/llcp_core.c:159) nfc_llcp_unregister_device (net/nfc/llcp_core.c:1617) nfc_unregister_device (net/nfc/core.c:1179) pn53x_unregister_nfc (drivers/nfc/pn533/pn533.c:2846) pn533_usb_disconnect (drivers/nfc/pn533/usb.c:579) usb_unbind_interface (drivers/usb/core/driver.c:458) device_release_driver_internal (drivers/base/dd.c:1279) bus_remove_device (drivers/base/bus.c:529) device_del (drivers/base/core.c:3665) usb_disable_device (drivers/usb/core/message.c:1420) usb_disconnect (drivers/usb/core.c:2261) hub_event (drivers/usb/core/hub.c:5833) process_one_work (arch/x86/include/asm/jump_label.h:27 include/linux/jump_label.h:212 include/trace/events/workqueue.h:108 kernel/workqueue.c:2281) worker_thread (include/linux/list.h:282 kernel/workqueue.c:2423) kthread (kernel/kthread.c:319) ret_from_fork (arch/x86/entry/entry_64.S:301) "}], "references": [{"url": "https://git.kernel.org/stable/c/4bb4db7f3187c6e3de6b229ffc87cdb30a2d22b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/54f7be61584b8ec4c6df405f479495b9397bae4a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7f129927feaf7c10b1c38bbce630172e9a08c834", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a59cdbda3714e11aa3ab579132864c4c8c6d54f9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ad1baab3a5c03692d22ce446f38596a126377f6a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b09ae26f08aaf2d85f96ea7f90ddd3387f62216f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d3605282ec3502ec8847915eb2cf1f340493ff79", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
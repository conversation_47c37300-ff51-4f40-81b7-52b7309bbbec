{"cve_id": "CVE-2022-49740", "published_date": "2025-03-27T17:15:38.583", "last_modified_date": "2025-04-14T20:26:30.123", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: brcmfmac: Check the count value of channel spec to prevent out-of-bounds reads\n\nThis patch fixes slab-out-of-bounds reads in brcmfmac that occur in\nbrcmf_construct_chaninfo() and brcmf_enable_bw40_2g() when the count\nvalue of channel specifications provided by the device is greater than\nthe length of 'list->element[]', decided by the size of the 'list'\nallocated with kzalloc(). The patch adds checks that make the functions\nfree the buffer and return -EINVAL if that is the case. Note that the\nnegative return is handled by the caller, brcmf_setup_wiphybands() or\nbrcmf_cfg80211_attach().\n\nFound by a modified version of syzkaller.\n\nCrash Report from brcmf_construct_chaninfo():\n==================================================================\nBUG: KASAN: slab-out-of-bounds in brcmf_setup_wiphybands+0x1238/0x1430\nRead of size 4 at addr ffff888115f24600 by task kworker/0:2/1896\n\nCPU: 0 PID: 1896 Comm: kworker/0:2 Tainted: G        W  O      5.14.0+ #132\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.12.1-0-ga5cab58e9a3f-prebuilt.qemu.org 04/01/2014\nWorkqueue: usb_hub_wq hub_event\nCall Trace:\n dump_stack_lvl+0x57/0x7d\n print_address_description.constprop.0.cold+0x93/0x334\n kasan_report.cold+0x83/0xdf\n brcmf_setup_wiphybands+0x1238/0x1430\n brcmf_cfg80211_attach+0x2118/0x3fd0\n brcmf_attach+0x389/0xd40\n brcmf_usb_probe+0x12de/0x1690\n usb_probe_interface+0x25f/0x710\n really_probe+0x1be/0xa90\n __driver_probe_device+0x2ab/0x460\n driver_probe_device+0x49/0x120\n __device_attach_driver+0x18a/0x250\n bus_for_each_drv+0x123/0x1a0\n __device_attach+0x207/0x330\n bus_probe_device+0x1a2/0x260\n device_add+0xa61/0x1ce0\n usb_set_configuration+0x984/0x1770\n usb_generic_driver_probe+0x69/0x90\n usb_probe_device+0x9c/0x220\n really_probe+0x1be/0xa90\n __driver_probe_device+0x2ab/0x460\n driver_probe_device+0x49/0x120\n __device_attach_driver+0x18a/0x250\n bus_for_each_drv+0x123/0x1a0\n __device_attach+0x207/0x330\n bus_probe_device+0x1a2/0x260\n device_add+0xa61/0x1ce0\n usb_new_device.cold+0x463/0xf66\n hub_event+0x10d5/0x3330\n process_one_work+0x873/0x13e0\n worker_thread+0x8b/0xd10\n kthread+0x379/0x450\n ret_from_fork+0x1f/0x30\n\nAllocated by task 1896:\n kasan_save_stack+0x1b/0x40\n __kasan_kmalloc+0x7c/0x90\n kmem_cache_alloc_trace+0x19e/0x330\n brcmf_setup_wiphybands+0x290/0x1430\n brcmf_cfg80211_attach+0x2118/0x3fd0\n brcmf_attach+0x389/0xd40\n brcmf_usb_probe+0x12de/0x1690\n usb_probe_interface+0x25f/0x710\n really_probe+0x1be/0xa90\n __driver_probe_device+0x2ab/0x460\n driver_probe_device+0x49/0x120\n __device_attach_driver+0x18a/0x250\n bus_for_each_drv+0x123/0x1a0\n __device_attach+0x207/0x330\n bus_probe_device+0x1a2/0x260\n device_add+0xa61/0x1ce0\n usb_set_configuration+0x984/0x1770\n usb_generic_driver_probe+0x69/0x90\n usb_probe_device+0x9c/0x220\n really_probe+0x1be/0xa90\n __driver_probe_device+0x2ab/0x460\n driver_probe_device+0x49/0x120\n __device_attach_driver+0x18a/0x250\n bus_for_each_drv+0x123/0x1a0\n __device_attach+0x207/0x330\n bus_probe_device+0x1a2/0x260\n device_add+0xa61/0x1ce0\n usb_new_device.cold+0x463/0xf66\n hub_event+0x10d5/0x3330\n process_one_work+0x873/0x13e0\n worker_thread+0x8b/0xd10\n kthread+0x379/0x450\n ret_from_fork+0x1f/0x30\n\nThe buggy address belongs to the object at ffff888115f24000\n which belongs to the cache kmalloc-2k of size 2048\nThe buggy address is located 1536 bytes inside of\n 2048-byte region [ffff888115f24000, ffff888115f24800)\n\nMemory state around the buggy address:\n ffff888115f24500: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n ffff888115f24580: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n>ffff888115f24600: fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc\n                   ^\n ffff888115f24680: fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc\n ffff888115f24700: fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc\n==================================================================\n\nCrash Report from brcmf_enable_bw40_2g():\n==========\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: brcmfmac: Comprueba el valor de conteo de la especificación del canal para evitar lecturas fuera de los límites Este parche corrige las lecturas fuera de los límites de slab en brcmfmac que ocurren en brcmf_construct_chaninfo() y brcmf_enable_bw40_2g() cuando el valor de conteo de las especificaciones del canal proporcionadas por el dispositivo es mayor que la longitud de 'list-&gt;element[]', decidida por el tamaño de la 'lista' asignada con kzalloc(). El parche agrega verificaciones que hacen que las funciones liberen el búfer y devuelvan -EINVAL si ese es el caso. Ten en cuenta que el retorno negativo lo maneja el llamador, brcmf_setup_wiphybands() o brcmf_cfg80211_attach(). Encontrado por una versión modificada de syzkaller. Informe de fallos de brcmf_construct_chaninfo(): ====================================================================== ERROR: KASAN: slab fuera de los límites en brcmf_setup_wiphybands+0x1238/0x1430 Lectura de tamaño 4 en la dirección ffff888115f24600 por la tarea kworker/0:2/1896 CPU: 0 PID: 1896 Comm: kworker/0:2 Contaminado: GWO 5.14.0+ #132 Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.12.1-0-ga5cab58e9a3f-prebuilt.qemu.org 01/04/2014 Cola de trabajo: usb_hub_wq hub_event Rastreo de llamadas: dump_stack_lvl+0x57/0x7d print_address_description.constprop.0.cold+0x93/0x334 kasan_report.cold+0x83/0xdf brcmf_setup_wiphybands+0x1238/0x1430 brcmf_cfg80211_attach+0x2118/0x3fd0 brcmf_attach+0x389/0xd40 brcmf_usb_probe+0x12de/0x1690 usb_probe_interface+0x25f/0x710 really_probe+0x1be/0xa90 __driver_probe_device+0x2ab/0x460 driver_probe_device+0x49/0x120 __device_attach_driver+0x18a/0x250 bus_for_each_drv+0x123/0x1a0 __device_attach+0x207/0x330 bus_probe_device+0x1a2/0x260 device_add+0xa61/0x1ce0 usb_set_configuration+0x984/0x1770 usb_generic_driver_probe+0x69/0x90 usb_probe_device+0x9c/0x220 really_probe+0x1be/0xa90 __driver_probe_device+0x2ab/0x460 driver_probe_device+0x49/0x120 __device_attach_driver+0x18a/0x250 bus_for_each_drv+0x123/0x1a0 __device_attach+0x207/0x330 bus_probe_device+0x1a2/0x260 device_add+0xa61/0x1ce0 usb_new_device.cold+0x463/0xf66 hub_event+0x10d5/0x3330 process_one_work+0x873/0x13e0 worker_thread+0x8b/0xd10 kthread+0x379/0x450 ret_from_fork+0x1f/0x30 Allocated by task 1896: kasan_save_stack+0x1b/0x40 __kasan_kmalloc+0x7c/0x90 kmem_cache_alloc_trace+0x19e/0x330 brcmf_setup_wiphybands+0x290/0x1430 brcmf_cfg80211_attach+0x2118/0x3fd0 brcmf_attach+0x389/0xd40 brcmf_usb_probe+0x12de/0x1690 usb_probe_interface+0x25f/0x710 really_probe+0x1be/0xa90 __driver_probe_device+0x2ab/0x460 driver_probe_device+0x49/0x120 __device_attach_driver+0x18a/0x250 bus_for_each_drv+0x123/0x1a0 __device_attach+0x207/0x330 bus_probe_device+0x1a2/0x260 device_add+0xa61/0x1ce0 usb_set_configuration+0x984/0x1770 usb_generic_driver_probe+0x69/0x90 usb_probe_device+0x9c/0x220 really_probe+0x1be/0xa90 __driver_probe_device+0x2ab/0x460 driver_probe_device+0x49/0x120 __device_attach_driver+0x18a/0x250 bus_for_each_drv+0x123/0x1a0 __device_attach+0x207/0x330 bus_probe_device+0x1a2/0x260 device_add+0xa61/0x1ce0 usb_new_device.cold+0x463/0xf66 hub_event+0x10d5/0x3330 process_one_work+0x873/0x13e0 worker_thread+0x8b/0xd10 kthread+0x379/0x450 ret_from_fork+0x1f/0x30 The buggy address belongs to the object at ffff888115f24000 which belongs to the cache kmalloc-2k of size 2048 The buggy address is located 1536 bytes inside of 2048-byte region [ffff888115f24000, ffff888115f24800) Memory state around the buggy address: ffff888115f24500: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ffff888115f24580: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 &gt;ffff888115f24600: fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc ^ ffff888115f24680: fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc ffff888115f24700: fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc fc  ========================================================================== Informe de fallo de brcmf_enable_bw40_2g(): ========== ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/4920ab131b2dbae7464b72bdcac465d070254209", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9cf5e99c1ae1a85286a76c9a970202750538394c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b2e412879595821ff1b5545cbed5f108fba7f5b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e4991910f15013db72f6ec0db7038ea67a57052e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f06de1bb6d61f0c18b0213bbc6298960037f9d42", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2022-49753", "published_date": "2025-03-27T17:15:40.227", "last_modified_date": "2025-04-01T15:41:09.700", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndmaengine: Fix double increment of client_count in dma_chan_get()\n\nThe first time dma_chan_get() is called for a channel the channel\nclient_count is incorrectly incremented twice for public channels,\nfirst in balance_ref_count(), and again prior to returning. This\nresults in an incorrect client count which will lead to the\nchannel resources not being freed when they should be. A simple\n test of repeated module load and unload of async_tx on a Dell\n Power Edge R7425 also shows this resulting in a kref underflow\n warning.\n\n[  124.329662] async_tx: api initialized (async)\n[  129.000627] async_tx: api initialized (async)\n[  130.047839] ------------[ cut here ]------------\n[  130.052472] refcount_t: underflow; use-after-free.\n[  130.057279] WARNING: CPU: 3 PID: 19364 at lib/refcount.c:28\nrefcount_warn_saturate+0xba/0x110\n[  130.065811] Modules linked in: async_tx(-) rfkill intel_rapl_msr\nintel_rapl_common amd64_edac edac_mce_amd ipmi_ssif kvm_amd dcdbas kvm\nmgag200 drm_shmem_helper acpi_ipmi irqbypass drm_kms_helper ipmi_si\nsyscopyarea sysfillrect rapl pcspkr ipmi_devintf sysimgblt fb_sys_fops\nk10temp i2c_piix4 ipmi_msghandler acpi_power_meter acpi_cpufreq vfat\nfat drm fuse xfs libcrc32c sd_mod t10_pi sg ahci crct10dif_pclmul\nlibahci crc32_pclmul crc32c_intel ghash_clmulni_intel igb megaraid_sas\ni40e libata i2c_algo_bit ccp sp5100_tco dca dm_mirror dm_region_hash\ndm_log dm_mod [last unloaded: async_tx]\n[  130.117361] CPU: 3 PID: 19364 Comm: modprobe Kdump: loaded Not\ntainted 5.14.0-185.el9.x86_64 #1\n[  130.126091] Hardware name: Dell Inc. PowerEdge R7425/02MJ3T, BIOS\n1.18.0 01/17/2022\n[  130.133806] RIP: 0010:refcount_warn_saturate+0xba/0x110\n[  130.139041] Code: 01 01 e8 6d bd 55 00 0f 0b e9 72 9d 8a 00 80 3d\n26 18 9c 01 00 75 85 48 c7 c7 f8 a3 03 9d c6 05 16 18 9c 01 01 e8 4a\nbd 55 00 <0f> 0b e9 4f 9d 8a 00 80 3d 01 18 9c 01 00 0f 85 5e ff ff ff\n48 c7\n[  130.157807] RSP: 0018:ffffbf98898afe68 EFLAGS: 00010286\n[  130.163036] RAX: 0000000000000000 RBX: ffff9da06028e598 RCX: 0000000000000000\n[  130.170172] RDX: ffff9daf9de26480 RSI: ffff9daf9de198a0 RDI: ffff9daf9de198a0\n[  130.177316] RBP: ffff9da7cddf3970 R08: 0000000000000000 R09: 00000000ffff7fff\n[  130.184459] R10: ffffbf98898afd00 R11: ffffffff9d9e8c28 R12: ffff9da7cddf1970\n[  130.191596] R13: 0000000000000000 R14: 0000000000000000 R15: 0000000000000000\n[  130.198739] FS:  00007f646435c740(0000) GS:ffff9daf9de00000(0000)\nknlGS:0000000000000000\n[  130.206832] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[  130.212586] CR2: 00007f6463b214f0 CR3: 00000008ab98c000 CR4: 00000000003506e0\n[  130.219729] Call Trace:\n[  130.222192]  <TASK>\n[  130.224305]  dma_chan_put+0x10d/0x110\n[  130.227988]  dmaengine_put+0x7a/0xa0\n[  130.231575]  __do_sys_delete_module.constprop.0+0x178/0x280\n[  130.237157]  ? syscall_trace_enter.constprop.0+0x145/0x1d0\n[  130.242652]  do_syscall_64+0x5c/0x90\n[  130.246240]  ? exc_page_fault+0x62/0x150\n[  130.250178]  entry_SYSCALL_64_after_hwframe+0x63/0xcd\n[  130.255243] RIP: 0033:0x7f6463a3f5ab\n[  130.258830] Code: 73 01 c3 48 8b 0d 75 a8 1b 00 f7 d8 64 89 01 48\n83 c8 ff c3 66 2e 0f 1f 84 00 00 00 00 00 90 f3 0f 1e fa b8 b0 00 00\n00 0f 05 <48> 3d 01 f0 ff ff 73 01 c3 48 8b 0d 45 a8 1b 00 f7 d8 64 89\n01 48\n[  130.277591] RSP: 002b:00007fff22f972c8 EFLAGS: 00000206 ORIG_RAX:\n00000000000000b0\n[  130.285164] RAX: ffffffffffffffda RBX: 000055b6786edd40 RCX: 00007f6463a3f5ab\n[  130.292303] RDX: 0000000000000000 RSI: 0000000000000800 RDI: 000055b6786edda8\n[  130.299443] RBP: 000055b6786edd40 R08: 0000000000000000 R09: 0000000000000000\n[  130.306584] R10: 00007f6463b9eac0 R11: 0000000000000206 R12: 000055b6786edda8\n[  130.313731] R13: 0000000000000000 R14: 000055b6786edda8 R15: 00007fff22f995f8\n[  130.320875]  </TASK>\n[  130.323081] ---[ end trace eff7156d56b5cf25 ]---\n\ncat /sys/class/dma/dma0chan*/in_use would get the wrong result.\n2\n2\n2\n\nTest-by: Jie Hai <<EMAIL>>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dmaengine: Corrección del doble incremento de client_count en dma_chan_get() La primera vez que se llama a dma_chan_get() para un canal, el canal client_count se incrementa incorrectamente dos veces para los canales públicos, primero en balance_ref_count() y nuevamente antes de regresar. Esto da como resultado un recuento de clientes incorrecto que provocará que los recursos del canal no se liberen cuando deberían. Una prueba simple de carga y descarga repetida del módulo async_tx en un Dell Power Edge R7425 también muestra que esto da como resultado una advertencia de desbordamiento por debajo de kref. [ 124.329662] async_tx: api inicializada (async) [ 129.000627] async_tx: api inicializada (async) [ 130.047839] ------------[ cortar aquí ]------------ [ 130.052472] refcount_t: desbordamiento; uso después de la liberación. [ 130.057279] ADVERTENCIA: CPU: 3 PID: 19364 en lib/refcount.c:28 refcount_warn_saturate+0xba/0x110 [ 130.065811] Módulos vinculados: async_tx(-) rfkill intel_rapl_msr intel_rapl_common amd64_edac edac_mce_amd ipmi_ssif kvm_amd dcdbas kvm mgag200 drm_shmem_helper acpi_ipmi irqbypass drm_kms_helper ipmi_si syscopyarea sysfillrect rapl pcspkr ipmi_devintf sysimgblt fb_sys_fops k10temp i2c_piix4 ipmi_msghandler acpi_power_meter acpi_cpufreq vfat fat drm fuse xfs libcrc32c sd_mod t10_pi sg ahci crct10dif_pclmul libahci crc32_pclmul crc32c_intel ghash_clmulni_intel igb megaraid_sas i40e libata i2c_algo_bit ccp sp5100_tco dca dm_mirror dm_region_hash dm_log dm_mod [última descarga: async_tx] [130.117361] CPU: 3 PID: 19364 Comm: modprobe Kdump: cargado No contaminado 5.14.0-185.el9.x86_64 #1 [130.126091] Nombre del hardware: Dell Inc. PowerEdge R7425/02MJ3T, BIOS 1.18.0 17/01/2022 [ 130.133806] RIP: 0010:refcount_warn_saturate+0xba/0x110 [ 130.139041] Código: 01 01 e8 6d bd 55 00 0f 0b e9 72 9d 8a 00 80 3d 26 18 9c 01 00 75 85 48 c7 c7 f8 a3 03 9d c6 05 16 18 9c 01 01 e8 4a bd 55 00 &lt;0f&gt; 0b e9 4f 9d 8a 00 80 3d 01 18 9c 01 00 0f 85 5e ff ff ff 48 c7 [ 130.157807] RSP: 0018:ffffbf98898afe68 EFLAGS: 00010286 [ 130.163036] RAX: 000000000000000 RBX: ffff9da06028e598 RCX: 0000000000000000 [ 130.170172] RDX: ffff9daf9de26480 RSI: ffff9daf9de198a0 RDI: ffff9daf9de198a0 [ 130.177316] RBP: ffff9da7cddf3970 R08: 0000000000000000 R09: 00000000ffff7fff [ 130.184459] R10: ffffbf98898afd00 R11: ffffffff9d9e8c28 R12: ffff9da7cddf1970 [ 130.191596] R13: 000000000000000 R14: 000000000000000 R15: 000000000000000 [ 130.198739] FS: 00007f646435c740(0000) GS:ffff9daf9de00000(0000) knlGS:0000000000000000 [ 130.206832] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [ 130.212586] CR2: 00007f6463b214f0 CR3: 00000008ab98c000 CR4: 00000000003506e0 [ 130.219729] Rastreo de llamadas: [ 130.222192]  [ 130.224305] dma_chan_put+0x10d/0x110 [ 130.227988] dmaengine_put+0x7a/0xa0 [ [130.231575] __do_sys_delete_module.constprop.0+0x178/0x280 [ 130.237157] ? syscall_trace_enter.constprop.0+0x145/0x1d0 [ 130.242652] do_syscall_64+0x5c/0x90 [ 130.246240] ? exc_page_fault+0x62/0x150 [ 130.250178] entry_SYSCALL_64_after_hwframe+0x63/0xcd [ 130.255243] RIP: 0033:0x7f6463a3f5ab [ 130.258830] Código: 73 01 c3 48 8b 0d 75 a8 1b 00 f7 d8 64 89 01 48 83 c8 ff c3 66 2e 0f 1f 84 00 00 00 00 00 90 f3 0f 1e fa b8 b0 00 00 00 0f 05 &lt;48&gt; 3d 01 f0 ff ff 73 01 c3 48 8b 0d 45 a8 1b 00 f7 d8 64 89 01 48 [ 130.277591] RSP: 002b:00007fff22f972c8 EFLAGS: 00000206 ORIG_RAX: 00000000000000b0 [ 130.285164] RAX: ffffffffffffffda RBX: 000055b6786edd40 RCX: 00007f6463a3f5ab [ 130.292303] RDX: 000000000000000 RSI: 0000000000000800 RDI: 000055b6786edda8 [ 130.299443] RBP: 000055b6786edd40 R08: 0000000000000000 R09: 0000000000000000 [ 130.306584] R10: 00007f6463b9eac0 R11: 0000000000000206 R12: 000055b6786edda8 [ 130.313731] R13: 000000000000000 R14: 000055b6786edda8 R15: 00007fff22f995f8 [ 130.320875]  [ 130.323081] ---[ fin del seguimiento eff7156d56b5cf25 ]--- cat /sys/class/dma/dma0chan*/in_use obtendría un resultado incorrecto. ----truncada----"}], "references": [{"url": "https://git.kernel.org/stable/c/142d644fd2cc059ffa042fbfb68e766433ef3afd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/18dd3b30d4c7e8440c63118c7a7b687372b9567f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1b409e14b4b7af034e0450f95c165b6c5c87dbc1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/42ecd72f02cd657b00b559621e7ef7d2c4d3e5f1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/71c601965532c38030133535f7cd93c1efa75af1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c6221afe573413fd2981e291f7df4a58283e0654", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f3dc1b3b4750851a94212dba249703dd0e50bb20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
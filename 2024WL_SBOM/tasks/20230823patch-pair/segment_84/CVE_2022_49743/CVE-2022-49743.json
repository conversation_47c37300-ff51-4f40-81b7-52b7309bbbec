{"cve_id": "CVE-2022-49743", "published_date": "2025-03-27T17:15:38.967", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\novl: Use \"buf\" flexible array for memcpy() destination\n\nThe \"buf\" flexible array needs to be the memcpy() destination to avoid\nfalse positive run-time warning from the recent FORTIFY_SOURCE\nhardening:\n\n  memcpy: detected field-spanning write (size 93) of single field \"&fh->fb\"\n  at fs/overlayfs/export.c:799 (size 21)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ovl: Usar la matriz flexible \"buf\" para el destino de memcpy() La matriz flexible \"buf\" debe ser el destino de memcpy() para evitar una advertencia de tiempo de ejecución de falsos positivos del reciente endurecimiento de FORTIFY_SOURCE: memcpy: se detectó una escritura que abarca el campo (tamaño 93) de un solo campo \"&amp;fh-&gt;fb\" en fs/overlayfs/export.c:799 (tamaño 21)"}], "references": [{"url": "https://git.kernel.org/stable/c/07a96977b2f462337a9121302de64277b8747ab1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a77141a06367825d639ac51b04703d551163e36c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cf8aa9bf97cadf85745506c6a3e244b22c268d63", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
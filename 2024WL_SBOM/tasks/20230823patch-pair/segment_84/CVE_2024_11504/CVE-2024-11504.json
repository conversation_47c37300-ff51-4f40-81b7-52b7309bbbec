{"cve_id": "CVE-2024-11504", "published_date": "2025-03-28T13:15:39.663", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "Input from multiple fields in Streamsoft Prestiż is not sanitized properly, leading to an SQL injection vulnerability, which might be exploited by an authenticated remote attacker. \nThis issue was fixed in 18.1.376.37 version of the software."}, {"lang": "es", "value": "La entrada de varios campos en Streamsoft Presti? no se desinfecta correctamente, lo que genera una vulnerabilidad de inyección SQL que un atacante remoto autenticado podría explotar. Este problema se solucionó en la versión 18.1.376.37 del software."}], "references": [{"url": "https://cert.pl/en/posts/2025/03/CVE-2024-7407/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.streamsoft.pl/streamsoft-prestiz/", "source": "<EMAIL>", "tags": []}]}
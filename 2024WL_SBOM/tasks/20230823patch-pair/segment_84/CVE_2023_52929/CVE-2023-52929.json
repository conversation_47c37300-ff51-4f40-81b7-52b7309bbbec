{"cve_id": "CVE-2023-52929", "published_date": "2025-03-27T17:15:42.353", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnvmem: core: fix cleanup after dev_set_name()\n\nIf dev_set_name() fails, we leak nvmem->wp_gpio as the cleanup does not\nput this. While a minimal fix for this would be to add the gpiod_put()\ncall, we can do better if we split device_register(), and use the\ntested nvmem_release() cleanup code by initialising the device early,\nand putting the device.\n\nThis results in a slightly larger fix, but results in clear code.\n\nNote: this patch depends on \"nvmem: core: initialise nvmem->id early\"\nand \"nvmem: core: remove nvmem_config wp_gpio\".\n\n[Srini: Fixed subject line and error code handing with wp_gpio while applying.]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nvmem: core: fix cleanup after dev_set_name(). Si dev_set_name() falla, se filtra nvmem-&gt;wp_gpio, ya que la limpieza no lo incluye. Si bien una solución mínima sería añadir la llamada gpiod_put(), podemos mejorar si dividimos device_register() y usamos el código de limpieza nvmem_release() probado, inicializando el dispositivo antes y colocándolo. Esto resulta en una corrección ligeramente mayor, pero con código limpio. Nota: Este parche depende de \"nvmem: core: initialise nvmem-&gt;id early\" y \"nvmem: core: remove nvmem_config wp_gpio\". [Srini: Se corrigió la línea de asunto y la gestión del código de error con wp_gpio durante la aplicación]."}], "references": [{"url": "https://git.kernel.org/stable/c/23676ecd2eb377f7c24a6ff578b0f4c7135658b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39708bc8da7858de0bed9b3a88b3beb1d1e0b443", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/560181d3ace61825f4ca9dd3481d6c0ee6709fa8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8f9c4b2a3b132bf6698e477aba6ee194b40c75f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49757", "published_date": "2025-03-27T17:15:40.910", "last_modified_date": "2025-04-15T14:51:20.980", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nEDAC/highbank: Fix memory leak in highbank_mc_probe()\n\nWhen devres_open_group() fails, it returns -ENOMEM without freeing memory\nallocated by edac_mc_alloc().\n\nCall edac_mc_free() on the error handling path to avoid a memory leak.\n\n  [ bp: Massage commit message. ]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: EDAC/highbank: Se corrige la pérdida de memoria en highbank_mc_probe(). <PERSON>uando devres_open_group() falla, devuelve -ENOMEM sin liberar la memoria asignada por edac_mc_alloc(). Se llama a edac_mc_free() en la ruta de gestión de errores para evitar una pérdida de memoria. [bp: Mensaje de confirmación de Modificación]."}], "references": [{"url": "https://git.kernel.org/stable/c/0db40e23b56d217eebd385bebb64057ef764b2c7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/329fbd260352a7b9a83781d8b8bd96f95844a51f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8d23f5d25264beb223ee79cdb530b88c237719fc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b7863ef8a8f0fee96b4eb41211f4918c0e047253", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/caffa7fed1397d1395052272c93900176de86557", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e7a293658c20a7945014570e1921bf7d25d68a36", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f1b3e23ed8df87d779ee86ac37f379e79a24169a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-52934", "published_date": "2025-03-27T17:15:43.207", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/MADV_COLLAPSE: catch !none !huge !bad pmd lookups\n\nIn commit 34488399fa08 (\"mm/madvise: add file and shmem support to\nMADV_COLLAPSE\") we make the following change to find_pmd_or_thp_or_none():\n\n\t-       if (!pmd_present(pmde))\n\t-               return SCAN_PMD_NULL;\n\t+       if (pmd_none(pmde))\n\t+               return SCAN_PMD_NONE;\n\nThis was for-use by MADV_COLLAPSE file/shmem codepaths, where\nMADV_COLLAPSE might identify a pte-mapped hugepage, only to have\nkhugepaged race-in, free the pte table, and clear the pmd.  Such codepaths\ninclude:\n\nA) If we find a suitably-aligned compound page of order HPAGE_PMD_ORDER\n   already in the pagecache.\nB) In retract_page_tables(), if we fail to grab mmap_lock for the target\n   mm/address.\n\nIn these cases, collapse_pte_mapped_thp() really does expect a none (not\njust !present) pmd, and we want to suitably identify that case separate\nfrom the case where no pmd is found, or it's a bad-pmd (of course, many\nthings could happen once we drop mmap_lock, and the pmd could plausibly\nundergo multiple transitions due to intervening fault, split, etc). \nRegardless, the code is prepared install a huge-pmd only when the existing\npmd entry is either a genuine pte-table-mapping-pmd, or the none-pmd.\n\nHowever, the commit introduces a logical hole; namely, that we've allowed\n!none- && !huge- && !bad-pmds to be classified as genuine\npte-table-mapping-pmds.  One such example that could leak through are swap\nentries.  The pmd values aren't checked again before use in\npte_offset_map_lock(), which is expecting nothing less than a genuine\npte-table-mapping-pmd.\n\nWe want to put back the !pmd_present() check (below the pmd_none() check),\nbut need to be careful to deal with subtleties in pmd transitions and\ntreatments by various arch.\n\nThe issue is that __split_huge_pmd_locked() temporarily clears the present\nbit (or otherwise marks the entry as invalid), but pmd_present() and\npmd_trans_huge() still need to return true while the pmd is in this\ntransitory state.  For example, x86's pmd_present() also checks the\n_PAGE_PSE , riscv's version also checks the _PAGE_LEAF bit, and arm64 also\nchecks a PMD_PRESENT_INVALID bit.\n\nCovering all 4 cases for x86 (all checks done on the same pmd value):\n\n1) pmd_present() && pmd_trans_huge()\n   All we actually know here is that the PSE bit is set. Either:\n   a) We aren't racing with __split_huge_page(), and PRESENT or PROTNONE\n      is set.\n      => huge-pmd\n   b) We are currently racing with __split_huge_page().  The danger here\n      is that we proceed as-if we have a huge-pmd, but really we are\n      looking at a pte-mapping-pmd.  So, what is the risk of this\n      danger?\n\n      The only relevant path is:\n\n\tmadvise_collapse() -> collapse_pte_mapped_thp()\n\n      Where we might just incorrectly report back \"success\", when really\n      the memory isn't pmd-backed.  This is fine, since split could\n      happen immediately after (actually) successful madvise_collapse().\n      So, it should be safe to just assume huge-pmd here.\n\n2) pmd_present() && !pmd_trans_huge()\n   Either:\n   a) PSE not set and either PRESENT or PROTNONE is.\n      => pte-table-mapping pmd (or PROT_NONE)\n   b) devmap.  This routine can be called immediately after\n      unlocking/locking mmap_lock -- or called with no locks held (see\n      khugepaged_scan_mm_slot()), so previous VMA checks have since been\n      invalidated.\n\n3) !pmd_present() && pmd_trans_huge()\n  Not possible.\n\n4) !pmd_present() && !pmd_trans_huge()\n  Neither PRESENT nor PROTNONE set\n  => not present\n\nI've checked all archs that implement pmd_trans_huge() (arm64, riscv,\npowerpc, longarch, x86, mips, s390) and this logic roughly translates\n(though devmap treatment is unique to x86 and powerpc, and (3) doesn't\nnecessarily hold in general -- but that doesn't matter since\n!pmd_present() always takes failure path).\n\nAlso, add a comment above find_pmd_or_thp_or_none()\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/MADV_COLLAPSE: catch !none !huge !bad pmd lookups En el commit 34488399fa08 (\"mm/madvise: agregar soporte de archivo y shmem a MADV_COLLAPSE\") realizamos el siguiente cambio en find_pmd_or_thp_or_none(): - if (!pmd_present(pmde)) - return SCAN_PMD_NULL; + if (pmd_none(pmde)) + return SCAN_PMD_NONE; Esto era para uso de las rutas de código de archivo/shmem MADV_COLLAPSE, donde MADV_COLLAPSE podría identificar una hugepage asignada a pte, solo para que khugepaged entrara en carrera, liberara la tabla pte y borrara el pmd. Tales rutas de código incluyen: A) Si encontramos una página compuesta adecuadamente alineada de orden HPAGE_PMD_ORDER ya en el pagecache. B) En retract_page_tables(), si no logramos obtener mmap_lock para el mm/dirección objetivo. En estos casos, collapse_pte_mapped_thp() realmente espera un pmd none (no solo !present), y queremos identificar adecuadamente ese caso separado del caso donde no se encuentra ningún pmd, o es un bad-pmd (por supuesto, muchas cosas podrían suceder una vez que eliminamos mmap_lock, y el pmd podría plausiblemente sufrir múltiples transiciones debido a la intervención de un fallo, división, etc.). En cualquier caso, el código está preparado para instalar un huge-pmd solo cuando la entrada pmd existente es un pte-table-mapping-pmd genuino, o el none-pmd. Sin embargo, la confirmación introduce un agujero lógico; Es decir, hemos permitido que los !none- &amp;&amp; !huge- &amp;&amp; !bad-pmds se clasifiquen como pte-table-mapping-pmds genuinos. Un ejemplo de fugas de información son las entradas de intercambio. Los valores de pmd no se comprueban de nuevo antes de su uso en pte_offset_map_lock(), que espera nada menos que un pte-table-mapping-pmd genuino. Queremos restablecer la comprobación de !pmd_present() (debajo de la comprobación de pmd_none()), pero debemos tener cuidado con las sutilezas en las transiciones y los tratamientos de pmd por parte de varias arquitecturas. El problema es que __split_huge_pmd_locked() borra temporalmente el bit presente (o marca la entrada como inválida), pero pmd_present() y pmd_trans_huge() aún deben devolver verdadero mientras el pmd esté en este estado transitorio. Por ejemplo, pmd_present() de x86 también verifica _PAGE_PSE, la versión de riscv también verifica el bit _PAGE_LEAF y arm64 también verifica el bit PMD_PRESENT_INVALID. Cubriendo los 4 casos para x86 (todas las verificaciones realizadas en el mismo valor pmd): 1) pmd_present() y pmd_trans_huge(). Lo único que sabemos es que el bit PSE está establecido. O bien: a) No estamos compitiendo con __split_huge_page(), y PRESENT o PROTNONE están establecidos. =&gt; huge-pmd. b) Actualmente estamos compitiendo con __split_huge_page(). El peligro aquí es que procedamos como si tuviéramos un huge-pmd, pero en realidad estamos viendo un pte-mapping-pmd. Entonces, ¿cuál es el riesgo de este peligro? La única ruta relevante es: madvise_collapse() -&gt; colapso_pte_mapped_thp(). Donde podríamos informar incorrectamente de \"éxito\", cuando en realidad la memoria no está respaldada por pmd. Esto no tiene problema, ya que la división podría ocurrir inmediatamente después de una ejecución (realmente) exitosa de madvise_collapse(). Por lo tanto, se puede asumir con seguridad que es huge-pmd. 2) pmd_present() &amp;&amp; !pmd_trans_huge(): a) PSE no definido y PRESENT o PROTNONE sí lo están. =&gt; pte-table-mapping pmd (o PROT_NONE). b) devmap. Esta rutina puede llamarse inmediatamente después de desbloquear/bloquear mmap_lock, o sin bloqueos (véase khugepaged_scan_mm_slot()), por lo que las comprobaciones VMA anteriores han sido invalidadas. 3) !pmd_present() &amp;&amp; pmd_trans_huge(): No es posible. 4) !pmd_present() &amp;&amp; !pmd_trans_huge() Ni PRESENT ni PROTNONE se establecen =&gt; no presente. He revisado todas las arquitecturas que implementan pmd_trans_huge() (arm64, riscv, powerpc, longarch, x86, mips, s390) y esta lógica se traduce aproximadamente  ---truncated---"}], "references": [{"url": "https://git.kernel.org/stable/c/96aaaf8666010a39430cecf8a65c7ce2908a030f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/edb5d0cf5525357652aff6eacd9850b8ced07143", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
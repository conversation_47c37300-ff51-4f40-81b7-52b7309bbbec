{"cve_id": "CVE-2023-52974", "published_date": "2025-03-27T17:15:44.417", "last_modified_date": "2025-04-01T15:39:48.477", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: iscsi_tcp: Fix UAF during login when accessing the shost ipaddress\n\nIf during iscsi_sw_tcp_session_create() iscsi_tcp_r2tpool_alloc() fails,\nuserspace could be accessing the host's ipaddress attr. If we then free the\nsession via iscsi_session_teardown() while userspace is still accessing the\nsession we will hit a use after free bug.\n\nSet the tcp_sw_host->session after we have completed session creation and\ncan no longer fail."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: iscsi_tcp: Se corrige el UAF durante el inicio de sesión al acceder a la dirección IP del host. Si durante iscsi_sw_tcp_session_create() falla iscsi_tcp_r2tpool_alloc(), el espacio de usuario podría estar accediendo a la dirección IP del host. Si liberamos la sesión mediante iscsi_session_teardown() mientras el espacio de usuario aún accede a ella, se producirá un error de use after free. Configure tcp_sw_host-&gt;session después de completar la creación de la sesión y ya no podrá fallar."}], "references": [{"url": "https://git.kernel.org/stable/c/0aaabdb900c7415caa2006ef580322f7eac5f6b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/496af9d3682ed4c28fb734342a09e6cc0c056ea4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/61e43ebfd243bcbad11be26bd921723027b77441", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6abd4698f4c8a78e7bbfc421205c060c199554a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9758ffe1c07b86aefd7ca8e40d9a461293427ca0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d4d765f4761f9e3a2d62992f825aeee593bcb6b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f484a794e4ee2a9ce61f52a78e810ac45f3fe3b3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
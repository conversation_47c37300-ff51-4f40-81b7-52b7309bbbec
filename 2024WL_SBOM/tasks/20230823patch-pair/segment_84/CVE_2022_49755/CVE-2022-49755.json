{"cve_id": "CVE-2022-49755", "published_date": "2025-03-27T17:15:40.640", "last_modified_date": "2025-04-01T15:40:57.590", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: gadget: f_fs: Prevent race during ffs_ep0_queue_wait\n\nWhile performing fast composition switch, there is a possibility that the\nprocess of ffs_ep0_write/ffs_ep0_read get into a race condition\ndue to ep0req being freed up from functionfs_unbind.\n\nConsider the scenario that the ffs_ep0_write calls the ffs_ep0_queue_wait\nby taking a lock &ffs->ev.waitq.lock. However, the functionfs_unbind isn't\nbounded so it can go ahead and mark the ep0req to NULL, and since there\nis no NULL check in ffs_ep0_queue_wait we will end up in use-after-free.\n\nFix this by making a serialized execution between the two functions using\na mutex_lock(ffs->mutex)."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: gadget: f_fs: Evitar ejecución durante ffs_ep0_queue_wait Mientras se realiza un cambio de composición rápida, existe la posibilidad de que el proceso de ffs_ep0_write/ffs_ep0_read entre en una condición de ejecución debido a que ep0req se libera de functionfs_unbind. Considere el escenario en el que ffs_ep0_write llama a ffs_ep0_queue_wait tomando un bloqueo &amp;ffs-&gt;ev.waitq.lock. Sin embargo, functionfs_unbind no está limitada, por lo que puede seguir adelante y marcar ep0req como NULL, y dado que no hay una comprobación de NULL en ffs_ep0_queue_wait, terminaremos en use-after-free. Solucione esto realizando una ejecución serializada entre las dos funciones usando un mutex_lock(ffs-&gt;mutex)."}], "references": [{"url": "https://git.kernel.org/stable/c/6a19da111057f69214b97c62fb0ac59023970850", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6aee197b7fbcd61596a78b47d553f2f99111f217", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6dd9ea05534f323668db94fcc2726c7a84547e78", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a8d40942df074f4ebcb9bd3413596d92f323b064", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ae8e136bcaae96163b5821984de1036efc9abb1a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e9036e951f93fb8d7b5e9d6e2c7f94a4da312ae4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/facf353c9e8d7885b686d9a4b173d4e0af6441d2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
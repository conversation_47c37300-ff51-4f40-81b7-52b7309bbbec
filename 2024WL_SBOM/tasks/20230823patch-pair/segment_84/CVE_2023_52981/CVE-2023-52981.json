{"cve_id": "CVE-2023-52981", "published_date": "2025-03-27T17:15:45.313", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915: Fix request ref counting during error capture & debugfs dump\n\nWhen GuC support was added to error capture, the reference counting\naround the request object was broken. Fix it up.\n\nThe context based search manages the spinlocking around the search\ninternally. So it needs to grab the reference count internally as\nwell. The execlist only request based search relies on external\nlocking, so it needs an external reference count but within the\nspinlock not outside it.\n\nThe only other caller of the context based search is the code for\ndumping engine state to debugfs. That code wasn't previously getting\nan explicit reference at all as it does everything while holding the\nexeclist specific spinlock. So, that needs updaing as well as that\nspinlock doesn't help when using GuC submission. Rather than trying to\nconditionally get/put depending on submission model, just change it to\nalways do the get/put.\n\nv2: Explicitly document adding an extra blank line in some dense code\n(<PERSON>). Fix multiple potential null pointer derefs in case\nof no request found (some spotted by <PERSON>v<PERSON><PERSON>, but there was more!).\nAlso fix a leaked request in case of !started and another in\n__guc_reset_context now that intel_context_find_active_request is\nactually reference counting the returned request.\nv3: Add a _get suffix to intel_context_find_active_request now that it\ngrabs a reference (<PERSON><PERSON>).\nv4: Split the intel_guc_find_hung_context change to a separate patch\nand rename intel_context_find_active_request_get to\nintel_context_get_active_request (Tvrtko).\nv5: s/locking/reference counting/ in commit message (Tvrtko)\n\n(cherry picked from commit 3700e353781e27f1bc7222f51f2cc36cbeb9b4ec)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915: Arreglar el conteo de referencias de solicitud durante la captura de errores y el volcado de debugfs Cuando se agregó soporte para GuC a la captura de errores, el conteo de referencias alrededor del objeto de solicitud se rompió. Arréglenlo. La búsqueda basada en contexto administra el bloqueo de giro alrededor de la búsqueda internamente. Por lo tanto, también necesita obtener el conteo de referencias internamente. La búsqueda basada en solicitud solo de execlist se basa en el bloqueo externo, por lo que necesita un conteo de referencias externo pero dentro del bloqueo de giro, no fuera de él. El único otro llamador de la búsqueda basada en contexto es el código para volcar el estado del motor a debugfs. Ese código anteriormente no obtenía una referencia explícita en absoluto, ya que hace todo mientras mantiene el bloqueo de giro específico de execlist. Por lo tanto, eso necesita actualizarse, ya que el bloqueo de giro no ayuda cuando se usa el envío de GuC. En lugar de intentar obtener/poner condicionalmente dependiendo del modelo de envío, simplemente cámbielo para que siempre haga obtener/poner. v2: Documentar explícitamente agregar una línea en blanco adicional en algún código denso (Andy Shevchenko). Se corrigen varias posibles desreferencias de puntero nulo en caso de no encontrarse ninguna solicitud (algunas detectadas por Tvrtko, ¡pero había más!). También se corrigen una solicitud filtrada en caso de !started y otra en __guc_reset_context, ahora que intel_context_find_active_request cuenta las referencias de la solicitud devuelta. v3: Se añade el sufijo _get a intel_context_find_active_request ahora que obtiene una referencia (Daniele). v4: Se divide el cambio de intel_guc_find_hung_context en un parche independiente y se cambia el nombre de intel_context_find_active_request_get a intel_context_get_active_request (Tvrtko). v5: s/locking/reference counting/ en el mensaje de confirmación (Tvrtko) (seleccionado del commit 3700e353781e27f1bc7222f51f2cc36cbeb9b4ec)."}], "references": [{"url": "https://git.kernel.org/stable/c/86d8ddc74124c3fdfc139f246ba6da15e45e86e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9467397f417dd7b5d0db91452f0474e79716a527", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-52983", "published_date": "2025-03-27T17:15:45.557", "last_modified_date": "2025-04-01T15:39:39.127", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblock, bfq: fix uaf for bfqq in bic_set_bfqq()\n\nAfter commit 64dc8c732f5c (\"block, bfq: fix possible uaf for 'bfqq->bic'\"),\nbic->bfqq will be accessed in bic_set_bfqq(), however, in some context\nbic->bfqq will be freed, and bic_set_bfqq() is called with the freed\nbic->bfqq.\n\nFix the problem by always freeing bfqq after bic_set_bfqq()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: block, bfq: corrección de uaf para bfqq en bic_set_bfqq(). Después del commit 64dc8c732f5c (\"block, bfq: corrección de posible uaf para 'bfqq-&gt;bic'\"), se accederá a bic-&gt;bfqq en bic_set_bfqq(). Sin embargo, en algunos contextos, se liberará bic-&gt;bfqq y se llamará a bic_set_bfqq() con el bic-&gt;bfqq liberado. Para solucionar el problema, libere siempre bfqq después de bic_set_bfqq()."}], "references": [{"url": "https://git.kernel.org/stable/c/511c922c5bf6c8a166bea826e702336bc2424140", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7f77f3dab5066a7c9da73d72d1eee895ff84a8d5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b600de2d7d3a16f9007fad1bdae82a3951a26af2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cb1876fc33af26d00efdd473311f1b664c77c44e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53026", "published_date": "2025-03-27T17:15:52.250", "last_modified_date": "2025-04-15T19:42:05.810", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRDMA/core: Fix ib block iterator counter overflow\n\nWhen registering a new DMA MR after selecting the best aligned page size\nfor it, we iterate over the given sglist to split each entry to smaller,\naligned to the selected page size, DMA blocks.\n\nIn given circumstances where the sg entry and page size fit certain\nsizes and the sg entry is not aligned to the selected page size, the\ntotal size of the aligned pages we need to cover the sg entry is >= 4GB.\nUnder this circumstances, while iterating page aligned blocks, the\ncounter responsible for counting how much we advanced from the start of\nthe sg entry is overflowed because its type is u32 and we pass 4GB in\nsize. This can lead to an infinite loop inside the iterator function\nbecause the overflow prevents the counter to be larger\nthan the size of the sg entry.\n\nFix the presented problem by changing the advancement condition to\neliminate overflow.\n\nBacktrace:\n[  192.374329] efa_reg_user_mr_dmabuf\n[  192.376783] efa_register_mr\n[  192.382579] pgsz_bitmap 0xfffff000 rounddown 0x80000000\n[  192.386423] pg_sz [0x80000000] umem_length[0xc0000000]\n[  192.392657] start 0x0 length 0xc0000000 params.page_shift 31 params.page_num 3\n[  192.399559] hp_cnt[3], pages_in_hp[524288]\n[  192.403690] umem->sgt_append.sgt.nents[1]\n[  192.407905] number entries: [1], pg_bit: [31]\n[  192.411397] biter->__sg_nents [1] biter->__sg [0000000008b0c5d8]\n[  192.415601] biter->__sg_advance [665837568] sg_dma_len[3221225472]\n[  192.419823] biter->__sg_nents [1] biter->__sg [0000000008b0c5d8]\n[  192.423976] biter->__sg_advance [2813321216] sg_dma_len[3221225472]\n[  192.428243] biter->__sg_nents [1] biter->__sg [0000000008b0c5d8]\n[  192.432397] biter->__sg_advance [665837568] sg_dma_len[3221225472]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: RDMA/core: Fix ib block iterator counter overflow Al registrar un nuevo DMA MR después de seleccionar el mejor tamaño de página alineado para él, iteramos sobre la sglist dada para dividir cada entrada en bloques DMA más pequeños, alineados con el tamaño de página seleccionado. En determinadas circunstancias en las que la entrada sg y el tamaño de página se ajustan a ciertos tamaños y la entrada sg no está alineada con el tamaño de página seleccionado, el tamaño total de las páginas alineadas que necesitamos para cubrir la entrada sg es &gt;= 4 GB. En estas circunstancias, al iterar bloques alineados de página, el contador responsable de contar cuánto avanzamos desde el inicio de la entrada sg se desborda porque su tipo es u32 y pasamos 4 GB de tamaño. Esto puede llevar a un bucle infinito dentro de la función del iterador porque el desbordamiento impide que el contador sea mayor que el tamaño de la entrada sg. Solucione el problema presentado cambiando la condición de avance para eliminar el desbordamiento. Backtrace: [ 192.374329] efa_reg_user_mr_dmabuf [ 192.376783] efa_register_mr [ 192.382579] pgsz_bitmap 0xfffff000 rounddown 0x80000000 [ 192.386423] pg_sz [0x80000000] umem_length[0xc0000000] [ 192.392657] start 0x0 length 0xc0000000 params.page_shift 31 params.page_num 3 [ 192.399559] hp_cnt[3], pages_in_hp[524288] [ 192.403690] umem-&gt;sgt_append.sgt.nents[1] [ 192.407905] number entries: [1], pg_bit: [31] [ 192.411397] biter-&gt;__sg_nents [1] biter-&gt;__sg [0000000008b0c5d8] [ 192.415601] biter-&gt;__sg_advance [665837568] sg_dma_len[3221225472] [ 192.419823] biter-&gt;__sg_nents [1] biter-&gt;__sg [0000000008b0c5d8] [ 192.423976] biter-&gt;__sg_advance [2813321216] sg_dma_len[3221225472] [ 192.428243] biter-&gt;__sg_nents [1] biter-&gt;__sg [0000000008b0c5d8] [ 192.432397] biter-&gt;__sg_advance [665837568] sg_dma_len[3221225472] "}], "references": [{"url": "https://git.kernel.org/stable/c/0afec5e9cea732cb47014655685a2a47fb180c31", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/362c9489720b31b6aa7491423ba65a4e98aa9838", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/43811d07ea64366af8ec9e168c558ec51440c39e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/902063a9fea5f8252df392ade746bc9cfd07a5ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d66c1d4178c219b6e7d7a6f714e3e3656faccc36", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
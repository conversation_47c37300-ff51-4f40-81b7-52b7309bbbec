{"cve_id": "CVE-2023-53008", "published_date": "2025-03-27T17:15:49.797", "last_modified_date": "2025-04-14T20:52:51.587", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncifs: fix potential memory leaks in session setup\n\nMake sure to free cifs_ses::auth_key.response before allocating it as\nwe might end up leaking memory in reconnect or mounting."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cifs: corrige posibles fugas de memoria en la configuración de la sesión. Asegúrese de liberar cifs_ses::auth_key.response antes de asignarlo, ya que podríamos terminar perdiendo memoria durante la reconexión o el montaje."}], "references": [{"url": "https://git.kernel.org/stable/c/2fe58d977ee05da5bb89ef5dc4f5bf2dc15db46f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/893d45394dbe4b5cbf3723c19e2ccc8b93a6ac9b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
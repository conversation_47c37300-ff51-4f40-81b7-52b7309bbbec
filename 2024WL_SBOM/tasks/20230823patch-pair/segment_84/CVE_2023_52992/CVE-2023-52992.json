{"cve_id": "CVE-2023-52992", "published_date": "2025-03-27T17:15:46.670", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: Skip task with pid=1 in send_signal_common()\n\nThe following kernel panic can be triggered when a task with pid=1 attaches\na prog that attempts to send killing signal to itself, also see [1] for more\ndetails:\n\n  Kern<PERSON> panic - not syncing: Attempted to kill init! exitcode=0x0000000b\n  CPU: 3 PID: 1 Comm: systemd Not tainted 6.1.0-09652-g59fe41b5255f #148\n  Call Trace:\n  <TASK>\n  __dump_stack lib/dump_stack.c:88 [inline]\n  dump_stack_lvl+0x100/0x178 lib/dump_stack.c:106\n  panic+0x2c4/0x60f kernel/panic.c:275\n  do_exit.cold+0x63/0xe4 kernel/exit.c:789\n  do_group_exit+0xd4/0x2a0 kernel/exit.c:950\n  get_signal+0x2460/0x2600 kernel/signal.c:2858\n  arch_do_signal_or_restart+0x78/0x5d0 arch/x86/kernel/signal.c:306\n  exit_to_user_mode_loop kernel/entry/common.c:168 [inline]\n  exit_to_user_mode_prepare+0x15f/0x250 kernel/entry/common.c:203\n  __syscall_exit_to_user_mode_work kernel/entry/common.c:285 [inline]\n  syscall_exit_to_user_mode+0x1d/0x50 kernel/entry/common.c:296\n  do_syscall_64+0x44/0xb0 arch/x86/entry/common.c:86\n  entry_SYSCALL_64_after_hwframe+0x63/0xcd\n\nSo skip task with pid=1 in bpf_send_signal_common() to avoid the panic.\n\n  [1] https://lore.kernel.org/bpf/<EMAIL>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: Omitir tarea con pid=1 en send_signal_common() El siguiente pánico del kernel se puede activar cuando una tarea con pid=1 adjunta un programa que intenta enviarse una señal de eliminación a sí mismo, consulte también [1] para obtener más detalles: Pánico del kernel - no sincroniza: ¡Intento de eliminar init! exitcode=0x0000000b CPU: 3 PID: 1 Comm: systemd No contaminado 6.1.0-09652-g59fe41b5255f #148 Rastreo de llamadas:   __dump_stack lib/dump_stack.c:88 [inline] dump_stack_lvl+0x100/0x178 lib/dump_stack.c:106 panic+0x2c4/0x60f kernel/panic.c:275 do_exit.cold+0x63/0xe4 kernel/exit.c:789 do_group_exit+0xd4/0x2a0 kernel/exit.c:950 get_signal+0x2460/0x2600 kernel/signal.c:2858 arch_do_signal_or_restart+0x78/0x5d0 arch/x86/kernel/signal.c:306 exit_to_user_mode_loop kernel/entry/common.c:168 [inline] exit_to_user_mode_prepare+0x15f/0x250 kernel/entry/common.c:203 __syscall_exit_to_user_mode_work kernel/entry/common.c:285 [inline] syscall_exit_to_user_mode+0x1d/0x50 kernel/entry/common.c:296 do_syscall_64+0x44/0xb0 arch/x86/entry/common.c:86 entry_SYSCALL_64_after_hwframe+0x63/0xcd So skip task with pid=1 in bpf_send_signal_common() para evitar el pánico. [1] https://lore.kernel.org/bpf/<EMAIL>"}], "references": [{"url": "https://git.kernel.org/stable/c/0dfef503133565fa0bcf3268d8eeb5b181191a65", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1283a01b6e19d05f7ed49584ea653947245cd41e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4923160393b06a34759a11b17930d71e06f396f2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a1c0263f1eb4deee132e11e52ee6982435460d81", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a3d81bc1eaef48e34dd0b9b48eefed9e02a06451", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
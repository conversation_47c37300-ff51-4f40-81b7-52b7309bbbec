{"cve_id": "CVE-2023-52935", "published_date": "2025-03-27T17:15:43.330", "last_modified_date": "2025-04-01T15:40:32.503", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/khugepaged: fix ->anon_vma race\n\nIf an ->anon_vma is attached to the VMA, collapse_and_free_pmd() requires\nit to be locked.\n\nPage table traversal is allowed under any one of the mmap lock, the\nanon_vma lock (if the VMA is associated with an anon_vma), and the\nmapping lock (if the VMA is associated with a mapping); and so to be\nable to remove page tables, we must hold all three of them. \nretract_page_tables() bails out if an ->anon_vma is attached, but does\nthis check before holding the mmap lock (as the comment above the check\nexplains).\n\nIf we racily merged an existing ->anon_vma (shared with a child\nprocess) from a neighboring VMA, subsequent rmap traversals on pages\nbelonging to the child will be able to see the page tables that we are\nconcurrently removing while assuming that nothing else can access them.\n\nRepeat the ->anon_vma check once we hold the mmap lock to ensure that\nthere really is no concurrent page table access.\n\nHitting this bug causes a lockdep warning in collapse_and_free_pmd(),\nin the line \"lockdep_assert_held_write(&vma->anon_vma->root->rwsem)\". \nIt can also lead to use-after-free access."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/khugepaged: corrección de la ejecución-&gt;anon_vma. Si se adjunta un -&gt;anon_vma al VMA, la función collapse_and_free_pmd() requiere que esté bloqueada. Se permite el recorrido de la tabla de páginas bajo cualquiera de los bloqueos mmap, anon_vma (si el VMA está asociado a un anon_vma) y mapeo (si el VMA está asociado a un mapeo). Por lo tanto, para poder eliminar tablas de páginas, debemos mantener las tres. La función retract_page_tables() se activa si se adjunta un -&gt;anon_vma, pero realiza esta comprobación antes de mantener el bloqueo mmap (como explica el comentario sobre la comprobación). Si fusionamos rápidamente un -&gt;anon_vma existente (compartido con un proceso hijo) de un VMA vecino, los recorridos posteriores de rmap en páginas pertenecientes al hijo podrán ver las tablas de páginas que estamos eliminando simultáneamente, asumiendo que nadie más puede acceder a ellas. Repetir la comprobación de -&gt;anon_vma una vez que mantengamos el bloqueo mmap para garantizar que no haya accesos simultáneos a la tabla de páginas. Este error genera una advertencia de lockdep en el comando breakdown_and_free_pmd(), en la línea \"lockdep_assert_held_write(&amp;vma-&gt;anon_vma-&gt;root-&gt;rwsem)\". También puede provocar accesos de use-after-free."}], "references": [{"url": "https://git.kernel.org/stable/c/023f47a8250c6bdb4aebe744db4bf7f73414028b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/acb08187b5a83cdb9ac4112fae9e18cf983b0128", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
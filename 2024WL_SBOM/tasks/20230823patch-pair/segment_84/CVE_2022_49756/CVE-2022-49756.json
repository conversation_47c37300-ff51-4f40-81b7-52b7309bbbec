{"cve_id": "CVE-2022-49756", "published_date": "2025-03-27T17:15:40.787", "last_modified_date": "2025-04-14T20:17:05.523", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nphy: usb: sunplus: Fix potential null-ptr-deref in sp_usb_phy_probe()\n\nsp_usb_phy_probe() will call platform_get_resource_byname() that may fail\nand return NULL. devm_ioremap() will use usbphy->moon4_res_mem->start as\ninput, which may causes null-ptr-deref. Check the ret value of\nplatform_get_resource_byname() to avoid the null-ptr-deref."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: phy: usb: sunplus: Se corrige la posible desreferencia de PTR nula en sp_usb_phy_probe(). Sp_usb_phy_probe() llamará a platform_get_resource_byname(), que podría fallar y devolver NULL. devm_ioremap() utilizará usbphy-&gt;moon4_res_mem-&gt;start como entrada, lo que puede causar una desreferencia de PTR nula. Compruebe el valor ret de platform_get_resource_byname() para evitar la desreferencia de PTR nula."}], "references": [{"url": "https://git.kernel.org/stable/c/17eee264ef386ef30a69dd70e36f29893b85c170", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d838b5c99bcecd593b4710a93fce8fdbf122395b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
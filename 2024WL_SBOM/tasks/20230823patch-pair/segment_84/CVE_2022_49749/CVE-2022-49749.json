{"cve_id": "CVE-2022-49749", "published_date": "2025-03-27T17:15:39.730", "last_modified_date": "2025-04-14T20:27:38.340", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ni2c: designware: use casting of u64 in clock multiplication to avoid overflow\n\nIn functions i2c_dw_scl_lcnt() and i2c_dw_scl_hcnt() may have overflow\nby depending on the values of the given parameters including the ic_clk.\nFor example in our use case where ic_clk is larger than one million,\nmultiplication of ic_clk * 4700 will result in 32 bit overflow.\n\nAdd cast of u64 to the calculation to avoid multiplication overflow, and\nuse the corresponding define for divide."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: i2c: designware: usar la conversión de u64 en la multiplicación de reloj para evitar el desbordamiento. Las funciones i2c_dw_scl_lcnt() e i2c_dw_scl_hcnt() pueden experimentar un desbordamiento al depender de los valores de los parámetros dados, incluido ic_clk. <PERSON>r eje<PERSON>lo, en nuestro caso práctico, donde ic_clk es mayor que un millón, la multiplicación de ic_clk * 4700 resultará en un desbordamiento de 32 bits. Añada la conversión de u64 al cálculo para evitar el desbordamiento de la multiplicación y utilice la definición correspondiente para la división."}], "references": [{"url": "https://git.kernel.org/stable/c/2f29d780bd691d20e89e5b35d5e6568607115e94", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9f36aae9e80e79b7a6d62227eaa96935166be9fe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c8c37bc514514999e62a17e95160ed9ebf75ca8d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ed173f77fd28a3e4fffc13b3f28687b9eba61157", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-52989", "published_date": "2025-03-27T17:15:46.293", "last_modified_date": "2025-04-15T14:31:57.060", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfirewire: fix memory leak for payload of request subaction to IEC 61883-1 FCP region\n\nThis patch is fix for Linux kernel v2.6.33 or later.\n\nFor request subaction to IEC 61883-1 FCP region, Linux FireWire subsystem\nhave had an issue of use-after-free. The subsystem allows multiple\nuser space listeners to the region, while data of the payload was likely\nreleased before the listeners execute read(2) to access to it for copying\nto user space.\n\nThe issue was fixed by a commit 281e20323ab7 (\"firewire: core: fix\nuse-after-free regression in FCP handler\"). The object of payload is\nduplicated in kernel space for each listener. When the listener executes\nioctl(2) with FW_CDEV_IOC_SEND_RESPONSE request, the object is going to\nbe released.\n\nHowever, it causes memory leak since the commit relies on call of\nrelease_request() in drivers/firewire/core-cdev.c. Against the\nexpectation, the function is never called due to the design of\nrelease_client_resource(). The function delegates release task\nto caller when called with non-NULL fourth argument. The implementation\nof ioctl_send_response() is the case. It should release the object\nexplicitly.\n\nThis commit fixes the bug."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: firewire: corrección de fuga de memoria para el payload de la subacción de solicitud a la región FCP IEC 61883-1. Este parche es una corrección para el kernel de Linux v2.6.33 o posterior. Para la subacción de solicitud a la región FCP IEC 61883-1, el subsistema FireWire de Linux ha tenido un problema de use-after-free. El subsistema permite que varios oyentes del espacio de usuario accedan a la región, mientras que los datos de el payload probablemente se liberaron antes de que los oyentes ejecutaran read(2) para acceder a ellos para copiarlos al espacio de usuario. El problema se solucionó mediante un commit 281e20323ab7 (\"firewire: core: corrección de la regresión de use-after-free en el controlador FCP\"). El objeto de el payload se duplica en el espacio del kernel para cada oyente. Cuando el oyente ejecuta ioctl(2) con la solicitud FW_CDEV_IOC_SEND_RESPONSE, el objeto se liberará. Sin embargo, esto causa una fuga de memoria, ya que el commit depende de la llamada a release_request() en drivers/firewire/core-cdev.c. Contrariamente a lo esperado, la función nunca se llama debido al diseño de release_client_resource(). La función delega la tarea de liberación al llamador cuando se llama con un cuarto argumento distinto de NULL. La implementación de ioctl_send_response() es la correcta. Debería liberar el objeto explícitamente. Esta confirmación corrige el error."}], "references": [{"url": "https://git.kernel.org/stable/c/356ff89acdbe6a66019154bc7eb2d300f5b15103", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/531390a243ef47448f8bad01c186c2787666bf4d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/53785fd9b315583cf029e39f72b73d23704a2253", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5f4543c9382ae2d5062f6aa4fecae0c9258d0b0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b2cd3947d116bb9ba7ff097b5fc747a8956764db", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c8bdc88216f09cb7387fedbdf613524367328616", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d5a2dcee53fa6e6e2822f93cb3f1b0cd23163bee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2022-49761", "published_date": "2025-03-27T17:15:41.407", "last_modified_date": "2025-04-01T15:40:43.063", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: always report error in run_one_delayed_ref()\n\nCurrently we have a btrfs_debug() for run_one_delayed_ref() failure, but\nif end users hit such problem, there will be no chance that\nbtrfs_debug() is enabled.  This can lead to very little useful info for\ndebugging.\n\nThis patch will:\n\n- Add extra info for error reporting\n  Including:\n  * logical bytenr\n  * num_bytes\n  * type\n  * action\n  * ref_mod\n\n- Replace the btrfs_debug() with btrfs_err()\n\n- Move the error reporting into run_one_delayed_ref()\n  This is to avoid use-after-free, the @node can be freed in the caller.\n\nThis error should only be triggered at most once.\n\nAs if run_one_delayed_ref() failed, we trigger the error message, then\ncausing the call chain to error out:\n\nbtrfs_run_delayed_refs()\n`- btrfs_run_delayed_refs()\n   `- btrfs_run_delayed_refs_for_head()\n      `- run_one_delayed_ref()\n\nAnd we will abort the current transaction in btrfs_run_delayed_refs().\nIf we have to run delayed refs for the abort transaction,\nrun_one_delayed_ref() will just cleanup the refs and do nothing, thus no\nnew error messages would be output."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: siempre informa el error en run_one_delayed_ref() Actualmente tenemos un btrfs_debug() para el fallo de run_one_delayed_ref(), pero si los usuarios finales se encuentran con dicho problema, no habrá ninguna posibilidad de que btrfs_debug() esté habilitado. Esto puede llevar a muy poca información útil para la depuración. Este parche hará lo siguiente: - Agregar información adicional para el informe de errores Incluyendo: * byte lógico * num_bytes * tipo * acción * ref_mod - Reemplazar btrfs_debug() con btrfs_err() - Mover el informe de errores a run_one_delayed_ref() Esto es para evitar el use-after-free, el @nodo se puede liberar en el llamador. Este error solo debe activarse como máximo una vez. Como si run_one_delayed_ref() fallara, se genera un mensaje de error, lo que provoca que la cadena de llamadas genere un error: btrfs_run_delayed_refs() `- btrfs_run_delayed_refs() `- btrfs_run_delayed_refs_for_head() `- run_one_delayed_ref(). Abortaremos la transacción actual en btrfs_run_delayed_refs(). Si necesitamos ejecutar referencias retrasadas para la transacción abortada, run_one_delayed_ref() simplemente las limpiará y no hará nada, por lo que no se generarán nuevos mensajes de error."}], "references": [{"url": "https://git.kernel.org/stable/c/18bd1c9c02e64a3567f90c83c2c8b855531c8098", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/39f501d68ec1ed5cd5c66ac6ec2a7131c517bb92", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/853ffa1511b058c79a4c9bb1407b3b20ce311792", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fdb4a70bb768d2a87890409597529ad81cb3de8a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
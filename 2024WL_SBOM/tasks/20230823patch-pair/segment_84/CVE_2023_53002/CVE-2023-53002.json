{"cve_id": "CVE-2023-53002", "published_date": "2025-03-27T17:15:49.050", "last_modified_date": "2025-04-14T20:52:28.503", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915: Fix a memory leak with reused mmap_offset\n\ndrm_vma_node_allow() and drm_vma_node_revoke() should be called in\nbalanced pairs. We call drm_vma_node_allow() once per-file everytime a\nuser calls mmap_offset, but only call drm_vma_node_revoke once per-file\non each mmap_offset. As the mmap_offset is reused by the client, the\nper-file vm_count may remain non-zero and the rbtree leaked.\n\nCall drm_vma_node_allow_once() instead to prevent that memory leak."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915: Se solucionó una fuga de memoria con la reutilización de mmap_offset. drm_vma_node_allow() y drm_vma_node_revoke() deben llamarse en pares equilibrados. Se llama a drm_vma_node_allow() una vez por archivo cada vez que un usuario llama a mmap_offset, pero solo se llama a drm_vma_node_revoke una vez por archivo en cada mmap_offset. Dado que el cliente reutiliza mmap_offset, el valor de vm_count por archivo puede ser distinto de cero y el árbol rb se filtra. En su lugar, se llama a drm_vma_node_allow_once() para evitar esta fuga de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/0220e4fe178c3390eb0291cdb34912d66972db8a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/0bdc4b4ba7206c452ee81c82fa66e39d0e1780fb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53010", "published_date": "2025-03-27T17:15:50.030", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbnxt: Do not read past the end of test names\n\nTest names were being concatenated based on a offset beyond the end of\nthe first name, which tripped the buffer overflow detection logic:\n\n detected buffer overflow in strnlen\n [...]\n Call Trace:\n bnxt_ethtool_init.cold+0x18/0x18\n\nRefactor struct hwrm_selftest_qlist_output to use an actual array,\nand adjust the concatenation to use snprintf() rather than a series of\nstrncat() calls."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bnxt: No leer más allá del final de los nombres de prueba Los nombres de prueba se estaban concatenando en función de un desplazamiento más allá del final del primer nombre, lo que activaba la lógica de detección de desbordamiento de búfer: desbordamiento de búfer detectado en strnlen [...] Seguimiento de llamadas: bnxt_ethtool_init.cold+0x18/0x18 Refactorice la estructura hwrm_selftest_qlist_output para usar una matriz real y ajuste la concatenación para usar snprintf() en lugar de una serie de llamadas strncat()."}], "references": [{"url": "https://git.kernel.org/stable/c/cefa85480ac99c0bef5a09daadb48d65fc28e279", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3e599c090fc6977331150c5f0a69ab8ce87da21", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
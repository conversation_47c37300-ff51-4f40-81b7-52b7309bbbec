{"cve_id": "CVE-2023-52938", "published_date": "2025-03-27T17:15:43.687", "last_modified_date": "2025-04-15T14:42:21.637", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: typec: ucsi: Don't attempt to resume the ports before they exist\n\nThis will fix null pointer dereference that was caused by\nthe driver attempting to resume ports that were not yet\nregistered."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: typec: ucsi: No intente reanudar los puertos antes de que existan. Esto solucionará la desreferencia de puntero nulo que fue causada por el controlador que intentaba reanudar puertos que aún no estaban registrados."}], "references": [{"url": "https://git.kernel.org/stable/c/f82060da749c611ed427523b6d1605d87338aac1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fdd11d7136fd070b3a74d6d8799d9eac28a57fc5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-52931", "published_date": "2025-03-27T17:15:42.800", "last_modified_date": "2025-04-01T15:40:49.540", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915: Avoid potential vm use-after-free\n\nAdding the vm to the vm_xa table makes it visible to userspace, which\ncould try to race with us to close the vm.  So we need to take our extra\nreference before putting it in the table.\n\n(cherry picked from commit 99343c46d4e2b34c285d3d5f68ff04274c2f9fb4)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915: Evite el posible use-after-free de la máquina virtual. Añada la máquina virtual a vm_xa la hace visible para el espacio de usuario, lo que podría intentar cerrarla rápidamente. Por lo tanto, debemos tomar nuestra referencia adicional antes de añadirla a la tabla. (Seleccionado del commit 99343c46d4e2b34c285d3d5f68ff04274c2f9fb4)"}], "references": [{"url": "https://git.kernel.org/stable/c/41d419382ec7e257e54b7b6ff0d3623aafb1316d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/764accc2c1b8fd1507be2e7f436c94cdce887a00", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
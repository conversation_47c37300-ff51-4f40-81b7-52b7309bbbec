{"cve_id": "CVE-2023-52940", "published_date": "2025-03-27T17:15:43.920", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm: multi-gen LRU: fix crash during cgroup migration\n\nlru_gen_migrate_mm() assumes lru_gen_add_mm() runs prior to itself.  This\nisn't true for the following scenario:\n\n    CPU 1                         CPU 2\n\n  clone()\n    cgroup_can_fork()\n                                cgroup_procs_write()\n    cgroup_post_fork()\n                                  task_lock()\n                                  lru_gen_migrate_mm()\n                                  task_unlock()\n    task_lock()\n    lru_gen_add_mm()\n    task_unlock()\n\nAnd when the above happens, kernel crashes because of linked list\ncorruption (mm_struct->lru_gen.list)."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm: LRU multigeneración: se corrige un fallo durante la migración de cgroup. lru_gen_migrate_mm() asume que lru_gen_add_mm() se ejecuta antes que él mismo. Esto no se cumple en el siguiente escenario: CPU 1 CPU 2 clone() cgroup_can_fork() cgroup_procs_write() cgroup_post_fork() task_lock() lru_gen_migrate_mm() task_unlock() task_lock() lru_gen_add_mm() task_unlock(). <PERSON><PERSON><PERSON> esto ocurre, el kernel se bloquea debido a la corrupción de la lista enlazada (mm_struct-&gt;lru_gen.list)."}], "references": [{"url": "https://git.kernel.org/stable/c/04448022311cebd30969d3aebdde765f1258b360", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de08eaa6156405f2e9369f06ba5afae0e4ab3b62", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53014", "published_date": "2025-03-27T17:15:50.540", "last_modified_date": "2025-04-15T19:40:48.390", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndmaengine: tegra: Fix memory leak in terminate_all()\n\nTerminate vdesc when terminating an ongoing transfer.\nThis will ensure that the vdesc is present in the desc_terminated list\nThe descriptor will be freed later in desc_free_list().\n\nThis fixes the memory leaks which can happen when terminating an\nongoing transfer."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dmaengine: tegra: Se corrige la fuga de memoria en la función \"terminate_all()\". Se finaliza vdesc al finalizar una transferencia en curso. Esto garantiza que vdesc esté presente en la lista \"desc_terminated\". El descriptor se liberará posteriormente en \"desc_free_list()\". Esto corrige las fugas de memoria que pueden ocurrir al finalizar una transferencia en curso."}], "references": [{"url": "https://git.kernel.org/stable/c/567128076d554e41609c61b7d447089094ff72c5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a7a7ee6f5a019ad72852c001abbce50d35e992f2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
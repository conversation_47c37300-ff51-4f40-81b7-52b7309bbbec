{"cve_id": "CVE-2023-52941", "published_date": "2025-03-27T17:15:44.043", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncan: isotp: split tx timer into transmission and timeout\n\nThe timer for the transmission of isotp PDUs formerly had two functions:\n1. send two consecutive frames with a given time gap\n2. monitor the timeouts for flow control frames and the echo frames\n\nThis led to larger txstate checks and potentially to a problem discovered\nby syzbot which enabled the panic_on_warn feature while testing.\n\nThe former 'txtimer' function is split into 'txfrtimer' and 'txtimer'\nto handle the two above functionalities with separate timer callbacks.\n\nThe two simplified timers now run in one-shot mode and make the state\ntransitions (especially with isotp_rcv_echo) better understandable."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: can: isotp: dividir el temporizador de transmisión en transmisión y tiempo de espera El temporizador para la transmisión de PDU isotp anteriormente tenía dos funciones: 1. enviar dos tramas consecutivas con un intervalo de tiempo determinado 2. supervisar los tiempos de espera para las tramas de control de flujo y las tramas de eco Esto llevó a comprobaciones de txstate más grandes y potencialmente a un problema descubierto por syzbot que habilitó la característica panic_on_warn durante las pruebas. La antigua función 'txtimer' se divide en 'txfrtimer' y 'txtimer' para manejar las dos funcionalidades anteriores con devoluciones de llamadas de temporizador independientes. Los dos temporizadores simplificados ahora se ejecutan en modo de una sola vez y hacen que las transiciones de estado (especialmente con isotp_rcv_echo) sean mejor comprensibles."}], "references": [{"url": "https://git.kernel.org/stable/c/4f027cba8216f42a18b544842efab134f8b1f9f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cae4c9bc35f72af5d4a079bb9d9fd62c4088a411", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
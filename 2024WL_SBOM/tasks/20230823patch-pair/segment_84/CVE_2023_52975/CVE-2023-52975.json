{"cve_id": "CVE-2023-52975", "published_date": "2025-03-27T17:15:44.533", "last_modified_date": "2025-04-01T15:39:59.800", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: iscsi_tcp: Fix UAF during logout when accessing the shost ipaddress\n\nBug report and analysis from <PERSON><PERSON>.\n\nDuring iSCSI session logout, if another task accesses the shost ipaddress\nattr, we can get a KASAN UAF report like this:\n\n[  276.942144] BUG: KASAN: use-after-free in _raw_spin_lock_bh+0x78/0xe0\n[  276.942535] Write of size 4 at addr ffff8881053b45b8 by task cat/4088\n[  276.943511] CPU: 2 PID: 4088 Comm: cat Tainted: G            E      6.1.0-rc8+ #3\n[  276.943997] Hardware name: VMware, Inc. VMware Virtual Platform/440BX Desktop Reference Platform, BIOS 6.00 11/12/2020\n[  276.944470] Call Trace:\n[  276.944943]  <TASK>\n[  276.945397]  dump_stack_lvl+0x34/0x48\n[  276.945887]  print_address_description.constprop.0+0x86/0x1e7\n[  276.946421]  print_report+0x36/0x4f\n[  276.947358]  kasan_report+0xad/0x130\n[  276.948234]  kasan_check_range+0x35/0x1c0\n[  276.948674]  _raw_spin_lock_bh+0x78/0xe0\n[  276.949989]  iscsi_sw_tcp_host_get_param+0xad/0x2e0 [iscsi_tcp]\n[  276.951765]  show_host_param_ISCSI_HOST_PARAM_IPADDRESS+0xe9/0x130 [scsi_transport_iscsi]\n[  276.952185]  dev_attr_show+0x3f/0x80\n[  276.953005]  sysfs_kf_seq_show+0x1fb/0x3e0\n[  276.953401]  seq_read_iter+0x402/0x1020\n[  276.954260]  vfs_read+0x532/0x7b0\n[  276.955113]  ksys_read+0xed/0x1c0\n[  276.955952]  do_syscall_64+0x38/0x90\n[  276.956347]  entry_SYSCALL_64_after_hwframe+0x63/0xcd\n[  276.956769] RIP: 0033:0x7f5d3a679222\n[  276.957161] Code: c0 e9 b2 fe ff ff 50 48 8d 3d 32 c0 0b 00 e8 a5 fe 01 00 0f 1f 44 00 00 f3 0f 1e fa 64 8b 04 25 18 00 00 00 85 c0 75 10 0f 05 <48> 3d 00 f0 ff ff 77 56 c3 0f 1f 44 00 00 48 83 ec 28 48 89 54 24\n[  276.958009] RSP: 002b:00007ffc864d16a8 EFLAGS: 00000246 ORIG_RAX: 0000000000000000\n[  276.958431] RAX: ffffffffffffffda RBX: 0000000000020000 RCX: 00007f5d3a679222\n[  276.958857] RDX: 0000000000020000 RSI: 00007f5d3a4fe000 RDI: 0000000000000003\n[  276.959281] RBP: 00007f5d3a4fe000 R08: 00000000ffffffff R09: 0000000000000000\n[  276.959682] R10: 0000000000000022 R11: 0000000000000246 R12: 0000000000020000\n[  276.960126] R13: 0000000000000003 R14: 0000000000000000 R15: 0000557a26dada58\n[  276.960536]  </TASK>\n[  276.961357] Allocated by task 2209:\n[  276.961756]  kasan_save_stack+0x1e/0x40\n[  276.962170]  kasan_set_track+0x21/0x30\n[  276.962557]  __kasan_kmalloc+0x7e/0x90\n[  276.962923]  __kmalloc+0x5b/0x140\n[  276.963308]  iscsi_alloc_session+0x28/0x840 [scsi_transport_iscsi]\n[  276.963712]  iscsi_session_setup+0xda/0xba0 [libiscsi]\n[  276.964078]  iscsi_sw_tcp_session_create+0x1fd/0x330 [iscsi_tcp]\n[  276.964431]  iscsi_if_create_session.isra.0+0x50/0x260 [scsi_transport_iscsi]\n[  276.964793]  iscsi_if_recv_msg+0xc5a/0x2660 [scsi_transport_iscsi]\n[  276.965153]  iscsi_if_rx+0x198/0x4b0 [scsi_transport_iscsi]\n[  276.965546]  netlink_unicast+0x4d5/0x7b0\n[  276.965905]  netlink_sendmsg+0x78d/0xc30\n[  276.966236]  sock_sendmsg+0xe5/0x120\n[  276.966576]  ____sys_sendmsg+0x5fe/0x860\n[  276.966923]  ___sys_sendmsg+0xe0/0x170\n[  276.967300]  __sys_sendmsg+0xc8/0x170\n[  276.967666]  do_syscall_64+0x38/0x90\n[  276.968028]  entry_SYSCALL_64_after_hwframe+0x63/0xcd\n[  276.968773] Freed by task 2209:\n[  276.969111]  kasan_save_stack+0x1e/0x40\n[  276.969449]  kasan_set_track+0x21/0x30\n[  276.969789]  kasan_save_free_info+0x2a/0x50\n[  276.970146]  __kasan_slab_free+0x106/0x190\n[  276.970470]  __kmem_cache_free+0x133/0x270\n[  276.970816]  device_release+0x98/0x210\n[  276.971145]  kobject_cleanup+0x101/0x360\n[  276.971462]  iscsi_session_teardown+0x3fb/0x530 [libiscsi]\n[  276.971775]  iscsi_sw_tcp_session_destroy+0xd8/0x130 [iscsi_tcp]\n[  276.972143]  iscsi_if_recv_msg+0x1bf1/0x2660 [scsi_transport_iscsi]\n[  276.972485]  iscsi_if_rx+0x198/0x4b0 [scsi_transport_iscsi]\n[  276.972808]  netlink_unicast+0x4d5/0x7b0\n[  276.973201]  netlink_sendmsg+0x78d/0xc30\n[  276.973544]  sock_sendmsg+0xe5/0x120\n[  276.973864]  ____sys_sendmsg+0x5fe/0x860\n[  276.974248]  ___sys_\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: iscsi_tcp: Corrección de UAF durante el cierre de sesión al acceder a la dirección IP del shost Informe de error y análisis de Ding Hui. Durante el cierre de sesión de iSCSI, si otra tarea accede al atributo shost ipaddress, podemos obtener un informe UAF de KASAN como este: [ 276.942144] ERROR: KASAN: use-after-free en _raw_spin_lock_bh+0x78/0xe0 [ 276.942535] Escritura de tamaño 4 en la dirección ffff8881053b45b8 por la tarea cat/4088 [ 276.943511] CPU: 2 PID: 4088 Comm: cat Tainted: GE 6.1.0-rc8+ #3 [ 276.943997] Nombre del hardware: VMware, Inc. VMware Virtual Platform/440BX Desktop Reference Platform, BIOS 6.00 11/12/2020 [ 276.944470] Rastreo de llamadas: [ 276.944943]  [ 276.945397] dump_stack_lvl+0x34/0x48 [ 276.945887] print_address_description.constprop.0+0x86/0x1e7 [ 276.946421] print_report+0x36/0x4f [ 276.947358] kasan_report+0xad/0x130 [ 276.948234] kasan_check_range+0x35/0x1c0 [ 276.948674] _raw_spin_lock_bh+0x78/0xe0 [ 276.949989] iscsi_sw_tcp_host_get_param+0xad/0x2e0 [iscsi_tcp] [ 276.951765] show_host_param_ISCSI_HOST_PARAM_IPADDRESS+0xe9/0x130 [scsi_transport_iscsi] [ 276.952185] dev_attr_show+0x3f/0x80 [ 276.953005] sysfs_kf_seq_show+0x1fb/0x3e0 [ 276.953401] seq_read_iter+0x402/0x1020 [ 276.954260] vfs_read+0x532/0x7b0 [ 276.955113] ksys_read+0xed/0x1c0 [ 276.955952] do_syscall_64+0x38/0x90 [ 276.956347] entry_SYSCALL_64_after_hwframe+0x63/0xcd [ 276.956769] RIP: 0033:0x7f5d3a679222 [ 276.957161] Code: c0 e9 b2 fe ff ff 50 48 8d 3d 32 c0 0b 00 e8 a5 fe 01 00 0f 1f 44 00 00 f3 0f 1e fa 64 8b 04 25 18 00 00 00 85 c0 75 10 0f 05 &lt;48&gt; 3d 00 f0 ff ff 77 56 c3 0f 1f 44 00 00 48 83 ec 28 48 89 54 24 [ 276.958009] RSP: 002b:00007ffc864d16a8 EFLAGS: 00000246 ORIG_RAX: 0000000000000000 [ 276.958431] RAX: ffffffffffffffda RBX: 0000000000020000 RCX: 00007f5d3a679222 [ 276.958857] RDX: 0000000000020000 RSI: 00007f5d3a4fe000 RDI: 0000000000000003 [ 276.959281] RBP: 00007f5d3a4fe000 R08: 00000000ffffffff R09: 0000000000000000 [ 276.959682] R10: 0000000000000022 R11: 0000000000000246 R12: 0000000000020000 [ 276.960126] R13: 0000000000000003 R14: 0000000000000000 R15: 0000557a26dada58 [ 276.960536]  [ 276.961357] Allocated by task 2209: [ 276.961756] kasan_save_stack+0x1e/0x40 [ 276.962170] kasan_set_track+0x21/0x30 [ 276.962557] __kasan_kmalloc+0x7e/0x90 [ 276.962923] __kmalloc+0x5b/0x140 [ 276.963308] iscsi_alloc_session+0x28/0x840 [scsi_transport_iscsi] [ 276.963712] iscsi_session_setup+0xda/0xba0 [libiscsi] [ 276.964078] iscsi_sw_tcp_session_create+0x1fd/0x330 [iscsi_tcp] [ 276.964431] iscsi_if_create_session.isra.0+0x50/0x260 [scsi_transport_iscsi] [ 276.964793] iscsi_if_recv_msg+0xc5a/0x2660 [scsi_transport_iscsi] [ 276.965153] iscsi_if_rx+0x198/0x4b0 [scsi_transport_iscsi] [ 276.965546] netlink_unicast+0x4d5/0x7b0 [ 276.965905] netlink_sendmsg+0x78d/0xc30 [ 276.966236] sock_sendmsg+0xe5/0x120 [ 276.966576] ____sys_sendmsg+0x5fe/0x860 [ 276.966923] ___sys_sendmsg+0xe0/0x170 [ 276.967300] __sys_sendmsg+0xc8/0x170 [ 276.967666] do_syscall_64+0x38/0x90 [ 276.968028] entry_SYSCALL_64_after_hwframe+0x63/0xcd [ 276.968773] Freed by task 2209: [ 276.969111] kasan_save_stack+0x1e/0x40 [ 276.969449] kasan_set_track+0x21/0x30 [ 276.969789] kasan_save_free_info+0x2a/0x50 [ 276.970146] __kasan_slab_free+0x106/0x190 [ 276.970470] __kmem_cache_free+0x133/0x270 [ 276.970816] device_release+0x98/0x210 [ 276.971145] kobject_cleanup+0x101/0x360 [ 276.971462] iscsi_session_teardown+0x3fb/0x530 [libiscsi] [ 276.971775] iscsi_sw_tcp_session_destroy+0xd8/0x130 [iscsi_tcp] [ 276.972143] iscsi_if_recv_msg+0x1bf1/0x2660 [scsi_transport_iscsi] [ 276.972485] iscsi_if_rx+0x198/0x4b0 [scsi_transport_iscsi] [ 276.972808] netlink_unicast+0x4d5/0x7b0 [ 276.973201] netlink_sendmsg+0x78d/0xc30 [ 276.973544] sock_sendmsg+0xe5/0x120 [ 276.973864] ____sys_sendmsg+0x5fe/0x860 [ 276.974248] ___sys_---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/17b738590b97fb3fc287289971d1519ff9b875a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6f1d64b13097e85abda0f91b5638000afc5f9a06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8859687f5b242c0b057461df0a9ff51d5500783b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
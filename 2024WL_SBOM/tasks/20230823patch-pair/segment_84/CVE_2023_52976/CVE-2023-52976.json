{"cve_id": "CVE-2023-52976", "published_date": "2025-03-27T17:15:44.657", "last_modified_date": "2025-04-15T14:46:18.690", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nefi: fix potential NULL deref in efi_mem_reserve_persistent\n\nWhen iterating on a linked list, a result of memremap is dereferenced\nwithout checking it for NULL.\n\nThis patch adds a check that falls back on allocating a new page in\ncase memremap doesn't succeed.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE.\n\n[ardb: return -ENOMEM instead of breaking out of the loop]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: efi: se corrige una posible desreferenciación NULL en efi_mem_reserve_persistent. Al iterar en una lista enlazada, se desreferencia un resultado de memremap sin comprobar si es NULL. Este parche añade una comprobación que recurre a la asignación de una nueva página en caso de que memremap no tenga éxito. Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con SVACE. [ardb: devuelve -ENOMEM en lugar de salir del bucle]"}], "references": [{"url": "https://git.kernel.org/stable/c/87d4ff18738fd71e7e3c10827c80257da6283697", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/966d47e1f27c45507c5df82b2a2157e5a4fd3909", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a2e6a9ff89f13666a1c3ff7195612ab949ea9afc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d8fc0b5fb3e816a4a8684bcd3ed02cbef0fce23c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d92a25627bcdf264183670da73c9a60c0bac327e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
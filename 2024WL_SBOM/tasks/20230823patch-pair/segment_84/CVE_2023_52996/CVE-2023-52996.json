{"cve_id": "CVE-2023-52996", "published_date": "2025-03-27T17:15:48.310", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipv4: prevent potential spectre v1 gadget in fib_metrics_match()\n\nif (!type)\n        continue;\n    if (type > RTAX_MAX)\n        return false;\n    ...\n    fi_val = fi->fib_metrics->metrics[type - 1];\n\n@type being used as an array index, we need to prevent\ncpu speculation or risk leaking kernel memory content."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipv4: evitar un potencial gadget spectre v1 en fib_metrics_match() if (!type) continue; if (type &gt; RTAX_MAX) return false; ... fi_val = fi-&gt;fib_metrics-&gt;metrics[type - 1]; @type se utiliza como un índice de matriz, debemos evitar la especulación de la CPU o corremos el riesgo de filtrar el contenido de la memoria del kernel."}], "references": [{"url": "https://git.kernel.org/stable/c/5e9398a26a92fc402d82ce1f97cc67d832527da0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f9828fb1f688210e681268490576f0ca65c322a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8f0eb24f1a7a60ce635f0d757a46f1a37a4d467d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ca3cf947760de050d558293002ad3e7f4b8745d2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f9753ebd61be2d957b5504cbd3fd719674f05b7a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
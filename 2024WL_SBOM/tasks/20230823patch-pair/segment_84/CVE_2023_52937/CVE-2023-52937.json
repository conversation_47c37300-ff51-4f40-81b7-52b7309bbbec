{"cve_id": "CVE-2023-52937", "published_date": "2025-03-27T17:15:43.577", "last_modified_date": "2025-04-15T16:00:47.400", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nHV: hv_balloon: fix memory leak with using debugfs_lookup()\n\nWhen calling debugfs_lookup() the result must have dput() called on it,\notherwise the memory will leak over time.  To make things simpler, just\ncall debugfs_lookup_and_remove() instead which handles all of the logic\nat once."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: HV: hv_balloon: se corrige una fuga de memoria con debugfs_lookup(). Al llamar a debugfs_lookup(), se debe ejecutar dput() en el resultado; de lo contrario, la fuga de memoria se producirá con el tiempo. Para simplificar, simplemente llame a debugfs_lookup_and_remove(), que gestiona toda la lógica a la vez."}], "references": [{"url": "https://git.kernel.org/stable/c/0b570a059cf42ad6e2eb632f47c23813d58d8303", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6dfb0771429a63db8561d44147f2bb76f93e1c86", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
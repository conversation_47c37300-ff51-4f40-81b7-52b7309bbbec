{"cve_id": "CVE-2023-53009", "published_date": "2025-03-27T17:15:49.920", "last_modified_date": "2025-03-28T18:11:40.180", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amdkfd: Add sync after creating vram bo\n\nThere will be data corruption on vram allocated by svm\nif the initialization is not complete and application is\nwritting on the memory. Adding sync to wait for the\ninitialization completion is to resolve this issue."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amdkfd: Añadir sincronización después de crear la VRAM. Se producirán daños en los datos de la VRAM asignada por SVM si la inicialización no se completa y la aplicación está escribiendo en la memoria. Añadir sincronización para esperar a que se complete la inicialización resuelve este problema."}], "references": [{"url": "https://git.kernel.org/stable/c/92af2d3b57a1afdfdcafb1c6a07ffd89cf3e98fb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ba029e9991d9be90a28b6a0ceb25e9a6fb348829", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
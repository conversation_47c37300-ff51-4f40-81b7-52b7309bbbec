{"cve_id": "CVE-2022-49754", "published_date": "2025-03-27T17:15:40.423", "last_modified_date": "2025-04-14T20:16:54.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: Fix a buffer overflow in mgmt_mesh_add()\n\nSmatch Warning:\nnet/bluetooth/mgmt_util.c:375 mgmt_mesh_add() error: __memcpy()\n'mesh_tx->param' too small (48 vs 50)\n\nAnalysis:\n\n'mesh_tx->param' is array of size 48. This is the destination.\nu8 param[sizeof(struct mgmt_cp_mesh_send) + 29]; // 19 + 29 = 48.\n\nBut in the caller 'mesh_send' we reject only when len > 50.\nlen > (MGMT_MESH_SEND_SIZE + 31) // 19 + 31 = 50."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: se corrige un desbordamiento de búfer en mgmt_mesh_add() Smatch Advertencia: net/bluetooth/mgmt_util.c:375 error de mgmt_mesh_add(): __memcpy() 'mesh_tx-&gt;param' demasiado pequeño (48 frente a 50) Análisis: 'mesh_tx-&gt;param' es una matriz de tamaño 48. Este es el destino. u8 param[sizeof(struct mgmt_cp_mesh_send) + 29]; // 19 + 29 = 48. Pero en el llamador 'mesh_send' rechazamos solo cuando len &gt; 50. len &gt; (MGMT_MESH_SEND_SIZE + 31) // 19 + 31 = 50."}], "references": [{"url": "https://git.kernel.org/stable/c/2185e0fdbb2137f22a9dd9fcbf6481400d56299b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ed818fd8c531abf561b379995ee7cc4c68029464", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
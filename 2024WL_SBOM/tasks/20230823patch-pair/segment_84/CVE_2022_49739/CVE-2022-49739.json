{"cve_id": "CVE-2022-49739", "published_date": "2025-03-27T17:15:38.460", "last_modified_date": "2025-03-28T18:11:49.747", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ngfs2: Always check inode size of inline inodes\n\nCheck if the inode size of stuffed (inline) inodes is within the allowed\nrange when reading inodes from disk (gfs2_dinode_in()).  This prevents\nus from on-disk corruption.\n\nThe two checks in stuffed_readpage() and gfs2_unstuffer_page() that just\ntruncate inline data to the maximum allowed size don't actually make\nsense, and they can be removed now as well."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: gfs2: Comprobar siempre el tamaño de los inodos en línea. Se comprueba si el tamaño de los inodos rellenos (en línea) está dentro del rango permitido al leer inodos del disco (gfs2_dinode_in()). Esto evita la corrupción en disco. Las dos comprobaciones en stuffed_readpage() y gfs2_unstuffer_page(), que simplemente truncan los datos en línea al tamaño máximo permitido, no tienen sentido y también se pueden eliminar."}], "references": [{"url": "https://git.kernel.org/stable/c/45df749f827c286adbc951f2a4865b67f0442ba9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/46c9088cabd4d0469fdb61ac2a9c5003057fe94d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4d4cb76636134bf9a0c9c3432dae936f99954586", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/70376c7ff31221f1d21db5611d8209e677781d3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7c414f6f06e9a3934901b6edc3177ae5a1e07094", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d458a0984429c2d47e60254f5bc4119cbafe83a2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
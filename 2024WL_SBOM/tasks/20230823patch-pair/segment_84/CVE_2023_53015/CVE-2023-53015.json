{"cve_id": "CVE-2023-53015", "published_date": "2025-03-27T17:15:50.757", "last_modified_date": "2025-04-15T19:41:18.383", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nHID: betop: check shape of output reports\n\nbetopff_init() only checks the total sum of the report counts for each\nreport field to be at least 4, but hid_betopff_play() expects 4 report\nfields.\nA device advertising an output report with one field and 4 report counts\nwould pass the check but crash the kernel with a NULL pointer dereference\nin hid_betopff_play()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: HID: betop: comprobar la forma de los informes de salida. betopff_init() solo comprueba que la suma total de los recuentos de informes de cada campo sea al menos 4, pero hid_betopff_play() espera 4 campos. Un dispositivo que anuncia un informe de salida con un campo y 4 recuentos superaría la comprobación, pero bloquearía el kernel con una desreferencia de puntero NULL en hid_betopff_play()."}], "references": [{"url": "https://git.kernel.org/stable/c/07bc32e53c7bd5c91472cc485231ef6274db9b76", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1a2a47b85cab50a3c146731bfeaf2d860f5344ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/28fc6095da22dc88433d79578ae1c495ebe8ca43", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3782c0d6edf658b71354a64d60aa7a296188fc90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7317326f685824c7c29bd80841fd18041af6bb73", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d3065cc56221d1a5eda237e94eaf2a627b88ab79", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/dbab4dba400d6ea9a9697fbbd287adbf7db1dac4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
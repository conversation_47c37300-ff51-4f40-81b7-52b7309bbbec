{"cve_id": "CVE-2023-53022", "published_date": "2025-03-27T17:15:51.710", "last_modified_date": "2025-04-15T19:41:54.910", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: enetc: avoid deadlock in enetc_tx_onestep_tstamp()\n\nThis lockdep splat says it better than I could:\n\n================================\nWARNING: inconsistent lock state\n6.2.0-rc2-07010-ga9b9500ffaac-dirty #967 Not tainted\n--------------------------------\ninconsistent {IN-SOFTIRQ-W} -> {SOFTIRQ-ON-W} usage.\nkworker/1:3/179 [HC0[0]:SC0[0]:HE1:SE1] takes:\nffff3ec4036ce098 (_xmit_ETHER#2){+.?.}-{3:3}, at: netif_freeze_queues+0x5c/0xc0\n{IN-SOFTIRQ-W} state was registered at:\n  _raw_spin_lock+0x5c/0xc0\n  sch_direct_xmit+0x148/0x37c\n  __dev_queue_xmit+0x528/0x111c\n  ip6_finish_output2+0x5ec/0xb7c\n  ip6_finish_output+0x240/0x3f0\n  ip6_output+0x78/0x360\n  ndisc_send_skb+0x33c/0x85c\n  ndisc_send_rs+0x54/0x12c\n  addrconf_rs_timer+0x154/0x260\n  call_timer_fn+0xb8/0x3a0\n  __run_timers.part.0+0x214/0x26c\n  run_timer_softirq+0x3c/0x74\n  __do_softirq+0x14c/0x5d8\n  ____do_softirq+0x10/0x20\n  call_on_irq_stack+0x2c/0x5c\n  do_softirq_own_stack+0x1c/0x30\n  __irq_exit_rcu+0x168/0x1a0\n  irq_exit_rcu+0x10/0x40\n  el1_interrupt+0x38/0x64\nirq event stamp: 7825\nhardirqs last  enabled at (7825): [<ffffdf1f7200cae4>] exit_to_kernel_mode+0x34/0x130\nhardirqs last disabled at (7823): [<ffffdf1f708105f0>] __do_softirq+0x550/0x5d8\nsoftirqs last  enabled at (7824): [<ffffdf1f7081050c>] __do_softirq+0x46c/0x5d8\nsoftirqs last disabled at (7811): [<ffffdf1f708166e0>] ____do_softirq+0x10/0x20\n\nother info that might help us debug this:\n Possible unsafe locking scenario:\n\n       CPU0\n       ----\n  lock(_xmit_ETHER#2);\n  <Interrupt>\n    lock(_xmit_ETHER#2);\n\n *** DEADLOCK ***\n\n3 locks held by kworker/1:3/179:\n #0: ffff3ec400004748 ((wq_completion)events){+.+.}-{0:0}, at: process_one_work+0x1f4/0x6c0\n #1: ffff80000a0bbdc8 ((work_completion)(&priv->tx_onestep_tstamp)){+.+.}-{0:0}, at: process_one_work+0x1f4/0x6c0\n #2: ffff3ec4036cd438 (&dev->tx_global_lock){+.+.}-{3:3}, at: netif_tx_lock+0x1c/0x34\n\nWorkqueue: events enetc_tx_onestep_tstamp\nCall trace:\n print_usage_bug.part.0+0x208/0x22c\n mark_lock+0x7f0/0x8b0\n __lock_acquire+0x7c4/0x1ce0\n lock_acquire.part.0+0xe0/0x220\n lock_acquire+0x68/0x84\n _raw_spin_lock+0x5c/0xc0\n netif_freeze_queues+0x5c/0xc0\n netif_tx_lock+0x24/0x34\n enetc_tx_onestep_tstamp+0x20/0x100\n process_one_work+0x28c/0x6c0\n worker_thread+0x74/0x450\n kthread+0x118/0x11c\n\nbut I'll say it anyway: the enetc_tx_onestep_tstamp() work item runs in\nprocess context, therefore with softirqs enabled (i.o.w., it can be\ninterrupted by a softirq). If we hold the netif_tx_lock() when there is\nan interrupt, and the NET_TX softirq then gets scheduled, this will take\nthe netif_tx_lock() a second time and deadlock the kernel.\n\nTo solve this, use netif_tx_lock_bh(), which blocks softirqs from\nrunning."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: enetc: evitar bloqueo en enetc_tx_onestep_tstamp() Este splat de lockdep lo dice mejor de lo que yo podría: ================================ ADVERTENCIA: estado de bloqueo inconsistente 6.2.0-rc2-07010-ga9b9500ffaac-dirty #967 No contaminado -------------------------------- uso inconsistente de {IN-SOFTIRQ-W} -&gt; {SOFTIRQ-ON-W}. kworker/1:3/179 [HC0[0]:SC0[0]:HE1:SE1] toma: ffff3ec4036ce098 (_xmit_ETHER#2){+.?.}-{3:3}, en: netif_freeze_queues+0x5c/0xc0 {IN-SOFTIRQ-W} estado se registró en: _raw_spin_lock+0x5c/0xc0 sch_direct_xmit+0x148/0x37c __dev_queue_xmit+0x528/0x111c ip6_finish_output2+0x5ec/0xb7c ip6_finish_output+0x240/0x3f0 ip6_output+0x78/0x360 ndisc_send_skb+0x33c/0x85c ndisc_send_rs+0x54/0x12c addrconf_rs_timer+0x154/0x260 call_timer_fn+0xb8/0x3a0 __run_timers.part.0+0x214/0x26c run_timer_softirq+0x3c/0x74 __do_softirq+0x14c/0x5d8 ____do_softirq+0x10/0x20 call_on_irq_stack+0x2c/0x5c do_softirq_own_stack+0x1c/0x30 __irq_exit_rcu+0x168/0x1a0 irq_exit_rcu+0x10/0x40 el1_interrupt+0x38/0x64 marca de evento de irq: 7825 hardirqs habilitados por última vez en (7825): [] exit_to_kernel_mode+0x34/0x130 hardirqs deshabilitados por última vez en (7823): [] __do_softirq+0x550/0x5d8 softirqs habilitados por última vez en (7824): [] __do_softirq+0x46c/0x5d8 softirqs deshabilitados por última vez en (7811): [] ____do_softirq+0x10/0x20 otra información que podría ayudarnos a depurar esto: Posible escenario de bloqueo inseguro: CPU0 ---- lock(_xmit_ETHER#2);  lock(_xmit_ETHER#2); *** BLOQUEO INTERMEDIO *** 3 bloqueos mantenidos por kworker/1:3/179: #0: ffff3ec400004748 ((wq_completion)eventos){+.+.}-{0:0}, en: process_one_work+0x1f4/0x6c0 #1: ffff80000a0bbdc8 ((work_completion)(&amp;priv-&gt;tx_onestep_tstamp)){+.+.}-{0:0}, en: process_one_work+0x1f4/0x6c0 #2: ffff3ec4036cd438 (&amp;dev-&gt;tx_global_lock){+.+.}-{3:3}, en: netif_tx_lock+0x1c/0x34 Cola de trabajo: eventos enetc_tx_onestep_tstamp Rastreo de llamadas: print_usage_bug.part.0+0x208/0x22c mark_lock+0x7f0/0x8b0 __lock_acquire+0x7c4/0x1ce0 lock_acquire.part.0+0xe0/0x220 lock_acquire+0x68/0x84 _raw_spin_lock+0x5c/0xc0 netif_freeze_queues+0x5c/0xc0 netif_tx_lock+0x24/0x34 enetc_tx_onestep_tstamp+0x20/0x100 process_one_work+0x28c/0x6c0worker_thread+0x74/0x450 kthread+0x118/0x11c pero lo diré de todos modos: el elemento de trabajo enetc_tx_onestep_tstamp() se ejecuta en el contexto del proceso, por lo tanto, con softirqs habilitado (Es decir, puede ser interrumpido por un softirq). Si mantenemos la ejecución de netif_tx_lock() cuando hay una interrupción, y luego se programa el softirq NET_TX, se ejecutará netif_tx_lock() por segunda vez y se bloqueará el kernel. Para solucionar esto, use netif_tx_lock_bh(), que impide la ejecución de los softirq."}], "references": [{"url": "https://git.kernel.org/stable/c/3c463721a73bdb57a913e0d3124677a3758886fc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8232e5a84d25a84a5cbda0f241a00793fb6eb608", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e893dced1a18e77b1262f5c10169413f0ece0da7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
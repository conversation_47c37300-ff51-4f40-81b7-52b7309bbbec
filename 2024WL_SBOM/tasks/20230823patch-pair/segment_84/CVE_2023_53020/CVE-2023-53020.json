{"cve_id": "CVE-2023-53020", "published_date": "2025-03-27T17:15:51.457", "last_modified_date": "2025-04-15T19:41:50.600", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nl2tp: close all race conditions in l2tp_tunnel_register()\n\nThe code in l2tp_tunnel_register() is racy in several ways:\n\n1. It modifies the tunnel socket _after_ publishing it.\n\n2. It calls setup_udp_tunnel_sock() on an existing socket without\n   locking.\n\n3. It changes sock lock class on fly, which triggers many syzbot\n   reports.\n\nThis patch amends all of them by moving socket initialization code\nbefore publishing and under sock lock. As suggested by <PERSON><PERSON><PERSON>, the\nl2tp lockdep class is not necessary as we can just switch to\nbh_lock_sock_nested()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: l2tp: cierra todas las condiciones de ejecución en l2tp_tunnel_register() El código en l2tp_tunnel_register() es de alto riesgo de varias maneras: 1. Modifica el socket _after_ publishing it. 2. Llama a setup_udp_tunnel_sock() en un socket existente sin bloquearlo. 3. Cambia la clase de bloqueo de sock sobre la marcha, lo que activa muchos informes de syzbot. Este parche las enmienda todas moviendo el código de inicialización del socket antes de publicarlo y bajo el bloqueo de sock. Como sugirió Jakub, la clase l2tp lockdep no es necesaria ya que podemos simplemente cambiar a bh_lock_sock_nested()."}], "references": [{"url": "https://git.kernel.org/stable/c/0b2c59720e65885a394a017d0cf9cab118914682", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2d77e5c0ad79004b5ef901895437e9cce6dfcc7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/77e8ed776cdb1a24b2aab8fe7c6f1f154235e1ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cef0845b6dcfa2f6c2c832e7f9622551456c741d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
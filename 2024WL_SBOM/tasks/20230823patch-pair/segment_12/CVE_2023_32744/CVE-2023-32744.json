{"cve_id": "CVE-2023-32744", "published_date": "2023-11-09T21:15:24.370", "last_modified_date": "2024-11-21T08:03:57.387", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in WooCommerce Product Recommendations plugin <= 2.3.0 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento WooCommerce Product Recommendations en versiones &lt;= 2.3.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/woocommerce-product-recommendations/wordpress-woocommerce-product-recommendations-plugin-2-3-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
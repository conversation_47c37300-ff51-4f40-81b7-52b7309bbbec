{"cve_id": "CVE-2023-34378", "published_date": "2023-11-13T02:15:08.230", "last_modified_date": "2024-11-21T08:07:08.313", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in scriptburn.Com WP Hide Post plugin <= 2.0.10 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento scriptburn.Com WP Hide Post en versiones &lt;= 2.0.10."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-hide-post/wordpress-wp-hide-post-plugin-2-0-10-cross-site-request-forgery-csrf-leading-to-post-status-change-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
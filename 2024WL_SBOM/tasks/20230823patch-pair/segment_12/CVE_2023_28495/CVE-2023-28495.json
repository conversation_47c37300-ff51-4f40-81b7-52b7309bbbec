{"cve_id": "CVE-2023-28495", "published_date": "2023-11-12T22:15:28.580", "last_modified_date": "2024-11-21T07:55:13.737", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in MyThemeShop WP Shortcode by MyThemeShop plugin <= 1.4.16 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento MyThemeShop WP Shortcode by MyThemeShop en versiones &lt;= 1.4.16."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-shortcode/wordpress-wp-shortcode-by-mythemeshop-plugin-1-4-16-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
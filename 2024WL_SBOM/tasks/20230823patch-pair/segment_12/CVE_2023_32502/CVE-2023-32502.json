{"cve_id": "CVE-2023-32502", "published_date": "2023-11-09T23:15:09.823", "last_modified_date": "2024-11-21T08:03:29.543", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Sybre Waaijer Pro Mime Types – Manage file media types plugin <= 1.0.7 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Sybre Waaijer Pro Mime Types – Manage file media types en versiones &lt;= 1.0.7."}], "references": [{"url": "https://patchstack.com/database/vulnerability/pro-mime-types/wordpress-pro-mime-types-plugin-1-0-7-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
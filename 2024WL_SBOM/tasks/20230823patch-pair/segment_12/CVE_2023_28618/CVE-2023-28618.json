{"cve_id": "CVE-2023-28618", "published_date": "2023-11-12T22:15:29.190", "last_modified_date": "2024-11-21T07:55:40.760", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Mario<PERSON> Alexandrou Enhanced Plugin Admin plugin <= 1.16 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Marios Alexandrou Enhanced Plugin Admin en versiones &lt;= 1.16."}], "references": [{"url": "https://patchstack.com/database/vulnerability/enhanced-plugin-admin/wordpress-enhanced-plugin-admin-plugin-1-16-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
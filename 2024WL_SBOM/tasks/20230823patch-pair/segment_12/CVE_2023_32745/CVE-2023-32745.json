{"cve_id": "CVE-2023-32745", "published_date": "2023-11-09T21:15:24.463", "last_modified_date": "2024-11-21T08:03:57.510", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in WooCommerce AutomateWoo plugin <= 5.7.1 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento WooCommerce AutomateWoo en versiones &lt;= 5.7.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/automatewoo/wordpress-automatewoo-plugin-5-7-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
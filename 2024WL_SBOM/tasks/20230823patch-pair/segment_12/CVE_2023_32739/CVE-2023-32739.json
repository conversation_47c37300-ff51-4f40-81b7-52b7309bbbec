{"cve_id": "CVE-2023-32739", "published_date": "2023-11-09T21:15:24.270", "last_modified_date": "2025-02-19T22:15:18.187", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Web_Trendy WP Custom Cursors | WordPress Cursor Plugin plugin < 3.2 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Web_Trendy WP Custom Cursors | WordPress Cursor Plugin en versiones &lt; 3.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-custom-cursors/wordpress-wp-custom-cursors-plugin-3-2-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-32701", "published_date": "2023-11-14T19:15:27.163", "last_modified_date": "2024-11-21T08:03:52.980", "descriptions": [{"lang": "en", "value": " Improper Input Validation in the Networking Stack of QNX SDP version(s) 6.6, 7.0, and 7.1 could allow an attacker to potentially cause Information Disclosure or a Denial-of-Service condition. \n\n"}, {"lang": "es", "value": "Una validación de entrada inadecuada en Networking Stack de QNX SDP versiones 6.6, 7.0 y 7.1 podría permitir que un atacante cause potencialmente la divulgación de información o una condición de denegación de servicio."}], "references": [{"url": "https://support.blackberry.com/kb/articleDetail?articleNumber=000112401", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2023-20596", "published_date": "2023-11-14T19:15:16.083", "last_modified_date": "2024-11-21T07:41:11.750", "descriptions": [{"lang": "en", "value": "Improper input validation in the SMM Supervisor may allow an attacker with a compromised SMI handler to gain Ring0 access potentially leading to arbitrary code execution.\n\n\n\n\n\n\n\n\n"}, {"lang": "es", "value": "Una validación de entrada incorrecta en SMM Supervisor puede permitir que un atacante con un controlador SMI comprometido obtenga acceso a Ring0, lo que podría conducir a la ejecución de código arbitrario."}], "references": [{"url": "https://www.amd.com/en/corporate/product-security/bulletin/AMD-SB-7011", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
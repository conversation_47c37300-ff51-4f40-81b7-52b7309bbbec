{"cve_id": "CVE-2023-28498", "published_date": "2023-11-12T22:15:29.007", "last_modified_date": "2024-11-21T07:55:14.170", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in MotoPress Hotel Booking Lite plugin <= 4.6.0 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento MotoPress Hotel Booking Lite en versiones &lt;= 4.6.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/motopress-hotel-booking-lite/wordpress-hotel-booking-lite-plugin-4-6-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-26531", "published_date": "2023-11-13T01:15:07.410", "last_modified_date": "2024-11-21T07:51:41.400", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in 闪电博 多合一搜索自动推送管理插件-支持Baidu/Google/Bing/IndexNow/Yandex/头条 allows Cross Site Request Forgery.This issue affects 多合一搜索自动推送管理插件-支持Baidu/Google/Bing/IndexNow/Yandex/头条: from n/a through 4.2.7."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento ??? ????????????-??Baidu/Google/Bing/IndexNow/Yandex/?? en versiones &lt;= 4.2.7."}], "references": [{"url": "https://patchstack.com/database/vulnerability/baidu-submit-link/wordpress-baidu-google-bing-indexnow-yandex-plugin-4-2-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2021-43609", "published_date": "2023-11-09T00:15:07.663", "last_modified_date": "2024-11-21T06:29:30.367", "descriptions": [{"lang": "en", "value": "An issue was discovered in Spiceworks Help Desk Server before 1.3.3. A Blind Boolean SQL injection vulnerability within the order_by_for_ticket function in app/models/reporting/database_query.rb allows an authenticated attacker to execute arbitrary SQL commands via the sort parameter. This can be leveraged to leak local files from the host system, leading to remote code execution (RCE) through deserialization of malicious data."}, {"lang": "es", "value": "Se descubrió un problema en Spiceworks Help Desk Server antes de la versión 1.3.3. Una vulnerabilidad de inyección Blind Boolean SQL dentro de la función order_by_for_ticket en app/models/reporting/database_query.rb permite a un atacante autenticado ejecutar comandos SQL arbitrarios a través del parámetro sort. Esto se puede aprovechar para filtrar archivos locales del sistema host, lo que lleva a la ejecución remota de código (RCE) mediante la deserialización de datos maliciosos."}], "references": [{"url": "https://community.spiceworks.com/blogs/help-desk-server-release-notes/3610-1-3-2-1-3-3", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/d5sec/CVE-2021-43609-POC", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.linkedin.com/pulse/cve-2021-43609-write-up-division5-security-4lgwe", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
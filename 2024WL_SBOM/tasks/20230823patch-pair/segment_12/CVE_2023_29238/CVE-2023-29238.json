{"cve_id": "CVE-2023-29238", "published_date": "2023-11-12T22:15:30.147", "last_modified_date": "2024-11-21T07:56:44.603", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Whydonate Whydonate – FREE Donate button – Crowdfunding – Fundraising plugin <= 3.12.15 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Whydonate Whydonate – FREE Donate button – Crowdfunding – Fundraising en versiones &lt;= 3.12.15."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-whydonate/wordpress-whydonate-plugin-3-12-13-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
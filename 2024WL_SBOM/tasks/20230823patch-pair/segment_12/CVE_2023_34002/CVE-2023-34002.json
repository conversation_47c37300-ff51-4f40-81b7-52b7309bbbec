{"cve_id": "CVE-2023-34002", "published_date": "2023-11-09T18:15:07.780", "last_modified_date": "2024-11-21T08:06:22.833", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in WP Inventory Manager plugin <= ******** versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento WP Inventory Manager en versiones &lt;= ********."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-inventory-manager/wordpress-wp-inventory-manager-plugin-2-1-0-13-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-27434", "published_date": "2023-11-13T00:15:08.350", "last_modified_date": "2024-11-21T07:52:54.320", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in WPGrim Classic Editor and Classic Widgets plugin <= 1.2.5 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento WPGrim Classic Editor and Classic Widgets en versiones &lt;= 1.2.5."}], "references": [{"url": "https://patchstack.com/database/vulnerability/classic-editor-and-classic-widgets/wordpress-classic-editor-and-classic-widgets-plugin-1-2-4-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
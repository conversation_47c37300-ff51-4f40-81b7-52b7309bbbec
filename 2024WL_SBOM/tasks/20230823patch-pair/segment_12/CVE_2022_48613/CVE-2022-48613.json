{"cve_id": "CVE-2022-48613", "published_date": "2023-11-08T10:15:08.400", "last_modified_date": "2024-11-21T07:33:36.170", "descriptions": [{"lang": "en", "value": "Race condition vulnerability in the kernel module. Successful exploitation of this vulnerability may cause variable values to be read with the condition evaluation bypassed."}, {"lang": "es", "value": "Vulnerabilidad de condición de ejecución en el módulo del kernel. La explotación exitosa de esta vulnerabilidad puede causar que los valores de las variables se lean sin pasar omitiendo la evaluación de la condición."}], "references": [{"url": "https://consumer.huawei.com/en/support/bulletin/2023/11/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://device.harmonyos.com/en/docs/security/update/security-bulletins-202311-0000001729189597", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
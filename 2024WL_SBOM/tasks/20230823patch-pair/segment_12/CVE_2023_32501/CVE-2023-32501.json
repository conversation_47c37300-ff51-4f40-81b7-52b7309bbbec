{"cve_id": "CVE-2023-32501", "published_date": "2023-11-09T23:15:09.733", "last_modified_date": "2024-11-21T08:03:29.357", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in E4J s.R.L. VikBooking Hotel Booking Engine & PMS plugin <= 1.6.1 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento E4J s.R.L. VikBooking Hotel Booking Engine &amp; PMS en versiones &lt;= 1.6.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/vikbooking/wordpress-vikbooking-hotel-booking-engine-pms-plugin-1-6-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
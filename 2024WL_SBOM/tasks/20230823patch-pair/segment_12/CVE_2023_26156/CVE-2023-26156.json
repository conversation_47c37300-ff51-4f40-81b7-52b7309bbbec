{"cve_id": "CVE-2023-26156", "published_date": "2023-11-09T05:15:09.230", "last_modified_date": "2024-11-21T07:50:53.900", "descriptions": [{"lang": "en", "value": "Versions of the package chromedriver before 119.0.1 are vulnerable to Command Injection when setting the chromedriver.path to an arbitrary system binary. This could lead to unauthorized access and potentially malicious actions on the host system.\r\r**Note:**\r\rAn attacker must have access to the system running the vulnerable chromedriver library to exploit it. The success of exploitation also depends on the permissions and privileges of the process running chromedriver."}, {"lang": "es", "value": "Las versiones del paquete chromedriver anteriores a 119.0.1 son vulnerables a la inyección de comandos cuando se configura chromedriver.path en un sistema binario arbitrario. Esto podría provocar un acceso no autorizado y acciones potencialmente maliciosas en el sistema host. **Nota:** Un atacante debe tener acceso al sistema que ejecuta la librería chromedriver vulnerable para explotarla. El éxito de la explotación también depende de los permisos y privilegios del proceso que ejecuta Chromedriver."}], "references": [{"url": "https://gist.github.com/mcoimbra/47b1da554a80795c45126d51e41b2b18", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/giggio/node-chromedriver/commit/de961e34e023afcf4fa5c0faeeec69aaa6c3c815", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://security.snyk.io/vuln/SNYK-JS-CHROMEDRIVER-6049539", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
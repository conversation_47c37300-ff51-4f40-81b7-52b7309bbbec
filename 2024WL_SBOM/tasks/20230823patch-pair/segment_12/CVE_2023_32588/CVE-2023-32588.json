{"cve_id": "CVE-2023-32588", "published_date": "2023-11-13T02:15:07.850", "last_modified_date": "2024-11-21T08:03:39.337", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in BRANDbrilliance Post State Tags plugin <= 2.0.6 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento BRANDbrilliance Post State Tags en versiones &lt;= 2.0.6."}], "references": [{"url": "https://patchstack.com/database/vulnerability/post-state-tags/wordpress-post-state-tags-plugin-2-0-6-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2021-46758", "published_date": "2023-11-14T19:15:10.310", "last_modified_date": "2024-11-21T06:34:39.233", "descriptions": [{"lang": "en", "value": "Insufficient validation of SPI flash addresses in the ASP (AMD Secure Processor) bootloader may allow an attacker to read data in memory mapped beyond SPI flash resulting in a potential loss of availability and integrity.\n\n\n\n\n\n\n\n\n"}, {"lang": "es", "value": "La validación insuficiente de las direcciones flash SPI en el cargador de arranque ASP (AMD Secure Processor) puede permitir que un atacante lea datos en la memoria asignada más allá de la flash SPI, lo que resulta en una posible pérdida de disponibilidad e integridad."}], "references": [{"url": "https://www.amd.com/en/corporate/product-security/bulletin/AMD-SB-4002", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
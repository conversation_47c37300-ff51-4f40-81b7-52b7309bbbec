{"cve_id": "CVE-2023-26543", "published_date": "2023-11-13T01:15:07.610", "last_modified_date": "2024-11-21T07:51:42.870", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Aleksandr <PERSON> WP Meteor Website Speed Optimization Addon plugin <= 3.1.4 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Aleksandr <PERSON>vitch WP Meteor Website Speed Optimization Addon en versiones &lt;= 3.1.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-meteor/wordpress-wp-meteor-page-speed-optimization-topping-plugin-3-1-4-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
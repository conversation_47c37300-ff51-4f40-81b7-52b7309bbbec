{"cve_id": "CVE-2023-33207", "published_date": "2023-11-13T02:15:08.037", "last_modified_date": "2024-11-21T08:05:07.897", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Krzysztof Wielogórski Stop Referrer Spam plugin <= 1.3.0 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Krzysztof Wielogórski Stop Referrer Spam en versiones &lt;= 1.3.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/stop-referrer-spam/wordpress-stop-referrer-spam-plugin-1-2-8-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-28002", "published_date": "2023-11-14T18:15:29.137", "last_modified_date": "2024-11-21T07:53:54.707", "descriptions": [{"lang": "en", "value": "An improper validation of integrity check value vulnerability [CWE-354] in FortiOS 7.2.0 through 7.2.3, 7.0.0 through 7.0.12, 6.4 all versions, 6.2 all versions, 6.0 all versions and VMs may allow a local attacker with admin privileges to boot a malicious image on the device and bypass the filesystem integrity check in place."}, {"lang": "es", "value": "Una vulnerabilidad de validación inadecuada del valor de verificación de integridad [CWE-354] en FortiOS 7.2.0 a 7.2.3, 7.0.0 a 7.0.12, 6.4 todas las versiones, 6.2 todas las versiones, 6.0 todas las versiones y FortiProxy 7.2 todas las versiones, 7.0 todas versiones, 2.0 todas las versiones. Las máquinas virtuales pueden permitir que un atacante local con privilegios de administrador inicie una imagen maliciosa en el dispositivo y omita la verificación de integridad del sistema de archivos vigente."}], "references": [{"url": "https://fortiguard.com/psirt/FG-IR-22-396", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
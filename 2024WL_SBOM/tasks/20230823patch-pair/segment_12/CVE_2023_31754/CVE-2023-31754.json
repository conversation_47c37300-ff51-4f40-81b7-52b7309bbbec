{"cve_id": "CVE-2023-31754", "published_date": "2023-11-14T04:15:07.697", "last_modified_date": "2024-11-21T08:02:14.660", "descriptions": [{"lang": "en", "value": "Optimizely CMS UI before v12.16.0 was discovered to contain a cross-site scripting (XSS) vulnerability via the Admin panel."}, {"lang": "es", "value": "Se descubrió que la interfaz de usuario de Optimizely CMS anterior a v12.16.0 contenía una vulnerabilidad de Cross-Site Scripting (XSS) a través del panel de Administración."}], "references": [{"url": "https://labs.withsecure.com/advisories/optimizely-admin-panel-dom-xss", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
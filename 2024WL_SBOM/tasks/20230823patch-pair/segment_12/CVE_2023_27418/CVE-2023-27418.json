{"cve_id": "CVE-2023-27418", "published_date": "2023-11-12T23:15:08.320", "last_modified_date": "2024-11-21T07:52:52.483", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Wow-Company Side Menu Lite – add sticky fixed buttons plugin <= 4.0 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en Wow-Company Side Menu Lite en el complemento add sticky fixed buttons en versiones &lt;= 4.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/side-menu-lite/wordpress-side-menu-lite-plugin-4-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
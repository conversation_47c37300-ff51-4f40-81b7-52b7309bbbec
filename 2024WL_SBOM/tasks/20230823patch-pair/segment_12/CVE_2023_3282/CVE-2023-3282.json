{"cve_id": "CVE-2023-3282", "published_date": "2023-11-08T18:15:07.827", "last_modified_date": "2024-11-21T08:16:55.490", "descriptions": [{"lang": "en", "value": "A local privilege escalation (PE) vulnerability in the Palo Alto Networks Cortex XSOAR engine software running on a Linux operating system enables a local attacker to execute programs with elevated privileges if the attacker has shell access to the engine."}, {"lang": "es", "value": "Una vulnerabilidad de escalada de privilegios local (PE) en el software del motor Cortex XSOAR de Palo Alto Networks que se ejecuta en un sistema operativo Linux permite a un atacante local ejecutar programas con privilegios elevados si el atacante tiene acceso de shell al motor."}], "references": [{"url": "https://security.paloaltonetworks.com/CVE-2023-3282", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
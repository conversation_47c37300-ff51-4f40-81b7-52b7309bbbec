{"cve_id": "CVE-2023-34371", "published_date": "2023-11-09T19:15:08.307", "last_modified_date": "2024-11-21T08:07:07.030", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Didier Sampaolo SpamReferrerBlock plugin <= 2.22 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento Didier Sampaolo SpamReferrerBlock en versiones &lt;= 2.22."}], "references": [{"url": "https://patchstack.com/database/vulnerability/spamreferrerblock/wordpress-spamreferrerblock-plugin-2-22-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
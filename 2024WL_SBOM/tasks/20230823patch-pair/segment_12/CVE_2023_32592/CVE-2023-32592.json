{"cve_id": "CVE-2023-32592", "published_date": "2023-11-09T22:15:10.900", "last_modified_date": "2024-11-21T08:03:39.843", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Palasthotel by <PERSON>, <PERSON><PERSON><PERSON> Search plugin <= 1.0.2 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en Palasthotel por <PERSON>, complemento <PERSON><PERSON><PERSON> en versiones &lt;= 1.0.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/fast-search-powered-by-solr/wordpress-sunny-search-plugin-1-0-2-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-31078", "published_date": "2023-11-10T14:15:35.997", "last_modified_date": "2024-11-21T08:01:22.183", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Marco <PERSON> WP BrowserUpdate plugin <= 4.4.1 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento <PERSON> WP BrowserUpdate en versiones &lt;= 4.4.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-browser-update/wordpress-wp-browserupdate-plugin-4-4-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
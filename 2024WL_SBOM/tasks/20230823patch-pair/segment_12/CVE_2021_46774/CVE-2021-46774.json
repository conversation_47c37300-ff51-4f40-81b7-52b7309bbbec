{"cve_id": "CVE-2021-46774", "published_date": "2023-11-14T19:15:10.407", "last_modified_date": "2024-11-21T06:34:41.667", "descriptions": [{"lang": "en", "value": "Insufficient DRAM address validation in System\nManagement Unit (SMU) may allow an attacker to read/write from/to an invalid\nDRAM address, potentially resulting in denial-of-service."}, {"lang": "es", "value": "Una validación de dirección DRAM insuficiente en System Management Unit (SMU) puede permitir que un atacante lea/escriba desde/hacia una dirección DRAM no válida, lo que podría provocar una denegación de servicio."}], "references": [{"url": "https://www.amd.com/en/corporate/product-security/bulletin/AMD-SB-3002", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.amd.com/en/corporate/product-security/bulletin/AMD-SB-4002", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.amd.com/en/corporate/product-security/bulletin/AMD-SB-5001", "source": "<EMAIL>", "tags": []}]}
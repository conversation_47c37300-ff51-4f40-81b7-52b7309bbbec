{"cve_id": "CVE-2021-46748", "published_date": "2023-11-14T19:15:10.267", "last_modified_date": "2025-02-13T17:15:34.023", "descriptions": [{"lang": "en", "value": "Insufficient bounds checking in the ASP (AMD Secure Processor) may allow an attacker to access memory outside the bounds of what is permissible to a TA (Trusted Application) resulting in a potential denial of service."}, {"lang": "es", "value": "Una verificación de límites insuficiente en el ASP (AMD Secure Processor) puede permitir que un atacante acceda a la memoria fuera de los límites de lo permitido para una TA (Trusted Application), lo que resulta en una posible denegación de servicio."}], "references": [{"url": "https://www.amd.com/en/corporate/product-security/bulletin/AMD-SB-6003", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.intel.com/content/www/us/en/security-center/advisory/intel-sa-00971.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
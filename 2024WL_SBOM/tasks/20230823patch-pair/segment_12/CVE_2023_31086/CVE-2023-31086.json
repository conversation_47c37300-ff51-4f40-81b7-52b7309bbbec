{"cve_id": "CVE-2023-31086", "published_date": "2023-11-09T23:15:09.000", "last_modified_date": "2024-11-21T08:01:23.310", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Igor <PERSON>ic Simple Giveaways – Grow your business, email lists and traffic with contests plugin <= 2.46.0 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en <PERSON> Simple Giveaways complemento: haga crecer su negocio, sus listas de correo electrónico y su tráfico con el complemento de concursos en versiones &lt;= 2.46.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/giveasap/wordpress-simple-giveaways-plugin-2-45-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
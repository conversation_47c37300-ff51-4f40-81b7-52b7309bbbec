{"cve_id": "CVE-2025-30612", "published_date": "2025-03-24T14:15:33.823", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in mandegarweb Replace Default Words allows Stored XSS. This issue affects Replace Default Words: from n/a through 1.3."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en mandegarweb Replace Default Words permite XSS almacenado. Este problema afecta a \"Reemplazar palabras predeterminadas\" desde n/d hasta la versión 1.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/replace-default-words/vulnerability/wordpress-replace-default-words-plugin-1-3-cross-site-request-forgery-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
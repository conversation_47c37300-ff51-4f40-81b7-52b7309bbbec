{"cve_id": "CVE-2025-2290", "published_date": "2025-03-19T05:15:41.180", "last_modified_date": "2025-07-11T21:23:28.510", "descriptions": [{"lang": "en", "value": "The LifterLMS – WP LMS for eLearning, Online Courses, & Quizzes plugin for WordPress is vulnerable to Unauthenticated Post Trashing due to a missing capability check on the delete_access_plan function and the related AJAX calls in all versions up to, and including, 8.0.1. This makes it possible for unauthenticated attackers to change status to \"Trash\" for every published post, therefore limiting the availability of the website's content."}, {"lang": "es", "value": "El complemento LifterLMS – WP LMS for eLearning, Online Courses, &amp; Quizzes para WordPress es vulnerable al borrado de entradas no autenticadas debido a la falta de comprobación de capacidad en la función delete_access_plan y las llamadas AJAX relacionadas en todas las versiones hasta la 8.0.1 incluida. Esto permite que atacantes no autenticados cambien el estado a \"Papelera\" para cada entrada publicada, limitando así la disponibilidad del contenido del sitio web."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3257328/lifterlms/trunk/includes/class.llms.ajax.handler.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4f64dbf2-b75a-4a35-9b4e-413b8fd1fff0?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
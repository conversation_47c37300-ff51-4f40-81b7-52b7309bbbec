{"cve_id": "CVE-2025-30162", "published_date": "2025-03-24T19:15:52.767", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Cilium is a networking, observability, and security solution with an eBPF-based dataplane. For Cilium users who use Gateway API for Ingress for some services and use LB-IPAM or BGP for LB Service implementation and use network policies to block egress traffic from workloads in a namespace to workloads in other namespaces, egress traffic from workloads covered by such network policies to LoadBalancers configured by `Gateway` resources will incorrectly be allowed. LoadBalancer resources not deployed via a Gateway API configuration are not affected by this issue. This issue affects: Cilium v1.15 between v1.15.0 and v1.15.14 inclusive, v1.16 between v1.16.0 and v1.16.7 inclusive, and v1.17 between v1.17.0 and v1.17.1 inclusive. This issue is fixed in Cilium v1.15.15, v1.16.8, and v1.17.2. A Clusterwide Cilium Network Policy can be used to work around this issue for users who are unable to upgrade."}, {"lang": "es", "value": "Cilium es una solución de redes, observabilidad y seguridad con un plano de datos basado en eBPF. Para los usuarios de Cilium que utilizan la API de Gateway para Ingress en algunos servicios y LB-IPAM o BGP para la implementación del servicio LB, y que utilizan políticas de red para bloquear el tráfico de salida de cargas de trabajo en un espacio de nombres a cargas de trabajo en otros espacios de nombres, se permitirá incorrectamente el tráfico de salida de cargas de trabajo cubiertas por dichas políticas de red a los balanceadores de carga configurados por recursos de \"Gateway\". Los recursos de balanceadores de carga que no se implementan mediante una configuración de la API de Gateway no se ven afectados por este problema. Este problema afecta a: Cilium v1.15 entre v1.15.0 y v1.15.14 inclusive, v1.16 entre v1.16.0 y v1.16.7 inclusive, y v1.17 entre v1.17.0 y v1.17.1 inclusive. Este problema se ha corregido en Cilium v1.15.15, v1.16.8 y v1.17.2. Se puede utilizar una política de red Cilium para todo el clúster para solucionar este problema para los usuarios que no pueden actualizar."}], "references": [{"url": "https://docs.cilium.io/en/stable/network/lb-ipam", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cilium/cilium/security/advisories/GHSA-24qp-4xx8-3jvj", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cilium/proxy/pull/1172", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-30552", "published_date": "2025-03-24T14:15:24.407", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Donald <PERSON> WordPress Admin Bar Improved allows Stored XSS. This issue affects WordPress Admin Bar Improved: from n/a through 3.3.5."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en <PERSON> WordPress Admin Bar Improved permite XSS almacenado. Este problema afecta a WordPress Admin Bar Improved: desde n/d hasta la versión 3.3.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wordpress-admin-bar-improved/vulnerability/wordpress-wordpress-admin-bar-improved-plugin-3-3-5-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0182", "published_date": "2025-03-20T10:15:50.910", "last_modified_date": "2025-03-20T10:15:50.910", "descriptions": [{"lang": "en", "value": "A vulnerability in danswer-ai/danswer version 0.9.0 allows for denial of service through memory exhaustion. The issue arises from the use of a vulnerable version of the starlette package (<=0.49) via fastapi, which was patched in fastapi version 0.115.3. The vulnerability can be exploited by sending multiple requests to the /auth/saml/callback endpoint, leading to uncontrolled memory consumption and eventual denial of service."}, {"lang": "es", "value": "Una vulnerabilidad en danswer-ai/danswer versión 0.9.0 permite la denegación de servicio por agotamiento de memoria. El problema surge del uso de una versión vulnerable del paquete starlette (&lt;=0.49) a través de fastapi, parcheada en fastapi versión 0.115.3. Esta vulnerabilidad puede explotarse enviando múltiples solicitudes al endpoint /auth/saml/callback, lo que provoca un consumo descontrolado de memoria y, finalmente, la denegación de servicio."}], "references": [{"url": "https://huntr.com/bounties/969b8056-b66c-4d70-8f77-04c1cbdc1d1a", "source": "<EMAIL>", "tags": []}]}
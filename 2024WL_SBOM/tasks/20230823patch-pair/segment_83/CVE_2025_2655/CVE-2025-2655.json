{"cve_id": "CVE-2025-2655", "published_date": "2025-03-23T17:15:29.687", "last_modified_date": "2025-05-13T20:30:13.660", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester AC Repair and Services System 1.0. It has been declared as critical. This vulnerability affects the function save_users of the file /classes/Users.php. The manipulation of the argument ID leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester AC Repair and Services System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta a la función save_users del archivo /classes/Users.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/Colorado-all/cve/blob/main/AC%20Repair%20and%20Services%20System%20using/SQL-8.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300670", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300670", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520017", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.sourcecodester.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
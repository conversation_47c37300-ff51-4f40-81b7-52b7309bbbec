{"cve_id": "CVE-2025-2751", "published_date": "2025-03-25T08:15:20.013", "last_modified_date": "2025-07-17T21:51:24.720", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Open Asset Import Library Assimp 5.4.3 and classified as problematic. This vulnerability affects the function Assimp::CSMImporter::InternReadFile of the file code/AssetLib/CSM/CSMLoader.cpp of the component CSM File Handler. The manipulation of the argument na leads to out-of-bounds read. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en Open Asset Import Library Assimp 5.4.3, clasificada como problemática. Esta vulnerabilidad afecta a la función Assimp::CSMImporter::InternReadFile del archivo code/AssetLib/CSM/CSMLoader.cpp del componente CSM File Handler. La manipulación del argumento \"na\" provoca una lectura fuera de los límites. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6012", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6012#issue-2877369817", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300856", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300856", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517785", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
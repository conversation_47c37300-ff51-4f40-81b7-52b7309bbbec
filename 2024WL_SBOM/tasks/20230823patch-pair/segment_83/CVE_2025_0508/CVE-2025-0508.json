{"cve_id": "CVE-2025-0508", "published_date": "2025-03-20T10:15:53.283", "last_modified_date": "2025-03-20T10:15:53.283", "descriptions": [{"lang": "en", "value": "A vulnerability in the SageMaker Workflow component of aws/sagemaker-python-sdk allows for the possibility of MD5 hash collisions in all versions. This can lead to workflows being inadvertently replaced due to the reuse of results from different configurations that produce the same MD5 hash. This issue can cause integrity problems within the pipeline, potentially leading to erroneous processing outcomes."}, {"lang": "es", "value": "Una vulnerabilidad en el componente SageMaker Workflow de aws/sagemaker-python-sdk permite la posibilidad de colisiones de hash MD5 en todas las versiones. Esto puede provocar que los flujos de trabajo se reemplacen inadvertidamente debido a la reutilización de resultados de diferentes configuraciones que generan el mismo hash MD5. Este problema puede causar problemas de integridad en la canalización, lo que podría generar resultados de procesamiento erróneos."}], "references": [{"url": "https://github.com/aws/sagemaker-python-sdk/commit/dcdd99f911e8b1a05d19cf1ad939b0fefae47864", "source": "<EMAIL>", "tags": []}, {"url": "https://huntr.com/bounties/eb056818-5b81-466f-81ee-916058d34af2", "source": "<EMAIL>", "tags": []}]}
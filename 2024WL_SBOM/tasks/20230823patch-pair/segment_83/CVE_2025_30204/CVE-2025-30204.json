{"cve_id": "CVE-2025-30204", "published_date": "2025-03-21T22:15:26.420", "last_modified_date": "2025-04-10T13:15:52.097", "descriptions": [{"lang": "en", "value": "golang-jwt is a Go implementation of JSON Web Tokens. Starting in version 3.2.0 and prior to versions 5.2.2 and 4.5.2, the function parse.ParseUnverified splits (via a call to strings.Split) its argument (which is untrusted data) on periods. As a result, in the face of a malicious request whose Authorization header consists of Bearer  followed by many period characters, a call to that function incurs allocations to the tune of O(n) bytes (where n stands for the length of the function's argument), with a constant factor of about 16. This issue is fixed in 5.2.2 and 4.5.2."}, {"lang": "es", "value": "golang-jwt es una implementación de Go de tokens web JSON. En versiones anteriores a la 5.2.2 y la 4.5.2, la función parse.ParseUnverified divide (mediante una llamada a strings.Split) su argumento (que contiene datos no confiables) en puntos. Como resultado, ante una solicitud maliciosa cuyo encabezado de autorización consiste en \"Bearer\" seguido de muchos puntos, una llamada a dicha función genera asignaciones de O(n) bytes (donde n representa la longitud del argumento de la función), con un factor constante de aproximadamente 16. Este problema se solucionó en las versiones 5.2.2 y 4.5.2."}], "references": [{"url": "https://github.com/golang-jwt/jwt/commit/0951d184286dece21f73c85673fd308786ffe9c3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/golang-jwt/jwt/commit/bf316c48137a1212f8d0af9288cc9ce8e59f1afb", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/golang-jwt/jwt/security/advisories/GHSA-mh63-6h87-95cp", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
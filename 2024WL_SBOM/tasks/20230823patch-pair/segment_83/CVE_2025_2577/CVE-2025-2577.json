{"cve_id": "CVE-2025-2577", "published_date": "2025-03-22T12:15:27.013", "last_modified_date": "2025-03-22T12:15:27.013", "descriptions": [{"lang": "en", "value": "The Bitspecter Suite plugin for WordPress is vulnerable to Stored Cross-Site Scripting via SVG File uploads in all versions up to, and including, 1.0.0 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Author-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses the SVG file."}, {"lang": "es", "value": "El complemento Bitspecter Suite para WordPress es vulnerable a Cross-Site Scripting almacenado al subir archivos SVG en todas las versiones hasta la 1.0.0 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite a atacantes autenticados, con acceso de autor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario al archivo SVG."}], "references": [{"url": "https://plugins.svn.wordpress.org/bitspecter-suite/trunk/includes/Hardening/BitspecterSuiteHardening.php", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3259470/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/bitspecter-suite/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1e0f35be-fbd1-4063-a1c8-a8e4398d8f0a?source=cve", "source": "<EMAIL>", "tags": []}]}
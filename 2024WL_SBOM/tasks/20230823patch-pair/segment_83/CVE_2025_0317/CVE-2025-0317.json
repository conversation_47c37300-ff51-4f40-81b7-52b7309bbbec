{"cve_id": "CVE-2025-0317", "published_date": "2025-03-20T10:15:52.647", "last_modified_date": "2025-04-02T16:07:20.300", "descriptions": [{"lang": "en", "value": "A vulnerability in ollama/ollama versions <=0.3.14 allows a malicious user to upload and create a customized GGUF model file on the Ollama server. This can lead to a division by zero error in the ggufPadding function, causing the server to crash and resulting in a Denial of Service (DoS) attack."}, {"lang": "es", "value": "Una vulnerabilidad en ollama/ollama versiones anteriores a la 0.3.14 permite a un usuario malintencionado cargar y crear un archivo de modelo GGUF personalizado en el servidor Ollama. Esto puede provocar un error de división por cero en la función ggufPadding, lo que provoca el bloqueo del servidor y un ataque de denegación de servicio (DoS)."}], "references": [{"url": "https://huntr.com/bounties/a9951bca-9bd8-49b2-b143-4cd4219f9fa0", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-2186", "published_date": "2025-03-22T13:15:35.683", "last_modified_date": "2025-03-22T13:15:35.683", "descriptions": [{"lang": "en", "value": "The Recover WooCommerce Cart Abandonment, Newsletter, Email Marketing, Marketing Automation By FunnelKit plugin for WordPress is vulnerable to SQL Injection via the ‘automationId’ parameter in all versions up to, and including, 3.5.1 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Recover WooCommerce Cart Abandonment, Newsletter, Email Marketing, Marketing Automation By FunnelKit para WordPress es vulnerable a la inyección SQL a través del parámetro \"automationId\" en todas las versiones hasta la 3.5.1 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-marketing-automations/trunk/includes/api/wc/class-bwfan-api-get-automation-dynamic-coupon.php#L50", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3257474/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/88f8fa25-e3d5-4dfd-aae5-68b5880ffd53?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2659", "published_date": "2025-03-23T19:15:14.570", "last_modified_date": "2025-07-09T01:23:44.977", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in Project Worlds Online Time Table Generator 1.0. This affects an unknown part of the file /student/index.php. The manipulation of the argument e leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en Project Worlds Online Time Table Generator 1.0. Esta afecta a una parte desconocida del archivo /student/index.php. La manipulación del argumento \"e\" provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ydnd/cve/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300675", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300675", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520482", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
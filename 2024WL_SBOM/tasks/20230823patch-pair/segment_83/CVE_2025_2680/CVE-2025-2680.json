{"cve_id": "CVE-2025-2680", "published_date": "2025-03-24T03:15:15.727", "last_modified_date": "2025-06-04T15:16:46.930", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Bank Locker Management System 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /edit-assign-locker.php?ltid=1. The manipulation of the argument mobilenumber leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Bank Locker Management System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /edit-assign-locker.php?ltid=1. La manipulación del argumento \"mobilenumber\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300697", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300697", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521448", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
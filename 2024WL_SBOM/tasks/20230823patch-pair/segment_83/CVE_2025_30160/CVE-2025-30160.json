{"cve_id": "CVE-2025-30160", "published_date": "2025-03-20T19:15:38.383", "last_modified_date": "2025-03-20T19:15:38.383", "descriptions": [{"lang": "en", "value": "Redlib is an alternative private front-end to Reddit. A vulnerability has been identified in Redlib where an attacker can cause a denial-of-service (DOS) condition by submitting a specially crafted base2048-encoded DEFLATE decompression bomb to the restore_preferences form. This leads to excessive memory consumption and potential system instability, which can be exploited to disrupt Redlib instances. This vulnerability is fixed in 0.36.0."}, {"lang": "es", "value": "Redlib es una interfaz privada alternativa a Reddit. Se ha identificado una vulnerabilidad en Redlib que permite a un atacante causar una denegación de servicio (DOS) al enviar una bomba de descompresión DEFLATE, especialmente diseñada y codificada en base2048, al formulario restore_preferences. Esto provoca un consumo excesivo de memoria y una posible inestabilidad del sistema, lo cual puede explotarse para interrumpir las instancias de Redlib. Esta vulnerabilidad está corregida en la versión 0.36.0."}], "references": [{"url": "https://github.com/redlib-org/redlib/commit/15147cea8e42f6569a11603d661d71122f6a02dc", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/redlib-org/redlib/commit/2e95e1fc6e2064ccfae87964b4860bda55eddb9a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/redlib-org/redlib/security/advisories/GHSA-g8vq-v3mg-7mrg", "source": "<EMAIL>", "tags": []}]}
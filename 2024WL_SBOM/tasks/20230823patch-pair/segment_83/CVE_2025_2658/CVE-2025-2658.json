{"cve_id": "CVE-2025-2658", "published_date": "2025-03-23T18:15:14.533", "last_modified_date": "2025-05-13T20:14:58.753", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Online Security Guards Hiring System 1.0. Affected by this issue is some unknown functionality of the file /search-request.php. The manipulation of the argument searchdata leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Online Security Guards Hiring System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /search-request.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/zyb26252/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300674", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300674", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520250", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
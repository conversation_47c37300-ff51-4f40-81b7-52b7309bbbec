{"cve_id": "CVE-2025-29410", "published_date": "2025-03-20T14:15:24.540", "last_modified_date": "2025-04-01T20:19:45.107", "descriptions": [{"lang": "en", "value": "A cross-site scripting (XSS) vulnerability in the component /contact.php of Hospital Management System v1.0 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the txtEmail parameter."}, {"lang": "es", "value": "Una vulnerabilidad de cross-site scripting (XSS) en el componente /contact.php de Hospital Management System v1.0 permite a los atacantes ejecutar scripts web o HTML arbitrarios mediante la inyección de un payload manipulada en el parámetro txtEmail."}], "references": [{"url": "https://github.com/kishan0725/Hospital-Management-System/issues/49", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://www.simonjuguna.com/cve-2025-29410-hospital-management-system-xss-vulnerability/", "source": "<EMAIL>", "tags": ["Exploit"]}]}
{"cve_id": "CVE-2025-29778", "published_date": "2025-03-24T17:15:20.970", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Kyverno is a policy engine designed for cloud native platform engineering teams. Prior to version 1.14.0-alpha.1, Kyverno ignores subjectRegExp and IssuerRegExp while verifying artifact's sign with keyless mode. It allows the attacker to deploy kubernetes resources with the artifacts that were signed by unexpected certificate. Deploying these unauthorized kubernetes resources can lead to full compromise of kubernetes cluster. Version 1.14.0-alpha.1 contains a patch for the issue."}, {"lang": "es", "value": "Kyverno es un motor de políticas diseñado para equipos de ingeniería de plataformas nativas de la nube. Antes de la versión 1.14.0-alpha.1, Kyverno ignoraba subjectRegExp y IssuerRegExp al verificar la firma de artefactos con el modo sin clave. Esto permite al atacante implementar recursos de Kubernetes con artefactos firmados por un certificado inesperado. Implementar estos recursos de Kubernetes no autorizados puede comprometer por completo el clúster de Kubernetes. La versión 1.14.0-alpha.1 incluye un parche para este problema."}], "references": [{"url": "https://github.com/Mohdcode/kyverno/blob/373f942ea9fa8b63140d0eb0e101b9a5f71033f3/pkg/cosign/cosign.go#L537", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kyverno/kyverno/commit/8777672fb17bdf252bd2e7d8de3441e240404a60", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kyverno/kyverno/pull/12237", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kyverno/kyverno/security/advisories/GHSA-46mp-8w32-6g94", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kyverno/policies/issues/1246", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1320", "published_date": "2025-03-25T07:15:38.180", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The teachPress plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 9.0.9. This is due to missing or incorrect nonce validation on the import.php page. This makes it possible for unauthenticated attackers to delete imports via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento teachPress para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 9.0.9 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la página import.php. Esto permite que atacantes no autenticados eliminen importaciones mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://wordpress.org/plugins/teachpress/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b677ad8b-4f01-4147-bcf6-ae769046be48?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-30091", "published_date": "2025-03-25T14:15:32.143", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "In Tiny MoxieManager PHP before 4.0.0, remote code execution can occur in the installer command. This vulnerability allows unauthenticated attackers to inject and execute arbitrary code. Attacker-controlled data to InstallCommand can be inserted into config.php, and InstallCommand is available after an installation has completed."}, {"lang": "es", "value": "En Tiny MoxieManager PHP anterior a la versión 4.0.0, se puede producir ejecución remota de código en el comando de instalación. Esta vulnerabilidad permite a atacantes no autenticados inyectar y ejecutar código arbitrario. Los datos controlados por el atacante para InstallCommand pueden insertarse en config.php, y InstallCommand está disponible una vez completada la instalación."}], "references": [{"url": "https://www.moxiemanager.com/changelog/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.moxiemanager.com/documentation/SEC-1063.php", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-30621", "published_date": "2025-03-24T14:15:34.660", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in kornelly Translator allows Stored XSS. This issue affects Translator: from n/a through 0.3."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Kornelly Translator permite XSS almacenado. Este problema afecta a Translator desde n/d hasta la versión 0.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/translator/vulnerability/wordpress-translator-plugin-0-3-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2645", "published_date": "2025-03-23T09:15:16.060", "last_modified_date": "2025-04-02T13:15:10.653", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.0. It has been classified as problematic. Affected is an unknown function of the file /product.php. The manipulation of the argument artname leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Art Gallery Management System 1.0. Se ha clasificado como problemática. Se ve afectada una función desconocida del archivo /product.php. La manipulación del argumento artname provoca ataques de cross site scripting. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300660", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300660", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.519775", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
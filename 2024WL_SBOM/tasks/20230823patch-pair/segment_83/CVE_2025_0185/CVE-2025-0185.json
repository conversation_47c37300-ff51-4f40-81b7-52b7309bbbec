{"cve_id": "CVE-2025-0185", "published_date": "2025-03-20T10:15:51.293", "last_modified_date": "2025-03-27T19:18:14.417", "descriptions": [{"lang": "en", "value": "A vulnerability in the Dify Tools' Vanna module of the langgenius/dify repository allows for a Pandas Query Injection in the latest version. The vulnerability occurs in the function `vn.get_training_plan_generic(df_information_schema)`, which does not properly sanitize user inputs before executing queries using the Pandas library. This can potentially lead to Remote Code Execution (RCE) if exploited."}, {"lang": "es", "value": "Una vulnerabilidad en el módulo Vanna de Dify Tools del repositorio langgenius/dify permite la inyección de consultas de Pandas en la última versión. La vulnerabilidad se produce en la función `vn.get_training_plan_generic(df_information_schema)`, que no depura correctamente las entradas del usuario antes de ejecutar consultas con la librería de Pandas. Esto podría provocar la ejecución remota de código (RCE) si se explota."}], "references": [{"url": "https://huntr.com/bounties/7d9eb9b2-7b86-45ed-89bd-276c1350db7e", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}
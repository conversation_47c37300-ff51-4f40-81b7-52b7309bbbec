{"cve_id": "CVE-2025-30144", "published_date": "2025-03-19T16:15:33.080", "last_modified_date": "2025-03-19T16:15:33.080", "descriptions": [{"lang": "en", "value": "fast-jwt provides fast JSON Web Token (JWT) implementation. Prior to 5.0.6, the fast-jwt library does not properly validate the iss claim based on the RFC 7519. The iss (issuer) claim validation within the fast-jwt library permits an array of strings as a valid iss value. This design flaw enables a potential attack where a malicious actor crafts a JWT with an iss claim structured as ['https://attacker-domain/', 'https://valid-iss']. Due to the permissive validation, the JWT will be deemed valid. Furthermore, if the application relies on external libraries like get-jwks that do not independently validate the iss claim, the attacker can leverage this vulnerability to forge a JWT that will be accepted by the victim application. Essentially, the attacker can insert their own domain into the iss array, alongside the legitimate issuer, and bypass the intended security checks. This issue is fixed in 5.0.6."}, {"lang": "es", "value": "fast-jwt proporciona una implementación rápida de JSON Web Token (JWT). Antes de la versión 5.0.6, la librería fast-jwt no validaba correctamente la declaración iss según el RFC 7519. La validación de la declaración iss (emisor) dentro de la librería fast-jwt permite una matriz de cadenas como valor iss válido. Esta falla de diseño permite un posible ataque donde un actor malicioso manipula un JWT con una declaración iss estructurada como ['https://attacker-domain/', 'https://valid-iss']. Debido a la validación permisiva, el JWT se considerará válido. Además, si la aplicación utiliza librerías externas como get-jwks, que no validan de forma independiente la declaración iss, el atacante puede aprovechar esta vulnerabilidad para falsificar un JWT que sea aceptado por la aplicación víctima. En esencia, el atacante puede insertar su propio dominio en la matriz iss, junto con el emisor legítimo, y eludir las comprobaciones de seguridad previstas. Este problema se solucionó en la versión 5.0.6."}], "references": [{"url": "https://datatracker.ietf.org/doc/html/rfc7519#page-9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nearform/fast-jwt/commit/cc26b1d473f900446ad846f8f0b10eb1c0adcbdd", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nearform/fast-jwt/security/advisories/GHSA-gm45-q3v2-6cf8", "source": "<EMAIL>", "tags": []}]}
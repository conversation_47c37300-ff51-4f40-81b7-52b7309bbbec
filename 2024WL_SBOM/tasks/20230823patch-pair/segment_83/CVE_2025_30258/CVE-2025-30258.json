{"cve_id": "CVE-2025-30258", "published_date": "2025-03-19T20:15:20.140", "last_modified_date": "2025-03-19T20:15:20.140", "descriptions": [{"lang": "en", "value": "In GnuPG before 2.5.5, if a user chooses to import a certificate with certain crafted subkey data that lacks a valid backsig or that has incorrect usage flags, the user loses the ability to verify signatures made from certain other signing keys, aka a \"verification DoS.\""}, {"lang": "es", "value": "En GnuPG anterior a 2.5.5, si un usuario elige importar un certificado con ciertos datos de subclave manipulado que carecen de una firma inversa válida o que tienen indicadores de uso incorrectos, el usuario pierde la capacidad de verificar las firmas realizadas a partir de ciertas otras claves de firma, lo que se conoce como \"denegación de servicio de verificación\"."}], "references": [{"url": "https://dev.gnupg.org/T7527", "source": "<EMAIL>", "tags": []}, {"url": "https://dev.gnupg.org/rG48978ccb4e20866472ef18436a32744350a65158", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.gnupg.org/pipermail/gnupg-announce/2025q1/000491.html", "source": "<EMAIL>", "tags": []}]}
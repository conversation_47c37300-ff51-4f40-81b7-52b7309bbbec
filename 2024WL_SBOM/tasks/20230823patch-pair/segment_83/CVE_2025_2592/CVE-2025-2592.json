{"cve_id": "CVE-2025-2592", "published_date": "2025-03-21T14:15:17.037", "last_modified_date": "2025-07-17T21:52:30.377", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in Open Asset Import Library Assimp 5.4.3. This issue affects the function CSMImporter::InternReadFile of the file code/AssetLib/CSM/CSMLoader.cpp. The manipulation leads to heap-based buffer overflow. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The patch is named 2690e354da0c681db000cfd892a55226788f2743. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad, que se clasificó como crítica, en Open Asset Import Library Assimp 5.4.3. Este problema afecta la función CSMIMPORTER::InternetFile del archivo de archivo/AssetLib/CSM/CSMLoader.cpp. La manipulación conduce al desbordamiento del búfer basado en el montón. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado. El parche se llama 2690E354DA0C681DB000CFD892A55226788F2743. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/assimp/assimp/commit/2690e354da0c681db000cfd892a55226788f2743", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/assimp/assimp/issues/6010", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6010#issue-2877368110", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/pull/6052", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://vuldb.com/?ctiid.300575", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300575", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517782", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
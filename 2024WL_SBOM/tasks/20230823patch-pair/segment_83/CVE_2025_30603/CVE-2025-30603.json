{"cve_id": "CVE-2025-30603", "published_date": "2025-03-24T14:15:32.810", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in DEJAN CopyLink allows Stored XSS. This issue affects CopyLink: from n/a through 1.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en DEJAN CopyLink permite XSS almacenado. Este problema afecta a CopyLink desde la versión n/d hasta la 1.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/copy-link/vulnerability/wordpress-copylink-plugin-1-1-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
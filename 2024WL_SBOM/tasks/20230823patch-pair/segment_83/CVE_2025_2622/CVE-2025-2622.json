{"cve_id": "CVE-2025-2622", "published_date": "2025-03-22T17:15:35.213", "last_modified_date": "2025-03-26T18:38:53.113", "descriptions": [{"lang": "en", "value": "A vulnerability was found in aizuda snail-job 1.4.0. It has been classified as critical. Affected is the function getRuntime of the file /snail-job/workflow/check-node-expression of the component Workflow-Task Management Module. The manipulation of the argument nodeExpression leads to deserialization. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en aizuda snail-job 1.4.0. Se ha clasificado como crítica. La función getRuntime del archivo /snail-job/workflow/check-node-expression del componente Workflow-Task Management Module se ve afectada. La manipulación del argumento nodeExpression provoca la deserialización. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://gitee.com/aizuda/snail-job/issues/IBSQ24", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://gitee.com/aizuda/snail-job/issues/IBSQ24#note_38500450_link", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300624", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300624", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.518999", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}]}
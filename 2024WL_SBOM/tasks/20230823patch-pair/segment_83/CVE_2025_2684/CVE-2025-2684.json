{"cve_id": "CVE-2025-2684", "published_date": "2025-03-24T05:15:13.373", "last_modified_date": "2025-03-24T17:18:54.283", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Bank Locker Management System 1.0. This issue affects some unknown processing of the file /search-report-details.php. The manipulation of the argument searchinput leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul Bank Locker Management System 1.0. Este problema afecta a un procesamiento desconocido del archivo /search-report-details.php. La manipulación del argumento \"searchinput\" provoca una inyección SQL. El ataque podría iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. "}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/13", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300701", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300701", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.521453", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}]}
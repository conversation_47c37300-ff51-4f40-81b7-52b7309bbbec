{"cve_id": "CVE-2025-30343", "published_date": "2025-03-21T06:15:26.700", "last_modified_date": "2025-03-27T14:00:35.087", "descriptions": [{"lang": "en", "value": "A directory traversal issue was discovered in OpenSlides before 4.2.5. Files can be uploaded to OpenSlides meetings and organized in folders. The interface allows users to download a ZIP archive that contains all files in a folder and its subfolders. If an attacker specifies the title of a file or folder as a relative or absolute path (e.g., ../../../etc/passwd), the ZIP archive generated for download converts that title into a path. Depending on the extraction tool used by the user, this might overwrite files locally outside of the chosen directory."}, {"lang": "es", "value": "Se detectó un problema de directory traversal en OpenSlides antes de la versión 4.2.5. Se pueden subir archivos a las reuniones de OpenSlides y organizarlos en carpetas. La interfaz permite a los usuarios descargar un archivo ZIP que contiene todos los archivos de una carpeta y sus subcarpetas. Si un atacante especifica el título de un archivo o carpeta como una ruta relativa o absoluta (p. ej., ../../../etc/passwd), el archivo ZIP generado para la descarga convierte ese título en una ruta. Según la herramienta de extracción utilizada, esto podría sobrescribir archivos localmente fuera del directorio seleccionado."}], "references": [{"url": "https://www.x41-dsec.de/lab/advisories/x41-2025-001-OpenSlides", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
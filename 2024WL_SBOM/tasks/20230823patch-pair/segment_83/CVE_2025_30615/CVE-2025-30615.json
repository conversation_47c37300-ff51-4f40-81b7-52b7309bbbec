{"cve_id": "CVE-2025-30615", "published_date": "2025-03-24T14:15:34.040", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Jacob <PERSON> WP e-Commerce Style Email allows Code Injection. This issue affects WP e-Commerce Style Email: from n/a through 0.6.2."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en <PERSON> WP e-Commerce Style Email permite la inyección de código. Este problema afecta a WP e-Commerce Style Email desde n/d hasta la versión 0.6.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-e-commerce-style-email/vulnerability/wordpress-wp-e-commerce-style-email-plugin-0-6-2-csrf-to-remote-code-execution-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2478", "published_date": "2025-03-22T07:15:24.963", "last_modified_date": "2025-03-22T07:15:24.963", "descriptions": [{"lang": "en", "value": "The Code Clone plugin for WordPress is vulnerable to time-based SQL Injection via the ‘snippetId’ parameter in all versions up to, and including, 0.9 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query. This makes it possible for authenticated attackers, with Administrator-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Code Clone para WordPress es vulnerable a la inyección SQL basada en tiempo mediante el parámetro 'snippetId' en todas las versiones hasta la 0.9 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de administrador o superior, añadir consultas SQL adicionales a las consultas existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/code-clone/trunk/admin/snippet-edit.php", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/code-clone/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9b6f21ce-8601-425f-bd44-6a1af31c67de?source=cve", "source": "<EMAIL>", "tags": []}]}
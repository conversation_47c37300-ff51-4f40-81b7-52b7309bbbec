{"cve_id": "CVE-2025-27787", "published_date": "2025-03-19T21:15:40.923", "last_modified_date": "2025-03-19T21:15:40.923", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.8-bugfix and prior are vulnerable to denial of service (DoS) in restart.py. `model_name` in train.py takes user input, and passes it to the `stop_train` function in restart.py, which uses it construct a path to a folder with `config.json`. That `config.json` is opened and the list of values under \"process_pids\" are read. Next all the process IDs listed in the JSON are killed. Using one of the arbitrary file writes, one can  write to `logs/foobar` a `config.json` file, which contains a list of process IDs. Then one can access this endpoint to kill these processes. Since an attacker can't know what process is running on which process ID, they can send a list of hundreds of process IDs, which can kill the process that <PERSON><PERSON> is using to run, as well as other, potentially important processes, which leads to DoS. Note that constructing a path with user input also enables path traversal. For example, by supplying \"../../\" in `model_name` one can access `config.json` freom locations two folders down on the server. As of time of publication, no known patches are available."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.8-bugfix y anteriores son vulnerables a denegación de servicio (DoS) en restart.py. `model_name` en train.py recibe la entrada del usuario y la pasa a la función `stop_train` en restart.py, que la utiliza para construir una ruta a una carpeta con `config.json`. Se abre ese `config.json` y se lee la lista de valores bajo \"process_pids\". A continuación, se eliminan todos los ID de proceso listados en el JSON. Mediante una escritura arbitraria en el archivo, se puede escribir en `logs/foobar` un archivo `config.json`, que contiene una lista de ID de proceso. A continuación, se puede acceder a este endpoint para eliminar estos procesos. Dado que un atacante no puede saber qué proceso se está ejecutando en qué ID de proceso, puede enviar una lista de cientos de ID de proceso, lo que puede eliminar el proceso que applio está ejecutando, así como otros procesos potencialmente importantes, lo que provoca una denegación de servicio (DoS). Tenga en cuenta que la construcción de una ruta con la entrada del usuario también permite el path traversal. Por ejemplo, al proporcionar \"../../\" en `model_name`, se puede acceder a `config.json` desde ubicaciones dos carpetas más abajo en el servidor. Al momento de la publicación, no se conocían parches disponibles."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/settings/sections/restart.py#L9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/train/train.py#L306", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-354_Applio/", "source": "<EMAIL>", "tags": []}]}
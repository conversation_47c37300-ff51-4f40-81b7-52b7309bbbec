{"cve_id": "CVE-2025-2530", "published_date": "2025-03-25T15:15:25.610", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Luxion KeyShot DAE File Parsing Access of Uninitialized Pointer Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Luxion KeyShot. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of dae files. The issue results from the lack of proper initialization of a pointer prior to accessing it. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-23698."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código en el análisis de archivos DAE de Luxion KeyShot acceso a punteros no inicializados. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Luxion KeyShot. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos DAE. El problema se debe a la falta de inicialización correcta de un puntero antes de acceder a él. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Anteriormente, se denominó ZDI-CAN-23698."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-173/", "source": "<EMAIL>", "tags": []}]}
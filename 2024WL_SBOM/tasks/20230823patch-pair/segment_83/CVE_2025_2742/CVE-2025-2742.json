{"cve_id": "CVE-2025-2742", "published_date": "2025-03-25T07:15:38.700", "last_modified_date": "2025-07-15T13:07:15.493", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in zhijiantianya ruoyi-vue-pro 2.4.1. This vulnerability affects unknown code of the file /admin-api/mp/material/upload-permanent of the component Material Upload Interface. The manipulation of the argument File leads to path traversal. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en zhijiantianya ruoyi-vue-pro 2.4.1. Esta vulnerabilidad afecta al código desconocido del archivo /admin-api/mp/material/upload-permanent del componente Material Upload Interface. La manipulación del argumento \"File\" provoca un path traversal. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/ruoyi-vue-pro.md#5arbitrary-file-deletion-vulnerability---uploadpermanentmaterial", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300844", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300844", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519691", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-27785", "published_date": "2025-03-19T21:15:40.650", "last_modified_date": "2025-03-19T21:15:40.650", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.8-bugfix and prior are vulnerable to arbitrary file read in train.py's `export_index` function. This issue may lead to reading arbitrary files on the Applio server. It can also be used in conjunction with blind server-side request forgery to read files from servers on the internal network that the Applio server has access to. As of time of publication, no known patches are available."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.8 (corrección de errores) y anteriores son vulnerables a la lectura de archivos arbitrarios en la función `export_index` de train.py. Este problema puede provocar la lectura de archivos arbitrarios en el servidor Applio. También puede utilizarse junto con blind server-side request forgery para leer archivos de servidores de la red interna a los que el servidor Applio tiene acceso. Al momento de la publicación, no se conocen parches disponibles."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/train/train.py#L273", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/train/train.py#L816", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
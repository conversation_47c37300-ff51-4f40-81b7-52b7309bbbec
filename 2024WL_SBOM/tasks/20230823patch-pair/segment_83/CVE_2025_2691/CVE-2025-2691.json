{"cve_id": "CVE-2025-2691", "published_date": "2025-03-23T15:15:13.940", "last_modified_date": "2025-03-26T15:06:35.310", "descriptions": [{"lang": "en", "value": "Versions of the package nossrf before 1.0.4 are vulnerable to Server-Side Request Forgery (SSRF) where an attacker can provide a hostname that resolves to a local or reserved IP address space and bypass the SSRF protection mechanism."}, {"lang": "es", "value": "Las versiones del paquete nossrf anteriores a 1.0.4 son vulnerables a Server-Side Request Forgery (SSRF), donde un atacante puede proporcionar un nombre de host que se resuelve en un espacio de dirección IP local o reservado y eludir el mecanismo de protección SSRF."}], "references": [{"url": "https://security.snyk.io/vuln/SNYK-JS-NOSSRF-9510842", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-27018", "published_date": "2025-03-19T09:15:14.457", "last_modified_date": "2025-06-03T21:11:28.860", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Apache Airflow MySQL Provider.\n\nWhen user triggered a DAG with dump_sql or load_sql functions they could pass a table parameter from a UI, that could cause SQL injection by running SQL that was not intended.\nIt could lead to data corruption, modification and others.\nThis issue affects Apache Airflow MySQL Provider: before 6.2.0.\n\nUsers are recommended to upgrade to version 6.2.0, which fixes the issue."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en una instrucción SQL ('Inyección SQL') en Apache Airflow MySQL Provider. Al activar un DAG con las funciones dump_sql o load_sql, el usuario podía pasar un parámetro de tabla desde una interfaz de usuario, lo que podía causar una inyección SQL al ejecutar SQL no previsto. Esto podía provocar corrupción y modificación de datos, entre otros problemas. Este problema afecta a Apache Airflow MySQL Provider anterior a la versión 6.2.0. Se recomienda actualizar a la versión 6.2.0, que soluciona el problema."}], "references": [{"url": "https://github.com/apache/airflow/pull/47254", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/apache/airflow/pull/47255", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://lists.apache.org/thread/m8ohgkwz4mq9njohf66sjwqjdy28gvzf", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/19/4", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}]}
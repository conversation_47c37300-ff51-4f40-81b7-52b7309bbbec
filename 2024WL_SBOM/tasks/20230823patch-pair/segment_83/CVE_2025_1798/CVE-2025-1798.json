{"cve_id": "CVE-2025-1798", "published_date": "2025-03-25T06:15:40.480", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The  does not sanitise and escape some parameters when outputting them back in a page, allowing unauthenticated users the ability to perform stored Cross-Site Scripting attacks."}, {"lang": "es", "value": "No depura ni escapa algunos parámetros al mostrarlos en una página, lo que permite que usuarios no autenticados tengan la capacidad de realizar ataques de cross site scripting almacenado."}], "references": [{"url": "https://wpscan.com/vulnerability/c5c30191-857c-419c-9096-d1fe14d34eaa/", "source": "<EMAIL>", "tags": []}]}
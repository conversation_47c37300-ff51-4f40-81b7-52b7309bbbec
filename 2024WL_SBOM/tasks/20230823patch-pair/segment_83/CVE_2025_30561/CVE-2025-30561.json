{"cve_id": "CVE-2025-30561", "published_date": "2025-03-24T14:15:27.460", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Henrique Mouta CAS Maestro allows Stored XSS. This issue affects CAS Maestro: from n/a through 1.1.3."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Henrique Mouta CAS Maestro permite XSS almacenado. Este problema afecta a CAS Maestro desde n/d hasta la versión 1.1.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/cas-maestro/vulnerability/wordpress-cas-maestro-plugin-1-1-3-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
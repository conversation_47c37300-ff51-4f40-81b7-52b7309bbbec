{"cve_id": "CVE-2025-2677", "published_date": "2025-03-24T02:15:15.540", "last_modified_date": "2025-06-04T16:03:58.597", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Bank Locker Management System 1.0 and classified as critical. This vulnerability affects unknown code of the file /changeidproof.php. The manipulation of the argument editid leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Bank Locker Management System 1.0, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /changeidproof.php. La manipulación del argumento editid provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300694", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300694", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521444", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
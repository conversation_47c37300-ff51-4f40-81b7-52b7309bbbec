{"cve_id": "CVE-2025-2511", "published_date": "2025-03-19T12:15:14.313", "last_modified_date": "2025-03-19T12:15:14.313", "descriptions": [{"lang": "en", "value": "The AHAthat Plugin plugin for WordPress is vulnerable to time-based SQL Injection via the 'id' parameter in all versions up to, and including, 1.6 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Administrator-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento AHAthat Plugin para WordPress es vulnerable a la inyección SQL basada en tiempo mediante el parámetro 'id' en todas las versiones hasta la 1.6 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de administrador o superior, añadir consultas SQL adicionales a las consultas existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://wordpress.org/plugins/ahathat/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/cde440a2-55f8-406a-b81b-919028f0e887?source=cve", "source": "<EMAIL>", "tags": []}]}
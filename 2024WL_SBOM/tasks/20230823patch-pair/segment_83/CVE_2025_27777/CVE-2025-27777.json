{"cve_id": "CVE-2025-27777", "published_date": "2025-03-19T21:15:39.600", "last_modified_date": "2025-03-19T21:15:39.600", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.7 and prior are vulnerable to server-side request forgery (SSRF) in `model_download.py` (line 195 in 3.2.7). The blind SSRF allows for sending requests on behalf of Applio server and can be leveraged to probe for other vulnerabilities on the server itself or on other back-end systems on the internal network, that the Applio server can reach. The blind SSRF can also be coupled with a arbitrary file read (e.g., CVE-2025-27784) to read files from hosts on the internal network, that the Applio server can reach, which would make it a full SSRF. As of time of publication, no known patches are available."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.7 y anteriores son vulnerables a server-side request forgery (SSRF) en `model_download.py` (línea 195 en 3.2.7). La SSRF ciega permite enviar solicitudes en nombre del servidor Applio y puede utilizarse para investigar otras vulnerabilidades en el propio servidor o en otros sistemas back-end de la red interna a los que el servidor Applio puede acceder. La SSRF ciega también puede combinarse con una lectura de archivo arbitraria (p. ej., CVE-2025-27784) para leer archivos de hosts de la red interna a los que el servidor Applio puede acceder, lo que la convertiría en una SSRF completa. Al momento de la publicación, no se conocían parches disponibles."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/assets/flask/routes.py#L14", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/rvc/lib/tools/model_download.py#L195", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/download/download.py#L192-L196", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
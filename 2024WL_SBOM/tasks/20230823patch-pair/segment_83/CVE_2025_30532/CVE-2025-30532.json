{"cve_id": "CVE-2025-30532", "published_date": "2025-03-24T14:15:21.850", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in MorganF Weather Layer allows Stored XSS. This issue affects Weather Layer: from n/a through 4.2.1."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en MorganF Weather Layer permite XSS almacenado. Este problema afecta a Weather Layer desde n/d hasta la versión 4.2.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/weather-layer/vulnerability/wordpress-weather-layer-plugin-4-2-1-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
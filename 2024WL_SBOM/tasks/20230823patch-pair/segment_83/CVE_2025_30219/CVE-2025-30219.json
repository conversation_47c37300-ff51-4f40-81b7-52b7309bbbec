{"cve_id": "CVE-2025-30219", "published_date": "2025-03-25T23:15:36.560", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "RabbitMQ is a messaging and streaming broker. Versions prior to 4.0.3 are vulnerable to a sophisticated attack that could modify virtual host name on disk and then make it unrecoverable (with other on disk file modifications) can lead to arbitrary JavaScript code execution in the browsers of management UI users. When a virtual host on a RabbitMQ node fails to start, recent versions\nwill display an error message (a notification) in the management UI. The error message includes virtual host name, which was not escaped prior to open source RabbitMQ 4.0.3 and Tanzu RabbitMQ 4.0.3, 3.13.8. An attack that both makes a virtual host fail to start and creates a new virtual host name with an XSS code snippet or changes the name of an existing virtual host on disk could trigger arbitrary JavaScript code execution in the management UI (the user's browser). Open source RabbitMQ `4.0.3` and Tanzu RabbitMQ `4.0.3` and `3.13.8` patch the issue."}, {"lang": "es", "value": "RabbitMQ es un bróker de mensajería y streaming. Las versiones anteriores a la 4.0.3 son vulnerables a un ataque sofisticado que podría modificar el nombre del host virtual en el disco y hacerlo irrecuperable (junto con otras modificaciones de archivos en el disco), lo que puede provocar la ejecución de código JavaScript arbitrario en los navegadores de los usuarios de la interfaz de administración. Cuando un host virtual en un nodo RabbitMQ no se inicia, las versiones recientes mostrarán un mensaje de error (una notificación) en la interfaz de administración. El mensaje de error incluye el nombre del host virtual, que no se escapaba antes de las versiones de código abierto RabbitMQ 4.0.3 y Tanzu RabbitMQ 4.0.3, 3.13.8. Un ataque que provoque el inicio de un host virtual y cree un nuevo nombre de host virtual con un fragmento de código XSS o cambie el nombre de un host virtual existente en el disco podría provocar la ejecución de código JavaScript arbitrario en la interfaz de administración (el navegador del usuario). Las versiones de código abierto RabbitMQ 4.0.3 y Tanzu RabbitMQ 4.0.3 y 3.13.8 solucionan el problema."}], "references": [{"url": "https://github.com/rabbitmq/rabbitmq-server/security/advisories/GHSA-g58g-82mw-9m3p", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-30623", "published_date": "2025-03-24T14:15:34.797", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in <PERSON> wA11y – The Web Accessibility Toolbox allows Stored XSS. This issue affects wA11y – The Web Accessibility Toolbox: from n/a through 1.0.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en <PERSON> wA11y – The Web Accessibility Toolbox permite XSS almacenado. Este problema afecta a wA11y – Web Accessibility Toolbox: desde n/d hasta la versión 1.0.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wa11y/vulnerability/wordpress-wa11y-the-web-accessibility-toolbox-plugin-1-0-3-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
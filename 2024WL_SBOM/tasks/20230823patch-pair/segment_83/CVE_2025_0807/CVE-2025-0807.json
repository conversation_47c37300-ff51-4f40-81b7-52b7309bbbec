{"cve_id": "CVE-2025-0807", "published_date": "2025-03-22T07:15:24.260", "last_modified_date": "2025-03-22T07:15:24.260", "descriptions": [{"lang": "en", "value": "The CITS Support svg, webp Media and TTF,OTF File Upload, Use Custom Fonts plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 4.2. This is due to missing or incorrect nonce validation on the cits_settings_tab() function. This makes it possible for unauthenticated attackers to update the plugin's settings via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento CITS Support svg, webp Media and TTF,OTF File Upload, Use Custom Fonts de WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 4.2 incluida. Esto se debe a la falta o a una validación de nonce incorrecta en la función cits_settings_tab(). Esto permite que atacantes no autenticados actualicen la configuración del plugin mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/cits-support-svg-webp-media-upload/trunk/includes/cits-custom-fonts.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/3772ddad-4960-48c8-904e-2457d12bd01c?source=cve", "source": "<EMAIL>", "tags": []}]}
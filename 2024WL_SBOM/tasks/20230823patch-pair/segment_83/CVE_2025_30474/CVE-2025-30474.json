{"cve_id": "CVE-2025-30474", "published_date": "2025-03-23T15:15:14.103", "last_modified_date": "2025-07-14T18:13:56.103", "descriptions": [{"lang": "en", "value": "Exposure of Sensitive Information to an Unauthorized Actor vulnerability in Apache Commons VFS.\n\nThe FtpFileObject class can throw an exception when a file is not found, revealing the original URI in its message, which may include a password. The fix is to mask the password in the exception message\nThis issue affects Apache Commons VFS: before 2.10.0.\n\nUsers are recommended to upgrade to version 2.10.0, which fixes the issue."}, {"lang": "es", "value": "Vulnerabilidad de exposición de información confidencial a un agente no autorizado en Apache Commons VFS. La clase FtpFileObject puede generar una excepción cuando no se encuentra un archivo, revelando la URI original en su mensaje, que puede incluir una contraseña. La solución consiste en ocultar la contraseña en el mensaje de excepción. Este problema afecta a Apache Commons VFS anteriores a la versión 2.10.0. Se recomienda actualizar a la versión 2.10.0, que soluciona el problema."}], "references": [{"url": "https://issues.apache.org/jira/browse/VFS-169", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://lists.apache.org/thread/w6ztgnbk6ccry3470x191g3xwrpgy6f4", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/23/2", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}]}
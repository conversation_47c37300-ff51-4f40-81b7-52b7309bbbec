{"cve_id": "CVE-2024-9900", "published_date": "2025-03-20T10:15:50.417", "last_modified_date": "2025-04-04T09:15:16.760", "descriptions": [{"lang": "en", "value": "mudler/localai version v2.21.1 contains a Cross-Site Scripting (XSS) vulnerability in its search functionality. The vulnerability arises due to improper sanitization of user input, allowing the injection and execution of arbitrary JavaScript code. This can lead to the execution of malicious scripts in the context of the victim's browser, potentially compromising user sessions, stealing session cookies, redirecting users to malicious websites, or manipulating the DOM."}, {"lang": "es", "value": "La versión 2.21.1 de mudler/localai contiene una vulnerabilidad de Cross-Site Scripting (XSS) en su función de búsqueda. Esta vulnerabilidad surge debido a una depuración inadecuada de la entrada del usuario, lo que permite la inyección y ejecución de código JavaScript arbitrario. Esto puede provocar la ejecución de scripts maliciosos en el navegador de la víctima, lo que podría comprometer las sesiones del usuario, robar cookies de sesión, redirigir a los usuarios a sitios web maliciosos o manipular el DOM."}], "references": [{"url": "https://github.com/mudler/localai/commit/a1634b219a4e52813e70ff07e6376a01449c4515", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://huntr.com/bounties/b39cd230-db66-471b-89b9-24afaa078e68", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
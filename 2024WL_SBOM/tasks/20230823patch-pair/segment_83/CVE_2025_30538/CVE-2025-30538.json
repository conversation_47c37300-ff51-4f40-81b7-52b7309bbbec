{"cve_id": "CVE-2025-30538", "published_date": "2025-03-24T14:15:22.743", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in ChrisHurst Simple Optimizer allows Cross Site Request Forgery. This issue affects Simple Optimizer: from n/a through 1.2.7."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en ChrisHurst Simple Optimizer permite Cross-Site Request Forgery. Este problema afecta a Simple Optimizer desde n/d hasta la versión 1.2.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/simple-optimizer/vulnerability/wordpress-simple-optimizer-plugin-1-2-7-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
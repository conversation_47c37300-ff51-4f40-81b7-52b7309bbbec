{"cve_id": "CVE-2025-29923", "published_date": "2025-03-20T18:15:19.230", "last_modified_date": "2025-03-20T18:15:19.230", "descriptions": [{"lang": "en", "value": "go-redis is the official Redis client library for the Go programming language. Prior to 9.5.5, 9.6.3, and 9.7.3, go-redis potentially responds out of order when `CLIENT SETINFO` times out during connection establishment. This can happen when the client is configured to transmit its identity, there are network connectivity issues, or the client was configured with aggressive timeouts. The problem occurs for multiple use cases. For sticky connections, you receive persistent out-of-order responses for the lifetime of the connection. All commands in the pipeline receive incorrect responses. When used with the default ConnPool once a connection is returned after use with ConnPool#Put the read buffer will be checked and the connection will be marked as bad due to the unread data. This means that at most one out-of-order response before the connection is discarded. This issue is fixed in 9.5.5, 9.6.3, and 9.7.3. You can prevent the vulnerability by setting the flag DisableIndentity to true when constructing the client instance."}, {"lang": "es", "value": "go-redis es la librería cliente oficial de Redis para el lenguaje de programación Go. En versiones anteriores a las 9.5.5, 9.6.3 y 9.7.3, go-redis podía responder de forma incorrecta cuando se agotaba el tiempo de espera de `CLIENT SETINFO` durante el establecimiento de la conexión. Esto puede ocurrir cuando el cliente está configurado para transmitir su identidad, existen problemas de conectividad de red o se configuró con tiempos de espera agresivos. El problema se presenta en varios casos de uso. En conexiones persistentes, se reciben respuestas incorrectas persistentes durante la vida útil de la conexión. Todos los comandos en la canalización reciben respuestas incorrectas. Al usar el ConnPool predeterminado, una vez que se devuelve una conexión después de usar ConnPool#Put, se revisa el búfer de lectura y la conexión se marca como incorrecta debido a los datos no leídos. Esto significa que se recibe como máximo una respuesta incorrecta antes de que se descarte la conexión. Este problema se solucionó en las versiones 9.5.5, 9.6.3 y 9.7.3. Puede evitar la vulnerabilidad estableciendo el indicador DisableIndentity en verdadero al construir la instancia del cliente."}], "references": [{"url": "https://github.com/redis/go-redis/commit/d236865b0cfa1b752ea4b7da666b1fdcd0acebb6", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/redis/go-redis/pull/3295", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/redis/go-redis/security/advisories/GHSA-92cp-5422-2mw7", "source": "<EMAIL>", "tags": []}]}
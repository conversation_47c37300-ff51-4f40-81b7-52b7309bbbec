{"cve_id": "CVE-2025-29922", "published_date": "2025-03-20T18:15:19.063", "last_modified_date": "2025-03-20T18:15:19.063", "descriptions": [{"lang": "en", "value": "kcp is a Kubernetes-like control plane for form-factors and use-cases beyond Kubernetes and container workloads. Prior to 0.26.3, the identified vulnerability allows creating or deleting an object via the APIExport VirtualWorkspace in any arbitrary target workspace for pre-existing resources. By design, this should only be allowed when the workspace owner decides to give access to an API provider by creating an APIBinding. With this vulnerability, it is possible for an attacker to create and delete objects even if none of these requirements are satisfied, i.e. even if there is no APIBinding in that workspace at all or the workspace owner has created an APIBinding, but rejected a permission claim. A fix for this issue has been identified and has been published with kcp 0.26.3 and 0.27.0."}, {"lang": "es", "value": "kcp es un plano de control similar a Kubernetes para formatos y casos de uso más allá de Kubernetes y cargas de trabajo de contenedores. Antes de la versión 0.26.3, la vulnerabilidad identificada permitía crear o eliminar un objeto mediante APIExport VirtualWorkspace en cualquier espacio de trabajo de destino arbitrario para recursos preexistentes. Por diseño, esto solo debería permitirse cuando el propietario del espacio de trabajo decide otorgar acceso a un proveedor de API mediante la creación de un APIBinding. Con esta vulnerabilidad, un atacante puede crear y eliminar objetos incluso si no se cumple ninguno de estos requisitos; es decir, incluso si no existe ningún APIBinding en ese espacio de trabajo o si el propietario del espacio de trabajo ha creado un APIBinding, pero ha rechazado una solicitud de permiso. Se ha identificado una solución para este problema y se ha publicado en kcp 0.26.3 y 0.27.0."}], "references": [{"url": "https://github.com/kcp-dev/kcp/commit/614ecbf35f11db00f65391ab6fbb1547ca8b5d38", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kcp-dev/kcp/pull/3338", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kcp-dev/kcp/security/advisories/GHSA-w2rr-38wv-8rrp", "source": "<EMAIL>", "tags": []}]}
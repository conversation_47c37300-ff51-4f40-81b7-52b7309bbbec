{"cve_id": "CVE-2025-2740", "published_date": "2025-03-25T07:15:38.493", "last_modified_date": "2025-04-01T16:45:02.273", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Old Age Home Management System 1.0. Affected is an unknown function of the file /admin/eligibility.php. The manipulation of the argument pagetitle leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Old Age Home Management System 1.0. Se ve afectada una función desconocida del archivo /admin/eligibility.php. La manipulación del argumento pagetitle provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/guimo3/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300762", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300762", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.524733", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
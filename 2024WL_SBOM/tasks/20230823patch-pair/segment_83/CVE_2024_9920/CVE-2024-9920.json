{"cve_id": "CVE-2024-9920", "published_date": "2025-03-20T10:15:50.787", "last_modified_date": "2025-04-03T18:02:58.177", "descriptions": [{"lang": "en", "value": "In version v12 of parisneo/lollms-webui, the 'Send file to AL' function allows uploading files with various extensions, including potentially dangerous ones like .py, .sh, .bat, and more. Attackers can exploit this by uploading files with malicious content and then using the '/open_file' API endpoint to execute these files. The vulnerability arises from the use of 'subprocess.Popen' to open files without proper validation, leading to potential remote code execution."}, {"lang": "es", "value": "En la versión v12 de parisneo/lollms-webui, la función \"Enviar archivo a AL\" permite subir archivos con diversas extensiones, incluyendo algunas potencialmente peligrosas como .py, .sh, .bat y otras. Los atacantes pueden explotar esto subiendo archivos con contenido malicioso y utilizando el endpoint de la API \"/open_file\" para ejecutarlos. La vulnerabilidad surge del uso de \"subprocess.Popen\" para abrir archivos sin la validación adecuada, lo que puede provocar la ejecución remota de código."}], "references": [{"url": "https://huntr.com/bounties/c70c6732-23b3-4ef8-aec6-0a47467d1ed5", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-30586", "published_date": "2025-03-24T14:15:30.767", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in bbodine1 cTabs allows Stored XSS. This issue affects cTabs: from n/a through 1.3."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en cTabs de bbodine1 permite XSS almacenado. Este problema afecta a cTabs desde la versión n/d hasta la 1.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/ctabs/vulnerability/wordpress-ctabs-plugin-1-3-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
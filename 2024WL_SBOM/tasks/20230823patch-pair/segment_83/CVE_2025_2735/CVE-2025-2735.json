{"cve_id": "CVE-2025-2735", "published_date": "2025-03-25T05:15:41.287", "last_modified_date": "2025-05-15T19:32:10.300", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Old Age Home Management System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /admin/add-services.php. The manipulation of the argument sertitle leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Old Age Home Management System 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/add-services.php. La manipulación del argumento sertitle provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/0xabandon/CVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300757", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300757", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.522266", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
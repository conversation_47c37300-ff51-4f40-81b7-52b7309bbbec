{"cve_id": "CVE-2025-0431", "published_date": "2025-03-19T17:15:41.217", "last_modified_date": "2025-03-19T17:15:41.217", "descriptions": [{"lang": "en", "value": "Enterprise Protection contains a vulnerability in URL rewriting that allows an unauthenticated remote attacker to send an email which bypasses URL protections impacting the integrity of recipient's email.  This occurs due to improper filtering of backslashes within URLs and affects all versions of 8.21, 8.20 and 8.18 prior to 8.21.0 patch 5115, 8.20.6 patch 5114 and 8.18.6 patch 5113 respectively."}, {"lang": "es", "value": "Enterprise Protection contiene una vulnerabilidad en la reescritura de URL que permite a un atacante remoto no autenticado enviar un correo electrónico que elude las protecciones de URL, lo que afecta la integridad del correo electrónico del destinatario. Esto se debe a un filtrado incorrecto de barras invertidas en las URL y afecta a todas las versiones de 8.21, 8.20 y 8.18 anteriores a la versión 8.21.0 (parche 5115), la versión 8.20.6 (parche 5114) y la versión 8.18.6 (parche 5113), respectivamente."}], "references": [{"url": "https://www.proofpoint.com/us/security/security-advisories/pfpt-sa-2025-0001", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2736", "published_date": "2025-03-25T05:15:41.490", "last_modified_date": "2025-05-15T19:29:57.387", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Old Age Home Management System 1.0 and classified as critical. Affected by this issue is some unknown functionality of the file /admin/bwdates-report-details.php. The manipulation of the argument fromdate leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Old Age Home Management System 1.0, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/bwdates-report-details.php. La manipulación del argumento \"fromdate\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/404heihei/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300758", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300758", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.522881", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-30546", "published_date": "2025-03-24T14:15:23.820", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in boroV Cackle allows Cross Site Request Forgery. This issue affects Cackle: from n/a through 4.33."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en boroV Cackle permite Cross-Site Request Forgery. Este problema afecta a Cackle desde la versión n/d hasta la 4.33."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/cackle/vulnerability/wordpress-cackle-plugin-4-33-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
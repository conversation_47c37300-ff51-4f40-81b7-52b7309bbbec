{"cve_id": "CVE-2025-30584", "published_date": "2025-03-24T14:15:30.490", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in alphaomegaplugins AlphaOmega Captcha &amp; Anti-Spam Filter allows Stored XSS. This issue affects AlphaOmega Captcha &amp; Anti-Spam Filter: from n/a through 3.3."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en alphaomegaplugins AlphaOmega Captcha &amp; Anti-Spam Filter permiten XSS almacenado. Este problema afecta a AlphaOmega Captcha y el Filtro Antispam: desde n/d hasta la versión 3.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/alphaomega-captcha-anti-spam/vulnerability/wordpress-alphaomega-captcha-anti-spam-filter-plugin-3-3-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
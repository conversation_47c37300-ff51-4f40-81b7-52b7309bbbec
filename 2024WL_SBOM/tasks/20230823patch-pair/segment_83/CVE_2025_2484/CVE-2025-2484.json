{"cve_id": "CVE-2025-2484", "published_date": "2025-03-22T07:15:25.470", "last_modified_date": "2025-03-22T07:15:25.470", "descriptions": [{"lang": "en", "value": "The Multi Video Box plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the 'video_id' and 'group_id' parameters in all versions up to, and including, 1.5.2 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Multi Video Box para WordPress es vulnerable a ataques de Cross-Site Scripting Reflejado a través de los parámetros 'video_id' y 'group_id' en todas las versiones hasta la 1.5.2 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutan si logran engañar al usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/multi-video-box/tags/1.5.2/views/group/get_shortcode.php#L10", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/multi-video-box/tags/1.5.2/views/video/get_shortcode.php#L10", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/multi-video-box/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/433e5ba3-c07e-48a1-a28b-781121d892ae?source=cve", "source": "<EMAIL>", "tags": []}]}
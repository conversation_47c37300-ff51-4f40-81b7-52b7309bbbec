{"cve_id": "CVE-2025-2590", "published_date": "2025-03-21T13:15:35.460", "last_modified_date": "2025-04-01T20:23:46.903", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Human Resource Management System 1.0.1. It has been classified as problematic. Affected is the function UpdateRecruitmentById of the file \\handler\\recruitment.go. The manipulation of the argument c leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Human Resource Management System 1.0.1. Se ha clasificado como problemático. Afectado es la función updaterecruitmentbyid del archivo \\handler\\ recruitment.go. La manipulación del argumento C conduce a Cross Site Scripting. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/38279/1/issues/2", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.300570", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300570", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517344", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
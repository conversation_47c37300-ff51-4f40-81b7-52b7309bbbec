{"cve_id": "CVE-2025-24513", "published_date": "2025-03-25T00:15:14.900", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "A security issue was discovered in  ingress-nginx https://github.com/kubernetes/ingress-nginx  where attacker-provided data are included in a filename by the ingress-nginx Admission Controller feature, resulting in directory traversal within the container. This could result in denial of service, or when combined with other vulnerabilities, limited disclosure of Secret objects from the cluster."}, {"lang": "es", "value": "Se descubrió un problema de seguridad en ingress-nginx (https://github.com/kubernetes/ingress-nginx). La función Controlador de Admisión de ingress-nginx incluye datos proporcionados por el atacante en un nombre de archivo, lo que provoca un directory traversal dentro del contenedor. Esto podría provocar una denegación de servicio o, en combinación con otras vulnerabilidades, una divulgación limitada de objetos secretos del clúster."}], "references": [{"url": "https://github.com/kubernetes/kubernetes/issues/131005", "source": "<EMAIL>", "tags": []}]}
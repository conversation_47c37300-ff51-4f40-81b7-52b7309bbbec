{"cve_id": "CVE-2025-2739", "published_date": "2025-03-25T06:15:41.953", "last_modified_date": "2025-05-06T19:34:57.733", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Old Age Home Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /admin/manage-services.php. The manipulation of the argument sertitle leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Old Age Home Management System 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/manage-services.php. La manipulación del argumento sertitle provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/silent1189/Phpgurukul-Old-Age-Home-Management-System-V1.0-SQL-injection/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300761", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300761", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.523400", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-29914", "published_date": "2025-03-20T18:15:18.907", "last_modified_date": "2025-03-20T18:15:18.907", "descriptions": [{"lang": "en", "value": "OWASP Coraza WAF is a golang modsecurity compatible web application firewall library. Prior to 3.3.3, if a request is made on an URI starting with //, coraza will set a wrong value in REQUEST_FILENAME. For example, if the URI //bar/uploads/foo.php?a=b is passed to coraza: , REQUEST_FILENAME will be set to /uploads/foo.php. This can lead to a rules bypass. This vulnerability is fixed in 3.3.3."}, {"lang": "es", "value": "OWASP Coraza WAF es una librería de firewall de aplicaciones web compatible con Golang Modsecurity. En versiones anteriores a la 3.3.3, si se realiza una solicitud en una URI que empieza por //, coraza asigna un valor incorrecto a REQUEST_FILENAME. Por ejemplo, si se asigna la URI //bar/uploads/foo.php?a=b a coraza:, REQUEST_FILENAME se asignará a /uploads/foo.php. Esto puede provocar la omisión de reglas. Esta vulnerabilidad se corrigió en la 3.3.3."}], "references": [{"url": "https://github.com/corazawaf/coraza/commit/4722c9ad0d502abd56b8d6733c6b47eb4111742d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/corazawaf/coraza/security/advisories/GHSA-q9f5-625g-xm39", "source": "<EMAIL>", "tags": []}]}
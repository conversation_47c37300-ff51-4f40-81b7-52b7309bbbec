{"cve_id": "CVE-2025-30595", "published_date": "2025-03-24T14:15:31.770", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in tstafford include-file allows Stored XSS. This issue affects include-file: from n/a through 1."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en tstafford include-file permite XSS almacenado. Este problema afecta al archivo de inclusión: desde n/d hasta 1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/include-file/vulnerability/wordpress-include-file-1-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
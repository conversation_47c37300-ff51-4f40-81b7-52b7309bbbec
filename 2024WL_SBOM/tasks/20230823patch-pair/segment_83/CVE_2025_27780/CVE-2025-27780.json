{"cve_id": "CVE-2025-27780", "published_date": "2025-03-19T21:15:39.980", "last_modified_date": "2025-03-19T21:15:39.980", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.8-bugfix and prior are vulnerable to unsafe deserialization in model_information.py. `model_name` in model_information.py takes user-supplied input (e.g. a path to a model) and pass that value to the `run_model_information_script` and later to `model_information` function, which loads that model with `torch.load` in rvc/train/process/model_information.py (on line 16 in 3.2.8-bugfix), which is vulnerable to unsafe deserialization. The issue can lead to remote code execution. A patch is available in the `main` branch of the repository."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.8-corrección de errores y anteriores son vulnerables a una deserialización insegura en model_information.py. `model_name` en model_information.py toma la entrada proporcionada por el usuario (por ejemplo, la ruta a un modelo) y pasa ese valor a la función `run_model_information_script` y, posteriormente, a la función `model_information`, que carga dicho modelo con `torch.load` en rvc/train/process/model_information.py (en la línea 16 de 3.2.8-corrección de errores), que es vulnerable a una deserialización insegura. Este problema puede provocar la ejecución remota de código. Hay un parche disponible en la rama `main` del repositorio."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/rvc/train/process/model_information.py#L16", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/extra/model_information.py#L11-L16", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/commit/11d139508d615a6db4d48b76634a443c66170dda", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
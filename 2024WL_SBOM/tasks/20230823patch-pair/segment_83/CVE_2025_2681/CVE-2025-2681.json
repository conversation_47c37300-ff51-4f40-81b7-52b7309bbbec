{"cve_id": "CVE-2025-2681", "published_date": "2025-03-24T04:15:13.510", "last_modified_date": "2025-03-27T18:14:32.930", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Bank Locker Management System 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /edit-locker.php?ltid=6. The manipulation of the argument lockersize leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Bank Locker Management System 1.0. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /edit-locker.php?ltid=6. La manipulación del argumento lockersize provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/10", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300698", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300698", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521450", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
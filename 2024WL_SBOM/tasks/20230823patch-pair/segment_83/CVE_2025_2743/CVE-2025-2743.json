{"cve_id": "CVE-2025-2743", "published_date": "2025-03-25T07:15:38.883", "last_modified_date": "2025-07-08T18:58:27.257", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in zhijiantianya ruoyi-vue-pro 2.4.1. This issue affects some unknown processing of the file /admin-api/mp/material/upload-temporary of the component Material Upload Interface. The manipulation of the argument File leads to path traversal. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como problemática en zhijiantianya ruoyi-vue-pro 2.4.1. Este problema afecta a un procesamiento desconocido del archivo /admin-api/mp/material/upload-temporary del componente Material Upload Interface. La manipulación del argumento File provoca un path traversal. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/ruoyi-vue-pro.md#6arbitrary-file-deletion-vulnerability---uploadtemporarymaterial", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300845", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300845", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519692", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-30163", "published_date": "2025-03-24T19:15:52.937", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Cilium is a networking, observability, and security solution with an eBPF-based dataplane. Node based network policies (`fromNodes` and `toNodes`) will incorrectly permit traffic to/from non-node endpoints that share the labels specified in `fromNodes` and `toNodes` sections of network policies. Node based network policy is disabled by default in Cilium. This issue affects: Cilium v1.16 between v1.16.0 and v1.16.7 inclusive and v1.17 between v1.17.0 and v1.17.1 inclusive. This issue is fixed in Cilium v1.16.8 and v1.17.2. Users can work around this issue by ensuring that the labels used in `fromNodes` and `toNodes` fields are used exclusively by nodes and not by other endpoints."}, {"lang": "es", "value": "Cilium es una solución de redes, observabilidad y seguridad con un plano de datos basado en eBPF. Las políticas de red basadas en nodos (`fromNodes` y `toNodes`) permiten incorrectamente el tráfico hacia/desde endpoints que no son nodos y que comparten las etiquetas especificadas en las secciones `fromNodes` y `toNodes` de las políticas de red. La política de red basada en nodos está deshabilitada por defecto en Cilium. Este problema afecta a Cilium v1.16 entre v1.16.0 y v1.16.7 inclusive, y a v1.17 entre v1.17.0 y v1.17.1 inclusive. Este problema se ha corregido en Cilium v1.16.8 y v1.17.2. Los usuarios pueden solucionar este problema asegurándose de que las etiquetas utilizadas en los campos `fromNodes` y `toNodes` sean utilizadas exclusivamente por los nodos y no por otros endpoints."}], "references": [{"url": "https://docs.cilium.io/en/stable/security/policy/language/#node-based", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cilium/cilium/pull/36657", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cilium/cilium/security/advisories/GHSA-c6pf-2v8j-96mc", "source": "<EMAIL>", "tags": []}]}
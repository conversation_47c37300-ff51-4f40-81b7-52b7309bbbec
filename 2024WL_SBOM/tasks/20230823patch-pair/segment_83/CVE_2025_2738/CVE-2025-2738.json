{"cve_id": "CVE-2025-2738", "published_date": "2025-03-25T06:15:41.767", "last_modified_date": "2025-05-06T19:35:42.687", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Old Age Home Management System 1.0. It has been declared as critical. This vulnerability affects unknown code of the file /admin/manage-scdetails.php. The manipulation of the argument namesc leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Old Age Home Management System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/manage-scdetails.php. La manipulación del argumento namesc provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/X-X-007/cve/issues/2", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300760", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300760", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.522931", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
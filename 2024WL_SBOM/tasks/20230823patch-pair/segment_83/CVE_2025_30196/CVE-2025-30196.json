{"cve_id": "CVE-2025-30196", "published_date": "2025-03-19T16:15:33.950", "last_modified_date": "2025-03-19T19:15:50.980", "descriptions": [{"lang": "en", "value": "Jenkins AnchorChain Plugin 1.0 does not limit URL schemes for links it creates based on workspace content, allowing the `javascript:` scheme, resulting in a stored cross-site scripting (XSS) vulnerability exploitable by attackers able to control the input file for the Anchor Chain post-build step."}, {"lang": "es", "value": "El complemento Jenkins AnchorChain 1.0 no limita los esquemas de URL para los enlaces que crea según el contenido del espacio de trabajo, lo que permite el esquema `javascript:`, lo que genera una vulnerabilidad de cross-site scripting (XSS) almacenado que puede ser explotada por atacantes capaces de controlar el archivo de entrada para el paso posterior a la compilación de Anchor Chain."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-03-19/#SECURITY-3529", "source": "jenkins<PERSON>-<EMAIL>", "tags": []}]}
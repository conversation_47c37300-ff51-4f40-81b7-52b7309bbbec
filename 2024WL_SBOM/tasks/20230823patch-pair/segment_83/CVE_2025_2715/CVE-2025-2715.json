{"cve_id": "CVE-2025-2715", "published_date": "2025-03-24T23:15:13.440", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic has been found in timschofield webERP up to 5.0.0.rc+13. This affects an unknown part of the file ConfirmDispatch_Invoice.php of the component Confirm Dispatch and Invoice Page. The manipulation of the argument Narrative leads to cross site scripting. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. It is recommended to apply a patch to fix this issue. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como problemática en timschofield webERP hasta la versión 5.0.0.rc+13. Esta vulnerabilidad afecta a una parte desconocida del archivo ConfirmDispatch_Invoice.php del componente Confirm Dispatch and Invoice Page. La manipulación del argumento Narrative provoca ataques de cross site scripting. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se recomienda instalar un parche para solucionar este problema. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/janssensjelle/published-pocs/blob/main/weberp-xss-confirm-dispatch.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.300735", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.300735", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.519791", "source": "<EMAIL>", "tags": []}]}
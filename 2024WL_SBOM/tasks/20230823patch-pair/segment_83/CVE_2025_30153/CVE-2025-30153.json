{"cve_id": "CVE-2025-30153", "published_date": "2025-03-19T16:15:33.607", "last_modified_date": "2025-03-19T16:15:33.607", "descriptions": [{"lang": "en", "value": "kin-openapi is a Go project for handling OpenAPI files. Prior to 0.131.0, when validating a request with a multipart/form-data schema, if the OpenAPI schema allows it, an attacker can upload a crafted ZIP file (e.g., a ZIP bomb), causing the server to consume all available system memory. The root cause comes from the ZipFileBodyDecoder, which is registered automatically by the module (contrary to what the documentation says). This vulnerability is fixed in 0.131.0."}, {"lang": "es", "value": "kin-openapi es un proyecto de Go para gestionar archivos OpenAPI. Antes de la versión 0.131.0, al validar una solicitud con un esquema multipart/form-data, si el esquema OpenAPI lo permite, un atacante puede cargar un archivo ZIP manipulado (por ejemplo, una bomba ZIP), lo que provoca que el servidor consuma toda la memoria disponible del sistema. La causa principal proviene del ZipFileBodyDecoder, que el módulo registra automáticamente (al contrario de lo que indica la documentación). Esta vulnerabilidad se corrigió en la versión 0.131.0."}], "references": [{"url": "https://github.com/getkin/kin-openapi/blob/6da871e0e170b7637eb568c265c08bc2b5d6e7a3/openapi3filter/req_resp_decoder.go#L1275", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getkin/kin-openapi/blob/6da871e0e170b7637eb568c265c08bc2b5d6e7a3/openapi3filter/req_resp_decoder.go#L1523", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getkin/kin-openapi/commit/67f0b233ffc01332f7d993f79490fbea5f4455f1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getkin/kin-openapi/security/advisories/GHSA-wq9g-9vfc-cfq9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getkin/kin-openapi?tab=readme-ov-file#custom-content-type-for-body-of-http-requestresponse", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2749", "published_date": "2025-03-24T19:15:52.400", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "An authenticated remote code execution in Kentico Xperience allows authenticated users Staging Sync Server to upload arbitrary data to path relative locations. This results in path traversal and arbitrary file upload, including content that can be executed server side leading to remote code execution.This issue affects Kentico Xperience through 13.0.178."}, {"lang": "es", "value": "Una ejecución remota de código autenticada en Kentico Xperience permite a los usuarios autenticados del servidor de sincronización de pruebas cargar datos arbitrarios en ubicaciones path traversal. Esto provoca la navegación de la ruta y la carga de archivos arbitrarios, incluyendo contenido que puede ejecutarse en el servidor, lo que provoca la ejecución remota de código. Este problema afecta a Kentico Xperience hasta la versión 13.0.178."}], "references": [{"url": "https://devnet.kentico.com/download/hotfixes", "source": "<EMAIL>", "tags": []}, {"url": "https://labs.watchtowr.com/bypassing-authentication-like-its-the-90s-pre-auth-rce-chain-s-in-kentico-xperience-cms/", "source": "<EMAIL>", "tags": []}]}
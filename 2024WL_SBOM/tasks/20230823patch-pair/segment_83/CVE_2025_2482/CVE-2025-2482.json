{"cve_id": "CVE-2025-2482", "published_date": "2025-03-22T07:15:25.307", "last_modified_date": "2025-03-22T07:15:25.307", "descriptions": [{"lang": "en", "value": "The Gotcha | Gesture-based Captcha plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the 'menu' parameter in all versions up to, and including, 1.0.0 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Gotcha | Gesture-based Captcha para WordPress es vulnerable a Cross-Site Scripting reflejado a través del parámetro 'menu' en todas las versiones hasta la 1.0.0 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutan si logran engañar al usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/gotcha-gesture-based-captcha/trunk/admin/libs/setting.php#L223", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/gotcha-gesture-based-captcha/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6e7f1fe6-0a23-48e1-a75f-f8c1c8d4f8e0?source=cve", "source": "<EMAIL>", "tags": []}]}
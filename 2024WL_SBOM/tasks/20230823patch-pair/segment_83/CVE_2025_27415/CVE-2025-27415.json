{"cve_id": "CVE-2025-27415", "published_date": "2025-03-19T19:15:47.257", "last_modified_date": "2025-03-19T19:15:47.257", "descriptions": [{"lang": "en", "value": "Nuxt is an open-source web development framework for Vue.js. Prior to 3.16.0, by sending a crafted HTTP request to a server behind an CDN, it is possible in some circumstances to poison the CDN cache and highly impacts the availability of a site. It is possible to craft a request, such as https://mysite.com/?/_payload.json which will be rendered as JSON. If the CDN in front of a Nuxt site ignores the query string when determining whether to cache a route, then this JSON response could be served to future visitors to the site. An attacker can perform this attack to a vulnerable site in order to make a site unavailable indefinitely. It is also possible in the case where the cache will be reset to make a small script to send a request each X seconds (=caching duration) so that the cache is permanently poisoned making the site completely unavailable. This vulnerability is fixed in 3.16.0."}, {"lang": "es", "value": "Nuxt es un framework de desarrollo web de código abierto para Vue.js. Antes de la versión 3.16.0, al enviar una solicitud HTTP manipulada a un servidor tras una CDN, era posible, en ciertas circunstancias, contaminar la caché de la CDN, lo que afectaba gravemente la disponibilidad de un sitio. Es posible manipular una solicitud, como https://mysite.com/?/_payload.json, que se renderizaría como JSON. Si la CDN frente a un sitio Nuxt ignora la cadena de consulta al determinar si se debe almacenar en caché una ruta, esta respuesta JSON podría enviarse a futuros visitantes del sitio. Un atacante puede realizar este ataque a un sitio vulnerable para inhabilitarlo indefinidamente. También es posible, si se restablece la caché, crear un pequeño script que envíe una solicitud cada X segundos (= duración de la caché), de modo que la caché se inutilice permanentemente, dejando el sitio completamente indisponible. Esta vulnerabilidad se corrigió en la versión 3.16.0."}], "references": [{"url": "https://github.com/nuxt/nuxt/security/advisories/GHSA-jvhm-gjrh-3h93", "source": "<EMAIL>", "tags": []}]}
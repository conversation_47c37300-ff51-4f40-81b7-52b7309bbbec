{"cve_id": "CVE-2025-2532", "published_date": "2025-03-25T15:15:25.907", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Luxion KeyShot USDC File Parsing Use-After-Free Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Luxion KeyShot. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of usdc files. The issue results from the lack of validating the existence of an object prior to performing operations on the object. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-23709."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por Use-After-Free en el análisis de archivos USDC de Luxion KeyShot. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Luxion KeyShot. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos USDC. El problema se debe a que no se valida la existencia de un objeto antes de realizar operaciones en él. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Era ZDI-CAN-23709."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-175/", "source": "<EMAIL>", "tags": []}]}
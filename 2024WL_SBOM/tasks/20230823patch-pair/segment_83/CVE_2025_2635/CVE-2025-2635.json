{"cve_id": "CVE-2025-2635", "published_date": "2025-03-25T10:15:16.430", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The Digital License Manager plugin for WordPress is vulnerable to Reflected Cross-Site Scripting due to the use of remove_query_arg() function without appropriate escaping on the URL in all versions up to, and including, 1.7.3. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Digital License Manager para WordPress es vulnerable a ataques de Cross-Site Scripting reflejado debido al uso de la función remove_query_arg() sin el escape adecuado en la URL en todas las versiones hasta la 1.7.3 incluida. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutan si logran engañar al usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/digital-license-manager/trunk/includes/ListTables/Activations.php#L476", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3260900/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/digital-license-manager/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a266e003-3a0a-4832-a88b-60c2a26b387c?source=cve", "source": "<EMAIL>", "tags": []}]}
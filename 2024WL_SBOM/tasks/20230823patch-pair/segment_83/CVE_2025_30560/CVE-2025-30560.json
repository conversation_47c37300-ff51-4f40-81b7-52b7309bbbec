{"cve_id": "CVE-2025-30560", "published_date": "2025-03-24T14:15:26.910", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Sana Ullah jQuery Dropdown Menu allows Stored XSS. This issue affects jQuery Dropdown Menu: from n/a through 3.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Sana Ullah jQuery Dropdown Menu permite XSS almacenado. Este problema afecta al menú desplegable de jQuery desde n/d hasta la versión 3.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/jquery-drop-down-menu-plugin/vulnerability/wordpress-jquery-dropdown-menu-plugin-3-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
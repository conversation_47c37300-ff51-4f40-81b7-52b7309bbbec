{"cve_id": "CVE-2025-2109", "published_date": "2025-03-25T11:15:36.333", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The WP Compress – Instant Performance & Speed Optimization plugin for WordPress is vulnerable to Server-Side Request Forgery in all versions up to, and including, 6.30.15 via the init() function. This makes it possible for unauthenticated attackers to make web requests to arbitrary locations originating from the web application and can be used to query information from internal services."}, {"lang": "es", "value": "El complemento WP Compress – Instant Performance &amp; Speed Optimization para WordPress es vulnerable a Server-Side Request Forgery en todas las versiones hasta la 6.30.15 incluida, a través de la función init(). Esto permite a atacantes no autenticados realizar solicitudes web a ubicaciones arbitrarias desde la aplicación web y utilizarlas para consultar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-compress-image-optimizer/tags/6.30.15/wp-compress-core.php#L994", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3254259/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/wp-compress-image-optimizer/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/10b9d703-de9d-472a-bdfb-bc9a41bf375e?source=cve", "source": "<EMAIL>", "tags": []}]}
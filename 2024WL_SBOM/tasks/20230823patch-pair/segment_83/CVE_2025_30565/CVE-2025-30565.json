{"cve_id": "CVE-2025-30565", "published_date": "2025-03-24T14:15:28.347", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in karrikas banner-manager allows Stored XSS. This issue affects banner-manager: from n/a through 16.04.19."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en karrikas banner-manager permite XSS almacenado. Este problema afecta a banner-manager desde n/d hasta el 16/04/19."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/banner-manager/vulnerability/wordpress-banner-manager-plugin-16-04-19-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
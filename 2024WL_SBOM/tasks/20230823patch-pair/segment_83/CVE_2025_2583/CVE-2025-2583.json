{"cve_id": "CVE-2025-2583", "published_date": "2025-03-21T07:15:37.157", "last_modified_date": "2025-04-21T13:15:57.130", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SimpleMachines SMF 2.1.4. It has been classified as problematic. This affects an unknown part of the file ManageNews.php. The manipulation of the argument subject/message leads to cross site scripting. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The real existence of this vulnerability is still doubted at the moment. The vendor does not declare this issue a security vulnerability due to authentication requirements before being able to access any feature in the software that allows file modification."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SimpleMachines SMF 2.1.4. Se ha clasificado como problemática. Afecta a una parte desconocida del archivo ManageNews.php. La manipulación del argumento asunto/mensaje provoca ataques de cross site scripting. Es posible iniciar el ataque de forma remota. El exploit se ha divulgado públicamente y podría utilizarse. Se contactó al proveedor con antelación para informarle sobre esta divulgación."}], "references": [{"url": "https://github.com/Fewword/Poc/blob/main/smf/smf-poc5.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/Fewword/Poc/blob/main/smf/smf-poc6.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300543", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300543", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512001", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
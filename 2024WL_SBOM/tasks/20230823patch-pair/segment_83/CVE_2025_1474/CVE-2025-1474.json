{"cve_id": "CVE-2025-1474", "published_date": "2025-03-20T10:15:54.037", "last_modified_date": "2025-03-27T15:36:42.540", "descriptions": [{"lang": "en", "value": "In mlflow/mlflow version 2.18, an admin is able to create a new user account without setting a password. This vulnerability could lead to security risks, as accounts without passwords may be susceptible to unauthorized access. Additionally, this issue violates best practices for secure user account management. The issue is fixed in version 2.19.0."}, {"lang": "es", "value": "En la versión 2.18 de mlflow/mlflow, un administrador puede crear una nueva cuenta de usuario sin establecer una contraseña. Esta vulnerabilidad podría generar riesgos de seguridad, ya que las cuentas sin contraseña podrían ser vulnerables a accesos no autorizados. Además, este problema infringe las prácticas recomendadas para la administración segura de cuentas de usuario. El problema se solucionó en la versión 2.19.0."}], "references": [{"url": "https://github.com/mlflow/mlflow/commit/149c9e18aa219bc47e86b432e130e467a36f4a17", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://huntr.com/bounties/e79f7774-10fe-46b2-b522-e73b748e3b2d", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-30212", "published_date": "2025-03-25T15:15:26.143", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Frappe is a full-stack web application framework. An SQL Injection vulnerability has been identified in Frappe Framework prior to versions 14.89.0 and 15.51.0 which could allow a malicious actor to access sensitive information. Versions 14.89.0 and 15.51.0 fix the issue. Upgrading is required; no other workaround is present."}, {"lang": "es", "value": "Frappe es un framework de aplicaciones web integral. Se ha identificado una vulnerabilidad de inyección SQL en Frappe Framework anterior a las versiones 14.89.0 y 15.51.0, que podría permitir que un atacante acceda a información confidencial. Las versiones 14.89.0 y 15.51.0 solucionan el problema. Se requiere una actualización; no hay otro workaround."}], "references": [{"url": "https://github.com/frappe/frappe/commit/27f13437db161a173137d91cd07d0f9287d7c556", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/frappe/frappe/commit/2ebd88520ecfa9bb7d3392b7de8c8f94a86ec05c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/frappe/frappe/security/advisories/GHSA-3hj6-r5c9-q8f3", "source": "<EMAIL>", "tags": []}]}
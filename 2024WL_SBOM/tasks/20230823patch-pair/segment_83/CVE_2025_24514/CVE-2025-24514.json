{"cve_id": "CVE-2025-24514", "published_date": "2025-03-25T00:15:15.047", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "A security issue was discovered in  ingress-nginx https://github.com/kubernetes/ingress-nginx  where the `auth-url` Ingress annotation can be used to inject configuration into nginx. This can lead to arbitrary code execution in the context of the ingress-nginx controller, and disclosure of Secrets accessible to the controller. (Note that in the default installation, the controller can access all Secrets cluster-wide.)"}, {"lang": "es", "value": "Se detectó un problema de seguridad en ingress-nginx (https://github.com/kubernetes/ingress-nginx) donde la anotación `auth-url` de Ingress puede usarse para inyectar configuración en nginx. Esto puede provocar la ejecución de código arbitrario en el contexto del controlador de ingress-nginx y la divulgación de secretos accesibles para el controlador. (Tenga en cuenta que, en la instalación predeterminada, el controlador puede acceder a todos los secretos del clúster)."}], "references": [{"url": "https://github.com/kubernetes/kubernetes/issues/131006", "source": "<EMAIL>", "tags": []}]}
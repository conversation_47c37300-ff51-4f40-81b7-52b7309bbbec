{"cve_id": "CVE-2025-2556", "published_date": "2025-03-20T18:15:20.400", "last_modified_date": "2025-03-20T18:15:20.400", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in Audi UTR Dashcam 2.0. Affected by this vulnerability is an unknown functionality of the component Video Stream Handler. The manipulation leads to hard-coded credentials. The attack can only be initiated within the local network. The exploit has been disclosed to the public and may be used. Upgrading to version 2.89 and 2.90 is able to address this issue. It is recommended to upgrade the affected component. The vendor was contacted early about these issues and acted very professional. Version 2.89 is fixing this issue for new customers and 2.90 is going to fix it for existing customers."}, {"lang": "es", "value": "Se detectó una vulnerabilidad clasificada como problemática en Audi UTR Dashcam 2.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del componente Video Stream Handler. La manipulación permite obtener credenciales codificadas. El ataque solo puede iniciarse dentro de la red local. Se ha hecho público el exploit y puede que sea utilizado. Actualizar a las versiones 2.89 y 2.90 puede solucionar este problema. Se recomienda actualizar el componente afectado. Se contactó con el proveedor con prontitud para informarle sobre estos problemas y actuó con gran profesionalidad. La versión 2.89 soluciona este problema para los nuevos clientes y la 2.90 lo solucionará para los clientes existentes."}], "references": [{"url": "https://github.com/geo-chen/Audi/blob/main/README.md#finding-3-rtsp-and-ftp-wide-open", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.300169", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.300169", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.513392", "source": "<EMAIL>", "tags": []}]}
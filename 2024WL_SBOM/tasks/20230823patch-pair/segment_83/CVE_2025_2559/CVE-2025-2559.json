{"cve_id": "CVE-2025-2559", "published_date": "2025-03-25T09:15:17.047", "last_modified_date": "2025-04-30T03:15:17.857", "descriptions": [{"lang": "en", "value": "A flaw was found in Keycloak. When the configuration uses JWT tokens for authentication, the tokens are cached until expiration. If a client uses JWT tokens with an excessively long expiration time, for example, 24 or 48 hours, the cache can grow indefinitely, leading to an OutOfMemoryError. This issue could result in a denial of service condition, preventing legitimate users from accessing the system."}, {"lang": "es", "value": "Se detectó una falla en Keycloak. Cuando la configuración utiliza tokens JWT para la autenticación, estos se almacenan en caché hasta su vencimiento. Si un cliente utiliza tokens JWT con un tiempo de vencimiento demasiado largo, por ejemplo, 24 o 48 horas, la caché puede crecer indefinidamente, lo que genera un error de memoria (OutOfMemoryError). Este problema podría generar una condición de denegación de servicio, impidiendo que los usuarios legítimos accedan al sistema."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2025:4335", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2025:4336", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2025-2559", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2353868", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2682", "published_date": "2025-03-24T04:15:14.643", "last_modified_date": "2025-03-27T18:14:29.287", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Bank Locker Management System 1.0. This affects an unknown part of the file /edit-subadmin.php?said=3. The manipulation of the argument mobilenumber leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Bank Locker Management System 1.0. Esta afecta a una parte desconocida del archivo /edit-subadmin.php?said=3. La manipulación del argumento \"mobilenumber\" provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/11", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300699", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300699", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521451", "source": "<EMAIL>", "tags": ["VDB Entry"]}]}
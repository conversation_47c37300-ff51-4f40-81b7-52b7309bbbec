{"cve_id": "CVE-2025-30564", "published_date": "2025-03-24T14:15:28.200", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in wpwox Custom Script Integration allows Stored XSS. This issue affects Custom Script Integration: from n/a through 2.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en wpwox Custom Script Integration permite XSS almacenado. Este problema afecta a la integración de scripts personalizados desde n/d hasta la versión 2.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/custom-script-integration/vulnerability/wordpress-custom-script-integration-2-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
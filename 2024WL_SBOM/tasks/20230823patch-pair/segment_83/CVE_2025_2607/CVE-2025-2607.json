{"cve_id": "CVE-2025-2607", "published_date": "2025-03-21T21:15:37.430", "last_modified_date": "2025-04-01T20:15:25.810", "descriptions": [{"lang": "en", "value": "A vulnerability was found in phplaozhang LzCMS-LaoZhangBoKeXiTong up to 1.1.4. It has been rated as critical. Affected by this issue is some unknown functionality of the file /admin/upload/upimage.html of the component HTTP POST Request Handler. The manipulation of the argument File leads to unrestricted upload. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en phplaozhang LzCMS-LaoZhangBoKeXiTong hasta la versión 1.1.4. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/upload/upimage.html del componente HTTP POST Request Handler. La manipulación del argumento \"File\" permite la carga sin restricciones. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Jingyi-u/lzcms/tree/main", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.300590", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300590", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.518021", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
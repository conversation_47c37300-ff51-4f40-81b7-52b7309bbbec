{"cve_id": "CVE-2025-2231", "published_date": "2025-03-24T20:15:18.370", "last_modified_date": "2025-07-09T00:49:38.953", "descriptions": [{"lang": "en", "value": "PDF-XChange Editor RTF File Parsing Out-Of-Bounds Read Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of PDF-XChange Editor. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of RTF files. The issue results from the lack of proper validation of user-supplied data, which can result in a read past the end of an allocated buffer. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-25473."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código al analizar archivos RTF en PDF-XChange Editor. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de PDF-XChange Editor. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos RTF. El problema se debe a la falta de una validación adecuada de los datos proporcionados por el usuario, lo que puede provocar una lectura más allá del límite del búfer asignado. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Anteriormente, se denominó ZDI-CAN-25473."}], "references": [{"url": "https://www.pdf-xchange.com/support/security-bulletins.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-129/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
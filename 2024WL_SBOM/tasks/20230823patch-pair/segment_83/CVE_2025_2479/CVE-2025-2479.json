{"cve_id": "CVE-2025-2479", "published_date": "2025-03-22T07:15:25.123", "last_modified_date": "2025-03-22T07:15:25.123", "descriptions": [{"lang": "en", "value": "The Easy Custom Admin Bar plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the ‘msg’ parameter in all versions up to, and including, 1.0 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Easy Custom Admin Bar para WordPress es vulnerable a ataques de Cross-Site Scripting Reflejado a través del parámetro 'msg' en todas las versiones hasta la 1.0 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutan si logran engañar al usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/easy-custom-admin-bar/trunk/adminbar.php#L198", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/easy-custom-admin-bar/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/140f633c-c2e4-4b3c-befc-d870e06be970?source=cve", "source": "<EMAIL>", "tags": []}]}
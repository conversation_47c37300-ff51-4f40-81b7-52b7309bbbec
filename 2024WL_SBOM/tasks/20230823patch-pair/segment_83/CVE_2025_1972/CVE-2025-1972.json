{"cve_id": "CVE-2025-1972", "published_date": "2025-03-22T12:15:26.453", "last_modified_date": "2025-07-09T17:46:11.870", "descriptions": [{"lang": "en", "value": "The Export and Import Users and Customers plugin for WordPress is vulnerable to arbitrary file deletion due to insufficient file path validation in the admin_log_page() function in all versions up to, and including, 2.6.2. This makes it possible for authenticated attackers, with Administrator-level access and above, to delete arbitrary log files on the server."}, {"lang": "es", "value": "El complemento Export and Import Users and Customers para WordPress es vulnerable a la eliminación arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función admin_log_page() en todas las versiones hasta la 2.6.2 incluida. Esto permite que atacantes autenticados, con acceso de administrador o superior, eliminen archivos de registro arbitrarios en el servidor."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/users-customers-import-export-for-wp-woocommerce/trunk/admin/modules/history/history.php#L248", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3259688/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/users-customers-import-export-for-wp-woocommerce/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2d443c70-6537-4c6d-a282-12d392f0f558?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-30578", "published_date": "2025-03-24T14:15:30.073", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in hotvanrod AdSense Privacy Policy allows Stored XSS. This issue affects AdSense Privacy Policy: from n/a through 1.1.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en hotvanrod AdSense Privacy Policy permite XSS almacenado. Este problema afecta a la Política de Privacidad de AdSense desde la versión n/d hasta la 1.1.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/adsense-privacy-policy/vulnerability/wordpress-adsense-privacy-policy-plugin-1-1-1-cross-site-request-forgery-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
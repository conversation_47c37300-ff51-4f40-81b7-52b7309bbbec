{"cve_id": "CVE-2025-30112", "published_date": "2025-03-24T17:15:21.550", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "On 70mai Dash Cam 1S devices, by connecting directly to the dashcam's network and accessing the API on port 80 and RTSP on port 554, an attacker can bypass the device authorization mechanism from the official mobile app that requires a user to physically press on the power button during a connection."}, {"lang": "es", "value": "En los dispositivos 70mai Dash Cam 1S, al conectarse directamente a la red de la cámara del tablero y acceder a la API en el puerto 80 y RTSP en el puerto 554, un atacante puede eludir el mecanismo de autorización del dispositivo desde la aplicación móvil oficial que requiere que el usuario presione físicamente el botón de encendido durante una conexión."}], "references": [{"url": "https://github.com/geo-chen/70mai/blob/main/README.md#finding-1---cve-2025-30112-bypass-device-pairing-of-70mai-dashcam-1s", "source": "<EMAIL>", "tags": []}, {"url": "https://www.70mai.com/cam1s/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0478", "published_date": "2025-03-24T12:15:13.227", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Software installed and run as a non-privileged user may conduct improper GPU system calls to issue reads and writes to arbitrary physical memory pages.\n\nUnder certain circumstances this exploit could be used to corrupt data pages not allocated by the GPU driver but memory pages in use by the kernel and drivers running on the platform, altering their behaviour."}, {"lang": "es", "value": "El software instalado y ejecutado por un usuario sin privilegios puede realizar llamadas indebidas al sistema de la GPU para ejecutar operaciones de lectura y escritura en páginas arbitrarias de memoria física. En determinadas circunstancias, este exploit podría utilizarse para corromper páginas de datos no asignadas por el controlador de la GPU, sino páginas de memoria utilizadas por el kernel y los controladores de la plataforma, alterando así su comportamiento."}], "references": [{"url": "https://www.imaginationtech.com/gpu-driver-vulnerabilities/", "source": "367425dc-4d06-4041-9650-c2dc6aaa27ce", "tags": []}]}
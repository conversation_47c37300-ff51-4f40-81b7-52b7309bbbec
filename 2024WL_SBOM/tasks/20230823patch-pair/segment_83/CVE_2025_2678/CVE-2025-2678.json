{"cve_id": "CVE-2025-2678", "published_date": "2025-03-24T03:15:14.573", "last_modified_date": "2025-06-04T15:46:43.063", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Bank Locker Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /changeimage1.php. The manipulation of the argument editid leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Bank Locker Management System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /changeimage1.php. La manipulación del argumento editid provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300695", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300695", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521445", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
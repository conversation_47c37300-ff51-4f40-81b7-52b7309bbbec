{"cve_id": "CVE-2025-2640", "published_date": "2025-03-23T04:15:14.120", "last_modified_date": "2025-04-02T14:26:23.040", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Doctor Appointment Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /doctor/appointment-bwdates-reports-details.php. The manipulation of the argument fromdate/todate leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Doctor Appointment Management System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /doctor/appointment-bwdates-reports-details.php. La manipulación del argumento fromdate/todate provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/AiENG07/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300641", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300641", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.519644", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
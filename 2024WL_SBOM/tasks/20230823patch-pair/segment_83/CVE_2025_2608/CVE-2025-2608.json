{"cve_id": "CVE-2025-2608", "published_date": "2025-03-21T22:15:26.250", "last_modified_date": "2025-05-28T20:56:58.183", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Banquet Booking System 1.2. This affects an unknown part of the file /admin/view-user-queries.php. The manipulation of the argument viewid leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Banquet Booking System 1.2. Esta afecta a una parte desconocida del archivo /admin/view-user-queries.php. La manipulación del argumento viewid provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/emano888/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300591", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300591", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.518587", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-1971", "published_date": "2025-03-22T12:15:26.250", "last_modified_date": "2025-07-09T17:50:49.670", "descriptions": [{"lang": "en", "value": "The Export and Import Users and Customers plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 2.6.2 via deserialization of untrusted input from the 'form_data' parameter. This makes it possible for authenticated attackers, with Administrator-level access and above, to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present."}, {"lang": "es", "value": "El complemento Export and Import Users and Customers para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 2.6.2 incluida, mediante la deserialización de entradas no confiables del parámetro 'form_data'. Esto permite a atacantes autenticados, con acceso de administrador o superior, inyectar un objeto PHP. No se conoce ninguna cadena POP presente en el software vulnerable, lo que significa que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si una cadena POP está presente a través de un complemento o tema adicional instalado en el sistema objetivo, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código, dependiendo de la cadena POP presente."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/users-customers-import-export-for-wp-woocommerce/trunk/admin/modules/export/classes/class-export-ajax.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/users-customers-import-export-for-wp-woocommerce/trunk/admin/modules/import/classes/class-import-ajax.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3259688/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/users-customers-import-export-for-wp-woocommerce/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4b24b3d2-589f-47b2-bcdd-bebc87cafeda?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
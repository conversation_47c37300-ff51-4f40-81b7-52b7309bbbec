{"cve_id": "CVE-2025-30205", "published_date": "2025-03-24T17:15:21.667", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "kanidim-provision is a helper utility that uses kanidm's API to provision users, groups and oauth2 systems. Prior to version 1.2.0, a faulty function intrumentation in the (optional) kanidm patches provided by kandim-provision will cause the provisioned admin credentials to be leaked to the system log. This only impacts users which both use the provided patches and provision their `admin` or `idm_admin` account credentials this way. No other credentials are affected. Users should recompile kanidm with the newest patchset from tag `v1.2.0` or higher. As a workaround, the user can set the log level `KANIDM_LOG_LEVEL` to any level higher than `info`, for example `warn`."}, {"lang": "es", "value": "kanidim-provision es una utilidad auxiliar que utiliza la API de kanidm para aprovisionar usuarios, grupos y sistemas OAuth2. En versiones anteriores a la 1.2.0, una implementación defectuosa de las funciones de los parches de kanidm (opcionales) proporcionados por kandim-provision provocaba que las credenciales de administrador aprovisionadas se filtraran al registro del sistema. Esto solo afecta a los usuarios que usan los parches proporcionados y aprovisionan sus credenciales de cuenta `admin` o `idm_admin` de esta manera. No se ven afectadas otras credenciales. Los usuarios deben recompilar kanidm con el conjunto de parches más reciente, a partir de la etiqueta `v1.2.0` o superior. Como solución alternativa, el usuario puede establecer el nivel de registro `KANIDM_LOG_LEVEL` en cualquier nivel superior a `info`, por ejemplo, `warn`."}], "references": [{"url": "https://github.com/oddlama/kanidm-provision/commit/a102b52e4a79be4263068577ba837f16c3e487a2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/oddlama/kanidm-provision/security/advisories/GHSA-57fc-pcqm-53rp", "source": "<EMAIL>", "tags": []}]}
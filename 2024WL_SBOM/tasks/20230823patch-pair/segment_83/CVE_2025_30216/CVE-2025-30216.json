{"cve_id": "CVE-2025-30216", "published_date": "2025-03-25T20:15:22.567", "last_modified_date": "2025-05-06T19:34:21.847", "descriptions": [{"lang": "en", "value": "CryptoLib provides a software-only solution using the CCSDS Space Data Link Security Protocol - Extended Procedures (SDLS-EP) to secure communications between a spacecraft running the core Flight System (cFS) and a ground station. In versions 1.3.3 and prior, a Heap Overflow vulnerability occurs in the `Crypto_TM_ProcessSecurity` function (`crypto_tm.c:1735:8`). When processing the Secondary Header Length of a TM protocol packet, if the Secondary Header Length exceeds the packet's total length, a heap overflow is triggered during the memcpy operation that copies packet data into the dynamically allocated buffer `p_new_dec_frame`. This allows an attacker to overwrite adjacent heap memory, potentially leading to arbitrary code execution or system instability. A patch is available at commit 810fd66d592c883125272fef123c3240db2f170f."}, {"lang": "es", "value": "CryptoLib ofrece una solución exclusivamente de software que utiliza el Protocolo de Seguridad de Enlace de Datos Espaciales CCSDS - Procedimientos Extendidos (SDLS-EP) para proteger las comunicaciones entre una nave espacial que ejecuta el Sistema de Vuelo (cFS) y una estación terrestre. En las versiones 1.3.3 y anteriores, se produce una vulnerabilidad de desbordamiento de pila en la función `Crypto_TM_ProcessSecurity` (`crypto_tm.c:1735:8`). Al procesar la longitud de la cabecera secundaria de un paquete del protocolo TM, si esta supera la longitud total del paquete, se produce un desbordamiento de pila durante la operación memcpy, que copia los datos del paquete en el búfer asignado dinámicamente `p_new_dec_frame`. Esto permite a un atacante sobrescribir la memoria de pila adyacente, lo que podría provocar la ejecución de código arbitrario o la inestabilidad del sistema. Hay un parche disponible en el commit 810fd66d592c883125272fef123c3240db2f170f."}], "references": [{"url": "https://github.com/nasa/CryptoLib/commit/810fd66d592c883125272fef123c3240db2f170f", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/nasa/CryptoLib/security/advisories/GHSA-v3jc-5j74-hcjv", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://github.com/user-attachments/assets/d49cea04-ce84-4d60-bb3a-987e843f09c4", "source": "<EMAIL>", "tags": ["Broken Link"]}]}
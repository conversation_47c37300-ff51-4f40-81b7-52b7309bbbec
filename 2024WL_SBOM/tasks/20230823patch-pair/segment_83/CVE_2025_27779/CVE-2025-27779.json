{"cve_id": "CVE-2025-27779", "published_date": "2025-03-19T21:15:39.850", "last_modified_date": "2025-03-19T21:15:39.850", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.8-bugfix and prior are vulnerable to unsafe deserialization in `model_blender.py` lines 20 and 21. `model_fusion_a` and `model_fusion_b` from voice_blender.py take user-supplied input (e.g. a path to a model) and pass that value to the `run_model_blender_script` and later to `model_blender` function, which loads these two models with `torch.load` in `model_blender.py (on lines 20-21 in 3.2.8-bugfix), which is vulnerable to unsafe deserialization. The issue can lead to remote code execution. A patch is available on the `main` branch of the Applio repository."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.8-corrección de errores y anteriores son vulnerables a una deserialización insegura en las líneas 20 y 21 de `model_blender.py`. `model_fusion_a` y `model_fusion_b` de `voice_blender.py` toman la entrada proporcionada por el usuario (por ejemplo, la ruta a un modelo) y pasan ese valor a la función `run_model_blender_script` y, posteriormente, a la función `model_blender`, que carga estos dos modelos con `torch.load` en `model_blender.py` (en las líneas 20-21 de la versión 3.2.8-corrección de errores), que es vulnerable a una deserialización insegura. Este problema puede provocar la ejecución remota de código. Hay un parche disponible en la rama `main` del repositorio de Applio."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/rvc/train/process/model_blender.py#L20-L21", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/voice_blender/voice_blender.py#L39-L56", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/commit/11d139508d615a6db4d48b76634a443c66170dda", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
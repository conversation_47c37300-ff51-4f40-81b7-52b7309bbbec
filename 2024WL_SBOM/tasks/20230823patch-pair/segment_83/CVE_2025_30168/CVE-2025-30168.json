{"cve_id": "CVE-2025-30168", "published_date": "2025-03-21T15:15:43.440", "last_modified_date": "2025-03-21T15:15:43.440", "descriptions": [{"lang": "en", "value": "Parse Server is an open source backend that can be deployed to any infrastructure that can run Node.js. Prior to 7.5.2 and 8.0.2, the 3rd party authentication handling of Parse Server allows the authentication credentials of some specific authentication providers to be used across multiple Parse Server apps. For example, if a user signed up using the same authentication provider in two unrelated Parse Server apps, the credentials stored by one app can be used to authenticate the same user in the other app. Note that this only affects Parse Server apps that specifically use an affected 3rd party authentication provider for user authentication, for example by setting the Parse Server option auth to configure a Parse Server authentication adapter. The fix of this vulnerability requires to upgrade Parse Server to a version that includes the bug fix, as well as upgrade the client app to send a secure payload, which is different from the previous insecure payload. This vulnerability is fixed in 7.5.2 and 8.0.2."}, {"lang": "es", "value": "Parse Server es un backend de código abierto que puede implementarse en cualquier infraestructura que ejecute Node.js. En versiones anteriores a la 7.5.2 y la 8.0.2, la gestión de la autenticación de terceros de Parse Server permitía usar las credenciales de algunos proveedores de autenticación específicos en varias aplicaciones de Parse Server. Por ejemplo, si un usuario se registraba con el mismo proveedor de autenticación en dos aplicaciones de Parse Server independientes, las credenciales almacenadas por una aplicación podían usarse para autenticar al mismo usuario en la otra. Tenga en cuenta que esto solo afecta a las aplicaciones de Parse Server que utilizan específicamente un proveedor de autenticación de terceros afectado para la autenticación de usuarios, por ejemplo, configurando la opción \"auth\" de Parse Server para configurar un adaptador de autenticación de Parse Server. Para corregir esta vulnerabilidad, es necesario actualizar Parse Server a una versión que incluya la corrección del error, así como actualizar la aplicación cliente para que envíe un payload seguro, diferente del payload inseguro anterior. Esta vulnerabilidad se corrigió en las versiones 7.5.2 y 8.0.2."}], "references": [{"url": "https://docs.parseplatform.org/parse-server/guide/#oauth-and-3rd-party-authentication", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/commit/2ff9c71030bce3aada0a00fbceedeb7ae2c8a41e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/commit/5ef0440c8e763854e62341acaeb6dc4ade3ba82f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/pull/9667", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/pull/9668", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/security/advisories/GHSA-837q-jhwx-cmpv", "source": "<EMAIL>", "tags": []}]}
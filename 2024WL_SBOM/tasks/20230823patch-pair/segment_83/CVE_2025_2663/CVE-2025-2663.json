{"cve_id": "CVE-2025-2663", "published_date": "2025-03-23T21:15:14.603", "last_modified_date": "2025-05-13T20:13:53.563", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Bank Locker Management System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /search-locker-details.php. The manipulation of the argument searchinput leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en PHPGurukul Bank Locker Management System 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /search-locker-details.php. La manipulación del argumento \"searchinput\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300685", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300685", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520436", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
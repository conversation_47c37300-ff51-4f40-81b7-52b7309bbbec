{"cve_id": "CVE-2025-0845", "published_date": "2025-03-25T06:15:40.213", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The DesignThemes Core Features plugin for WordPress is vulnerable to Stored Cross-Site Scripting via shortcodes in versions up to, and including, 4.8 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with contributor-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento DesignThemes Core Features para WordPress es vulnerable a cross site scripting almacenado mediante shortcodes en versiones hasta la 4.8, incluida, debido a una depuración de entrada y al escape de salida insuficiente en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados con permisos de colaborador o superiores inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://themeforest.net/item/lms-learning-management-system-education-lms-wordpress-theme/7867581", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/39ea4627-66b2-42a6-913e-04c708491b8d?source=cve", "source": "<EMAIL>", "tags": []}]}
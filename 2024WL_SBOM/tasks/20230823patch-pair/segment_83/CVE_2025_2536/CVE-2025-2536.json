{"cve_id": "CVE-2025-2536", "published_date": "2025-03-19T19:15:50.560", "last_modified_date": "2025-03-19T19:15:50.560", "descriptions": [{"lang": "en", "value": "Cross-site scripting (XSS) vulnerability on Liferay Portal ******** through *********, and Liferay DXP 2024.Q3.0, 2024.Q2.0 through 2024.Q2.13, 2024.Q1.1 through 2024.Q1.12, 2023.Q4.0 through 2023.Q4.10, 2023.Q3.1 through 2023.Q3.10, 7.4 update 82 through update 92 in the Frontend JS module's layout-taglib/__liferay__/index.js allows remote attackers to inject arbitrary web script or HTML via toastData parameter"}, {"lang": "es", "value": "Vulnerabilidad de cross-site scripting (XSS) en Liferay Portal ******** a *********, y Liferay DXP 2024.Q3.0, 2024.Q2.0 a 2024.Q2.13, 2024.Q1.1 a 2024.Q1.12, 2023.Q4.0 a 2023.Q4.10, 2023.Q3.1 a 2023.Q3.10, 7.4 actualización 82 a 92 en layout-taglib/__liferay__/index.js del módulo Frontend JS permite a atacantes remotos inyectar script web o HTML arbitrarios mediante el parámetro toastData."}], "references": [{"url": "https://liferay.dev/portal/security/known-vulnerabilities/-/asset_publisher/jekt/content/CVE-2025-2536", "source": "<EMAIL>", "tags": []}]}
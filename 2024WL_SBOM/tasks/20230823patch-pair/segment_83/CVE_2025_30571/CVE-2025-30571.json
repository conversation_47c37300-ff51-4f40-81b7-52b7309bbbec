{"cve_id": "CVE-2025-30571", "published_date": "2025-03-24T14:15:29.077", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in STEdb Corp. STEdb Forms allows SQL Injection. This issue affects STEdb Forms: from n/a through 1.0.4."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en STEdb Corp. STEdb Forms permite la inyección SQL. Este problema afecta a STEdb Forms desde n/d hasta la versión 1.0.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/stedb-forms/vulnerability/wordpress-stedb-forms-1-0-4-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
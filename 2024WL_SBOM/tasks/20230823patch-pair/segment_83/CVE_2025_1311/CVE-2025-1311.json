{"cve_id": "CVE-2025-1311", "published_date": "2025-03-22T07:15:24.433", "last_modified_date": "2025-03-22T07:15:24.433", "descriptions": [{"lang": "en", "value": "The WooCommerce Multivendor Marketplace – REST API plugin for WordPress is vulnerable to SQL Injection via the 'id' parameter in the update_delivery_status() function in all versions up to, and including, 1.6.2 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento WooCommerce Multivendor Marketplace – API REST para WordPress es vulnerable a la inyección SQL a través del parámetro 'id' de la función update_delivery_status() en todas las versiones hasta la 1.6.2 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, añadir consultas SQL adicionales a las consultas existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wcfm-marketplace-rest-api/trunk/includes/api/class-api-deliveries-controller.php#L303", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/wcfm-marketplace-rest-api/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6ff5cda2-edcd-4fa5-9c8e-427a43802ed1?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1974", "published_date": "2025-03-25T00:15:14.753", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "A security issue was discovered in Kubernetes where under certain conditions, an unauthenticated attacker with access to the pod network can achieve arbitrary code execution in the context of the ingress-nginx controller. This can lead to disclosure of Secrets accessible to the controller. (Note that in the default installation, the controller can access all Secrets cluster-wide.)"}, {"lang": "es", "value": "Se descubrió un problema de seguridad en Kubernetes donde, bajo ciertas condiciones, un atacante no autenticado con acceso a la red de pods puede ejecutar código arbitrario en el contexto del controlador ingress-nginx. Esto puede provocar la divulgación de secretos accesibles al controlador. (Tenga en cuenta que, en la instalación predeterminada, el controlador puede acceder a todos los secretos del clúster)."}], "references": [{"url": "https://https://github.com/kubernetes/kubernetes/issues/131009", "source": "<EMAIL>", "tags": []}]}
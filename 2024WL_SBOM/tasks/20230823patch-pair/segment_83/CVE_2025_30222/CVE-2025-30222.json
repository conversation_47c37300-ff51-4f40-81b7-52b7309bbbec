{"cve_id": "CVE-2025-30222", "published_date": "2025-03-25T23:15:36.707", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Shescape is a simple shell escape library for JavaScript. Versions 1.7.2 through 2.1.1 are vulnerable to potential environment variable exposure on Windows with CMD. This impact users of Shescape on Windows that explicitly configure `shell: 'cmd.exe'` or `shell: true` using any of `quote`/`quoteAll`/`escape`/`escapeAll`. An attacker may be able to get read-only access to environment variables. This bug has been patched in v2.1.2. For those who are already using v2 of Shescape, no further changes are required. Those who are are using v1 of Shescape should follow the migration guide to upgrade to v2. There is no plan to release a patch compatible with v1 of Shescape. As a workaround, users can remove all instances of `%` from user input before using Shescape."}, {"lang": "es", "value": "Shescape es una librería de escape de shell sencilla para JavaScript. Las versiones 1.7.2 a 2.1.1 son vulnerables a la posible exposición de variables de entorno en Windows con CMD. Esto afecta a los usuarios de Shescape en Windows que configuran explícitamente `shell: 'cmd.exe'` o `shell: true` mediante `quote`/`quoteAll`/`escape`/`escapeAll`. Un atacante podría obtener acceso de solo lectura a las variables de entorno. Este error se ha corregido en la versión 2.1.2. Quienes ya utilizan la versión 2 de Shescape no requieren cambios adicionales. Quienes utilizan la versión 1 de Shescape deben seguir la guía de migración para actualizar a la versión 2. No se prevé publicar un parche compatible con la versión 1 de Shescape. Como workaround, los usuarios pueden eliminar todas las instancias de `%` de la entrada de usuario antes de usar Shescape."}], "references": [{"url": "https://github.com/ericcornelissen/shescape/commit/0a81f1eb077bab8caae283a2490cd7be9af179c6", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ericcornelissen/shescape/pull/1916", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ericcornelissen/shescape/releases/tag/v2.1.2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ericcornelissen/shescape/security/advisories/GHSA-66pp-5p9w-q87j", "source": "<EMAIL>", "tags": []}]}
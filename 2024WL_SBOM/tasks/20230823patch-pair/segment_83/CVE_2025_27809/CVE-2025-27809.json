{"cve_id": "CVE-2025-27809", "published_date": "2025-03-25T06:15:41.000", "last_modified_date": "2025-07-17T15:57:21.527", "descriptions": [{"lang": "en", "value": "Mbed TLS before 2.28.10 and 3.x before 3.6.3, on the client side, accepts servers that have trusted certificates for arbitrary hostnames unless the TLS client application calls mbedtls_ssl_set_hostname."}, {"lang": "es", "value": "Mbed TLS anterior a 2.28.10 y 3.x anterior a 3.6.3, en el lado del cliente, acepta servidores que tienen certificados confiables para nombres de host arbitrarios a menos que la aplicación cliente TLS llame a mbedtls_ssl_set_hostname."}], "references": [{"url": "https://github.com/Mbed-TLS/mbedtls/releases", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://mbed-tls.readthedocs.io/en/latest/security-advisories/mbedtls-security-advisory-2025-03-1/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/Mbed-TLS/mbedtls/issues/466", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Issue Tracking"]}, {"url": "https://mastodon.social/@bagder/114219540623402700", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Not Applicable"]}]}
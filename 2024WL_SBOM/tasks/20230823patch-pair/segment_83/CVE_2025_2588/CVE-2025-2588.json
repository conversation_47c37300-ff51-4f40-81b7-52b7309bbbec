{"cve_id": "CVE-2025-2588", "published_date": "2025-03-21T12:15:26.553", "last_modified_date": "2025-04-01T20:24:28.240", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Hercules Augeas 1.14.1 and classified as problematic. This vulnerability affects the function re_case_expand of the file src/fa.c. The manipulation of the argument re leads to null pointer dereference. Attacking locally is a requirement. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en Hercules Augeas 1.14.1, clasificada como problemática. Esta vulnerabilidad afecta a la función re_case_expand del archivo src/fa.c. La manipulación del argumento re provoca la desreferenciación de puntero nulo. Es necesario realizar ataques locales. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/hercules-team/augeas/issues/852", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/hercules-team/augeas/issues/852#issue-2905999609", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300568", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300568", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517281", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
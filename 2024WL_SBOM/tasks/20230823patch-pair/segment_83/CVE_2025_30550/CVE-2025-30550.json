{"cve_id": "CVE-2025-30550", "published_date": "2025-03-24T14:15:24.117", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in WPShop.ru CallPhone'r allows Stored XSS. This issue affects CallPhone'r: from n/a through 1.1.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en CallPhone'r de WPShop.ru permite XSS almacenado. Este problema afecta a CallPhone'r desde la versión n/d hasta la 1.1.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/callphoner/vulnerability/wordpress-callphone-r-plugin-1-1-1-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
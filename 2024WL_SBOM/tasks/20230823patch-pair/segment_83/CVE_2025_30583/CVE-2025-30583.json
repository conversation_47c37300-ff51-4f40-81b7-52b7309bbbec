{"cve_id": "CVE-2025-30583", "published_date": "2025-03-24T14:15:30.353", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in ProRankTracker Pro Rank Tracker allows Stored XSS. This issue affects Pro Rank Tracker: from n/a through 1.0.0."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en ProRankTracker. Pro Rank Tracker permite XSS almacenado. Este problema afecta a Pro Rank Tracker desde n/d hasta la versión 1.0.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/proranktracker/vulnerability/wordpress-pro-rank-tracker-plugin-1-0-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2662", "published_date": "2025-03-23T20:15:12.697", "last_modified_date": "2025-07-09T01:02:12.363", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Project Worlds Online Time Table Generator 1.0. It has been classified as critical. Affected is an unknown function of the file student/studentdashboard.php. The manipulation of the argument course leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Project Worlds Online Time Table Generator 1.0. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo student/studentdashboard.php. La manipulación del argumento \"course\" provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ydnd/cve/issues/10", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300678", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300678", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520503", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
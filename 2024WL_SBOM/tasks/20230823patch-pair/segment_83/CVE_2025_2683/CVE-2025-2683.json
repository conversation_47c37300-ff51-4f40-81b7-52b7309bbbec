{"cve_id": "CVE-2025-2683", "published_date": "2025-03-24T05:15:13.037", "last_modified_date": "2025-03-27T18:14:24.227", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Bank Locker Management System 1.0. This vulnerability affects unknown code of the file /profile.php. The manipulation of the argument mobilenumber leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Bank Locker Management System 1.0. Esta vulnerabilidad afecta al código desconocido del archivo /profile.php. La manipulación del argumento \"mobilenumber\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/12", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300700", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300700", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521452", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
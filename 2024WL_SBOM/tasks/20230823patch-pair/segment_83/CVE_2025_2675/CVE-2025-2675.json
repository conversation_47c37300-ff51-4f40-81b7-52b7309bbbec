{"cve_id": "CVE-2025-2675", "published_date": "2025-03-24T01:15:16.490", "last_modified_date": "2025-03-26T14:03:31.380", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Bank Locker Management System 1.0. Affected by this issue is some unknown functionality of the file /add-lockertype.php. The manipulation of the argument lockerprice leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul Bank Locker Management System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /add-lockertype.php. La manipulación del argumento lockerprice provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300692", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300692", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.521442", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}]}
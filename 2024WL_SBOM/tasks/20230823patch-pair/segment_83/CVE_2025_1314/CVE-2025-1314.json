{"cve_id": "CVE-2025-1314", "published_date": "2025-03-20T06:15:22.437", "last_modified_date": "2025-03-20T06:15:22.437", "descriptions": [{"lang": "en", "value": "The Custom Twitter Feeds – A Tweets Widget or X Feed Widget plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 2.2.5. This is due to missing or incorrect nonce validation on the ctf_clear_cache_admin() function. This makes it possible for unauthenticated attackers to reset the plugin's cache via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Custom Twitter Feeds – A Tweets Widget or X Feed Widget para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 2.2.5 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la función ctf_clear_cache_admin(). Esto permite que atacantes no autenticados restablezcan la caché del complemento mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/custom-twitter-feeds/trunk/custom-twitter-feed.php#L1014", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/custom-twitter-feeds/trunk/custom-twitter-feed.php#L810", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/custom-twitter-feeds/trunk/custom-twitter-feed.php#L833", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3254840/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/custom-twitter-feeds/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/28d47605-ecb8-42cc-901a-3cf07b946877?source=cve", "source": "<EMAIL>", "tags": []}]}
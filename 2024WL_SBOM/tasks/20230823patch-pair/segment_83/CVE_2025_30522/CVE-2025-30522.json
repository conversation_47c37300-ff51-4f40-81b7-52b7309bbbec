{"cve_id": "CVE-2025-30522", "published_date": "2025-03-24T14:15:20.397", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Damian Orzol Contact Form 7 Material Design allows Stored XSS. This issue affects Contact Form 7 Material Design: from n/a through 1.0.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Damian <PERSON> Contact Form 7 Material Design permite XSS almacenado. Este problema afecta a Material Design de Contact Form 7 desde n/d hasta la versión 1.0.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/cf7-material-design/vulnerability/wordpress-contact-form-7-material-design-plugin-1-0-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
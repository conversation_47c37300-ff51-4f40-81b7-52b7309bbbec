{"cve_id": "CVE-2025-29980", "published_date": "2025-03-20T19:15:38.080", "last_modified_date": "2025-03-20T20:15:33.233", "descriptions": [{"lang": "en", "value": "A SQL injection issue has been discovered in eTRAKiT.net release ********. Due to improper input validation, a remote unauthenticated attacker can run arbitrary commands as the current MS SQL server account. It is recommended that the CRM feature is turned off while on eTRAKiT.net release ********. eTRAKiT.Net is no longer supported, and users are recommended to migrate to the latest version of CentralSquare Community Development."}, {"lang": "es", "value": "Se ha detectado un problema de inyección SQL en la versión ******** de eTRAKiT.net. Debido a una validación de entrada incorrecta, un atacante remoto no autenticado puede ejecutar comandos arbitrarios utilizando la cuenta actual del servidor MS SQL. Se recomienda desactivar la función CRM en la versión ******** de eTRAKiT.net. eTRAKiT.Net ya no es compatible, y se recomienda a los usuarios migrar a la última versión de CentralSquare Community Development."}], "references": [{"url": "https://github.com/cisagov/CSAF/pull/182/files#diff-53861466371a59578b21f5e4b4b6be7b2a6267c5d0fe81eda2a849bf6915ed8d", "source": "9119a7d8-5eab-497f-8521-727c672e3725", "tags": []}, {"url": "https://raw.githubusercontent.com/cisagov/CSAF/develop/csaf_files/IT/white/2025/va-25-079-01.json", "source": "9119a7d8-5eab-497f-8521-727c672e3725", "tags": []}]}
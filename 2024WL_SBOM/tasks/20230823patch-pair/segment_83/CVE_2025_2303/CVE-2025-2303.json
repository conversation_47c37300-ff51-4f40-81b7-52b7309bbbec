{"cve_id": "CVE-2025-2303", "published_date": "2025-03-22T07:15:24.613", "last_modified_date": "2025-03-22T07:15:24.613", "descriptions": [{"lang": "en", "value": "The Block Logic – Full Gutenberg Block Display Control plugin for WordPress is vulnerable to Remote Code Execution in all versions up to, and including, 1.0.8 via the block_logic_check_logic function. This is due to the unsafe evaluation of user-controlled input. This makes it possible for authenticated attackers, with Contributor-level access and above, to execute code on the server."}, {"lang": "es", "value": "El complemento Block Logic – Full Gutenberg Block Display Control para WordPress es vulnerable a la ejecución remota de código en todas las versiones hasta la 1.0.8 incluida, a través de la función block_logic_check_logic. Esto se debe a la evaluación insegura de la entrada controlada por el usuario. Esto permite que atacantes autenticados, con acceso de colaborador o superior, ejecuten código en el servidor."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/block-logic/tags/1.0.8/block-logic.php#L127", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4a76f851-3f4e-4457-a33c-eede51c4b4d1?source=cve", "source": "<EMAIL>", "tags": []}]}
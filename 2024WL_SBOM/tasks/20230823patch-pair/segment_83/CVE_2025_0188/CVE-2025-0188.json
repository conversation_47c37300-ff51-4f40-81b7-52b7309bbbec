{"cve_id": "CVE-2025-0188", "published_date": "2025-03-20T10:15:51.533", "last_modified_date": "2025-07-09T18:06:57.527", "descriptions": [{"lang": "en", "value": "A Server-Side Request Forgery (SSRF) vulnerability was discovered in gaizhenbiao/chuanhuchatgpt version 20240914. The vulnerability allows an attacker to construct a response link by saving the response in a folder named after the SHA-1 hash of the target URL. This enables the attacker to access the response directly, potentially leading to unauthorized access to internal systems, data theft, service disruption, or further attacks such as port scanning and accessing metadata endpoints."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de Server-Side Request Forgery (SSRF) en gaizhenbiao/chuanhuchatgpt versión 20240914. Esta vulnerabilidad permite a un atacante crear un enlace de respuesta guardándola en una carpeta cuyo nombre coincide con el hash SHA-1 de la URL de destino. Esto permite al atacante acceder directamente a la respuesta, lo que podría provocar acceso no autorizado a sistemas internos, robo de datos, interrupción del servicio o ataques adicionales, como escaneo de puertos y acceso a puntos finales de metadatos."}], "references": [{"url": "https://huntr.com/bounties/879d2470-eca5-49c0-b3d1-57469cfff412", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
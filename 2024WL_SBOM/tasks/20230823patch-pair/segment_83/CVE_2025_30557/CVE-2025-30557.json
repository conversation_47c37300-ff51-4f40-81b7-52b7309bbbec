{"cve_id": "CVE-2025-30557", "published_date": "2025-03-24T14:15:24.973", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in odihost Easy 301 Redirects allows Cross Site Request Forgery. This issue affects Easy 301 Redirects: from n/a through 1.33."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en odihost Easy 301 Redirects permite Cross-Site Request Forgery. Este problema afecta a las redirecciones Easy 301 desde la versión n/d hasta la 1.33."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/odihost-easy-redirect-301/vulnerability/wordpress-easy-301-redirects-plugin-1-33-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1451", "published_date": "2025-03-20T10:15:53.777", "last_modified_date": "2025-03-27T15:40:03.640", "descriptions": [{"lang": "en", "value": "A vulnerability in parisneo/lollms-webui v13 arises from the server's handling of multipart boundaries in file uploads. The server does not limit or validate the length of the boundary or the characters appended to it, allowing an attacker to craft requests with excessively long boundaries, leading to resource exhaustion and eventual denial of service (DoS). Despite an attempted patch in commit 483431bb, which blocked hyphen characters from being appended to the multipart boundary, the fix is insufficient. The server remains vulnerable if other characters (e.g., '4', 'a') are used instead of hyphens. This allows attackers to exploit the vulnerability using different characters, causing resource exhaustion and service unavailability."}, {"lang": "es", "value": "Una vulnerabilidad en parisneo/lollms-webui v13 surge de la gestión de los límites multiparte por parte del servidor al subir archivos. El servidor no limita ni valida la longitud del límite ni los caracteres añadidos, lo que permite a un atacante manipular solicitudes con límites excesivamente largos, lo que provoca el agotamiento de recursos y, finalmente, una denegación de servicio (DoS). A pesar de un parche en el commit 483431bb, que impedía añadir guiones al límite multiparte, la solución es insuficiente. El servidor sigue siendo vulnerable si se utilizan otros caracteres (p. ej., '4', 'a') en lugar de guiones. Esto permite a los atacantes explotar la vulnerabilidad utilizando caracteres diferentes, lo que provoca el agotamiento de recursos y la indisponibilidad del servicio."}], "references": [{"url": "https://huntr.com/bounties/63f5aea4-953b-4b38-9f10-3afe425be1d4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
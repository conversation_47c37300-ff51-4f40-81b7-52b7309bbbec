{"cve_id": "CVE-2025-1796", "published_date": "2025-03-20T10:15:54.157", "last_modified_date": "2025-07-16T15:15:54.623", "descriptions": [{"lang": "en", "value": "A vulnerability in langgenius/dify v0.10.1 allows an attacker to take over any account, including administrator accounts, by exploiting a weak pseudo-random number generator (PRNG) used for generating password reset codes. The application uses `random.randint` for this purpose, which is not suitable for cryptographic use and can be cracked. An attacker with access to workflow tools can extract the PRNG output and predict future password reset codes, leading to a complete compromise of the application."}, {"lang": "es", "value": "Una vulnerabilidad en langgenius/dify v0.10.1 permite a un atacante tomar el control de cualquier cuenta, incluidas las de administrador, explotando un generador de números pseudoaleatorios (PRNG) débil, utilizado para generar códigos de restablecimiento de contraseña. La aplicación utiliza `random.randint` para este propósito, que no es apto para uso criptográfico y puede ser descifrado. Un atacante con acceso a herramientas de flujo de trabajo puede extraer la salida del PRNG y predecir futuros códigos de restablecimiento de contraseña, lo que compromete completamente la aplicación."}], "references": [{"url": "https://huntr.com/bounties/a60f3039-5394-4e22-8de7-a7da9c6a6e00", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
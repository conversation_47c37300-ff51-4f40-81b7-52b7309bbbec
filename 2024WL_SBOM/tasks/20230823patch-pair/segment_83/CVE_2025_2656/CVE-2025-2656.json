{"cve_id": "CVE-2025-2656", "published_date": "2025-03-23T17:15:29.867", "last_modified_date": "2025-05-13T20:27:57.163", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Zoo Management System 2.1. Affected is an unknown function of the file /admin/login.php. The manipulation of the argument Username leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Zoo Management System 2.1. Se ve afectada una función desconocida del archivo /admin/login.php. La manipulación del argumento \"Username\" provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ydnd/cve/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300672", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300672", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520234", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
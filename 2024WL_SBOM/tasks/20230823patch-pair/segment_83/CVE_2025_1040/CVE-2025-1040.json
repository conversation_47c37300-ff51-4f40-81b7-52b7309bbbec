{"cve_id": "CVE-2025-1040", "published_date": "2025-03-20T10:15:53.653", "last_modified_date": "2025-04-01T20:19:55.317", "descriptions": [{"lang": "en", "value": "AutoGPT versions 0.3.4 and earlier are vulnerable to a Server-Side Template Injection (SSTI) that could lead to Remote Code Execution (RCE). The vulnerability arises from the improper handling of user-supplied format strings in the `AgentOutputBlock` implementation, where malicious input is passed to the Jinja2 templating engine without adequate security measures. Attackers can exploit this flaw to execute arbitrary commands on the host system. The issue is fixed in version 0.4.0."}, {"lang": "es", "value": "Las versiones 0.3.4 y anteriores de AutoGPT son vulnerables a Server-Side Template Injection (SSTI) que podría provocar una Ejecución Remota de Código (RCE). Esta vulnerabilidad se debe al manejo inadecuado de las cadenas de formato proporcionadas por el usuario en la implementación `AgentOutputBlock`, donde se pasa información maliciosa al motor de plantillas Jinja2 sin las medidas de seguridad adecuadas. Los atacantes pueden explotar esta vulnerabilidad para ejecutar comandos arbitrarios en el sistema host. El problema se ha corregido en la versión 0.4.0."}], "references": [{"url": "https://github.com/significant-gravitas/autogpt/commit/6dba31e0215549604bdcc1aed24e3a1714e75ee2", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://huntr.com/bounties/b74ef75f-61d5-4422-ab15-9550c8b4f185", "source": "<EMAIL>", "tags": ["Exploit"]}]}
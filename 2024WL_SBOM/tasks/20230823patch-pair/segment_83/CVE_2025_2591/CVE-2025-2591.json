{"cve_id": "CVE-2025-2591", "published_date": "2025-03-21T14:15:16.853", "last_modified_date": "2025-07-17T21:53:55.683", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in Open Asset Import Library Assimp 5.4.3. This vulnerability affects the function MDLImporter::InternReadFile_Quake1 of the file code/AssetLib/MDL/MDLLoader.cpp. The manipulation of the argument skinwidth/skinheight leads to divide by zero. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. The patch is identified as ab66a1674fcfac87aaba4c8b900b315ebc3e7dbd. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en Open Asset Import Library Assimp 5.4.3. Esta vulnerabilidad afecta la función MDLImporter::InternReadFile_Quake1 del archivo code/AssetLib/MDL/MDLLoader.cpp. La manipulación del argumento Skinwidth/SkinHeight conduce a dividir por cero. El ataque se puede iniciar de forma remota. Se ha hecho público el exploit y puede que sea utilizado . El parche se identifica como AB66A1674FCFAC87AABA4C8B900B315EBC3E7DBD. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6009", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6009#issue-2877367021", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/pull/6047", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/assimp/assimp/pull/6047/commits/ab66a1674fcfac87aaba4c8b900b315ebc3e7dbd", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://vuldb.com/?ctiid.300574", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300574", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517781", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
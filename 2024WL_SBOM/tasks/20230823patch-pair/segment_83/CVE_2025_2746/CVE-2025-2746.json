{"cve_id": "CVE-2025-2746", "published_date": "2025-03-24T19:15:51.460", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "An authentication bypass vulnerability in Kentico Xperience allows authentication bypass via the Staging Sync Server password handling of empty SHA1 usernames in digest authentication. Authentication bypass allows an attacker to control administrative objects.This issue affects Xperience through 13.0.172."}, {"lang": "es", "value": "Una vulnerabilidad de omisión de autenticación en Kentico Xperience permite omitir la autenticación mediante la gestión de contraseñas del servidor de sincronización de pruebas de nombres de usuario SHA1 vacíos en la autenticación implícita. Esta omisión de autenticación permite a un atacante controlar objetos administrativos. Este problema afecta a Xperience hasta la versión 13.0.172."}], "references": [{"url": "https://devnet.kentico.com/download/hotfixes", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/watchtowrlabs/kentico-xperience13-AuthBypass-wt-2025-0011", "source": "<EMAIL>", "tags": []}, {"url": "https://labs.watchtowr.com/bypassing-authentication-like-its-the-90s-pre-auth-rce-chain-s-in-kentico-xperience-cms/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-29927", "published_date": "2025-03-21T15:15:42.660", "last_modified_date": "2025-04-08T14:15:33.973", "descriptions": [{"lang": "en", "value": "Next.js is a React framework for building full-stack web applications. Starting in version 1.11.4 and prior to versions 12.3.5, 13.5.9, 14.2.25, and 15.2.3, it is possible to bypass authorization checks within a Next.js application, if the authorization check occurs in middleware. If patching to a safe version is infeasible, it is recommend that you prevent external user requests which contain the x-middleware-subrequest header from reaching your Next.js application. This vulnerability is fixed in 12.3.5, 13.5.9, 14.2.25, and 15.2.3."}, {"lang": "es", "value": "Next.js es un framework de React para crear aplicaciones web full-stack. En versiones anteriores a la 14.2.25 y la 15.2.3, era posible omitir las comprobaciones de autorización dentro de una aplicación Next.js si esta se realizaba en middleware. Si no es posible aplicar una versión segura, se recomienda evitar que las solicitudes de usuarios externos que contengan el encabezado \"x-middleware-subrequest\" lleguen a la aplicación Next.js. Esta vulnerabilidad se corrigió en las versiones 14.2.25 y 15.2.3."}], "references": [{"url": "https://github.com/vercel/next.js/commit/52a078da3884efe6501613c7834a3d02a91676d2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vercel/next.js/commit/5fd3ae8f8542677c6294f32d18022731eab6fe48", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vercel/next.js/releases/tag/v12.3.5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vercel/next.js/releases/tag/v13.5.9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vercel/next.js/security/advisories/GHSA-f82v-jwr5-mffw", "source": "<EMAIL>", "tags": []}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/23/3", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/23/4", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20250328-0002/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
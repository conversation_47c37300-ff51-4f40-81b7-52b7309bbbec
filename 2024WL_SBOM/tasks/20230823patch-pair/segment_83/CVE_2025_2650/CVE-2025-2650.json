{"cve_id": "CVE-2025-2650", "published_date": "2025-03-23T14:15:13.797", "last_modified_date": "2025-03-27T16:29:06.943", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in PHPGurukul Medical Card Generation System 1.0. This issue affects some unknown processing of the file /download-medical-cards.php. The manipulation of the argument searchdata leads to cross site scripting. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en PHPGurukul Medical Card Generation System 1.0. Este problema afecta a un procesamiento desconocido del archivo /download-medical-cards.php. La manipulación del argumento \"searchdata\" provoca ataques de cross site scripting. El ataque podría iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/13", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300665", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300665", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519781", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-2252", "published_date": "2025-03-25T07:15:38.337", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The Easy Digital Downloads – eCommerce Payments and Subscriptions made easy plugin for WordPress is vulnerable to Sensitive Information Exposure in all versions up to, and including, ******* via the edd_ajax_get_download_title() function. This makes it possible for unauthenticated attackers to extract private post titles of downloads. The impact here is minimal."}, {"lang": "es", "value": "El complemento Easy Digital Downloads – eCommerce Payments and Subscriptions made easy para WordPress es vulnerable a la exposición de información confidencial en todas las versiones hasta la ******* incluida, a través de la función edd_ajax_get_download_title(). Esto permite a atacantes no autenticados extraer títulos privados de las publicaciones de las descargas. El impacto en este caso es mínimo."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/easy-digital-downloads/tags/*******/includes/ajax-functions.php#L459", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/easy-digital-downloads/tags/*******/includes/ajax-functions.php#L466", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3257409/easy-digital-downloads/trunk/includes/ajax-functions.php?contextall=1", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3257409/easy-digital-downloads/trunk/includes/ajax-functions.php?old=3226442&old_path=easy-digital-downloads%2Ftrunk%2Fincludes%2Fajax-functions.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9e0e3b81-55fe-46b2-bae1-d7321d74c485?source=cve", "source": "<EMAIL>", "tags": []}]}
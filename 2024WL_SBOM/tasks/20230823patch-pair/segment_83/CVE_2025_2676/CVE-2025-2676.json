{"cve_id": "CVE-2025-2676", "published_date": "2025-03-24T01:15:17.177", "last_modified_date": "2025-06-04T16:05:00.420", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Bank Locker Management System 1.0. This affects an unknown part of the file /add-subadmin.php. The manipulation of the argument sadminusername leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Bank Locker Management System 1.0. Esta afecta a una parte desconocida del archivo /add-subadmin.php. La manipulación del argumento sadminusername provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300693", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300693", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521443", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-2584", "published_date": "2025-03-21T08:15:11.273", "last_modified_date": "2025-03-24T14:02:56.053", "descriptions": [{"lang": "en", "value": "A vulnerability was found in WebAssembly wabt 1.0.36. It has been declared as critical. This vulnerability affects the function BinaryReaderInterp::GetReturnCallDropKeepCount of the file wabt/src/interp/binary-reader-interp.cc. The manipulation leads to heap-based buffer overflow. The attack can be initiated remotely. The complexity of an attack is rather high. The exploitation appears to be difficult. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en WebAssembly wabt 1.0.36. Se ha declarado crítica. Esta vulnerabilidad afecta a la función BinaryReaderInterp::GetReturnCallDropKeepCount del archivo wabt/src/interp/binary-reader-interp.cc. La manipulación provoca un desbordamiento del búfer basado en el montón. El ataque puede iniciarse remotamente. Es un ataque de complejidad bastante alta. Parece difícil de explotar. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/WebAssembly/wabt/issues/2557", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/WebAssembly/wabt/issues/2557#issue-2900405517", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300544", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300544", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.515406", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
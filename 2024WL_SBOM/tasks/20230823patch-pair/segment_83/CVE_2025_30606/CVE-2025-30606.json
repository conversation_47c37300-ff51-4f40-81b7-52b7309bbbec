{"cve_id": "CVE-2025-30606", "published_date": "2025-03-24T14:15:33.247", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Logan Carlile Easy Page Transition allows Stored XSS. This issue affects Easy Page Transition: from n/a through 1.0.1."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Logan Carlile Easy Page Transition permite XSS almacenado. Este problema afecta a Easy Page Transition desde n/d hasta la versión 1.0.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/easy-page-transition/vulnerability/wordpress-easy-page-transition-plugin-1-0-1-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
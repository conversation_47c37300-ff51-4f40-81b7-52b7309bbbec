{"cve_id": "CVE-2024-9919", "published_date": "2025-03-20T10:15:50.660", "last_modified_date": "2025-07-09T14:17:49.673", "descriptions": [{"lang": "en", "value": "A missing authentication check in the uninstall endpoint of parisneo/lollms-webui V13 allows attackers to perform unauthorized directory deletions. The /uninstall/{app_name} API endpoint does not call the check_access() function to verify the client_id, enabling attackers to delete directories without proper authentication."}, {"lang": "es", "value": "Una comprobación de autenticación faltante en el endpoint de desinstalación de parisneo/lollms-webui V13 permite a los atacantes eliminar directorios sin autorización. El endpoint de la API /uninstall/{app_name} no llama a la función check_access() para verificar el client_id, lo que permite a los atacantes eliminar directorios sin la autenticación adecuada."}], "references": [{"url": "https://huntr.com/bounties/5c00f56b-32a8-4e26-a4e3-de64f139da6b", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}]}
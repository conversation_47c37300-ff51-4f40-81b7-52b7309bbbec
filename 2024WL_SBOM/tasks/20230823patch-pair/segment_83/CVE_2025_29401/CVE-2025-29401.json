{"cve_id": "CVE-2025-29401", "published_date": "2025-03-19T16:15:31.827", "last_modified_date": "2025-06-16T18:49:10.790", "descriptions": [{"lang": "en", "value": "An arbitrary file upload vulnerability in the component /views/plugin.php of emlog pro v2.5.7 allows attackers to execute arbitrary code via uploading a crafted PHP file."}, {"lang": "es", "value": "Una vulnerabilidad de carga de archivos arbitrarios en el componente /views/plugin.php de emlog pro v2.5.7 permite a los atacantes ejecutar código arbitrario mediante la carga de un archivo PHP manipulado."}], "references": [{"url": "https://github.com/bGl1o/emlogpro/blob/main/emlog%20pro2.5.7-getshell.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
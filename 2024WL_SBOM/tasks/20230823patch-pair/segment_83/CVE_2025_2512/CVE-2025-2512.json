{"cve_id": "CVE-2025-2512", "published_date": "2025-03-19T12:15:14.463", "last_modified_date": "2025-03-19T12:15:14.463", "descriptions": [{"lang": "en", "value": "The File Away plugin for WordPress is vulnerable to arbitrary file uploads due to a missing capability check and missing file type validation in the upload() function in all versions up to, and including, *******.1. This makes it possible for unauthenticated attackers to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento File Away para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de una comprobación de capacidad y de una validación del tipo de archivo en la función upload() en todas las versiones hasta la *******.1 incluida. Esto permite que atacantes no autenticados carguen archivos arbitrarios en el servidor del sitio afectado, lo que podría posibilitar la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/file-away/trunk/lib/cls/class.fileaway_management.php#L1094", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/file-away/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9a93313d-a5d7-4109-93c5-b2da26e7a486?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2708", "published_date": "2025-03-24T20:15:18.950", "last_modified_date": "2025-07-09T00:42:06.787", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in zhijiantianya ruoyi-vue-pro 2.4.1. This affects an unknown part of the file /admin-api/infra/file/upload of the component Backend File Upload Interface. The manipulation of the argument path leads to path traversal. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en zhijiantianya ruoyi-vue-pro 2.4.1. Esta afecta a una parte desconocida del archivo /admin-api/infra/file/upload del componente Interfaz de Carga de Archivos del Backend. La manipulación de la ruta del argumento provoca un path traversal. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/ruoyi-vue-pro.md#4file-path-traversal-back-end", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300729", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300729", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517030", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
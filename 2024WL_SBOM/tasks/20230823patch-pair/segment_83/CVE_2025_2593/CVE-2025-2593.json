{"cve_id": "CVE-2025-2593", "published_date": "2025-03-21T15:15:42.943", "last_modified_date": "2025-04-01T20:23:07.257", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in FastCMS up to 0.1.5 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /api/client/article/list. The manipulation of the argument orderBy leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en FastCMS hasta la versión 0.1.5, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /api/client/article/list. La manipulación del argumento orderBy provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/IceFoxH/VULN/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300577", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300577", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517926", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
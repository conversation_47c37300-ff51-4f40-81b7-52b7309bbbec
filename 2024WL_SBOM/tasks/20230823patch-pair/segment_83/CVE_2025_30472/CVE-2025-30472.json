{"cve_id": "CVE-2025-30472", "published_date": "2025-03-22T02:15:16.620", "last_modified_date": "2025-04-01T20:28:02.283", "descriptions": [{"lang": "en", "value": "Corosync through 3.1.9, if encryption is disabled or the attacker knows the encryption key, has a stack-based buffer overflow in orf_token_endian_convert in exec/totemsrp.c via a large UDP packet."}, {"lang": "es", "value": "Desde Corosync hasta la versión 3.1.9, si el cifrado está deshabilitado o el atacante conoce la clave de cifrado, se produce un desbordamiento de búfer basado en pila en orf_token_endian_convert en exec/totemsrp.c a través de un paquete UDP grande."}], "references": [{"url": "https://corosync.org", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/corosync/corosync/blob/73ba225cc48ebb1903897c792065cb5e876613b0/exec/totemsrp.c#L4677", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/corosync/corosync/issues/778", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}
{"cve_id": "CVE-2025-27888", "published_date": "2025-03-20T12:15:14.563", "last_modified_date": "2025-07-14T12:58:48.687", "descriptions": [{"lang": "en", "value": "Severity: medium (5.8) / important\n\nServer-Side Request Forgery (SSRF), Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'), URL Redirection to Untrusted Site ('Open Redirect') vulnerability in Apache Druid.\n\nThis issue affects all previous Druid versions.\n\n\nWhen using the Druid management proxy, a request that has a specially crafted URL could be used to redirect the request to an arbitrary server instead. This has the potential for XSS or XSRF. The user is required to be authenticated for this exploit. The management proxy is enabled in Druid's out-of-box configuration. It may be disabled to mitigate this vulnerability. If the management proxy is disabled, some web console features will not work properly, but core functionality is unaffected.\n\n\nUsers are recommended to upgrade to Druid 31.0.2 or Druid 32.0.1, which fixes the issue."}, {"lang": "es", "value": "Gravedad: media (5.8) / importante Vulnerabilidad de  Server-Side Request Forgery (SSRF), neutralización incorrecta de la entrada durante la generación de páginas web (\"Cross-site Scripting\") y redirección de URL a un sitio no confiable (\"Open Redirect\") en Apache Druid. Este problema afecta a todas las versiones anteriores de Druid. Al usar el proxy de administración de Druid, una solicitud con una URL especialmente manipulada podría usarse para redirigir la solicitud a un servidor arbitrario. Esto tiene el potencial de generar XSS o XSRF. El usuario debe estar autenticado para esta vulnerabilidad. El proxy de administración está habilitado en la configuración predeterminada de Druid. Se puede deshabilitar para mitigar esta vulnerabilidad. Si se deshabilita el proxy de administración, algunas funciones de la consola web no funcionarán correctamente, pero la funcionalidad principal no se verá afectada. Se recomienda a los usuarios actualizar a Druid 31.0.2 o Druid 32.0.1, que soluciona el problema."}], "references": [{"url": "https://lists.apache.org/thread/c0qo989pwtrqkjv6xfr0c30dnjq8vf39", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory", "Issue Tracking"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/19/7", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-0628", "published_date": "2025-03-20T10:15:53.407", "last_modified_date": "2025-03-20T10:15:53.407", "descriptions": [{"lang": "en", "value": "An improper authorization vulnerability exists in the main-latest version of BerriAI/litellm. When a user with the role 'internal_user_viewer' logs into the application, they are provided with an overly privileged API key. This key can be used to access all the admin functionality of the application, including endpoints such as '/users/list' and '/users/get_users'. This vulnerability allows for privilege escalation within the application, enabling any account to become a PROXY ADMIN."}, {"lang": "es", "value": "Existe una vulnerabilidad de autorización indebida en la última versión principal de BerriAI/litellm. Cuando un usuario con el rol \"internal_user_viewer\" inicia sesión en la aplicación, se le proporciona una clave API con privilegios excesivos. Esta clave permite acceder a todas las funciones de administración de la aplicación, incluyendo endpoints como \"/users/list\" y \"/users/get_users\". Esta vulnerabilidad permite la escalada de privilegios dentro de la aplicación, lo que permite que cualquier cuenta se convierta en administrador proxy."}], "references": [{"url": "https://github.com/berriai/litellm/commit/566d9354aab4215091b2e51ad0333e948125fa1b", "source": "<EMAIL>", "tags": []}, {"url": "https://huntr.com/bounties/6c0e2f75-2d03-42f9-9530-e16a973317fc", "source": "<EMAIL>", "tags": []}]}
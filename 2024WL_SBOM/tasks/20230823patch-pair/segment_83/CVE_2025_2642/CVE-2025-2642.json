{"cve_id": "CVE-2025-2642", "published_date": "2025-03-23T07:15:12.103", "last_modified_date": "2025-04-02T13:48:31.510", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Art Gallery Management System 1.0. This affects an unknown part of the file /admin/edit-art-product-detail.php?editid=2. The manipulation of the argument editide/sprice/description leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Art Gallery Management System 1.0. Esta afecta a una parte desconocida del archivo /admin/edit-art-product-detail.php?editid=2. La manipulación del argumento editide/sprice/description provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300657", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300657", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.519769", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
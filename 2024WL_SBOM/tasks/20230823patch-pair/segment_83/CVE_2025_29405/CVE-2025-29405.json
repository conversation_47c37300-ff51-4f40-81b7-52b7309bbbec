{"cve_id": "CVE-2025-29405", "published_date": "2025-03-19T18:15:25.023", "last_modified_date": "2025-06-12T19:35:35.770", "descriptions": [{"lang": "en", "value": "An arbitrary file upload vulnerability in the component /admin/template.php of emlog pro 2.5.0 and pro 2.5.* allows attackers to execute arbitrary code via uploading a crafted PHP file."}, {"lang": "es", "value": "Una vulnerabilidad de carga de archivos arbitrarios en el componente /admin/template.php de emlog pro 2.5.0 y pro 2.5.* permite a los atacantes ejecutar código arbitrario mediante la carga de un archivo PHP manipulado."}], "references": [{"url": "https://gist.github.com/bGl1o/19a141ee6e899884fa85f3a52898bcc6", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/bGl1o/emlogpro/blob/main/emlog%20pro2.5.7-getshell-2.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
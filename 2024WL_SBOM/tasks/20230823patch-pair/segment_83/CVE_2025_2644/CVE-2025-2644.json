{"cve_id": "CVE-2025-2644", "published_date": "2025-03-23T08:15:12.743", "last_modified_date": "2025-04-02T13:30:34.347", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /admin/add-art-product.php. The manipulation of the argument arttype leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Art Gallery Management System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/add-art-product.php. La manipulación del argumento arttype provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300659", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300659", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.519773", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
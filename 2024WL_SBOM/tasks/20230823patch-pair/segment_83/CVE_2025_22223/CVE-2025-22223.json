{"cve_id": "CVE-2025-22223", "published_date": "2025-03-24T18:15:22.673", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Spring Security 6.4.0 - 6.4.3 may not correctly locate method security annotations on parameterized types or methods. This may cause an authorization bypass. \n\nYou are not affected if you are not using @EnableMethodSecurity, or\nyou do not have method security annotations on parameterized types or methods, or all method security annotations are attached to target methods"}, {"lang": "es", "value": "Es posible que Spring Security 6.4.0 - 6.4.3 no ubique correctamente las anotaciones de seguridad de métodos en tipos o métodos parametrizados. Esto puede provocar una omisión de autorización. Esto no se ve afectado si no utiliza @EnableMethodSecurity, si no tiene anotaciones de seguridad de métodos en tipos o métodos parametrizados, o si todas las anotaciones de seguridad de métodos están asociadas a los métodos de destino."}], "references": [{"url": "https://spring.io/security/cve-2025-22223", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2648", "published_date": "2025-03-23T12:15:12.110", "last_modified_date": "2025-03-27T16:27:24.183", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Art Gallery Management System 1.0. This affects an unknown part of the file /admin/view-enquiry-detail.php. The manipulation of the argument viewid leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Art Gallery Management System 1.0. Esta afecta a una parte desconocida del archivo /admin/view-enquiry-detail.php. La manipulación del argumento viewid provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/11", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300663", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300663", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519779", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
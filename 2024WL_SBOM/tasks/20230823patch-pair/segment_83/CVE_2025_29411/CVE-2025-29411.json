{"cve_id": "CVE-2025-29411", "published_date": "2025-03-20T15:15:46.050", "last_modified_date": "2025-03-28T19:38:32.533", "descriptions": [{"lang": "en", "value": "An arbitrary file upload vulnerability in the Client Profile Update section of Mart Developers iBanking v2.0.0 allows attackers to execute arbitrary code via uploading a crafted PHP file."}, {"lang": "es", "value": "Una vulnerabilidad de carga de archivos arbitrarios en la sección actualización de perfil de cliente de Mart Developers iBanking v2.0.0 permite a los atacantes ejecutar código arbitrario mediante la carga de un archivo PHP manipulado."}], "references": [{"url": "https://github.com/MartMbithi/iBanking/issues/12", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://www.simonjuguna.com/cve-2025-29411-authenticated-remote-code-execution-rce-via-arbitrary-file-upload/", "source": "<EMAIL>", "tags": ["Exploit"]}]}
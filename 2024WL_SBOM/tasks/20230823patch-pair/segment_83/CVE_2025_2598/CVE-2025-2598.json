{"cve_id": "CVE-2025-2598", "published_date": "2025-03-21T15:15:43.120", "last_modified_date": "2025-03-21T17:15:40.090", "descriptions": [{"lang": "en", "value": "When the AWS Cloud Development Kit (AWS CDK) Command Line Interface (AWS CDK CLI) is used with a credential plugin which returns an expiration property with the retrieved AWS credentials, the credentials are printed to the console output. To mitigate this issue, users should upgrade to version 2.178.2 or later and ensure any forked or derivative code is patched to incorporate the new fixes."}, {"lang": "es", "value": "Cuando se utiliza la interfaz de línea de comandos (CLI) de AWS CDK de AWS Cloud Development Kit (AWS CDK) con un complemento de credenciales que devuelve una propiedad de expiración con las credenciales de AWS recuperadas, estas se imprimen en la salida de la consola. Para mitigar este problema, los usuarios deben actualizar a la versión 2.178.2 o posterior y asegurarse de que cualquier código bifurcado o derivado esté parcheado para incorporar las nuevas correcciones."}], "references": [{"url": "https://aws.amazon.com/security/security-bulletins/AWS-2025-005/", "source": "ff89ba41-3aa1-4d27-914a-91399e9639e5", "tags": []}, {"url": "https://github.com/aws/aws-cdk/security/advisories/GHSA-v63m-x9r9-8gqp", "source": "ff89ba41-3aa1-4d27-914a-91399e9639e5", "tags": []}]}
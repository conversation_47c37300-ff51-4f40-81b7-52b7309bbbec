{"cve_id": "CVE-2025-2686", "published_date": "2025-03-24T06:15:13.127", "last_modified_date": "2025-03-24T06:15:13.127", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in mingyue<PERSON>su 明月复苏 tushuguanlixitong 图书管理系统 up to d4836f6b49cd0ac79a4021b15ce99ff7229d4694 and classified as critical. Affected by this vulnerability is the function doFilter of the file /admin/ of the component Backend. The manipulation of the argument Request leads to improper access controls. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en mingyuefusu ???? tushuguanlixitong ?????? hasta d4836f6b49cd0ac79a4021b15ce99ff7229d4694, clasificada como crítica. Esta vulnerabilidad afecta a la función doFilter del archivo /admin/ del componente Backend. La manipulación del argumento Request genera controles de acceso inadecuados. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://gitee.com/mingyuefusu/tushuguanlixitong/issues/IBTS25", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.300703", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.300703", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.521449", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-30549", "published_date": "2025-03-24T14:15:23.970", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Yummly Yummly Rich Recipes allows Cross Site Request Forgery. This issue affects Yummly Rich Recipes: from n/a through 4.2."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Yummly Rich Recipes permite Cross-Site Request Forgery. Este problema afecta a Yummly Rich Recipes desde la versión n/d hasta la 4.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/yummly-rich-recipes/vulnerability/wordpress-yummly-rich-recipes-plugin-4-2-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
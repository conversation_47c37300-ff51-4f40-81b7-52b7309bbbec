{"cve_id": "CVE-2025-27781", "published_date": "2025-03-19T21:15:40.117", "last_modified_date": "2025-03-19T21:15:40.117", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.8-bugfix and prior are vulnerable to unsafe deserialization in inference.py. `model_file` in inference.py as well as `model_file` in tts.py take user-supplied input (e.g. a path to a model) and pass that value to the `change_choices` and later to `get_speakers_id` function, which loads that model with `torch.load` in inference.py (line 326 in 3.2.8-bugfix), which is vulnerable to unsafe deserialization. The issue can lead to remote code execution. A patch is available on the `main` branch of the repository."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.8 corrección de errores y anteriores son vulnerables a una deserialización insegura en inference.py. Tanto `model_file` en inference.py como `model_file` en tts.py toman la entrada proporcionada por el usuario (por ejemplo, la ruta a un modelo) y pasan ese valor a la función `change_choices` y, posteriormente, a la función `get_speakers_id`, que carga dicho modelo con `torch.load` en inference.py (línea 326 en 3.2.8-corrección de errores), que es vulnerable a una deserialización insegura. Este problema puede provocar la ejecución remota de código. Hay un parche disponible en la rama `main` del repositorio."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/inference/inference.py#L325", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/inference/inference.py#L338-L345", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/tts/tts.py#L50-L57", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/commit/eb21d9dd349a6ae1a28c440b30d306eafba65097", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
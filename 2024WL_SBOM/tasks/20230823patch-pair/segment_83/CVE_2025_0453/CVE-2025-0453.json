{"cve_id": "CVE-2025-0453", "published_date": "2025-03-20T10:15:53.017", "last_modified_date": "2025-04-02T16:10:48.930", "descriptions": [{"lang": "en", "value": "In mlflow/mlflow version 2.17.2, the `/graphql` endpoint is vulnerable to a denial of service attack. An attacker can create large batches of queries that repeatedly request all runs from a given experiment. This can tie up all the workers allocated by MLFlow, rendering the application unable to respond to other requests. This vulnerability is due to uncontrolled resource consumption."}, {"lang": "es", "value": "En mlflow/mlflow versión 2.17.2, el endpoint `/graphql` es vulnerable a un ataque de denegación de servicio. Un atacante puede crear grandes lotes de consultas que solicitan repetidamente todas las ejecuciones de un experimento determinado. Esto puede saturar todos los trabajadores asignados por MLFlow, impidiendo que la aplicación responda a otras solicitudes. Esta vulnerabilidad se debe al consumo descontrolado de recursos."}], "references": [{"url": "https://huntr.com/bounties/788327ec-714a-4d5c-83aa-8df04dd7612b", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
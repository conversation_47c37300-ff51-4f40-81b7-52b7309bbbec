{"cve_id": "CVE-2025-2673", "published_date": "2025-03-24T00:15:12.467", "last_modified_date": "2025-05-14T16:37:16.993", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic has been found in code-projects Payroll Management System 1.0. Affected is an unknown function of the file /home_employee.php. The manipulation of the argument division leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como problemática en code-projects Payroll Management System 1.0. Se ve afectada una función desconocida del archivo /home_employee.php. La manipulación de la división de argumentos provoca ataques de cross site scripting. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/FoLaJJ/cve/blob/main/xsscve.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300690", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300690", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521244", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
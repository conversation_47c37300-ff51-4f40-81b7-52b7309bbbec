{"cve_id": "CVE-2025-2643", "published_date": "2025-03-23T08:15:11.860", "last_modified_date": "2025-04-02T13:42:29.387", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Art Gallery Management System 1.0 and classified as critical. This vulnerability affects unknown code of the file /admin/edit-art-type-detail.php?editid=1. The manipulation of the argument arttype leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Art Gallery Management System 1.0, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/edit-art-type-detail.php?editid=1. La manipulación del argumento arttype provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300658", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300658", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.519772", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
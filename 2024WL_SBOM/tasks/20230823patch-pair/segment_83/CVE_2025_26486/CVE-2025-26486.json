{"cve_id": "CVE-2025-26486", "published_date": "2025-03-19T16:15:31.457", "last_modified_date": "2025-07-02T15:15:25.807", "descriptions": [{"lang": "en", "value": "Broken or Risky Cryptographic Algorithm, Use of Password Hash \nWith Insufficient Computational Effort, Use of Weak Hash, Use of a \nOne-Way Hash with a Predictable Salt vulnerabilities in Beta80 \"Life 1st Identity Manager\"\nenable an attacker with access to\npassword hashes\nto bruteforce user passwords or find a collision to ultimately while attempting to gain access to a target application that uses \"Life 1st Identity Manager\" as a service for authentication.\nThis issue affects Life 1st: 1.5.2.14234."}, {"lang": "es", "value": "El uso de un algoritmo criptográfico roto o riesgoso, el uso de un hash de contraseña con un esfuerzo computacional insuficiente, el uso de un hash débil, el uso de un hash unidireccional con una vulnerabilidad de sal predecible en Beta80 Life 1st permite a un atacante usar la fuerza bruta de las contraseñas de los usuarios o encontrar una colisión para obtener acceso a una aplicación de destino que utiliza BETA80 \"Life 1st Identity Manager\" como un servicio para la autenticación. Este problema afecta a Life 1st: 1.5.2.14234."}], "references": [{"url": "https://euvd.enisa.europa.eu/vulnerability/CVE-2025-26486", "source": "a6d3dc9e-0591-4a13-bce7-0f5b31ff6158", "tags": []}, {"url": "https://www.cvcn.gov.it/cvcn/cve/CVE-2025-26486", "source": "a6d3dc9e-0591-4a13-bce7-0f5b31ff6158", "tags": []}]}
{"cve_id": "CVE-2025-30609", "published_date": "2025-03-24T14:15:33.533", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Insertion of Sensitive Information Into Sent Data vulnerability in AppExperts AppExperts – WordPress to Mobile App – WooCommerce to iOs and Android Apps allows Retrieve Embedded Sensitive Data. This issue affects AppExperts – WordPress to Mobile App – WooCommerce to iOs and Android Apps: from n/a through 1.4.3."}, {"lang": "es", "value": "Vulnerabilidad de inserción de información confidencial en los datos enviados en AppExperts AppExperts – WordPress to Mobile App – WooCommerce to iOs and Android Apps que permite recuperar datos confidenciales incrustados. Este problema afecta a AppExperts (de WordPress a aplicaciones móviles y de WooCommerce a iOS y Android): desde n/d hasta la versión 1.4.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/appexperts/vulnerability/wordpress-appexperts-wordpress-to-mobile-app-woocommerce-to-ios-and-android-apps-1-4-3-sensitive-data-exposure-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
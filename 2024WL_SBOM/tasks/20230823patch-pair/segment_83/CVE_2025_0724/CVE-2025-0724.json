{"cve_id": "CVE-2025-0724", "published_date": "2025-03-22T05:15:38.200", "last_modified_date": "2025-03-27T00:43:04.040", "descriptions": [{"lang": "en", "value": "The ProfileGrid – User Profiles, Groups and Communities plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, ******* via deserialization of untrusted input in the get_user_meta_fields_html function. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present."}, {"lang": "es", "value": "El complemento ProfileGrid – User Profiles, Groups and Communities para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la ******* incluida, mediante la deserialización de entradas no confiables en la función get_user_meta_fields_html. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar un objeto PHP. No se conoce ninguna cadena POP presente en el software vulnerable, por lo que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si una cadena POP está presente a través de un complemento o tema adicional instalado en el sistema objetivo, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código, dependiendo de la cadena POP presente."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/profilegrid-user-profiles-groups-and-communities/tags/*******/includes/class-profile-magic-html-generator.php#L259", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6bb1de69-7bc2-4785-9789-0a2d1cf35b9b?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
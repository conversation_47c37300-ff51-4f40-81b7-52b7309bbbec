{"cve_id": "CVE-2025-2646", "published_date": "2025-03-23T10:15:12.810", "last_modified_date": "2025-04-02T12:32:58.283", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /admin/admin-profile.php. The manipulation of the argument contactnumber leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Art Gallery Management System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/admin-profile.php. La manipulación del argumento contactnumber provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300661", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300661", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.519776", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
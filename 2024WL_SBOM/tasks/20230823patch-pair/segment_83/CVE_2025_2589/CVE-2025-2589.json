{"cve_id": "CVE-2025-2589", "published_date": "2025-03-21T13:15:34.830", "last_modified_date": "2025-04-01T20:23:54.560", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Human Resource Management System 1.0.1 and classified as critical. This issue affects the function Index of the file \\handler\\Account.go. The manipulation of the argument user_cookie leads to improper authorization. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Human Resource Management System 1.0.1, clasificada como crítica. Este problema afecta a la función \"Índice\" del archivo \\handler\\Account.go. La manipulación del argumento \"user_cookie\" provoca una autorización indebida. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/38279/1/issues/1", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.300569", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300569", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517343", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
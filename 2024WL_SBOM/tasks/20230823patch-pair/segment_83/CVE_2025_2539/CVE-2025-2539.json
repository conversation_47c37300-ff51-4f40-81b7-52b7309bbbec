{"cve_id": "CVE-2025-2539", "published_date": "2025-03-20T12:15:14.900", "last_modified_date": "2025-03-20T12:15:14.900", "descriptions": [{"lang": "en", "value": "The File Away plugin for WordPress is vulnerable to unauthorized access of data due to a missing capability check on the ajax() function in all versions up to, and including, *******.1. This makes it possible for unauthenticated attackers, leveraging the use of a reversible weak algorithm,  to read the contents of arbitrary files on the server, which can contain sensitive information."}, {"lang": "es", "value": "El complemento File Away para WordPress es vulnerable al acceso no autorizado a datos debido a la falta de una comprobación de capacidad en la función ajax() en todas las versiones hasta la *******.1 incluida. Esto permite que atacantes no autenticados, aprovechando un algoritmo débil reversible, lean el contenido de archivos arbitrarios en el servidor, que pueden contener información confidencial."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/file-away/trunk/lib/cls/class.fileaway_encrypted.php", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/file-away/trunk/lib/cls/class.fileaway_stats.php", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/file-away/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5b23bd5c-db27-4d63-8461-1f36958a2ff6?source=cve", "source": "<EMAIL>", "tags": []}]}
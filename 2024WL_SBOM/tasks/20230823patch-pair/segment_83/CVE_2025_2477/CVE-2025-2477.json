{"cve_id": "CVE-2025-2477", "published_date": "2025-03-22T07:15:24.780", "last_modified_date": "2025-03-22T07:15:24.780", "descriptions": [{"lang": "en", "value": "The CryoKey plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the ‘ckemail’ parameter in all versions up to, and including, 2.4 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento CryoKey para WordPress es vulnerable a ataques de Cross-Site Scripting reflejado a través del parámetro 'ckemail' en todas las versiones hasta la 2.4 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutan si logran engañar al usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/cryokey/trunk/cryokey.php#L95", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/cryokey/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1646f96c-f0f4-433a-ac5e-04c1c251972d?source=cve", "source": "<EMAIL>", "tags": []}]}
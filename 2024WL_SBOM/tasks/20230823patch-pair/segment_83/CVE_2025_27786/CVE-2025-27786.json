{"cve_id": "CVE-2025-27786", "published_date": "2025-03-19T21:15:40.787", "last_modified_date": "2025-03-19T21:15:40.787", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.8-bugfix and prior are vulnerable to arbitrary file removal in core.py. `output_tts_path` in tts.py takes arbitrary user input and passes it to `run_tts_script` function in core.py, which checks if the path in `output_tts_path` exists, and if yes, removes that path, which leads to arbitrary file removal. As of time of publication, no known patches are available."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.8 (corrección de errores) y anteriores son vulnerables a la eliminación arbitraria de archivos en core.py. La función `output_tts_path` de tts.py toma la entrada arbitraria del usuario y la pasa a la función `run_tts_script` de core.py, que comprueba si la ruta en `output_tts_path` existe y, de ser así, la elimina, lo que provoca la eliminación arbitraria de archivos. Al momento de la publicación, no se conocían parches disponibles."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/core.py#L329", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/tts/tts.py#L133", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
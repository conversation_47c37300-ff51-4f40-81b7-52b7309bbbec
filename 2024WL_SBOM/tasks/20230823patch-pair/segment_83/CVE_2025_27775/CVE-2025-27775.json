{"cve_id": "CVE-2025-27775", "published_date": "2025-03-19T21:15:39.360", "last_modified_date": "2025-03-19T21:15:39.360", "descriptions": [{"lang": "en", "value": "Applio is a voice conversion tool. Versions 3.2.7 and prior are vulnerable to server-side request forgery (SSRF) and file write in `model_download.py` (line 143 in 3.2.7). The blind SSRF allows for sending requests on behalf of Applio server and can be leveraged to probe for other vulnerabilities on the server itself or on other back-end systems on the internal network, that the Applio server can reach. The file write allows for writing files on the server, which can be coupled with other vulnerabilities, for example an unsafe deserialization, to achieve remote code execution on the Applio server. As of time of publication, no known patches are available."}, {"lang": "es", "value": "Applio es una herramienta de conversión de voz. Las versiones 3.2.7 y anteriores son vulnerables a server-side request forgery (SSRF) y a la escritura de archivos en `model_download.py` (línea 143 en 3.2.7). La SSRF ciega permite enviar solicitudes en nombre del servidor Applio y puede utilizarse para investigar otras vulnerabilidades en el propio servidor o en otros sistemas back-end de la red interna a los que el servidor Applio pueda acceder. La escritura de archivos permite escribir archivos en el servidor, lo cual puede combinarse con otras vulnerabilidades, como una deserialización insegura, para lograr la ejecución remota de código en el servidor Applio. Al momento de la publicación, no se conocían parches disponibles."}], "references": [{"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/assets/flask/routes.py#L14", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/rvc/lib/tools/model_download.py#L156", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/rvc/lib/tools/model_download.py#L169-L171", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/IAHispano/Applio/blob/29b4a00e4be209f9aac51cd9ccffcc632dfb2973/tabs/download/download.py#L192-L196", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-341_GHSL-2024-353_Applio/", "source": "<EMAIL>", "tags": []}]}
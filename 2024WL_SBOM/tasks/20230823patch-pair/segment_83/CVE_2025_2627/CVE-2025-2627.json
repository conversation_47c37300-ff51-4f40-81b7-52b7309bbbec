{"cve_id": "CVE-2025-2627", "published_date": "2025-03-22T21:15:36.997", "last_modified_date": "2025-04-02T15:46:50.193", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Art Gallery Management System 1.0. This issue affects some unknown processing of the file /admin/contactus.php. The manipulation of the argument pagetitle leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul Art Gallery Management System 1.0. Este problema afecta a un procesamiento desconocido del archivo /admin/contactus.php. La manipulación del argumento pagetitle provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300629", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300629", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519335", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
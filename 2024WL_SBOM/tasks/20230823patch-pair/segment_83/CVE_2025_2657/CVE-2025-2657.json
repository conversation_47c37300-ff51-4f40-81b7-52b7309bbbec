{"cve_id": "CVE-2025-2657", "published_date": "2025-03-23T18:15:13.607", "last_modified_date": "2025-05-13T20:18:35.010", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in projectworlds Apartment Visitors Management System 1.0. Affected by this vulnerability is an unknown functionality of the file /front.php. The manipulation of the argument rid leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se detectó una vulnerabilidad crítica en projectworlds Apartment Visitors Management System 1.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /front.php. La manipulación del argumento rid provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ydnd/cve/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300673", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300673", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.520237", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-30572", "published_date": "2025-03-24T14:15:29.217", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Igor <PERSON> Simple Rating allows Stored XSS. This issue affects Simple Rating: from n/a through 1.4."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en <PERSON> Simple Rating permite XSS almacenado. Este problema afecta a Simple Rating desde n/d hasta la versión 1.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/simple-rating/vulnerability/wordpress-simple-rating-plugin-1-4-cross-site-request-forgery-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
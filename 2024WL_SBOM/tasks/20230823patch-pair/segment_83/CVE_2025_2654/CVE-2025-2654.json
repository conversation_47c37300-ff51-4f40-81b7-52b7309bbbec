{"cve_id": "CVE-2025-2654", "published_date": "2025-03-23T16:15:13.840", "last_modified_date": "2025-03-26T14:54:37.560", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester AC Repair and Services System 1.0. It has been classified as critical. This affects an unknown part of the file /admin/services/manage_service.php. The manipulation of the argument ID leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester AC Repair and Services System 1.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/services/manage_service.php. La manipulación del ID del argumento provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Colorado-all/cve/blob/main/AC%20Repair%20and%20Services%20System%20using/SQL-4.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.300669", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300669", "source": "<EMAIL>", "tags": ["VDB Entry", "Third Party Advisory"]}, {"url": "https://vuldb.com/?submit.520013", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.sourcecodester.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
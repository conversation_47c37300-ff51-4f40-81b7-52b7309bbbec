{"cve_id": "CVE-2025-1385", "published_date": "2025-03-20T08:15:11.723", "last_modified_date": "2025-03-20T08:15:11.723", "descriptions": [{"lang": "en", "value": "When the library bridge feature is enabled, the clickhouse-library-bridge exposes an HTTP API on localhost. This allows clickhouse-server to dynamically load a library from a specified path and execute it in an isolated process. Combined with the ClickHouse table engine functionality that permits file uploads to specific directories, a misconfigured server can be exploited by an attacker with privilege to access to both table engines to execute arbitrary code on the ClickHouse server.\n\nYou can check if your ClickHouse server is vulnerable to this vulnerability by inspecting the configuration file and confirming if the following setting is enabled:\n\n<library_bridge>\n   <port>9019</port>\n</library_bridge>"}, {"lang": "es", "value": "Cuando la función de puente de librería está habilitada, clickhouse-library-bridge expone una API HTTP en el host local. Esto permite a clickhouse-server cargar dinámicamente una librería desde una ruta específica y ejecutarla en un proceso aislado. En combinación con la funcionalidad del motor de tablas de ClickHouse, que permite la carga de archivos a directorios específicos, un servidor mal configurado puede ser explotado por un atacante con privilegios de acceso a ambos motores de tablas para ejecutar código arbitrario en el servidor de ClickHouse. Puede comprobar si su servidor de ClickHouse es vulnerable a esta vulnerabilidad inspeccionando el archivo de configuración y confirmando si la siguiente configuración está habilitada:  9019 "}], "references": [{"url": "https://github.com/ClickHouse/ClickHouse/security/advisories/GHSA-5phv-x8x4-83x5", "source": "cb7ba516-3b07-4c98-b0c2-715220f1a8f6", "tags": []}]}
{"cve_id": "CVE-2025-2707", "published_date": "2025-03-24T19:15:50.963", "last_modified_date": "2025-07-15T13:07:48.617", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in zhijiantianya ruoyi-vue-pro 2.4.1. Affected by this issue is some unknown functionality of the file /app-api/infra/file/upload of the component Front-End Store Interface. The manipulation of the argument path leads to path traversal. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en zhijiantianya ruoyi-vue-pro 2.4.1. Este problema afecta a una funcionalidad desconocida del archivo /app-api/infra/file/upload del componente Interfaz de la Tienda Front-End. La manipulación de la ruta del argumento provoca un path traversal. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/ruoyi-vue-pro.md#3file-path-traversal-front-end", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300728", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300728", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517029", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
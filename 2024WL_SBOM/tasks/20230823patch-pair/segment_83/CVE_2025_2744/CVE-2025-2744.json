{"cve_id": "CVE-2025-2744", "published_date": "2025-03-25T07:15:39.103", "last_modified_date": "2025-07-14T20:11:35.590", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in zhijiantianya ruoyi-vue-pro 2.4.1. Affected is an unknown function of the file /admin-api/mp/material/upload-news-image of the component Material Upload Interface. The manipulation of the argument File leads to path traversal. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en zhijiantianya ruoyi-vue-pro 2.4.1. La vulnerabilidad afecta a una función desconocida del archivo /admin-api/mp/material/upload-news-image del componente Material Upload Interface. La manipulación del argumento \"File\" provoca un path traversal. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/ruoyi-vue-pro.md#7arbitrary-file-deletion-vulnerability---uploadnewsimage", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300846", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300846", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519694", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-30531", "published_date": "2025-03-24T14:15:21.693", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in GBS Developer WP Ride Booking allows Cross Site Request Forgery. This issue affects WP Ride Booking: from n/a through 2.4."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en GBS Developer WP Ride Booking permite Cross-Site Request Forgery. Este problema afecta a WP Ride Booking desde n/d hasta la versión 2.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-ride-booking/vulnerability/wordpress-wp-ride-booking-plugin-2-4-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
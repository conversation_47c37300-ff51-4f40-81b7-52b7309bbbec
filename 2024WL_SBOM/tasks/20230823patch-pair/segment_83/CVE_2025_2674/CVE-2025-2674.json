{"cve_id": "CVE-2025-2674", "published_date": "2025-03-24T00:15:13.533", "last_modified_date": "2025-03-27T18:21:20.710", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Bank Locker Management System 1.0. Affected by this vulnerability is an unknown functionality of the file /aboutus.php. The manipulation of the argument pagetitle leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad crítica en PHPGurukul Bank Locker Management System 1.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /aboutus.php. La manipulación del argumento pagetitle provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300691", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300691", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521441", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
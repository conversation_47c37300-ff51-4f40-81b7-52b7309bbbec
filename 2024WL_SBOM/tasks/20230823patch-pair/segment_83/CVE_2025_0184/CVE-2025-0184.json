{"cve_id": "CVE-2025-0184", "published_date": "2025-03-20T10:15:51.173", "last_modified_date": "2025-07-15T15:41:34.817", "descriptions": [{"lang": "en", "value": "A Server-Side Request Forgery (SSRF) vulnerability was identified in langgenius/dify version 0.10.2. The vulnerability occurs in the 'Create Knowledge' section when uploading DOCX files. If an external relationship exists in the DOCX file, the reltype value is requested as a URL using the 'requests' module instead of the 'ssrf_proxy', leading to an SSRF vulnerability. This issue was fixed in version 0.11.0."}, {"lang": "es", "value": "Se identificó una vulnerabilidad de Server-Side Request Forgery (SSRF) en langgenius/dify versión 0.10.2. La vulnerabilidad se produce en la sección \"Crear conocimiento\" al subir archivos DOCX. Si existe una relación externa en el archivo DOCX, el valor reltype se solicita como URL mediante el módulo \"requests\" en lugar de \"ssrf_proxy\", lo que genera una vulnerabilidad SSRF. Este problema se solucionó en la versión 0.11.0."}], "references": [{"url": "https://github.com/langgenius/dify/commit/c135ec4b08d946a1a1d3a198a1d72c1ccf47250f", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://huntr.com/bounties/a7eac4ae-5d5e-4ac1-894b-7a8cce5cba9b", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-30208", "published_date": "2025-03-24T17:15:21.820", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Vite, a provider of frontend development tooling, has a vulnerability in versions prior to 6.2.3, 6.1.2, 6.0.12, 5.4.15, and 4.5.10. `@fs` denies access to files outside of Vite serving allow list. Adding `?raw??` or `?import&raw??` to the URL bypasses this limitation and returns the file content if it exists. This bypass exists because trailing separators such as `?` are removed in several places, but are not accounted for in query string regexes. The contents of arbitrary files can be returned to the browser. Only apps explicitly exposing the Vite dev server to the network (using `--host` or `server.host` config option) are affected. Versions 6.2.3, 6.1.2, 6.0.12, 5.4.15, and 4.5.10 fix the issue."}, {"lang": "es", "value": "<PERSON>ite, un proveedor de herramientas de desarrollo frontend, presenta una vulnerabilidad en versiones anteriores a 6.2.3, 6.1.2, 6.0.12, 5.4.15 y 4.5.10. `@fs` deniega el acceso a archivos fuera de la lista de permitidos del servidor Vite. Añadir `?raw??` o `?import&amp;raw??` a la URL evita esta limitación y devuelve el contenido del archivo, si existe. Esta omisión se debe a que los separadores finales, como `?`, se eliminan en varios lugares, pero no se tienen en cuenta en las expresiones regulares de las cadenas de consulta. El contenido de archivos arbitrarios puede devolverse al navegador. Solo las aplicaciones que exponen explícitamente el servidor de desarrollo de Vite a la red (mediante la opción de configuración `--host` o `server.host`) se ven afectadas. Las versiones 6.2.3, 6.1.2, 6.0.12, 5.4.15 y 4.5.10 solucionan el problema."}], "references": [{"url": "https://github.com/vitejs/vite/commit/315695e9d97cc6cfa7e6d9e0229fb50cdae3d9f4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vitejs/vite/commit/80381c38d6f068b12e6e928cd3c616bd1d64803c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vitejs/vite/commit/807d7f06d33ab49c48a2a3501da3eea1906c0d41", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vitejs/vite/commit/92ca12dc79118bf66f2b32ff81ed09e0d0bd07ca", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vitejs/vite/commit/f234b5744d8b74c95535a7b82cc88ed2144263c1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vitejs/vite/security/advisories/GHSA-x574-m823-4x7w", "source": "<EMAIL>", "tags": []}]}
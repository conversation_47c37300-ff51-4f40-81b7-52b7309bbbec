{"cve_id": "CVE-2025-30523", "published_date": "2025-03-24T14:15:20.593", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in Marcel-NL Super Simple Subscriptions allows SQL Injection. This issue affects Super Simple Subscriptions: from n/a through 1.1.0."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en Marcel-NL Super Simple Subscriptions permite la inyección SQL. Este problema afecta a las Suscripciones Super Simples desde n/d hasta la versión 1.1.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/super-simple-subscriptions/vulnerability/wordpress-super-simple-subscriptions-plugin-1-1-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
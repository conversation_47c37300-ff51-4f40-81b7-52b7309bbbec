{"cve_id": "CVE-2025-1446", "published_date": "2025-03-23T06:15:12.110", "last_modified_date": "2025-04-02T14:09:12.570", "descriptions": [{"lang": "en", "value": "The Pods  WordPress plugin before ******* does not sanitize and escape a parameter before using it in a SQL statement, allowing admins to perform SQL injection attacks"}, {"lang": "es", "value": "El complemento Pods de WordPress anterior a la versión ******* no depura ni escapa un parámetro antes de usarlo en una declaración SQL, lo que permite a los administradores realizar ataques de inyección SQL."}], "references": [{"url": "https://wpscan.com/vulnerability/c170fb45-7ed5-40ef-99f6-8da035a23d89/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
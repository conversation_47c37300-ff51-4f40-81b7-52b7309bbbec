{"cve_id": "CVE-2025-30551", "published_date": "2025-03-24T14:15:24.260", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in smartredfox Pretty file links allows Stored XSS. This issue affects Pretty file links: from n/a through 0.9."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en smartredfox Pretty file links que permite XSS almacenado. Este problema afecta a los enlaces de archivos Pretty desde n/a hasta la versión 0.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/pretty-file-links/vulnerability/wordpress-pretty-file-links-plugin-0-9-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
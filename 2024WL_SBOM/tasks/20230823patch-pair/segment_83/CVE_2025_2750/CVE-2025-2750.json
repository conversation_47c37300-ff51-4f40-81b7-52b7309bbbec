{"cve_id": "CVE-2025-2750", "published_date": "2025-03-25T08:15:19.203", "last_modified_date": "2025-07-17T21:51:55.853", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in Open Asset Import Library Assimp 5.4.3. This affects the function Assimp::CSMImporter::InternReadFile of the file code/AssetLib/CSM/CSMLoader.cpp of the component CSM File Handler. The manipulation leads to out-of-bounds write. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en Open Asset Import Library Assimp 5.4.3. Esta afecta a la función Assimp::CSMImporter::InternReadFile del archivo code/AssetLib/CSM/CSMLoader.cpp del componente CSM File Handler. La manipulación provoca escritura fuera de los límites. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6011", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6011#issue-2877369004", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300855", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300855", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517783", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-27612", "published_date": "2025-03-21T15:15:42.153", "last_modified_date": "2025-03-21T15:15:42.153", "descriptions": [{"lang": "en", "value": "libcontainer is a library for container control. Prior to libcontainer 0.5.3, while creating a tenant container, the tenant builder accepts a list of capabilities to be added in the spec of tenant container. The logic here adds the given capabilities to all capabilities of main container if present in spec, otherwise simply set provided capabilities as capabilities of the tenant container. However, setting inherited caps in any case for tenant container can lead to elevation of capabilities, similar to CVE-2022-29162. This does not affect youki binary itself. This is only applicable if you are using libcontainer directly and using the tenant builder."}, {"lang": "es", "value": "libcontainer es una librería para el control de contenedores. Antes de libcontainer 0.5.3, al crear un contenedor de inquilinos, el generador de inquilinos aceptaba una lista de capacidades que se añadían a la especificación del contenedor. La lógica aquí añade las capacidades dadas a todas las capacidades del contenedor principal si están presentes en la especificación; de lo contrario, simplemente se establecen las capacidades proporcionadas como capacidades del contenedor de inquilinos. Sin embargo, establecer límites heredados para el contenedor de inquilinos puede provocar la elevación de capacidades, similar a CVE-2022-29162. Esto no afecta al binario de Youki. Esto solo aplica si se usa libcontainer directamente y el generador de inquilinos."}], "references": [{"url": "https://github.com/opencontainers/runc/security/advisories/GHSA-f3fp-gc8g-vw66", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/youki-dev/youki/blob/9e63fa4da1672a78ca45100f3059a732784a5174/crates/libcontainer/src/container/tenant_builder.rs#L408", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/youki-dev/youki/commit/747e342d2026fbf3a395db3e2a491ebef00082f1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/youki-dev/youki/security/advisories/GHSA-5w4j-f78p-4wh9", "source": "<EMAIL>", "tags": []}]}
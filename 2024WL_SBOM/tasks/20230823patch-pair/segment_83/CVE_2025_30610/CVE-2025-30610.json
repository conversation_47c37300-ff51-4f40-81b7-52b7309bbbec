{"cve_id": "CVE-2025-30610", "published_date": "2025-03-24T14:15:33.680", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in catchsquare WP Social Widget allows Stored XSS. This issue affects WP Social Widget: from n/a through 2.2.6."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en catchsquare WP Social Widget permite XSS almacenado. Este problema afecta al widget social de WP desde la versión n/d hasta la 2.2.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-social-widget/vulnerability/wordpress-wp-social-widget-2-2-6-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
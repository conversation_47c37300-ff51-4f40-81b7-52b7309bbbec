{"cve_id": "CVE-2025-2699", "published_date": "2025-03-24T08:15:12.967", "last_modified_date": "2025-07-22T15:07:55.677", "descriptions": [{"lang": "en", "value": "A vulnerability was found in GetmeUK ContentTools up to 1.6.16. It has been rated as problematic. Affected by this issue is some unknown functionality of the component Image Handler. The manipulation of the argument onload leads to cross site scripting. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en GetmeUK ContentTools hasta la versión 1.6.16. Se ha clasificado como problemática. Este problema afecta a una funcionalidad desconocida del componente Image Handler. La manipulación del argumento \"onload\" provoca ataques de cross site scripting. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://gist.github.com/Masamuneee/657f2e2b0eb5bf9b0d4dbb79f00dac37", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300716", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300716", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.515864", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
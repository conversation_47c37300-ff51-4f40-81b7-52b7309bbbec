{"cve_id": "CVE-2025-29770", "published_date": "2025-03-19T16:15:31.977", "last_modified_date": "2025-03-19T16:15:31.977", "descriptions": [{"lang": "en", "value": "vLLM is a high-throughput and memory-efficient inference and serving engine for LLMs. The outlines library is one of the backends used by vLLM to support structured output (a.k.a. guided decoding). Outlines provides an optional cache for its compiled grammars on the local filesystem. This cache has been on by default in vLLM. Outlines is also available by default through the OpenAI compatible API server. The affected code in vLLM is vllm/model_executor/guided_decoding/outlines_logits_processors.py, which unconditionally uses the cache from outlines. A malicious user can send a stream of very short decoding requests with unique schemas, resulting in an addition to the cache for each request. This can result in a Denial of Service if the filesystem runs out of space. Note that even if vLLM was configured to use a different backend by default, it is still possible to choose outlines on a per-request basis using the guided_decoding_backend key of the extra_body field of the request. This issue applies only to the V0 engine and is fixed in 0.8.0."}, {"lang": "es", "value": "vLLM es un motor de inferencia y servicio de alto rendimiento y eficiente en memoria para LLM. La librería de esquemas es uno de los backends que vLLM utiliza para la salida estructurada (también conocida como decodificación guiada). Outlines proporciona una caché opcional para sus gramáticas compiladas en el sistema de archivos local. Esta caché está activada por defecto en vLLM. Outlines también está disponible por defecto a través del servidor de API compatible con OpenAI. El código afectado en vLLM es vllm/model_executor/guided_decoding/outlines_logits_processors.py, que utiliza incondicionalmente la caché de outlines. Un usuario malintencionado puede enviar un flujo de solicitudes de decodificación muy cortas con esquemas únicos, lo que resulta en una adición a la caché para cada solicitud. Esto puede provocar una denegación de servicio si el sistema de archivos se queda sin espacio. Tenga en cuenta que, incluso si vLLM se configuró para usar un backend diferente por defecto, aún es posible seleccionar esquemas por solicitud mediante la clave `guided_decoding_backend` del campo `extra_body` de la solicitud. Este problema solo afecta al motor V0 y se solucionó en la versión 0.8.0."}], "references": [{"url": "https://github.com/vllm-project/vllm/blob/53be4a863486d02bd96a59c674bbec23eec508f6/vllm/model_executor/guided_decoding/outlines_logits_processors.py", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vllm-project/vllm/pull/14837", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-mgrm-fgjv-mhv8", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2756", "published_date": "2025-03-25T10:15:16.627", "last_modified_date": "2025-07-17T21:47:29.243", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in Open Asset Import Library Assimp 5.4.3. This affects the function Assimp::AC3DImporter::ConvertObjectSection of the file code/AssetLib/AC/ACLoader.cpp of the component AC3D File Handler. The manipulation of the argument tmp leads to heap-based buffer overflow. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en Open Asset Import Library Assimp 5.4.3. Esta afecta a la función Assimp::AC3DImporter::ConvertObjectSection del archivo code/AssetLib/AC/ACLoader.cpp del componente AC3D File Handler. La manipulación del argumento tmp provoca un desbordamiento del búfer en el montón. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6018", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6018#issue-2877375815", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300861", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300861", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517790", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
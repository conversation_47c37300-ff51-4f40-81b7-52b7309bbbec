{"cve_id": "CVE-2025-30527", "published_date": "2025-03-24T14:15:21.083", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in codetoolbox My Bootstrap Menu allows Stored XSS. This issue affects My Bootstrap Menu: from n/a through 1.2.1."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en codetoolbox My Bootstrap Menu permite XSS almacenado. Este problema afecta a My Bootstrap Menu desde n/d hasta la versión 1.2.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/my-bootstrap-menu/vulnerability/wordpress-my-bootstrap-menu-plugin-1-2-1-stored-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
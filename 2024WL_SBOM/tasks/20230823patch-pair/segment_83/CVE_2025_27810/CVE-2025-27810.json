{"cve_id": "CVE-2025-27810", "published_date": "2025-03-25T06:15:41.180", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Mbed TLS before 2.28.10 and 3.x before 3.6.3, in some cases of failed memory allocation or hardware errors, uses uninitialized stack memory to compose the TLS Finished message, potentially leading to authentication bypasses such as replays."}, {"lang": "es", "value": "Mbed TLS anterior a 2.28.10 y 3.x anterior a 3.6.3, en algunos casos de asignación de memoria fallida o errores de hardware, utiliza memoria de pila no inicializada para componer el mensaje TLS Finalizado, lo que puede provocar omisiones de autenticación como repeticiones."}], "references": [{"url": "https://github.com/Mbed-TLS/mbedtls/releases", "source": "<EMAIL>", "tags": []}, {"url": "https://mbed-tls.readthedocs.io/en/latest/security-advisories/mbedtls-security-advisory-2025-03-2/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27553", "published_date": "2025-03-23T15:15:13.377", "last_modified_date": "2025-04-02T22:15:19.203", "descriptions": [{"lang": "en", "value": "Relative Path Traversal vulnerability in Apache Commons VFS before 2.10.0.\n\nThe FileObject API in Commons VFS has a 'resolveFile' method that\ntakes a 'scope' parameter. Specifying 'NameScope.DESCENDENT' promises that \"an exception is thrown if the resolved file is not a descendent of\nthe base file\". However, when the path contains encoded \"..\"\ncharacters (for example, \"%2E%2E/bar.txt\"), it might return file objects that are not\na descendent of the base file, without throwing an exception.\nThis issue affects Apache Commons VFS: before 2.10.0.\n\nUsers are recommended to upgrade to version 2.10.0, which fixes the issue."}, {"lang": "es", "value": "Vulnerabilidad de Path Traversal relativo en Apache Commons VFS anterior a la versión 2.10.0. La API FileObject de Commons VFS incluye un método \"resolveFile\" que utiliza el parámetro \"scope\". Especificar \"NameScope.DESCENDENT\" implica que se lanzará una excepción si el archivo resuelto no es descendiente del archivo base. Sin embargo, si la ruta contiene caracteres \"..\" codificados (por ejemplo, \"%2E%2E/bar.txt\"), podría devolver objetos de archivo que no son descendientes del archivo base, sin lanzar una excepción. Este problema afecta a Apache Commons VFS anterior a la versión 2.10.0. Se recomienda actualizar a la versión 2.10.0, que soluciona el problema."}], "references": [{"url": "https://lists.apache.org/thread/cnzqowyw9r2pl263cylmxhnvh41hyjcb", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/23/1", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2025/04/msg00006.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
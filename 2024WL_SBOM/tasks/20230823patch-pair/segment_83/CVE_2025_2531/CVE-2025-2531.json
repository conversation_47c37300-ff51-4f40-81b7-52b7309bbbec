{"cve_id": "CVE-2025-2531", "published_date": "2025-03-25T15:15:25.773", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "Luxion KeyShot DAE File Parsing Heap-based Buffer Overflow Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Luxion KeyShot. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of dae files. The issue results from the lack of proper validation of the length of user-supplied data prior to copying it to a heap-based buffer. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-23704."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por desbordamiento de búfer basado en montón en el análisis de archivos DAE de Luxion KeyShot. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Luxion KeyShot. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos DAE. El problema se debe a la falta de una validación adecuada de la longitud de los datos proporcionados por el usuario antes de copiarlos a un búfer basado en montón. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Era ZDI-CAN-23704."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-174/", "source": "<EMAIL>", "tags": []}]}
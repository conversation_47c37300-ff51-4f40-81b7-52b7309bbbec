{"cve_id": "CVE-2025-2665", "published_date": "2025-03-23T22:15:13.340", "last_modified_date": "2025-05-13T20:03:09.317", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Security Guards Hiring System 1.0. It has been classified as critical. This affects an unknown part of the file /admin/bwdates-reports-details.php. The manipulation of the argument fromdate/todate leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Security Guards Hiring System 1.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/bwdates-reports-details.php. La manipulación del argumento fromdate/todate provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/i-Corner/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300687", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300687", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521167", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-30537", "published_date": "2025-03-24T14:15:22.593", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in C<PERSON><PERSON> Upload Quota per User allows Stored XSS. This issue affects Upload Quota per User: from n/a through 1.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en <PERSON><PERSON><PERSON> Upload Quota per User permite XSS almacenado. Este problema afecta a la cuota de carga por usuario desde n/d hasta la versión 1.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/upload-quota-per-user/vulnerability/wordpress-upload-quota-per-user-1-3-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
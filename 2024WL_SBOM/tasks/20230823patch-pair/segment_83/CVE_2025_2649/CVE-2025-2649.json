{"cve_id": "CVE-2025-2649", "published_date": "2025-03-23T12:15:13.083", "last_modified_date": "2025-03-27T16:28:22.013", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Doctor Appointment Management System 1.0. This vulnerability affects unknown code of the file /check-appointment.php. The manipulation of the argument searchdata leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Doctor Appointment Management System 1.0. Esta vulnerabilidad afecta al código desconocido del archivo /check-appointment.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/liuhao2638/cve/issues/12", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300664", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300664", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.519780", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
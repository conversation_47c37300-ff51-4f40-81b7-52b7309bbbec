{"cve_id": "CVE-2025-0312", "published_date": "2025-03-20T10:15:52.280", "last_modified_date": "2025-03-28T14:11:12.457", "descriptions": [{"lang": "en", "value": "A vulnerability in ollama/ollama versions <=0.3.14 allows a malicious user to create a customized GGUF model file that, when uploaded and created on the Ollama server, can cause a crash due to an unchecked null pointer dereference. This can lead to a Denial of Service (DoS) attack via remote network."}, {"lang": "es", "value": "Una vulnerabilidad en ollama/ollama versiones anteriores a la 0.3.14 permite a un usuario malintencionado crear un archivo de modelo GGUF personalizado que, al cargarse y crearse en el servidor Ollama, puede provocar un bloqueo debido a una desreferencia de puntero nulo no comprobada. Esto puede provocar un ataque de denegación de servicio (DoS) a través de una red remota."}], "references": [{"url": "https://huntr.com/bounties/522c87b6-a7ac-41b2-84f3-62fd58921f21", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
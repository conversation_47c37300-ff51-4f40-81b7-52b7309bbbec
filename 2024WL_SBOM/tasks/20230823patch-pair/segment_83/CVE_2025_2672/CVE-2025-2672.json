{"cve_id": "CVE-2025-2672", "published_date": "2025-03-23T23:15:13.847", "last_modified_date": "2025-05-14T16:36:24.390", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Payroll Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /add_deductions.php. The manipulation of the argument bir leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Payroll Management System 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /add_deductions.php. La manipulación del argumento bir provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/FoLaJJ/cve/blob/main/sqlcve.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.300689", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300689", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521243", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
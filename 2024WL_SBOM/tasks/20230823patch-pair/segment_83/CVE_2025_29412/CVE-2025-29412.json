{"cve_id": "CVE-2025-29412", "published_date": "2025-03-20T14:15:24.653", "last_modified_date": "2025-04-01T20:19:31.970", "descriptions": [{"lang": "en", "value": "A cross-site scripting (XSS) vulnerability in the Client Profile Update section of Mart Developers iBanking v2.0.0 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the Name parameter."}, {"lang": "es", "value": "Una vulnerabilidad de cross-site scripting (XSS) en la sección actualización del perfil del cliente de Mart Developers iBanking v2.0.0 permite a los atacantes ejecutar scripts web o HTML arbitrarios mediante la inyección de un payload manipulada en el parámetro Nombre."}], "references": [{"url": "https://github.com/MartMbithi/iBanking/issues/11", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://www.simonjuguna.com/cve-2025-29412-cross-site-scripting-xss-vulnerability-in-ibanking-v2-0-0/", "source": "<EMAIL>", "tags": ["Exploit"]}]}
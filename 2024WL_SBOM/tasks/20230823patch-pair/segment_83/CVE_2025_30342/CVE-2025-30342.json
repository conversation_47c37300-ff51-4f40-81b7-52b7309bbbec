{"cve_id": "CVE-2025-30342", "published_date": "2025-03-21T06:15:26.510", "last_modified_date": "2025-03-27T13:35:33.940", "descriptions": [{"lang": "en", "value": "An XSS issue was discovered in OpenSlides before 4.2.5. When submitting descriptions such as Moderator Notes or Agenda Topics, an editor is shown that allows one to format the submitted text. This allows insertion of various HTML elements. When trying to insert a SCRIPT element, it is properly encoded when reflected; however, adding attributes to links is possible, which allows the injection of JavaScript via the onmouseover attribute and others. When a user moves the mouse over such a prepared link, JavaScript is executed in that user's session."}, {"lang": "es", "value": "Se detectó un problema de XSS en OpenSlides antes de la versión 4.2.5. Al enviar descripciones como Notas del moderador o Temas de la agenda, se muestra un editor que permite formatear el texto enviado. Esto permite insertar varios elementos HTML. Al insertar un elemento SCRIPT, este se codifica correctamente al reflejarse; sin embargo, es posible añadir atributos a los enlaces, lo que permite la inyección de JavaScript mediante el atributo onmouseover y otros. Cuando un usuario pasa el ratón sobre un enlace preparado, se ejecuta JavaScript en su sesión."}], "references": [{"url": "https://www.x41-dsec.de/lab/advisories/x41-2025-001-OpenSlides", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
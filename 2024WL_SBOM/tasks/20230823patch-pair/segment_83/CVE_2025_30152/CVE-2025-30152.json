{"cve_id": "CVE-2025-30152", "published_date": "2025-03-19T16:15:33.313", "last_modified_date": "2025-03-19T16:15:33.313", "descriptions": [{"lang": "en", "value": "The Syliud PayPal Plugin is the Sylius Core Team’s plugin for the PayPal Commerce Platform. Prior to 1.6.2, 1.7.2, and 2.0.2, a discovered vulnerability allows users to modify their shopping cart after completing the PayPal Checkout process and payment authorization. If a user initiates a PayPal transaction from a product page or the cart page and then returns to the order summary page, they can still manipulate the cart contents before finalizing the order. As a result, the order amount in Sylius may be higher than the amount actually captured by PayPal, leading to a scenario where merchants deliver products or services without full payment. The issue is fixed in versions: 1.6.2, 1.7.2, 2.0.2 and above."}, {"lang": "es", "value": "El Syliud PayPal es el complemento del equipo principal de Sylius para la plataforma PayPal Commerce. En versiones anteriores a las 1.6.2, 1.7.2 y 2.0.2, se descubrió una vulnerabilidad que permitía a los usuarios modificar su carrito de compra tras completar el proceso de pago y la autorización del pago. Si un usuario inicia una transacción de PayPal desde la página de un producto o del carrito y luego regresa a la página de resumen del pedido, aún puede manipular el contenido del carrito antes de finalizarlo. Como resultado, el importe del pedido en Sylius puede ser superior al importe real capturado por PayPal, lo que provoca que los comerciantes entreguen productos o servicios sin el pago completo. El problema se ha solucionado en las versiones 1.6.2, 1.7.2, 2.0.2 y posteriores."}], "references": [{"url": "https://github.com/Sylius/PayPalPlugin/commit/5613df827a6d4fc50862229295976200a68e97aa", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Sylius/PayPalPlugin/security/advisories/GHSA-hxg4-65p5-9w37", "source": "<EMAIL>", "tags": []}]}
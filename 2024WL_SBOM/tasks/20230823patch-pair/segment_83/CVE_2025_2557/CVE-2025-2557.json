{"cve_id": "CVE-2025-2557", "published_date": "2025-03-20T19:15:38.220", "last_modified_date": "2025-03-20T19:15:38.220", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in Audi UTR Dashcam 2.0. Affected by this issue is some unknown functionality of the component Command API. The manipulation leads to improper access controls. The attack needs to be done within the local network. The exploit has been disclosed to the public and may be used. Upgrading to version 2.89 and 2.90 is able to address this issue. It is recommended to upgrade the affected component. The vendor was contacted early about these issues and acted very professional. Version 2.89 is fixing this issue for new customers and 2.90 is going to fix it for existing customers."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en la cámara Audi UTR Dashcam 2.0. Este problema afecta a una funcionalidad desconocida del componente Command API. La manipulación genera controles de acceso inadecuados. El ataque debe realizarse dentro de la red local. Se ha hecho público el exploit y puede que sea utilizado. Actualizar a las versiones 2.89 y 2.90 puede solucionar este problema. Se recomienda actualizar el componente afectado. Se contactó con el proveedor con prontitud para informarle sobre estos problemas y actuó con gran profesionalidad. La versión 2.89 soluciona este problema para los nuevos clientes y la 2.90 lo solucionará para los clientes existentes."}], "references": [{"url": "https://github.com/geo-chen/Audi/blob/main/README.md#finding-4-execute-remote-commands", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.300170", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.300170", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.513393", "source": "<EMAIL>", "tags": []}]}
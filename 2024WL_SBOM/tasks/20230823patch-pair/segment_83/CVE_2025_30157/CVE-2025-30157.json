{"cve_id": "CVE-2025-30157", "published_date": "2025-03-21T15:15:43.290", "last_modified_date": "2025-04-01T20:22:34.500", "descriptions": [{"lang": "en", "value": "Envoy is a cloud-native high-performance edge/middle/service proxy. Prior to 1.33.1, 1.32.4, 1.31.6, and 1.30.10, Envoy's ext_proc HTTP filter is at risk of crashing if a local reply is sent to the external server due to the filter's life time issue. A known situation is the failure of a websocket handshake will trigger a local reply leading to the crash of <PERSON><PERSON>. This vulnerability is fixed in 1.33.1, 1.32.4, 1.31.6, and 1.30.10."}, {"lang": "es", "value": "Envoy es un proxy de alto rendimiento para servicios perimetrales, intermedios y de borde, nativo de la nube. En versiones anteriores a las 1.33.1, 1.32.4, 1.31.6 y 1.30.10, el filtro HTTP ext_proc de Envoy corría el riesgo de bloquearse si se enviaba una respuesta local al servidor externo debido a un problema de duración del filtro. Un fallo en el protocolo de enlace websocket desencadenaba una respuesta local que provocaba el bloqueo de Envoy. Esta vulnerabilidad se corrigió en las versiones 1.33.1, 1.32.4, 1.31.6 y 1.30.10."}], "references": [{"url": "https://github.com/envoyproxy/envoy/commit/8eda1b8ef5ba8663d16a737ab99458c039a9b53c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/envoyproxy/envoy/security/advisories/GHSA-cf3q-gqg7-3fm9", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2025-2510", "published_date": "2025-03-25T09:15:16.597", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "The Frndzk Expandable Bottom Bar plugin for WordPress is vulnerable to Stored Cross-Site Scripting via 'text' parameter in all versions up to, and including, 1.0 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled."}, {"lang": "es", "value": "El complemento Frndzk Expandable Bottom Bar para WordPress es vulnerable a cross site scripting almacenado mediante el parámetro \"text\" en todas las versiones hasta la 1.0 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite a atacantes autenticados, con permisos de administrador o superiores, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada. Esto solo afecta a instalaciones multisitio y a instalaciones donde se ha deshabilitado unfiltered_html."}], "references": [{"url": "https://wordpress.org/plugins/frndzk-expandable-bottom-bar/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4261c81e-13a2-4022-8048-aeb0ea4e9ee4?source=cve", "source": "<EMAIL>", "tags": []}]}
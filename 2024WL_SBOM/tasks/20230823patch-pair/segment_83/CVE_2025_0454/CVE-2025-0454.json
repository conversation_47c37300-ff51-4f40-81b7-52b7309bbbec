{"cve_id": "CVE-2025-0454", "published_date": "2025-03-20T10:15:53.157", "last_modified_date": "2025-03-20T10:15:53.157", "descriptions": [{"lang": "en", "value": "A Server-Side Request Forgery (SSRF) vulnerability was identified in the Requests utility of significant-gravitas/autogpt versions prior to v0.4.0. The vulnerability arises due to a hostname confusion between the `urlparse` function from the `urllib.parse` library and the `requests` library. A malicious user can exploit this by submitting a specially crafted URL, such as `http://localhost:\\@google.com/../`, to bypass the SSRF check and perform an SSRF attack."}, {"lang": "es", "value": "Se identificó una vulnerabilidad de Server-Side Request Forgery (SSRF) en la utilidad de solicitudes de las versiones de significant-gravitas/autogpt anteriores a la v0.4.0. Esta vulnerabilidad surge debido a una confusión de nombre de host entre la función `urlparse` de la librería `urllib.parse` y la librería `requests`. Un usuario malintencionado puede explotar esto enviando una URL especialmente manipulada, como `http://localhost:\\@google.com/../`, para eludir la comprobación de SSRF y ejecutar un ataque SSRF."}], "references": [{"url": "https://github.com/significant-gravitas/autogpt/commit/ff065cd24c2289878c0abdb9adbf91c305f0d70a", "source": "<EMAIL>", "tags": []}, {"url": "https://huntr.com/bounties/0664fdee-bdc2-4650-8075-74d7b8d3e308", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0281", "published_date": "2025-03-20T10:15:52.153", "last_modified_date": "2025-03-28T14:22:53.790", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability exists in lunary-ai/lunary versions 1.6.7 and earlier. An attacker can inject malicious JavaScript into the SAML IdP XML metadata, which is used to generate the SAML login redirect URL. This URL is then set as the value of `window.location.href` without proper validation or sanitization. This vulnerability allows the attacker to execute arbitrary JavaScript in the context of the user's browser, potentially leading to session hijacking, data theft, or other malicious actions. The issue is fixed in version 1.7.10."}, {"lang": "es", "value": "Existe una vulnerabilidad de Cross-Site Scripting (XSS) almacenado en las versiones 1.6.7 y anteriores de lunary-ai/lunary. Un atacante puede inyectar JavaScript malicioso en los metadatos XML del IdP SAML, que se utilizan para generar la URL de redirección de inicio de sesión SAML. Esta URL se configura con el valor `window.location.href` sin la validación ni la depuración adecuada. Esta vulnerabilidad permite al atacante ejecutar JavaScript arbitrario en el navegador del usuario, lo que podría provocar el secuestro de sesión, el robo de datos u otras acciones maliciosas. El problema se solucionó en la versión 1.7.10."}], "references": [{"url": "https://github.com/lunary-ai/lunary/commit/fa0fd7742ae029ed934690d282519263f5d838de", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://huntr.com/bounties/b3f4a655-5b08-4fef-be2c-aac8703ad5d0", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
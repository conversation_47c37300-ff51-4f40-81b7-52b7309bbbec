{"cve_id": "CVE-2025-2734", "published_date": "2025-03-25T05:15:41.073", "last_modified_date": "2025-05-15T19:38:14.710", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Old Age Home Management System 1.0. Affected is an unknown function of the file /admin/aboutus.php. The manipulation of the argument pagetitle leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Old Age Home Management System 1.0. Se ve afectada una función desconocida del archivo /admin/aboutus.php. La manipulación del argumento pagetitle provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/0xabandon/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300756", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300756", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.522265", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
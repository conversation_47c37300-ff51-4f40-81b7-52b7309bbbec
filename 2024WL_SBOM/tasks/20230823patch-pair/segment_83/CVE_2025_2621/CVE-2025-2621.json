{"cve_id": "CVE-2025-2621", "published_date": "2025-03-22T17:15:34.790", "last_modified_date": "2025-03-26T18:43:08.013", "descriptions": [{"lang": "en", "value": "A vulnerability was found in D-Link DAP-1620 1.03 and classified as critical. This issue affects the function check_dws_cookie of the file /storage. The manipulation of the argument uid leads to stack-based buffer overflow. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. This vulnerability only affects products that are no longer supported by the maintainer."}, {"lang": "es", "value": "Se detectó una vulnerabilidad en D-Link DAP-1620 1.03, clasificada como crítica. Este problema afecta a la función check_dws_cookie del archivo /storage. La manipulación del argumento uid provoca un desbordamiento del búfer en la pila. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Esta vulnerabilidad solo afecta a los productos que ya no reciben soporte del fabricante."}], "references": [{"url": "https://vuldb.com/?ctiid.300623", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300623", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.518980", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://witty-maiasaura-083.notion.site/D-link-DAP-1620-check_dws_uid-Vulnerability-1b4b2f2a63618025b049f6e62a1835c0", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.dlink.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
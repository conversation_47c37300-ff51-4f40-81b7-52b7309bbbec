{"cve_id": "CVE-2025-30587", "published_date": "2025-03-24T14:15:30.910", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in shawfactor LH OGP Meta allows Stored XSS. This issue affects LH OGP Meta: from n/a through 1.73."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Shawfactor LH OGP Meta permite XSS almacenado. Este problema afecta a LH OGP Meta desde n/d hasta la versión 1.73."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lh-ogp-meta-tags/vulnerability/wordpress-lh-ogp-meta-plugin-1-73-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
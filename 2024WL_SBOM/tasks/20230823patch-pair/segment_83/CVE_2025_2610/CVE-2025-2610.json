{"cve_id": "CVE-2025-2610", "published_date": "2025-03-21T23:15:21.613", "last_modified_date": "2025-04-01T20:28:16.643", "descriptions": [{"lang": "en", "value": "Improper neutralization of input during web page generation vulnerability in MagnusSolution MagnusBilling (Alarm Module modules) allows authenticated stored cross-site scripting. This vulnerability is associated with program files protected/components/MagnusLog.Php.\n\nThis issue affects MagnusBilling: through 7.3.0."}, {"lang": "es", "value": "Una vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web en MagnusSolution MagnusBilling (Alarm Module modules) permite cross-site scripting almacenado autenticados. Esta vulnerabilidad está asociada a los archivos de programa protected/components/MagnusLog.Php. Este problema afecta a MagnusBilling hasta la versión 7.3.0."}], "references": [{"url": "https://chocapikk.com/posts/2025/magnusbilling/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/magnussolution/magnusbilling7/commit/f0f083c76157e31149ae58342342fb1bf1629e22", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://vulncheck.com/advisories/magnusbilling-alarm-xss", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
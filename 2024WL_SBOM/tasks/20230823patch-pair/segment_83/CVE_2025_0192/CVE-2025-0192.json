{"cve_id": "CVE-2025-0192", "published_date": "2025-03-20T10:15:52.033", "last_modified_date": "2025-03-20T10:15:52.033", "descriptions": [{"lang": "en", "value": "A stored Cross-site Scripting (XSS) vulnerability exists in the latest version of wandb/openui. The vulnerability is present in the edit HTML functionality, where an attacker can inject malicious scripts. When the modified HTML is shared with another user, the XSS payload executes, potentially leading to the theft of user prompt history and other sensitive information."}, {"lang": "es", "value": "Existe una vulnerabilidad de Cross-Site Scripting (XSS) almacenado en la última versión de wandb/openui. Esta vulnerabilidad se presenta en la función de edición de HTML, donde un atacante puede inyectar scripts maliciosos. Al compartir el HTML modificado con otro usuario, se ejecuta el payload XSS, lo que podría provocar el robo del historial de solicitudes del usuario y otra información confidencial."}], "references": [{"url": "https://huntr.com/bounties/5f82c722-b674-456a-8691-a6565bf90e39", "source": "<EMAIL>", "tags": []}]}
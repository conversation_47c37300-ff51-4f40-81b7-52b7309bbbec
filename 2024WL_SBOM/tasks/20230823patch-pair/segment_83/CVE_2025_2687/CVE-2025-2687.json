{"cve_id": "CVE-2025-2687", "published_date": "2025-03-24T06:15:13.397", "last_modified_date": "2025-03-27T18:14:18.840", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul eLearning System 1.0. Affected is an unknown function of the file /user/index.php of the component Image Handler. The manipulation leads to unrestricted upload. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul eLearning System 1.0. Se ve afectada una función desconocida del archivo /user/index.php del componente Image Handler. Esta manipulación permite la carga sin restricciones. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/14", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300708", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300708", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521454", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
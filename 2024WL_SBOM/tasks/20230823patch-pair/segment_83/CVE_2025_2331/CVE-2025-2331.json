{"cve_id": "CVE-2025-2331", "published_date": "2025-03-22T12:15:26.833", "last_modified_date": "2025-03-22T12:15:26.833", "descriptions": [{"lang": "en", "value": "The GiveWP – Donation Plugin and Fundraising Platform plugin for WordPress is vulnerable to Sensitive Information Exposure in all versions up to, and including, 3.22.1 via a misconfigured capability check in the 'permissionsCheck' function. This makes it possible for authenticated attackers, with Subscriber-level access and above, to extract sensitive data including reports detailing donors and donation amounts."}, {"lang": "es", "value": "El complemento GiveWP – Donation Plugin and Fundraising Platform para WordPress es vulnerable a la Exposición de Información Sensible en todas las versiones hasta la 3.22.1 incluida, debido a una comprobación de capacidad mal configurada en la función \"permissionsCheck\". Esto permite a atacantes autenticados, con acceso de suscriptor o superior, extraer datos sensibles, incluyendo informes que detallan los donantes y los montos de las donaciones."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/give/trunk/src/API/Endpoints/Reports/Endpoint.php?rev=3252319#L117", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/give/trunk/src/API/Endpoints/Reports/Endpoint.php?rev=3252319#L227", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/give/trunk/src/API/Endpoints/Reports/Endpoint.php?rev=3252319#L68", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3258797/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b4d9acfb-bb9d-4b00-b439-c7ccea751f8d?source=cve", "source": "<EMAIL>", "tags": []}]}
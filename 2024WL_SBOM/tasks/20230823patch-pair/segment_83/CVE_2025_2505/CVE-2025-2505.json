{"cve_id": "CVE-2025-2505", "published_date": "2025-03-20T08:15:11.873", "last_modified_date": "2025-03-20T08:15:11.873", "descriptions": [{"lang": "en", "value": "The Age Gate plugin for WordPress is vulnerable to Local PHP File Inclusion in all versions up to, and including, 3.5.3 via the 'lang' parameter. This makes it possible for unauthenticated attackers to include and execute arbitrary PHP files on the server, allowing the execution of code in those files. This can be used to bypass access controls, obtain sensitive data, or achieve code execution in cases where images and other “safe” file types can be uploaded and included."}, {"lang": "es", "value": "El complemento Age Gate para WordPress es vulnerable a la inclusión local de archivos PHP en todas las versiones hasta la 3.5.3 incluida, a través del parámetro 'lang'. Esto permite a atacantes no autenticados incluir y ejecutar archivos PHP arbitrarios en el servidor, lo que permite la ejecución de código en dichos archivos. Esto puede utilizarse para eludir los controles de acceso, obtener datos confidenciales o ejecutar código cuando se pueden subir e incluir imágenes y otros tipos de archivos \"seguros\"."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/age-gate/trunk/vendor/agegate/common/src/Settings.php#L27", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3258075/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d6ac2996-098f-474c-b44e-78d5af7b503a?source=cve", "source": "<EMAIL>", "tags": []}]}
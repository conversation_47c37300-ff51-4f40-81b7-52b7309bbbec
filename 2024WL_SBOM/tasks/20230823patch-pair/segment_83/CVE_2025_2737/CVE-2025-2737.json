{"cve_id": "CVE-2025-2737", "published_date": "2025-03-25T06:15:41.493", "last_modified_date": "2025-05-06T19:39:08.970", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Old Age Home Management System 1.0. It has been classified as critical. This affects an unknown part of the file /admin/contactus.php. The manipulation of the argument pagetitle leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Old Age Home Management System 1.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/contactus.php. La manipulación del argumento pagetitle provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/X-X-007/cve/issues/1", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300759", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300759", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.522898", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-23204", "published_date": "2025-03-24T16:15:56.040", "last_modified_date": "2025-03-27T16:45:46.410", "descriptions": [{"lang": "en", "value": "API Platform Core is a system to create hypermedia-driven REST and GraphQL APIs. Starting in version 3.3.8, a security check that gets called after GraphQl resolvers is always replaced by another one as there's no break in a clause. As this falls back to `security`, the impact is there only when there's only a security after resolver and none inside security. Version 3.3.15 contains a patch for the issue."}, {"lang": "es", "value": "API Platform Core es un sistema para crear APIs REST y GraphQL basadas en hipermedia. A partir de la versión 3.3.8, una comprobación de seguridad que se ejecuta después de los resolvers GraphQL siempre se reemplaza por otra, ya que no hay interrupción en una cláusula. Dado que esto se basa en la seguridad, el impacto solo se produce cuando solo hay una seguridad después del resolver y ninguna dentro de la seguridad. La versión 3.3.15 incluye un parche para este problema."}], "references": [{"url": "https://github.com/api-platform/core/commit/dc4fc84ba93e22b4f44a37e90a93c6d079c1c620", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/api-platform/core/pull/6444", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/api-platform/core/pull/6444/files#diff-09e3c2cfe12a2ce65bd6c983c7ca6bfcf783f852b8d0554bb938e8ebf5e5fa65R56", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/api-platform/core/security/advisories/GHSA-7mxx-3cgm-xxv3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/soyuka/core/blob/7e2e8f9ff322ac5f6eb5f65baf432bffdca0fd51/src/Symfony/Security/State/AccessCheckerProvider.php#L49-L57", "source": "<EMAIL>", "tags": []}]}
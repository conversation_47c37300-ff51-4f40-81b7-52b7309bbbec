{"cve_id": "CVE-2025-2757", "published_date": "2025-03-25T10:15:16.887", "last_modified_date": "2025-07-17T21:46:34.960", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in Open Asset Import Library Assimp 5.4.3. This vulnerability affects the function AI_MD5_PARSE_STRING_IN_QUOTATION of the file code/AssetLib/MD5/MD5Parser.cpp of the component MD5 File Handler. The manipulation of the argument data leads to heap-based buffer overflow. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se detectó una vulnerabilidad crítica en Open Asset Import Library Assimp 5.4.3. Esta vulnerabilidad afecta a la función AI_MD5_PARSE_STRING_IN_QUOTATION del archivo code/AssetLib/MD5/MD5Parser.cpp del componente MD5 File Handler. La manipulación de los datos de los argumentos provoca un desbordamiento del búfer basado en el montón. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6019", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6019#issue-2877376386", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.300862", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300862", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.517817", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
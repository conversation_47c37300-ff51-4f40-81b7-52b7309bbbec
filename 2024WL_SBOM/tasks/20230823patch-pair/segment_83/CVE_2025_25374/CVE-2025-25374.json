{"cve_id": "CVE-2025-25374", "published_date": "2025-03-25T21:15:42.250", "last_modified_date": "2025-04-01T18:46:33.137", "descriptions": [{"lang": "en", "value": "In NASA cFS (Core Flight System) Aquila, it is possible to put the onboard software in a state that will prevent the launch of any external application, causing a platform denial of service."}, {"lang": "es", "value": "En NASA cFS (Core Flight System) Aquila, es posible poner el software de a bordo en un estado que impida el lanzamiento de cualquier aplicación externa, provocando una denegación de servicio de la plataforma."}], "references": [{"url": "https://visionspace.com/nasa-cfs-version-aquila-software-vulnerability-assessment/", "source": "<EMAIL>", "tags": ["Exploit", "Technical Description"]}]}
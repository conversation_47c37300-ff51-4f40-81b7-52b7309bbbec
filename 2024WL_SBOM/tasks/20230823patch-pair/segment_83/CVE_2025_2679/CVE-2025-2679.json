{"cve_id": "CVE-2025-2679", "published_date": "2025-03-24T03:15:15.553", "last_modified_date": "2025-06-04T15:19:35.357", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Bank Locker Management System 1.0. It has been classified as critical. Affected is an unknown function of the file /contact-us.php. The manipulation of the argument pagetitle leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Bank Locker Management System 1.0. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /contact-us.php. La manipulación del argumento pagetitle provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cyber/CVE/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.300696", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.300696", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.521447", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
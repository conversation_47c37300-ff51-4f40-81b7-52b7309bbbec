{"cve_id": "CVE-2025-1970", "published_date": "2025-03-22T12:15:25.797", "last_modified_date": "2025-07-09T17:57:31.420", "descriptions": [{"lang": "en", "value": "The Export and Import Users and Customers plugin for WordPress is vulnerable to Server-Side Request Forgery in all versions up to, and including, 2.6.2 via the validate_file() function. This makes it possible for authenticated attackers, with Administrator-level access and above, to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Export and Import Users and Customers para WordPress es vulnerable a Server-Side Request Forgery en todas las versiones hasta la 2.6.2 incluida, mediante la función validate_file(). Esto permite a atacantes autenticados, con acceso de administrador o superior, realizar solicitudes web a ubicaciones arbitrarias desde la aplicación web y utilizarlas para consultar y modificar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/users-customers-import-export-for-wp-woocommerce/trunk/admin/modules/import/classes/class-import-ajax.php#L175", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3259688/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/users-customers-import-export-for-wp-woocommerce/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5a4d7d40-8e0e-4251-8e25-3fd4ebd3a93e?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-30593", "published_date": "2025-03-24T14:15:31.620", "last_modified_date": "2025-03-27T16:44:44.143", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in samsk Include URL allows Stored XSS. This issue affects Include URL: from n/a through 0.3.5."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en samsk Include URL permite XSS almacenado. Este problema afecta a la URL de inclusión desde n/d hasta la versión 0.3.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/include-url/vulnerability/wordpress-include-url-0-3-5-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0723", "published_date": "2025-03-22T05:15:38.053", "last_modified_date": "2025-03-27T00:47:32.027", "descriptions": [{"lang": "en", "value": "The ProfileGrid – User Profiles, Groups and Communities plugin for WordPress is vulnerable to blind and time-based SQL Injections via the rid and search parameters in all versions up to, and including, ******* due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento ProfileGrid – User Profiles, Groups and Communities para WordPress es vulnerable a inyecciones SQL ciegas y temporales mediante los parámetros rid y search en todas las versiones hasta la ******* incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, añadir consultas SQL adicionales a las consultas existentes, las cuales pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/profilegrid-user-profiles-groups-and-communities/tags/*******/includes/class-profile-magic-chat-system.php#L39", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/profilegrid-user-profiles-groups-and-communities/tags/*******/includes/class-profile-magic-request.php#L2379", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/140fa6e8-4381-4df2-af62-44d40b116daf?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
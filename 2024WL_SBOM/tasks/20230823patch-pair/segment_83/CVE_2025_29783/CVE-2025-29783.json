{"cve_id": "CVE-2025-29783", "published_date": "2025-03-19T16:15:32.477", "last_modified_date": "2025-07-01T20:52:17.273", "descriptions": [{"lang": "en", "value": "vLLM is a high-throughput and memory-efficient inference and serving engine for LLMs. When vLLM is configured to use Mooncake, unsafe deserialization exposed directly over ZMQ/TCP on all network interfaces will allow attackers to execute remote code on distributed hosts. This is a remote code execution vulnerability impacting any deployments using Mooncake to distribute KV across distributed hosts. This vulnerability is fixed in 0.8.0."}, {"lang": "es", "value": "vLLM es un motor de inferencia y servicio de alto rendimiento y eficiente en el uso de memoria para LLM. Cuando vLLM se configura para usar Mooncake, la deserialización insegura expuesta directamente a través de ZMQ/TCP en todas las interfaces de red permitirá a los atacantes ejecutar código remoto en hosts distribuidos. Esta vulnerabilidad de ejecución remota de código afecta a cualquier implementación que use Mooncake para distribuir KV entre hosts distribuidos. Esta vulnerabilidad se corrigió en la versión 0.8.0."}], "references": [{"url": "https://github.com/vllm-project/vllm/commit/288ca110f68d23909728627d3100e5a8db820aa2", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/vllm-project/vllm/pull/14228", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-x3m8-f7g5-qhm7", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
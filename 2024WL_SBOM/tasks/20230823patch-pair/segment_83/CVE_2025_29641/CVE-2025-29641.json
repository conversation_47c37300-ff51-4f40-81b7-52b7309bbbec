{"cve_id": "CVE-2025-29641", "published_date": "2025-03-21T15:15:42.473", "last_modified_date": "2025-04-01T20:23:16.657", "descriptions": [{"lang": "en", "value": "Phpgurukul Vehicle Record Management System v1.0 is vulnerable to SQL Injection in /index.php via the 'searchinputdata' parameter."}, {"lang": "es", "value": "Phpgurukul Vehicle Record Management System v1.0 es vulnerable a la inyección SQL en /index.php a través del parámetro 'searchinputdata'."}], "references": [{"url": "https://github.com/Pei4AN/CVE/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}
{"cve_id": "CVE-2025-2545", "published_date": "2025-05-05T12:15:16.170", "last_modified_date": "2025-05-29T11:15:20.477", "descriptions": [{"lang": "en", "value": "Vulnerability in Best Practical Solutions, LLC's Request Tracker prior to v5.0.8, where the Triple DES (3DES) cryptographic algorithm is used to protect emails sent with S/MIME encryption. Triple DES is considered obsolete and insecure due to its susceptibility to birthday attacks, which could compromise the confidentiality of encrypted messages."}, {"lang": "es", "value": "Vulnerabilidad en las versiones de Request Tracker anteriores a 5.0.8 de Best Practical Solutions, LLC,, donde se utiliza el algoritmo criptográfico Triple DES (3DES) para proteger los correos enviados con cifrado S/MIME. El algoritmo Triple DES se considera obsoleto e inseguro debido a su susceptibilidad a ataques de cumpleaños, lo que podría comprometer la confidencialidad de los mensajes cifrados."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/cryptographic-algorithm-not-recommended-request-tracker-best-practical", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.bestpractical.com/release-notes/rt/4.4.8", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://docs.bestpractical.com/release-notes/rt/5.0.8", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
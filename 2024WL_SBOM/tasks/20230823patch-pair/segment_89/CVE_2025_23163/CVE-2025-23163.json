{"cve_id": "CVE-2025-23163", "published_date": "2025-05-01T13:15:52.273", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: vlan: don't propagate flags on open\n\nWith the device instance lock, there is now a possibility of a deadlock:\n\n[    1.211455] ============================================\n[    1.211571] WARNING: possible recursive locking detected\n[    1.211687] 6.14.0-rc5-01215-g032756b4ca7a-dirty #5 Not tainted\n[    1.211823] --------------------------------------------\n[    1.211936] ip/184 is trying to acquire lock:\n[    1.212032] ffff8881024a4c30 (&dev->lock){+.+.}-{4:4}, at: dev_set_allmulti+0x4e/0xb0\n[    1.212207]\n[    1.212207] but task is already holding lock:\n[    1.212332] ffff8881024a4c30 (&dev->lock){+.+.}-{4:4}, at: dev_open+0x50/0xb0\n[    1.212487]\n[    1.212487] other info that might help us debug this:\n[    1.212626]  Possible unsafe locking scenario:\n[    1.212626]\n[    1.212751]        CPU0\n[    1.212815]        ----\n[    1.212871]   lock(&dev->lock);\n[    1.212944]   lock(&dev->lock);\n[    1.213016]\n[    1.213016]  *** DEADLOCK ***\n[    1.213016]\n[    1.213143]  May be due to missing lock nesting notation\n[    1.213143]\n[    1.213294] 3 locks held by ip/184:\n[    1.213371]  #0: ffffffff838b53e0 (rtnl_mutex){+.+.}-{4:4}, at: rtnl_nets_lock+0x1b/0xa0\n[    1.213543]  #1: ffffffff84e5fc70 (&net->rtnl_mutex){+.+.}-{4:4}, at: rtnl_nets_lock+0x37/0xa0\n[    1.213727]  #2: ffff8881024a4c30 (&dev->lock){+.+.}-{4:4}, at: dev_open+0x50/0xb0\n[    1.213895]\n[    1.213895] stack backtrace:\n[    1.213991] CPU: 0 UID: 0 PID: 184 Comm: ip Not tainted 6.14.0-rc5-01215-g032756b4ca7a-dirty #5\n[    1.213993] Hardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS Arch Linux 1.16.3-1-1 04/01/2014\n[    1.213994] Call Trace:\n[    1.213995]  <TASK>\n[    1.213996]  dump_stack_lvl+0x8e/0xd0\n[    1.214000]  print_deadlock_bug+0x28b/0x2a0\n[    1.214020]  lock_acquire+0xea/0x2a0\n[    1.214027]  __mutex_lock+0xbf/0xd40\n[    1.214038]  dev_set_allmulti+0x4e/0xb0 # real_dev->flags & IFF_ALLMULTI\n[    1.214040]  vlan_dev_open+0xa5/0x170 # ndo_open on vlandev\n[    1.214042]  __dev_open+0x145/0x270\n[    1.214046]  __dev_change_flags+0xb0/0x1e0\n[    1.214051]  netif_change_flags+0x22/0x60 # IFF_UP vlandev\n[    1.214053]  dev_change_flags+0x61/0xb0 # for each device in group from dev->vlan_info\n[    1.214055]  vlan_device_event+0x766/0x7c0 # on netdevsim0\n[    1.214058]  notifier_call_chain+0x78/0x120\n[    1.214062]  netif_open+0x6d/0x90\n[    1.214064]  dev_open+0x5b/0xb0 # locks netdevsim0\n[    1.214066]  bond_enslave+0x64c/0x1230\n[    1.214075]  do_set_master+0x175/0x1e0 # on netdevsim0\n[    1.214077]  do_setlink+0x516/0x13b0\n[    1.214094]  rtnl_newlink+0xaba/0xb80\n[    1.214132]  rtnetlink_rcv_msg+0x440/0x490\n[    1.214144]  netlink_rcv_skb+0xeb/0x120\n[    1.214150]  netlink_unicast+0x1f9/0x320\n[    1.214153]  netlink_sendmsg+0x346/0x3f0\n[    1.214157]  __sock_sendmsg+0x86/0xb0\n[    1.214160]  ____sys_sendmsg+0x1c8/0x220\n[    1.214164]  ___sys_sendmsg+0x28f/0x2d0\n[    1.214179]  __x64_sys_sendmsg+0xef/0x140\n[    1.214184]  do_syscall_64+0xec/0x1d0\n[    1.214190]  entry_SYSCALL_64_after_hwframe+0x77/0x7f\n[    1.214191] RIP: 0033:0x7f2d1b4a7e56\n\nDevice setup:\n\n     netdevsim0 (down)\n     ^        ^\n  bond        netdevsim1.100@netdevsim1 allmulticast=on (down)\n\nWhen we enslave the lower device (netdevsim0) which has a vlan, we\npropagate vlan's allmuti/promisc flags during ndo_open. This causes\n(re)locking on of the real_dev.\n\nPropagate allmulti/promisc on flags change, not on the open. There\nis a slight semantics change that vlans that are down now propagate\nthe flags, but this seems unlikely to result in the real issues.\n\nReproducer:\n\n  echo 0 1 > /sys/bus/netdevsim/new_device\n\n  dev_path=$(ls -d /sys/bus/netdevsim/devices/netdevsim0/net/*)\n  dev=$(echo $dev_path | rev | cut -d/ -f1 | rev)\n\n  ip link set dev $dev name netdevsim0\n  ip link set dev netdevsim0 up\n\n  ip link add link netdevsim0 name netdevsim0.100 type vlan id 100\n  ip link set dev netdevsim0.100 allm\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: vlan: no propagar indicadores al abrir Con el bloqueo de la instancia del dispositivo, ahora existe la posibilidad de un interbloqueo: [ 1.211455] =============================================== [ 1.211571] ADVERTENCIA: posible bloqueo recursivo detectado [ 1.211687] 6.14.0-rc5-01215-g032756b4ca7a-dirty #5 No contaminado [ 1.211823] -------------------------------------------- [ 1.211936] ip/184 is trying to acquire lock: [ 1.212032] ffff8881024a4c30 (&amp;dev-&gt;lock){+.+.}-{4:4}, at: dev_set_allmulti+0x4e/0xb0 [ 1.212207] [ 1.212207] but task is already holding lock: [ 1.212332] ffff8881024a4c30 (&amp;dev-&gt;lock){+.+.}-{4:4}, at: dev_open+0x50/0xb0 [ 1.212487] [ 1.212487] other info that might help us debug this: [ 1.212626] Possible unsafe locking scenario: [ 1.212626] [ 1.212751] CPU0 [ 1.212815] ---- [ 1.212871] lock(&amp;dev-&gt;lock); [ 1.212944] lock(&amp;dev-&gt;lock); [ 1.213016] [ 1.213016] *** DEADLOCK *** [ 1.213016] [ 1.213143] May be due to missing lock nesting notation [ 1.213143] [ 1.213294] 3 locks held by ip/184: [ 1.213371] #0: ffffffff838b53e0 (rtnl_mutex){+.+.}-{4:4}, at: rtnl_nets_lock+0x1b/0xa0 [ 1.213543] #1: ffffffff84e5fc70 (&amp;net-&gt;rtnl_mutex){+.+.}-{4:4}, at: rtnl_nets_lock+0x37/0xa0 [ 1.213727] #2: ffff8881024a4c30 (&amp;dev-&gt;lock){+.+.}-{4:4}, at: dev_open+0x50/0xb0 [ 1.213895] [ 1.213895] stack backtrace: [ 1.213991] CPU: 0 UID: 0 PID: 184 Comm: ip Not tainted 6.14.0-rc5-01215-g032756b4ca7a-dirty #5 [ 1.213993] Hardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS Arch Linux 1.16.3-1-1 04/01/2014 [ 1.213994] Call Trace: [ 1.213995]  [ 1.213996] dump_stack_lvl+0x8e/0xd0 [ 1.214000] print_deadlock_bug+0x28b/0x2a0 [ 1.214020] lock_acquire+0xea/0x2a0 [ 1.214027] __mutex_lock+0xbf/0xd40 [ 1.214038] dev_set_allmulti+0x4e/0xb0 # real_dev-&gt;flags &amp; IFF_ALLMULTI [ 1.214040] vlan_dev_open+0xa5/0x170 # ndo_open on vlandev [ 1.214042] __dev_open+0x145/0x270 [ 1.214046] __dev_change_flags+0xb0/0x1e0 [ 1.214051] netif_change_flags+0x22/0x60 # IFF_UP vlandev [ 1.214053] dev_change_flags+0x61/0xb0 # for each device in group from dev-&gt;vlan_info [ 1.214055] vlan_device_event+0x766/0x7c0 # on netdevsim0 [ 1.214058] notifier_call_chain+0x78/0x120 [ 1.214062] netif_open+0x6d/0x90 [ 1.214064] dev_open+0x5b/0xb0 # locks netdevsim0 [ 1.214066] bond_enslave+0x64c/0x1230 [ 1.214075] do_set_master+0x175/0x1e0 # on netdevsim0 [ 1.214077] do_setlink+0x516/0x13b0 [ 1.214094] rtnl_newlink+0xaba/0xb80 [ 1.214132] rtnetlink_rcv_msg+0x440/0x490 [ 1.214144] netlink_rcv_skb+0xeb/0x120 [ 1.214150] netlink_unicast+0x1f9/0x320 [ 1.214153] netlink_sendmsg+0x346/0x3f0 [ 1.214157] __sock_sendmsg+0x86/0xb0 [ 1.214160] ____sys_sendmsg+0x1c8/0x220 [ 1.214164] ___sys_sendmsg+0x28f/0x2d0 [ 1.214179] __x64_sys_sendmsg+0xef/0x140 [ 1.214184] do_syscall_64+0xec/0x1d0 [ 1.214190] entry_SYSCALL_64_after_hwframe+0x77/0x7f [ 1.214191] RIP: 0033:0x7f2d1b4a7e56 Device setup: netdevsim0 (down) ^ ^ bond netdevsim1.100@netdevsim1 allmulticast=on (down)  Al esclavizar el dispositivo inferior (netdevsim0) que tiene una VLAN, propagamos los indicadores allmuti/promisc de la VLAN durante ndo_open. Esto provoca el (re)bloqueo de real_dev. Allmulti/promisc se propaga cuando cambian los indicadores, no cuando están abiertos. Hay un ligero cambio semántico: las VLAN inactivas ahora propagan los indicadores, pero es poco probable que esto cause los problemas reales. Reproductor: echo 0 1 &gt; /sys/bus/netdevsim/new_device dev_path=$(ls -d /sys/bus/netdevsim/devices/netdevsim0/net/*) dev=$(echo $dev_path | rev | cut -d/ -f1 | rev) ip link set dev $dev name netdevsim0 ip link set dev netdevsim0 up ip link add link netdevsim0 name netdevsim0.100 type vlan id 100 ip link set dev netdevsim0.100 allm ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/27b918007d96402aba10ed52a6af8015230f1793", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/299d7d27af6b5844cda06a0fdfa635705e1bc50f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/523fa0979d842443aa14b80002e45b471cbac137", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/538b43aa21e3b17c110104efd218b966d2eda5f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/53fb25e90c0a503a17c639341ba5e755cb2feb5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8980018a9806743d9b80837330d46f06ecf78516", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a32f1d4f1f4c9d978698f3c718621f6198f2e7ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b1e3eeb037256a2f1206a8d69810ec47eb152026", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d537859e56bcc3091805c524484a4c85386b3cc8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
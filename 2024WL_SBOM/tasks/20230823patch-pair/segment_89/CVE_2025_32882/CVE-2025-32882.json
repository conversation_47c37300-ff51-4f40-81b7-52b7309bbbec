{"cve_id": "CVE-2025-32882", "published_date": "2025-05-01T18:15:55.127", "last_modified_date": "2025-06-20T16:52:25.717", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna v1 devices with app 5.5.3 and firmware 0.25.5. The app uses a custom implementation of encryption without any additional integrity checking mechanisms. This leaves messages malleable to an attacker that can access the message."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna v1 con la aplicación 5.5.3 y el firmware 0.25.5. La aplicación utiliza una implementación personalizada de cifrado sin mecanismos adicionales de verificación de integridad. Esto permite que los mensajes sean vulnerables a un atacante que pueda acceder a ellos."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
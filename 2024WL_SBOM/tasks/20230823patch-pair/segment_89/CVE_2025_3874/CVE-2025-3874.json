{"cve_id": "CVE-2025-3874", "published_date": "2025-05-01T12:15:17.400", "last_modified_date": "2025-05-06T15:39:29.083", "descriptions": [{"lang": "en", "value": "The WordPress Simple Shopping Cart plugin for WordPress is vulnerable to Insecure Direct Object Reference in all versions up to, and including, 5.1.3 due to lack of randomization of a user controlled key. This makes it possible for unauthenticated attackers to access customer shopping carts and edit product links, add or delete products, and discover coupon codes."}, {"lang": "es", "value": "El complemento Simple Shopping Cart para WordPress es vulnerable a una Referencia Directa a Objetos Insegura en todas las versiones hasta la 5.1.3 incluida, debido a la falta de aleatorización de una clave controlada por el usuario. Esto permite a atacantes no autenticados acceder a los carritos de compra de los clientes, editar enlaces de productos, añadir o eliminar productos y descubrir códigos de cupón."}], "references": [{"url": "https://developer.wordpress.org/reference/functions/wp_generate_password/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/includes/class-wpsc-cart.php#L32", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/includes/class-wpsc-cart.php#L68", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/wp_shopping_cart.php#L158", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/wp_shopping_cart.php#L265", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/wp_shopping_cart.php#L525", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3284572/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.tipsandtricks-hq.com/ecommerce/wp-shopping-cart", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4fed59bf-885b-4a06-aff2-8e5ab5f83ba7?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
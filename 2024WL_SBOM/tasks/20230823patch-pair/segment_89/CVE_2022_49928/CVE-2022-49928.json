{"cve_id": "CVE-2022-49928", "published_date": "2025-05-01T15:16:18.783", "last_modified_date": "2025-05-07T13:28:44.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nSUNRPC: Fix null-ptr-deref when xps sysfs alloc failed\n\nThere is a null-ptr-deref when xps sysfs alloc failed:\n  BUG: KASAN: null-ptr-deref in sysfs_do_create_link_sd+0x40/0xd0\n  Read of size 8 at addr 0000000000000030 by task gssproxy/457\n\n  CPU: 5 PID: 457 Comm: gssproxy Not tainted 6.0.0-09040-g02357b27ee03 #9\n  Call Trace:\n   <TASK>\n   dump_stack_lvl+0x34/0x44\n   kasan_report+0xa3/0x120\n   sysfs_do_create_link_sd+0x40/0xd0\n   rpc_sysfs_client_setup+0x161/0x1b0\n   rpc_new_client+0x3fc/0x6e0\n   rpc_create_xprt+0x71/0x220\n   rpc_create+0x1d4/0x350\n   gssp_rpc_create+0xc3/0x160\n   set_gssp_clnt+0xbc/0x140\n   write_gssp+0x116/0x1a0\n   proc_reg_write+0xd6/0x130\n   vfs_write+0x177/0x690\n   ksys_write+0xb9/0x150\n   do_syscall_64+0x35/0x80\n   entry_SYSCALL_64_after_hwframe+0x46/0xb0\n\nWhen the xprt_switch sysfs alloc failed, should not add xprt and\nswitch sysfs to it, otherwise, maybe null-ptr-deref; also initialize\nthe 'xps_sysfs' to NULL to avoid oops when destroy it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: SUNRPC: Se corrige null-ptr-deref cuando falla la asignación de sysfs en XPS. Hay un null-ptr-deref cuando falla la asignación de sysfs en XPS: ERROR: KASAN: null-ptr-deref en sysfs_do_create_link_sd+0x40/0xd0 Lectura de tamaño 8 en la dirección 0000000000000030 por la tarea gssproxy/457 CPU: 5 PID: 457 Comm: gssproxy No contaminado 6.0.0-09040-g02357b27ee03 #9 Rastreo de llamadas:   dump_stack_lvl+0x34/0x44 kasan_report+0xa3/0x120 sysfs_do_create_link_sd+0x40/0xd0 rpc_sysfs_client_setup+0x161/0x1b0 rpc_new_client+0x3fc/0x6e0 rpc_create_xprt+0x71/0x220 rpc_create+0x1d4/0x350 gssp_rpc_create+0xc3/0x160 set_gssp_clnt+0xbc/0x140 write_gssp+0x116/0x1a0 proc_reg_write+0xd6/0x130 vfs_write+0x177/0x690 ksys_write+0xb9/0x150 do_syscall_64+0x35/0x80 entry_SYSCALL_64_after_hwframe+0x46/0xb0 Cuando la asignación sysfs xprt_switch falla, no se debe agregar xprt y switch sysfs a él, de lo contrario, tal vez null-ptr-deref; también inicialice 'xps_sysfs' a NULL para evitar errores al destruirlo."}], "references": [{"url": "https://git.kernel.org/stable/c/7b189b0aa8dab14b49c31c65af8a982e96e25b62", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cbdeaee94a415800c65a8c3fa04d9664a8b8fb3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d59722d088a9d86ce6d9d39979e5d1d669d249f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
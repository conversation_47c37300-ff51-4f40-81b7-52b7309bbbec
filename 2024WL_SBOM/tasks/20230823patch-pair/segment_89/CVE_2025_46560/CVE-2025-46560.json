{"cve_id": "CVE-2025-46560", "published_date": "2025-04-30T01:15:52.097", "last_modified_date": "2025-05-28T19:15:56.887", "descriptions": [{"lang": "en", "value": "vLLM is a high-throughput and memory-efficient inference and serving engine for LLMs. Versions starting from 0.8.0 and prior to 0.8.5 are affected by a critical performance vulnerability in the input preprocessing logic of the multimodal tokenizer. The code dynamically replaces placeholder tokens (e.g., <|audio_|>, <|image_|>) with repeated tokens based on precomputed lengths. Due to ​​inefficient list concatenation operations​​, the algorithm exhibits ​​quadratic time complexity (O(n²))​​, allowing malicious actors to trigger resource exhaustion via specially crafted inputs. This issue has been patched in version 0.8.5."}, {"lang": "es", "value": "vLLM es un motor de inferencia y servicio de alto rendimiento y eficiente en memoria para LLM. Las versiones a partir de la 0.8.0 y anteriores a la 0.8.5 se ven afectadas por una vulnerabilidad crítica de rendimiento en la lógica de preprocesamiento de entrada del tokenizador multimodal. El código reemplaza dinámicamente los tokens de marcador de posición (p. ej., &lt;|audio_|&gt;, &lt;|image_|&gt;) con tokens repetidos basados ??en longitudes precalculadas. Debido a las ineficientes operaciones de concatenación de listas, el algoritmo presenta una complejidad temporal cuadrática (O(n²)), lo que permite a los actores maliciosos activar el agotamiento de recursos mediante entradas especialmente manipuladas. Este problema se ha corregido en la versión 0.8.5."}], "references": [{"url": "https://github.com/vllm-project/vllm/blob/8cac35ba435906fb7eb07e44fe1a8c26e8744f4e/vllm/model_executor/models/phi4mm.py#L1182-L1197", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-vc6m-hm49-g9qg", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
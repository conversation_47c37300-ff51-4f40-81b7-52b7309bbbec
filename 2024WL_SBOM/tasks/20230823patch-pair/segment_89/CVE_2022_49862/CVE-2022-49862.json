{"cve_id": "CVE-2022-49862", "published_date": "2025-05-01T15:16:11.097", "last_modified_date": "2025-05-07T13:22:46.437", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntipc: fix the msg->req tlv len check in tipc_nl_compat_name_table_dump_header\n\nThis is a follow-up for commit 974cb0e3e7c9 (\"tipc: fix uninit-value\nin tipc_nl_compat_name_table_dump\") where it should have type casted\nsizeof(..) to int to work when TLV_GET_DATA_LEN() returns a negative\nvalue.\n\nsyzbot reported a call trace because of it:\n\n  BUG: KMSAN: uninit-value in ...\n   tipc_nl_compat_name_table_dump+0x841/0xea0 net/tipc/netlink_compat.c:934\n   __tipc_nl_compat_dumpit+0xab2/0x1320 net/tipc/netlink_compat.c:238\n   tipc_nl_compat_dumpit+0x991/0xb50 net/tipc/netlink_compat.c:321\n   tipc_nl_compat_recv+0xb6e/0x1640 net/tipc/netlink_compat.c:1324\n   genl_family_rcv_msg_doit net/netlink/genetlink.c:731 [inline]\n   genl_family_rcv_msg net/netlink/genetlink.c:775 [inline]\n   genl_rcv_msg+0x103f/0x1260 net/netlink/genetlink.c:792\n   netlink_rcv_skb+0x3a5/0x6c0 net/netlink/af_netlink.c:2501\n   genl_rcv+0x3c/0x50 net/netlink/genetlink.c:803\n   netlink_unicast_kernel net/netlink/af_netlink.c:1319 [inline]\n   netlink_unicast+0xf3b/0x1270 net/netlink/af_netlink.c:1345\n   netlink_sendmsg+0x1288/0x1440 net/netlink/af_netlink.c:1921\n   sock_sendmsg_nosec net/socket.c:714 [inline]\n   sock_sendmsg net/socket.c:734 [inline]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tipc: corrige la comprobación de longitud del tlv msg-&gt;req en tipc_nl_compat_name_table_dump_header Este es un seguimiento del commit 974cb0e3e7c9 (\"tipc: corrige uninit-value en tipc_nl_compat_name_table_dump\") donde debería haber convertido el tipo sizeof(..) a int para que funcione cuando TLV_GET_DATA_LEN() devuelve un valor negativo. syzbot informó un seguimiento de llamadas debido a esto: ERROR: KMSAN: valor no inicializado en ... tipc_nl_compat_name_table_dump+0x841/0xea0 net/tipc/netlink_compat.c:934 __tipc_nl_compat_dumpit+0xab2/0x1320 net/tipc/netlink_compat.c:238 tipc_nl_compat_dumpit+0x991/0xb50 net/tipc/netlink_compat.c:321 tipc_nl_compat_recv+0xb6e/0x1640 net/tipc/netlink_compat.c:1324 genl_family_rcv_msg_doit net/netlink/genetlink.c:731 [inline] genl_family_rcv_msg net/netlink/genetlink.c:775 [inline] genl_rcv_msg+0x103f/0x1260 net/netlink/genetlink.c:792 netlink_rcv_skb+0x3a5/0x6c0 net/netlink/af_netlink.c:2501 genl_rcv+0x3c/0x50 net/netlink/genetlink.c:803 netlink_unicast_kernel net/netlink/af_netlink.c:1319 [inline] netlink_unicast+0xf3b/0x1270 net/netlink/af_netlink.c:1345 netlink_sendmsg+0x1288/0x1440 net/netlink/af_netlink.c:1921 sock_sendmsg_nosec net/socket.c:714 [inline] sock_sendmsg net/socket.c:734 [inline] "}], "references": [{"url": "https://git.kernel.org/stable/c/082707d3df191bf5bb8801d43e4ce3dea39ca173", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1c075b192fe41030457cd4a5f7dea730412bca40", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/301caa06091af4d5cf056ac8249cbda4e6029c6a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/36769b9477491a7af6635863bd950309c1e1b96c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/55a253a6753a603e80b95932ca971ba514aa6ce7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6cee2c60bd168279852ac7dbe54c2b70d1028644", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a0ead1d648df9c456baec832b494513ef405949a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f31dd158580940938f77514b87337a777520185a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53117", "published_date": "2025-05-02T16:15:30.783", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfs: prevent out-of-bounds array speculation when closing a file descriptor\n\nGoogle-Bug-Id: 114199369"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: fs: evitar la especulación de matrices fuera de los límites al cerrar un descriptor de archivo Google-Bug-Id: 114199369"}], "references": [{"url": "https://git.kernel.org/stable/c/3d5d9501b634fd268eb56428cda92cd317752d69", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/609d54441493c99f21c1823dfd66fa7f4c512ff4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6631c8da02cfad96c53b217cf647b511c7f34faf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a759905de9cd6ec9ca08ceadf0920272772ed830", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cec08b7d1ebcd3138d4658b3868ce26aeb1e8e06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eea8e4e056a5ffbeb539a13854c017d5d62c756a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f31cd5da636682caea424fa1c22679016cbfc16b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f8cd8754a03a3748384ee438c572423643c9c315", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
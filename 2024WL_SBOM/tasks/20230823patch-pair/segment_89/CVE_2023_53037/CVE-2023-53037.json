{"cve_id": "CVE-2023-53037", "published_date": "2025-05-02T16:15:22.827", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: mpi3mr: Bad drive in topology results kernel crash\n\nWhen the SAS Transport Layer support is enabled and a device exposed to\nthe OS by the driver fails INQUIRY commands, the driver frees up the memory\nallocated for an internal HBA port data structure. However, in some places,\nthe reference to the freed memory is not cleared. When the firmware sends\nthe Device Info change event for the same device again, the freed memory is\naccessed and that leads to memory corruption and OS crash."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: mpi3mr: Una unidad defectuosa en la topología provoca un bloqueo del kernel. Cuando se habilita la compatibilidad con la capa de transporte SAS y un dispositivo expuesto al sistema operativo por el controlador no cumple con los comandos INQUIRY, el controlador libera la memoria asignada a una estructura de datos de puerto HBA interno. Sin embargo, en algunos lugares, la referencia a la memoria liberada no se borra. Cuando el firmware vuelve a enviar el evento de cambio de información del dispositivo para el mismo dispositivo, se accede a la memoria liberada, lo que provoca la corrupción de la memoria y el bloqueo del sistema operativo."}], "references": [{"url": "https://git.kernel.org/stable/c/1f822ae8fb2a20fffa71e9bfa9b203c03d72d3ba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e45183978d64699df639e795235433a60f35047", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aa11e4b6cdb403b9fdef6939550f6b36dd61624d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
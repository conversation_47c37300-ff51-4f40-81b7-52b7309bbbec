{"cve_id": "CVE-2025-40622", "published_date": "2025-05-06T11:15:51.917", "last_modified_date": "2025-05-13T19:07:51.707", "descriptions": [{"lang": "en", "value": "SQL injection in TCMAN's GIM v11. This vulnerability allows an unauthenticated attacker to inject an SQL statement to obtain, update and delete all information in the database. This vulnerability was found in each of the following parameters according to the vulnerability identifier ‘username’ parameter of the ‘GetLastDatePasswordChange’ endpoint."}, {"lang": "es", "value": "Inyección SQL en GIM v11 de TCMAN. Esta vulnerabilidad permite a un atacante no autenticado inyectar una sentencia SQL para obtener, actualizar y eliminar toda la información de la base de datos. Esta vulnerabilidad se detectó en cada uno de los siguientes parámetros, según el identificador de vulnerabilidad \"username\" del endpoint \"GetLastDatePasswordChange\"."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/multiple-vulnerabilities-tcmans-gim", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
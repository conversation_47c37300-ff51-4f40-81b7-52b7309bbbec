{"cve_id": "CVE-2023-53096", "published_date": "2025-05-02T16:15:28.543", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ninterconnect: fix mem leak when freeing nodes\n\nThe node link array is allocated when adding links to a node but is not\ndeallocated when nodes are destroyed."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: interconexión: se corrige una pérdida de memoria al liberar nodos. La matriz de enlaces de nodos se asigna cuando se agregan enlaces a un nodo, pero no se desasigna cuando se destruyen los nodos."}], "references": [{"url": "https://git.kernel.org/stable/c/2e0b13a1827229a02abef97b50ffaf89ba25370a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3167306455d0fbbbcf08cb25651acc527a86a95e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a5904f415e1af72fa8fe6665aa4f554dc2099a95", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c1722e4113281fb34e5b4fb5c5387b17cd39a537", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/efae80ca13faa94457208852825731da44a788ad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1e3a20c60196c37a402c584d0c9de306ba988ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
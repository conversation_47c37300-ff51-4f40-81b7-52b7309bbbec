{"cve_id": "CVE-2025-4153", "published_date": "2025-05-01T07:15:58.847", "last_modified_date": "2025-05-07T20:00:07.240", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Park Ticketing Management System 2.0. Affected by this vulnerability is an unknown functionality of the file /profile.php. The manipulation of the argument adminname leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Park Ticketing Management System 2.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /profile.php. La manipulación del argumento adminname provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/y77-88/myCVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306685", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306685", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560809", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-32885", "published_date": "2025-05-01T18:15:55.593", "last_modified_date": "2025-06-20T16:48:21.597", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna v1 devices with app 5.5.3 and firmware 0.25.5. The app there makes it possible to inject any custom message (into existing v1 networks) with any GID and Callsign via a software defined radio. This can be exploited if the device is being used in an unencrypted environment or if the cryptography has already been compromised."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna v1 con la aplicación 5.5.3 y el firmware 0.25.5. Esta aplicación permite inyectar cualquier mensaje personalizado (en redes v1 existentes) con cualquier GID e indicativo mediante una radio definida por software. Esto puede explotarse si el dispositivo se utiliza en un entorno sin cifrar o si la criptografía ya ha sido comprometida."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2022-49860", "published_date": "2025-05-01T15:16:09.610", "last_modified_date": "2025-05-07T13:31:43.957", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndmaengine: ti: k3-udma-glue: fix memory leak when register device fail\n\nIf device_register() fails, it should call put_device() to give\nup reference, the name allocated in dev_set_name() can be freed\nin callback function kobject_cleanup()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dmaengine: ti: k3-udma-glue: corrige pérdida de memoria cuando falla el registro del dispositivo Si device_register() falla, debe llamar a put_device() para entregar la referencia, el nombre asignado en dev_set_name() se puede liberar en la función de devolución de llamada kobject_cleanup()."}], "references": [{"url": "https://git.kernel.org/stable/c/025eab5189fc7ee223ae9b4bc49d7df196543e53", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1dd27541aa2b95bde71bddd43d73f9c16d73272c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ac2b9f34f02052709aea7b34bb2a165e1853eb41", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
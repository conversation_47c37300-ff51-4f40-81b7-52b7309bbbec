{"cve_id": "CVE-2025-37778", "published_date": "2025-05-01T14:15:41.617", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: Fix dangling pointer in krb_authenticate\n\nkrb_authenticate frees sess->user and does not set the pointer\nto NULL. It calls ksmbd_krb5_authenticate to reinitialise\nsess->user but that function may return without doing so. If\nthat happens then smb2_sess_setup, which calls krb_authenticate,\nwill be accessing free'd memory when it later uses sess->user."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: Se corrige el puntero colgante en krb_authenticate. krb_authenticate libera sess-&gt;user y no establece el puntero en NULL. Llama a ksmbd_krb5_authenticate para reinicializar sess-&gt;user, pero es posible que esta función no lo retorne. Si esto ocurre, smb2_sess_setup, que llama a krb_authenticate, accederá a la memoria liberada cuando utilice posteriormente sess-&gt;user."}], "references": [{"url": "https://git.kernel.org/stable/c/1db2451de23e98bc864c6a6e52aa0d82c91cb325", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1e440d5b25b7efccb3defe542a73c51005799a5f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6e30c0e10210c714f3d4453dc258d4abcc70364e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d5b554bc8d554ed6ddf443d3db2fad9f665cec10", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e83e39a5f6a01a81411a4558a59a10f87aa88dd6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-23151", "published_date": "2025-05-01T13:15:51.003", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbus: mhi: host: Fix race between unprepare and queue_buf\n\nA client driver may use mhi_unprepare_from_transfer() to quiesce\nincoming data during the client driver's tear down. The client driver\nmight also be processing data at the same time, resulting in a call to\nmhi_queue_buf() which will invoke mhi_gen_tre(). If mhi_gen_tre() runs\nafter mhi_unprepare_from_transfer() has torn down the channel, a panic\nwill occur due to an invalid dereference leading to a page fault.\n\nThis occurs because mhi_gen_tre() does not verify the channel state\nafter locking it. Fix this by having mhi_gen_tre() confirm the channel\nstate is valid, or return error to avoid accessing deinitialized data.\n\n[mani: added stable tag]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bus: mhi: host: Corrección de la competencia entre unprepare y queue_buf. Un controlador de cliente podría usar mhi_unprepare_from_transfer() para silenciar los datos entrantes durante su desconexión. El controlador de cliente también podría estar procesando datos simultáneamente, lo que resulta en una llamada a mhi_queue_buf(), que invocará mhi_gen_tre(). Si mhi_gen_tre() se ejecuta después de que mhi_unprepare_from_transfer() haya desconectado el canal, se producirá un pánico debido a una desreferencia no válida que provoca un fallo de página. Esto ocurre porque mhi_gen_tre() no verifica el estado del canal después de bloquearlo. Para solucionar esto, haga que mhi_gen_tre() confirme que el estado del canal es válido o devuelva un error para evitar acceder a los datos desinicializados. [mani: etiqueta estable añadida]"}], "references": [{"url": "https://git.kernel.org/stable/c/0686a818d77a431fc3ba2fab4b46bbb04e8c9380", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/178e5657c8fd285125cc6743a81b513bce099760", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3e7ecf181cbdde9753204ada3883ca1704d8702b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5f084993c90d9d0b4a52a349ede5120f992a7ca1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/899d0353ea69681f474b6bc9de32c663b89672da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a77955f7704b2a00385e232cbcc1cb06b5c7a425", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee1fce83ed56450087309b9b74ad9bcb2b010fa6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
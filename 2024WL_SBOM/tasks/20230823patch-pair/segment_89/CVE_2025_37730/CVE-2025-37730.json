{"cve_id": "CVE-2025-37730", "published_date": "2025-05-06T18:15:38.410", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "Improper certificate validation in Logstash's TCP output could lead to a man-in-the-middle (MitM) attack in “client” mode, as hostname verification in TCP output was not being performed when the ssl_verification_mode => full was set."}, {"lang": "es", "value": "Una validación incorrecta del certificado en la salida TCP de Logstash podría provocar un ataque de intermediario (MitM) en modo “client”, ya que no se estaba realizando la verificación del nombre de host en la salida TCP cuando se configuraba ssl_verification_mode =&gt; full."}], "references": [{"url": "https://discuss.elastic.co/t/logstash-8-17-6-8-18-1-and-9-0-1-security-update-esa-2025-08/377869", "source": "<EMAIL>", "tags": []}]}
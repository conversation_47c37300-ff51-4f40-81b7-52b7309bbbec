{"cve_id": "CVE-2022-49925", "published_date": "2025-05-01T15:16:18.337", "last_modified_date": "2025-05-07T13:28:29.707", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRDMA/core: Fix null-ptr-deref in ib_core_cleanup()\n\nKASAN reported a null-ptr-deref error:\n\n  KASAN: null-ptr-deref in range [0x0000000000000118-0x000000000000011f]\n  CPU: 1 PID: 379\n  Hardware name: QEMU Standard PC (i440FX + PIIX, 1996)\n  RIP: 0010:destroy_workqueue+0x2f/0x740\n  RSP: 0018:ffff888016137df8 EFLAGS: 00000202\n  ...\n  Call Trace:\n   ib_core_cleanup+0xa/0xa1 [ib_core]\n   __do_sys_delete_module.constprop.0+0x34f/0x5b0\n   do_syscall_64+0x3a/0x90\n   entry_SYSCALL_64_after_hwframe+0x63/0xcd\n  RIP: 0033:0x7fa1a0d221b7\n  ...\n\nIt is because the fail of roce_gid_mgmt_init() is ignored:\n\n ib_core_init()\n   roce_gid_mgmt_init()\n     gid_cache_wq = alloc_ordered_workqueue # fail\n ...\n ib_core_cleanup()\n   roce_gid_mgmt_cleanup()\n     destroy_workqueue(gid_cache_wq)\n     # destroy an unallocated wq\n\nFix this by catching the fail of roce_gid_mgmt_init() in ib_core_init()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: RDMA/core: Fix null-ptr-deref en ib_core_cleanup() KASAN informó un error null-ptr-deref: KASAN: null-ptr-deref en el rango [0x0000000000000118-0x000000000000011f] CPU: 1 PID: 379 Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996) RIP: 0010:destroy_workqueue+0x2f/0x740 RSP: 0018:ffff888016137df8 EFLAGS: 00000202 ... Seguimiento de llamadas:  ib_core_cleanup+0xa/0xa1 [ib_core] __do_sys_delete_module.constprop.0+0x34f/0x5b0 do_syscall_64+0x3a/0x90 entry_SYSCALL_64_after_hwframe+0x63/0xcd RIP: 0033:0x7fa1a0d221b7 ... It is because the fail of roce_gid_mgmt_init() is ignored: ib_core_init() roce_gid_mgmt_init() gid_cache_wq = alloc_ordered_workqueue # fail ... ib_core_cleanup() roce_gid_mgmt_cleanup() destroy_workqueue(gid_cache_wq) # destroy an unallocated wq Fix this by catching the fail of roce_gid_mgmt_init() in ib_core_init(). "}], "references": [{"url": "https://git.kernel.org/stable/c/07c0d131cc0fe1f3981a42958fc52d573d303d89", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6b3d5dcb12347f3518308c2c9d2cf72453a3e1e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ab817f75e5e0fa58d9be0825da6a7b7d8a1fa1d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/af8fb5a0600e9ae29950e9422a032c3c22649ee5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d360e875c011a005628525bf290322058927e7dc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-46762", "published_date": "2025-05-06T10:15:16.047", "last_modified_date": "2025-05-13T20:25:00.003", "descriptions": [{"lang": "en", "value": "Schema parsing in the parquet-avro module of Apache Parquet 1.15.0 and previous versions allows bad actors to execute arbitrary code.\n\nWhile 1.15.1 introduced a fix to restrict untrusted packages, the default setting of trusted packages still allows malicious classes from these packages to be executed.\n\nThe exploit is only applicable if the client code of parquet-avro uses the \"specific\" or the \"reflect\" models deliberately for reading Parquet files. (\"generic\" model is not impacted)\n\nUsers are recommended to upgrade to 1.15.2 or set the system property \"org.apache.parquet.avro.SERIALIZABLE_PACKAGES\" to an empty string on 1.15.1. Both are sufficient to fix the issue."}, {"lang": "es", "value": "El análisis de esquemas en el módulo parquet-avro de Apache Parquet 1.15.0 y versiones anteriores permite a actores maliciosos ejecutar código arbitrario. Si bien la versión 1.15.1 introdujo una corrección para restringir los paquetes no confiables, la configuración predeterminada de los paquetes confiables aún permite la ejecución de clases maliciosas de estos paquetes. El exploit solo es aplicable si el código cliente de parquet-avro utiliza deliberadamente los modelos \"specific\" o \"reflect\" para leer archivos de Parquet. (El modelo \"genérico\" no se ve afectado). Se recomienda a los usuarios actualizar a la versión 1.15.2 o configurar la propiedad del sistema \"org.apache.parquet.avro.SERIALIZABLE_PACKAGES\" con una cadena vacía en la versión 1.15.1. Ambas opciones son suficientes para solucionar el problema."}], "references": [{"url": "https://lists.apache.org/thread/t7724lpvl110xsbgqwsmrdsns0rhycdp", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/05/02/1", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}]}
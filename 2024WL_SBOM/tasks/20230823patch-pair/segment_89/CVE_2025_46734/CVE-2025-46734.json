{"cve_id": "CVE-2025-46734", "published_date": "2025-05-05T20:15:21.613", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "league/commonmark is a PHP Markdown parser. A cross-site scripting (XSS) vulnerability in the Attributes extension of the league/commonmark library (versions 1.5.0 through 2.6.x) allows remote attackers to insert malicious JavaScript calls into HTML. The league/commonmark library provides configuration options such as `html_input: 'strip'` and `allow_unsafe_links: false` to mitigate cross-site scripting (XSS) attacks by stripping raw HTML and disallowing unsafe links. However, when the Attributes Extension is enabled, it introduces a way for users to inject arbitrary HTML attributes into elements via Markdown syntax using curly braces. Version 2.7.0 contains three changes to prevent this XSS attack vector: All attributes starting with `on` are considered unsafe and blocked by default; support for an explicit allowlist of allowed HTML attributes; and manually-added `href` and `src` attributes now respect the existing `allow_unsafe_links` configuration option. If upgrading is not feasible, please consider disabling the `AttributesExtension` for untrusted users and/or filtering the rendered HTML through a library like HTMLPurifier."}, {"lang": "es", "value": "league/commonmark es un analizador de PHP Markdown. Una vulnerabilidad de cross-site scripting (XSS) en la extensión Attributes de la librería league/commonmark (versiones 1.5.0 a 2.6.x) permite a atacantes remotos insertar llamadas JavaScript maliciosas en HTML. La librería league/commonmark ofrece opciones de configuración como `html_input: 'strip'` y `allow_unsafe_links: false` para mitigar los ataques de cross-site scripting (XSS) eliminando HTML sin formato y deshabilitando enlaces no seguros. Sin embargo, al habilitar la extensión Attributes, los usuarios pueden inyectar atributos HTML arbitrarios en elementos mediante la sintaxis Markdown, utilizando llaves. La versión 2.7.0 incluye tres cambios para prevenir este vector de ataque XSS: todos los atributos que empiezan por `on` se consideran no seguros y se bloquean por defecto; se admite una lista explícita de atributos HTML permitidos; y los atributos `href` y `src` añadidos manualmente ahora respetan la opción de configuración `allow_unsafe_links`. Si la actualización no es posible, considere deshabilitar `AttributesExtension` para usuarios no confiables y/o filtrar el HTML renderizado a través de una librería como HTMLPurifier."}], "references": [{"url": "https://github.com/thephpleague/commonmark/commit/f0d626cf05ad3e99e6db26ebcb9091b6cd1cd89b", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/thephpleague/commonmark/security/advisories/GHSA-3527-qv2q-pfvx", "source": "<EMAIL>", "tags": []}]}
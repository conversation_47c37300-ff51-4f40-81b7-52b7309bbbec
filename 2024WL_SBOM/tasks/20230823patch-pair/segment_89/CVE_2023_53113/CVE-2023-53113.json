{"cve_id": "CVE-2023-53113", "published_date": "2025-05-02T16:15:30.407", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: nl80211: fix NULL-ptr deref in offchan check\n\nIf, e.g. in AP mode, the link was already created by userspace\nbut not activated yet, it has a chandef but the chandef isn't\nvalid and has no channel. Check for this and ignore this link."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: nl80211: se corrige la deref NULL-ptr en la comprobación offchan. Si, por ejemplo, en modo AP, el enlace ya fue creado por el espacio de usuario, pero aún no se activó, tiene una definición de canal (chandef), pero esta no es válida y no tiene canal. Verifique esto e ignore este enlace."}], "references": [{"url": "https://git.kernel.org/stable/c/201a836c2385fdd2b9d0a8e7737bba5b26f1863a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/87e80ea4fbc9ce2f2005905fdbcd38baaa47463a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f624bb6fad23df3270580b4fcef415c6e7bf7705", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
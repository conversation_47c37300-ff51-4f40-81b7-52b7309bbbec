{"cve_id": "CVE-2023-53070", "published_date": "2025-05-02T16:15:26.050", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nACPI: PPTT: Fix to avoid sleep in the atomic context when PPTT is absent\n\nCommit 0c80f9e165f8 (\"ACPI: PPTT: Leave the table mapped for the runtime usage\")\nenabled to map PPTT once on the first invocation of acpi_get_pptt() and\nnever unmapped the same allowing it to be used at runtime with out the\nhassle of mapping and unmapping the table. This was needed to fetch LLC\ninformation from the PPTT in the cpuhotplug path which is executed in\nthe atomic context as the acpi_get_table() might sleep waiting for a\nmutex.\n\nHowever it missed to handle the case when there is no PPTT on the system\nwhich results in acpi_get_pptt() being called from all the secondary\nCPUs attempting to fetch the LLC information in the atomic context\nwithout knowing the absence of PPTT resulting in the splat like below:\n\n | BUG: sleeping function called from invalid context at kernel/locking/semaphore.c:164\n | in_atomic(): 1, irqs_disabled(): 1, non_block: 0, pid: 0, name: swapper/1\n | preempt_count: 1, expected: 0\n | RCU nest depth: 0, expected: 0\n | no locks held by swapper/1/0.\n | irq event stamp: 0\n | hardirqs last  enabled at (0): 0x0\n | hardirqs last disabled at (0): copy_process+0x61c/0x1b40\n | softirqs last  enabled at (0): copy_process+0x61c/0x1b40\n | softirqs last disabled at (0): 0x0\n | CPU: 1 PID: 0 Comm: swapper/1 Not tainted 6.3.0-rc1 #1\n | Call trace:\n |  dump_backtrace+0xac/0x138\n |  show_stack+0x30/0x48\n |  dump_stack_lvl+0x60/0xb0\n |  dump_stack+0x18/0x28\n |  __might_resched+0x160/0x270\n |  __might_sleep+0x58/0xb0\n |  down_timeout+0x34/0x98\n |  acpi_os_wait_semaphore+0x7c/0xc0\n |  acpi_ut_acquire_mutex+0x58/0x108\n |  acpi_get_table+0x40/0xe8\n |  acpi_get_pptt+0x48/0xa0\n |  acpi_get_cache_info+0x38/0x140\n |  init_cache_level+0xf4/0x118\n |  detect_cache_attributes+0x2e4/0x640\n |  update_siblings_masks+0x3c/0x330\n |  store_cpu_topology+0x88/0xf0\n |  secondary_start_kernel+0xd0/0x168\n |  __secondary_switched+0xb8/0xc0\n\nUpdate acpi_get_pptt() to consider the fact that PPTT is once checked and\nis not available on the system and return NULL avoiding any attempts to\nfetch PPTT and thereby avoiding any possible sleep waiting for a mutex\nin the atomic context."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ACPI: PPTT: Corrección para evitar la suspensión en el contexto atómico cuando PPTT está ausente. el commit 0c80f9e165f8 (\"ACPI: PPTT: Dejar la tabla asignada para el uso en tiempo de ejecución\") habilitó la asignación de PPTT una vez en la primera invocación de acpi_get_pptt() y nunca la desasignó, lo que permite su uso en tiempo de ejecución sin la molestia de asignar y desasignar la tabla. Esto era necesario para obtener información de LLC del PPTT en la ruta cpuhotplug, que se ejecuta en el contexto atómico, ya que acpi_get_table() podría estar en suspensión esperando un mutex. Sin embargo, no logró gestionar el caso en que no hay PPTT en el sistema, lo que provoca que acpi_get_pptt() se llame desde todas las CPU secundarias que intentan obtener la información de LLC en el contexto atómico sin conocer la ausencia de PPTT, lo que resulta en un error como el siguiente: | ERROR: función inactiva llamada desde contexto no válido en kernel/locking/semaphore.c:164 | in_atomic(): 1, irqs_disabled(): 1, non_block: 0, pid: 0, name: swapper/1 | preempt_count: 1, expected: 0 | Profundidad de anidamiento de RCU: 0, expected: 0 | swapper/1/0 no tiene bloqueos. | marca de evento irq: 0 | hardirqs habilitado por última vez en (0): 0x0 | hardirqs deshabilitado por última vez en (0): copy_process+0x61c/0x1b40 | softirqs habilitado por última vez en (0): copy_process+0x61c/0x1b40 | softirqs deshabilitado por última vez en (0): 0x0 | CPU: 1 PID: 0 Comm: swapper/1 No contaminado 6.3.0-rc1 #1 | Rastreo de llamadas: | dump_backtrace+0xac/0x138 | show_stack+0x30/0x48 | dump_stack_lvl+0x60/0xb0 | dump_stack+0x18/0x28 | __might_resched+0x160/0x270 | __might_sleep+0x58/0xb0 | down_timeout+0x34/0x98 | acpi_os_wait_semaphore+0x7c/0xc0 | acpi_ut_acquire_mutex+0x58/0x108 | acpi_get_table+0x40/0xe8 | acpi_get_pptt+0x48/0xa0 | acpi_get_cache_info+0x38/0x140 | init_cache_level+0xf4/0x118 | detect_cache_attributes+0x2e4/0x640 | update_siblings_masks+0x3c/0x330 | store_cpu_topology+0x88/0xf0 | secondary_start_kernel+0xd0/0x168 | __secondary_switched+0xb8/0xc0 Actualice acpi_get_pptt() para considerar el hecho de que PPTT se verifica una vez y no está disponible en el sistema y devuelve NULL evitando cualquier intento de obtener PPTT y, por lo tanto, evitando cualquier posible suspensión esperando un mutex en el contexto atómico."}], "references": [{"url": "https://git.kernel.org/stable/c/1318a07706bb2f8c65f88f39a16c2b5260bcdcd4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/91d7b60a65d9f71230ea09b86d2058a884a3c2af", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e0c1106d51b9abc8eae03c5522b20649b6a55f6e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
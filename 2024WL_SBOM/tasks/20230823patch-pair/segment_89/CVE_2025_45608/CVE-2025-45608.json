{"cve_id": "CVE-2025-45608", "published_date": "2025-05-05T20:15:19.887", "last_modified_date": "2025-05-06T14:15:37.827", "descriptions": [{"lang": "en", "value": "Incorrect access control in the /system/user/findUserList API of Xinguan v0.0.1-SNAPSHOT allows attackers to access sensitive information via a crafted payload."}, {"lang": "es", "value": "El control de acceso incorrecto en la API /system/user/findUserList de Xinguan v0.0.1-SNAPSHOT permite a los atacantes acceder a información confidencial a través de un payload manipulado."}], "references": [{"url": "https://github.com/zykzhangyukang/Xinguan/issues/26", "source": "<EMAIL>", "tags": []}]}
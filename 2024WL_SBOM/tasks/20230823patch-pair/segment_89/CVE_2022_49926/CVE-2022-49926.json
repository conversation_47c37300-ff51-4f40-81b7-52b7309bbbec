{"cve_id": "CVE-2022-49926", "published_date": "2025-05-01T15:16:18.440", "last_modified_date": "2025-05-07T13:28:34.500", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: dsa: Fix possible memory leaks in dsa_loop_init()\n\nkmemleak reported memory leaks in dsa_loop_init():\n\nkmemleak: 12 new suspected memory leaks\n\nunreferenced object 0xffff8880138ce000 (size 2048):\n  comm \"modprobe\", pid 390, jiffies 4295040478 (age 238.976s)\n  backtrace:\n    [<000000006a94f1d5>] kmalloc_trace+0x26/0x60\n    [<00000000a9c44622>] phy_device_create+0x5d/0x970\n    [<00000000d0ee2afc>] get_phy_device+0xf3/0x2b0\n    [<00000000dca0c71f>] __fixed_phy_register.part.0+0x92/0x4e0\n    [<000000008a834798>] fixed_phy_register+0x84/0xb0\n    [<0000000055223fcb>] dsa_loop_init+0xa9/0x116 [dsa_loop]\n    ...\n\nThere are two reasons for memleak in dsa_loop_init().\n\nFirst, fixed_phy_register() create and register phy_device:\n\nfixed_phy_register()\n  get_phy_device()\n    phy_device_create() # freed by phy_device_free()\n  phy_device_register() # freed by phy_device_remove()\n\nBut fixed_phy_unregister() only calls phy_device_remove().\nSo the memory allocated in phy_device_create() is leaked.\n\nSecond, when mdio_driver_register() fail in dsa_loop_init(),\nit just returns and there is no cleanup for phydevs.\n\nFix the problems by catching the error of mdio_driver_register()\nin dsa_loop_init(), then calling both fixed_phy_unregister() and\nphy_device_free() to release phydevs.\nAlso add a function for phydevs cleanup to avoid duplacate."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: dsa: Se corrigen posibles fugas de memoria en dsa_loop_init() kmemleak informó de fugas de memoria en dsa_loop_init(): kmemleak: 12 nuevas fugas de memoria sospechosas objeto no referenciado 0xffff8880138ce000 (tamaño 2048): comm \"modprobe\", pid 390, jiffies 4295040478 (edad 238,976 s) backtrace: [&lt;000000006a94f1d5&gt;] kmalloc_trace+0x26/0x60 [&lt;00000000a9c44622&gt;] phy_device_create+0x5d/0x970 [&lt;00000000d0ee2afc&gt;] Hay dos razones para la pérdida de memoria en dsa_loop_init(). Primero, fixed_phy_register() crea y registra phy_device: fixed_phy_register() get_phy_device() phy_device_create() # liberado por phy_device_free() phy_device_register() # liberado por phy_device_remove() Pero fixed_phy_unregister() solo llama a phy_device_remove(). Por lo tanto, la memoria asignada en phy_device_create() se filtra. Segundo, cuando mdio_driver_register() falla en dsa_loop_init(), simplemente regresa y no hay limpieza para phydevs. Solucione los problemas capturando el error de mdio_driver_register() en dsa_loop_init(), luego llame a fixed_phy_unregister() y phy_device_free() para liberar phydevs. También agregue una función para la limpieza de phydevs para evitar la duplicación."}], "references": [{"url": "https://git.kernel.org/stable/c/37a098fc9b42bd7fce66764866aa514639667b6e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/4d2024b138d9f7b02ae13ee997fd3a71e9e46254", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/633efc8b3dc96f56f5a57f2a49764853a2fa3f50", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/935b4beb724946a37cebf97191592d4879d3a3a3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9f555b1584fc2d5d16ee3c4d9438e93ac7c502c7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bbc5d7b46a729bfcbb5544f6612b7a67dd4f4d6f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d593e1ede655b74c42e4e4fe285ea64aee96fb5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
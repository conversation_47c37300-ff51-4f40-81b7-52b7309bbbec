{"cve_id": "CVE-2025-26241", "published_date": "2025-05-05T16:15:50.750", "last_modified_date": "2025-06-13T18:38:51.750", "descriptions": [{"lang": "en", "value": "A SQL injection vulnerability in the \"Search\" functionality of \"tickets.php\" page in osTicket <=1.17.5 allows authenticated attackers to execute arbitrary SQL commands via the \"keywords\" and \"topic_id\" URL parameters combination."}, {"lang": "es", "value": "Una vulnerabilidad de inyección SQL en la funcionalidad \"Search\" de la página \"tickets.php\" en osTicket &lt;=1.17.5 permite a atacantes autenticados ejecutar comandos SQL arbitrarios a través de la combinación de parámetros de URL \"keywords\" y \"topic_id\"."}], "references": [{"url": "https://members.backbox.org/osticket-sql-injection-bypass/", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}]}
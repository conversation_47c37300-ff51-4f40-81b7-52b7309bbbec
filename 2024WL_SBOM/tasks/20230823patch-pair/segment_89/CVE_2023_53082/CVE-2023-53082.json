{"cve_id": "CVE-2023-53082", "published_date": "2025-05-02T16:15:27.220", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nvp_vdpa: fix the crash in hot unplug with vp_vdpa\n\nWhile unplugging the vp_vdpa device, it triggers a kernel panic\nThe root cause is: vdpa_mgmtdev_unregister() will accesses modern\ndevices which will cause a use after free.\nSo need to change the sequence in vp_vdpa_remove\n\n[  195.003359] BUG: unable to handle page fault for address: ff4e8beb80199014\n[  195.004012] #PF: supervisor read access in kernel mode\n[  195.004486] #PF: error_code(0x0000) - not-present page\n[  195.004960] PGD 100000067 P4D 1001b6067 PUD 1001b7067 PMD 1001b8067 PTE 0\n[  195.005578] Oops: 0000 1 PREEMPT SMP PTI\n[  195.005968] CPU: 13 PID: 164 Comm: kworker/u56:10 Kdump: loaded Not tainted 5.14.0-252.el9.x86_64 #1\n[  195.006792] Hardware name: Red Hat KVM/RHEL, BIOS edk2-20221207gitfff6d81270b5-2.el9 unknown\n[  195.007556] Workqueue: kacpi_hotplug acpi_hotplug_work_fn\n[  195.008059] RIP: 0010:ioread8+0x31/0x80\n[  195.008418] Code: 77 28 48 81 ff 00 00 01 00 76 0b 89 fa ec 0f b6 c0 c3 cc cc cc cc 8b 15 ad 72 93 01 b8 ff 00 00 00 85 d2 75 0f c3 cc cc cc cc <8a> 07 0f b6 c0 c3 cc cc cc cc 83 ea 01 48 83 ec 08 48 89 fe 48 c7\n[  195.010104] RSP: 0018:ff4e8beb8067bab8 EFLAGS: 00010292\n[  195.010584] RAX: ffffffffc05834a0 RBX: ffffffffc05843c0 RCX: ff4e8beb8067bae0\n[  195.011233] RDX: ff1bcbd580f88000 RSI: 0000000000000246 RDI: ff4e8beb80199014\n[  195.011881] RBP: ff1bcbd587e39000 R08: ffffffff916fa2d0 R09: ff4e8beb8067ba68\n[  195.012527] R10: 000000000000001c R11: 0000000000000000 R12: ff1bcbd5a3de9120\n[  195.013179] R13: ffffffffc062d000 R14: 0000000000000080 R15: ff1bcbe402bc7805\n[  195.013826] FS:  0000000000000000(0000) GS:ff1bcbe402740000(0000) knlGS:0000000000000000\n[  195.014564] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[  195.015093] CR2: ff4e8beb80199014 CR3: 0000000107dea002 CR4: 0000000000771ee0\n[  195.015741] PKRU: 55555554\n[  195.016001] Call Trace:\n[  195.016233]  <TASK>\n[  195.016434]  vp_modern_get_status+0x12/0x20\n[  195.016823]  vp_vdpa_reset+0x1b/0x50 [vp_vdpa]\n[  195.017238]  virtio_vdpa_reset+0x3c/0x48 [virtio_vdpa]\n[  195.017709]  remove_vq_common+0x1f/0x3a0 [virtio_net]\n[  195.018178]  virtnet_remove+0x5d/0x70 [virtio_net]\n[  195.018618]  virtio_dev_remove+0x3d/0x90\n[  195.018986]  device_release_driver_internal+0x1aa/0x230\n[  195.019466]  bus_remove_device+0xd8/0x150\n[  195.019841]  device_del+0x18b/0x3f0\n[  195.020167]  ? kernfs_find_ns+0x35/0xd0\n[  195.020526]  device_unregister+0x13/0x60\n[  195.020894]  unregister_virtio_device+0x11/0x20\n[  195.021311]  device_release_driver_internal+0x1aa/0x230\n[  195.021790]  bus_remove_device+0xd8/0x150\n[  195.022162]  device_del+0x18b/0x3f0\n[  195.022487]  device_unregister+0x13/0x60\n[  195.022852]  ? vdpa_dev_remove+0x30/0x30 [vdpa]\n[  195.023270]  vp_vdpa_dev_del+0x12/0x20 [vp_vdpa]\n[  195.023694]  vdpa_match_remove+0x2b/0x40 [vdpa]\n[  195.024115]  bus_for_each_dev+0x78/0xc0\n[  195.024471]  vdpa_mgmtdev_unregister+0x65/0x80 [vdpa]\n[  195.024937]  vp_vdpa_remove+0x23/0x40 [vp_vdpa]\n[  195.025353]  pci_device_remove+0x36/0xa0\n[  195.025719]  device_release_driver_internal+0x1aa/0x230\n[  195.026201]  pci_stop_bus_device+0x6c/0x90\n[  195.026580]  pci_stop_and_remove_bus_device+0xe/0x20\n[  195.027039]  disable_slot+0x49/0x90\n[  195.027366]  acpiphp_disable_and_eject_slot+0x15/0x90\n[  195.027832]  hotplug_event+0xea/0x210\n[  195.028171]  ? hotplug_event+0x210/0x210\n[  195.028535]  acpiphp_hotplug_notify+0x22/0x80\n[  195.028942]  ? hotplug_event+0x210/0x210\n[  195.029303]  acpi_device_hotplug+0x8a/0x1d0\n[  195.029690]  acpi_hotplug_work_fn+0x1a/0x30\n[  195.030077]  process_one_work+0x1e8/0x3c0\n[  195.030451]  worker_thread+0x50/0x3b0\n[  195.030791]  ? rescuer_thread+0x3a0/0x3a0\n[  195.031165]  kthread+0xd9/0x100\n[  195.031459]  ? kthread_complete_and_exit+0x20/0x20\n[  195.031899]  ret_from_fork+0x22/0x30\n[  195.032233]  </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: vp_vdpa: corrige el fallo en la desconexión activa con vp_vdpa Al desconectar el dispositivo vp_vdpa, se desencadena un pánico del kernel La causa raíz es: vdpa_mgmtdev_unregister() accederá a dispositivos modernos, lo que provocará un use-after-free. Entonces es necesario cambiar la secuencia en vp_vdpa_remove [ 195.003359] ERROR: no se puede manejar el error de página para la dirección: ff4e8beb80199014 [ 195.004012] #PF: acceso de lectura del supervisor en modo kernel [ 195.004486] #PF: error_code(0x0000) - página no presente [ 195.004960] PGD 100000067 P4D 1001b6067 PUD 1001b7067 PMD 1001b8067 PTE 0 [ 195.005578] Oops: 0000 1 PREEMPT SMP PTI [ 195.005968] CPU: 13 PID: 164 Comm: kworker/u56:10 Kdump: cargado No contaminado 5.14.0-252.el9.x86_64 #1 [ 195.006792] Nombre del hardware: Red Hat KVM/RHEL, BIOS edk2-20221207gitfff6d81270b5-2.el9 unknown [ 195.007556] Workqueue: kacpi_hotplug acpi_hotplug_work_fn [ 195.008059] RIP: 0010:ioread8+0x31/0x80 [ 195.008418] Code: 77 28 48 81 ff 00 00 01 00 76 0b 89 fa ec 0f b6 c0 c3 cc cc cc cc 8b 15 ad 72 93 01 b8 ff 00 00 00 85 d2 75 0f c3 cc cc cc cc &lt;8a&gt; 07 0f b6 c0 c3 cc cc cc cc 83 ea 01 48 83 ec 08 48 89 fe 48 c7 [ 195.010104] RSP: 0018:ff4e8beb8067bab8 EFLAGS: 00010292 [ 195.010584] RAX: ffffffffc05834a0 RBX: ffffffffc05843c0 RCX: ff4e8beb8067bae0 [ 195.011233] RDX: ff1bcbd580f88000 RSI: 0000000000000246 RDI: ff4e8beb80199014 [ 195.011881] RBP: ff1bcbd587e39000 R08: ffffffff916fa2d0 R09: ff4e8beb8067ba68 [ 195.012527] R10: 000000000000001c R11: 0000000000000000 R12: ff1bcbd5a3de9120 [ 195.013179] R13: ffffffffc062d000 R14: 0000000000000080 R15: ff1bcbe402bc7805 [ 195.013826] FS: 0000000000000000(0000) GS:ff1bcbe402740000(0000) knlGS:0000000000000000 [ 195.014564] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [ 195.015093] CR2: ff4e8beb80199014 CR3: 0000000107dea002 CR4: 0000000000771ee0 [ 195.015741] PKRU: 55555554 [ 195.016001] Call Trace: [ 195.016233]  [ 195.016434] vp_modern_get_status+0x12/0x20 [ 195.016823] vp_vdpa_reset+0x1b/0x50 [vp_vdpa] [ 195.017238] virtio_vdpa_reset+0x3c/0x48 [virtio_vdpa] [ 195.017709] remove_vq_common+0x1f/0x3a0 [virtio_net] [ 195.018178] virtnet_remove+0x5d/0x70 [virtio_net] [ 195.018618] virtio_dev_remove+0x3d/0x90 [ 195.018986] device_release_driver_internal+0x1aa/0x230 [ 195.019466] bus_remove_device+0xd8/0x150 [ 195.019841] device_del+0x18b/0x3f0 [ 195.020167] ? kernfs_find_ns+0x35/0xd0 [ 195.020526] device_unregister+0x13/0x60 [ 195.020894] unregister_virtio_device+0x11/0x20 [ 195.021311] device_release_driver_internal+0x1aa/0x230 [ 195.021790] bus_remove_device+0xd8/0x150 [ 195.022162] device_del+0x18b/0x3f0 [ 195.022487] device_unregister+0x13/0x60 [ 195.022852] ? vdpa_dev_remove+0x30/0x30 [vdpa] [ 195.023270] vp_vdpa_dev_del+0x12/0x20 [vp_vdpa] [ 195.023694] vdpa_match_remove+0x2b/0x40 [vdpa] [ 195.024115] bus_for_each_dev+0x78/0xc0 [ 195.024471] vdpa_mgmtdev_unregister+0x65/0x80 [vdpa] [ 195.024937] vp_vdpa_remove+0x23/0x40 [vp_vdpa] [ 195.025353] pci_device_remove+0x36/0xa0 [ 195.025719] device_release_driver_internal+0x1aa/0x230 [ 195.026201] pci_stop_bus_device+0x6c/0x90 [ 195.026580] pci_stop_and_remove_bus_device+0xe/0x20 [ 195.027039] disable_slot+0x49/0x90 [ 195.027366] acpiphp_disable_and_eject_slot+0x15/0x90 [ 195.027832] hotplug_event+0xea/0x210 [ 195.028171] ? hotplug_event+0x210/0x210 [ 195.028535] acpiphp_hotplug_notify+0x22/0x80 [ 195.028942] ? hotplug_event+0x210/0x210 [ 195.029303] acpi_device_hotplug+0x8a/0x1d0 [ 195.029690] acpi_hotplug_work_fn+0x1a/0x30 [ 195.030077] process_one_work+0x1e8/0x3c0 [ 195.030451] worker_thread+0x50/0x3b0 [ 195.030791] ? rescuer_thread+0x3a0/0x3a0 [ 195.031165] kthread+0xd9/0x100 [ 195.031459] ? kthread_complete_and_exit+0x20/0x20 [ 195.031899] ret_from_fork+0x22/0x30 [ 195.032233]  "}], "references": [{"url": "https://git.kernel.org/stable/c/aed8efddd39b3434c96718d39009285c52b1cafc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/baafa2960731211837d8fc04ff3873ecb7440464", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fa1f327f93c9a7310cce9d2fcda28b7af91f7437", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
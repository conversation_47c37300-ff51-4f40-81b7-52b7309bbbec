{"cve_id": "CVE-2022-49914", "published_date": "2025-05-01T15:16:16.593", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: fix inode list leak during backref walking at resolve_indirect_refs()\n\nDuring backref walking, at resolve_indirect_refs(), if we get an error\nwe jump to the 'out' label and call ulist_free() on the 'parents' ulist,\nwhich frees all the elements in the ulist - however that does not free\nany inode lists that may be attached to elements, through the 'aux' field\nof a ulist node, so we end up leaking lists if we have any attached to\nthe unodes.\n\nFix this by calling free_leaf_list() instead of ulist_free() when we exit\nfrom resolve_indirect_refs(). The static function free_leaf_list() is\nmoved up for this to be possible and it's slightly simplified by removing\nunnecessary code."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: corrección de fuga de lista de inodos durante el recorrido de backref en resolve_indirect_refs() Durante el recorrido de backref, en resolve_indirect_refs(), si obtenemos un error saltamos a la etiqueta 'out' y llamamos a ulist_free() en la ulist 'parents', que libera todos los elementos en la ulist - sin embargo, eso no libera ninguna lista de inodos que pueda estar adjunta a elementos, a través del campo 'aux' de un nodo ulist, por lo que terminamos filtrando listas si tenemos alguna adjunta a los unodes. Arregle esto llamando a free_leaf_list() en lugar de ulist_free() cuando salimos de resolve_indirect_refs(). La función estática free_leaf_list() se mueve hacia arriba para que esto sea posible y se simplifica ligeramente eliminando código innecesario."}], "references": [{"url": "https://git.kernel.org/stable/c/2c0329406bb28109c07c6e23e5e3e0fa618a95d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/396515db923ad5cbeb179d6b88927870b4cbebb7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5614dc3a47e3310fbc77ea3b67eaadd1c6417bf1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ba3479f9e96b9ad460c7e77abc26dd16e5dec4f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a52e24c7fcc3c5ce3588a14e3663c00868d36623", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b1dc9019bb5f89abae85645de1a2dd4830c1f8e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cded2c89774b99b67c98147ae103ea878c92a206", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
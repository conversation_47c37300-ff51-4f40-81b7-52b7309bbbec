{"cve_id": "CVE-2025-37758", "published_date": "2025-05-01T13:15:54.583", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nata: pata_pxa: Fix potential NULL pointer dereference in pxa_ata_probe()\n\ndevm_ioremap() returns NULL on error. Currently, pxa_ata_probe() does\nnot check for this case, which can result in a NULL pointer dereference.\n\nAdd NULL check after devm_ioremap() to prevent this issue."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ata: pata_pxa: Se corrige la posible desreferencia de punteros nulos en pxa_ata_probe(). Devm_ioremap() devuelve NULL en caso de error. Actualmente, pxa_ata_probe() no verifica este caso, lo que puede provocar una desreferencia de punteros nulos. Agregue la comprobación de NULL después de devm_ioremap() para evitar este problema."}], "references": [{"url": "https://git.kernel.org/stable/c/17d5e6e915fad5a261db3698c9c5bbe702102d7c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2ba9e4c69207777bb0775c7c091800ecd69de144", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2dc53c7a0c1f57b082931facafa804a7ca32a9a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5b09bf6243b0bc0ae58bd9efdf6f0de5546f8d06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a551f75401793ba8075d7f46ffc931ce5151f03f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad320e408a8c95a282ab9c05cdf0c9b95e317985", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c022287f6e599422511aa227dc6da37b58d9ceac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d0d720f9282839b9db625a376c02a1426a16b0ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee2b0301d6bfe16b35d57947687c664ecb815775", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
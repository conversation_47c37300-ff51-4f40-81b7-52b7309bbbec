{"cve_id": "CVE-2025-4242", "published_date": "2025-05-03T19:15:48.833", "last_modified_date": "2025-05-09T13:38:19.673", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Online Birth Certificate System 2.0. Affected by this vulnerability is an unknown functionality of the file /admin/between-dates-report.php. The manipulation of the argument fromdate leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad crítica en PHPGurukul Online Birth Certificate System 2.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/between-dates-report.php. La manipulación del argumento \"fromdate\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/bluechips-zhao/myCVE/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307333", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307333", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562624", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
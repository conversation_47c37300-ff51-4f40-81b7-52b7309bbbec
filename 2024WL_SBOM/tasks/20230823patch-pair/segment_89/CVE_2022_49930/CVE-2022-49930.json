{"cve_id": "CVE-2022-49930", "published_date": "2025-05-01T15:16:18.983", "last_modified_date": "2025-05-07T13:28:51.413", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRDMA/hns: Fix NULL pointer problem in free_mr_init()\n\nLock grab occurs in a concurrent scenario, resulting in stepping on a NULL\npointer.  It should be init mutex_init() first before use the lock.\n\n  Unable to handle kernel NULL pointer dereference at virtual address 0000000000000000\n  Call trace:\n   __mutex_lock.constprop.0+0xd0/0x5c0\n   __mutex_lock_slowpath+0x1c/0x2c\n   mutex_lock+0x44/0x50\n   free_mr_send_cmd_to_hw+0x7c/0x1c0 [hns_roce_hw_v2]\n   hns_roce_v2_dereg_mr+0x30/0x40 [hns_roce_hw_v2]\n   hns_roce_dereg_mr+0x4c/0x130 [hns_roce_hw_v2]\n   ib_dereg_mr_user+0x54/0x124\n   uverbs_free_mr+0x24/0x30\n   destroy_hw_idr_uobject+0x38/0x74\n   uverbs_destroy_uobject+0x48/0x1c4\n   uobj_destroy+0x74/0xcc\n   ib_uverbs_cmd_verbs+0x368/0xbb0\n   ib_uverbs_ioctl+0xec/0x1a4\n   __arm64_sys_ioctl+0xb4/0x100\n   invoke_syscall+0x50/0x120\n   el0_svc_common.constprop.0+0x58/0x190\n   do_el0_svc+0x30/0x90\n   el0_svc+0x2c/0xb4\n   el0t_64_sync_handler+0x1a4/0x1b0\n   el0t_64_sync+0x19c/0x1a0"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: RDMA/hns: Se solucionó el problema del puntero nulo en free_mr_init(). La captura de bloqueo ocurre en un escenario concurrente, lo que resulta en la sobreexposición a un puntero nulo. Se debe inicializar mutex_init() antes de usar el bloqueo. No se puede gestionar la desreferencia del puntero NULL del núcleo en la dirección virtual 0000000000000000 Rastreo de llamadas: __mutex_lock.constprop.0+0xd0/0x5c0 __mutex_lock_slowpath+0x1c/0x2c mutex_lock+0x44/0x50 free_mr_send_cmd_to_hw+0x7c/0x1c0 [hns_roce_hw_v2] hns_roce_v2_dereg_mr+0x30/0x40 [hns_roce_hw_v2] hns_roce_dereg_mr+0x4c/0x130 [hns_roce_hw_v2] ib_dereg_mr_user+0x54/0x124 uverbs_free_mr+0x24/0x30 destroy_hw_idr_uobject+0x38/0x74 uverbs_destroy_uobject+0x48/0x1c4 uobj_destroy+0x74/0xcc ib_uverbs_cmd_verbs+0x368/0xbb0 ib_uverbs_ioctl+0xec/0x1a4 __arm64_sys_ioctl+0xb4/0x100 invoke_syscall+0x50/0x120 el0_svc_common.constprop.0+0x58/0x190 do_el0_svc+0x30/0x90 el0_svc+0x2c/0xb4 el0t_64_sync_handler+0x1a4/0x1b0 el0t_64_sync+0x19c/0x1a0 "}], "references": [{"url": "https://git.kernel.org/stable/c/0e23e85d86b78e734dd6654f1b69fbaeb5534c81", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/12bcaf87d8b66d8cd812479c8a6349dcb245375c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53062", "published_date": "2025-05-02T16:15:25.257", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: usb: smsc95xx: Limit packet length to skb->len\n\nPacket length retrieved from descriptor may be larger than\nthe actual socket buffer length. In such case the cloned\nskb passed up the network stack will leak kernel memory contents."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: usb: smsc95xx: Limitar la longitud del paquete a skb-&gt;len. La longitud del paquete obtenida del descriptor puede ser mayor que la longitud real del búfer del socket. En tal caso, el skb clonado que se pasa a la pila de red filtrará el contenido de la memoria del kernel."}], "references": [{"url": "https://git.kernel.org/stable/c/33d1603a38e05886c538129ddfe00bd52d347e7b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/70eb25c6a6cde149affe8a587371a3a8ad295ba0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/733580e268a53db1cd01f2251419da91866378f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ba6c40227108f8ee428e42eb0337b48ed3001e65", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3c145a4d24b752c9a1314d5a595014d51471418", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e041bef1adee02999cf24f9a2e15ed452bc363fe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f2111c791d885211714db85f9a06188571c57dd0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ff821092cf02a70c2bccd2d19269f01e29aa52cf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
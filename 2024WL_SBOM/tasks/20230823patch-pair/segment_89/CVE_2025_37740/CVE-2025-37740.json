{"cve_id": "CVE-2025-37740", "published_date": "2025-05-01T13:15:52.617", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\njfs: add sanity check for agwidth in dbMount\n\nThe width in dmapctl of the AG is zero, it trigger a divide error when\ncalculating the control page level in dbAllocAG.\n\nTo avoid this issue, add a check for agwidth in dbAllocAG."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: jfs: añadir una comprobación de validez para agwidth en dbMount. El ancho en dmapctl del AG es cero, lo que genera un error de división al calcular el nivel de página de control en dbAllocAG. Para evitar este problema, añada una comprobación para agwidth en dbAllocAG."}], "references": [{"url": "https://git.kernel.org/stable/c/722e72f7f9c69fcb3ab7988c2471feff7a4c8de1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a065cec230aa807c18828a3eee82f1c8592c2adf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a260bf14cd347878f01f70739ba829442a474a16", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a741f29ac8b6374c9904be8b7ac7cdfcd7e7e4fa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8c96a9e7660e5e5eea445978fe8f2e432d91c1f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cc0bc4cb62ce5fa0c383e3bf0765d01f46bd49ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ccd97c8a4f90810f228ee40d1055148fa146dd57", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ddf2846f22e8575d6b4b6a66f2100f168b8cd73d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3f85edb03183fb06539e5b50dd2c4bb42b869f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
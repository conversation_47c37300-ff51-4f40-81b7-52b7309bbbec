{"cve_id": "CVE-2024-13322", "published_date": "2025-05-02T04:15:44.210", "last_modified_date": "2025-05-06T15:49:41.500", "descriptions": [{"lang": "en", "value": "The Ads Pro Plugin - Multi-Purpose WordPress Advertising Manager plugin for WordPress is vulnerable to SQL Injection via the 'a_id' parameter in all versions up to, and including, 4.88 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Ads Pro Plugin - Multi-Purpose de WordPress Advertising Manager para WordPress, es vulnerable a la inyección SQL a través del parámetro 'a_id' en todas las versiones hasta la 4.88 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/ads-pro-plugin-multipurpose-wordpress-advertising-manager/10275010", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/3bcb60a8-220f-45a4-a9a9-10f64acf470c?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
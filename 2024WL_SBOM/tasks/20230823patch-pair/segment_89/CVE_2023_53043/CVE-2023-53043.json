{"cve_id": "CVE-2023-53043", "published_date": "2025-05-02T16:15:23.400", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64: dts: qcom: sc7280: Mark PCIe controller as cache coherent\n\nIf the controller is not marked as cache coherent, then kernel will\ntry to ensure coherency during dma-ops and that may cause data corruption.\nSo, mark the PCIe node as dma-coherent as the devices on PCIe bus are\ncache coherent."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64: dts: qcom: sc7280: Marcar el controlador PCIe como coherente con la caché. Si el controlador no está marcado como coherente con la caché, el kernel intentará asegurar la coherencia durante las operaciones DMA, lo que puede causar corrupción de datos. Por lo tanto, marque el nodo PCIe como coherente con la caché, ya que los dispositivos en el bus PCIe sí lo son."}], "references": [{"url": "https://git.kernel.org/stable/c/267b899375bf38944d915c9654d6eb434edad0ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8a63441e83724fee1ef3fd37b237d40d90780766", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e43bba938e2c9104bb4f8bc417ac4d7bb29755e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
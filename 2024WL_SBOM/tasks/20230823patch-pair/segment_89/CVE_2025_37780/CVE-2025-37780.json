{"cve_id": "CVE-2025-37780", "published_date": "2025-05-01T14:15:41.863", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nisofs: Prevent the use of too small fid\n\nsyzbot reported a slab-out-of-bounds Read in isofs_fh_to_parent. [1]\n\nThe handle_bytes value passed in by the reproducing program is equal to 12.\nIn handle_to_path(), only 12 bytes of memory are allocated for the structure\nfile_handle->f_handle member, which causes an out-of-bounds access when\naccessing the member parent_block of the structure isofs_fid in isofs,\nbecause accessing parent_block requires at least 16 bytes of f_handle.\nHere, fh_len is used to indirectly confirm that the value of handle_bytes\nis greater than 3 before accessing parent_block.\n\n[1]\nBUG: KASAN: slab-out-of-bounds in isofs_fh_to_parent+0x1b8/0x210 fs/isofs/export.c:183\nRead of size 4 at addr ffff0000cc030d94 by task syz-executor215/6466\nCPU: 1 UID: 0 PID: 6466 Comm: syz-executor215 Not tainted 6.14.0-rc7-syzkaller-ga2392f333575 #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 02/12/2025\nCall trace:\n show_stack+0x2c/0x3c arch/arm64/kernel/stacktrace.c:466 (C)\n __dump_stack lib/dump_stack.c:94 [inline]\n dump_stack_lvl+0xe4/0x150 lib/dump_stack.c:120\n print_address_description mm/kasan/report.c:408 [inline]\n print_report+0x198/0x550 mm/kasan/report.c:521\n kasan_report+0xd8/0x138 mm/kasan/report.c:634\n __asan_report_load4_noabort+0x20/0x2c mm/kasan/report_generic.c:380\n isofs_fh_to_parent+0x1b8/0x210 fs/isofs/export.c:183\n exportfs_decode_fh_raw+0x2dc/0x608 fs/exportfs/expfs.c:523\n do_handle_to_path+0xa0/0x198 fs/fhandle.c:257\n handle_to_path fs/fhandle.c:385 [inline]\n do_handle_open+0x8cc/0xb8c fs/fhandle.c:403\n __do_sys_open_by_handle_at fs/fhandle.c:443 [inline]\n __se_sys_open_by_handle_at fs/fhandle.c:434 [inline]\n __arm64_sys_open_by_handle_at+0x80/0x94 fs/fhandle.c:434\n __invoke_syscall arch/arm64/kernel/syscall.c:35 [inline]\n invoke_syscall+0x98/0x2b8 arch/arm64/kernel/syscall.c:49\n el0_svc_common+0x130/0x23c arch/arm64/kernel/syscall.c:132\n do_el0_svc+0x48/0x58 arch/arm64/kernel/syscall.c:151\n el0_svc+0x54/0x168 arch/arm64/kernel/entry-common.c:744\n el0t_64_sync_handler+0x84/0x108 arch/arm64/kernel/entry-common.c:762\n el0t_64_sync+0x198/0x19c arch/arm64/kernel/entry.S:600\n\nAllocated by task 6466:\n kasan_save_stack mm/kasan/common.c:47 [inline]\n kasan_save_track+0x40/0x78 mm/kasan/common.c:68\n kasan_save_alloc_info+0x40/0x50 mm/kasan/generic.c:562\n poison_kmalloc_redzone mm/kasan/common.c:377 [inline]\n __kasan_kmalloc+0xac/0xc4 mm/kasan/common.c:394\n kasan_kmalloc include/linux/kasan.h:260 [inline]\n __do_kmalloc_node mm/slub.c:4294 [inline]\n __kmalloc_noprof+0x32c/0x54c mm/slub.c:4306\n kmalloc_noprof include/linux/slab.h:905 [inline]\n handle_to_path fs/fhandle.c:357 [inline]\n do_handle_open+0x5a4/0xb8c fs/fhandle.c:403\n __do_sys_open_by_handle_at fs/fhandle.c:443 [inline]\n __se_sys_open_by_handle_at fs/fhandle.c:434 [inline]\n __arm64_sys_open_by_handle_at+0x80/0x94 fs/fhandle.c:434\n __invoke_syscall arch/arm64/kernel/syscall.c:35 [inline]\n invoke_syscall+0x98/0x2b8 arch/arm64/kernel/syscall.c:49\n el0_svc_common+0x130/0x23c arch/arm64/kernel/syscall.c:132\n do_el0_svc+0x48/0x58 arch/arm64/kernel/syscall.c:151\n el0_svc+0x54/0x168 arch/arm64/kernel/entry-common.c:744\n el0t_64_sync_handler+0x84/0x108 arch/arm64/kernel/entry-common.c:762\n el0t_64_sync+0x198/0x19c arch/arm64/kernel/entry.S:600"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: isofs: Impedir el uso de fid demasiado pequeño. syzbot informó de una lectura fuera de los límites de slab en isofs_fh_to_parent. [1] El valor de handle_bytes pasado por el programa de reproducción es igual a 12. En handle_to_path(), solo se asignan 12 bytes de memoria para el miembro de la estructura file_handle-&gt;f_handle, lo que provoca un acceso fuera de los límites al acceder al miembro parent_block de la estructura isofs_fid en isofs, ya que acceder a parent_block requiere al menos 16 bytes de f_handle. Aquí, fh_len se utiliza para confirmar indirectamente que el valor de handle_bytes es mayor que 3 antes de acceder a parent_block. [1] ERROR: KASAN: slab fuera de los límites en isofs_fh_to_parent+0x1b8/0x210 fs/isofs/export.c:183 Lectura de tamaño 4 en la dirección ffff0000cc030d94 por la tarea syz-executor215/6466 CPU: 1 UID: 0 PID: 6466 Comm: syz-executor215 No contaminado 6.14.0-rc7-syzkaller-ga2392f333575 #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 02/12/2025 Call trace: show_stack+0x2c/0x3c arch/arm64/kernel/stacktrace.c:466 (C) __dump_stack lib/dump_stack.c:94 [inline] dump_stack_lvl+0xe4/0x150 lib/dump_stack.c:120 print_address_description mm/kasan/report.c:408 [inline] print_report+0x198/0x550 mm/kasan/report.c:521 kasan_report+0xd8/0x138 mm/kasan/report.c:634 __asan_report_load4_noabort+0x20/0x2c mm/kasan/report_generic.c:380 isofs_fh_to_parent+0x1b8/0x210 fs/isofs/export.c:183 exportfs_decode_fh_raw+0x2dc/0x608 fs/exportfs/expfs.c:523 do_handle_to_path+0xa0/0x198 fs/fhandle.c:257 handle_to_path fs/fhandle.c:385 [inline] do_handle_open+0x8cc/0xb8c fs/fhandle.c:403 __do_sys_open_by_handle_at fs/fhandle.c:443 [inline] __se_sys_open_by_handle_at fs/fhandle.c:434 [inline] __arm64_sys_open_by_handle_at+0x80/0x94 fs/fhandle.c:434 __invoke_syscall arch/arm64/kernel/syscall.c:35 [inline] invoke_syscall+0x98/0x2b8 arch/arm64/kernel/syscall.c:49 el0_svc_common+0x130/0x23c arch/arm64/kernel/syscall.c:132 do_el0_svc+0x48/0x58 arch/arm64/kernel/syscall.c:151 el0_svc+0x54/0x168 arch/arm64/kernel/entry-common.c:744 el0t_64_sync_handler+0x84/0x108 arch/arm64/kernel/entry-common.c:762 el0t_64_sync+0x198/0x19c arch/arm64/kernel/entry.S:600 Allocated by task 6466: kasan_save_stack mm/kasan/common.c:47 [inline] kasan_save_track+0x40/0x78 mm/kasan/common.c:68 kasan_save_alloc_info+0x40/0x50 mm/kasan/generic.c:562 poison_kmalloc_redzone mm/kasan/common.c:377 [inline] __kasan_kmalloc+0xac/0xc4 mm/kasan/common.c:394 kasan_kmalloc include/linux/kasan.h:260 [inline] __do_kmalloc_node mm/slub.c:4294 [inline] __kmalloc_noprof+0x32c/0x54c mm/slub.c:4306 kmalloc_noprof include/linux/slab.h:905 [inline] handle_to_path fs/fhandle.c:357 [inline] do_handle_open+0x5a4/0xb8c fs/fhandle.c:403 __do_sys_open_by_handle_at fs/fhandle.c:443 [inline] __se_sys_open_by_handle_at fs/fhandle.c:434 [inline] __arm64_sys_open_by_handle_at+0x80/0x94 fs/fhandle.c:434 __invoke_syscall arch/arm64/kernel/syscall.c:35 [inline] invoke_syscall+0x98/0x2b8 arch/arm64/kernel/syscall.c:49 el0_svc_common+0x130/0x23c arch/arm64/kernel/syscall.c:132 do_el0_svc+0x48/0x58 arch/arm64/kernel/syscall.c:151 el0_svc+0x54/0x168 arch/arm64/kernel/entry-common.c:744 el0t_64_sync_handler+0x84/0x108 arch/arm64/kernel/entry-common.c:762 el0t_64_sync+0x198/0x19c arch/arm64/kernel/entry.S:600 "}], "references": [{"url": "https://git.kernel.org/stable/c/007124c896e7d4614ac1f6bd4dedb975c35a2a8e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0405d4b63d082861f4eaff9d39c78ee9dc34f845", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0fdafdaef796816a9ed0fd7ac812932d569d9beb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/56dfffea9fd3be0b3795a9ca6401e133a8427e0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e7de55602c61c8ff28db075cc49c8dd6989d7e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/63d5a3e207bf315a32c7d16de6c89753a759f95a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/952e7a7e317f126d0a2b879fc531b716932d5ffa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee01a309ebf598be1ff8174901ed6e91619f1749", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
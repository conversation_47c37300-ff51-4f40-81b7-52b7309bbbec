{"cve_id": "CVE-2022-49901", "published_date": "2025-05-01T15:16:15.167", "last_modified_date": "2025-05-07T13:34:07.623", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblk-mq: Fix kmemleak in blk_mq_init_allocated_queue\n\nThere is a kmemleak caused by modprobe null_blk.ko\n\nunreferenced object 0xffff8881acb1f000 (size 1024):\n  comm \"modprobe\", pid 836, jiffies 4294971190 (age 27.068s)\n  hex dump (first 32 bytes):\n    00 00 00 00 ad 4e ad de ff ff ff ff 00 00 00 00  .....N..........\n    ff ff ff ff ff ff ff ff 00 53 99 9e ff ff ff ff  .........S......\n  backtrace:\n    [<000000004a10c249>] kmalloc_node_trace+0x22/0x60\n    [<00000000648f7950>] blk_mq_alloc_and_init_hctx+0x289/0x350\n    [<00000000af06de0e>] blk_mq_realloc_hw_ctxs+0x2fe/0x3d0\n    [<00000000e00c1872>] blk_mq_init_allocated_queue+0x48c/0x1440\n    [<00000000d16b4e68>] __blk_mq_alloc_disk+0xc8/0x1c0\n    [<00000000d10c98c3>] 0xffffffffc450d69d\n    [<00000000b9299f48>] 0xffffffffc4538392\n    [<0000000061c39ed6>] do_one_initcall+0xd0/0x4f0\n    [<00000000b389383b>] do_init_module+0x1a4/0x680\n    [<0000000087cf3542>] load_module+0x6249/0x7110\n    [<00000000beba61b8>] __do_sys_finit_module+0x140/0x200\n    [<00000000fdcfff51>] do_syscall_64+0x35/0x80\n    [<000000003c0f1f71>] entry_SYSCALL_64_after_hwframe+0x46/0xb0\n\nThat is because q->ma_ops is set to NULL before blk_release_queue is\ncalled.\n\nblk_mq_init_queue_data\n  blk_mq_init_allocated_queue\n    blk_mq_realloc_hw_ctxs\n      for (i = 0; i < set->nr_hw_queues; i++) {\n        old_hctx = xa_load(&q->hctx_table, i);\n        if (!blk_mq_alloc_and_init_hctx(.., i, ..))\t\t[1]\n          if (!old_hctx)\n\t    break;\n\n      xa_for_each_start(&q->hctx_table, j, hctx, j)\n        blk_mq_exit_hctx(q, set, hctx, j); \t\t\t[2]\n\n    if (!q->nr_hw_queues)\t\t\t\t\t[3]\n      goto err_hctxs;\n\n  err_exit:\n      q->mq_ops = NULL;\t\t\t  \t\t\t[4]\n\n  blk_put_queue\n    blk_release_queue\n      if (queue_is_mq(q))\t\t\t\t\t[5]\n        blk_mq_release(q);\n\n[1]: blk_mq_alloc_and_init_hctx failed at i != 0.\n[2]: The hctxs allocated by [1] are moved to q->unused_hctx_list and\nwill be cleaned up in blk_mq_release.\n[3]: q->nr_hw_queues is 0.\n[4]: Set q->mq_ops to NULL.\n[5]: queue_is_mq returns false due to [4]. And blk_mq_release\nwill not be called. The hctxs in q->unused_hctx_list are leaked.\n\nTo fix it, call blk_release_queue in exception path."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: blk-mq: Se corrige kmemleak en blk_mq_init_allocated_queue Hay una kmemleak causada por modprobe null_blk.ko objeto no referenciado 0xffff8881acb1f000 (tamaño 1024): comm \"modprobe\", pid 836, jiffies 4294971190 (edad 27.068s) volcado hexadecimal (primeros 32 bytes): 00 00 00 00 ad 4e ad de ff ff ff ff 00 00 00 00 .....N.......... ff ff ff ff ff ff ff ff ff 00 53 99 9e ff ff ff ff .........S...... backtrace: [&lt;000000004a10c249&gt;] kmalloc_node_trace+0x22/0x60 [&lt;00000000648f7950&gt;] blk_mq_alloc_and_init_hctx+0x289/0x350 [&lt;00000000af06de0e&gt;] blk_mq_realloc_hw_ctxs+0x2fe/0x3d0 [&lt;00000000e00c1872&gt;] blk_mq_init_allocated_queue+0x48c/0x1440 [&lt;00000000d16b4e68&gt;] __blk_mq_alloc_disk+0xc8/0x1c0 [&lt;00000000d10c98c3&gt;] 0xffffffffc450d69d [&lt;00000000b9299f48&gt;] 0xffffffffc4538392 [&lt;0000000061c39ed6&gt;] hacer_una_llamada_inicio+0xd0/0x4f0 [&lt;00000000b389383b&gt;] hacer_módulo_inicio+0x1a4/0x680 [&lt;0000000087cf3542&gt;] cargar_módulo+0x6249/0x7110 [&lt;00000000beba61b8&gt;] __hacer_módulo_finit_sys+0x140/0x200 [&lt;00000000fdcfff51&gt;] hacer_llamada_al_sistema_64+0x35/0x80 [&lt;000000003c0f1f71&gt;] entry_SYSCALL_64_after_hwframe+0x46/0xb0 Esto se debe a que q-&gt;ma_ops se establece en NULL antes de llamar a blk_release_queue. blk_mq_init_queue_data blk_mq_init_allocated_queue blk_mq_realloc_hw_ctxs para (i = 0; i &lt; set-&gt;nr_hw_queues; i++) { old_hctx = xa_load(&amp;q-&gt;hctx_table, i); si (!blk_mq_alloc_and_init_hctx(.., i, ..)) [1] si (!old_hctx) break; xa_for_each_start(&amp;q-&gt;hctx_table, j, hctx, j) blk_mq_exit_hctx(q, set, hctx, j); [2] if (!q-&gt;nr_hw_queues) [3] goto err_hctxs; err_exit: q-&gt;mq_ops = NULL; [4] blk_put_queue blk_release_queue if (queue_is_mq(q)) [5] blk_mq_release(q); [1]: blk_mq_alloc_and_init_hctx falló en i != 0. [2]: Los hctxs asignados por [1] se mueven a q-&gt;unused_hctx_list y se limpiarán en blk_mq_release. [3]: q-&gt;nr_hw_queues es 0. [4]: Establece q-&gt;mq_ops en NULL. [5]: queue_is_mq devuelve falso debido a [4]. No se llamará a blk_mq_release. Los hctxs en q-&gt;unused_hctx_list tienen fugas. Para solucionarlo, llame a blk_release_queue en la ruta de excepción."}], "references": [{"url": "https://git.kernel.org/stable/c/2dc97e15a54b7bdf457848aa8c663c98a24e58a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/943f45b9399ed8b2b5190cbc797995edaa97f58f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-37760", "published_date": "2025-05-01T14:15:38.110", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/vma: add give_up_on_oom option on modify/merge, use in uffd release\n\nCurrently, if a VMA merge fails due to an OOM condition arising on commit\nmerge or a failure to duplicate anon_vma's, we report this so the caller\ncan handle it.\n\nHowever there are cases where the caller is only ostensibly trying a\nmerge, and doesn't mind if it fails due to this condition.\n\nSince we do not want to introduce an implicit assumption that we only\nactually modify VMAs after OOM conditions might arise, add a 'give up on\noom' option and make an explicit contract that, should this flag be set, we\nabsolutely will not modify any VMAs should OOM arise and just bail out.\n\nSince it'd be very unusual for a user to try to vma_modify() with this flag\nset but be specifying a range within a VMA which ends up being split (which\ncan fail due to rlimit issues, not only OOM), we add a debug warning for\nthis condition.\n\nThe motivating reason for this is uffd release - <PERSON><PERSON><PERSON><PERSON><PERSON> (and <PERSON>'s VERY astute analysis) found a way in which an injected fault on\nallocation, triggering an OOM condition on commit merge, would result in\nuffd code becoming confused and treating an error value as if it were a VMA\npointer.\n\nTo avoid this, we make use of this new VMG flag to ensure that this never\noccurs, utilising the fact that, should we be clearing entire VMAs, we do\nnot wish an OOM event to be reported to us.\n\nMany thanks to <PERSON> for his excellent analysis and <PERSON><PERSON> for\nhis insightful and intelligent analysis of the situation, both of whom were\ninstrumental in this fix."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/vma: añadir la opción give_up_on_oom en modificar/fusionar, usar en la versión uffd Actualmente, si una fusión de VMA falla debido a una condición OOM que surge en la fusión de confirmación o un fallo al duplicar anon_vma, informamos de ello para que el llamador pueda gestionarlo. Sin embargo, hay casos en los que el llamador solo está intentando una fusión ostensiblemente y no le importa si falla debido a esta condición. Dado que no queremos introducir una suposición implícita de que solo modificamos realmente los VMA después de que puedan surgir condiciones OOM, agregue una opción 'renunciar a oom' y haga un contrato explícito de que, si se establece este indicador, no modificaremos en absoluto ningún VMA si surge OOM y simplemente nos retiraremos. Dado que sería muy inusual que un usuario intentara usar vma_modify() con este indicador activado, pero especificando un rango dentro de un VMA que termina dividiéndose (lo cual puede fallar debido a problemas con rlimit, no solo por OOM), añadimos una advertencia de depuración para esta condición. El motivo es la versión de uffd: syzkaller (y el astuto análisis de Pedro Falcato) encontró una forma en la que un fallo inyectado en la asignación, que desencadena una condición OOM al fusionar las confirmaciones, provocaba que el código de uffd se confundiera y tratara un valor de error como si fuera un puntero a un VMA. Para evitar esto, utilizamos este nuevo indicador VMG para asegurarnos de que esto nunca ocurra, aprovechando que, si borramos VMAs completas, no queremos que se nos informe de un evento OOM. Muchas gracias a Pedro Falcato por su excelente análisis y a Jann Horn por su perspicaz e inteligente análisis de la situación, ambos fundamentales en esta solución."}], "references": [{"url": "https://git.kernel.org/stable/c/41e6ddcaa0f18dda4c3fadf22533775a30d6f72f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b906c1ad25adce6ff35be19b65a1aa7d960fe1d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c103a75c61648203d731e3b97a6fbeea4003cb15", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-46726", "published_date": "2025-05-05T20:15:21.107", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Langroid is a framework for building large-language-model-powered applications. Prior to version 0.53.4, a LLM application leveraging `XMLToolMessage` class may be exposed to untrusted XML input that could result in DoS and/or exposing local files with sensitive information. Version 0.53.4 fixes the issue."}, {"lang": "es", "value": "Langroid es un framework para crear aplicaciones basadas en modelos de lenguaje de gran tamaño. Antes de la versión 0.53.4, una aplicación LLM que utiliza la clase `XMLToolMessage` podía estar expuesta a entradas XML no confiables, lo que podía provocar un ataque de denegación de servicio (DoS) o la exposición de archivos locales con información confidencial. La versión 0.53.4 soluciona este problema."}], "references": [{"url": "https://github.com/langroid/langroid/blob/df6227e6c079ec22bb2768498423148d6685acff/langroid/agent/xml_tool_message.py#L51-L52", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/langroid/langroid/commit/36e7e7db4dd1636de225c2c66c84052b1e9ac3c3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/langroid/langroid/security/advisories/GHSA-pw95-88fg-3j6f", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2023-53088", "published_date": "2025-05-02T16:15:27.760", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmptcp: fix UaF in listener shutdown\n\nAs reported by <PERSON> after having refactored the passive\nsocket initialization, the mptcp listener shutdown path is prone\nto an UaF issue.\n\n  BUG: KASAN: use-after-free in _raw_spin_lock_bh+0x73/0xe0\n  Write of size 4 at addr ffff88810cb23098 by task syz-executor731/1266\n\n  CPU: 1 PID: 1266 Comm: syz-executor731 Not tainted 6.2.0-rc59af4eaa31c1f6c00c8f1e448ed99a45c66340dd5 #6\n  Hardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.13.0-0-gf21b5a4aeb02-prebuilt.qemu.org 04/01/2014\n  Call Trace:\n   <TASK>\n   dump_stack_lvl+0x6e/0x91\n   print_report+0x16a/0x46f\n   kasan_report+0xad/0x130\n   kasan_check_range+0x14a/0x1a0\n   _raw_spin_lock_bh+0x73/0xe0\n   subflow_error_report+0x6d/0x110\n   sk_error_report+0x3b/0x190\n   tcp_disconnect+0x138c/0x1aa0\n   inet_child_forget+0x6f/0x2e0\n   inet_csk_listen_stop+0x209/0x1060\n   __mptcp_close_ssk+0x52d/0x610\n   mptcp_destroy_common+0x165/0x640\n   mptcp_destroy+0x13/0x80\n   __mptcp_destroy_sock+0xe7/0x270\n   __mptcp_close+0x70e/0x9b0\n   mptcp_close+0x2b/0x150\n   inet_release+0xe9/0x1f0\n   __sock_release+0xd2/0x280\n   sock_close+0x15/0x20\n   __fput+0x252/0xa20\n   task_work_run+0x169/0x250\n   exit_to_user_mode_prepare+0x113/0x120\n   syscall_exit_to_user_mode+0x1d/0x40\n   do_syscall_64+0x48/0x90\n   entry_SYSCALL_64_after_hwframe+0x72/0xdc\n\nThe msk grace period can legitly expire in between the last\nreference count dropped in mptcp_subflow_queue_clean() and\nthe later eventual access in inet_csk_listen_stop()\n\nAfter the previous patch we don't need anymore special-casing\nmsk listener socket cleanup: the mptcp worker will process each\nof the unaccepted msk sockets.\n\nJust drop the now unnecessary code.\n\nPlease note this commit depends on the two parent ones:\n\n  mptcp: refactor passive socket initialization\n  mptcp: use the workqueue to destroy unaccepted sockets"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mptcp: corrección de UaF en el apagado del oyente Como informó Christoph después de haber refactorizado la inicialización del socket pasivo, la ruta de apagado del oyente mptcp es propensa a un problema de UaF. ERROR: KASAN: use-after-free en _raw_spin_lock_bh+0x73/0xe0 Escritura de tamaño 4 en la dirección ffff88810cb23098 por la tarea syz-executor731/1266 CPU: 1 PID: 1266 Comm: syz-executor731 No contaminado 6.2.0-rc59af4eaa31c1f6c00c8f1e448ed99a45c66340dd5 #6 Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.13.0-0-gf21b5a4aeb02-prebuilt.qemu.org 01/04/2014 Rastreo de llamadas:  dump_stack_lvl+0x6e/0x91 print_report+0x16a/0x46f kasan_report+0xad/0x130 kasan_check_range+0x14a/0x1a0 _raw_spin_lock_bh+0x73/0xe0 subflow_error_report+0x6d/0x110 sk_error_report+0x3b/0x190 tcp_disconnect+0x138c/0x1aa0 inet_child_forget+0x6f/0x2e0 inet_csk_listen_stop+0x209/0x1060 __mptcp_close_ssk+0x52d/0x610 mptcp_destroy_common+0x165/0x640 mptcp_destroy+0x13/0x80 __mptcp_destroy_sock+0xe7/0x270 __mptcp_close+0x70e/0x9b0 mptcp_close+0x2b/0x150 inet_release+0xe9/0x1f0 __sock_release+0xd2/0x280 sock_close+0x15/0x20 __fput+0x252/0xa20 task_work_run+0x169/0x250 exit_to_user_mode_prepare+0x113/0x120 syscall_exit_to_user_mode+0x1d/0x40 do_syscall_64+0x48/0x90 entry_SYSCALL_64_after_hwframe+0x72/0xdc puede expirar legítimamente entre el último recuento de referencias introducido en mptcp_subflow_queue_clean() y el acceso eventual posterior en inet_csk_listen_stop(). Tras la actualización anterior, ya no necesitamos la limpieza de sockets del receptor MSK con casos especiales: el trabajador de mptcp procesará cada uno de los sockets MSK no aceptados. Simplemente elimine el código innecesario. Tenga en cuenta que esta confirmación depende de las dos principales: mptcp: refactorizar la inicialización pasiva de sockets. mptcp: usar la cola de trabajo para eliminar los sockets no aceptados."}], "references": [{"url": "https://git.kernel.org/stable/c/0a3f4f1f9c27215e4ddcd312558342e57b93e518", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0f4f4cf5d32f10543deb946a37111e714579511e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5564be74a22a61855f8b8c100d8c4abb003bb792", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
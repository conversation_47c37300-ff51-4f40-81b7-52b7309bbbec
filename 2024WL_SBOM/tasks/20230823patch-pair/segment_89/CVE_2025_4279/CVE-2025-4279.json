{"cve_id": "CVE-2025-4279", "published_date": "2025-05-05T19:15:57.477", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The External image replace plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the 'external_image_replace_get_posts::replace_post' function in all versions up to, and including, 1.0.8. This makes it possible for authenticated attackers, with contributor-level and above permissions, to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento External image replace para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de validación del tipo de archivo en la función 'external_image_replace_get_posts::replace_post' en todas las versiones hasta la 1.0.8 incluida. Esto permite que atacantes autenticados, con permisos de colaborador o superiores, carguen archivos arbitrarios en el servidor del sitio afectado, lo que podría posibilitar la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/external-image-replace/tags/1.0.8/class.php#L87", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/ee1624fd-d98b-4953-99dc-a952dda48aa1?source=cve", "source": "<EMAIL>", "tags": []}]}
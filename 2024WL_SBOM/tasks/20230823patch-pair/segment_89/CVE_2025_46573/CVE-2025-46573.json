{"cve_id": "CVE-2025-46573", "published_date": "2025-05-06T21:16:20.540", "last_modified_date": "2025-05-07T16:15:22.590", "descriptions": [{"lang": "en", "value": "passport-wsfed-saml2 provides passport strategy for both WS-fed and SAML2 protocol. A vulnerability present starting in version 3.0.5 up to and including version 4.6.3 allows an attacker to impersonate any user during SAML authentication by tampering with a valid SAML response. This can be done by adding attributes to the response. Users are affected specifically when the service provider is using `passport-wsfed-saml2` and a valid SAML Response signed by the Identity Provider can be obtained. Version 4.6.4 contains a fix for the vulnerability."}, {"lang": "es", "value": "passport-wsfed-saml2 proporciona una estrategia de pasaporte para los protocolos WS-fed y SAML2. Una vulnerabilidad, presente desde la versión 3.0.5 hasta la 4.6.3 incluida, permite a un atacante suplantar la identidad de cualquier usuario durante la autenticación SAML alterando una respuesta SAML válida. Esto se puede lograr añadiendo atributos a la respuesta. Los usuarios se ven afectados específicamente cuando el proveedor de servicios utiliza `passport-wsfed-saml2` y se puede obtener una respuesta SAML válida firmada por el proveedor de identidad. La versión 4.6.4 contiene una corrección para esta vulnerabilidad."}], "references": [{"url": "https://github.com/auth0/passport-wsfed-saml2/commit/e5cf3cc2a53748207f7a81bfba9195c8efa94181", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/auth0/passport-wsfed-saml2/security/advisories/GHSA-8gqj-226h-gm8r", "source": "<EMAIL>", "tags": []}]}
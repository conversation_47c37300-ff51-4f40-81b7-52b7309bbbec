{"cve_id": "CVE-2025-4303", "published_date": "2025-05-06T01:15:51.240", "last_modified_date": "2025-05-08T19:03:26.480", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Human Metapneumovirus Testing Management System 1.0. Affected by this issue is some unknown functionality of the file /add-phlebotomist.php. The manipulation of the argument empid leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Human Metapneumovirus Testing Management System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /add-phlebotomist.php. La manipulación del argumento empid provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/eneover/myCVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307406", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307406", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563706", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
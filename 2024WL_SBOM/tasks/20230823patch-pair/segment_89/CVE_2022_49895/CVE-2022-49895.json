{"cve_id": "CVE-2022-49895", "published_date": "2025-05-01T15:16:14.517", "last_modified_date": "2025-05-07T13:19:26.667", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncxl/region: Fix decoder allocation crash\n\nWhen an intermediate port's decoders have been exhausted by existing\nregions, and creating a new region with the port in question in it's\nhierarchical path is attempted, cxl_port_attach_region() fails to find a\nport decoder (as would be expected), and drops into the failure / cleanup\npath.\n\nHowever, during cleanup of the region reference, a sanity check attempts\nto dereference the decoder, which in the above case didn't exist. This\ncauses a NULL pointer dereference BUG.\n\nTo fix this, refactor the decoder allocation and de-allocation into\nhelper routines, and in this 'free' routine, check that the decoder,\n@cxld, is valid before attempting any operations on it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cxl/region: Fix decoder assignment crash Cuando los decodificadores de un puerto intermedio han sido agotados por las regiones existentes, y se intenta crear una nueva región con el puerto en cuestión en su ruta jerárquica, cxl_port_attach_region() no puede encontrar un decodificador de puerto (como sería de esperar), y cae en la ruta de error/limpieza. Sin embargo, durante la limpieza de la referencia de la región, una comprobación de cordura intenta desreferenciar el decodificador, que en el caso anterior no existía. Esto causa un ERROR de desreferencia de puntero NULL. Para corregir esto, refactorice la asignación y desasignación del decodificador en rutinas de ayuda, y en esta rutina 'libre', verifique que el decodificador, @cxld, sea válido antes de intentar cualquier operación en él."}], "references": [{"url": "https://git.kernel.org/stable/c/71ee71d7adcba648077997a29a91158d20c40b09", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c6813b5610ac53af73edd87a660d23a0511faa47", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
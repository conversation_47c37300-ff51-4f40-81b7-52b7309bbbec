{"cve_id": "CVE-2025-24338", "published_date": "2025-04-30T11:15:48.150", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "A vulnerability in the “Manages app data” functionality of the web application of ctrlX OS allows a remote authenticated (lowprivileged) attacker to execute arbitrary client-side code in the context of another user's browser via multiple crafted HTTP requests."}, {"lang": "es", "value": "Una vulnerabilidad en la funcionalidad “Administra datos de la aplicación” de la aplicación web de ctrlX OS permite a un atacante remoto autenticado (con pocos privilegios) ejecutar código arbitrario del lado del cliente en el contexto del navegador de otro usuario a través de múltiples solicitudes HTTP manipuladas."}], "references": [{"url": "https://psirt.bosch.com/security-advisories/BOSCH-SA-640452.html", "source": "<EMAIL>", "tags": []}]}
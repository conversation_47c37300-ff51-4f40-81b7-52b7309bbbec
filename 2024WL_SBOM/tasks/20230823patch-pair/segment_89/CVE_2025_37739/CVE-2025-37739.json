{"cve_id": "CVE-2025-37739", "published_date": "2025-05-01T13:15:52.500", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nf2fs: fix to avoid out-of-bounds access in f2fs_truncate_inode_blocks()\n\nsyzbot reports an UBSAN issue as below:\n\n------------[ cut here ]------------\nUBSAN: array-index-out-of-bounds in fs/f2fs/node.h:381:10\nindex 18446744073709550692 is out of range for type '__le32[5]' (aka 'unsigned int[5]')\nCPU: 0 UID: 0 PID: 5318 Comm: syz.0.0 Not tainted 6.14.0-rc3-syzkaller-00060-g6537cfb395f3 #0\nCall Trace:\n <TASK>\n __dump_stack lib/dump_stack.c:94 [inline]\n dump_stack_lvl+0x241/0x360 lib/dump_stack.c:120\n ubsan_epilogue lib/ubsan.c:231 [inline]\n __ubsan_handle_out_of_bounds+0x121/0x150 lib/ubsan.c:429\n get_nid fs/f2fs/node.h:381 [inline]\n f2fs_truncate_inode_blocks+0xa5e/0xf60 fs/f2fs/node.c:1181\n f2fs_do_truncate_blocks+0x782/0x1030 fs/f2fs/file.c:808\n f2fs_truncate_blocks+0x10d/0x300 fs/f2fs/file.c:836\n f2fs_truncate+0x417/0x720 fs/f2fs/file.c:886\n f2fs_file_write_iter+0x1bdb/0x2550 fs/f2fs/file.c:5093\n aio_write+0x56b/0x7c0 fs/aio.c:1633\n io_submit_one+0x8a7/0x18a0 fs/aio.c:2052\n __do_sys_io_submit fs/aio.c:2111 [inline]\n __se_sys_io_submit+0x171/0x2e0 fs/aio.c:2081\n do_syscall_x64 arch/x86/entry/common.c:52 [inline]\n do_syscall_64+0xf3/0x230 arch/x86/entry/common.c:83\n entry_SYSCALL_64_after_hwframe+0x77/0x7f\nRIP: 0033:0x7f238798cde9\n\nindex 18446744073709550692 (decimal, unsigned long long)\n= 0xfffffffffffffc64 (hexadecimal, unsigned long long)\n= -924 (decimal, long long)\n\nIn f2fs_truncate_inode_blocks(), UBSAN detects that get_nid() tries to\naccess .i_nid[-924], it means both offset[0] and level should zero.\n\nThe possible case should be in f2fs_do_truncate_blocks(), we try to\ntruncate inode size to zero, however, dn.ofs_in_node is zero and\ndn.node_page is not an inode page, so it fails to truncate inode page,\nand then pass zeroed free_from to f2fs_truncate_inode_blocks(), result\nin this issue.\n\n\tif (dn.ofs_in_node || IS_INODE(dn.node_page)) {\n\t\tf2fs_truncate_data_blocks_range(&dn, count);\n\t\tfree_from += count;\n\t}\n\nI guess the reason why dn.node_page is not an inode page could be: there\nare multiple nat entries share the same node block address, once the node\nblock address was reused, f2fs_get_node_page() may load a non-inode block.\n\nLet's add a sanity check for such condition to avoid out-of-bounds access\nissue."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: f2fs: corrección para evitar el acceso fuera de los límites en f2fs_truncate_inode_blocks() syzbot informa un problema de UBSAN como se muestra a continuación: ------------[ cortar aquí ]------------ UBSAN: array-index-out-of-bounds en fs/f2fs/node.h:381:10 el índice 18446744073709550692 está fuera de rango para el tipo '__le32[5]' (también conocido como 'unsigned int[5]') CPU: 0 UID: 0 PID: 5318 Comm: syz.0.0 No contaminado 6.14.0-rc3-syzkaller-00060-g6537cfb395f3 #0 Rastreo de llamadas:  __dump_stack lib/dump_stack.c:94 [en línea] dump_stack_lvl+0x241/0x360 lib/dump_stack.c:120 ubsan_epilogue lib/ubsan.c:231 [en línea] __ubsan_handle_out_of_bounds+0x121/0x150 lib/ubsan.c:429 get_nid fs/f2fs/node.h:381 [en línea] f2fs_truncate_inode_blocks+0xa5e/0xf60 fs/f2fs/node.c:1181 f2fs_do_truncate_blocks+0x782/0x1030 fs/f2fs/file.c:808 f2fs_truncate_blocks+0x10d/0x300 fs/f2fs/archivo.c:836 f2fs_truncate+0x417/0x720 fs/f2fs/archivo.c:886 f2fs_file_write_iter+0x1bdb/0x2550 fs/f2fs/archivo.c:5093 aio_write+0x56b/0x7c0 fs/aio.c:1633 io_submit_one+0x8a7/0x18a0 fs/aio.c:2052 __do_sys_io_submit fs/aio.c:2111 [en línea] __se_sys_io_submit+0x171/0x2e0 fs/aio.c:2081 do_syscall_x64 arch/x86/entry/common.c:52 [en línea] do_syscall_64+0xf3/0x230 arch/x86/entry/common.c:83 entry_SYSCALL_64_after_hwframe+0x77/0x7f RIP: 0033:0x7f238798cde9 índice 18446744073709550692 (decimal, unsigned long long) = 0xfffffffffffffc64 (hexadecimal, unsigned long long) = -924 (decimal, long long) En f2fs_truncate_inode_blocks(), UBSAN detecta que get_nid() intenta acceder a .i_nid[-924], lo que significa que tanto offset[0] como level deben ser cero. El caso posible debería estar en f2fs_do_truncate_blocks(), tratamos de truncar el tamaño del inodo a cero, sin embargo, dn.ofs_in_node es cero y dn.node_page no es una página de inodo, por lo que no puede truncar la página de inodo y luego pasa free_from en cero a f2fs_truncate_inode_blocks(), lo que da como resultado este problema. if (dn.ofs_in_node || IS_INODE(dn.node_page)) { f2fs_truncate_data_blocks_range(&amp;dn, count); free_from += count; } Supongo que la razón por la que dn.node_page no es una página de inodo podría ser: hay varias entradas nat que comparten la misma dirección de bloque de nodo, una vez que se reutilizó la dirección de bloque de nodo, f2fs_get_node_page() puede cargar un bloque que no sea de inodo. Agreguemos una verificación de cordura para tal condición para evitar problemas de acceso fuera de los límites."}], "references": [{"url": "https://git.kernel.org/stable/c/67e16ccba74dd8de0a7b10062f1e02d77432f573", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ba8b41d0aa4b82f90f0c416cb53fcef9696525d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8b5e5aac44fee122947a269f9034c048e4c295de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/98dbf2af63de0b551082c9bc48333910e009b09f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a67e1bf03c609a751d1740a1789af25e599966fa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d7242fd7946d4cba0411effb6b5048ca55125747", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e6494977bd4a83862118a05f57a8df40256951c0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ecc461331604b07cdbdb7360dbdf78471653264c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-44848", "published_date": "2025-05-01T17:15:51.140", "last_modified_date": "2025-05-21T19:47:27.850", "descriptions": [{"lang": "en", "value": "TOTOLINK CA600-PoE V5.3c.6665_B20180820 was found to contain a command injection vulnerability in the msg_process function via the Url parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA600-PoE V5.3c.6665_B20180820 contenía una vulnerabilidad de inyección de comandos en la función msg_process mediante el parámetro Url. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA600-PoE/msg_process_Url/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
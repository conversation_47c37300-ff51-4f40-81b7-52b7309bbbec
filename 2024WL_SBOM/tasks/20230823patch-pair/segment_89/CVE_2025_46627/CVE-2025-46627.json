{"cve_id": "CVE-2025-46627", "published_date": "2025-05-01T20:15:38.350", "last_modified_date": "2025-05-27T14:23:32.547", "descriptions": [{"lang": "en", "value": "Use of weak credentials in the Tenda RX2 Pro *********** allows an unauthenticated attacker to authenticate to the telnet service by calculating the root password based on easily-obtained device information. The password is based on the last two digits/octets of the MAC address."}, {"lang": "es", "value": "El uso de credenciales débiles en el Tenda RX2 Pro *********** permite a un atacante no autenticado autenticarse en el servicio Telnet calculando la contraseña root a partir de información del dispositivo fácilmente obtenible. La contraseña se basa en los dos últimos dígitos/octetos de la dirección MAC."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46627-calculated-os-root-password", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
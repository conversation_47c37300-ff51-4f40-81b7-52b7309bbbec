{"cve_id": "CVE-2025-4256", "published_date": "2025-05-05T01:15:48.970", "last_modified_date": "2025-06-12T19:22:02.567", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in SeaCMS 13.2. This vulnerability affects unknown code of the file /admin_paylog.php. The manipulation of the argument cstatus leads to cross site scripting. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en SeaCMS 13.2. Esta vulnerabilidad afecta al código desconocido del archivo /admin_paylog.php. La manipulación del argumento cstatus provoca ataques de cross site scriptin. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/seacms-net/CMS/issues/25", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/seacms-net/CMS/issues/25#issue-3007083568", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.307360", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307360", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562718", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
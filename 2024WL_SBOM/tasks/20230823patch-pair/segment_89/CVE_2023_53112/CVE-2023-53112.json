{"cve_id": "CVE-2023-53112", "published_date": "2025-05-02T16:15:30.140", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915/sseu: fix max_subslices array-index-out-of-bounds access\n\nIt seems that commit bc3c5e0809ae (\"drm/i915/sseu: Don't try to store EU\nmask internally in UAPI format\") exposed a potential out-of-bounds\naccess, reported by UBSAN as following on a laptop with a gen 11 i915\ncard:\n\n  UBSAN: array-index-out-of-bounds in drivers/gpu/drm/i915/gt/intel_sseu.c:65:27\n  index 6 is out of range for type 'u16 [6]'\n  CPU: 2 PID: 165 Comm: systemd-udevd Not tainted 6.2.0-9-generic #9-Ubuntu\n  Hardware name: Dell Inc. XPS 13 9300/077Y9N, BIOS 1.11.0 03/22/2022\n  Call Trace:\n   <TASK>\n   show_stack+0x4e/0x61\n   dump_stack_lvl+0x4a/0x6f\n   dump_stack+0x10/0x18\n   ubsan_epilogue+0x9/0x3a\n   __ubsan_handle_out_of_bounds.cold+0x42/0x47\n   gen11_compute_sseu_info+0x121/0x130 [i915]\n   intel_sseu_info_init+0x15d/0x2b0 [i915]\n   intel_gt_init_mmio+0x23/0x40 [i915]\n   i915_driver_mmio_probe+0x129/0x400 [i915]\n   ? intel_gt_probe_all+0x91/0x2e0 [i915]\n   i915_driver_probe+0xe1/0x3f0 [i915]\n   ? drm_privacy_screen_get+0x16d/0x190 [drm]\n   ? acpi_dev_found+0x64/0x80\n   i915_pci_probe+0xac/0x1b0 [i915]\n   ...\n\nAccording to the definition of sseu_dev_info, eu_mask->hsw is limited to\na maximum of GEN_MAX_SS_PER_HSW_SLICE (6) sub-slices, but\ngen11_sseu_info_init() can potentially set 8 sub-slices, in the\n!IS_JSL_EHL(gt->i915) case.\n\nFix this by reserving up to 8 slots for max_subslices in the eu_mask\nstruct.\n\n(cherry picked from commit 3cba09a6ac86ea1d456909626eb2685596c07822)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915/sseu: fix max_subslices array-index-out-of-bounds access Parece que el commit bc3c5e0809ae (\"drm/i915/sseu: No intente almacenar la máscara EU internamente en formato UAPI\") expuso un posible acceso fuera de los límites, informado por UBSAN de la siguiente manera en una computadora portátil con una tarjeta i915 gen 11: UBSAN: array-index-out-of-bounds en drivers/gpu/drm/i915/gt/intel_sseu.c:65:27 el índice 6 está fuera de rango para el tipo 'u16 [6]' CPU: 2 PID: 165 Comm: systemd-udevd No contaminado 6.2.0-9-generic #9-Ubuntu Nombre del hardware: Dell Inc. XPS 13 9300/077Y9N, BIOS 1.11.0 22/03/2022 Seguimiento de llamadas:  show_stack+0x4e/0x61 dump_stack_lvl+0x4a/0x6f dump_stack+0x10/0x18 ubsan_epilogue+0x9/0x3a __ubsan_handle_out_of_bounds.cold+0x42/0x47 gen11_compute_sseu_info+0x121/0x130 [i915] intel_sseu_info_init+0x15d/0x2b0 [i915] intel_gt_init_mmio+0x23/0x40 [i915] i915_driver_mmio_probe+0x129/0x400 [i915] ? intel_gt_probe_all+0x91/0x2e0 [i915] i915_driver_probe+0xe1/0x3f0 [i915] ? drm_privacy_screen_get+0x16d/0x190 [drm] ? acpi_dev_found+0x64/0x80 i915_pci_probe+0xac/0x1b0 [i915] ... Según la definición de sseu_dev_info, eu_mask-&gt;hsw está limitado a un máximo de GEN_MAX_SS_PER_HSW_SLICE (6) subsecciones, pero gen11_sseu_info_init() puede establecer potencialmente 8 subsecciones, en el caso de !IS_JSL_EHL(gt-&gt;i915). Para solucionar esto, reserve hasta 8 espacios para max_subslices en la estructura eu_mask. (Seleccionado de la confirmación 3cba09a6ac86ea1d456909626eb2685596c07822)"}], "references": [{"url": "https://git.kernel.org/stable/c/193c41926d152761764894f46e23b53c00186a82", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1a1682abf7399318ac074b1f2ac6a8c992b5b3da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/36b076ab6247cf0d2135b2ad6bb337617c3b5a1b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
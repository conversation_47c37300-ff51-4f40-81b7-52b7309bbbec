{"cve_id": "CVE-2025-23148", "published_date": "2025-05-01T13:15:50.670", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsoc: samsung: exynos-chipid: Add NULL pointer check in exynos_chipid_probe()\n\nsoc_dev_attr->revision could be NULL, thus,\na pointer check is added to prevent potential NULL pointer dereference.\nThis is similar to the fix in commit 3027e7b15b02\n(\"ice: Fix some null pointer dereference issues in ice_ptp.c\").\n\nThis issue is found by our static analysis tool."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: soc: samsung: exynos-chipid: Se ha añadido una comprobación de puntero nulo en exynos_chipid_probe(). soc_dev_attr-&gt;revision podría ser nulo, por lo que se ha añadido una comprobación de puntero para evitar posibles desreferencias de punteros nulos. Esto es similar a la corrección en el commit 3027e7b15b02 (\"ice: Se corrigen algunos problemas de desreferencia de puntero nulo en ice_ptp.c\"). Nuestra herramienta de análisis estático ha detectado este problema."}], "references": [{"url": "https://git.kernel.org/stable/c/4129760e462f45f14e61b10408ace61aa7c2ed30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/44a2572a0fdcf3e7565763690d579b998a8f0562", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/475b9b45dc32eba58ab794b5d47ac689fc018398", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4f51d169fd0d4821bce775618db024062b09a3f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5f80fd2ff8bfd13e41554741740e0ca8e6445ded", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ce469d23205249bb17c1135ccadea879576adfc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ee067cf0cf82429e9b204283c7d0d8d6891d10e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8222ef6cf29dd7cad21643228f96535cc02b327", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-46629", "published_date": "2025-05-01T20:15:38.660", "last_modified_date": "2025-05-27T14:24:23.877", "descriptions": [{"lang": "en", "value": "Lack of access controls in the 'ate' management binary of the Tenda RX2 Pro *********** allows an unauthenticated remote attacker to perform unauthorized configuration changes for any router where 'ate' has been enabled by sending a crafted UDP packet"}, {"lang": "es", "value": "La falta de controles de acceso en el binario de administración 'ate' del Tenda RX2 Pro *********** permite que un atacante remoto no autenticado realice cambios de configuración no autorizados para cualquier enrutador donde se haya habilitado 'ate' mediante el envío de un paquete UDP manipulado."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46629-lack-of-authentication-in-ate", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2022-49911", "published_date": "2025-05-01T15:16:16.260", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetfilter: ipset: enforce documented limit to prevent allocating huge memory\n\n<PERSON> reported that the hash:net,iface type of the ipset subsystem does\nnot limit adding the same network with different interfaces to a set, which\ncan lead to huge memory usage or allocation failure.\n\nThe quick reproducer is\n\n$ ipset create ACL.IN.ALL_PERMIT hash:net,iface hashsize 1048576 timeout 0\n$ for i in $(seq 0 100); do /sbin/ipset add ACL.IN.ALL_PERMIT 0.0.0.0/0,kaf_$i timeout 0 -exist; done\n\nThe backtrace when vmalloc fails:\n\n        [Tue Oct 25 00:13:08 2022] ipset: vmalloc error: size 1073741848, exceeds total pages\n        <...>\n        [Tue Oct 25 00:13:08 2022] Call Trace:\n        [Tue Oct 25 00:13:08 2022]  <TASK>\n        [Tue Oct 25 00:13:08 2022]  dump_stack_lvl+0x48/0x60\n        [Tue Oct 25 00:13:08 2022]  warn_alloc+0x155/0x180\n        [Tue Oct 25 00:13:08 2022]  __vmalloc_node_range+0x72a/0x760\n        [Tue Oct 25 00:13:08 2022]  ? hash_netiface4_add+0x7c0/0xb20\n        [Tue Oct 25 00:13:08 2022]  ? __kmalloc_large_node+0x4a/0x90\n        [Tue Oct 25 00:13:08 2022]  kvmalloc_node+0xa6/0xd0\n        [Tue Oct 25 00:13:08 2022]  ? hash_netiface4_resize+0x99/0x710\n        <...>\n\nThe fix is to enforce the limit documented in the ipset(8) manpage:\n\n>  The internal restriction of the hash:net,iface set type is that the same\n>  network prefix cannot be stored with more than 64 different interfaces\n>  in a single set."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netfilter: ipset: aplicar el límite documentado para evitar la asignación de grandes cantidades de memoria. Daniel Xu informó que el tipo hash:net,iface del subsistema ipset no limita la adición de la misma red con diferentes interfaces a un conjunto, lo que puede provocar un uso excesivo de memoria o fallos de asignación. El reproductor rápido es: $ ipset create ACL.IN.ALL_PERMIT hash:net,iface hashsize 1048576 timeout 0 $ for i in $(seq 0 100); do /sbin/ipset add ACL.IN.ALL_PERMIT 0.0.0.0/0,kaf_$i timeout 0 -exist; hecho El backtrace cuando vmalloc falla: [Tue Oct 25 00:13:08 2022] ipset: vmalloc error: size 1073741848, exceeds total pages &lt;...&gt; [Tue Oct 25 00:13:08 2022] Call Trace: [Tue Oct 25 00:13:08 2022]  [Tue Oct 25 00:13:08 2022] dump_stack_lvl+0x48/0x60 [Tue Oct 25 00:13:08 2022] warn_alloc+0x155/0x180 [Tue Oct 25 00:13:08 2022] __vmalloc_node_range+0x72a/0x760 [Tue Oct 25 00:13:08 2022] ? hash_netiface4_add+0x7c0/0xb20 [Tue Oct 25 00:13:08 2022] ? __kmalloc_large_node+0x4a/0x90 [Tue Oct 25 00:13:08 2022] kvmalloc_node+0xa6/0xd0 [Tue Oct 25 00:13:08 2022] ? hash_netiface4_resize+0x99/0x710 &lt;...&gt;  La solución es aplicar el límite documentado en la página de manual de ipset(8): &gt; La restricción interna del tipo de conjunto hash:net,iface es que el mismo prefijo de red no se puede almacenar con más de 64 interfaces diferentes en un solo conjunto."}], "references": [{"url": "https://git.kernel.org/stable/c/42d20d5e24575c9afa2d66d9a51e7386db9514f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/510841da1fcc16f702440ab58ef0b4d82a9056b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a37ef32fe5956fe9248df68f6a61997845ba047e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
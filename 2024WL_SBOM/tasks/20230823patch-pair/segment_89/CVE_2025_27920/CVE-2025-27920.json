{"cve_id": "CVE-2025-27920", "published_date": "2025-05-05T16:15:50.857", "last_modified_date": "2025-05-21T19:40:21.300", "descriptions": [{"lang": "en", "value": "Output Messenger before 2.0.63 was vulnerable to a directory traversal attack through improper file path handling. By using ../ sequences in parameters, attackers could access sensitive files outside the intended directory, potentially leading to configuration leakage or arbitrary file access."}, {"lang": "es", "value": "Output Messenger, antes de la versión 2.0.63, era vulnerable a un ataque de salto de directorio debido a la gestión incorrecta de las rutas de archivo. Al usar secuencias ../ en los parámetros, los atacantes podían acceder a archivos confidenciales fuera del directorio previsto, lo que podía provocar fugas de configuración o acceso arbitrario a archivos."}], "references": [{"url": "https://www.outputmessenger.com/cve-2025-27920/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.srimax.com/products-2/output-messenger/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.microsoft.com/en-us/security/blog/2025/05/12/marbled-dust-leverages-zero-day-in-output-messenger-for-regional-espionage/", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Mitigation", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-47229", "published_date": "2025-05-03T03:15:28.183", "last_modified_date": "2025-07-18T15:54:13.770", "descriptions": [{"lang": "en", "value": "libpspp-core.a in GNU PSPP through 2.0.1 allows attackers to cause a denial of service (var_set_leave_quiet assertion failure and application exit) via crafted input data, such as data that triggers a call from src/data/dictionary.c code into src/data/variable.c code."}, {"lang": "es", "value": "libpspp-core.a en GNU PSPP hasta 2.0.1 permite a los atacantes provocar una denegación de servicio (error de afirmación var_set_leave_quiet y salida de la aplicación) a través de datos de entrada manipulados, como datos que activan una llamada desde el código src/data/dictionary.c al código src/data/variable.c."}], "references": [{"url": "https://savannah.gnu.org/bugs/?67049", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}
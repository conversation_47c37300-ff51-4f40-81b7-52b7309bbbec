{"cve_id": "CVE-2022-49855", "published_date": "2025-05-01T15:16:09.090", "last_modified_date": "2025-05-07T13:31:58.267", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: wwan: iosm: fix memory leak in ipc_pcie_read_bios_cfg\n\nipc_pcie_read_bios_cfg() is using the acpi_evaluate_dsm() to\nobtain the wwan power state configuration from BIOS but is\nnot freeing the acpi_object. The acpi_evaluate_dsm() returned\nacpi_object to be freed.\n\nFree the acpi_object after use."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: wwan: iosm: se corrige la pérdida de memoria en ipc_pcie_read_bios_cfg. ipc_pcie_read_bios_cfg() utiliza acpi_evaluate_dsm() para obtener la configuración del estado de energía de wwan desde la BIOS, pero no libera acpi_object. Acpi_evaluate_dsm() devolvió acpi_object para su liberación. Libere acpi_object después de su uso."}], "references": [{"url": "https://git.kernel.org/stable/c/13b1ea861e8aeb701bcfbfe436b943efa2d44029", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7560ceef4d2832a67e8781d924e129c7f542376f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d38a648d2d6cc7bee11c6f533ff9426a00c2a74c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53080", "published_date": "2025-05-02T16:15:27.020", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nxsk: Add missing overflow check in xdp_umem_reg\n\nThe number of chunks can overflow u32. Make sure to return -EINVAL on\noverflow. Also remove a redundant u32 cast assigning umem->npgs."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: xsk: Se ha añadido una comprobación de desbordamiento faltante en xdp_umem_reg. El número de fragmentos puede desbordar u32. Asegúrese de devolver -EINVAL en caso de desbordamiento. También se ha eliminado una conversión u32 redundante que asigna umem-&gt;npgs."}], "references": [{"url": "https://git.kernel.org/stable/c/3cfc3564411acf96bf2fb791f706a1aa4f872c1d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/580634b03a55f04a3c1968bcbd97736c079c6601", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a069909acc4435eeb41d05ccc03baa447cc01b7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bb2e3bfb2a79db0c2057c6f701b782954394c67f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c7df4813b149362248d6ef7be41a311e27bf75fe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
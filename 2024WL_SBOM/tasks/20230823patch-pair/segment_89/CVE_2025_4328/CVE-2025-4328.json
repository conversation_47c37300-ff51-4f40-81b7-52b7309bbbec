{"cve_id": "CVE-2025-4328", "published_date": "2025-05-06T07:15:48.823", "last_modified_date": "2025-05-07T14:13:35.980", "descriptions": [{"lang": "en", "value": "A vulnerability was found in fp2952 spring-cloud-base up to 7f050dc6db9afab82c5ce1d41cd74ed255ec9bfa. It has been declared as problematic. Affected by this vulnerability is the function sendBack of the file /spring-cloud-base-master/auth-center/auth-center-provider/src/main/java/com/peng/auth/provider/config/web/MvcController.java of the component HTTP Header Handler. The manipulation of the argument Referer leads to open redirect. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en fp2952 spring-cloud-base hasta 7f050dc6db9afab82c5ce1d41cd74ed255ec9bfa. Se ha declarado problemática. Esta vulnerabilidad afecta a la función \"sendBack\" del archivo /spring-cloud-base-master/auth-center/auth-center-provider/src/main/java/com/peng/auth/provider/config/web/MvcController.java del componente HTTP Header Handler. La manipulación del argumento \"Referer\" provoca una redirección abierta. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza el enfoque de lanzamiento continuo para garantizar una entrega continua. Por lo tanto, no se dispone de información sobre las versiones afectadas ni sobre las actualizadas."}], "references": [{"url": "https://github.com/ShenxiuSec/cve-proofs/blob/main/POC-20250423-01.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.307429", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.307429", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.564161", "source": "<EMAIL>", "tags": []}]}
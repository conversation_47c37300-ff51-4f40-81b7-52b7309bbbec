{"cve_id": "CVE-2024-58253", "published_date": "2025-05-02T20:15:19.793", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the obfstr crate before 0.4.4 for Rust, the obfstr! argument type is not restricted to string slices, leading to invalid UTF-8 conversion that produces an invalid value."}, {"lang": "es", "value": "Obfstr crate anterior a la versión 0.4.4 para Rust, el tipo de argumento obfstr! no está restringido a segmentos de cadena, lo que genera una conversión UTF-8 no válida que produce un valor no válido."}], "references": [{"url": "https://github.com/CasualX/obfstr/compare/v0.4.3...v0.4.4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/CasualX/obfstr/issues/60", "source": "<EMAIL>", "tags": []}]}
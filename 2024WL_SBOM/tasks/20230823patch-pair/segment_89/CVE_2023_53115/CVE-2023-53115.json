{"cve_id": "CVE-2023-53115", "published_date": "2025-05-02T16:15:30.590", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: mpi3mr: Fix memory leaks in mpi3mr_init_ioc()\n\nDon't allocate memory again when IOC is being reinitialized."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: mpi3mr: corrige fugas de memoria en mpi3mr_init_ioc() No vuelva a asignar memoria cuando se reinicialice IOC."}], "references": [{"url": "https://git.kernel.org/stable/c/5aab9342f12f980b64617a034d121efbbf09100a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7277b4eec2f25a0653646ba95b1f25fa16be1d6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c798304470cab88723d895726d17fcb96472e0e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
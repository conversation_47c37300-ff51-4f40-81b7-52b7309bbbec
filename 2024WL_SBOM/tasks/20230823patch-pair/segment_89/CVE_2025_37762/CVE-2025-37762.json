{"cve_id": "CVE-2025-37762", "published_date": "2025-05-01T14:15:38.500", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/virtio: Fix missed dmabuf unpinning in error path of prepare_fb()\n\nCorrect error handling in prepare_fb() to fix leaking resources when\nerror happens."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/virtio: Se corrige la falta de fijación de dmabuf en la ruta de error de prepare_fb(). Se corrige el manejo de errores en prepare_fb() para corregir la pérdida de recursos cuando ocurre un error."}], "references": [{"url": "https://git.kernel.org/stable/c/395cc80051f8da267b27496a4029dd931a198855", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fe983e925bf7062d7b975357afcbc77bb7f354d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
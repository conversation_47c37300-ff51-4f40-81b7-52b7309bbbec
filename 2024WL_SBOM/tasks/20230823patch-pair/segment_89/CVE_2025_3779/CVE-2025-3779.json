{"cve_id": "CVE-2025-3779", "published_date": "2025-05-03T03:15:27.890", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The Personizely plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘widgetId’ parameter in all versions up to, and including, 0.10 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Personizely para WordPress es vulnerable a Cross-Site Scripting almacenado a través del parámetro 'widgetId' en todas las versiones hasta la 0.10 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/personizely/tags/0.10/class.personizely.php#L49", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/personizely/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f07d22ef-5afd-48a4-9e67-31a3ab3efdd6?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27132", "published_date": "2025-05-06T09:15:25.300", "last_modified_date": "2025-05-09T19:14:30.360", "descriptions": [{"lang": "en", "value": "in OpenHarmony v5.0.3 and prior versions allow a local attacker arbitrary code execution in pre-installed apps through out-of-bounds write. This vulnerability can be exploited only in restricted scenarios."}, {"lang": "es", "value": "En OpenHarmony v5.0.3 y versiones anteriores, se permite a un atacante local la ejecución de código arbitrario en aplicaciones preinstaladas mediante escritura fuera de los límites. Esta vulnerabilidad solo se puede explotar en escenarios restringidos."}], "references": [{"url": "https://gitee.com/openharmony/security/blob/master/zh/security-disclosure/2025/2025-05.md", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
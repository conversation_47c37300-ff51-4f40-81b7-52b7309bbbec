{"cve_id": "CVE-2022-49912", "published_date": "2025-05-01T15:16:16.363", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: fix ulist leaks in error paths of qgroup self tests\n\nIn the test_no_shared_qgroup() and test_multiple_refs() qgroup self tests,\nif we fail to add the tree ref, remove the extent item or remove the\nextent ref, we are returning from the test function without freeing the\n\"old_roots\" ulist that was allocated by the previous calls to\nbtrfs_find_all_roots(). Fix that by calling ulist_free() before returning."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: se corrigen fugas de ulist en las rutas de error de las autopruebas de qgroup. En las autopruebas de qgroup test_no_shared_qgroup() y test_multiple_refs(), si no se añade la referencia del árbol, se elimina el elemento de extensión o se elimina la referencia de extensión, se regresa de la función de prueba sin liberar la ulist \"old_roots\" asignada por las llamadas anteriores a btrfs_find_all_roots(). Se puede solucionar llamando a ulist_free() antes de regresar."}], "references": [{"url": "https://git.kernel.org/stable/c/0a0dead4ad1a2e2a9bdf133ef45111d7c8daef84", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/203204798831c35d855ecc6417d98267d2d2184b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3f58283d83a588ff5da62fc150de19e798ed2ec2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5d1a47ebf84540e40b5b43fc21aef0d6c0f627d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d37de92b38932d40e4a251e876cc388f9aee5f42", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d81370396025cf63a7a1b5f8bb25a3479203b2ca", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da7003434bcab0ae9aba3f2c003e734cae093326", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f46ea5fa3320dca4fe0c0926b49a5f14cb85de62", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
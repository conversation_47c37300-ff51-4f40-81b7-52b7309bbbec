{"cve_id": "CVE-2025-3707", "published_date": "2025-05-02T04:15:55.140", "last_modified_date": "2025-05-07T16:50:32.967", "descriptions": [{"lang": "en", "value": "The eHDR CTMS from Sunnet has a SQL Injection vulnerability, allowing remote attackers with regular privileges to inject arbitrary SQL command to read database contents."}, {"lang": "es", "value": "El eHDR CTMS de Sunnet tiene una vulnerabilidad de inyección SQL, que permite a atacantes remotos con privilegios regulares inyectar comandos SQL arbitrarios para leer el contenido de la base de datos."}], "references": [{"url": "https://www.twcert.org.tw/en/cp-139-10084-d7c47-2.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.twcert.org.tw/tw/cp-132-10083-4ed7f-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-49915", "published_date": "2025-05-01T15:16:16.710", "last_modified_date": "2025-05-07T13:26:51.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmISDN: fix possible memory leak in mISDN_register_device()\n\nAfer commit 1fa5ae857bb1 (\"driver core: get rid of struct device's\nbus_id string array\"), the name of device is allocated dynamically,\nadd put_device() to give up the reference, so that the name can be\nfreed in kobject_cleanup() when the refcount is 0.\n\nSet device class before put_device() to avoid null release() function\nWARN message in device_release()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mISDN: se corrige una posible pérdida de memoria en mISDN_register_device() Después de el commit 1fa5ae857bb1 (\"núcleo del controlador: deshacerse de la matriz de cadenas bus_id del dispositivo de estructura\"), el nombre del dispositivo se asigna dinámicamente, agregue put_device() para renunciar a la referencia, de modo que el nombre se pueda liberar en kobject_cleanup() cuando el refcount sea 0. Establezca la clase del dispositivo antes de put_device() para evitar el mensaje WARN de la función release() nula en device_release()."}], "references": [{"url": "https://git.kernel.org/stable/c/029d5b7688a2f3a86f2a3be5a6ba9cc968c80e41", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/080aabfb29b2ee9cbb8894a1d039651943d3773e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/0d4e91efcaee081e919b3c50e875ecbb84290e41", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2ff6b669523d3b3d253a044fa9636a67d0694995", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a636fc5a7cabd05699b5692ad838c2c7a3abec7b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d1d1aede313eb2b9a84afd60ff6cfb7c33631e0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e77d213843e67b4373285712699b692f9c743f61", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e7d1d4d9ac0dfa40be4c2c8abd0731659869b297", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-2816", "published_date": "2025-05-01T03:15:14.627", "last_modified_date": "2025-05-12T19:38:14.733", "descriptions": [{"lang": "en", "value": "The Page View Count plugin for WordPress is vulnerable to unauthorized modification of data that can lead to a denial of service due to a missing capability check on the yellow_message_dontshow() function in versions 2.8.0 to 2.8.4. This makes it possible for authenticated attackers, with Subscriber-level access and above, to update option values to one on the WordPress site. This can be leveraged to update an option that would create an error on the site and deny service to legitimate users or be used to set some values to true such as registration."}, {"lang": "es", "value": "El complemento Page View Count para WordPress es vulnerable a la modificación no autorizada de datos, lo que puede provocar una denegación de servicio debido a la falta de una comprobación de capacidad en la función yellow_message_dontshow() en las versiones 2.8.0 a 2.8.4. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, actualicen los valores de las opciones a uno en el sitio de WordPress. Esto puede aprovecharse para actualizar una opción que generaría un error en el sitio y denegaría el servicio a usuarios legítimos, o para establecer algunos valores como \"verdaderos\", como el registro."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3282975%40page-views-count&new=3282975%40page-views-count&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e6fb9558-06e5-4297-93df-ee9a6971f0ec?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
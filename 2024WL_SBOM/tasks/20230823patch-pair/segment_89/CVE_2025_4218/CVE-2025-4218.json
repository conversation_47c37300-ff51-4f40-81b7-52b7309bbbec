{"cve_id": "CVE-2025-4218", "published_date": "2025-05-02T21:15:24.057", "last_modified_date": "2025-06-17T14:16:53.353", "descriptions": [{"lang": "en", "value": "A vulnerability was found in handrew browserpilot up to 0.2.51. It has been declared as critical. Affected by this vulnerability is the function GPTSeleniumAgent of the file browserpilot/browserpilot/agents/gpt_selenium_agent.py. The manipulation of the argument instructions leads to code injection. The attack needs to be approached locally. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en la versión 0.2.51 de handrew browserpilot. Se ha declarado crítica. Esta vulnerabilidad afecta a la función GPTSeleniumAgent del archivo browserpilot/browserpilot/agents/gpt_selenium_agent.py. La manipulación de las instrucciones de los argumentos provoca la inyección de código. El ataque debe abordarse localmente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/handrew/browserpilot/issues/20", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/handrew/browserpilot/issues/20#issue-2999815850", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307195", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307195", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562383", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
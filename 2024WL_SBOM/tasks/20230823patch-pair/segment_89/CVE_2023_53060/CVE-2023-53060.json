{"cve_id": "CVE-2023-53060", "published_date": "2025-05-02T16:15:25.060", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nigb: revert rtnl_lock() that causes deadlock\n\nThe commit 6faee3d4ee8b (\"igb: Add lock to avoid data race\") adds\nrtnl_lock to eliminate a false data race shown below\n\n (FREE from device detaching)      |   (USE from netdev core)\nigb_remove                         |  igb_ndo_get_vf_config\n igb_disable_sriov                 |  vf >= adapter->vfs_allocated_count?\n  kfree(adapter->vf_data)          |\n  adapter->vfs_allocated_count = 0 |\n                                   |    memcpy(... adapter->vf_data[vf]\n\nThe above race will never happen and the extra rtnl_lock causes deadlock\nbelow\n\n[  141.420169]  <TASK>\n[  141.420672]  __schedule+0x2dd/0x840\n[  141.421427]  schedule+0x50/0xc0\n[  141.422041]  schedule_preempt_disabled+0x11/0x20\n[  141.422678]  __mutex_lock.isra.13+0x431/0x6b0\n[  141.423324]  unregister_netdev+0xe/0x20\n[  141.423578]  igbvf_remove+0x45/0xe0 [igbvf]\n[  141.423791]  pci_device_remove+0x36/0xb0\n[  141.423990]  device_release_driver_internal+0xc1/0x160\n[  141.424270]  pci_stop_bus_device+0x6d/0x90\n[  141.424507]  pci_stop_and_remove_bus_device+0xe/0x20\n[  141.424789]  pci_iov_remove_virtfn+0xba/0x120\n[  141.425452]  sriov_disable+0x2f/0xf0\n[  141.425679]  igb_disable_sriov+0x4e/0x100 [igb]\n[  141.426353]  igb_remove+0xa0/0x130 [igb]\n[  141.426599]  pci_device_remove+0x36/0xb0\n[  141.426796]  device_release_driver_internal+0xc1/0x160\n[  141.427060]  driver_detach+0x44/0x90\n[  141.427253]  bus_remove_driver+0x55/0xe0\n[  141.427477]  pci_unregister_driver+0x2a/0xa0\n[  141.428296]  __x64_sys_delete_module+0x141/0x2b0\n[  141.429126]  ? mntput_no_expire+0x4a/0x240\n[  141.429363]  ? syscall_trace_enter.isra.19+0x126/0x1a0\n[  141.429653]  do_syscall_64+0x5b/0x80\n[  141.429847]  ? exit_to_user_mode_prepare+0x14d/0x1c0\n[  141.430109]  ? syscall_exit_to_user_mode+0x12/0x30\n[  141.430849]  ? do_syscall_64+0x67/0x80\n[  141.431083]  ? syscall_exit_to_user_mode_prepare+0x183/0x1b0\n[  141.431770]  ? syscall_exit_to_user_mode+0x12/0x30\n[  141.432482]  ? do_syscall_64+0x67/0x80\n[  141.432714]  ? exc_page_fault+0x64/0x140\n[  141.432911]  entry_SYSCALL_64_after_hwframe+0x72/0xdc\n\nSince the igb_disable_sriov() will call pci_disable_sriov() before\nreleasing any resources, the netdev core will synchronize the cleanup to\navoid any races. This patch removes the useless rtnl_(un)lock to guarantee\ncorrectness."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: igb: revertir rtnl_lock() que causa un bloqueo el commit 6faee3d4ee8b (\"igb: Agregar bloqueo para evitar ejecución de datos\") agrega rtnl_lock para eliminar una ejecución de datos falsa que se muestra a continuación (GRATIS de la desconexión del dispositivo) | (USO desde el núcleo netdev) igb_remove | igb_ndo_get_vf_config igb_disable_sriov | vf &gt;= adapter-&gt;vfs_allocated_count? kfree(adapter-&gt;vf_data) | adapter-&gt;vfs_allocated_count = 0 | | memcpy(... adapter-&gt;vf_data[vf] La ejecución anterior nunca ocurrirá y el rtnl_lock adicional provoca un bloqueo a continuación [ 141.420169]  [ 141.420672] __schedule+0x2dd/0x840 [ 141.421427] schedule+0x50/0xc0 [ 141.422041] schedule_preempt_disabled+0x11/0x20 [ 141.422678] __mutex_lock.isra.13+0x431/0x6b0 [ 141.423324] unregister_netdev+0xe/0x20 [ 141.423578] igbvf_remove+0x45/0xe0 [igbvf] [ 141.423791] pci_device_remove+0x36/0xb0 [ 141.423990] device_release_driver_internal+0xc1/0x160 [ 141.424270] pci_stop_bus_device+0x6d/0x90 [ 141.424507] pci_stop_and_remove_bus_device+0xe/0x20 [ 141.424789] pci_iov_remove_virtfn+0xba/0x120 [ 141.425452] sriov_disable+0x2f/0xf0 [ 141.425679] igb_disable_sriov+0x4e/0x100 [igb] [ 141.426353] igb_remove+0xa0/0x130 [igb] [ 141.426599] pci_device_remove+0x36/0xb0 [ 141.426796] device_release_driver_internal+0xc1/0x160 [ 141.427060] driver_detach+0x44/0x90 [ 141.427253] bus_remove_driver+0x55/0xe0 [ 141.427477] pci_unregister_driver+0x2a/0xa0 [ 141.428296] __x64_sys_delete_module+0x141/0x2b0 [ 141.429126] ? mntput_no_expire+0x4a/0x240 [ 141.429363] ? syscall_trace_enter.isra.19+0x126/0x1a0 [ 141.429653] ? do_syscall_64+0x5b/0x80 [ 141.429847] ? salir_al_modo_usuario_prepare+0x14d/0x1c0 [ 141.430109] ? syscall_salir_al_modo_usuario+0x12/0x30 [ 141.430849] ? do_syscall_64+0x67/0x80 [ 141.431083] ? syscall_exit_to_user_mode_prepare+0x183/0x1b0 [141.431770] ? syscall_exit_to_user_mode+0x12/0x30 [141.432482] ? do_syscall_64+0x67/0x80 [141.432714] ? exc_page_fault+0x64/0x140 [141.432911] entry_SYSCALL_64_after_hwframe+0x72/0xdc. Dado que igb_disable_sriov() llamará a pci_disable_sriov() antes de liberar recursos, el núcleo netdev sincronizará la limpieza para evitar ejecucións. Este parche elimina el bloqueo rtnl_(un)lock innecesario para garantizar la corrección."}], "references": [{"url": "https://git.kernel.org/stable/c/0dabb72b923e17cb3b4ac99ea1adc9ef35116930", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4d2626e10709ff8474ffd1a9db3cf4647569e89c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/62a64645749926f9d75af82a96440941f22b046f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/65f69851e44d71248b952a687e44759a7abb5016", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/66e5577cabc3d463eea540332727929d0ace41c6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d845e9a485f287181ff81567c3900a8e7ad1e28", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cd1e320ac0958298c2774605ad050483f33a21f2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de91528d8ba274c614a2265077d695c61e31fd43", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
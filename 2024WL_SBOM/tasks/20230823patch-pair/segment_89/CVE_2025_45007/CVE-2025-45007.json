{"cve_id": "CVE-2025-45007", "published_date": "2025-04-30T13:15:49.310", "last_modified_date": "2025-05-09T13:45:28.627", "descriptions": [{"lang": "en", "value": "A Reflected Cross-Site Scripting (XSS) vulnerability was discovered in the profile.php file of PHPGurukul Timetable Generator System v1.0. This vulnerability allows remote attackers to execute arbitrary JavaScript code via the adminname POST request parameter."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de Cross-Site Scripting (XSS) reflejado en el archivo profile.php de PHPGurukul Timetable Generator System v1.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código JavaScript arbitrario mediante el parámetro de solicitud POST adminname."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Time-Table-Generator-System/xss-injection.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-4318", "published_date": "2025-05-05T19:15:57.847", "last_modified_date": "2025-07-18T17:15:43.943", "descriptions": [{"lang": "en", "value": "The AWS Amplify Studio UI component property expressions in the aws-amplify/amplify-codegen-ui package lack input validation. This could potentially allow an authenticated user who has access to create or modify components to run arbitrary JavaScript code during the component rendering and build process."}, {"lang": "es", "value": "Las expresiones de propiedad del componente de interfaz de usuario de AWS Amplify Studio en el paquete aws-amplify/amplify-codegen-ui carecen de validación de entrada. Esto podría permitir que un usuario autenticado con acceso para crear o modificar componentes ejecute código JavaScript arbitrario durante el proceso de renderizado y compilación de componentes."}], "references": [{"url": "https://aws.amazon.com/security/security-bulletins/AWS-2025-010/", "source": "ff89ba41-3aa1-4d27-914a-91399e9639e5", "tags": []}, {"url": "https://blog.securelayer7.net/cve-2025-4318-aws-amplify-rce/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://github.com/aws-amplify/amplify-codegen-ui/security/advisories/GHSA-hf3j-86p7-mfw8", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
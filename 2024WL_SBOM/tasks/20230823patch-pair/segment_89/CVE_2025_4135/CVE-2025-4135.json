{"cve_id": "CVE-2025-4135", "published_date": "2025-04-30T18:15:48.400", "last_modified_date": "2025-06-23T15:13:00.287", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Netgear WG302v2 up to 5.2.9 and classified as critical. Affected by this issue is the function ui_get_input_value. The manipulation of the argument host leads to command injection. The attack may be launched remotely. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se detectó una vulnerabilidad en Netgear WG302v2 hasta la versión 5.2.9, clasificada como crítica. Este problema afecta a la función ui_get_input_value. La manipulación del argumento \"host\" provoca la inyección de comandos. El ataque puede ejecutarse remotamente. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/jylsec/vuldb/blob/main/Netgear/netgear_WG302v2/Command_injection-basic_settings_handler-static-ip-update/README.md", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://vuldb.com/?ctiid.306626", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306626", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560779", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.netgear.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2025-23154", "published_date": "2025-05-01T13:15:51.310", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nio_uring/net: fix io_req_post_cqe abuse by send bundle\n\n[  114.987980][ T5313] WARNING: CPU: 6 PID: 5313 at io_uring/io_uring.c:872 io_req_post_cqe+0x12e/0x4f0\n[  114.991597][ T5313] RIP: 0010:io_req_post_cqe+0x12e/0x4f0\n[  115.001880][ T5313] Call Trace:\n[  115.002222][ T5313]  <TASK>\n[  115.007813][ T5313]  io_send+0x4fe/0x10f0\n[  115.009317][ T5313]  io_issue_sqe+0x1a6/0x1740\n[  115.012094][ T5313]  io_wq_submit_work+0x38b/0xed0\n[  115.013223][ T5313]  io_worker_handle_work+0x62a/0x1600\n[  115.013876][ T5313]  io_wq_worker+0x34f/0xdf0\n\nAs the comment states, io_req_post_cqe() should only be used by\nmultishot requests, i.e. REQ_F_APOLL_MULTISHOT, which bundled sends are\nnot. Add a flag signifying whether a request wants to post multiple\nCQEs. Eventually REQ_F_APOLL_MULTISHOT should imply the new flag, but\nthat's left out for simplicity."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: io_uring/net: corrección del abuso de io_req_post_cqe por parte del paquete de envío [ 114.987980][ T5313] ADVERTENCIA: CPU: 6 PID: 5313 en io_uring/io_uring.c:872 io_req_post_cqe+0x12e/0x4f0 [ 114.991597][ T5313] RIP: 0010:io_req_post_cqe+0x12e/0x4f0 [ 115.001880][ T5313] Rastreo de llamadas: [ 115.002222][ T5313]  [ 115.007813][ T5313] io_send+0x4fe/0x10f0 [ 115.009317][ T5313] io_issue_sqe+0x1a6/0x1740 [ 115.012094][ T5313] io_wq_submit_work+0x38b/0xed0 [ 115.013223][ T5313] io_worker_handle_work+0x62a/0x1600 [ 115.013876][ T5313] io_wq_worker+0x34f/0xdf0 Como se indica en el comentario, io_req_post_cqe() solo debe usarse en solicitudes multienvío, como REQ_F_APOLL_MULTISHOT, que no se usan en envíos agrupados. Agregue un indicador que indique si una solicitud desea publicar múltiples CQE. Finalmente, REQ_F_APOLL_MULTISHOT debería implicar la nueva bandera, pero esto se omite para simplificar."}], "references": [{"url": "https://git.kernel.org/stable/c/6889ae1b4df1579bcdffef023e2ea9a982565dff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7888c9fc0b2d3636f2e821ed1ad3c6920fa8e378", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9aa804e6b9696998308095fb9d335046a71550f1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b7c6d081c19a5e11bbd77bb97a62cff2b6b21cb5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
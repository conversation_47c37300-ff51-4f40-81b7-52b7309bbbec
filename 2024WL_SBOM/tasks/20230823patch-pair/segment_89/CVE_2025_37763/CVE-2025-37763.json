{"cve_id": "CVE-2025-37763", "published_date": "2025-05-01T14:15:38.817", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/imagination: take paired job reference\n\nFor paired jobs, have the fragment job take a reference on the\ngeometry job, so that the geometry job cannot be freed until\nthe fragment job has finished with it.\n\nThe geometry job structure is accessed when the fragment job is being\nprepared by the GPU scheduler. Taking the reference prevents the\ngeometry job being freed until the fragment job no longer requires it.\n\nFixes a use after free bug detected by KASAN:\n\n[  124.256386] BUG: KASAN: slab-use-after-free in pvr_queue_prepare_job+0x108/0x868 [powervr]\n[  124.264893] Read of size 1 at addr ffff0000084cb960 by task kworker/u16:4/63"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/imagination: toma de referencia de trabajo emparejado. Para trabajos emparejados, el trabajo de fragmento toma una referencia en el trabajo de geometría, de modo que este no pueda liberarse hasta que el trabajo de fragmento haya terminado con él. Se accede a la estructura del trabajo de geometría cuando el programador de GPU prepara el trabajo de fragmento. Tomar la referencia impide que el trabajo de geometría se libere hasta que el trabajo de fragmento ya no la necesite. Corrige un error de use-after-free detectado por KASAN: [124.256386] ERROR: KASAN: slab-use-after-free en pvr_queue_prepare_job+0x108/0x868 [powervr] [124.264893] Lectura de tamaño 1 en la dirección ffff0000084cb960 por la tarea kworker/u16:4/63"}], "references": [{"url": "https://git.kernel.org/stable/c/4ba2abe154ef68f9612eee9d6fbfe53a1736b064", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b5a6f97a78e2fc008fd6503b7040cb7e1120b873", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c90b95e12eb88d23740e5ea2c43d71675d17ac8d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37738", "published_date": "2025-05-01T13:15:52.383", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: ignore xattrs past end\n\nOnce inside 'ext4_xattr_inode_dec_ref_all' we should\nignore xattrs entries past the 'end' entry.\n\nThis fixes the following KASAN reported issue:\n\n==================================================================\nBUG: KASAN: slab-use-after-free in ext4_xattr_inode_dec_ref_all+0xb8c/0xe90\nRead of size 4 at addr ffff888012c120c4 by task repro/2065\n\nCPU: 1 UID: 0 PID: 2065 Comm: repro Not tainted 6.13.0-rc2+ #11\nHardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS rel-1.16.3-0-ga6ed6b701f0a-prebuilt.qemu.org 04/01/2014\nCall Trace:\n <TASK>\n dump_stack_lvl+0x1fd/0x300\n ? tcp_gro_dev_warn+0x260/0x260\n ? _printk+0xc0/0x100\n ? read_lock_is_recursive+0x10/0x10\n ? irq_work_queue+0x72/0xf0\n ? __virt_addr_valid+0x17b/0x4b0\n print_address_description+0x78/0x390\n print_report+0x107/0x1f0\n ? __virt_addr_valid+0x17b/0x4b0\n ? __virt_addr_valid+0x3ff/0x4b0\n ? __phys_addr+0xb5/0x160\n ? ext4_xattr_inode_dec_ref_all+0xb8c/0xe90\n kasan_report+0xcc/0x100\n ? ext4_xattr_inode_dec_ref_all+0xb8c/0xe90\n ext4_xattr_inode_dec_ref_all+0xb8c/0xe90\n ? ext4_xattr_delete_inode+0xd30/0xd30\n ? __ext4_journal_ensure_credits+0x5f0/0x5f0\n ? __ext4_journal_ensure_credits+0x2b/0x5f0\n ? inode_update_timestamps+0x410/0x410\n ext4_xattr_delete_inode+0xb64/0xd30\n ? ext4_truncate+0xb70/0xdc0\n ? ext4_expand_extra_isize_ea+0x1d20/0x1d20\n ? __ext4_mark_inode_dirty+0x670/0x670\n ? ext4_journal_check_start+0x16f/0x240\n ? ext4_inode_is_fast_symlink+0x2f2/0x3a0\n ext4_evict_inode+0xc8c/0xff0\n ? ext4_inode_is_fast_symlink+0x3a0/0x3a0\n ? do_raw_spin_unlock+0x53/0x8a0\n ? ext4_inode_is_fast_symlink+0x3a0/0x3a0\n evict+0x4ac/0x950\n ? proc_nr_inodes+0x310/0x310\n ? trace_ext4_drop_inode+0xa2/0x220\n ? _raw_spin_unlock+0x1a/0x30\n ? iput+0x4cb/0x7e0\n do_unlinkat+0x495/0x7c0\n ? try_break_deleg+0x120/0x120\n ? 0xffffffff81000000\n ? __check_object_size+0x15a/0x210\n ? strncpy_from_user+0x13e/0x250\n ? getname_flags+0x1dc/0x530\n __x64_sys_unlinkat+0xc8/0xf0\n do_syscall_64+0x65/0x110\n entry_SYSCALL_64_after_hwframe+0x67/0x6f\nRIP: 0033:0x434ffd\nCode: 66 2e 0f 1f 84 00 00 00 00 00 0f 1f 00 f3 0f 1e fa 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 8\nRSP: 002b:00007ffc50fa7b28 EFLAGS: 00000246 ORIG_RAX: 0000000000000107\nRAX: ffffffffffffffda RBX: 00007ffc50fa7e18 RCX: 0000000000434ffd\nRDX: 0000000000000000 RSI: 0000000020000240 RDI: 0000000000000005\nRBP: 00007ffc50fa7be0 R08: 0000000000000000 R09: 0000000000000000\nR10: 0000000000000000 R11: 0000000000000246 R12: 0000000000000001\nR13: 00007ffc50fa7e08 R14: 00000000004bbf30 R15: 0000000000000001\n </TASK>\n\nThe buggy address belongs to the object at ffff888012c12000\n which belongs to the cache filp of size 360\nThe buggy address is located 196 bytes inside of\n freed 360-byte region [ffff888012c12000, ffff888012c12168)\n\nThe buggy address belongs to the physical page:\npage: refcount:1 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x12c12\nhead: order:1 mapcount:0 entire_mapcount:0 nr_pages_mapped:0 pincount:0\nflags: 0x40(head|node=0|zone=0)\npage_type: f5(slab)\nraw: 0000000000000040 ffff888000ad7640 ffffea0000497a00 dead000000000004\nraw: 0000000000000000 0000000000100010 00000001f5000000 0000000000000000\nhead: 0000000000000040 ffff888000ad7640 ffffea0000497a00 dead000000000004\nhead: 0000000000000000 0000000000100010 00000001f5000000 0000000000000000\nhead: 0000000000000001 ffffea00004b0481 ffffffffffffffff 0000000000000000\nhead: 0000000000000002 0000000000000000 00000000ffffffff 0000000000000000\npage dumped because: kasan: bad access detected\n\nMemory state around the buggy address:\n ffff888012c11f80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n ffff888012c12000: fa fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n> ffff888012c12080: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n                                           ^\n ffff888012c12100: fb fb fb fb fb fb fb fb fb fb fb fb fb fc fc fc\n ffff888012c12180: fc fc fc fc fc fc fc fc fc\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: ignorar xattrs después del final Una vez dentro de 'ext4_xattr_inode_dec_ref_all' debemos ignorar las entradas xattrs más allá de la entrada 'end'. Esto corrige el siguiente problema informado por KASAN: ====================================================================== ERROR: KASAN: slab-use-after-free en ext4_xattr_inode_dec_ref_all+0xb8c/0xe90 Lectura de tamaño 4 en la dirección ffff888012c120c4 por la tarea repro/2065 CPU: 1 UID: 0 PID: 2065 Comm: repro No contaminado 6.13.0-rc2+ #11 Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS rel-1.16.3-0-ga6ed6b701f0a-prebuilt.qemu.org 01/04/2014 Rastreo de llamadas:   dump_stack_lvl+0x1fd/0x300 ? tcp_gro_dev_warn+0x260/0x260 ? _printk+0xc0/0x100 ? read_lock_is_recursive+0x10/0x10 ? irq_work_queue+0x72/0xf0 ? __virt_addr_valid+0x17b/0x4b0 print_address_description+0x78/0x390 print_report+0x107/0x1f0 ? __virt_addr_valid+0x17b/0x4b0 ? __virt_addr_valid+0x3ff/0x4b0 ? __phys_addr+0xb5/0x160 ? ext4_xattr_inode_dec_ref_all+0xb8c/0xe90 kasan_report+0xcc/0x100 ? ext4_xattr_inode_dec_ref_all+0xb8c/0xe90 ext4_xattr_inode_dec_ref_all+0xb8c/0xe90 ? ext4_xattr_delete_inode+0xd30/0xd30 ? __ext4_journal_ensure_credits+0x5f0/0x5f0 ? __ext4_journal_ensure_credits+0x2b/0x5f0 ? inode_update_timestamps+0x410/0x410 ext4_xattr_delete_inode+0xb64/0xd30 ? ext4_truncate+0xb70/0xdc0 ? ext4_expand_extra_isize_ea+0x1d20/0x1d20 ? __ext4_mark_inode_dirty+0x670/0x670 ? ext4_journal_check_start+0x16f/0x240 ? ext4_inode_is_fast_symlink+0x2f2/0x3a0 ext4_evict_inode+0xc8c/0xff0 ? ext4_inode_is_fast_symlink+0x3a0/0x3a0 ? do_raw_spin_unlock+0x53/0x8a0 ? ext4_inode_is_fast_symlink+0x3a0/0x3a0 evict+0x4ac/0x950 ? proc_nr_inodes+0x310/0x310 ? trace_ext4_drop_inode+0xa2/0x220 ? _raw_spin_unlock+0x1a/0x30 ? iput+0x4cb/0x7e0 do_unlinkat+0x495/0x7c0 ? try_break_deleg+0x120/0x120 ? 0xffffffff81000000 ? __check_object_size+0x15a/0x210 ? strncpy_from_user+0x13e/0x250 ? getname_flags+0x1dc/0x530 __x64_sys_unlinkat+0xc8/0xf0 do_syscall_64+0x65/0x110 entry_SYSCALL_64_after_hwframe+0x67/0x6f RIP: 0033:0x434ffd Code: 66 2e 0f 1f 84 00 00 00 00 00 0f 1f 00 f3 0f 1e fa 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 8 RSP: 002b:00007ffc50fa7b28 EFLAGS: 00000246 ORIG_RAX: 0000000000000107 RAX: ffffffffffffffda RBX: 00007ffc50fa7e18 RCX: 0000000000434ffd RDX: 0000000000000000 RSI: 0000000020000240 RDI: 0000000000000005 RBP: 00007ffc50fa7be0 R08: 0000000000000000 R09: 0000000000000000 R10: 0000000000000000 R11: 0000000000000246 R12: 0000000000000001 R13: 00007ffc50fa7e08 R14: 00000000004bbf30 R15: 0000000000000001  La dirección con errores pertenece al objeto en ffff888012c12000 que pertenece al filp de caché de tamaño 360 La dirección con errores se encuentra 196 bytes dentro de la región liberada de 360 bytes [ffff888012c12000, ffff888012c12168) La dirección con errores pertenece a la página física: page: refcount:1 mapcount:0 mapping:000000000000000 index:0x0 pfn:0x12c12 head: order:1 mapcount:0 entire_mapcount:0 nr_pages_mapped:0 conteo de pines:0 indicadores: 0x40(cabeza|nodo=0|zona=0) tipo_de_página: f5(losa) sin procesar: 0000000000000040 ffff888000ad7640 ffffea0000497a00 muerto000000000004 sin procesar: 0000000000000000 0000000000100010 00000001f5000000 0000000000000000 cabeza: 0000000000000040 ffff888000ad7640 ffffea0000497a00 muerto000000000004 cabeza: 0000000000000000 0000000000100010 00000001f5000000 00000000000000000 cabeza: 0000000000000001 ffffea00004b0481 ffffffffffffffff 000000000000000 cabeza: 0000000000000002 000000000000000 00000000ffffffff 000000000000000 página volcada porque: kasan: se detectó un acceso incorrecto Estado de la memoria alrededor de la dirección con errores: ffff888012c11f80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ffff888012c12000: fa fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb &gt; ffff888012c12080: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ^ ffff888012c12100: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb  ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/362a90cecd36e8a5c415966d0b75b04a0270e4dd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3bc6317033f365ce578eb6039445fb66162722fd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6aff941cb0f7d0c897c3698ad2e30672709135e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76c365fa7e2a8bb85f0190cdb4b8cdc99b2fdce3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/836e625b03a666cf93ff5be328c8cb30336db872", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8e008b60492cf6fd31ef127aea6d02fd3d314cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cf9291a3449b04688b81e32621e88de8f4314b54", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb59cc31b6ea076021d14b04e7faab1636b87d0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f737418b6de31c962c7192777ee4018906975383", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37743", "published_date": "2025-05-01T13:15:53.000", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: ath12k: Avoid memory leak while enabling statistics\n\nDriver uses monitor destination rings for extended statistics mode and\nstandalone monitor mode. In extended statistics mode, TLVs are parsed from\nthe buffer received from the monitor destination ring and assigned to the\nppdu_info structure to update per-packet statistics. In standalone monitor\nmode, along with per-packet statistics, the packet data (payload) is\ncaptured, and the driver updates per MSDU to mac80211.\n\nWhen the AP interface is enabled, only extended statistics mode is\nactivated. As part of enabling monitor rings for collecting statistics,\nthe driver subscribes to HAL_RX_MPDU_START TLV in the filter\nconfiguration. This TLV is received from the monitor destination ring, and\nkzalloc for the mon_mpdu object occurs, which is not freed, leading to a\nmemory leak. The kzalloc for the mon_mpdu object is only required while\nenabling the standalone monitor interface. This causes a memory leak while\nenabling extended statistics mode in the driver.\n\nFix this memory leak by removing the kzalloc for the mon_mpdu object in\nthe HAL_RX_MPDU_START TLV handling. Additionally, remove the standalone\nmonitor mode handlings in the HAL_MON_BUF_ADDR and HAL_RX_MSDU_END TLVs.\nThese TLV tags will be handled properly when enabling standalone monitor\nmode in the future.\n\nTested-on: QCN9274 hw2.0 PCI WLAN.WBE.1.3.1-00173-QCAHKSWPL_SILICONZ-1\nTested-on: WCN7850 hw2.0 PCI WLAN.HMT.1.0.c5-00481-QCAHMTSWPL_V1.0_V2.0_SILICONZ-3"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: ath12k: Evitar fugas de memoria al habilitar las estadísticas. El controlador utiliza anillos de destino del monitor para el modo de estadísticas extendidas y el modo de monitor independiente. En el modo de estadísticas extendidas, los TLV se analizan desde el búfer recibido del anillo de destino del monitor y se asignan a la estructura ppdu_info para actualizar las estadísticas por paquete. En el modo de monitor independiente, junto con las estadísticas por paquete, se capturan los datos del paquete (carga útil) y el controlador actualiza por MSDU a mac80211. Cuando la interfaz AP está habilitada, solo se activa el modo de estadísticas extendidas. Como parte de la habilitación de los anillos de monitor para recopilar estadísticas, el controlador se suscribe al TLV HAL_RX_MPDU_START en la configuración del filtro. Este TLV se recibe del anillo de destino del monitor y se produce kzalloc para el objeto mon_mpdu, que no se libera, lo que provoca una fuga de memoria. El kzalloc para el objeto mon_mpdu solo es necesario mientras se habilita la interfaz de monitor independiente. Esto causa una fuga de memoria al habilitar el modo de estadísticas extendidas en el controlador. Solucione esta fuga de memoria eliminando el kzalloc del objeto mon_mpdu en la gestión de la TLV HAL_RX_MPDU_START. Además, elimine la gestión del modo de monitor independiente en las TLV HAL_MON_BUF_ADDR y HAL_RX_MSDU_END. Estas etiquetas TLV se gestionarán correctamente al habilitar el modo de monitor independiente en el futuro. Probado en: QCN9274 hw2.0 PCI WLAN.WBE.1.3.1-00173-QCAHKSWPL_SILICONZ-1. Probado en: WCN7850 hw2.0 PCI WLAN.HMT.1.0.c5-00481-QCAHMTSWPL_V1.0_V2.0_SILICONZ-3."}], "references": [{"url": "https://git.kernel.org/stable/c/286bab0fc7b9db728dab8c63cadf6be9b3facf8c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ecfc131389923405be8e7a6f4408fd9321e4d19b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53067", "published_date": "2025-05-02T16:15:25.777", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nLoongArch: Only call get_timer_irq() once in constant_clockevent_init()\n\nUnder CONFIG_DEBUG_ATOMIC_SLEEP=y and CONFIG_DEBUG_PREEMPT=y, we can see\nthe following messages on LoongArch, this is because using might_sleep()\nin preemption disable context.\n\n[    0.001127] smp: Bringing up secondary CPUs ...\n[    0.001222] Booting CPU#1...\n[    0.001244] 64-bit Loongson Processor probed (LA464 Core)\n[    0.001247] CPU1 revision is: 0014c012 (Loongson-64bit)\n[    0.001250] FPU1 revision is: 00000000\n[    0.001252] BUG: sleeping function called from invalid context at kernel/locking/mutex.c:283\n[    0.001255] in_atomic(): 1, irqs_disabled(): 1, non_block: 0, pid: 0, name: swapper/1\n[    0.001257] preempt_count: 1, expected: 0\n[    0.001258] RCU nest depth: 0, expected: 0\n[    0.001259] Preemption disabled at:\n[    0.001261] [<9000000000223800>] arch_dup_task_struct+0x20/0x110\n[    0.001272] CPU: 1 PID: 0 Comm: swapper/1 Not tainted 6.2.0-rc7+ #43\n[    0.001275] Hardware name: Loongson Loongson-3A5000-7A1000-1w-A2101/Loongson-LS3A5000-7A1000-1w-A2101, BIOS vUDK2018-LoongArch-V4.0.05132-beta10 12/13/202\n[    0.001277] Stack : 0072617764726148 0000000000000000 9000000000222f1c 90000001001e0000\n[    0.001286]         90000001001e3be0 90000001001e3be8 0000000000000000 0000000000000000\n[    0.001292]         90000001001e3be8 0000000000000040 90000001001e3cb8 90000001001e3a50\n[    0.001297]         9000000001642000 90000001001e3be8 be694d10ce4139dd 9000000100174500\n[    0.001303]         0000000000000001 0000000000000001 00000000ffffe0a2 0000000000000020\n[    0.001309]         000000000000002f 9000000001354116 00000000056b0000 ffffffffffffffff\n[    0.001314]         0000000000000000 0000000000000000 90000000014f6e90 9000000001642000\n[    0.001320]         900000000022b69c 0000000000000001 0000000000000000 9000000001736a90\n[    0.001325]         9000000100038000 0000000000000000 9000000000222f34 0000000000000000\n[    0.001331]         00000000000000b0 0000000000000004 0000000000000000 0000000000070000\n[    0.001337]         ...\n[    0.001339] Call Trace:\n[    0.001342] [<9000000000222f34>] show_stack+0x5c/0x180\n[    0.001346] [<90000000010bdd80>] dump_stack_lvl+0x60/0x88\n[    0.001352] [<9000000000266418>] __might_resched+0x180/0x1cc\n[    0.001356] [<90000000010c742c>] mutex_lock+0x20/0x64\n[    0.001359] [<90000000002a8ccc>] irq_find_matching_fwspec+0x48/0x124\n[    0.001364] [<90000000002259c4>] constant_clockevent_init+0x68/0x204\n[    0.001368] [<900000000022acf4>] start_secondary+0x40/0xa8\n[    0.001371] [<90000000010c0124>] smpboot_entry+0x60/0x64\n\nHere are the complete call chains:\n\nsmpboot_entry()\n  start_secondary()\n    constant_clockevent_init()\n      get_timer_irq()\n        irq_find_matching_fwnode()\n          irq_find_matching_fwspec()\n            mutex_lock()\n              might_sleep()\n                __might_sleep()\n                  __might_resched()\n\nIn order to avoid the above issue, we should break the call chains,\nusing timer_irq_installed variable as check condition to only call\nget_timer_irq() once in constant_clockevent_init() is a simple and\nproper way."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: LoongArch: solo llamar a get_timer_irq() una vez en constant_clockevent_init() Bajo CONFIG_DEBUG_ATOMIC_SLEEP=y y CONFIG_DEBUG_PREEMPT=y, podemos ver los siguientes mensajes en LoongArch, esto se debe a que se usa might_sleep() en el contexto de deshabilitación de preempción. [ 0.001127] smp: Activando CPU secundarias... [ 0.001222] Arrancando CPU#1... [ 0.001244] Procesador <PERSON>son de 64 bits probado (núcleo LA464) [ 0.001247] La revisión de CPU1 es: 0014c012 (Loongson-64bit) [ 0.001250] La revisión de FPU1 es: 00000000 [ 0.001252] ERROR: función de suspensión llamada desde un contexto no válido en kernel/locking/mutex.c:283 [ 0.001255] in_atomic(): 1, irqs_disabled(): 1, non_block: 0, pid: 0, name: swapper/1 [ 0.001257] preempt_count: 1, expected: 0 [ 0.001258] Profundidad de anidamiento de RCU: 0, esperado: 0 [ 0.001259] Preempción deshabilitada en: [ 0.001261] [&lt;9000000000223800&gt;] arch_dup_task_struct+0x20/0x110 [ 0.001272] CPU: 1 PID: 0 Comm: swapper/1 No contaminado 6.2.0-rc7+ #43 [ 0.001275] Nombre del hardware: Loongson Loongson-3A5000-7A1000-1w-A2101/Loongson-LS3A5000-7A1000-1w-A2101, BIOS vUDK2018-LoongArch-V4.0.05132-beta10 12/13/202 [ 0.001277] Pila: 0072617764726148 0000000000000000 9000000000222f1c 90000001001e0000 [ 0.001286] 90000001001e3be0 90000001001e3be8 0000000000000000 000000000000000 [ 0.001292] 90000001001e3be8 0000000000000040 90000001001e3cb8 90000001001e3a50 [ 0.001297] 9000000001642000 90000001001e3be8 be694d10ce4139dd 9000000100174500 [ 0.001303] 0000000000000001 000000000000001 000000000ffffe0a2 0000000000000020 [ 0.001309] 00000000000002f 9000000001354116 00000000056b0000 ffffffffffffffffff [ 0.001314] 0000000000000000 0000000000000000 90000000014f6e90 9000000001642000 [ 0.001320] 900000000022b69c 0000000000000001 000000000000000 9000000001736a90 [ 0.001325] 9000000100038000 000000000000000 9000000000222f34 000000000000000 [ 0.001331] 00000000000000b0 0000000000000004 0000000000000000 0000000000070000 [ 0.001337] ... [ 0.001339] Rastreo de llamadas: [ 0.001342] [&lt;9000000000222f34&gt;] show_stack+0x5c/0x180 [ 0.001346] [&lt;90000000010bdd80&gt;] dump_stack_lvl+0x60/0x88 [ 0.001352] [&lt;9000000000266418&gt;] __might_resched+0x180/0x1cc [ 0.001356] [&lt;90000000010c742c&gt;] mutex_lock+0x20/0x64 [ 0.001359] [&lt;90000000002a8ccc&gt;] irq_find_matching_fwspec+0x48/0x124 [ 0.001364] [&lt;90000000002259c4&gt;] constant_clockevent_init+0x68/0x204 [ 0.001368] [&lt;900000000022acf4&gt;] start_secondary+0x40/0xa8 [ 0.001371] [&lt;90000000010c0124&gt;] smpboot_entry+0x60/0x64 Estas son las cadenas de llamadas completas: Para evitar el problema anterior, debemos romper las cadenas de llamadas, utilizando la variable timer_irq_installed como condición de verificación para llamar a get_timer_irq() solo una vez en constant_clockevent_init() es una forma simple y adecuada."}], "references": [{"url": "https://git.kernel.org/stable/c/acadbd058fa12b510fbecca11eae22bd6f654250", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b9c379e1d7e141b102f41858c9b8f6f36e7c89a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bb7a78e343468873bf00b2b181fcfd3c02d8cb56", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
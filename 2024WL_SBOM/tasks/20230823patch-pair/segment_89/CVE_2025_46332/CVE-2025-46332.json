{"cve_id": "CVE-2025-46332", "published_date": "2025-05-02T17:15:52.947", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Flags SDK is an open-source feature flags toolkit for Next.js and SvelteKit. Impacted versions include flags from 3.2.0 and prior and @vercel/flags from 3.1.1 and prior as certain circumstances allows a bad actor with detailed knowledge of the vulnerability to list all flags returned by the flags discovery endpoint (.well-known/vercel/flags). This vulnerability allows for information disclosure, where a bad actor could gain access to a list of all feature flags exposed through the flags discovery endpoint, including the flag names, flag descriptions, available options and their labels (e.g. true, false), and default flag values. This issue has been patched in flags@4.0.0, users of flags and @vercel/flags should also migrate to flags@4.0.0."}, {"lang": "es", "value": "Flags SDK es un kit de herramientas de código abierto para Next.js y SvelteKit. Las versiones afectadas incluyen flags de la versión 3.2.0 y anteriores, y @vercel/flags de la versión 3.1.1 y anteriores, ya que, en determinadas circunstancias, un atacante con conocimiento detallado de la vulnerabilidad puede listar todos los flags devueltos por el endpoint de descubrimiento de flags (.well-known/vercel/flags). Esta vulnerabilidad permite la divulgación de información, lo que permite a un atacante acceder a una lista de todos los flags de características expuestos a través del endpoint de descubrimiento de flags, incluyendo los nombres y descripciones de los flags, las opciones disponibles y sus etiquetas (p. ej., verdadero, falso) y los valores predeterminados de los flags. Este problema se ha corregido en flags@4.0.0; los usuarios de flags y @vercel/flags también deben migrar a flags@4.0.0."}], "references": [{"url": "https://github.com/vercel/flags/blob/main/packages/flags/guides/upgrade-to-v4.md", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vercel/flags/security/advisories/GHSA-892p-pqrr-hxqr", "source": "<EMAIL>", "tags": []}, {"url": "https://vercel.com/changelog/information-disclosure-in-flags-sdk-cve-2025-46332", "source": "<EMAIL>", "tags": []}]}
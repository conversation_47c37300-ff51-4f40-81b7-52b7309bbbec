{"cve_id": "CVE-2025-4100", "published_date": "2025-05-01T07:15:58.693", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "The Nautic Pages plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'np_marinetraffic_map' shortcode in all versions up to, and including, 2.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Nautic Pages para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del shortcode 'np_marinetraffic_map' en todas las versiones hasta la 2.0 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en las páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/nautic-pages/trunk/nautic_pages.php#L22", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2f6bfe18-bb9b-4cc2-bdb7-fd9163b61323?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2022-49871", "published_date": "2025-05-01T15:16:12.030", "last_modified_date": "2025-05-07T13:22:01.837", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: tun: Fix memory leaks of napi_get_frags\n\nkmemleak reports after running test_progs:\n\nunreferenced object 0xffff8881b1672dc0 (size 232):\n  comm \"test_progs\", pid 394388, jiffies 4354712116 (age 841.975s)\n  hex dump (first 32 bytes):\n    e0 84 d7 a8 81 88 ff ff 80 2c 67 b1 81 88 ff ff  .........,g.....\n    00 40 c5 9b 81 88 ff ff 00 00 00 00 00 00 00 00  .@..............\n  backtrace:\n    [<00000000c8f01748>] napi_skb_cache_get+0xd4/0x150\n    [<0000000041c7fc09>] __napi_build_skb+0x15/0x50\n    [<00000000431c7079>] __napi_alloc_skb+0x26e/0x540\n    [<000000003ecfa30e>] napi_get_frags+0x59/0x140\n    [<0000000099b2199e>] tun_get_user+0x183d/0x3bb0 [tun]\n    [<000000008a5adef0>] tun_chr_write_iter+0xc0/0x1b1 [tun]\n    [<0000000049993ff4>] do_iter_readv_writev+0x19f/0x320\n    [<000000008f338ea2>] do_iter_write+0x135/0x630\n    [<000000008a3377a4>] vfs_writev+0x12e/0x440\n    [<00000000a6b5639a>] do_writev+0x104/0x280\n    [<00000000ccf065d8>] do_syscall_64+0x3b/0x90\n    [<00000000d776e329>] entry_SYSCALL_64_after_hwframe+0x63/0xcd\n\nThe issue occurs in the following scenarios:\ntun_get_user()\n  napi_gro_frags()\n    napi_frags_finish()\n      case GRO_NORMAL:\n        gro_normal_one()\n          list_add_tail(&skb->list, &napi->rx_list);\n          <-- While napi->rx_count < READ_ONCE(gro_normal_batch),\n          <-- gro_normal_list() is not called, napi->rx_list is not empty\n  <-- not ask to complete the gro work, will cause memory leaks in\n  <-- following tun_napi_del()\n...\ntun_napi_del()\n  netif_napi_del()\n    __netif_napi_del()\n    <-- &napi->rx_list is not empty, which caused memory leaks\n\nTo fix, add napi_complete() after napi_gro_frags()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: tun: Se corrigen las fugas de memoria de napi_get_frags que informa kmemleak tras ejecutar test_progs: objeto sin referencia 0xffff8881b1672dc0 (tamaño 232): comm \"test_progs\", pid 394388, jiffies 4354712116 (edad 841,975 s) volcado hexadecimal (primeros 32 bytes): e0 84 d7 a8 81 88 ff ff 80 2c 67 b1 81 88 ff ff .........,g..... 00 40 c5 9b 81 88 ff ff 00 00 00 00 00 00 00 00 .@.............. backtrace: [&lt;00000000c8f01748&gt;] napi_skb_cache_get+0xd4/0x150 [&lt;0000000041c7fc09&gt;] __napi_build_skb+0x15/0x50 [&lt;00000000431c7079&gt;] __napi_alloc_skb+0x26e/0x540 [&lt;000000003ecfa30e&gt;] napi_get_frags+0x59/0x140 [&lt;0000000099b2199e&gt;] tun_get_user+0x183d/0x3bb0 [tun] [&lt;000000008a5adef0&gt;] tun_chr_write_iter+0xc0/0x1b1 [tun] El problema ocurre en los siguientes escenarios: tun_get_user() napi_gro_frags() napi_frags_finish() caso GRO_NORMAL: gro_normal_one() list_add_tail(&amp;skb-&gt;list, &amp;napi-&gt;rx_list); &lt;-- Mientras napi-&gt;rx_count &lt; READ_ONCE(gro_normal_batch), &lt;-- gro_normal_list() no se llama, napi-&gt;rx_list no está vacío &lt;-- no solicita completar el trabajo de gro, causará pérdidas de memoria en &lt;-- siguientes tun_napi_del() ... tun_napi_del() netif_napi_del() __netif_napi_del() &lt;-- &amp;napi-&gt;rx_list no está vacío, lo que causó pérdidas de memoria Para corregirlo, agregue napi_complete() después de napi_gro_frags()."}], "references": [{"url": "https://git.kernel.org/stable/c/1118b2049d77ca0b505775fc1a8d1909cf19a7ec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/223ef6a94e52331a6a7ef31e59921e0e82d2d40a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3401f964028ac941425b9b2c8ff8a022539ef44a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8b12a020b20a78f62bedc50f26db3bf4fadf8cb9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a4f73f6adc53fd7a3f9771cbc89a03ef39b0b755", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d7569302a7a52a9305d2fb054df908ff985553bb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
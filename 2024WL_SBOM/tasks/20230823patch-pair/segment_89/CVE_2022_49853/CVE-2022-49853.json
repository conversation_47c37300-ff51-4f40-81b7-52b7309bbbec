{"cve_id": "CVE-2022-49853", "published_date": "2025-05-01T15:16:08.890", "last_modified_date": "2025-05-07T13:32:15.960", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: macvlan: fix memory leaks of macvlan_common_newlink\n\nkmemleak reports memory leaks in macvlan_common_newlink, as follows:\n\n ip link add link eth0 name .. type macvlan mode source macaddr add\n <MAC-ADDR>\n\nkmemleak reports:\n\nunreferenced object 0xffff8880109bb140 (size 64):\n  comm \"ip\", pid 284, jiffies 4294986150 (age 430.108s)\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 b8 aa 5a 12 80 88 ff ff  ..........Z.....\n    80 1b fa 0d 80 88 ff ff 1e ff ac af c7 c1 6b 6b  ..............kk\n  backtrace:\n    [<ffffffff813e06a7>] kmem_cache_alloc_trace+0x1c7/0x300\n    [<ffffffff81b66025>] macvlan_hash_add_source+0x45/0xc0\n    [<ffffffff81b66a67>] macvlan_changelink_sources+0xd7/0x170\n    [<ffffffff81b6775c>] macvlan_common_newlink+0x38c/0x5a0\n    [<ffffffff81b6797e>] macvlan_newlink+0xe/0x20\n    [<ffffffff81d97f8f>] __rtnl_newlink+0x7af/0xa50\n    [<ffffffff81d98278>] rtnl_newlink+0x48/0x70\n    ...\n\nIn the scenario where the macvlan mode is configured as 'source',\nmacvlan_changelink_sources() will be execured to reconfigure list of\nremote source mac addresses, at the same time, if register_netdevice()\nreturn an error, the resource generated by macvlan_changelink_sources()\nis not cleaned up.\n\nUsing this patch, in the case of an error, it will execute\nmacvlan_flush_sources() to ensure that the resource is cleaned up."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: macvlan: corregir fugas de memoria de macvlan_common_newlink kmemleak informa de fugas de memoria en macvlan_common_newlink, como se indica a continuación: ip link add link eth0 name .. type macvlan mode source macaddr add  kmemleak informa: objeto no referenciado 0xffff8880109bb140 (tamaño 64): comm \"ip\", pid 284, jiffies 4294986150 (edad 430.108s) volcado hexadecimal (primeros 32 bytes): 00 00 00 00 00 00 00 00 b8 aa 5a 12 80 88 ff ff ..........Z..... 80 1b fa 0d 80 88 ff ff 1e ff ac af c7 c1 6b 6b ..............kk seguimiento inverso: [] kmem_cache_alloc_trace+0x1c7/0x300 [] macvlan_hash_add_source+0x45/0xc0 [] macvlan_changelink_sources+0xd7/0x170 [] macvlan_common_newlink+0x38c/0x5a0 [] macvlan_newlink+0xe/0x20 [] __rtnl_newlink+0x7af/0xa50 [] rtnl_newlink+0x48/0x70 ... En el escenario donde el modo macvlan está configurado como 'origen', se ejecutará macvlan_changelink_sources() para reconfigurar la lista de direcciones MAC de origen remoto. Al mismo tiempo, si register_netdevice() devuelve un error, el recurso generado por macvlan_changelink_sources() no se limpia. Con este parche, en caso de error, se ejecutará macvlan_flush_sources() para garantizar que el recurso se limpie."}], "references": [{"url": "https://git.kernel.org/stable/c/21d3a8b6a1e39e7529ce9de07316ee13a63f305b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/23569b5652ee8e8e55a12f7835f59af6f3cefc30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/685e73e3f7a9fb75cbf049a9d0b7c45cc6b57b2e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/956e0216a19994443c90ba2ea6b0b284c9c4f9cb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9ea003c4671b2fc455320ecf6d4a43b0a3c1878a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9f288e338be206713d79b29144c27fca4503c39b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a81b44d1df1f07f00c0dcc0a0b3d2fa24a46289e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a8d67367ab33604326cc37ab44fd1801bf5691ba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-45020", "published_date": "2025-04-30T13:15:49.480", "last_modified_date": "2025-05-09T13:45:23.603", "descriptions": [{"lang": "en", "value": "A SQL Injection vulnerability was discovered in the normal-bwdates-reports-details.php file of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary SQL code via the todate parameter in a POST request."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección SQL en el archivo normal-bwdates-reports-details.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código SQL arbitrario mediante el parámetro \"todate\" en una solicitud POST."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/SQL/SQL_Injection_normal-bwdates-reports-details.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
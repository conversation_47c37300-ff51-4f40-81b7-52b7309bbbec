{"cve_id": "CVE-2025-46567", "published_date": "2025-05-01T18:15:58.117", "last_modified_date": "2025-06-17T14:19:39.290", "descriptions": [{"lang": "en", "value": "LLama Factory enables fine-tuning of large language models. Prior to version 1.0.0, a critical vulnerability exists in the `llamafy_baichuan2.py` script of the LLaMA-Factory project. The script performs insecure deserialization using `torch.load()` on user-supplied `.bin` files from an input directory. An attacker can exploit this behavior by crafting a malicious `.bin` file that executes arbitrary commands during deserialization. This issue has been patched in version 1.0.0."}, {"lang": "es", "value": "LLama Factory permite el ajuste fino de modelos de lenguaje grandes. Antes de la versión 1.0.0, existía una vulnerabilidad crítica en el script `llamafy_baichuan2.py` del proyecto LLaMA-Factory. Este script realiza una deserialización insegura mediante `torch.load()` en archivos `.bin` proporcionados por el usuario desde un directorio de entrada. Un atacante puede explotar este comportamiento manipulando un archivo `.bin` malicioso que ejecute comandos arbitrarios durante la deserialización. Este problema se ha corregido en la versión 1.0.0."}], "references": [{"url": "https://github.com/hiyouga/LLaMA-Factory/commit/2989d39239d2f46e584c1e1180ba46b9768afb2a", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/hiyouga/LLaMA-Factory/security/advisories/GHSA-f2f7-gj54-6vpv", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
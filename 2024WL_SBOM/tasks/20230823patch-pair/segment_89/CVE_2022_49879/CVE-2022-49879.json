{"cve_id": "CVE-2022-49879", "published_date": "2025-05-01T15:16:12.857", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: fix BUG_ON() when directory entry has invalid rec_len\n\nThe rec_len field in the directory entry has to be a multiple of 4.  A\ncorrupted filesystem image can be used to hit a BUG() in\next4_rec_len_to_disk(), called from make_indexed_dir().\n\n ------------[ cut here ]------------\n kernel BUG at fs/ext4/ext4.h:2413!\n ...\n RIP: 0010:make_indexed_dir+0x53f/0x5f0\n ...\n Call Trace:\n  <TASK>\n  ? add_dirent_to_buf+0x1b2/0x200\n  ext4_add_entry+0x36e/0x480\n  ext4_add_nondir+0x2b/0xc0\n  ext4_create+0x163/0x200\n  path_openat+0x635/0xe90\n  do_filp_open+0xb4/0x160\n  ? __create_object.isra.0+0x1de/0x3b0\n  ? _raw_spin_unlock+0x12/0x30\n  do_sys_openat2+0x91/0x150\n  __x64_sys_open+0x6c/0xa0\n  do_syscall_64+0x3c/0x80\n  entry_SYSCALL_64_after_hwframe+0x46/0xb0\n\nThe fix simply adds a call to ext4_check_dir_entry() to validate the\ndirectory entry, returning -EFSCORRUPTED if the entry is invalid."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: corrección de BUG_ON() cuando la entrada de directorio tiene un rec_len no válido El campo rec_len en la entrada de directorio debe ser un múltiplo de 4. Una imagen de sistema de archivos dañada se puede usar para generar un BUG() en ext4_rec_len_to_disk(), llamado desde make_indexed_dir(). ------------[ cortar aquí ]------------ ¡ERROR del kernel en fs/ext4/ext4.h:2413! ... RIP: 0010:make_indexed_dir+0x53f/0x5f0 ... Rastreo de llamadas:   ? add_dirent_to_buf+0x1b2/0x200 ext4_add_entry+0x36e/0x480 ext4_add_nondir+0x2b/0xc0 ext4_create+0x163/0x200 path_openat+0x635/0xe90 do_filp_open+0xb4/0x160 ? __create_object.isra.0+0x1de/0x3b0 ? _raw_spin_unlock+0x12/0x30 do_sys_openat2+0x91/0x150 __x64_sys_open+0x6c/0xa0 do_syscall_64+0x3c/0x80 entry_SYSCALL_64_after_hwframe+0x46/0xb0 La solución simplemente agrega una llamada a ext4_check_dir_entry() para validar la entrada del directorio, devolviendo -EFSCORRUPTED si la entrada no es válida."}], "references": [{"url": "https://git.kernel.org/stable/c/156451a67b93986fb07c274ef6995ff40766c5ad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/17a0bc9bd697f75cfdf9b378d5eb2d7409c91340", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2fa24d0274fbf913b56ee31f15bc01168669d909", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/999cff2b6ce3b45c08abf793bf55534777421327", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ce1ee2c8827fb6493e91acbd50f664cf2a972c3d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
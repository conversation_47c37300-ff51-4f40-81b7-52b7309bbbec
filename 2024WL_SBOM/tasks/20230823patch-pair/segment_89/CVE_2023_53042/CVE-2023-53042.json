{"cve_id": "CVE-2023-53042", "published_date": "2025-05-02T16:15:23.320", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/display: Do not set DRR on pipe Commit\n\n[WHY]\nWriting to DRR registers such as OTG_V_TOTAL_MIN on the same frame as a\npipe commit can cause underflow."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/display: No configure DRR en el commit de tubería [POR QUÉ] Escribir en registros DRR como OTG_V_TOTAL_MIN en el mismo marco que una confirmación de tubería puede causar desbordamiento."}], "references": [{"url": "https://git.kernel.org/stable/c/3c20a098b507020936e02a98f4fbb924deeef44b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/56574f89dbd84004c3fd6485bcaafb5aa9b8be14", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f8080f1e300e7abcc03025ec8b5bab69ae98daaa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
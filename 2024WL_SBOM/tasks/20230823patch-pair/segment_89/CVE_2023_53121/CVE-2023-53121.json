{"cve_id": "CVE-2023-53121", "published_date": "2025-05-02T16:15:31.173", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntcp: tcp_make_synack() can be called from process context\n\ntcp_rtx_synack() now could be called in process context as explained in\n0a375c822497 (\"tcp: tcp_rtx_synack() can be called from process\ncontext\").\n\ntcp_rtx_synack() might call tcp_make_synack(), which will touch per-CPU\nvariables with preemption enabled. This causes the following BUG:\n\n    BUG: using __this_cpu_add() in preemptible [00000000] code: ThriftIO1/5464\n    caller is tcp_make_synack+0x841/0xac0\n    Call Trace:\n     <TASK>\n     dump_stack_lvl+0x10d/0x1a0\n     check_preemption_disabled+0x104/0x110\n     tcp_make_synack+0x841/0xac0\n     tcp_v6_send_synack+0x5c/0x450\n     tcp_rtx_synack+0xeb/0x1f0\n     inet_rtx_syn_ack+0x34/0x60\n     tcp_check_req+0x3af/0x9e0\n     tcp_rcv_state_process+0x59b/0x2030\n     tcp_v6_do_rcv+0x5f5/0x700\n     release_sock+0x3a/0xf0\n     tcp_sendmsg+0x33/0x40\n     ____sys_sendmsg+0x2f2/0x490\n     __sys_sendmsg+0x184/0x230\n     do_syscall_64+0x3d/0x90\n\nAvoid calling __TCP_INC_STATS() with will touch per-cpu variables. Use\nTCP_INC_STATS() which is safe to be called from context switch."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tcp: tcp_make_synack() se puede llamar desde el contexto del proceso. tcp_rtx_synack() ahora se puede llamar en el contexto del proceso como se explica en 0a375c822497 (\"tcp: tcp_rtx_synack() se puede llamar desde el contexto del proceso\"). tcp_rtx_synack() podría llamar a tcp_make_synack(), que tocará las variables por CPU con la preempción habilitada. Esto provoca el siguiente ERROR: ERROR: uso de __this_cpu_add() en código preemptible [00000000]: El llamador de ThriftIO1/5464 es tcp_make_synack+0x841/0xac0 Rastreo de llamadas:  dump_stack_lvl+0x10d/0x1a0 check_preemption_disabled+0x104/0x110 tcp_make_synack+0x841/0xac0 tcp_v6_send_synack+0x5c/0x450 tcp_rtx_synack+0xeb/0x1f0 inet_rtx_syn_ack+0x34/0x60 tcp_check_req+0x3af/0x9e0 tcp_rcv_state_process+0x59b/0x2030 tcp_v6_do_rcv+0x5f5/0x700 release_sock+0x3a/0xf0 tcp_sendmsg+0x33/0x40 ____sys_sendmsg+0x2f2/0x490 __sys_sendmsg+0x184/0x230 do_syscall_64+0x3d/0x90 Evite llamar a __TCP_INC_STATS(), ya que afectará las variables por CPU. Use TCP_INC_STATS(), que se puede llamar de forma segura desde un cambio de contexto."}], "references": [{"url": "https://git.kernel.org/stable/c/442aa78ed70188b21ccd8669738448702c0a3281", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7613cde8c0c1f02a7ec2e1d536c01b65b135fc1c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/77ad58bca0119e8cc3e0e9d91a3f22caa66e4dfa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9180aa4622a720b433e842b4d3aa34d73eec577a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad07290d63ff6689f50565b02f5b6f34ec15a5ca", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bced3f7db95ff2e6ca29dc4d1c9751ab5e736a09", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d493d4fe88195a144d6a277a90062a7534ed2192", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e23ca307745be3df7fe9762f3e2a7e311a57852e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37755", "published_date": "2025-05-01T13:15:54.267", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: libwx: handle page_pool_dev_alloc_pages error\n\npage_pool_dev_alloc_pages could return NULL. There was a WARN_ON(!page)\nbut it would still proceed to use the NULL pointer and then crash.\n\nThis is similar to commit 001ba0902046\n(\"net: fec: handle page_pool_dev_alloc_pages error\").\n\nThis is found by our static analysis tool KNighter."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: libwx: error en el controlador page_pool_dev_alloc_pages. page_pool_dev_alloc_pages podría devolver NULL. Se ejecutó un WARN_ON(!page), pero seguía usando el puntero NULL y se bloqueaba. Esto es similar a la confirmación 001ba0902046 (\"net: fec: error en el controlador page_pool_dev_alloc_pages\"). Esta vulnerabilidad fue detectada por nuestra herramienta de análisis está<PERSON>o <PERSON>er."}], "references": [{"url": "https://git.kernel.org/stable/c/1dd13c60348f515acd8c6f25a561b9c4e3b04fea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f1ff1b38a7c8b872382b796023419d87d78c47e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90bec7cef8805f9a23145e070dff28a02bb584eb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad81d666e114ebf989fc9994d4c93d451dc60056", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c17ef974bfcf1a50818168b47c4606b425a957c4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
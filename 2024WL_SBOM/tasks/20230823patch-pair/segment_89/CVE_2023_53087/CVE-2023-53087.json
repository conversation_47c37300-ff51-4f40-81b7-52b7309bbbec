{"cve_id": "CVE-2023-53087", "published_date": "2025-05-02T16:15:27.667", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915/active: Fix misuse of non-idle barriers as fence trackers\n\nUsers reported oopses on list corruptions when using i915 perf with a\nnumber of concurrently running graphics applications.  Root cause analysis\npointed at an issue in barrier processing code -- a race among perf open /\nclose replacing active barriers with perf requests on kernel context and\nconcurrent barrier preallocate / acquire operations performed during user\ncontext first pin / last unpin.\n\nWhen adding a request to a composite tracker, we try to reuse an existing\nfence tracker, already allocated and registered with that composite.  The\ntracker we obtain may already track another fence, may be an idle barrier,\nor an active barrier.\n\nIf the tracker we get occurs a non-idle barrier then we try to delete that\nbarrier from a list of barrier tasks it belongs to.  However, while doing\nthat we don't respect return value from a function that performs the\nbarrier deletion.  Should the deletion ever fail, we would end up reusing\nthe tracker still registered as a barrier task.  Since the same structure\nfield is reused with both fence callback lists and barrier tasks list,\nlist corruptions would likely occur.\n\nBarriers are now deleted from a barrier tasks list by temporarily removing\nthe list content, traversing that content with skip over the node to be\ndeleted, then populating the list back with the modified content.  Should\nthat intentionally racy concurrent deletion attempts be not serialized,\none or more of those may fail because of the list being temporary empty.\n\nRelated code that ignores the results of barrier deletion was initially\nintroduced in v5.4 by commit d8af05ff38ae (\"drm/i915: Allow sharing the\nidle-barrier from other kernel requests\").  However, all users of the\nbarrier deletion routine were apparently serialized at that time, then the\nissue didn't exhibit itself.  Results of git bisect with help of a newly\ndeveloped igt@gem_barrier_race@remote-request IGT test indicate that list\ncorruptions might start to appear after commit 311770173fac (\"drm/i915/gt:\nSchedule request retirement when timeline idles\"), introduced in v5.5.\n\nRespect results of barrier deletion attempts -- mark the barrier as idle\nonly if successfully deleted from the list.  Then, before proceeding with\nsetting our fence as the one currently tracked, make sure that the tracker\nwe've got is not a non-idle barrier.  If that check fails then don't use\nthat tracker but go back and try to acquire a new, usable one.\n\nv3: use unlikely() to document what outcome we expect (Andi),\n  - fix bad grammar in commit description.\nv2: no code changes,\n  - blame commit 311770173fac (\"drm/i915/gt: Schedule request retirement\n    when timeline idles\"), v5.5, not commit d8af05ff38ae (\"drm/i915: Allow\n    sharing the idle-barrier from other kernel requests\"), v5.4,\n  - reword commit description.\n\n(cherry picked from commit 506006055769b10d1b2b4e22f636f3b45e0e9fc7)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915/active: Arregla el mal uso de barreras no inactivas como rastreadores de vallas Los usuarios informaron errores en las corrupciones de listas al usar i915 perf con varias aplicaciones gráficas que se ejecutan simultáneamente. El análisis de la causa raíz apuntó a un problema en el código de procesamiento de barreras: una ejecución entre la apertura/cierre de perf que reemplaza las barreras activas con solicitudes de perf en el contexto del kernel y las operaciones de preasignación/adquisición de barreras simultáneas realizadas durante el primer pin/último desanclaje del contexto del usuario. Al agregar una solicitud a un rastreador compuesto, intentamos reutilizar un rastreador de vallas existente, ya asignado y registrado con ese compuesto. El rastreador que obtenemos puede que ya rastree otra valla, puede ser una barrera inactiva o una barrera activa. Si el rastreador que obtenemos ocurre con una barrera no inactiva, entonces intentamos eliminar esa barrera de una lista de tareas de barrera a la que pertenece. Sin embargo, mientras hacemos eso no respetamos el valor de retorno de una función que realiza la eliminación de la barrera. Si la eliminación falla, terminaríamos reutilizando el rastreador aún registrado como tarea de barrera. Dado que el mismo campo de estructura se reutiliza tanto con las listas de devolución de llamadas de valla como con la lista de tareas de barrera, es probable que se produzcan daños en la lista. Ahora, las barreras se eliminan de una lista de tareas de barrera eliminando temporalmente su contenido, recorriéndolo con la omisión del nodo que se va a eliminar y, a continuación, rellenando la lista con el contenido modificado. Si estos intentos de eliminación concurrentes, intencionalmente agresivos, no se serializan, uno o más de ellos podrían fallar debido a que la lista está temporalmente vacía. El código relacionado que ignora los resultados de la eliminación de barrera se introdujo inicialmente en la versión 5.4 mediante el commit d8af05ff38ae (\"drm/i915: Permitir compartir la barrera inactiva con otras solicitudes del kernel\"). Sin embargo, todos los usuarios de la rutina de eliminación de barrera aparentemente estaban serializados en ese momento, por lo que el problema no se manifestó. Los resultados de git bisect con la ayuda de una prueba IGT igt@gem_barrier_race@remote-request recientemente desarrollada indican que podrían aparecer corrupciones en la lista después deel commit 311770173fac (\"drm/i915/gt: Retirada de solicitud de programación cuando la línea de tiempo está inactiva\"), introducida en la v5.5. Respetar los resultados de los intentos de eliminación de barreras: marcar la barrera como inactiva solo si se elimina correctamente de la lista. Luego, antes de configurar nuestra barrera como la que se rastrea actualmente, asegurarse de que el rastreador que tenemos no sea una barrera no inactiva. Si la comprobación falla, no usar ese rastreador, sino volver atrás e intentar obtener uno nuevo y utilizable. v3: usar Unlikely() para documentar el resultado esperado (Andi). Corregir errores gramaticales en la descripción de la confirmación. v2: sin cambios de código, - culpar a el commit 311770173fac (\"drm/i915/gt: Programar el retiro de solicitudes cuando la línea de tiempo está inactiva\"), v5.5, no confirmar d8af05ff38ae (\"drm/i915: Permitir compartir la barrera de inactividad con otras solicitudes del kernel\"), v5.4, - reformular la descripción deel commit. (Seleccionado de la confirmación 506006055769b10d1b2b4e22f636f3b45e0e9fc7)"}], "references": [{"url": "https://git.kernel.org/stable/c/5c7591b8574c52c56b3994c2fbef1a3a311b5715", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e784a7d07af42057c0576fb647b482f4cb0dc2c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ab7d33617559cced63d467928f478ea5c459021", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9159db27fb19bbf1c91b5c9d5285e66cc96cc5ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e0e6b416b25ee14716f3549e0cbec1011b193809", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
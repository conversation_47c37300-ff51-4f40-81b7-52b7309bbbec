{"cve_id": "CVE-2025-23143", "published_date": "2025-05-01T13:15:50.127", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: Fix null-ptr-deref by sock_lock_init_class_and_name() and rmmod.\n\nWhen I ran the repro [0] and waited a few seconds, I observed two\nLOCKDEP splats: a warning immediately followed by a null-ptr-deref. [1]\n\nReproduction Steps:\n\n  1) Mount CIFS\n  2) Add an iptables rule to drop incoming FIN packets for CIFS\n  3) Unmount CIFS\n  4) Unload the CIFS module\n  5) Remove the iptables rule\n\nAt step 3), the CIFS module calls sock_release() for the underlying\nTCP socket, and it returns quickly.  However, the socket remains in\nFIN_WAIT_1 because incoming FIN packets are dropped.\n\nAt this point, the module's refcnt is 0 while the socket is still\nalive, so the following rmmod command succeeds.\n\n  # ss -tan\n  State      Recv-Q Send-Q Local Address:Port  Peer Address:Port\n  FIN-WAIT-1 0      477        *********:51062   **********:445\n\n  # lsmod | grep cifs\n  cifs                 1159168  0\n\nThis highlights a discrepancy between the lifetime of the CIFS module\nand the underlying TCP socket.  Even after CIFS calls sock_release()\nand it returns, the TCP socket does not die immediately in order to\nclose the connection gracefully.\n\nWhile this is generally fine, it causes an issue with LOCKDEP because\nCIFS assigns a different lock class to the TCP socket's sk->sk_lock\nusing sock_lock_init_class_and_name().\n\nOnce an incoming packet is processed for the socket or a timer fires,\nsk->sk_lock is acquired.\n\nThen, LOCKDEP checks the lock context in check_wait_context(), where\nhlock_class() is called to retrieve the lock class.  However, since\nthe module has already been unloaded, hlock_class() logs a warning\nand returns NULL, triggering the null-ptr-deref.\n\nIf LOCKDEP is enabled, we must ensure that a module calling\nsock_lock_init_class_and_name() (CIFS, NFS, etc) cannot be unloaded\nwhile such a socket is still alive to prevent this issue.\n\nLet's hold the module reference in sock_lock_init_class_and_name()\nand release it when the socket is freed in sk_prot_free().\n\nNote that sock_lock_init() clears sk->sk_owner for svc_create_socket()\nthat calls sock_lock_init_class_and_name() for a listening socket,\nwhich clones a socket by sk_clone_lock() without GFP_ZERO.\n\n[0]:\nCIFS_SERVER=\"**********\"\nCIFS_PATH=\"//${CIFS_SERVER}/Users/<USER>/Desktop/CIFS_TEST\"\nDEV=\"enp0s3\"\nCRED=\"/root/WindowsCredential.txt\"\n\nMNT=$(mktemp -d /tmp/XXXXXX)\nmount -t cifs ${CIFS_PATH} ${MNT} -o vers=3.0,credentials=${CRED},cache=none,echo_interval=1\n\niptables -A INPUT -s ${CIFS_SERVER} -j DROP\n\nfor i in $(seq 10);\ndo\n    umount ${MNT}\n    rmmod cifs\n    sleep 1\ndone\n\nrm -r ${MNT}\n\niptables -D INPUT -s ${CIFS_SERVER} -j DROP\n\n[1]:\nDEBUG_LOCKS_WARN_ON(1)\nWARNING: CPU: 10 PID: 0 at kernel/locking/lockdep.c:234 hlock_class (kernel/locking/lockdep.c:234 kernel/locking/lockdep.c:223)\nModules linked in: cifs_arc4 nls_ucs2_utils cifs_md4 [last unloaded: cifs]\nCPU: 10 UID: 0 PID: 0 Comm: swapper/10 Not tainted 6.14.0 #36\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.16.0-0-gd239552ce722-prebuilt.qemu.org 04/01/2014\nRIP: 0010:hlock_class (kernel/locking/lockdep.c:234 kernel/locking/lockdep.c:223)\n...\nCall Trace:\n <IRQ>\n __lock_acquire (kernel/locking/lockdep.c:4853 kernel/locking/lockdep.c:5178)\n lock_acquire (kernel/locking/lockdep.c:469 kernel/locking/lockdep.c:5853 kernel/locking/lockdep.c:5816)\n _raw_spin_lock_nested (kernel/locking/spinlock.c:379)\n tcp_v4_rcv (./include/linux/skbuff.h:1678 ./include/net/tcp.h:2547 net/ipv4/tcp_ipv4.c:2350)\n...\n\nBUG: kernel NULL pointer dereference, address: 00000000000000c4\n PF: supervisor read access in kernel mode\n PF: error_code(0x0000) - not-present page\nPGD 0\nOops: Oops: 0000 [#1] PREEMPT SMP NOPTI\nCPU: 10 UID: 0 PID: 0 Comm: swapper/10 Tainted: G        W          6.14.0 #36\nTainted: [W]=WARN\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.16.0-0-gd239552ce722-prebuilt.qemu.org 04/01/2014\nRIP: 0010:__lock_acquire (kernel/\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: Fix null-ptr-deref por sock_lock_init_class_and_name() y rmmod. Cuando ejecuté la reproducción [0] y esperé unos segundos, observé dos símbolos LOCKDEP: una advertencia seguida inmediatamente por un null-ptr-deref. [1] Pasos de reproducción: 1) Montar CIFS 2) Agregar una regla de iptables para descartar los paquetes FIN entrantes para CIFS 3) Desmontar CIFS 4) Descargar el módulo CIFS 5) Eliminar la regla de iptables En el paso 3), el módulo CIFS llama a sock_release() para el socket TCP subyacente y regresa rápidamente. Sin embargo, el socket permanece en FIN_WAIT_1 porque los paquetes FIN entrantes se descartan. En este punto, el refcnt del módulo es 0 mientras el socket sigue activo, por lo que el siguiente comando rmmod tiene éxito. # ss -tan State Recv-Q Send-Q Local Address:Port Peer Address:Port FIN-WAIT-1 0 477 *********:51062 **********:445 # lsmod | grep cifs cifs 1159168 0 Esto indica una discrepancia entre la duración del módulo CIFS y el socket TCP subyacente. Incluso después de que CIFS invoque sock_release() y este regrese, el socket TCP no se cierra inmediatamente para cerrar la conexión correctamente. Si bien esto generalmente funciona bien, causa un problema con LOCKDEP, ya que CIFS asigna una clase de bloqueo diferente al sk-&gt;sk_lock del socket TCP mediante sock_lock_init_class_and_name(). Una vez que se procesa un paquete entrante para el socket o se activa un temporizador, se adquiere sk-&gt;sk_lock. Luego, LOCKDEP verifica el contexto de bloqueo en check_wait_context(), donde se llama a hlock_class() para recuperar la clase de bloqueo. Sin embargo, dado que el módulo ya se ha descargado, hlock_class() registra una advertencia y devuelve NULL, lo que activa la desreferencia null-ptr. Si LOCKDEP está habilitado, debemos asegurarnos de que un módulo que llama a sock_lock_init_class_and_name() (CIFS, NFS, etc.) no pueda descargarse mientras dicho socket siga activo para evitar este problema. Mantendremos la referencia del módulo en sock_lock_init_class_and_name() y la liberaremos cuando el socket se libere en sk_prot_free(). Tenga en cuenta que sock_lock_init() borra sk-&gt;sk_owner para svc_create_socket(), que llama a sock_lock_init_class_and_name() para un socket que escucha, lo que clona un socket mediante sk_clone_lock() sin GFP_ZERO. [0]: CIFS_SERVER=\"**********\" CIFS_PATH=\"//${CIFS_SERVER}/Usuarios/Administrador/Escritorio/CIFS_TEST\" DEV=\"enp0s3\" CRED=\"/root/WindowsCredential.txt\" MNT=$(mktemp -d /tmp/XXXXXX) mount -t cifs ${CIFS_PATH} ${MNT} -o vers=3.0,credenciales=${CRED},caché=ninguno,intervalo_de_eco=1 iptables -A INPUT -s ${CIFS_SERVER} -j DROP para i en $(seq 10); Desmontar ${MNT} rmmod cifs sleep 1 hecho rm -r ${MNT} iptables -D INPUT -s ${CIFS_SERVER} -j DROP [1]: DEBUG_LOCKS_WARN_ON(1) ADVERTENCIA: CPU: 10 PID: 0 en kernel/locking/lockdep.c:234 hlock_class (kernel/locking/lockdep.c:234 kernel/locking/lockdep.c:223) Módulos enlazados en: cifs_arc4 nls_ucs2_utils cifs_md4 [última descarga: cifs] CPU: 10 UID: 0 PID: 0 Comm: swapper/10 No contaminado 6.14.0 #36 Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.16.0-0-gd239552ce722-prebuilt.qemu.org 01/04/2014 RIP: 0010:hlock_class (kernel/locking/lockdep.c:234 kernel/locking/lockdep.c:223) ... Rastreo de llamadas:  __lock_acquire (kernel/locking/lockdep.c:4853 kernel/locking/lockdep.c:5178) lock_acquire (kernel/locking/lockdep.c:469 kernel/locking/lockdep.c:5853 kernel/locking/lockdep.c:5816) _raw_spin_lock_nested (kernel/locking/spinlock.c:379) tcp_v4_rcv (./include/linux/skbuff.h:1678 ./include/net/tcp.h:2547 net/ipv4/tcp_ipv4.c:2350) ... ERROR: desreferencia de puntero NULL del núcleo, dirección: 00000000000000c4 PF: acceso de lectura del supervisor en modo núcleo PF: error_code(0x0000) - página no presente PGD 0 Oops: Oops: 0000 [#1] PREEMPT SMP NOPTI CPU: 10 UID: 0 PID: 0 Comm: swapper/10 Contaminado: GW 6.14.0 #36 Contaminado: [W]=WARN Nombre del hardware: QEMU Standard PC (i440FX + PIIX, ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/0bb2f7a1ad1f11d861f58e5ee5051c8974ff9569", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2155802d3313d7b8365935c6b8d6edc0ddd7eb94", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5f7f6abd92b6c8dc8f19625ef93c3a18549ede04", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c11247a21aab4b50a23c8b696727d7483de2f1e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
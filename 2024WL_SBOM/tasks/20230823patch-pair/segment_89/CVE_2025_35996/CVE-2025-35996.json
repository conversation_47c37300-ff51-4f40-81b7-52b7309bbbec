{"cve_id": "CVE-2025-35996", "published_date": "2025-05-01T19:15:58.047", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "KUNBUS PiCtory version 2.11.1 and earlier are vulnerable when an authenticated remote attacker crafts a special filename that can be stored by API endpoints. That filename is later transmitted to the client in order to show a list of configuration files. Due to a missing escape or sanitization, the filename could be executed as HTML script tag resulting in a cross-site-scripting attack."}, {"lang": "es", "value": "KUNBUS PiCtory versión 2.11.1 y anteriores son vulnerables cuando un atacante remoto autenticado crea un nombre de archivo especial que puede ser almacenado por los endpoints de la API. Este nombre de archivo se transmite posteriormente al cliente para mostrar una lista de archivos de configuración. Debido a la falta de un escape o depuración, el nombre de archivo podría ejecutarse como una etiqueta de script HTML, lo que resulta en un ataque de cross-site-scripting."}], "references": [{"url": "http://packages.revolutionpi.de/pool/main/p/pictory/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-25-121-01", "source": "<EMAIL>", "tags": []}]}
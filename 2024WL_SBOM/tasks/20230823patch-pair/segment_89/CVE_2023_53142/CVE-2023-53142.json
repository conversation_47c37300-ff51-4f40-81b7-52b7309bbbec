{"cve_id": "CVE-2023-53142", "published_date": "2025-05-02T16:15:33.137", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nice: copy last block omitted in ice_get_module_eeprom()\n\nice_get_module_eeprom() is broken since commit e9c9692c8a81 (\"ice:\nReimplement module reads used by ethtool\") In this refactor,\nice_get_module_eeprom() reads the eeprom in blocks of size 8.\nBut the condition that should protect the buffer overflow\nignores the last block. The last block always contains zeros.\n\nBug uncovered by ethtool upstream commit 9538f384b535\n(\"netlink: eeprom: Defer page requests to individual parsers\")\nAfter this commit, ethtool reads a block with length = 1;\nto read the SFF-8024 identifier value.\n\nunpatched driver:\n$ ethtool -m enp65s0f0np0 offset 0x90 length 8\nOffset          Values\n------          ------\n0x0090:         00 00 00 00 00 00 00 00\n$ ethtool -m enp65s0f0np0 offset 0x90 length 12\nOffset          Values\n------          ------\n0x0090:         00 00 01 a0 4d 65 6c 6c 00 00 00 00\n$\n\n$ ethtool -m enp65s0f0np0\nOffset          Values\n------          ------\n0x0000:         11 06 06 00 00 00 00 00 00 00 00 00 00 00 00 00\n0x0010:         00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n0x0020:         00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n0x0030:         00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n0x0040:         00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n0x0050:         00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n0x0060:         00 00 00 00 00 00 00 00 00 00 00 00 00 01 08 00\n0x0070:         00 10 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n\npatched driver:\n$ ethtool -m enp65s0f0np0 offset 0x90 length 8\nOffset          Values\n------          ------\n0x0090:         00 00 01 a0 4d 65 6c 6c\n$ ethtool -m enp65s0f0np0 offset 0x90 length 12\nOffset          Values\n------          ------\n0x0090:         00 00 01 a0 4d 65 6c 6c 61 6e 6f 78\n$ ethtool -m enp65s0f0np0\n    Identifier                                : 0x11 (QSFP28)\n    Extended identifier                       : 0x00\n    Extended identifier description           : 1.5W max. Power consumption\n    Extended identifier description           : No CDR in TX, No CDR in RX\n    Extended identifier description           : High Power Class (> 3.5 W) not enabled\n    Connector                                 : 0x23 (No separable connector)\n    Transceiver codes                         : 0x88 0x00 0x00 0x00 0x00 0x00 0x00 0x00\n    Transceiver type                          : 40G Ethernet: 40G Base-CR4\n    Transceiver type                          : 25G Ethernet: 25G Base-CR CA-N\n    Encoding                                  : 0x05 (64B/66B)\n    BR, Nominal                               : 25500Mbps\n    Rate identifier                           : 0x00\n    Length (SMF,km)                           : 0km\n    Length (OM3 50um)                         : 0m\n    Length (OM2 50um)                         : 0m\n    Length (OM1 62.5um)                       : 0m\n    Length (Copper or Active cable)           : 1m\n    Transmitter technology                    : 0xa0 (Copper cable unequalized)\n    Attenuation at 2.5GHz                     : 4db\n    Attenuation at 5.0GHz                     : 5db\n    Attenuation at 7.0GHz                     : 7db\n    Attenuation at 12.9GHz                    : 10db\n    ........\n    ...."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ice: se omite la copia del último bloque en ice_get_module_eeprom(). ice_get_module_eeprom() no funciona desde el commit e9c9692c8a81 (\"ice: Reimplementar las lecturas del módulo utilizadas por ethtool\"). En esta refactorización, ice_get_module_eeprom() lee la EEPROM en bloques de tamaño 8. Sin embargo, la condición que debería proteger contra el desbordamiento del búfer ignora el último bloque. Este último bloque siempre contiene ceros. Error descubierto por el commit upstream de ethtool 9538f384b535 (\"netlink: eeprom: Aplazar las solicitudes de página a analizadores individuales\"). Después de esta confirmación, ethtool lee un bloque con longitud = 1; para leer el valor del identificador SFF-8024. controlador sin parchear: $ ethtool -m enp65s0f0np0 offset 0x90 length 8 Valores de desplazamiento ------ ------ 0x0090: 00 00 00 00 00 00 00 00 $ ethtool -m enp65s0f0np0 offset 0x90 length 12 Valores de desplazamiento ------ ------ 0x0090: 00 00 01 a0 4d 65 6c 6c 00 00 00 00 $ $ ethtool -m enp65s0f0np0 Valores de desplazamiento ------ ------ 0x0000: 11 06 06 00 00 00 00 00 00 00 00 00 00 00 00 00 0x0010: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 0x0020: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 0x0030: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 0x0040: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 0x0050: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 0x0060: 00 00 00 00 00 00 00 00 00 00 00 00 01 08 00 0x0070: 00 10 00 00 00 00 00 00 00 00 00 00 00 00 00 00 controlador parcheado: $ ethtool -m enp65s0f0np0 offset 0x90 length 8 Valores de desplazamiento ------ ------ 0x0090: 00 00 01 a0 4d 65 6c 6c $ ethtool -m enp65s0f0np0 offset 0x90 length 12 Valores de desplazamiento ------ ------ 0x0090: 00 00 01 a0 4d 65 6c 6c 61 6e 6f 78 $ ethtool -m enp65s0f0np0 Identificador: 0x11 (QSFP28) Identificador extendido: 0x00 Descripción del identificador extendido: 1,5 W máx. Consumo de energía Descripción extendida del identificador: Sin CDR en TX, Sin CDR en RX Descripción extendida del identificador: Clase de alta potencia (&gt; 3,5 W) no habilitada Conector: 0x23 (sin conector separable) Códigos del transceptor: 0x88 0x00 0x00 0x00 0x00 0x00 0x00 0x00 Tipo de transceptor: Ethernet de 40 G: Base-CR4 de 40 G Tipo de transceptor: Ethernet de 25 G: Base-CR de 25 G Codificación CA-N: 0x05 (64B/66B) BR, nominal: 25500 Mbps Identificador de velocidad: 0x00 Longitud (SMF, km): 0 km Longitud (OM3 50 um): 0 m Longitud (OM2 50 um): 0 m Longitud (OM1 62,5 um): 0 m Longitud (cobre o cable activo): 1m Tecnología del transmisor: 0xa0 (cable de cobre sin ecualizar) Atenuación a 2,5 GHz: 4 db Atenuación a 5,0 GHz: 5 db Atenuación a 7,0 GHz: 7 db Atenuación a 12,9 GHz: 10 db ........ ...."}], "references": [{"url": "https://git.kernel.org/stable/c/84cba1840e68430325ac133a11be06bfb2f7acd8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8cfbdda65588e75bfbd93e5ee847efcb4796ad09", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90b40ab29298db3a4879c1d3c4e685184386bce6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c813f7a3161481483ae2077651b21bc217c419e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53047", "published_date": "2025-05-02T16:15:23.790", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntee: amdtee: fix race condition in amdtee_open_session\n\nThere is a potential race condition in amdtee_open_session that may\nlead to use-after-free. For instance, in amdtee_open_session() after\nsess->sess_mask is set, and before setting:\n\n    sess->session_info[i] = session_info;\n\nif amdtee_close_session() closes this same session, then 'sess' data\nstructure will be released, causing kernel panic when 'sess' is\naccessed within amdtee_open_session().\n\nThe solution is to set the bit sess->sess_mask as the last step in\namdtee_open_session()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tee: amdtee: corrección de la condición de ejecución en amdtee_open_session. Existe una posible condición de ejecución en amdtee_open_session que podría provocar un use-after-free. Por ejemplo, en amdtee_open_session(), después de configurar sess-&gt;sess_mask y antes de configurar: sess-&gt;session_info[i] = session_info; si amdtee_close_session() cierra esta misma sesión, se liberará la estructura de datos 'sess', lo que provocará un pánico del kernel al acceder a 'sess' dentro de amdtee_open_session(). La solución es configurar el bit sess-&gt;sess_mask como último paso en amdtee_open_session()."}], "references": [{"url": "https://git.kernel.org/stable/c/02b296978a2137d7128151c542e84dc96400bc00", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a63cce9393e4e7dbc5af82dc87e68cb321cb1a78", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b3ef9e6fe09f1a132af28c623edcf4d4f39d9f35", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f632a90f8e39db39b322107b9a8d438b826a7f4f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f8502fba45bd30e1a6a354d9d898bc99d1a11e6d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
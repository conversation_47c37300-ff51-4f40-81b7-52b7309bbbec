{"cve_id": "CVE-2024-13418", "published_date": "2025-05-02T04:15:45.697", "last_modified_date": "2025-05-06T15:25:54.093", "descriptions": [{"lang": "en", "value": "Multiple plugins and/or themes for WordPress are vulnerable to Arbitrary File Uploads due to a missing capability check on the ajaxUploadFonts() function in various versions. This makes it possible for authenticated attackers, with Subscriber-level access and above, to upload arbitrary files that can make remote code execution possible. This issue was escalated to Envato over two months from the date of this disclosure and the issue, while partially patched, is still vulnerable."}, {"lang": "es", "value": "Varios complementos y/o temas de WordPress son vulnerables a la carga de archivos arbitrarios debido a la falta de una comprobación de capacidad en la función ajaxUploadFonts() en varias versiones. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, carguen archivos arbitrarios que pueden permitir la ejecución remota de código. Este problema se escaló a Envato más de dos meses después de la fecha de esta divulgación y, aunque parcialmente corregido, sigue siendo vulnerable."}], "references": [{"url": "https://themeforest.net/item/beyot-wordpress-real-estate-theme/19514964", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/bced4547-3264-43dc-8bb1-89a06f74ccbd?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
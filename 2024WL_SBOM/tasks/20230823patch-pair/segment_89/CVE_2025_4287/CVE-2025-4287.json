{"cve_id": "CVE-2025-4287", "published_date": "2025-05-05T20:15:22.100", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PyTorch 2.6.0+cu124. It has been rated as problematic. Affected by this issue is the function torch.cuda.nccl.reduce of the file torch/cuda/nccl.py. The manipulation leads to denial of service. It is possible to launch the attack on the local host. The exploit has been disclosed to the public and may be used. The patch is identified as 5827d2061dcb4acd05ac5f8e65d8693a481ba0f5. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PyTorch 2.6.0+cu124. Se ha clasificado como problemática. Este problema afecta a la función torch.cuda.nccl.reduce del archivo torch/cuda/nccl.py. La manipulación provoca una denegación de servicio. Es posible lanzar el ataque contra el host local. Se ha hecho público el exploit y puede que sea utilizado. El parche se identifica como 5827d2061dcb4acd05ac5f8e65d8693a481ba0f5. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/Divigroup-RAP/PYTORCH/commit/5827d2061dcb4acd05ac5f8e65d8693a481ba0f5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pytorch/pytorch/issues/150836", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pytorch/pytorch/issues/150836#issue-2979097872", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pytorch/pytorch/pull/150923", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.307394", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.307394", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.553644", "source": "<EMAIL>", "tags": []}]}
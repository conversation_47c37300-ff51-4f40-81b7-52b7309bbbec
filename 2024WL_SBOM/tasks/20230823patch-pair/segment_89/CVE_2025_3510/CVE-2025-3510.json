{"cve_id": "CVE-2025-3510", "published_date": "2025-05-02T04:15:51.480", "last_modified_date": "2025-05-06T15:19:30.527", "descriptions": [{"lang": "en", "value": "The tagDiv Composer plugin for WordPress is vulnerable to Stored Cross-Site Scripting via multiple shortcodes in all versions up to, and including, 5.4 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento tagDiv Composer para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de múltiples shortcodes en todas las versiones hasta la 5.4 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://tagdiv.com/newspaper-changelog/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://tagdiv.com/tagdiv-composer-page-builder-basics/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://themeforest.net/item/newspaper/5489609", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2bd6b66d-f33e-4287-850b-a199de72f6ad?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-53116", "published_date": "2025-05-02T16:15:30.680", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnvmet: avoid potential UAF in nvmet_req_complete()\n\nAn nvme target ->queue_response() operation implementation may free the\nrequest passed as argument. Such implementation potentially could result\nin a use after free of the request pointer when percpu_ref_put() is\ncalled in nvmet_req_complete().\n\nAvoid such problem by using a local variable to save the sq pointer\nbefore calling __nvmet_req_complete(), thus avoiding dereferencing the\nreq pointer after that function call."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nvmet: evitar posible UAF en nvmet_req_complete(). La implementación de la operación nvme target -&gt;queue_response() puede liberar la solicitud pasada como argumento. Esta implementación podría provocar un use after free del puntero de solicitud al llamar a percpu_ref_put() en nvmet_req_complete(). Para evitar este problema, utilice una variable local para guardar el puntero sq antes de llamar a __nvmet_req_complete(), evitando así la desreferenciación del puntero req después de esa llamada."}], "references": [{"url": "https://git.kernel.org/stable/c/04c394208831d5e0d5cfee46722eb0f033cd4083", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6173a77b7e9d3e202bdb9897b23f2a8afe7bf286", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ed9813871038b25a934b21ab76b5b7dbf44fc3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a6317235da8aa7cb97529ebc8121cc2a4c4c437a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bcd535f07c58342302a2cd2bdd8894fe0872c8a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e5d99b29012bbf0e86929403209723b2806500c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1d5888a5efe345b63c430b256e95acb0a475642", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fafcb4b26393870c45462f9af6a48e581dbbcf7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4199", "published_date": "2025-05-03T03:15:29.070", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The Abundatrade Plugin plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.8.02. This is due to missing or incorrect nonce validation on the 'abundatrade' page. This makes it possible for unauthenticated attackers to update settings and inject malicious web scripts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Abundatrade Plugin para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.8.02 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la página \"abundatrade\". Esto permite que atacantes no autenticados actualicen la configuración e inyecten scripts web maliciosos mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/abundatrade-plugin/tags/1.8.02/abundatrade_pugin.php", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/abundatrade-plugin/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/ef828667-f241-4c5c-92a8-0a4f366e190f?source=cve", "source": "<EMAIL>", "tags": []}]}
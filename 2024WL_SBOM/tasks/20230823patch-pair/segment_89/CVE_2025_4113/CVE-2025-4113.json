{"cve_id": "CVE-2025-4113", "published_date": "2025-04-30T11:15:50.760", "last_modified_date": "2025-05-13T20:25:18.237", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Curfew e-Pass Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /admin/edit-pass-detail.php. The manipulation of the argument editid leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Curfew e-Pass Management System 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/edit-pass-detail.php. La manipulación del argumento editid provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/yhy7612/cve-01/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306593", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306593", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560738", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
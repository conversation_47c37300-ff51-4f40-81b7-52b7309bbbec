{"cve_id": "CVE-2023-53141", "published_date": "2025-05-02T16:15:33.023", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nila: do not generate empty messages in ila_xlat_nl_cmd_get_mapping()\n\nila_xlat_nl_cmd_get_mapping() generates an empty skb,\ntriggerring a recent sanity check [1].\n\nInstead, return an error code, so that user space\ncan get it.\n\n[1]\nskb_assert_len\nWARNING: CPU: 0 PID: 5923 at include/linux/skbuff.h:2527 skb_assert_len include/linux/skbuff.h:2527 [inline]\nWARNING: CPU: 0 PID: 5923 at include/linux/skbuff.h:2527 __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156\nModules linked in:\nCPU: 0 PID: 5923 Comm: syz-executor269 Not tainted 6.2.0-syzkaller-18300-g2ebd1fbb946d #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 01/21/2023\npstate: 60400005 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)\npc : skb_assert_len include/linux/skbuff.h:2527 [inline]\npc : __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156\nlr : skb_assert_len include/linux/skbuff.h:2527 [inline]\nlr : __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156\nsp : ffff80001e0d6c40\nx29: ffff80001e0d6e60 x28: dfff800000000000 x27: ffff0000c86328c0\nx26: dfff800000000000 x25: ffff0000c8632990 x24: ffff0000c8632a00\nx23: 0000000000000000 x22: 1fffe000190c6542 x21: ffff0000c8632a10\nx20: ffff0000c8632a00 x19: ffff80001856e000 x18: ffff80001e0d5fc0\nx17: 0000000000000000 x16: ffff80001235d16c x15: 0000000000000000\nx14: 0000000000000000 x13: 0000000000000001 x12: 0000000000000001\nx11: ff80800008353a30 x10: 0000000000000000 x9 : 21567eaf25bfb600\nx8 : 21567eaf25bfb600 x7 : 0000000000000001 x6 : 0000000000000001\nx5 : ffff80001e0d6558 x4 : ffff800015c74760 x3 : ffff800008596744\nx2 : 0000000000000001 x1 : 0000000100000000 x0 : 000000000000000e\nCall trace:\nskb_assert_len include/linux/skbuff.h:2527 [inline]\n__dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156\ndev_queue_xmit include/linux/netdevice.h:3033 [inline]\n__netlink_deliver_tap_skb net/netlink/af_netlink.c:307 [inline]\n__netlink_deliver_tap+0x45c/0x6f8 net/netlink/af_netlink.c:325\nnetlink_deliver_tap+0xf4/0x174 net/netlink/af_netlink.c:338\n__netlink_sendskb net/netlink/af_netlink.c:1283 [inline]\nnetlink_sendskb+0x6c/0x154 net/netlink/af_netlink.c:1292\nnetlink_unicast+0x334/0x8d4 net/netlink/af_netlink.c:1380\nnlmsg_unicast include/net/netlink.h:1099 [inline]\ngenlmsg_unicast include/net/genetlink.h:433 [inline]\ngenlmsg_reply include/net/genetlink.h:443 [inline]\nila_xlat_nl_cmd_get_mapping+0x620/0x7d0 net/ipv6/ila/ila_xlat.c:493\ngenl_family_rcv_msg_doit net/netlink/genetlink.c:968 [inline]\ngenl_family_rcv_msg net/netlink/genetlink.c:1048 [inline]\ngenl_rcv_msg+0x938/0xc1c net/netlink/genetlink.c:1065\nnetlink_rcv_skb+0x214/0x3c4 net/netlink/af_netlink.c:2574\ngenl_rcv+0x38/0x50 net/netlink/genetlink.c:1076\nnetlink_unicast_kernel net/netlink/af_netlink.c:1339 [inline]\nnetlink_unicast+0x660/0x8d4 net/netlink/af_netlink.c:1365\nnetlink_sendmsg+0x800/0xae0 net/netlink/af_netlink.c:1942\nsock_sendmsg_nosec net/socket.c:714 [inline]\nsock_sendmsg net/socket.c:734 [inline]\n____sys_sendmsg+0x558/0x844 net/socket.c:2479\n___sys_sendmsg net/socket.c:2533 [inline]\n__sys_sendmsg+0x26c/0x33c net/socket.c:2562\n__do_sys_sendmsg net/socket.c:2571 [inline]\n__se_sys_sendmsg net/socket.c:2569 [inline]\n__arm64_sys_sendmsg+0x80/0x94 net/socket.c:2569\n__invoke_syscall arch/arm64/kernel/syscall.c:38 [inline]\ninvoke_syscall+0x98/0x2c0 arch/arm64/kernel/syscall.c:52\nel0_svc_common+0x138/0x258 arch/arm64/kernel/syscall.c:142\ndo_el0_svc+0x64/0x198 arch/arm64/kernel/syscall.c:193\nel0_svc+0x58/0x168 arch/arm64/kernel/entry-common.c:637\nel0t_64_sync_handler+0x84/0xf0 arch/arm64/kernel/entry-common.c:655\nel0t_64_sync+0x190/0x194 arch/arm64/kernel/entry.S:591\nirq event stamp: 136484\nhardirqs last enabled at (136483): [<ffff800008350244>] __up_console_sem+0x60/0xb4 kernel/printk/printk.c:345\nhardirqs last disabled at (136484): [<ffff800012358d60>] el1_dbg+0x24/0x80 arch/arm64/kernel/entry-common.c:405\nsoftirqs last enabled at (136418): [<ffff800008020ea8>] softirq_ha\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ila: no generar mensajes vacíos en ila_xlat_nl_cmd_get_mapping(). ila_xlat_nl_cmd_get_mapping() genera un skb vacío, lo que activa una comprobación de integridad reciente [1]. En su lugar, devuelve un código de error para que el espacio de usuario pueda obtenerlo. [1] skb_assert_len ADVERTENCIA: CPU: 0 PID: 5923 en include/linux/skbuff.h:2527 skb_assert_len include/linux/skbuff.h:2527 [en línea] ADVERTENCIA: CPU: 0 PID: 5923 en include/linux/skbuff.h:2527 __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156 Módulos vinculados: CPU: 0 PID: 5923 Comm: syz-executor269 No contaminado 6.2.0-syzkaller-18300-g2ebd1fbb946d #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 21/01/2023 pstate: 60400005 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--) pc : skb_assert_len include/linux/skbuff.h:2527 [en línea] pc : __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156 lr : skb_assert_len include/linux/skbuff.h:2527 [en línea] lr : __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156 sp : ffff80001e0d6c40 x29: ffff80001e0d6e60 x28: dfff800000000000 x27: ffff0000c86328c0 x26: dfff800000000000 x25: ffff0000c8632990 x24: ffff0000c8632a00 x23: 0000000000000000 x22: 1fffe000190c6542 x21: ffff0000c8632a10 x20: ffff0000c8632a00 x19: ffff80001856e000 x18: ffff80001e0d5fc0 x17: 000000000000000 x16: ffff80001235d16c x15: 000000000000000 x14: 0000000000000000 x13: 0000000000000001 x12: 0000000000000001 x11: ff80800008353a30 x10: 0000000000000000 x9: 21567eaf25bfb600 x8: 21567eaf25bfb600 x7: 000000000000001 x6: 000000000000001 x5: ffff80001e0d6558 x4: ffff800015c74760 x3: ffff800008596744 x2: 0000000000000001 x1 : 0000000100000000 x0 : 000000000000000e Rastreo de llamadas: skb_assert_len include/linux/skbuff.h:2527 [en línea] __dev_queue_xmit+0x1bc0/0x3488 net/core/dev.c:4156 dev_queue_xmit include/linux/netdevice.h:3033 [en línea] __netlink_deliver_tap_skb net/netlink/af_netlink.c:307 [en línea] __netlink_deliver_tap+0x45c/0x6f8 net/netlink/af_netlink.c:325 netlink_deliver_tap+0xf4/0x174 net/netlink/af_netlink.c:338 __netlink_sendskb net/netlink/af_netlink.c:1283 [en línea] netlink_sendskb+0x6c/0x154 net/netlink/af_netlink.c:1292 netlink_unicast+0x334/0x8d4 net/netlink/af_netlink.c:1380 nlmsg_unicast include/net/netlink.h:1099 [en línea] genlmsg_unicast include/net/genetlink.h:433 [en línea] genlmsg_reply include/net/genetlink.h:443 [en línea] ila_xlat_nl_cmd_get_mapping+0x620/0x7d0 net/ipv6/ila/ila_xlat.c:493 genl_family_rcv_msg_doit net/netlink/genetlink.c:968 [en línea] genl_family_rcv_msg net/netlink/genetlink.c:1048 [en línea] genl_rcv_msg+0x938/0xc1c net/netlink/genetlink.c:1065 netlink_rcv_skb+0x214/0x3c4 net/netlink/af_netlink.c:2574 genl_rcv+0x38/0x50 net/netlink/genetlink.c:1076 netlink_unicast_kernel net/netlink/af_netlink.c:1339 [en línea] netlink_unicast+0x660/0x8d4 net/netlink/af_netlink.c:1365 netlink_sendmsg+0x800/0xae0 net/netlink/af_netlink.c:1942 sock_sendmsg_nosec net/socket.c:714 [en línea] sock_sendmsg net/socket.c:734 [en línea] ____sys_sendmsg+0x558/0x844 net/socket.c:2479 ___sys_sendmsg net/socket.c:2533 [en línea] __sys_sendmsg+0x26c/0x33c net/socket.c:2562 __do_sys_sendmsg net/socket.c:2571 [en línea] __se_sys_sendmsg net/socket.c:2569 [en línea] __arm64_sys_sendmsg+0x80/0x94 net/socket.c:2569 __invoke_syscall arch/arm64/kernel/syscall.c:38 [en línea] invocar_syscall+0x98/0x2c0 arch/arm64/kernel/syscall.c:52 el0_svc_common+0x138/0x258 arch/arm64/kernel/syscall.c:142 do_el0_svc+0x64/0x198 arch/arm64/kernel/syscall.c:193 el0_svc+0x58/0x168 arch/arm64/kernel/entry-common.c:637 el0t_64_sync_handler+0x84/0xf0 arch/arm64/kernel/entry-common.c:655 el0t_64_sync+0x190/0x194 arch/arm64/kernel/entry.S:591 marca de evento irq: 136484 hardirqs habilitados por última vez en (136483): [] __up_console_sem+0x60/0xb4 kernel/printk/printk.c:345 hardirqs deshabilitados por última vez en (136484): [] el1_dbg+0x24/0x80 arch/arm6 ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/25b54f247ea060aeb85ec88a82c75060fca03521", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42d9ed4e5dc5f87fbd67c232e2e4a9b88ceeb47f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/60fe7cb483c8c5dcadaeeac867251d6e59c7badc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/693aa2c0d9b6d5b1f2745d31b6e70d09dbbaf06e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/783f218940b3c7b872e4111d0145000f26ecbdf6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/91aceb3844d4aec555c7f423f9fd843eff5835e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b26bc5861505f04dea933ca3e522772b20fa086f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c631e52aea0fc8d4deea06e439f5810a8b40ad0f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
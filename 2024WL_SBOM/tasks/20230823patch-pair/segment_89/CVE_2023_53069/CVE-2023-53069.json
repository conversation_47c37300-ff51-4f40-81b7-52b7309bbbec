{"cve_id": "CVE-2023-53069", "published_date": "2025-05-02T16:15:25.960", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocteontx2-vf: Add missing free for alloc_percpu\n\nAdd the free_percpu for the allocated \"vf->hw.lmt_info\" in order to avoid\nmemory leak, same as the \"pf->hw.lmt_info\" in\n`drivers/net/ethernet/marvell/octeontx2/nic/otx2_pf.c`."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: octeontx2-vf: Agregar libre faltante para alloc_percpu Agregue libre_percpu para el \"vf-&gt;hw.lmt_info\" asignado para evitar fugas de memoria, igual que \"pf-&gt;hw.lmt_info\" en `drivers/net/ethernet/marvell/octeontx2/nic/otx2_pf.c`."}], "references": [{"url": "https://git.kernel.org/stable/c/813b590840771890c738ce6dbfd0c5938a1bafb9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/840631bcf21f58c0a3f01378a54d79e9ce86b226", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90874b76e5f82eaa3309714d72ff2cd8bb8d1b02", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f038f3917baf04835ba2b7bcf2a04ac93fbf8a9c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
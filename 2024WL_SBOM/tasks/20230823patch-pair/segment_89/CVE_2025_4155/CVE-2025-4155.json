{"cve_id": "CVE-2025-4155", "published_date": "2025-05-01T08:15:18.130", "last_modified_date": "2025-05-07T19:49:39.517", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Boat Booking System 1.0. This affects an unknown part of the file /admin/edit-boat.php. The manipulation of the argument bid leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Boat Booking System 1.0. Esta afecta a una parte desconocida del archivo /admin/edit-boat.php. La manipulación del argumento \"bid\" provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306687", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306687", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560853", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-45611", "published_date": "2025-05-05T20:15:20.243", "last_modified_date": "2025-05-06T14:15:38.317", "descriptions": [{"lang": "en", "value": "Incorrect access control in the /user/edit/ component of hope-boot v1.0.0 allows attackers to bypass authentication via a crafted GET request."}, {"lang": "es", "value": "El control de acceso incorrecto en el componente /user/edit/ de hope-boot v1.0.0 permite a los atacantes eludir la autenticación a través de una solicitud GET manipulada."}], "references": [{"url": "https://github.com/java-aodeng/hope-boot/issues/86", "source": "<EMAIL>", "tags": []}]}
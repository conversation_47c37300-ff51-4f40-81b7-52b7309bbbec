{"cve_id": "CVE-2025-37774", "published_date": "2025-05-01T14:15:40.877", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nslab: ensure slab->obj_exts is clear in a newly allocated slab page\n\nktest recently reported crashes while running several buffered io tests\nwith __alloc_tagging_slab_alloc_hook() at the top of the crash call stack.\nThe signature indicates an invalid address dereference with low bits of\nslab->obj_exts being set. The bits were outside of the range used by\npage_memcg_data_flags and objext_flags and hence were not masked out\nby slab_obj_exts() when obtaining the pointer stored in slab->obj_exts.\nThe typical crash log looks like this:\n\n00510 Unable to handle kernel NULL pointer dereference at virtual address 0000000000000010\n00510 Mem abort info:\n00510   ESR = 0x0000000096000045\n00510   EC = 0x25: DABT (current EL), IL = 32 bits\n00510   SET = 0, FnV = 0\n00510   EA = 0, S1PTW = 0\n00510   FSC = 0x05: level 1 translation fault\n00510 Data abort info:\n00510   ISV = 0, ISS = 0x00000045, ISS2 = 0x00000000\n00510   CM = 0, WnR = 1, TnD = 0, TagAccess = 0\n00510   GCS = 0, Overlay = 0, DirtyBit = 0, Xs = 0\n00510 user pgtable: 4k pages, 39-bit VAs, pgdp=0000000104175000\n00510 [0000000000000010] pgd=0000000000000000, p4d=0000000000000000, pud=0000000000000000\n00510 Internal error: Oops: 0000000096000045 [#1]  SMP\n00510 Modules linked in:\n00510 CPU: 10 UID: 0 PID: 7692 Comm: cat Not tainted 6.15.0-rc1-ktest-g189e17946605 #19327 NONE\n00510 Hardware name: linux,dummy-virt (DT)\n00510 pstate: 20001005 (nzCv daif -PAN -UAO -TCO -DIT +SSBS BTYPE=--)\n00510 pc : __alloc_tagging_slab_alloc_hook+0xe0/0x190\n00510 lr : __kmalloc_noprof+0x150/0x310\n00510 sp : ffffff80c87df6c0\n00510 x29: ffffff80c87df6c0 x28: 000000000013d1ff x27: 000000000013d200\n00510 x26: ffffff80c87df9e0 x25: 0000000000000000 x24: 0000000000000001\n00510 x23: ffffffc08041953c x22: 000000000000004c x21: ffffff80c0002180\n00510 x20: fffffffec3120840 x19: ffffff80c4821000 x18: 0000000000000000\n00510 x17: fffffffec3d02f00 x16: fffffffec3d02e00 x15: fffffffec3d00700\n00510 x14: fffffffec3d00600 x13: 0000000000000200 x12: 0000000000000006\n00510 x11: ffffffc080bb86c0 x10: 0000000000000000 x9 : ffffffc080201e58\n00510 x8 : ffffff80c4821060 x7 : 0000000000000000 x6 : 0000000055555556\n00510 x5 : 0000000000000001 x4 : 0000000000000010 x3 : 0000000000000060\n00510 x2 : 0000000000000000 x1 : ffffffc080f50cf8 x0 : ffffff80d801d000\n00510 Call trace:\n00510  __alloc_tagging_slab_alloc_hook+0xe0/0x190 (P)\n00510  __kmalloc_noprof+0x150/0x310\n00510  __bch2_folio_create+0x5c/0xf8\n00510  bch2_folio_create+0x2c/0x40\n00510  bch2_readahead+0xc0/0x460\n00510  read_pages+0x7c/0x230\n00510  page_cache_ra_order+0x244/0x3a8\n00510  page_cache_async_ra+0x124/0x170\n00510  filemap_readahead.isra.0+0x58/0xa0\n00510  filemap_get_pages+0x454/0x7b0\n00510  filemap_read+0xdc/0x418\n00510  bch2_read_iter+0x100/0x1b0\n00510  vfs_read+0x214/0x300\n00510  ksys_read+0x6c/0x108\n00510  __arm64_sys_read+0x20/0x30\n00510  invoke_syscall.constprop.0+0x54/0xe8\n00510  do_el0_svc+0x44/0xc8\n00510  el0_svc+0x18/0x58\n00510  el0t_64_sync_handler+0x104/0x130\n00510  el0t_64_sync+0x154/0x158\n00510 Code: d5384100 f9401c01 b9401aa3 b40002e1 (f8227881)\n00510 ---[ end trace 0000000000000000 ]---\n00510 Kernel panic - not syncing: Oops: Fatal exception\n00510 SMP: stopping secondary CPUs\n00510 Kernel Offset: disabled\n00510 CPU features: 0x0000,000000e0,00000410,8240500b\n00510 Memory Limit: none\n\nInvestigation indicates that these bits are already set when we allocate\nslab page and are not zeroed out after allocation. We are not yet sure\nwhy these crashes start happening only recently but regardless of the\nreason, not initializing a field that gets used later is wrong. Fix it\nby initializing slab->obj_exts during slab page allocation."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: slab: garantizar que slab-&gt;obj_exts esté libre en una página slab recién asignada. ktest reportó recientemente fallos al ejecutar varias pruebas de E/S con buffer con __alloc_tagging_slab_alloc_hook() en la parte superior de la pila de llamadas de fallo. La firma indica una desreferencia de dirección no válida con bits bajos de slab-&gt;obj_exts activados. Los bits estaban fuera del rango utilizado por page_memcg_data_flags y objext_flags, por lo que slab_obj_exts() no los enmascaró al obtener el puntero almacenado en slab-&gt;obj_exts. El registro de fallos típico se ve así: 00510 No se puede controlar la desreferencia del puntero NULL del núcleo en la dirección virtual 0000000000000010 00510 Información de aborto de memoria: 00510 ESR = 0x0000000096000045 00510 EC = 0x25: DABT (current EL), IL = 32 bits 00510 SET = 0, FnV = 0 00510 EA = 0, S1PTW = 0 00510 FSC = 0x05: level 1 translation fault 00510 Data abort info: 00510 ISV = 0, ISS = 0x00000045, ISS2 = 0x00000000 00510 CM = 0, WnR = 1, TnD = 0, TagAccess = 0 00510 GCS = 0, Overlay = 0, DirtyBit = 0, Xs = 0 00510 user pgtable: 4k pages, 39-bit VAs, pgdp=0000000104175000 00510 [0000000000000010] pgd=0000000000000000, p4d=0000000000000000, pud=0000000000000000 00510 Internal error: Oops: 0000000096000045 [#1] SMP 00510 Modules linked in: 00510 CPU: 10 UID: 0 PID: 7692 Comm: cat Not tainted 6.15.0-rc1-ktest-g189e17946605 #19327 NONE 00510 Hardware name: linux,dummy-virt (DT) 00510 pstate: 20001005 (nzCv daif -PAN -UAO -TCO -DIT +SSBS BTYPE=--) 00510 pc : __alloc_tagging_slab_alloc_hook+0xe0/0x190 00510 lr : __kmalloc_noprof+0x150/0x310 00510 sp : ffffff80c87df6c0 00510 x29: ffffff80c87df6c0 x28: 000000000013d1ff x27: 000000000013d200 00510 x26: ffffff80c87df9e0 x25: 0000000000000000 x24: 0000000000000001 00510 x23: ffffffc08041953c x22: 000000000000004c x21: ffffff80c0002180 00510 x20: fffffffec3120840 x19: ffffff80c4821000 x18: 0000000000000000 00510 x17: fffffffec3d02f00 x16: fffffffec3d02e00 x15: fffffffec3d00700 00510 x14: fffffffec3d00600 x13: 0000000000000200 x12: 0000000000000006 00510 x11: ffffffc080bb86c0 x10: 0000000000000000 x9 : ffffffc080201e58 00510 x8 : ffffff80c4821060 x7 : 0000000000000000 x6 : 0000000055555556 00510 x5 : 0000000000000001 x4 : 0000000000000010 x3 : 0000000000000060 00510 x2 : 0000000000000000 x1 : ffffffc080f50cf8 x0 : ffffff80d801d000 00510 Call trace: 00510 __alloc_tagging_slab_alloc_hook+0xe0/0x190 (P) 00510 __kmalloc_noprof+0x150/0x310 00510 __bch2_folio_create+0x5c/0xf8 00510 bch2_folio_create+0x2c/0x40 00510 bch2_readahead+0xc0/0x460 00510 read_pages+0x7c/0x230 00510 page_cache_ra_order+0x244/0x3a8 00510 page_cache_async_ra+0x124/0x170 00510 filemap_readahead.isra.0+0x58/0xa0 00510 filemap_get_pages+0x454/0x7b0 00510 filemap_read+0xdc/0x418 00510 bch2_read_iter+0x100/0x1b0 00510 vfs_read+0x214/0x300 00510 ksys_read+0x6c/0x108 00510 __arm64_sys_read+0x20/0x30 00510 invoke_syscall.constprop.0+0x54/0xe8 00510 do_el0_svc+0x44/0xc8 00510 el0_svc+0x18/0x58 00510 el0t_64_sync_handler+0x104/0x130 00510 el0t_64_sync+0x154/0x158 00510 Code: d5384100 f9401c01 b9401aa3 b40002e1 (f8227881) 00510 ---[ end trace 0000000000000000 ]--- 00510 Kernel panic - not syncing: Oops: Fatal exception 00510 SMP: stopping secondary CPUs 00510 Kernel Offset: disabled 00510 CPU features: 0x0000,000000e0,00000410,8240500b 00510 Límite de memoria: ninguno. La investigación indica que estos bits ya están configurados al asignar la página slab y no se ponen a cero tras la asignación. Aún no sabemos con certeza por qué estos fallos han comenzado a ocurrir recientemente, pero independientemente del motivo, es incorrecto no inicializar un campo que se utiliza posteriormente. Para solucionarlo, inicialice slab-&gt;obj_exts durante la asignación de la página slab."}], "references": [{"url": "https://git.kernel.org/stable/c/28bef6622a1a874fe63aceeb0c684fab75afb3ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8baa747193591410a853bac9c3710142dfa4937b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d2f5819b6ed357c0c350c0616b6b9f38be59adf6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
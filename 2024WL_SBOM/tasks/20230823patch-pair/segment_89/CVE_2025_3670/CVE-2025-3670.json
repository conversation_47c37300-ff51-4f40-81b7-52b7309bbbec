{"cve_id": "CVE-2025-3670", "published_date": "2025-05-02T03:15:20.700", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "The KiwiChat NextClient plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘url’ parameter in all versions up to, and including, 6.2 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento KiwiChat NextClient para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del parámetro 'url' en todas las versiones hasta la 6.2 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/kiwichat/trunk/public/index.php#L25", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/kiwichat/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fd6f6a2c-59d3-4091-82ac-0edf9f47ef65?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37757", "published_date": "2025-05-01T13:15:54.480", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntipc: fix memory leak in tipc_link_xmit\n\nIn case the backlog transmit queue for system-importance messages is overloaded,\ntipc_link_xmit() returns -ENOBUFS but the skb list is not purged. This leads to\nmemory leak and failure when a skb is allocated.\n\nThis commit fixes this issue by purging the skb list before tipc_link_xmit()\nreturns."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tipc: se corrige una fuga de memoria en tipc_link_xmit. Si la cola de transmisión de mensajes importantes del sistema está sobrecargada, tipc_link_xmit() devuelve -ENOBUFS, pero la lista de skb no se purga. Esto provoca una fuga de memoria y un fallo al asignar un skb. Esta confirmación corrige este problema purgando la lista de skb antes del retorno de tipc_link_xmit()."}], "references": [{"url": "https://git.kernel.org/stable/c/09c2dcda2c551bba30710c33f6ac678ae7395389", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/24e6280cdd7f8d01fc6b9b365fb800c2fb7ea9bb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/69ae94725f4fc9e75219d2d69022029c5b24bc9a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7c5957f7905b4aede9d7a559d271438f3ca9e852", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/84895f5ce3829d9fc030e5ec2d8729da4c0c9d08", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a40cbfbb8f95c325430f017883da669b2aa927d4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d0e02d3d27a0b4dcb13f954f537ca1dd8f282dcf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d4d40e437adb376be16b3a12dd5c63f0fa768247", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ed06675d3b8cd37120b447646d53f7cd3e6fcd63", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
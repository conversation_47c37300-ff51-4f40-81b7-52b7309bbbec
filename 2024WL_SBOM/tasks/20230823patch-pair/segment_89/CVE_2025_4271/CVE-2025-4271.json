{"cve_id": "CVE-2025-4271", "published_date": "2025-05-05T08:15:15.790", "last_modified_date": "2025-05-07T16:38:36.743", "descriptions": [{"lang": "en", "value": "A vulnerability was found in TOTOLINK A720R 4.1.5cu.374. It has been declared as problematic. Affected by this vulnerability is an unknown functionality of the file /cgi-bin/cstecgi.cgi. The manipulation of the argument topicurl with the input showSyslog leads to information disclosure. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en TOTOLINK A720R 4.1.5cu.374. Se ha declarado problemática. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /cgi-bin/cstecgi.cgi. La manipulación del argumento \"topicurl\" con la entrada \"showSyslog\" provoca la divulgación de información. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/at0de/my_vulns/blob/main/TOTOLINK/A720R/showSyslog.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307375", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307375", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563444", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}
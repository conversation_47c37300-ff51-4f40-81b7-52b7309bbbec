{"cve_id": "CVE-2025-4337", "published_date": "2025-05-06T05:15:50.597", "last_modified_date": "2025-05-07T14:13:35.980", "descriptions": [{"lang": "en", "value": "The AHAthat Plugin plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.6. This is due to missing or incorrect nonce validation on the aha_plugin_page() function. This makes it possible for unauthenticated attackers to delete AHA pages via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento AHAthat para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.6 incluida. Esto se debe a la falta o a una validación incorrecta de nonce en la función aha_plugin_page(). Esto permite que atacantes no autenticados eliminen páginas AHA mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/ahathat/trunk/includes/class-aha-admin-menu.php#L42", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/af15ae80-dbce-4899-9604-82fdca222bf5?source=cve", "source": "<EMAIL>", "tags": []}]}
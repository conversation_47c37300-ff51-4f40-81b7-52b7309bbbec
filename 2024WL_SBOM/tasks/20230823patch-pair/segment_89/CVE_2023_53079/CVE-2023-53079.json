{"cve_id": "CVE-2023-53079", "published_date": "2025-05-02T16:15:26.923", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/mlx5: Fix steering rules cleanup\n\nvport's mc, uc and multicast rules are not deleted in teardown path when\nEEH happens. Since the vport's promisc settings(uc, mc and all) in\nfirmware are reset after EEH, mlx5 driver will try to delete the above\nrules in the initialization path. This cause kernel crash because these\nsoftware rules are no longer valid.\n\nFix by nullifying these rules right after delete to avoid accessing any dangling\npointers.\n\nCall Trace:\n__list_del_entry_valid+0xcc/0x100 (unreliable)\ntree_put_node+0xf4/0x1b0 [mlx5_core]\ntree_remove_node+0x30/0x70 [mlx5_core]\nmlx5_del_flow_rules+0x14c/0x1f0 [mlx5_core]\nesw_apply_vport_rx_mode+0x10c/0x200 [mlx5_core]\nesw_update_vport_rx_mode+0xb4/0x180 [mlx5_core]\nesw_vport_change_handle_locked+0x1ec/0x230 [mlx5_core]\nesw_enable_vport+0x130/0x260 [mlx5_core]\nmlx5_eswitch_enable_sriov+0x2a0/0x2f0 [mlx5_core]\nmlx5_device_enable_sriov+0x74/0x440 [mlx5_core]\nmlx5_load_one+0x114c/0x1550 [mlx5_core]\nmlx5_pci_resume+0x68/0xf0 [mlx5_core]\neeh_report_resume+0x1a4/0x230\neeh_pe_dev_traverse+0x98/0x170\neeh_handle_normal_event+0x3e4/0x640\neeh_handle_event+0x4c/0x370\neeh_event_handler+0x14c/0x210\nkthread+0x168/0x1b0\nret_from_kernel_thread+0x5c/0x84"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/mlx5: Se corrige la eliminación de las reglas de direccionamiento de las reglas de limpieza de vport mc, uc y multicast en la ruta de desmontaje cuando se produce EEH. Dado que la configuración promisc del vport (uc, mc y todas) en el firmware se restablece después de EEH, el controlador mlx5 intentará eliminar las reglas mencionadas en la ruta de inicialización. Esto provoca un fallo del kernel porque estas reglas de software ya no son válidas. Se corrige anulando estas reglas justo después de la eliminación para evitar el acceso a punteros colgantes. Rastreo de llamadas: __list_del_entry_valid+0xcc/0x100 (unreliable) tree_put_node+0xf4/0x1b0 [mlx5_core] tree_remove_node+0x30/0x70 [mlx5_core] mlx5_del_flow_rules+0x14c/0x1f0 [mlx5_core] esw_apply_vport_rx_mode+0x10c/0x200 [mlx5_core] esw_update_vport_rx_mode+0xb4/0x180 [mlx5_core] esw_vport_change_handle_locked+0x1ec/0x230 [mlx5_core] esw_enable_vport+0x130/0x260 [mlx5_core] mlx5_eswitch_enable_sriov+0x2a0/0x2f0 [mlx5_core] mlx5_device_enable_sriov+0x74/0x440 [mlx5_core] mlx5_load_one+0x114c/0x1550 [mlx5_core] mlx5_pci_resume+0x68/0xf0 [mlx5_core] eeh_report_resume+0x1a4/0x230 eeh_pe_dev_traverse+0x98/0x170 eeh_handle_normal_event+0x3e4/0x640 eeh_handle_event+0x4c/0x370 eeh_event_handler+0x14c/0x210 kthread+0x168/0x1b0 ret_from_kernel_thread+0x5c/0x84 "}], "references": [{"url": "https://git.kernel.org/stable/c/18cead61e437f4c7898acca0a5f3df12f801d97f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4df1f2d36bdc9a368650bf14b9097c555e95f71d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/63546395a0e6ac264f78f65218086ce6014b4494", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6f5780536181d1d0d09a11a1bc92f22e143447e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/922f56e9a795d6f3dd72d3428ebdd7ee040fa855", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53035", "published_date": "2025-05-02T16:15:22.627", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnilfs2: fix kernel-infoleak in nilfs_ioctl_wrap_copy()\n\nThe ioctl helper function nilfs_ioctl_wrap_copy(), which exchanges a\nmetadata array to/from user space, may copy uninitialized buffer regions\nto user space memory for read-only ioctl commands NILFS_IOCTL_GET_SUINFO\nand NILFS_IOCTL_GET_CPINFO.\n\nThis can occur when the element size of the user space metadata given by\nthe v_size member of the argument nilfs_argv structure is larger than the\nsize of the metadata element (nilfs_suinfo structure or nilfs_cpinfo\nstructure) on the file system side.\n\nKMSAN-enabled kernels detect this issue as follows:\n\n BUG: KMSAN: kernel-infoleak in instrument_copy_to_user\n include/linux/instrumented.h:121 [inline]\n BUG: KMSAN: kernel-infoleak in _copy_to_user+0xc0/0x100 lib/usercopy.c:33\n  instrument_copy_to_user include/linux/instrumented.h:121 [inline]\n  _copy_to_user+0xc0/0x100 lib/usercopy.c:33\n  copy_to_user include/linux/uaccess.h:169 [inline]\n  nilfs_ioctl_wrap_copy+0x6fa/0xc10 fs/nilfs2/ioctl.c:99\n  nilfs_ioctl_get_info fs/nilfs2/ioctl.c:1173 [inline]\n  nilfs_ioctl+0x2402/0x4450 fs/nilfs2/ioctl.c:1290\n  nilfs_compat_ioctl+0x1b8/0x200 fs/nilfs2/ioctl.c:1343\n  __do_compat_sys_ioctl fs/ioctl.c:968 [inline]\n  __se_compat_sys_ioctl+0x7dd/0x1000 fs/ioctl.c:910\n  __ia32_compat_sys_ioctl+0x93/0xd0 fs/ioctl.c:910\n  do_syscall_32_irqs_on arch/x86/entry/common.c:112 [inline]\n  __do_fast_syscall_32+0xa2/0x100 arch/x86/entry/common.c:178\n  do_fast_syscall_32+0x37/0x80 arch/x86/entry/common.c:203\n  do_SYSENTER_32+0x1f/0x30 arch/x86/entry/common.c:246\n  entry_SYSENTER_compat_after_hwframe+0x70/0x82\n\n Uninit was created at:\n  __alloc_pages+0x9f6/0xe90 mm/page_alloc.c:5572\n  alloc_pages+0xab0/0xd80 mm/mempolicy.c:2287\n  __get_free_pages+0x34/0xc0 mm/page_alloc.c:5599\n  nilfs_ioctl_wrap_copy+0x223/0xc10 fs/nilfs2/ioctl.c:74\n  nilfs_ioctl_get_info fs/nilfs2/ioctl.c:1173 [inline]\n  nilfs_ioctl+0x2402/0x4450 fs/nilfs2/ioctl.c:1290\n  nilfs_compat_ioctl+0x1b8/0x200 fs/nilfs2/ioctl.c:1343\n  __do_compat_sys_ioctl fs/ioctl.c:968 [inline]\n  __se_compat_sys_ioctl+0x7dd/0x1000 fs/ioctl.c:910\n  __ia32_compat_sys_ioctl+0x93/0xd0 fs/ioctl.c:910\n  do_syscall_32_irqs_on arch/x86/entry/common.c:112 [inline]\n  __do_fast_syscall_32+0xa2/0x100 arch/x86/entry/common.c:178\n  do_fast_syscall_32+0x37/0x80 arch/x86/entry/common.c:203\n  do_SYSENTER_32+0x1f/0x30 arch/x86/entry/common.c:246\n  entry_SYSENTER_compat_after_hwframe+0x70/0x82\n\n Bytes 16-127 of 3968 are uninitialized\n ...\n\nThis eliminates the leak issue by initializing the page allocated as\nbuffer using get_zeroed_page()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nilfs2: corrección de una fuga de información del kernel en nilfs_ioctl_wrap_copy(). La función auxiliar de ioctl nilfs_ioctl_wrap_copy(), que intercambia una matriz de metadatos hacia/desde el espacio de usuario, puede copiar regiones de búfer no inicializadas a la memoria del espacio de usuario para los comandos ioctl de solo lectura NILFS_IOCTL_GET_SUINFO y NILFS_IOCTL_GET_CPINFO. Esto puede ocurrir cuando el tamaño del elemento de los metadatos del espacio de usuario, indicado por el miembro v_size de la estructura del argumento nilfs_argv, es mayor que el tamaño del elemento de metadatos (estructura nilfs_suinfo o nilfs_cpinfo) en el sistema de archivos. Los kernels con KMSAN habilitado detectan este problema de la siguiente manera: ERROR: KMSAN: fuga de información del kernel en instrument_copy_to_user include/linux/instrumented.h:121 [en línea] ERROR: KMSAN: fuga de información del kernel en _copy_to_user+0xc0/0x100 lib/usercopy.c:33 instrument_copy_to_user include/linux/instrumented.h:121 [en línea] _copy_to_user+0xc0/0x100 lib/usercopy.c:33 copy_to_user include/linux/uaccess.h:169 [en línea] nilfs_ioctl_wrap_copy+0x6fa/0xc10 fs/nilfs2/ioctl.c:99 nilfs_ioctl_get_info fs/nilfs2/ioctl.c:1173 [en línea] nilfs_ioctl+0x2402/0x4450 fs/nilfs2/ioctl.c:1290 nilfs_compat_ioctl+0x1b8/0x200 fs/nilfs2/ioctl.c:1343 __do_compat_sys_ioctl fs/ioctl.c:968 [en línea] __se_compat_sys_ioctl+0x7dd/0x1000 fs/ioctl.c:910 __ia32_compat_sys_ioctl+0x93/0xd0 fs/ioctl.c:910 do_syscall_32_irqs_on arch/x86/entry/common.c:112 [en línea] __do_fast_syscall_32+0xa2/0x100 arch/x86/entry/common.c:178 do_fast_syscall_32+0x37/0x80 arch/x86/entry/common.c:203 do_SYSENTER_32+0x1f/0x30 arch/x86/entry/common.c:246 entry_SYSENTER_compat_after_hwframe+0x70/0x82 Uninit se creó en: __alloc_pages+0x9f6/0xe90 mm/page_alloc.c:5572 alloc_pages+0xab0/0xd80 mm/mempolicy.c:2287 __get_free_pages+0x34/0xc0 mm/page_alloc.c:5599 nilfs_ioctl_wrap_copy+0x223/0xc10 fs/nilfs2/ioctl.c:74 nilfs_ioctl_get_info fs/nilfs2/ioctl.c:1173 [en línea] nilfs_ioctl+0x2402/0x4450 fs/nilfs2/ioctl.c:1290 nilfs_compat_ioctl+0x1b8/0x200 fs/nilfs2/ioctl.c:1343 __do_compat_sys_ioctl fs/ioctl.c:968 [en línea] __se_compat_sys_ioctl+0x7dd/0x1000 fs/ioctl.c:910 __ia32_compat_sys_ioctl+0x93/0xd0 fs/ioctl.c:910 do_syscall_32_irqs_on arch/x86/entry/common.c:112 [en línea] __do_fast_syscall_32+0xa2/0x100 arch/x86/entry/common.c:178 do_fast_syscall_32+0x37/0x80 arch/x86/entry/common.c:203 do_SYSENTER_32+0x1f/0x30 arch/x86/entry/common.c:246 entry_SYSENTER_compat_after_hwframe+0x70/0x82 Los bytes 16-127 de 3968 no están inicializados... Esto elimina el problema de pérdida al inicializar la página asignada como búfer usando get_zeroed_page()."}], "references": [{"url": "https://git.kernel.org/stable/c/003587000276f81d0114b5ce773d80c119d8cb30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5bb105cc72beb9d51bf12f5c657336d2d35bdc5d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5f33b042f74fc9662eba17f4cd19b07d84bbc6c5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8a6550b365c0ce2e65905de57dcbfe1f7d629726", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8f5cbf6a8c0e19b062b829c5b7aca01468bb57f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9c5034e9a0e03db8d5e9eabb176340259b5b97e4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a94932381e8dae4117e9129b3c1282e18aa97b05", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d18db946cc6a394291539e030df32324285648f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
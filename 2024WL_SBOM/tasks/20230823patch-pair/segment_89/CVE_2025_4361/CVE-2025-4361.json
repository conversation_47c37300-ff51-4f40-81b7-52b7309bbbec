{"cve_id": "CVE-2025-4361", "published_date": "2025-05-06T15:16:04.980", "last_modified_date": "2025-05-16T17:45:38.793", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Company Visitor Management System 2.0. This affects an unknown part of the file /department.php. The manipulation of the argument departmentname leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Company Visitor Management System 2.0. Esta afecta a una parte desconocida del archivo /department.php. La manipulación del argumento departmentname provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Pjwww13447/pjwww/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307485", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307485", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564750", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-32972", "published_date": "2025-04-30T15:16:01.680", "last_modified_date": "2025-05-13T15:05:07.237", "descriptions": [{"lang": "en", "value": "XWiki is a generic wiki platform. In versions starting from 6.1-milestone-1 to before 15.10.12, from 16.0.0-rc-1 to before 16.4.3, and from 16.5.0-rc-1 to before 16.8.0-rc-1, the script API of the LESS compiler in XWiki is incorrectly checking for rights when calling the cache cleaning API, making it possible to clean the cache without having programming right. The only impact of this is a slowdown in XWiki execution as the caches are re-filled. As this vulnerability requires script right to exploit, and script right already allows unlimited execution of scripts, the additional impact due to this vulnerability is low. This issue has been patched in versions 15.10.12, 16.4.3, and 16.8.0-rc-1."}, {"lang": "es", "value": "XWiki es una plataforma wiki genérica. En versiones desde la 6.1-milestone-1 hasta anteriores a la 15.10.12, desde la 16.0.0-rc-1 hasta anteriores a la 16.4.3, y desde la 16.5.0-rc-1 hasta anteriores a la 16.8.0-rc-1, la API de scripts del compilador LESS en XWiki verifica incorrectamente los permisos al llamar a la API de limpieza de caché, lo que permite limpiar la caché sin tener permisos de programación. El único impacto es una ralentización en la ejecución de XWiki al rellenar las cachés. Dado que esta vulnerabilidad requiere permisos de script para explotarse, y estos permisos ya permiten la ejecución ilimitada de scripts, el impacto adicional es bajo. Este problema se ha corregido en las versiones 15.10.12, 16.4.3 y 16.8.0-rc-1."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/91752122d8782f171f8728004a57bdaefc34253e", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-rp38-24m3-rx87", "source": "<EMAIL>", "tags": ["Vendor Advisory", "Patch"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-22462", "source": "<EMAIL>", "tags": ["Vendor Advisory", "Issue Tracking"]}]}
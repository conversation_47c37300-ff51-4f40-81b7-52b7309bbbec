{"cve_id": "CVE-2025-4267", "published_date": "2025-05-05T06:15:31.897", "last_modified_date": "2025-05-07T16:38:08.487", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in SourceCodester/oretnom23 Stock Management System 1.0. This affects an unknown part of the file /admin/?page=purchase_order/view_po of the component Purchase Order Details Page. The manipulation of the argument ID leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en SourceCodester/oretnom23 Stock Management System 1.0. Esta afecta a una parte desconocida del archivo /admin/?page=purchase_order/view_po del componente \"Página de Detalles de la Orden de Compra\". La manipulación del ID del argumento provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/th3w0lf-1337/Vulnerabilities/blob/main/SMS-PHP/SQLi/PO/info.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307371", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307371", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563231", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
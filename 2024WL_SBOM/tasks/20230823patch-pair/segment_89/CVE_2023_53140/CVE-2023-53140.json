{"cve_id": "CVE-2023-53140", "published_date": "2025-05-02T16:15:32.920", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: core: Remove the /proc/scsi/${proc_name} directory earlier\n\nRemove the /proc/scsi/${proc_name} directory earlier to fix a race\ncondition between unloading and reloading kernel modules. This fixes a bug\nintroduced in 2009 by commit 77c019768f06 (\"[SCSI] fix /proc memory leak in\nthe SCSI core\").\n\nFix the following kernel warning:\n\nproc_dir_entry 'scsi/scsi_debug' already registered\nWARNING: CPU: 19 PID: 27986 at fs/proc/generic.c:376 proc_register+0x27d/0x2e0\nCall Trace:\n proc_mkdir+0xb5/0xe0\n scsi_proc_hostdir_add+0xb5/0x170\n scsi_host_alloc+0x683/0x6c0\n sdebug_driver_probe+0x6b/0x2d0 [scsi_debug]\n really_probe+0x159/0x540\n __driver_probe_device+0xdc/0x230\n driver_probe_device+0x4f/0x120\n __device_attach_driver+0xef/0x180\n bus_for_each_drv+0xe5/0x130\n __device_attach+0x127/0x290\n device_initial_probe+0x17/0x20\n bus_probe_device+0x110/0x130\n device_add+0x673/0xc80\n device_register+0x1e/0x30\n sdebug_add_host_helper+0x1a7/0x3b0 [scsi_debug]\n scsi_debug_init+0x64f/0x1000 [scsi_debug]\n do_one_initcall+0xd7/0x470\n do_init_module+0xe7/0x330\n load_module+0x122a/0x12c0\n __do_sys_finit_module+0x124/0x1a0\n __x64_sys_finit_module+0x46/0x50\n do_syscall_64+0x38/0x80\n entry_SYSCALL_64_after_hwframe+0x46/0xb0"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: core: Eliminar el directorio /proc/scsi/${proc_name} antes. Eliminar el directorio /proc/scsi/${proc_name} antes para corregir una condición de ejecución entre la descarga y la recarga de módulos del kernel. Esto corrige un error introducido en 2009 por el commit 77c019768f06 (\"[SCSI] corregir fuga de memoria de /proc en el núcleo SCSI\"). Corrija la siguiente advertencia del kernel: proc_dir_entry 'scsi/scsi_debug' ya está registrado ADVERTENCIA: CPU: 19 PID: 27986 en fs/proc/generic.c:376 proc_register+0x27d/0x2e0 Seguimiento de llamadas: proc_mkdir+0xb5/0xe0 scsi_proc_hostdir_add+0xb5/0x170 scsi_host_alloc+0x683/0x6c0 sdebug_driver_probe+0x6b/0x2d0 [scsi_debug] really_probe+0x159/0x540 __driver_probe_device+0xdc/0x230 driver_probe_device+0x4f/0x120 __device_attach_driver+0xef/0x180 bus_for_each_drv+0xe5/0x130 __device_attach+0x127/0x290 device_initial_probe+0x17/0x20 bus_probe_device+0x110/0x130 device_add+0x673/0xc80 device_register+0x1e/0x30 sdebug_add_host_helper+0x1a7/0x3b0 [scsi_debug] scsi_debug_init+0x64f/0x1000 [scsi_debug] do_one_initcall+0xd7/0x470 do_init_module+0xe7/0x330 load_module+0x122a/0x12c0 __do_sys_finit_module+0x124/0x1a0 __x64_sys_finit_module+0x46/0x50 do_syscall_64+0x38/0x80 entry_SYSCALL_64_after_hwframe+0x46/0xb0 "}], "references": [{"url": "https://git.kernel.org/stable/c/13daafe1e209b03e9bda16ff2bd2b2da145a139b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/17e98a5ede81b7696bec421f7afa2dfe467f5e6b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1ec363599f8346d5a8d08c71a0d9860d6c420ec0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6b223e32d66ca9db1f252f433514783d8b22a8e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/891a3cba425cf483d96facca55aebd6ff1da4338", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e471e928de97b00f297ad1015cc14f9459765713", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fc663711b94468f4e1427ebe289c9f05669699c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
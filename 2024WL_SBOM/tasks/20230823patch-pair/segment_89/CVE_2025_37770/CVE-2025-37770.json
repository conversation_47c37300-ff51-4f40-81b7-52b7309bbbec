{"cve_id": "CVE-2025-37770", "published_date": "2025-05-01T14:15:40.330", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/pm: Prevent division by zero\n\nThe user can set any speed value.\nIf speed is greater than UINT_MAX/8, division by zero is possible.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/pm: Impide la división por cero. El usuario puede establecer cualquier valor de velocidad. Si la velocidad es superior a UINT_MAX/8, es posible la división por cero. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/05de66de280ea1bd0459c994bfd2dd332cfbc2a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0c02fcbe4a1393a3c02da6ae35e72493cfdb2155", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4b8c3c0d17c07f301011e2908fecd2ebdcfe3d1c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/587de3ca7875c06fe3c3aa4073a85c4eff46591f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/836a189fb422e7efb81c51d5160e47ec7bc11500", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bd4d90adbca1862d03e581e10e74ab73ec75e61b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e109528bbf460e50074c156253d9080d223ee37f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
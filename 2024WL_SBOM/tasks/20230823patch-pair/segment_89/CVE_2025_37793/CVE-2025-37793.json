{"cve_id": "CVE-2025-37793", "published_date": "2025-05-01T14:15:43.787", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: Intel: avs: Fix null-ptr-deref in avs_component_probe()\n\ndevm_kasprintf() returns NULL when memory allocation fails. Currently,\navs_component_probe() does not check for this case, which results in a\nNULL pointer dereference."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: Intel: avs: Se corrige la desreferencia de puntero null en avs_component_probe(). Devm_kasprintf() devuelve NULL cuando falla la asignación de memoria. Actualmente, avs_component_probe() no verifica este caso, lo que resulta en una desreferencia de puntero NULL."}], "references": [{"url": "https://git.kernel.org/stable/c/23fde311ea1d0a6c36bf92ce48b90b77d0ece1a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/95f723cf141b95e3b3a5b92cf2ea98a863fe7275", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aaa93b8846101461de815759d39979661b82d5a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c2825073271b6f15e669a424b363612082494863", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-1304", "published_date": "2025-05-01T04:16:43.183", "last_modified_date": "2025-05-06T15:38:17.380", "descriptions": [{"lang": "en", "value": "The NewsBlogger theme for WordPress is vulnerable to arbitrary file uploads due to a missing capability check on the newsblogger_install_and_activate_plugin() function in all versions up to, and including, *******. This makes it possible for authenticated attackers, with subscriber-level access and above, to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El tema NewsBlogger para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de una comprobación de capacidad en la función newsblogger_install_and_activate_plugin() en todas las versiones hasta la ******* incluida. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, carguen archivos arbitrarios en el servidor del sitio afectado, lo que podría posibilitar la ejecución remota de código."}], "references": [{"url": "https://themes.trac.wordpress.org/browser/newsblogger/*******/functions.php?annotate=blame&rev=269615#file2", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://themes.trac.wordpress.org/browser/newsblogger/0.2/functions.php#L440", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://themes.trac.wordpress.org/browser/newsblogger/0.2/functions.php#L461", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://themes.trac.wordpress.org/browser/newsblogger/0.2/functions.php#L470", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/85cea6b5-d57b-495e-a504-a0c1ba691637?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
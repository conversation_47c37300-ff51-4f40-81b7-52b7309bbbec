{"cve_id": "CVE-2022-49866", "published_date": "2025-05-01T15:16:11.530", "last_modified_date": "2025-05-07T13:22:26.790", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: wwan: mhi: fix memory leak in mhi_mbim_dellink\n\nMHI driver registers network device without setting the\nneeds_free_netdev flag, and does NOT call free_netdev() when\nunregisters network device, which causes a memory leak.\n\nThis patch sets needs_free_netdev to true when registers\nnetwork device, which makes netdev subsystem call free_netdev()\nautomatically after unregister_netdevice()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: wwan: mhi: se corrige una fuga de memoria en mhi_mbim_dellink. El controlador MHI registra el dispositivo de red sin configurar el indicador needs_free_netdev y NO llama a free_netdev() al cancelar el registro del dispositivo de red, lo que provoca una fuga de memoria. Este parche establece needs_free_netdev como verdadero al registrar el dispositivo de red, lo que hace que el subsistema netdev llame automáticamente a free_netdev() después de unregister_netdevice()."}], "references": [{"url": "https://git.kernel.org/stable/c/2845bc9070cef0c651987487d84d4813d64675dd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3cd3ffe952f78ec5dadf300cb58d4b38a0c0106d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/668205b9c9f94d5ed6ab00cce9a46a654c2b5d16", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
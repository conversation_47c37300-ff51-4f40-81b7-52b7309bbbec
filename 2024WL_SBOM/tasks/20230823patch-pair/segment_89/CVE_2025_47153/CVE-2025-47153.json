{"cve_id": "CVE-2025-47153", "published_date": "2025-05-01T07:15:58.520", "last_modified_date": "2025-05-02T19:15:55.930", "descriptions": [{"lang": "en", "value": "Certain build processes for libuv and Node.js for 32-bit systems, such as for the nodejs binary package through nodejs_20.19.0+dfsg-2_i386.deb for Debian GNU/Linux, have an inconsistent off_t size (e.g., building on i386 De<PERSON> always uses _FILE_OFFSET_BITS=64 for the libuv dynamic library, but uses the _FILE_OFFSET_BITS global system default of 32 for nodejs), leading to out-of-bounds access. NOTE: this is not a problem in the Node.js software itself. In particular, the Node.js website's download page does not offer prebuilt Node.js for Linux on i386."}, {"lang": "es", "value": "Ciertos procesos de compilación para libuv y Node.js en sistemas de 32 bits, como el paquete binario nodejs hasta nodejs_20.19.0+dfsg-2_i386.deb para Debian GNU/Linux, tienen un tamaño de archivo off_t inconsistente (p. ej., al compilar en Debian i386 siempre se usa _FILE_OFFSET_BITS=64 para la biblioteca dinámica libuv, pero se usa el valor predeterminado del sistema global _FILE_OFFSET_BITS de 32 para nodejs), lo que provoca accesos fuera de los límites. NOTA: Esto no es un problema del software Node.js en sí. En particular, la página de descargas del sitio web de Node.js no ofrece Node.js precompilado para Linux en i386."}], "references": [{"url": "https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=1076350", "source": "<EMAIL>", "tags": []}, {"url": "https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=922075", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=892601", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nodejs/node-v0.x-archive/issues/4549", "source": "<EMAIL>", "tags": []}, {"url": "http://www.openwall.com/lists/oss-security/2025/05/02/2", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2025/05/msg00003.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
{"cve_id": "CVE-2025-47268", "published_date": "2025-05-05T14:15:29.063", "last_modified_date": "2025-07-23T15:15:32.887", "descriptions": [{"lang": "en", "value": "ping in iputils before 20250602 allows a denial of service (application error or incorrect data collection) via a crafted ICMP Echo Reply packet, because of a signed 64-bit integer overflow in timestamp multiplication."}, {"lang": "es", "value": "Ping en iputils hasta 20240905 permite una denegación de servicio (error de aplicación o recopilación de datos incorrecta) a través de un paquete de respuesta de eco ICMP manipulado, debido a un desbordamiento de entero con signo de 64 bits en la multiplicación de marcas de tiempo."}], "references": [{"url": "https://bugzilla.suse.com/show_bug.cgi?id=1242300", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/Zephkek/ping-rtt-overflow/", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/iputils/iputils/issues/584", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://github.com/iputils/iputils/pull/585", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://github.com/iputils/iputils/releases/tag/20250602", "source": "<EMAIL>", "tags": []}]}
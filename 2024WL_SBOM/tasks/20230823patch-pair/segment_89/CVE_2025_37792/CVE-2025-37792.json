{"cve_id": "CVE-2025-37792", "published_date": "2025-05-01T14:15:43.660", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: btrtl: Prevent potential NULL dereference\n\nThe btrtl_initialize() function checks that rtl_load_file() either\nhad an error or it loaded a zero length file.  However, if it loaded\na zero length file then the error code is not set correctly.  It\nresults in an error pointer vs NULL bug, followed by a NULL pointer\ndereference.  This was detected by Smatch:\n\ndrivers/bluetooth/btrtl.c:592 btrtl_initialize() warn: passing zero to 'ERR_PTR'"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: btrtl: Prevenir posible desreferencia de NULL. La función btrtl_initialize() comprueba si rtl_load_file() tuvo un error o cargó un archivo de longitud cero. Sin embargo, si cargó un archivo de longitud cero, el código de error no se configura correctamente. Esto genera un error de puntero de error vs. NULL, seguido de una desreferencia de puntero NULL. Esto fue detectado por Smatch: drivers/bluetooth/btrtl.c:592 btrtl_initialize() warn: passing zero to 'ERR_PTR'"}], "references": [{"url": "https://git.kernel.org/stable/c/2d7c60c2a38b4b461fa960ad0995136a6bfe0756", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/324dddea321078a6eeb535c2bff5257be74c9799", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3db6605043b50c8bb768547b23e0222f67ceef3e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/53ceef799dcfc22c734d600811bfc9dd32eaea0a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/73dc99c0ea94abd22379b2d82cacbc73f3e18ec1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aaf356f872a60db1e96fb762a62c4607fd22741f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c3e9717276affe59fd8213706db021b493e81e34", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d8441818690d795232331bd8358545c5c95b6b72", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4292", "published_date": "2025-05-05T23:15:47.437", "last_modified_date": "2025-06-17T20:17:44.000", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in MRCMS 3.1.3 and classified as problematic. Affected by this vulnerability is an unknown functionality of the file /admin/user/edit.do of the component Edit User Page. The manipulation of the argument Username leads to cross site scripting. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en MRCMS 3.1.3, clasificada como problemática. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/user/edit.do del componente Edit User Page. La manipulación del argumento \"Username\" provoca ataques de cross site scripting. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bdkuzma/vuln/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307399", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307399", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563533", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
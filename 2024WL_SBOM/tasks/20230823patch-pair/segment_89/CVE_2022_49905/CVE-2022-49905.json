{"cve_id": "CVE-2022-49905", "published_date": "2025-05-01T15:16:15.593", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/smc: Fix possible leaked pernet namespace in smc_init()\n\nIn smc_init(), register_pernet_subsys(&smc_net_stat_ops) is called\nwithout any error handling.\nIf it fails, registering of &smc_net_ops won't be reverted.\nAnd if smc_nl_init() fails, &smc_net_stat_ops itself won't be reverted.\n\nThis leaves wild ops in subsystem linkedlist and when another module\ntries to call register_pernet_operations() it triggers page fault:\n\nBUG: unable to handle page fault for address: fffffbfff81b964c\nRIP: 0010:register_pernet_operations+0x1b9/0x5f0\nCall Trace:\n  <TASK>\n  register_pernet_subsys+0x29/0x40\n  ebtables_init+0x58/0x1000 [ebtables]\n  ..."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/smc: Se corrige una posible fuga de espacio de nombres pernet en smc_init(). En smc_init(), se llama a register_pernet_subsys(&amp;smc_net_stat_ops) sin gestionar errores. Si falla, el registro de &amp;smc_net_ops no se revertirá. Y si smc_nl_init() falla, &amp;smc_net_stat_ops no se revertirá. Esto deja operaciones salvajes en la lista enlazada del subsistema y cuando otro módulo intenta llamar a register_pernet_operations() desencadena un error de página: ERROR: no se puede manejar el error de página para la dirección: fffffbfff81b964c RIP: 0010:register_pernet_operations+0x1b9/0x5f0 Rastreo de llamada:  register_pernet_subsys+0x29/0x40 ebtables_init+0x58/0x1000 [ebtables] ... "}], "references": [{"url": "https://git.kernel.org/stable/c/61defd6450a9ef4a1487090449999b0fd83518ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/62ff373da2534534c55debe6c724c7fe14adb97f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c97daf836f7caf81d3144b8cd2b2a51f9bc3bd09", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
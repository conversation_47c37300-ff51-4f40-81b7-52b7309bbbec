{"cve_id": "CVE-2023-53119", "published_date": "2025-05-02T16:15:30.980", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfc: pn533: initialize struct pn533_out_arg properly\n\nstruct pn533_out_arg used as a temporary context for out_urb is not\ninitialized properly. Its uninitialized 'phy' field can be dereferenced in\nerror cases inside pn533_out_complete() callback function. It causes the\nfollowing failure:\n\ngeneral protection fault, probably for non-canonical address 0xdffffc0000000000: 0000 [#1] PREEMPT SMP KASAN\nKASAN: null-ptr-deref in range [0x0000000000000000-0x0000000000000007]\nCPU: 1 PID: 0 Comm: swapper/1 Not tainted 6.2.0-rc3-next-20230110-syzkaller #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 10/26/2022\nRIP: 0010:pn533_out_complete.cold+0x15/0x44 drivers/nfc/pn533/usb.c:441\nCall Trace:\n <IRQ>\n __usb_hcd_giveback_urb+0x2b6/0x5c0 drivers/usb/core/hcd.c:1671\n usb_hcd_giveback_urb+0x384/0x430 drivers/usb/core/hcd.c:1754\n dummy_timer+0x1203/0x32d0 drivers/usb/gadget/udc/dummy_hcd.c:1988\n call_timer_fn+0x1da/0x800 kernel/time/timer.c:1700\n expire_timers+0x234/0x330 kernel/time/timer.c:1751\n __run_timers kernel/time/timer.c:2022 [inline]\n __run_timers kernel/time/timer.c:1995 [inline]\n run_timer_softirq+0x326/0x910 kernel/time/timer.c:2035\n __do_softirq+0x1fb/0xaf6 kernel/softirq.c:571\n invoke_softirq kernel/softirq.c:445 [inline]\n __irq_exit_rcu+0x123/0x180 kernel/softirq.c:650\n irq_exit_rcu+0x9/0x20 kernel/softirq.c:662\n sysvec_apic_timer_interrupt+0x97/0xc0 arch/x86/kernel/apic/apic.c:1107\n\nInitialize the field with the pn533_usb_phy currently used.\n\nFound by Linux Verification Center (linuxtesting.org) with Syzkaller."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfc: pn533: inicializar correctamente la estructura pn533_out_arg. La estructura pn533_out_arg, utilizada como contexto temporal para out_urb, no se inicializa correctamente. Su campo \"phy\" no inicializado puede desreferenciarse en casos de error dentro de la función de devolución de llamada pn533_out_complete(). Provoca el siguiente error: fallo de protección general, probablemente para la dirección no canónica 0xdffffc0000000000: 0000 [#1] PREEMPT SMP KASAN KASAN: null-ptr-deref en el rango [0x0000000000000000-0x0000000000000007] CPU: 1 PID: 0 Comm: swapper/1 No contaminado 6.2.0-rc3-next-20230110-syzkaller #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 26/10/2022 RIP: 0010:pn533_out_complete.cold+0x15/0x44 drivers/nfc/pn533/usb.c:441 Rastreo de llamadas:  __usb_hcd_giveback_urb+0x2b6/0x5c0 drivers/usb/core/hcd.c:1671 usb_hcd_giveback_urb+0x384/0x430 drivers/usb/core/hcd.c:1754 dummy_timer+0x1203/0x32d0 drivers/usb/gadget/udc/dummy_hcd.c:1988 call_timer_fn+0x1da/0x800 kernel/time/timer.c:1700 expire_timers+0x234/0x330 kernel/time/timer.c:1751 __run_timers kernel/time/timer.c:2022 [inline] __run_timers kernel/time/timer.c:1995 [inline] run_timer_softirq+0x326/0x910 kernel/time/timer.c:2035 __do_softirq+0x1fb/0xaf6 kernel/softirq.c:571 invoke_softirq kernel/softirq.c:445 [inline] __irq_exit_rcu+0x123/0x180 kernel/softirq.c:650 irq_exit_rcu+0x9/0x20 kernel/softirq.c:662 sysvec_apic_timer_interrupt+0x97/0xc0 arch/x86/kernel/apic/apic.c:1107 Inicializa el campo con el pn533_usb_phy utilizado actualmente. Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con Syzkaller."}], "references": [{"url": "https://git.kernel.org/stable/c/0f9c1f26d434c32520dfe33326b28c5954bc4299", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2703da78849c47b6b5b4471edb35fc7b7f91dead", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2bd1ed6d607d7013ed4959e86990a04f028543ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2bee84369b76f6c9ef71938069c65a6ebd1a12f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2cbd4213baf7be5d87d183e2032c54003de0790f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/484b7059796e3bc1cb527caa61dfc60da649b4f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4c20a07ed26a71a8ccc9c6d935fc181573f5462e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a97ef110c491b72c138111a595a3a3af56cbc94c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
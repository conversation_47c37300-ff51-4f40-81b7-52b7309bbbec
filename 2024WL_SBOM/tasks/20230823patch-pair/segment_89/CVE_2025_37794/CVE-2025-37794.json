{"cve_id": "CVE-2025-37794", "published_date": "2025-05-01T14:15:43.913", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: mac80211: Purge vif txq in ieee80211_do_stop()\n\nAfter ieee80211_do_stop() SKB from vif's txq could still be processed.\nIndeed another concurrent vif schedule_and_wake_txq call could cause\nthose packets to be dequeued (see ieee80211_handle_wake_tx_queue())\nwithout checking the sdata current state.\n\nBecause vif.drv_priv is now cleared in this function, this could lead to\ndriver crash.\n\nFor example in ath12k, ahvif is store in vif.drv_priv. Thus if\nath12k_mac_op_tx() is called after ieee80211_do_stop(), ahvif->ah can be\nNULL, leading the ath12k_warn(ahvif->ah,...) call in this function to\ntrigger the NULL deref below.\n\n  Unable to handle kernel paging request at virtual address dfffffc000000001\n  KASAN: null-ptr-deref in range [0x0000000000000008-0x000000000000000f]\n  batman_adv: bat0: Interface deactivated: brbh1337\n  Mem abort info:\n    ESR = 0x0000000096000004\n    EC = 0x25: DABT (current EL), IL = 32 bits\n    SET = 0, FnV = 0\n    EA = 0, S1PTW = 0\n    FSC = 0x04: level 0 translation fault\n  Data abort info:\n    ISV = 0, ISS = 0x00000004, ISS2 = 0x00000000\n    CM = 0, WnR = 0, TnD = 0, TagAccess = 0\n    GCS = 0, Overlay = 0, DirtyBit = 0, Xs = 0\n  [dfffffc000000001] address between user and kernel address ranges\n  Internal error: Oops: 0000000096000004 [#1] SMP\n  CPU: 1 UID: 0 PID: 978 Comm: lbd Not tainted 6.13.0-g633f875b8f1e #114\n  Hardware name: HW (DT)\n  pstate: 10000005 (nzcV daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--)\n  pc : ath12k_mac_op_tx+0x6cc/0x29b8 [ath12k]\n  lr : ath12k_mac_op_tx+0x174/0x29b8 [ath12k]\n  sp : ffffffc086ace450\n  x29: ffffffc086ace450 x28: 0000000000000000 x27: 1ffffff810d59ca4\n  x26: ffffff801d05f7c0 x25: 0000000000000000 x24: 000000004000001e\n  x23: ffffff8009ce4926 x22: ffffff801f9c0800 x21: ffffff801d05f7f0\n  x20: ffffff8034a19f40 x19: 0000000000000000 x18: ffffff801f9c0958\n  x17: ffffff800bc0a504 x16: dfffffc000000000 x15: ffffffc086ace4f8\n  x14: ffffff801d05f83c x13: 0000000000000000 x12: ffffffb003a0bf03\n  x11: 0000000000000000 x10: ffffffb003a0bf02 x9 : ffffff8034a19f40\n  x8 : ffffff801d05f818 x7 : 1ffffff0069433dc x6 : ffffff8034a19ee0\n  x5 : ffffff801d05f7f0 x4 : 0000000000000000 x3 : 0000000000000001\n  x2 : 0000000000000000 x1 : dfffffc000000000 x0 : 0000000000000008\n  Call trace:\n   ath12k_mac_op_tx+0x6cc/0x29b8 [ath12k] (P)\n   ieee80211_handle_wake_tx_queue+0x16c/0x260\n   ieee80211_queue_skb+0xeec/0x1d20\n   ieee80211_tx+0x200/0x2c8\n   ieee80211_xmit+0x22c/0x338\n   __ieee80211_subif_start_xmit+0x7e8/0xc60\n   ieee80211_subif_start_xmit+0xc4/0xee0\n   __ieee80211_subif_start_xmit_8023.isra.0+0x854/0x17a0\n   ieee80211_subif_start_xmit_8023+0x124/0x488\n   dev_hard_start_xmit+0x160/0x5a8\n   __dev_queue_xmit+0x6f8/0x3120\n   br_dev_queue_push_xmit+0x120/0x4a8\n   __br_forward+0xe4/0x2b0\n   deliver_clone+0x5c/0xd0\n   br_flood+0x398/0x580\n   br_dev_xmit+0x454/0x9f8\n   dev_hard_start_xmit+0x160/0x5a8\n   __dev_queue_xmit+0x6f8/0x3120\n   ip6_finish_output2+0xc28/0x1b60\n   __ip6_finish_output+0x38c/0x638\n   ip6_output+0x1b4/0x338\n   ip6_local_out+0x7c/0xa8\n   ip6_send_skb+0x7c/0x1b0\n   ip6_push_pending_frames+0x94/0xd0\n   rawv6_sendmsg+0x1a98/0x2898\n   inet_sendmsg+0x94/0xe0\n   __sys_sendto+0x1e4/0x308\n   __arm64_sys_sendto+0xc4/0x140\n   do_el0_svc+0x110/0x280\n   el0_svc+0x20/0x60\n   el0t_64_sync_handler+0x104/0x138\n   el0t_64_sync+0x154/0x158\n\nTo avoid that, empty vif's txq at ieee80211_do_stop() so no packet could\nbe dequeued after ieee80211_do_stop() (new packets cannot be queued\nbecause SDATA_STATE_RUNNING is cleared at this point)."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: mac80211: Purgar la txq de vif en ieee80211_do_stop(). Después de ieee80211_do_stop(), el SKB de la txq de vif aún podía procesarse. De hecho, otra llamada simultánea a vif schedule_and_wake_txq podría provocar que esos paquetes se descolgaran (véase ieee80211_handle_wake_tx_queue()) sin comprobar el estado actual de sdata. Dado que vif.drv_priv se borra en esta función, esto podría provocar un fallo del controlador. Por ejemplo, en ath12k, ahvif se almacena en vif.drv_priv. Por lo tanto, si se llama ath12k_mac_op_tx() después de ieee80211_do_stop(), ahvif-&gt;ah puede ser NULL, lo que lleva a la llamada ath12k_warn(ahvif-&gt;ah,...) en esta función a activar la deref NULL a continuación. No se puede gestionar la solicitud de paginación del núcleo en la dirección virtual dfffffc000000001 KASAN: null-ptr-deref en el rango [0x000000000000008-0x000000000000000f] batman_adv: bat0: Interfaz desactivada: brbh1337 Información de aborto de memoria: ESR = 0x0000000096000004 EC = 0x25: DABT (EL actual), IL = 32 bits SET = 0, FnV = 0 EA = 0, S1PTW = 0 FSC = 0x04: fallo de traducción de nivel 0 Información de aborto de datos: ISV = 0, ISS = 0x00000004, ISS2 = 0x00000000 CM = 0, WnR = 0, TnD = 0, TagAccess = 0 GCS = 0, Overlay = 0, DirtyBit = 0, Xs = 0 [dfffffc000000001] dirección entre rangos de direcciones de usuario y kernel Error interno: Oops: 0000000096000004 [#1] SMP CPU: 1 UID: 0 PID: 978 Comm: lbd No contaminado 6.13.0-g633f875b8f1e #114 Nombre del hardware: HW (DT) pstate: 10000005 (nzcV daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--) pc : ath12k_mac_op_tx+0x6cc/0x29b8 [ath12k] lr : ath12k_mac_op_tx+0x174/0x29b8 [ath12k] sp : ffffffc086ace450 x29: ffffffc086ace450 x28: 0000000000000000 x27: 1ffffff810d59ca4 x26: ffffff801d05f7c0 x25: 000000000000000 x24: 000000004000001e x23: ffffff8009ce4926 x22: ffffff801f9c0800 x21: ffffff801d05f7f0 x20: ffffff8034a19f40 x19: 0000000000000000 x18: ffffff801f9c0958 x17: ffffff800bc0a504 x16: dfffffc000000000 x15: ffffffc086ace4f8 x14: ffffff801d05f83c x13: 000000000000000 x12: ffffffb003a0bf03 x11: 000000000000000 x10: ffffffb003a0bf02 x9: ffffff8034a19f40 x8: ffffff801d05f818 x7: 1ffffff0069433dc x6: ffffff8034a19ee0 x5: ffffff801d05f7f0 x4: 0000000000000000 x3: 0000000000000001 x2: 0000000000000000 x1: dfffffc000000000 x0: 0000000000000008 Rastreo de llamadas: ath12k_mac_op_tx+0x6cc/0x29b8 [ath12k] (P) ieee80211_handle_wake_tx_queue+0x16c/0x260 ieee80211_queue_skb+0xeec/0x1d20 ieee80211_tx+0x200/0x2c8 ieee80211_xmit+0x22c/0x338 __ieee80211_subif_start_xmit+0x7e8/0xc60 ieee80211_subif_start_xmit+0xc4/0xee0 __ieee80211_subif_start_xmit_8023.isra.0+0x854/0x17a0 ieee80211_subif_start_xmit_8023+0x124/0x488 dev_hard_start_xmit+0x160/0x5a8 __dev_queue_xmit+0x6f8/0x3120 br_dev_queue_push_xmit+0x120/0x4a8 __br_forward+0xe4/0x2b0 deliver_clone+0x5c/0xd0 br_flood+0x398/0x580 br_dev_xmit+0x454/0x9f8 dev_hard_start_xmit+0x160/0x5a8 __dev_queue_xmit+0x6f8/0x3120 ip6_finish_output2+0xc28/0x1b60 __ip6_finish_output+0x38c/0x638 ip6_output+0x1b4/0x338 ip6_local_out+0x7c/0xa8 ip6_send_skb+0x7c/0x1b0 ip6_push_pending_frames+0x94/0xd0 rawv6_sendmsg+0x1a98/0x2898 inet_sendmsg+0x94/0xe0 __sys_sendto+0x1e4/0x308 __arm64_sys_sendto+0xc4/0x140 do_el0_svc+0x110/0x280 el0_svc+0x20/0x60 el0t_64_sync_handler+0x104/0x138 el0t_64_sync+0x154/0x158 Para evitar eso, vacíe el txq de vif en ieee80211_do_stop() para que ningún paquete pueda sacarse de la cola después de ieee80211_do_stop() (los paquetes nuevos no pueden ponerse en cola porque SDATA_STATE_RUNNING se borra en este punto)."}], "references": [{"url": "https://git.kernel.org/stable/c/305741e7e63234cbcf9b5c4e6aeca25ba0834be8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/378677eb8f44621ecc9ce659f7af61e5baa94d81", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5f6863dc407f25fcf23fc857f9ac51756a09ea2c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8bc34db7f771a464ff8f686b6f8d4e04963fec27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/929ec2c9ad34248ef625e137b6118b6e965797d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a8df245b5b29f6de98d016dc18e2bb35ec70b0cb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a932a5ce4eee0cbad20220f950fe7bd3534bcbc9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c74b84544dee27298a71715b3ce2c40d372b5a23", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
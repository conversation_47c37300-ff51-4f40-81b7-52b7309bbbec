{"cve_id": "CVE-2025-4297", "published_date": "2025-05-05T23:15:48.130", "last_modified_date": "2025-05-16T17:44:43.850", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Men Salon Management System 2.0. It has been classified as critical. This affects an unknown part of the file /admin/change-password.php. The manipulation leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. Multiple parameters might be affected."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Men Salon Management System 2.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/change-password.php. La manipulación provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Múltiples parámetros podrían verse afectados."}], "references": [{"url": "https://github.com/lierran1/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307401", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307401", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563548", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-46634", "published_date": "2025-05-01T20:15:39.460", "last_modified_date": "2025-05-27T14:18:00.567", "descriptions": [{"lang": "en", "value": "Cleartext transmission of sensitive information in the web management portal of the Tenda RX2 Pro *********** may allow an unauthenticated attacker to authenticate to the web management portal by collecting credentials from observed/collected traffic. It implements encryption, but not until after the user has transmitted the hash of their password in cleartext. The hash can be replayed to authenticate."}, {"lang": "es", "value": "La transmisión de información confidencial en texto plano en el portal de administración web del Tenda RX2 Pro *********** podría permitir que un atacante no autenticado se autentique en dicho portal recopilando credenciales del tráfico observado/recopilado. Implementa cifrado, pero solo después de que el usuario haya transmitido el hash de su contraseña en texto plano. El hash puede reproducirse para la autenticación."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46634-transmission-of-plaintext-credentials-in-httpd", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2022-49892", "published_date": "2025-05-01T15:16:14.210", "last_modified_date": "2025-05-07T13:19:41.120", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nftrace: Fix use-after-free for dynamic ftrace_ops\n\nKASAN reported a use-after-free with ftrace ops [1]. It was found from\nvmcore that perf had registered two ops with the same content\nsuccessively, both dynamic. After unregistering the second ops, a\nuse-after-free occurred.\n\nIn ftrace_shutdown(), when the second ops is unregistered, the\nFTRACE_UPDATE_CALLS command is not set because there is another enabled\nops with the same content.  Also, both ops are dynamic and the ftrace\ncallback function is ftrace_ops_list_func, so the\nFTRACE_UPDATE_TRACE_FUNC command will not be set. Eventually the value\nof 'command' will be 0 and ftrace_shutdown() will skip the rcu\nsynchronization.\n\nHowever, ftrace may be activated. When the ops is released, another CPU\nmay be accessing the ops.  Add the missing synchronization to fix this\nproblem.\n\n[1]\nBUG: KASAN: use-after-free in __ftrace_ops_list_func kernel/trace/ftrace.c:7020 [inline]\nBUG: KASAN: use-after-free in ftrace_ops_list_func+0x2b0/0x31c kernel/trace/ftrace.c:7049\nRead of size 8 at addr ffff56551965bbc8 by task syz-executor.2/14468\n\nCPU: 1 PID: 14468 Comm: syz-executor.2 Not tainted 5.10.0 #7\nHardware name: linux,dummy-virt (DT)\nCall trace:\n dump_backtrace+0x0/0x40c arch/arm64/kernel/stacktrace.c:132\n show_stack+0x30/0x40 arch/arm64/kernel/stacktrace.c:196\n __dump_stack lib/dump_stack.c:77 [inline]\n dump_stack+0x1b4/0x248 lib/dump_stack.c:118\n print_address_description.constprop.0+0x28/0x48c mm/kasan/report.c:387\n __kasan_report mm/kasan/report.c:547 [inline]\n kasan_report+0x118/0x210 mm/kasan/report.c:564\n check_memory_region_inline mm/kasan/generic.c:187 [inline]\n __asan_load8+0x98/0xc0 mm/kasan/generic.c:253\n __ftrace_ops_list_func kernel/trace/ftrace.c:7020 [inline]\n ftrace_ops_list_func+0x2b0/0x31c kernel/trace/ftrace.c:7049\n ftrace_graph_call+0x0/0x4\n __might_sleep+0x8/0x100 include/linux/perf_event.h:1170\n __might_fault mm/memory.c:5183 [inline]\n __might_fault+0x58/0x70 mm/memory.c:5171\n do_strncpy_from_user lib/strncpy_from_user.c:41 [inline]\n strncpy_from_user+0x1f4/0x4b0 lib/strncpy_from_user.c:139\n getname_flags+0xb0/0x31c fs/namei.c:149\n getname+0x2c/0x40 fs/namei.c:209\n [...]\n\nAllocated by task 14445:\n kasan_save_stack+0x24/0x50 mm/kasan/common.c:48\n kasan_set_track mm/kasan/common.c:56 [inline]\n __kasan_kmalloc mm/kasan/common.c:479 [inline]\n __kasan_kmalloc.constprop.0+0x110/0x13c mm/kasan/common.c:449\n kasan_kmalloc+0xc/0x14 mm/kasan/common.c:493\n kmem_cache_alloc_trace+0x440/0x924 mm/slub.c:2950\n kmalloc include/linux/slab.h:563 [inline]\n kzalloc include/linux/slab.h:675 [inline]\n perf_event_alloc.part.0+0xb4/0x1350 kernel/events/core.c:11230\n perf_event_alloc kernel/events/core.c:11733 [inline]\n __do_sys_perf_event_open kernel/events/core.c:11831 [inline]\n __se_sys_perf_event_open+0x550/0x15f4 kernel/events/core.c:11723\n __arm64_sys_perf_event_open+0x6c/0x80 kernel/events/core.c:11723\n [...]\n\nFreed by task 14445:\n kasan_save_stack+0x24/0x50 mm/kasan/common.c:48\n kasan_set_track+0x24/0x34 mm/kasan/common.c:56\n kasan_set_free_info+0x20/0x40 mm/kasan/generic.c:358\n __kasan_slab_free.part.0+0x11c/0x1b0 mm/kasan/common.c:437\n __kasan_slab_free mm/kasan/common.c:445 [inline]\n kasan_slab_free+0x2c/0x40 mm/kasan/common.c:446\n slab_free_hook mm/slub.c:1569 [inline]\n slab_free_freelist_hook mm/slub.c:1608 [inline]\n slab_free mm/slub.c:3179 [inline]\n kfree+0x12c/0xc10 mm/slub.c:4176\n perf_event_alloc.part.0+0xa0c/0x1350 kernel/events/core.c:11434\n perf_event_alloc kernel/events/core.c:11733 [inline]\n __do_sys_perf_event_open kernel/events/core.c:11831 [inline]\n __se_sys_perf_event_open+0x550/0x15f4 kernel/events/core.c:11723\n [...]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ftrace: Arreglo de use-after-free para ftrace_ops dinámicos KASAN informó de un use-after-free con operaciones ftrace [1]. Se encontró desde vmcore que perf había registrado dos operaciones con el mismo contenido sucesivamente, ambas dinámicas. Después de anular el registro de la segunda operación, se produjo un use-after-free. En ftrace_shutdown(), cuando se anula el registro de la segunda operación, el comando FTRACE_UPDATE_CALLS no se establece porque hay otra operación habilitada con el mismo contenido. Además, ambas operaciones son dinámicas y la función de devolución de llamada de ftrace es ftrace_ops_list_func, por lo que el comando FTRACE_UPDATE_TRACE_FUNC no se establecerá. Finalmente, el valor de 'command' será 0 y ftrace_shutdown() omitirá la sincronización de rcu. Sin embargo, ftrace puede estar activado. Cuando se libera la operación, otra CPU puede estar accediendo a ella. Agregue la sincronización faltante para solucionar este problema. [1] ERROR: KASAN: use-after-free en __ftrace_ops_list_func kernel/trace/ftrace.c:7020 [en línea] ERROR: KASAN: use-after-free en ftrace_ops_list_func+0x2b0/0x31c kernel/trace/ftrace.c:7049 Lectura de tamaño 8 en la dirección ffff56551965bbc8 por la tarea syz-executor.2/14468 CPU: 1 PID: 14468 Comm: syz-executor.2 No contaminado 5.10.0 #7 Nombre del hardware: linux,dummy-virt (DT) Rastreo de llamadas: dump_backtrace+0x0/0x40c arch/arm64/kernel/stacktrace.c:132 show_stack+0x30/0x40 arch/arm64/kernel/stacktrace.c:196 __dump_stack lib/dump_stack.c:77 [en línea] dump_stack+0x1b4/0x248 lib/dump_stack.c:118 print_address_description.constprop.0+0x28/0x48c mm/kasan/report.c:387 __kasan_report mm/kasan/report.c:547 [en línea] kasan_report+0x118/0x210 mm/kasan/report.c:564 check_memory_region_inline mm/kasan/generic.c:187 [en línea] __asan_load8+0x98/0xc0 mm/kasan/generic.c:253 __ftrace_ops_list_func kernel/trace/ftrace.c:7020 [en línea] ftrace_ops_list_func+0x2b0/0x31c kernel/trace/ftrace.c:7049 ftrace_graph_call+0x0/0x4 __might_sleep+0x8/0x100 include/linux/perf_event.h:1170 __might_fault mm/memory.c:5183 [en línea] __might_fault+0x58/0x70 mm/memory.c:5171 do_strncpy_from_user lib/strncpy_from_user.c:41 [en línea] strncpy_from_user+0x1f4/0x4b0 lib/strncpy_from_user.c:139 getname_flags+0xb0/0x31c fs/namei.c:149 getname+0x2c/0x40 fs/namei.c:209 [...] Asignado por la tarea 14445: kasan_save_stack+0x24/0x50 mm/kasan/common.c:48 kasan_set_track mm/kasan/common.c:56 [en línea] __kasan_kmalloc mm/kasan/common.c:479 [en línea] __kasan_kmalloc.constprop.0+0x110/0x13c mm/kasan/common.c:449 kasan_kmalloc+0xc/0x14 mm/kasan/common.c:493 kmem_cache_alloc_trace+0x440/0x924 mm/slub.c:2950 kmalloc include/linux/slab.h:563 [en línea] kzalloc include/linux/slab.h:675 [en línea] perf_event_alloc.part.0+0xb4/0x1350 kernel/events/core.c:11230 perf_event_alloc kernel/events/core.c:11733 [en línea] __do_sys_perf_event_open kernel/events/core.c:11831 [en línea] __se_sys_perf_event_open+0x550/0x15f4 kernel/events/core.c:11723 __arm64_sys_perf_event_open+0x6c/0x80 kernel/events/core.c:11723 [...] Liberado por la tarea 14445: kasan_save_stack+0x24/0x50 mm/kasan/common.c:48 kasan_set_track+0x24/0x34 mm/kasan/common.c:56 kasan_set_free_info+0x20/0x40 mm/kasan/generic.c:358 __kasan_slab_free.part.0+0x11c/0x1b0 mm/kasan/common.c:437 __kasan_slab_free mm/kasan/common.c:445 [en línea] kasan_slab_free+0x2c/0x40 mm/kasan/common.c:446 slab_free_hook mm/slub.c:1569 [en línea] slab_free_freelist_hook mm/slub.c:1608 [en línea] slab_free mm/slub.c:3179 [en línea] kfree+0x12c/0xc10 mm/slub.c:4176 perf_event_alloc.part.0+0xa0c/0x1350 kernel/events/core.c:11434 perf_event_alloc kernel/events/core.c:11733 [en línea] __do_sys_perf_event_open kernel/events/core.c:11831 [en línea] __se_sys_perf_event_open+0x550/0x15f4 kernel/events/core.c:11723 [...]"}], "references": [{"url": "https://git.kernel.org/stable/c/0e792b89e6800cd9cb4757a76a96f7ef3e8b6294", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/88561a66777e7a2fe06638c6dcb22a9fae0b6733", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cc1b9961a0ceb70f6ca4e2f4b8bb71c87c7a495c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ea5f2fd4640ecbb9df969bf8bb27733ae2183169", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
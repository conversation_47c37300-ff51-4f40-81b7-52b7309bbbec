{"cve_id": "CVE-2022-49920", "published_date": "2025-05-01T15:16:17.517", "last_modified_date": "2025-05-07T13:27:38.097", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetfilter: nf_tables: netlink notifier might race to release objects\n\ncommit release path is invoked via call_rcu and it runs lockless to\nrelease the objects after rcu grace period. The netlink notifier handler\nmight win race to remove objects that the transaction context is still\nreferencing from the commit release path.\n\nCall rcu_barrier() to ensure pending rcu callbacks run to completion\nif the list of transactions to be destroyed is not empty."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netfilter: nf_tables: el notificador netlink podría acelerar la liberación de objetos. La ruta de liberación de confirmación se invoca mediante call_rcu y se ejecuta sin bloqueo para liberar los objetos después del período de gracia de rcu. El controlador del notificador netlink podría ganar la ejecución para eliminar objetos de la ruta de liberación de confirmación a los que el contexto de la transacción aún hace referencia. Se debe llamar a rcu_barrier() para garantizar que las devoluciones de llamada rcu pendientes se ejecuten por completo si la lista de transacciones a destruir no está vacía."}], "references": [{"url": "https://git.kernel.org/stable/c/1ffe7100411a8b9015115ce124cd6c9c9da6f8e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d4bc8271db21ea9f1c86a1ca4d64999f184d4aae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e40b7c44d19e327ad8b49a491ef1fa8dcc4566e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
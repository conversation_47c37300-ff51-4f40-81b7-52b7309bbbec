{"cve_id": "CVE-2025-32971", "published_date": "2025-04-30T15:16:01.540", "last_modified_date": "2025-05-13T15:06:38.253", "descriptions": [{"lang": "en", "value": "XWiki is a generic wiki platform. In versions starting from 4.5.1 to before 15.10.13, from 16.0.0-rc-1 to before 16.4.4, and from 16.5.0-rc-1 to before 16.8.0-rc-1, the Solr script service doesn't take dropped programming rights into account. The Solr script service that is accessible in XWiki's scripting API normally requires programming rights to be called. Due to using the wrong API for checking rights, it doesn't take the fact into account that programming rights might have been dropped by calling `$xcontext.dropPermissions()`. If some code relies on this for the safety of executing Velocity code with the wrong author context, this could allow a user with script rights to either cause a high load by indexing documents or to temporarily remove documents from the search index. This issue has been patched in versions 15.10.13, 16.4.4, and 16.8.0-rc-1."}, {"lang": "es", "value": "XWiki es una plataforma wiki genérica. En las versiones desde la 4.5.1 hasta anteriores a la 15.10.13, desde la 16.0.0-rc-1 hasta anteriores a la 16.4.4, y desde la 16.5.0-rc-1 hasta anteriores a la 16.8.0-rc-1, el servicio de scripts de Solr no tiene en cuenta la pérdida de permisos de programación. El servicio de scripts de Solr, accesible a través de la API de scripts de XWiki, normalmente requiere la llamada a los permisos de programación. Debido al uso de una API incorrecta para la comprobación de permisos, no tiene en cuenta que los permisos de programación podrían haberse perdido al llamar a `$xcontext.dropPermissions()`. Si algún código depende de esto para la seguridad de ejecutar código de Velocity con un contexto de autor incorrecto, esto podría permitir que un usuario con permisos de script genere una alta carga al indexar documentos o los elimine temporalmente del índice de búsqueda. Este problema se ha solucionado en las versiones 15.10.13, 16.4.4 y 16.8.0-rc-1."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/6570f40f976aec82baf388b5239d1412cab238c9", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-987p-r3jc-8c8v", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-22474", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory", "Issue Tracking"]}]}
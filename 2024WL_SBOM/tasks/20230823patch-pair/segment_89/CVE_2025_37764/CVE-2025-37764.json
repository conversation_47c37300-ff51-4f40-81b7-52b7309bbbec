{"cve_id": "CVE-2025-37764", "published_date": "2025-05-01T14:15:39.150", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/imagination: fix firmware memory leaks\n\nFree the memory used to hold the results of firmware image processing\nwhen the module is unloaded.\n\nFix the related issue of the same memory being leaked if processing\nof the firmware image fails during module load.\n\nEnsure all firmware GEM objects are destroyed if firmware image\nprocessing fails.\n\nFixes memory leaks on powervr module unload detected by Kmemleak:\n\nunreferenced object 0xffff000042e20000 (size 94208):\n  comm \"modprobe\", pid 470, jiffies 4295277154\n  hex dump (first 32 bytes):\n    02 ae 7f ed bf 45 84 00 3c 5b 1f ed 9f 45 45 05  .....E..<[...EE.\n    d5 4f 5d 14 6c 00 3d 23 30 d0 3a 4a 66 0e 48 c8  .O].l.=#0.:Jf.<PERSON>.\n  backtrace (crc dd329dec):\n    kmemleak_alloc+0x30/0x40\n    ___kmalloc_large_node+0x140/0x188\n    __kmalloc_large_node_noprof+0x2c/0x13c\n    __kmalloc_noprof+0x48/0x4c0\n    pvr_fw_init+0xaa4/0x1f50 [powervr]\n\nunreferenced object 0xffff000042d20000 (size 20480):\n  comm \"modprobe\", pid 470, jiffies 4295277154\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 09 00 00 00 0b 00 00 00  ................\n    00 00 00 00 00 00 00 00 07 00 00 00 08 00 00 00  ................\n  backtrace (crc 395b02e3):\n    kmemleak_alloc+0x30/0x40\n    ___kmalloc_large_node+0x140/0x188\n    __kmalloc_large_node_noprof+0x2c/0x13c\n    __kmalloc_noprof+0x48/0x4c0\n    pvr_fw_init+0xb0c/0x1f50 [powervr]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/imagination: se corrigen fugas de memoria del firmware. Libera la memoria utilizada para almacenar los resultados del procesamiento de la imagen del firmware al descargar el módulo. Se soluciona el problema relacionado de la fuga de la misma memoria si el procesamiento de la imagen del firmware falla durante la carga del módulo. Se garantiza la destrucción de todos los objetos GEM del firmware si falla el procesamiento de la imagen. Se corrigen las fugas de memoria detectadas por Kmemleak al descargar el módulo powervr: objeto sin referencia 0xffff000042e20000 (tamaño 94208): comm \"modprobe\", pid 470, jiffies 4295277154 hex dump (first 32 bytes): 02 ae 7f ed bf 45 84 00 3c 5b 1f ed 9f 45 45 05 .....E..&lt;[...EE. d5 4f 5d 14 6c 00 3d 23 30 d0 3a 4a 66 0e 48 c8 .O].l.=#0.:Jf.<PERSON>. backtrace (crc dd329dec): kmemleak_alloc+0x30/0x40 ___kmalloc_large_node+0x140/0x188 __kmalloc_large_node_noprof+0x2c/0x13c __kmalloc_noprof+0x48/0x4c0 pvr_fw_init+0xaa4/0x1f50 [powervr] unreferenced object 0xffff000042d20000 (size 20480): comm \"modprobe\", pid 470, jiffies 4295277154 hex dump (first 32 bytes): 00 00 00 00 00 00 00 00 09 00 00 00 0b 00 00 00 ................ 00 00 00 00 00 00 00 00 07 00 00 00 08 00 00 00 ................ backtrace (crc 395b02e3): kmemleak_alloc+0x30/0x40 ___kmalloc_large_node+0x140/0x188 __kmalloc_large_node_noprof+0x2c/0x13c __kmalloc_noprof+0x48/0x4c0 pvr_fw_init+0xb0c/0x1f50 [powervr] "}], "references": [{"url": "https://git.kernel.org/stable/c/490c30fd554597e78f66650044877e7defb5f83c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/891c12ba855ccb34c06a2e5da75c644683087036", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a5b230e7f3a55bd8bd8d012eec75a4b7baa671d5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4282", "published_date": "2025-05-05T18:15:44.350", "last_modified_date": "2025-05-14T20:56:01.263", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in SourceCodester/oretnom23 Stock Management System 1.0 and classified as problematic. This vulnerability affects unknown code of the file /classes/Users.php?f=save. The manipulation leads to cross-site request forgery. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en SourceCodester/oretnom23 Stock Management System 1.0, clasificada como problemática. Esta vulnerabilidad afecta al código desconocido del archivo /classes/Users.php?f=save. La manipulación provoca cross-site request forgery. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/th3w0lf-1337/Vulnerabilities/blob/main/SMS-PHP/CSRF/info.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307390", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307390", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563102", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-44860", "published_date": "2025-05-01T18:15:56.537", "last_modified_date": "2025-05-21T19:47:22.580", "descriptions": [{"lang": "en", "value": "TOTOLINK CA300-POE V6.2c.884_B20180522 was found to contain a command injection vulnerability in the msg_process function via the Port parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA300-POE V6.2c.884_B20180522 contenía una vulnerabilidad de inyección de comandos en la función msg_process mediante el parámetro Port. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA300-POE/msg_process_Port/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
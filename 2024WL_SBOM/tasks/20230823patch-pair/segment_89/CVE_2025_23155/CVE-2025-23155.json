{"cve_id": "CVE-2025-23155", "published_date": "2025-05-01T13:15:51.413", "last_modified_date": "2025-07-06T10:15:23.670", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: stmmac: Fix accessing freed irq affinity_hint\n\nThe cpumask should not be a local variable, since its pointer is saved\nto irq_desc and may be accessed from procfs.\nTo fix it, use the persistent mask cpumask_of(cpu#)."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: stmmac: Se corrige el acceso a la IRQ liberada affinity_hint. La máscara cpu no debe ser una variable local, ya que su puntero se guarda en irq_desc y se puede acceder desde procfs. Para corregirla, utilice la máscara persistente cpumask_of(cpu#)."}], "references": [{"url": "https://git.kernel.org/stable/c/442312c2a90d60c7a5197246583fa91d9e579985", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e51a6a44e2c4de780a26e8fe110d708e806a8cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c60d101a226f18e9a8f01bb4c6ca2b47dfcb15ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e148266e104fce396ad624079a6812ac3a9982ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49857", "published_date": "2025-05-01T15:16:09.307", "last_modified_date": "2025-05-07T13:31:48.520", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: marvell: prestera: fix memory leak in prestera_rxtx_switch_init()\n\nWhen prestera_sdma_switch_init() failed, the memory pointed to by\nsw->rxtx isn't released. Fix it. Only be compiled, not be tested."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: marvell: prestera: se corrige una fuga de memoria en prestera_rxtx_switch_init(). Cuando prestera_sdma_switch_init() falla, la memoria a la que apunta sw-&gt;rxtx no se libera. Se debe corregir. Solo se compilará, no se probará."}], "references": [{"url": "https://git.kernel.org/stable/c/31e5084ac6876e52dbb0a1cc4fc18b6c79979f31", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/409731df6310a33f4d0a3ef594d2410cdcd637f2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/519b58bbfa825f042fcf80261cc18e1e35f85ffd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5333cf1b7f6861912aff6263978d4781f9858e47", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
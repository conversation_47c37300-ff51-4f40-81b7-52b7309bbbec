{"cve_id": "CVE-2025-4384", "published_date": "2025-05-06T16:15:32.340", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "The MQTT add-on of PcVue fails to verify that a remote device’s certificate has not already expired or has not yet become valid. This allows malicious devices to present certificates that are not rejected properly.\n\nThe use of a client certificate reduces the risk for random devices to take advantage of this flaw."}, {"lang": "es", "value": "El complemento MQTT de PcVue no verifica que el certificado de un dispositivo remoto no haya caducado o no sea válido. Esto permite que dispositivos maliciosos presenten certificados que no se rechazan correctamente. El uso de un certificado de cliente reduce el riesgo de que dispositivos aleatorios se aprovechen de esta vulnerabilidad."}], "references": [{"url": "https://www.pcvue.com/security/#SB2025-3", "source": "87c8e6ad-f0f5-4ca8-89e2-89f26d6ed932", "tags": []}]}
{"cve_id": "CVE-2022-49868", "published_date": "2025-05-01T15:16:11.733", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nphy: ralink: mt7621-pci: add sentinel to quirks table\n\nWith mt7621 soc_dev_attr fixed to register the soc as a device,\nkernel will experience an oops in soc_device_match_attr\n\nThis quirk test was introduced in the staging driver in\ncommit 9445ccb3714c (\"staging: mt7621-pci-phy: add quirks for 'E2'\nrevision using 'soc_device_attribute'\"). The staging driver was removed,\nand later re-added in commit d87da32372a0 (\"phy: ralink: Add PHY driver\nfor MT7621 PCIe PHY\") for kernel 5.11"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: phy: ralink: mt7621-pci: añadir centinela a la tabla de peculiaridades. Con la corrección de la variable mt7621 soc_dev_attr para registrar el SOC como dispositivo, el kernel experimentará un error en soc_device_match_attr. Esta prueba de peculiaridades se introdujo en el controlador de pruebas en el commit 9445ccb3714c (\"staging: mt7621-pci-phy: añadir peculiaridades para la revisión 'E2' mediante 'soc_device_attribute'\"). El controlador de pruebas se eliminó y se volvió a añadir posteriormente en el commit d87da32372a0 (\"phy: ralink: Añadir controlador PHY para MT7621 PCIe PHY\") para el kernel 5.11."}], "references": [{"url": "https://git.kernel.org/stable/c/500bcd3a99eae84412067c3b9e7ffba1c66e6383", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/819b885cd886c193782891c4f51bbcab3de119a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d539cfd1202d66c2dcea383f1d96835ae72d5809", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
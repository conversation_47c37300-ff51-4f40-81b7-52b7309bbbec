{"cve_id": "CVE-2025-46342", "published_date": "2025-04-30T15:16:02.440", "last_modified_date": "2025-05-16T16:42:35.460", "descriptions": [{"lang": "en", "value": "Kyverno is a policy engine designed for cloud native platform engineering teams. Prior to versions 1.13.5 and 1.14.0, it may happen that policy rules using namespace selector(s) in their match statements are mistakenly not applied during admission review request processing due to a missing error propagation in function `GetNamespaceSelectorsFromNamespaceLister` in `pkg/utils/engine/labels.go`. As a consequence, security-critical mutations and validations are bypassed, potentially allowing attackers with K8s API access to perform malicious operations. This issue has been patched in versions 1.13.5 and 1.14.0."}, {"lang": "es", "value": "Kyverno es un motor de políticas diseñado para equipos de ingeniería de plataformas nativas de la nube. En versiones anteriores a la 1.13.5 y la 1.14.0, podía ocurrir que las reglas de políticas que usaban selectores de espacios de nombres en sus declaraciones de coincidencia no se aplicaran por error durante el procesamiento de solicitudes de revisión de admisión debido a la falta de propagación de errores en la función `GetNamespaceSelectorsFromNamespaceLister` en `pkg/utils/engine/labels.go`. Como consecuencia, se omiten las mutaciones y validaciones críticas para la seguridad, lo que podría permitir que atacantes con acceso a la API de K8 realicen operaciones maliciosas. Este problema se ha corregido en las versiones 1.13.5 y 1.14.0."}], "references": [{"url": "https://github.com/kyverno/kyverno/commit/3ff923b7756e1681daf73849954bd88516589194", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/kyverno/kyverno/security/advisories/GHSA-jrr2-x33p-6hvc", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
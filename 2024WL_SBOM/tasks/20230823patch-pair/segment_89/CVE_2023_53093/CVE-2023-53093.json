{"cve_id": "CVE-2023-53093", "published_date": "2025-05-02T16:15:28.270", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntracing: Do not let histogram values have some modifiers\n\nHistogram values can not be strings, stacktraces, graphs, symbols,\nsyscalls, or grouped in buckets or log. Give an error if a value is set to\ndo so.\n\nNote, the histogram code was not prepared to handle these modifiers for\nhistograms and caused a bug.\n\n<PERSON> reported:\n\n # echo 'p:copy_to_user __arch_copy_to_user n=$arg2' >> /sys/kernel/tracing/kprobe_events\n # echo 'hist:keys=n:vals=hitcount.buckets=8:sort=hitcount' > /sys/kernel/tracing/events/kprobes/copy_to_user/trigger\n # cat /sys/kernel/tracing/events/kprobes/copy_to_user/hist\n[  143.694628] Unable to handle kernel NULL pointer dereference at virtual address 0000000000000000\n[  143.695190] Mem abort info:\n[  143.695362]   ESR = 0x0000000096000004\n[  143.695604]   EC = 0x25: DABT (current EL), IL = 32 bits\n[  143.695889]   SET = 0, FnV = 0\n[  143.696077]   EA = 0, S1PTW = 0\n[  143.696302]   FSC = 0x04: level 0 translation fault\n[  143.702381] Data abort info:\n[  143.702614]   ISV = 0, ISS = 0x00000004\n[  143.702832]   CM = 0, WnR = 0\n[  143.703087] user pgtable: 4k pages, 48-bit VAs, pgdp=00000000448f9000\n[  143.703407] [0000000000000000] pgd=0000000000000000, p4d=0000000000000000\n[  143.704137] Internal error: Oops: 0000000096000004 [#1] PREEMPT SMP\n[  143.704714] Modules linked in:\n[  143.705273] CPU: 0 PID: 133 Comm: cat Not tainted 6.2.0-00003-g6fc512c10a7c #3\n[  143.706138] Hardware name: linux,dummy-virt (DT)\n[  143.706723] pstate: 80000005 (Nzcv daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--)\n[  143.707120] pc : hist_field_name.part.0+0x14/0x140\n[  143.707504] lr : hist_field_name.part.0+0x104/0x140\n[  143.707774] sp : ffff800008333a30\n[  143.707952] x29: ffff800008333a30 x28: 0000000000000001 x27: 0000000000400cc0\n[  143.708429] x26: ffffd7a653b20260 x25: 0000000000000000 x24: ffff10d303ee5800\n[  143.708776] x23: ffffd7a6539b27b0 x22: ffff10d303fb8c00 x21: 0000000000000001\n[  143.709127] x20: ffff10d303ec2000 x19: 0000000000000000 x18: 0000000000000000\n[  143.709478] x17: 0000000000000000 x16: 0000000000000000 x15: 0000000000000000\n[  143.709824] x14: 0000000000000000 x13: 203a6f666e692072 x12: 6567676972742023\n[  143.710179] x11: 0a230a6d6172676f x10: 000000000000002c x9 : ffffd7a6521e018c\n[  143.710584] x8 : 000000000000002c x7 : 7f7f7f7f7f7f7f7f x6 : 000000000000002c\n[  143.710915] x5 : ffff10d303b0103e x4 : ffffd7a653b20261 x3 : 000000000000003d\n[  143.711239] x2 : 0000000000020001 x1 : 0000000000000001 x0 : 0000000000000000\n[  143.711746] Call trace:\n[  143.712115]  hist_field_name.part.0+0x14/0x140\n[  143.712642]  hist_field_name.part.0+0x104/0x140\n[  143.712925]  hist_field_print+0x28/0x140\n[  143.713125]  event_hist_trigger_print+0x174/0x4d0\n[  143.713348]  hist_show+0xf8/0x980\n[  143.713521]  seq_read_iter+0x1bc/0x4b0\n[  143.713711]  seq_read+0x8c/0xc4\n[  143.713876]  vfs_read+0xc8/0x2a4\n[  143.714043]  ksys_read+0x70/0xfc\n[  143.714218]  __arm64_sys_read+0x24/0x30\n[  143.714400]  invoke_syscall+0x50/0x120\n[  143.714587]  el0_svc_common.constprop.0+0x4c/0x100\n[  143.714807]  do_el0_svc+0x44/0xd0\n[  143.714970]  el0_svc+0x2c/0x84\n[  143.715134]  el0t_64_sync_handler+0xbc/0x140\n[  143.715334]  el0t_64_sync+0x190/0x194\n[  143.715742] Code: a9bd7bfd 910003fd a90153f3 aa0003f3 (f9400000)\n[  143.716510] ---[ end trace 0000000000000000 ]---\nSegmentation fault"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rastreo: No permitir que los valores del histograma tengan modificadores. Los valores del histograma no pueden ser cadenas, seguimientos de pila, gr<PERSON><PERSON>os, s<PERSON><PERSON><PERSON>, llamadas al sistema ni agruparse en contenedores o registros. Se genera un error si se configura un valor para ello. Tenga en cuenta que el código del histograma no estaba preparado para manejar estos modificadores, lo que provocó un error. Mark Rutland informó: # echo 'p:copy_to_user __arch_copy_to_user n=$arg2' &gt;&gt; /sys/kernel/tracing/kprobe_events # echo 'hist:keys=n:vals=hitcount.buckets=8:sort=hitcount' &gt; /sys/kernel/tracing/events/kprobes/copy_to_user/trigger # cat /sys/kernel/tracing/events/kprobes/copy_to_user/hist [ 143.694628] No se puede manejar la desreferencia del puntero NULL del kernel en la dirección virtual 0000000000000000 [ 143.695190] Información de aborto de memoria: [ 143.695362] ESR = 0x0000000096000004 [ 143.695604] EC = 0x25: DABT (EL actual), IL = 32 bits [ 143.695889] SET = 0, FnV = 0 [ 143.696077] EA = 0, S1PTW = 0 [ 143.696302] FSC = 0x04: fallo de traducción de nivel 0 [ 143.702381] Información de cancelación de datos: [ 143.702614] ISV = 0, ISS = 0x00000004 [ 143.702832] CM = 0, WnR = 0 [ 143.703087] pgtable de usuario: páginas de 4k, VA de 48 bits, pgdp=00000000448f9000 [ 143.703407] [0000000000000000] pgd=0000000000000000, p4d=0000000000000000 [ 143.704137] Error interno: Oops: 0000000096000004 [#1] PREEMPT SMP [ 143.704714] Módulos vinculados: [ 143.705273] CPU: 0 PID: 133 Comm: cat No contaminado 6.2.0-00003-g6fc512c10a7c #3 [ 143.706138] Nombre del hardware: linux,dummy-virt (DT) [ 143.706723] pstate: 80000005 (Nzcv daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--) [ 143.707120] pc : nombre_campo_hist.parte.0+0x14/0x140 [ 143.707504] lr : nombre_campo_hist.parte.0+0x104/0x140 [ 143.707774] sp : ffff800008333a30 [ 143.707952] x29: ffff800008333a30 x28: 0000000000000001 x27: 0000000000400cc0 [ 143.708429] x26: ffffd7a653b20260 x25: 0000000000000000 x24: ffff10d303ee5800 [ 143.708776] x23: ffffd7a6539b27b0 x22: ffff10d303fb8c00 x21: 0000000000000001 [ 143.709127] x20: ffff10d303ec2000 x19: 0000000000000000 x18: 0000000000000000 [ 143.709478] x17: 000000000000000 x16: 0000000000000000 x15: 0000000000000000 [ 143.709824] x14: 0000000000000000 x13: 203a6f666e692072 x12: 6567676972742023 [ 143.710179] x11: 0a230a6d6172676f x10: 000000000000002c x9: ffffd7a6521e018c [143.710584] x8: 000000000000002c x7: 7f7f7f7f7f7f7f7f x6: 000000000000002c [ 143.710915] x5 : ffff10d303b0103e x4 : ffffd7a653b20261 x3 : 000000000000003d [ 143.711239] x2 : 0000000000020001 x1 : 0000000000000001 x0 : 0000000000000000 [ 143.711746] Rastreo de llamadas:[ 143.712115] hist_field_name.part.0+0x14/0x140 [ 143.712642] hist_field_name.part.0+0x104/0x140 [ 143.712925] hist_field_print+0x28/0x140 [ 143.713125] event_hist_trigger_print+0x174/0x4d0 [ 143.713348] hist_show+0xf8/0x980 [ 143.713521] seq_read_iter+0x1bc/0x4b0 [ 143.713711] seq_read+0x8c/0xc4 [ 143.713876] vfs_read+0xc8/0x2a4 [ 143.714043] ksys_read+0x70/0xfc [ 143.714218] __arm64_sys_read+0x24/0x30 [ 143.714400] invoke_syscall+0x50/0x120 [ 143.714587] el0_svc_common.constprop.0+0x4c/0x100 [ 143.714807] do_el0_svc+0x44/0xd0 [ 143.714970] el0_svc+0x2c/0x84 [ 143.715134] el0t_64_sync_handler+0xbc/0x140 [ 143.715334] el0t_64_sync+0x190/0x194 [ 143.715742] Code: a9bd7bfd 910003fd a90153f3 aa0003f3 (f9400000) [ 143.716510]--- Fallo de segmentación"}], "references": [{"url": "https://git.kernel.org/stable/c/2fc0ee435c9264cdb7c5e872f76cd9bb97640227", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39cd75f2f3a43c0e2f95749eb6dd6420c553f87d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e0213434fe3e4a0d118923dc98d31e7ff1cd9e45", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
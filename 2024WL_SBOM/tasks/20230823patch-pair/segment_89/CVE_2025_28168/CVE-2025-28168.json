{"cve_id": "CVE-2025-28168", "published_date": "2025-05-05T14:15:28.500", "last_modified_date": "2025-06-17T14:15:20.720", "descriptions": [{"lang": "en", "value": "The Multiple File Upload add-on component 3.1.0 for OutSystems is vulnerable to Unrestricted File Upload. This occurs because file extension and size validations are enforced solely on the client side. An attacker can intercept the upload request and modify a parameter to bypass extension restrictions and upload arbitrary files. NOTE: this is a third-party component that is not supplied or supported by OutSystems."}, {"lang": "es", "value": "La versión anterior a la 3.1.0 de Outsystems Multiple File Upload es vulnerable a la carga de archivos sin restricciones. Esta vulnerabilidad se debe a que las validaciones de extensión y tamaño de archivo se aplican únicamente en el lado del cliente. Un atacante puede interceptar la solicitud de carga y modificar el parámetro para eludir las restricciones de extensión y cargar archivos arbitrarios."}], "references": [{"url": "https://gist.github.com/IamLeandrooooo/01090be3023f5e7c7397bb9b1f5505b9", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.outsystems.com/forge/component-overview/200/multiple-file-upload-o11", "source": "<EMAIL>", "tags": ["Product"]}]}
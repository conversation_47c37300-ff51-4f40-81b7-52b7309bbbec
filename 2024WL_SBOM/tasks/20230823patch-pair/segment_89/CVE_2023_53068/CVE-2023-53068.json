{"cve_id": "CVE-2023-53068", "published_date": "2025-05-02T16:15:25.870", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: usb: lan78xx: Limit packet length to skb->len\n\nPacket length retrieved from descriptor may be larger than\nthe actual socket buffer length. In such case the cloned\nskb passed up the network stack will leak kernel memory contents.\n\nAdditionally prevent integer underflow when size is less than\nETH_FCS_LEN."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: usb: lan78xx: Limitar la longitud del paquete a skb-&gt;len. La longitud del paquete obtenida del descriptor puede ser mayor que la longitud real del búfer del socket. En tal caso, el skb clonado que se pasa a la pila de red filtrará el contenido de la memoria del kernel. Además, se evita el subdesbordamiento de enteros cuando el tamaño es menor que ETH_FCS_LEN."}], "references": [{"url": "https://git.kernel.org/stable/c/44b9ed73369fc5ec85dd2ee487e986301792a82d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f247f5a2c18b3f21206cdd51193df4f38e1b9f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/83de34967473ed31d276381373713cc2869a42e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
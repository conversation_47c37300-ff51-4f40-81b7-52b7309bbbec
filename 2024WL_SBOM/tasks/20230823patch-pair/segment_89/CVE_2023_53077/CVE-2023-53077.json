{"cve_id": "CVE-2023-53077", "published_date": "2025-05-02T16:15:26.720", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/display: fix shift-out-of-bounds in CalculateVMAndRowBytes\n\n[WHY]\nWhen PTEBufferSizeInRequests is zero, UBSAN reports the following\nwarning because dml_log2 returns an unexpected negative value:\n\n  shift exponent 4294966273 is too large for 32-bit type 'int'\n\n[HOW]\n\nIn the case PTEBufferSizeInRequests is zero, skip the dml_log2() and\nassign the result directly."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/display: corregir desplazamiento fuera de los límites en CalculateVMAndRowBytes [POR QUÉ] Cuando PTEBufferSizeInRequests es cero, UBSAN informa la siguiente advertencia porque dml_log2 devuelve un valor negativo inesperado: el exponente de desplazamiento 4294966273 es demasiado grande para el tipo de 32 bits 'int' [CÓMO] En el caso de que PTEBufferSizeInRequests sea cero, omita dml_log2() y asigne el resultado directamente."}], "references": [{"url": "https://git.kernel.org/stable/c/031f196d1b1b6d5dfcb0533b431e3ab1750e6189", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7257070be70e19a9138f39009c1a26c83a8a7cfa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a16394b5d661afec9a264fecac3abd87aea439ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bec1bea2fa974e63f6059c33edde669c7894d0bc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e12b95680821b9880cd9992c0f3555389363604f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
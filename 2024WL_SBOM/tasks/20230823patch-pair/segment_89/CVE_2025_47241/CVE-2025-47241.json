{"cve_id": "CVE-2025-47241", "published_date": "2025-05-03T21:15:48.023", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In browser-use (aka Browser Use) before 0.1.45, URL parsing of allowed_domains is mishandled because userinfo can be placed in the authority component."}, {"lang": "es", "value": "En browser-use (también conocido como Browser Use) anterior a la versión 0.1.45, el análisis de URL de allowed_domains se gestiona incorrectamente porque la información del usuario se puede colocar en el componente de autoridad."}], "references": [{"url": "https://github.com/browser-use/browser-use/pull/1561", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/browser-use/browser-use/releases/tag/0.1.45", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/browser-use/browser-use/security/advisories/GHSA-x39x-9qw5-ghrf", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37783", "published_date": "2025-05-01T14:15:42.593", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/msm/dpu: Fix error pointers in dpu_plane_virtual_atomic_check\n\nThe function dpu_plane_virtual_atomic_check was dereferencing pointers\nreturned by drm_atomic_get_plane_state without checking for errors. This\ncould lead to undefined behavior if the function returns an error pointer.\n\nThis commit adds checks using IS_ERR to ensure that plane_state is\nvalid before dereferencing them.\n\nSimilar to commit da29abe71e16\n(\"drm/amd/display: Fix error pointers in amdgpu_dm_crtc_mem_type_changed\").\n\nPatchwork: https://patchwork.freedesktop.org/patch/643132/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/msm/dpu: Corrección de punteros de error en dpu_plane_virtual_atomic_check. La función dpu_plane_virtual_atomic_check desreferenciaba los punteros devueltos por drm_atomic_get_plane_state sin comprobar si había errores. Esto podría provocar un comportamiento indefinido si la función devuelve un puntero de error. Esta confirmación añade comprobaciones mediante IS_ERR para garantizar que plane_state sea válido antes de desreferenciarlo. Similar al commit da29abe71e16 (\"drm/amd/display: Corrección de punteros de error en amdgpu_dm_crtc_mem_type_changed\"). Patchwork: https://patchwork.freedesktop.org/patch/643132/"}], "references": [{"url": "https://git.kernel.org/stable/c/5cb1b130e1cd04239cc9c26a98279f4660dce583", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a9670ed1cce3216778c89936d3ae91cf0d436035", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37747", "published_date": "2025-05-01T13:15:53.417", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nperf: Fix hang while freeing sigtrap event\n\nPer<PERSON> can hang while freeing a sigtrap event if a related deferred\nsignal hadn't managed to be sent before the file got closed:\n\nperf_event_overflow()\n   task_work_add(perf_pending_task)\n\nfput()\n   task_work_add(____fput())\n\ntask_work_run()\n    ____fput()\n        perf_release()\n            perf_event_release_kernel()\n                _free_event()\n                    perf_pending_task_sync()\n                        task_work_cancel() -> FAILED\n                        rcuwait_wait_event()\n\nOnce task_work_run() is running, the list of pending callbacks is\nremoved from the task_struct and from this point on task_work_cancel()\ncan't remove any pending and not yet started work items, hence the\ntask_work_cancel() failure and the hang on rcuwait_wait_event().\n\nTask work could be changed to remove one work at a time, so a work\nrunning on the current task can always cancel a pending one, however\nthe wait / wake design is still subject to inverted dependencies when\nremote targets are involved, as pictured by Oleg:\n\nT1                                                      T2\n\nfd = perf_event_open(pid => T2->pid);                  fd = perf_event_open(pid => T1->pid);\nclose(fd)                                              close(fd)\n    <IRQ>                                                  <IRQ>\n    perf_event_overflow()                                  perf_event_overflow()\n       task_work_add(perf_pending_task)                        task_work_add(perf_pending_task)\n    </IRQ>                                                 </IRQ>\n    fput()                                                 fput()\n        task_work_add(____fput())                              task_work_add(____fput())\n\n    task_work_run()                                        task_work_run()\n        ____fput()                                             ____fput()\n            perf_release()                                         perf_release()\n                perf_event_release_kernel()                            perf_event_release_kernel()\n                    _free_event()                                          _free_event()\n                        perf_pending_task_sync()                               perf_pending_task_sync()\n                            rcuwait_wait_event()                                   rcuwait_wait_event()\n\nTherefore the only option left is to acquire the event reference count\nupon queueing the perf task work and release it from the task work, just\nlike it was done before 3a5465418f5f (\"perf: Fix event leak upon exec and file release\")\nbut without the leaks it fixed.\n\nSome adjustments are necessary to make it work:\n\n* A child event might dereference its parent upon freeing. Care must be\n  taken to release the parent last.\n\n* Some places assuming the event doesn't have any reference held and\n  therefore can be freed right away must instead put the reference and\n  let the reference counting to its job."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: perf: Se soluciona el bloqueo al liberar el evento sigtrap Perf puede bloquearse al liberar un evento sigtrap si no se ha logrado enviar una señal diferida relacionada antes de que se cerrara el archivo: perf_event_overflow() task_work_add(perf_pending_task) fput() task_work_add(____fput()) task_work_run() ____fput() perf_release() perf_event_release_kernel() _free_event() perf_pending_task_sync() task_work_cancel() -&gt; FAILED rcuwait_wait_event() Una vez que task_work_run() se está ejecutando, la lista de devoluciones de llamadas pendientes se elimina de task_struct y desde este punto, task_work_cancel() no puede eliminar ningún elemento de trabajo pendiente y aún no iniciado, de ahí el error de task_work_cancel() y el bloqueo de rcuwait_wait_event(). El trabajo de la tarea se puede cambiar para eliminar un trabajo a la vez, de modo que un trabajo que se ejecuta en la tarea actual siempre puede cancelar uno pendiente, sin embargo, el diseño de espera/activación aún está sujeto a dependencias invertidas cuando se involucran objetivos remotos, como lo ilustra Oleg: T1 T2 fd = perf_event_open(pid =&gt; T2-&gt;pid); fd = perf_event_open(pid =&gt; T1-&gt;pid); close(fd) close(fd)   perf_event_overflow() perf_event_overflow() task_work_add(perf_pending_task) task_work_add(perf_pending_task)   fput() fput() task_work_add(____fput()) task_work_add(____fput()) task_work_run() task_work_run() ____fput() ____fput() perf_release() perf_release() perf_event_release_kernel() perf_event_release_kernel() _free_event() _free_event() perf_pending_task_sync() perf_pending_task_sync() rcuwait_wait_event() rcuwait_wait_event() Por lo tanto, la única opción que queda es adquirir el recuento de referencias de evento al poner en cola el trabajo de la tarea de rendimiento y liberarlo del trabajo de la tarea. Tal como se hizo antes de 3a5465418f5f (\"perf: Corregir fuga de eventos al ejecutar y liberar archivos\"), pero sin las fugas corregidas. Se requieren algunos ajustes para que funcione: * Un evento secundario podría desreferenciar a su padre al liberarse. Se debe tener cuidado de liberar al padre al final. * Algunos sitios, al asumir que el evento no tiene ninguna referencia retenida y, por lo tanto, puede liberarse de inmediato, deben colocar la referencia y dejar que el recuento de referencias continúe."}], "references": [{"url": "https://git.kernel.org/stable/c/1267bd38f161c1a27d9b722de017027167a225a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/56799bc035658738f362acec3e7647bb84e68933", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/665b87b8f8b3aeb49083ef3b65c4953e7753fc12", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fa1827fa968c0674e9b6fca223fa9fb4da4493eb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
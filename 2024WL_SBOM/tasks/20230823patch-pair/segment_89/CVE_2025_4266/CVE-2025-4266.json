{"cve_id": "CVE-2025-4266", "published_date": "2025-05-05T06:15:31.723", "last_modified_date": "2025-05-07T16:37:54.360", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Notice Board System 1.0. Affected by this issue is some unknown functionality of the file /bwdates-reports-details.php?vid=2. The manipulation of the argument fromdate/tomdate leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Notice Board System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /bwdates-reports-details.php?vid=2. La manipulación del argumento fromdate/tomdate provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bottlekv/CVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307370", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307370", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563148", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-4168", "published_date": "2025-05-03T03:15:28.350", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The Subpage List plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'subpages' shortcode in all versions up to, and including, 1.3.3 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Subpage List para WordPress es vulnerable a Cross-Site Scripting almacenado a través del shortcode \"subpages\" del complemento en todas las versiones hasta la 1.3.3 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en las páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/subpage-view/trunk/inc/class-subpage-list-shortcode.php#L25", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/aca48ddf-4256-4a55-bff5-1718110147dd?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-4163", "published_date": "2025-05-01T12:15:18.027", "last_modified_date": "2025-05-16T17:47:32.577", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Land Record System 1.0. This issue affects some unknown processing of the file /admin/aboutus.php. The manipulation of the argument pagetitle leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad, clasificada como crítica, en PHPGurukul Land Record System 1.0. Este problema afecta a un procesamiento desconocido del archivo /admin/aboutus.php. La manipulación del argumento pagetitle provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/10", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306695", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306695", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560883", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
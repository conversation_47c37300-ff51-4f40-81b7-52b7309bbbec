{"cve_id": "CVE-2023-53085", "published_date": "2025-05-02T16:15:27.493", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/edid: fix info leak when failing to get panel id\n\nMake sure to clear the transfer buffer before fetching the EDID to\navoid leaking slab data to the logs on errors that leave the buffer\nunchanged."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/edid: corrige pérdida de información cuando no se puede obtener el ID del panel. Asegúrese de borrar el búfer de transferencia antes de obtener el EDID para evitar filtrar datos de la losa a los registros en errores que dejan el búfer sin cambios."}], "references": [{"url": "https://git.kernel.org/stable/c/4d8457fe0eb9c80ff7795cf8a30962128b71d853", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/598c42c78919117dc0d235ae22d17ad642377483", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
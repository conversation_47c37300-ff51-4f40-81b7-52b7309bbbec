{"cve_id": "CVE-2024-11390", "published_date": "2025-05-01T14:15:34.913", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "Unrestricted upload of a file with dangerous type in Kibana can lead to arbitrary JavaScript execution in a victim’s browser (XSS) via crafted HTML and JavaScript files.\n\nThe attacker must have access to the Synthetics app AND/OR have access to write to the synthetics indices."}, {"lang": "es", "value": "La carga sin restricciones de un archivo con un tipo peligroso en Kibana puede provocar la ejecución arbitraria de JavaScript en el navegador de la víctima (XSS) mediante archivos HTML y JavaScript manipulados. El atacante debe tener acceso a la aplicación Synthetics y/o acceso para escribir en los índices de Synthetics."}], "references": [{"url": "https://discuss.elastic.co/t/kibana-7-17-24-and-8-12-0-security-update-esa-2024-20/377712", "source": "<EMAIL>", "tags": []}]}
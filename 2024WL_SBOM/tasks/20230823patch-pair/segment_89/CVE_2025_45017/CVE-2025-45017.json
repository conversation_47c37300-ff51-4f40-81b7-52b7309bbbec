{"cve_id": "CVE-2025-45017", "published_date": "2025-04-30T14:15:29.427", "last_modified_date": "2025-05-09T13:44:30.123", "descriptions": [{"lang": "en", "value": "A SQL injection vulnerability was discovered in edit-ticket.php of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary code via the tprice POST request parameter."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección SQL en edit-ticket.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario mediante el parámetro de solicitud POST tprice."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/SQL/SQL_injection_in_edit_ticket.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
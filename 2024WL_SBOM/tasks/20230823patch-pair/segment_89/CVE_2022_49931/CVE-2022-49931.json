{"cve_id": "CVE-2022-49931", "published_date": "2025-05-01T15:16:19.087", "last_modified_date": "2025-05-07T13:29:02.710", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nIB/hfi1: Correctly move list in sc_disable()\n\nCommit 13bac861952a (\"IB/hfi1: Fix abba locking issue with sc_disable()\")\nincorrectly tries to move a list from one list head to another.  The\nresult is a kernel crash.\n\nThe crash is triggered when a link goes down and there are waiters for a\nsend to complete.  The following signature is seen:\n\n  BUG: kernel NULL pointer dereference, address: 0000000000000030\n  [...]\n  Call Trace:\n   sc_disable+0x1ba/0x240 [hfi1]\n   pio_freeze+0x3d/0x60 [hfi1]\n   handle_freeze+0x27/0x1b0 [hfi1]\n   process_one_work+0x1b0/0x380\n   ? process_one_work+0x380/0x380\n   worker_thread+0x30/0x360\n   ? process_one_work+0x380/0x380\n   kthread+0xd7/0x100\n   ? kthread_complete_and_exit+0x20/0x20\n   ret_from_fork+0x1f/0x30\n\nThe fix is to use the correct call to move the list."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: IB/hfi1: Mover correctamente la lista en sc_disable(). El commit 13bac861952a (\"IB/hfi1: Solucionar el problema de bloqueo de abba con sc_disable()\") intenta mover incorrectamente una lista de una cabecera a otra. Esto provoca un fallo del kernel. El fallo se activa cuando un enlace se cae y hay esperas para que se complete un envío. Se observa la siguiente firma: ERROR: desreferencia de puntero nulo del kernel, dirección: 000000000000030 [...] Seguimiento de llamadas:  sc_disable+0x1ba/0x240 [hfi1] pio_freeze+0x3d/0x60 [hfi1] handle_freeze+0x27/0x1b0 [hfi1] process_one_work+0x1b0/0x380 ? process_one_work+0x380/0x380 worker_thread+0x30/0x360 ? process_one_work+0x380/0x380 kthread+0xd7/0x100 ? kthread_complete_and_exit+0x20/0x20 ret_from_fork+0x1f/0x30 La solución es utilizar la llamada correcta para mover la lista."}], "references": [{"url": "https://git.kernel.org/stable/c/1afac08b39d85437187bb2a92d89a741b1078f55", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/25760a41e3802f54aadcc31385543665ab349b8e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7c4260f8f188df32414a5ecad63e8b934c2aa3f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b8bcff99b07cc175a6ee12a52db51cdd2229586c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ba95409d6b580501ff6d78efd00064f7df669926", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
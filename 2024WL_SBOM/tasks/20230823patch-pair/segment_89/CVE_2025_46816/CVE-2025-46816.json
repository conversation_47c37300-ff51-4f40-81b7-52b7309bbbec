{"cve_id": "CVE-2025-46816", "published_date": "2025-05-06T19:16:00.080", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "goshs is a SimpleHTTPServer written in Go. Starting in version 0.3.4 and prior to version 1.0.5, running goshs without arguments makes it possible for anyone to execute commands on the server. The function `dispatchReadPump` does not checks the option cli `-c`, thus allowing anyone to execute arbitrary command through the use of websockets. Version 1.0.5 fixes the issue."}, {"lang": "es", "value": "goshs es un SimpleHTTPServer escrito en Go. A partir de la versión 0.3.4 y anteriores a la 1.0.5, ejecutar goshs sin argumentos permite que cualquiera pueda ejecutar comandos en el servidor. La función `dispatchReadPump` no verifica la opción `-c` de la CLI, lo que permite que cualquiera ejecute cualquier comando mediante websockets. La versión 1.0.5 corrige este problema."}], "references": [{"url": "https://github.com/patrickhener/goshs/commit/160220974576afe5111485b8d12fd36058984cfa", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/patrickhener/goshs/security/advisories/GHSA-rwj2-w85g-5cmm", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-44842", "published_date": "2025-05-01T17:15:50.467", "last_modified_date": "2025-05-22T15:30:58.983", "descriptions": [{"lang": "en", "value": "TOTOLINK CA600-PoE V5.3c.6665_B20180820 was found to contain a command injection vulnerability in the msg_process function via the Port parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA600-PoE V5.3c.6665_B20180820 contenía una vulnerabilidad de inyección de comandos en la función msg_process mediante el parámetro Port. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA600-PoE/msg_process_Port/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
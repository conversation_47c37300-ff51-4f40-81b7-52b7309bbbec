{"cve_id": "CVE-2024-13860", "published_date": "2025-05-02T07:15:51.220", "last_modified_date": "2025-05-09T18:16:02.803", "descriptions": [{"lang": "en", "value": "The Buddyboss Platform plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘bbp_topic_title’ parameter in all versions up to, and including, 2.8.50 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. The vulnerability was partially patched in version 2.8.41."}, {"lang": "es", "value": "El complemento Buddyboss Platform para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del parámetro 'bbp_topic_title' en todas las versiones hasta la 2.8.50 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada. La vulnerabilidad se corrigió parcialmente en la versión 2.8.41."}], "references": [{"url": "https://www.buddyboss.com/platform/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.buddyboss.com/resources/buddyboss-platform-releases/2-8-51/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a0ac8a41-553e-473b-82a7-226de17e472d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
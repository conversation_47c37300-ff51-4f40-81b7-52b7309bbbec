{"cve_id": "CVE-2022-49864", "published_date": "2025-05-01T15:16:11.313", "last_modified_date": "2025-05-07T13:22:34.333", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amdkfd: Fix NULL pointer dereference in svm_migrate_to_ram()\n\n./drivers/gpu/drm/amd/amdkfd/kfd_migrate.c:985:58-62: ERROR: p is NULL but dereferenced."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amdkfd: Se corrige la desreferencia del puntero NULL en svm_migrate_to_ram() ./drivers/gpu/drm/amd/amdkfd/kfd_migrate.c:985:58-62: ERROR: p es NULL pero está desreferenciado."}], "references": [{"url": "https://git.kernel.org/stable/c/3c1bb6187e566143f15dbf0367ae671584aead5b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5b994354af3cab770bf13386469c5725713679af", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/613d5a9a440828970f1543b962779401ac2c9c62", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
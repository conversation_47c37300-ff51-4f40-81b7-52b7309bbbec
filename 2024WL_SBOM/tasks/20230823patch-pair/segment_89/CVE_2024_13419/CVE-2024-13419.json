{"cve_id": "CVE-2024-13419", "published_date": "2025-05-02T04:15:45.873", "last_modified_date": "2025-05-06T14:57:41.080", "descriptions": [{"lang": "en", "value": "Multiple plugins and/or themes for WordPress using Smart Framework are vulnerable to Stored Cross-Site Scripting due to a missing capability check on the saveOptions() and importThemeOptions() functions in various versions. This makes it possible for authenticated attackers, with Subscriber-level access and above, to update the plugin's settings which includes custom JavaScript that is enabled site-wide. This issue was escalated to Envato over two months from the date of this disclosure and the issue is still vulnerable."}, {"lang": "es", "value": "Varios complementos y/o temas para WordPress que utilizan Smart Framework son vulnerables a Cross-Site Scripting Almacenado debido a la falta de comprobación de capacidad en las funciones saveOptions() e importThemeOptions() en varias versiones. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, actualicen la configuración del complemento, que incluye JavaScript personalizado habilitado para todo el sitio. Este problema se escaló a Envato más de dos meses después de la fecha de esta divulgación y aún es vulnerable."}], "references": [{"url": "https://themeforest.net/item/beyot-wordpress-real-estate-theme/19514964", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/07729c28-a73a-46f4-853e-116792d612f5?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
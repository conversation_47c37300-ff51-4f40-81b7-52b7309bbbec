{"cve_id": "CVE-2025-4283", "published_date": "2025-05-05T19:15:57.687", "last_modified_date": "2025-05-14T20:56:20.573", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester/oretnom23 Stock Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /classes/Login.php?f=login. The manipulation of the argument Username leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester/oretnom23 Stock Management System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /classes/Login.php?f=login. La manipulación del argumento \"Username\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/th3w0lf-1337/Vulnerabilities/blob/main/SMS-PHP/SQLi/Auth-Bypass/info.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307391", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307391", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563175", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
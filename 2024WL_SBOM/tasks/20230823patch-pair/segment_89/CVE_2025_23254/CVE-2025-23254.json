{"cve_id": "CVE-2025-23254", "published_date": "2025-05-01T14:15:36.507", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "NVIDIA TensorRT-LLM for any platform contains a vulnerability in python executor where an attacker may cause a data validation issue by local access to the TRTLLM server. A successful exploit of this vulnerability may lead to code execution, information disclosure and data tampering."}, {"lang": "es", "value": "NVIDIA TensorRT-LLM para cualquier plataforma contiene una vulnerabilidad en el ejecutor de Python que permite a un atacante causar un problema de validación de datos mediante el acceso local al servidor TRTLLM. Una explotación exitosa de esta vulnerabilidad puede provocar la ejecución de código, la divulgación de información y la manipulación de datos."}], "references": [{"url": "https://nvidia.custhelp.com/app/answers/detail/a_id/5648", "source": "<EMAIL>", "tags": []}]}
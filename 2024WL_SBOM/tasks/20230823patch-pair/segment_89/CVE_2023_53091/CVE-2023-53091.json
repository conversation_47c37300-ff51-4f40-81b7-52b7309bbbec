{"cve_id": "CVE-2023-53091", "published_date": "2025-05-02T16:15:28.073", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: update s_journal_inum if it changes after journal replay\n\nWhen mounting a crafted ext4 image, s_journal_inum may change after journal\nreplay, which is obviously unreasonable because we have successfully loaded\nand replayed the journal through the old s_journal_inum. And the new\ns_journal_inum bypasses some of the checks in ext4_get_journal(), which\nmay trigger a null pointer dereference problem. So if s_journal_inum\nchanges after the journal replay, we ignore the change, and rewrite the\ncurrent journal_inum to the superblock."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: actualizar s_journal_inum si cambia después de la reproducción del diario. Al montar una imagen ext4 manipulada, s_journal_inum puede cambiar después de la reproducción del diario, lo cual es obviamente irrazonable porque hemos cargado y reproducido correctamente el diario a través del antiguo s_journal_inum. Y el nuevo s_journal_inum omite algunas de las comprobaciones en ext4_get_journal(), lo que puede desencadenar un problema de desreferencia de puntero nulo. Por lo tanto, si s_journal_inum cambia después de la reproducción del diario, ignoramos el cambio y reescribimos el journal_inum actual en el superbloque."}], "references": [{"url": "https://git.kernel.org/stable/c/3039d8b8692408438a618fac2776b629852663c3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/499fef2030fb754c68b1c7cb3a799a3bc1d0d925", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/70e66bdeae4d0f7c8e87762f425b68aedd5e8955", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee0c5277d4fab920bd31345c49e193ecede9ecef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
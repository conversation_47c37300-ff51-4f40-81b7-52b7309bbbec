{"cve_id": "CVE-2025-23162", "published_date": "2025-05-01T13:15:52.167", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/xe/vf: Don't try to trigger a full GT reset if VF\n\nVFs don't have access to the GDRST(0x941c) register that driver\nuses to reset a GT. Attempt to trigger a reset using debugfs:\n\n $ cat /sys/kernel/debug/dri/0000:00:02.1/gt0/force_reset\n\nor due to a hang condition detected by the driver leads to:\n\n [ ] xe 0000:00:02.1: [drm] GT0: trying reset from force_reset [xe]\n [ ] xe 0000:00:02.1: [drm] GT0: reset queued\n [ ] xe 0000:00:02.1: [drm] GT0: reset started\n [ ] ------------[ cut here ]------------\n [ ] xe 0000:00:02.1: [drm] GT0: V<PERSON> is trying to write 0x1 to an inaccessible register 0x941c+0x0\n [ ] WARNING: CPU: 3 PID: 3069 at drivers/gpu/drm/xe/xe_gt_sriov_vf.c:996 xe_gt_sriov_vf_write32+0xc6/0x580 [xe]\n [ ] RIP: 0010:xe_gt_sriov_vf_write32+0xc6/0x580 [xe]\n [ ] Call Trace:\n [ ]  <TASK>\n [ ]  ? show_regs+0x6c/0x80\n [ ]  ? __warn+0x93/0x1c0\n [ ]  ? xe_gt_sriov_vf_write32+0xc6/0x580 [xe]\n [ ]  ? report_bug+0x182/0x1b0\n [ ]  ? handle_bug+0x6e/0xb0\n [ ]  ? exc_invalid_op+0x18/0x80\n [ ]  ? asm_exc_invalid_op+0x1b/0x20\n [ ]  ? xe_gt_sriov_vf_write32+0xc6/0x580 [xe]\n [ ]  ? xe_gt_sriov_vf_write32+0xc6/0x580 [xe]\n [ ]  ? xe_gt_tlb_invalidation_reset+0xef/0x110 [xe]\n [ ]  ? __mutex_unlock_slowpath+0x41/0x2e0\n [ ]  xe_mmio_write32+0x64/0x150 [xe]\n [ ]  do_gt_reset+0x2f/0xa0 [xe]\n [ ]  gt_reset_worker+0x14e/0x1e0 [xe]\n [ ]  process_one_work+0x21c/0x740\n [ ]  worker_thread+0x1db/0x3c0\n\nFix that by sending H2G VF_RESET(0x5507) action instead."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/xe/vf: No intente activar un reinicio completo de GT si VF Los VF no tienen acceso al registro GDRST(0x941c) que el controlador usa para reiniciar un GT. Intento de activar un reinicio usando debugfs: $ cat /sys/kernel/debug/dri/0000:00:02.1/gt0/force_reset o debido a una condición de bloqueo detectada por el controlador conduce a: [ ] xe 0000:00:02.1: [drm] GT0: intentando reiniciar desde force_reset [xe] [ ] xe 0000:00:02.1: [drm] GT0: reinicio en cola [ ] xe 0000:00:02.1: [drm] GT0: reinicio iniciado [ ] ------------[ cortar aquí ]------------ [ ] xe 0000:00:02.1: [drm] GT0: VF está intentando escribir 0x1 en un registro inaccesible 0x941c+0x0 [ ] ADVERTENCIA: CPU: 3 PID: 3069 en controladores/gpu/drm/xe/xe_gt_sriov_vf.c:996 xe_gt_sriov_vf_write32+0xc6/0x580 [xe] [ ] RIP: 0010:xe_gt_sriov_vf_write32+0xc6/0x580 [xe] [ ] Rastreo de llamadas: [ ]  [ ] ? show_regs+0x6c/0x80 [ ] ? __warn+0x93/0x1c0 [ ] ? xe_gt_sriov_vf_write32+0xc6/0x580 [xe] [ ] ? report_bug+0x182/0x1b0 [ ] ? handle_bug+0x6e/0xb0 [ ] ? asm_exc_invalid_op+0x1b/0x20 [ ] ? xe_gt_sriov_vf_write32+0xc6/0x580 [xe] [ ] ? xe_gt_sriov_vf_write32+0xc6/0x580 [xe] [ ] ? xe_gt_tlb_invalidation_reset+0xef/0x110 [xe] [ ] ? Solucione esto enviando la acción H2G VF_RESET(0x5507) en su lugar."}], "references": [{"url": "https://git.kernel.org/stable/c/2eec2fa8666dcecebae33a565a818c9de9af8b50", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/459777724d306315070d24608fcd89aea85516d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90b16edb3213e4ae4a3138bb20703ae367e88a01", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a9bc61a61372897886f58fdaa5582e3f7bf9a50b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49881", "published_date": "2025-05-01T15:16:13.077", "last_modified_date": "2025-05-07T13:21:11.203", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: cfg80211: fix memory leak in query_regdb_file()\n\nIn the function query_regdb_file() the alpha2 parameter is duplicated\nusing kmemdup() and subsequently freed in regdb_fw_cb(). However,\nrequest_firmware_nowait() can fail without calling regdb_fw_cb() and\nthus leak memory."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: cfg80211: se corrige una fuga de memoria en query_regdb_file(). En la función query_regdb_file(), el parámetro alpha2 se duplica mediante kmemdup() y posteriormente se libera en regdb_fw_cb(). Sin embargo, request_firmware_nowait() puede fallar sin llamar a regdb_fw_cb() y, por lo tanto, provocar una fuga de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/0ede1a988299e95d54bd89551fd635980572e920", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/219446396786330937bcd382a7bc4ccd767383bc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/38c9fa2cc6bf4b6e1a74057aef8b5cffd23d3264", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/57b962e627ec0ae53d4d16d7bd1033e27e67677a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e1e12180321f416d83444f2cdc9259e0f5093d35", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e9b5a4566d5bc71cc901be50d1fa24da00613120", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-37797", "published_date": "2025-05-02T15:15:48.557", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet_sched: hfsc: Fix a UAF vulnerability in class handling\n\nThis patch fixes a Use-After-Free vulnerability in the HFSC qdisc class\nhandling. The issue occurs due to a time-of-check/time-of-use condition\nin hfsc_change_class() when working with certain child qdiscs like netem\nor codel.\n\nThe vulnerability works as follows:\n1. hfsc_change_class() checks if a class has packets (q.qlen != 0)\n2. It then calls qdisc_peek_len(), which for certain qdiscs (e.g.,\n   codel, netem) might drop packets and empty the queue\n3. The code continues assuming the queue is still non-empty, adding\n   the class to vttree\n4. This breaks HFSC scheduler assumptions that only non-empty classes\n   are in vttree\n5. Later, when the class is destroyed, this can lead to a Use-After-Free\n\nThe fix adds a second queue length check after qdisc_peek_len() to verify\nthe queue wasn't emptied."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net_sched: hfsc: Se corrige una vulnerabilidad de UAF en la gestión de clases. Este parche corrige una vulnerabilidad de use-after-free en la gestión de clases de qdisc HFSC. El problema se produce debido a una condición de tiempo de comprobación/tiempo de uso en hfsc_change_class() al trabajar con ciertas qdiscs secundarias como netem o codel. La vulnerabilidad funciona de la siguiente manera: 1. hfsc_change_class() verifica si una clase tiene paquetes (q.qlen != 0) 2. Luego llama a qdisc_peek_len(), que para ciertos qdiscs (por ejemplo, codel, netem) puede descartar paquetes y vaciar la cola 3. El código continúa asumiendo que la cola todavía no está vacía, agregando la clase a vttree 4. Esto rompe las suposiciones del programador HFSC de que solo las clases no vacías están en vttree 5. <PERSON><PERSON> tarde, cuando se destruye la clase, esto puede llevar a un Use-After-Free La solución agrega una segunda verificación de longitud de cola después de qdisc_peek_len() para verificar que la cola no se haya vaciado."}], "references": [{"url": "https://git.kernel.org/stable/c/20d584a33e480ae80d105f43e0e7b56784da41b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/28b09a067831f7317c3841812276022d6c940677", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39b9095dd3b55d9b2743df038c32138efa34a9de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3aa852e3605000d5c47035c3fc3a986d14ccfa9f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3df275ef0a6ae181e8428a6589ef5d5231e58b5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/86cd4641c713455a4f1c8e54c370c598c2b1cee0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bb583c88d23b72d8d16453d24856c99bd93dadf5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fcc8ede663569c704fb00a702973bd6c00373283", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
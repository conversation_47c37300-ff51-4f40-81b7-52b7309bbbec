{"cve_id": "CVE-2025-46731", "published_date": "2025-05-05T20:15:21.460", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Craft is a content management system. Versions of Craft CMS on the 4.x branch prior to 4.14.13 and on the 5.x branch prior to 5.6.16 contains a potential remote code execution vulnerability via Twig SSTI. One must have administrator access and `ALLOW_ADMIN_CHANGES` must be enabled for this to work. Users should update to the patched versions 4.14.13 or 5.6.15 to mitigate the issue."}, {"lang": "es", "value": "Craft es un sistema de gestión de contenido. Las versiones de Craft CMS en la rama 4.x anterior a la 4.14.13 y en la rama 5.x anterior a la 5.6.16 contienen una posible vulnerabilidad de ejecución remota de código a través de Twig SSTI. Se requiere acceso de administrador y la opción `ALLOW_ADMIN_CHANGES` debe estar habilitada para que esto funcione. Los usuarios deben actualizar a las versiones parcheadas 4.14.13 o 5.6.15 para mitigar el problema."}], "references": [{"url": "http://github.com/craftcms/cms/pull/17026", "source": "<EMAIL>", "tags": []}, {"url": "https://craftcms.com/knowledge-base/securing-craft#set-allowAdminChanges-to-false-in-production", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/craftcms/cms/security/advisories/GHSA-7c58-g782-9j38", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/craftcms/cms/security/advisories/GHSA-f3cw-hg6r-chfv", "source": "<EMAIL>", "tags": []}]}
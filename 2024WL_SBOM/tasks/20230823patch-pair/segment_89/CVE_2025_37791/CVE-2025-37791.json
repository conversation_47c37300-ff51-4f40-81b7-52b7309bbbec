{"cve_id": "CVE-2025-37791", "published_date": "2025-05-01T14:15:43.540", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nethtool: cmis_cdb: use correct rpl size in ethtool_cmis_module_poll()\n\nrpl is passed as a pointer to ethtool_cmis_module_poll(), so the correct\nsize of rpl is sizeof(*rpl) which should be just 1 byte.  Using the\npointer size instead can cause stack corruption:\n\nKernel panic - not syncing: stack-protector: Kernel stack is corrupted in: ethtool_cmis_wait_for_cond+0xf4/0x100\nCPU: 72 UID: 0 PID: 4440 Comm: kworker/72:2 Kdump: loaded Tainted: G           OE      6.11.0 #24\nTainted: [O]=OOT_MODULE, [E]=UNSIGNED_MODULE\nHardware name: Dell Inc. PowerEdge R760/04GWWM, BIOS 1.6.6 09/20/2023\nWorkqueue: events module_flash_fw_work\nCall Trace:\n <TASK>\n panic+0x339/0x360\n ? ethtool_cmis_wait_for_cond+0xf4/0x100\n ? __pfx_status_success+0x10/0x10\n ? __pfx_status_fail+0x10/0x10\n __stack_chk_fail+0x10/0x10\n ethtool_cmis_wait_for_cond+0xf4/0x100\n ethtool_cmis_cdb_execute_cmd+0x1fc/0x330\n ? __pfx_status_fail+0x10/0x10\n cmis_cdb_module_features_get+0x6d/0xd0\n ethtool_cmis_cdb_init+0x8a/0xd0\n ethtool_cmis_fw_update+0x46/0x1d0\n module_flash_fw_work+0x17/0xa0\n process_one_work+0x179/0x390\n worker_thread+0x239/0x340\n ? __pfx_worker_thread+0x10/0x10\n kthread+0xcc/0x100\n ? __pfx_kthread+0x10/0x10\n ret_from_fork+0x2d/0x50\n ? __pfx_kthread+0x10/0x10\n ret_from_fork_asm+0x1a/0x30\n </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ethtool: cmis_cdb: use el tamaño rpl correcto en ethtool_cmis_module_poll() rpl se pasa como un puntero a ethtool_cmis_module_poll(), por lo que el tamaño correcto de rpl es sizeof(*rpl), que debería ser solo 1 byte. El uso del tamaño del puntero en su lugar puede causar corrupción en la pila: Pánico del kernel - no sincroniza: protector de pila: La pila del kernel está dañada en: ethtool_cmis_wait_for_cond+0xf4/0x100 CPU: 72 UID: 0 PID: 4440 Comm: kworker/72:2 Kdump: cargado Contaminado: G OE 6.11.0 #24 Contaminado: [O]=OOT_MODULE, [E]=UNSIGNED_MODULE Nombre del hardware: Dell Inc. PowerEdge R760/04GWWM, BIOS 1.6.6 20/09/2023 Cola de trabajo: eventos module_flash_fw_work Rastreo de llamadas:  panic+0x339/0x360 ? ethtool_cmis_wait_for_cond+0xf4/0x100 ? __pfx_status_success+0x10/0x10 ? __pfx_status_fail+0x10/0x10 __stack_chk_fail+0x10/0x10 ethtool_cmis_wait_for_cond+0xf4/0x100 ethtool_cmis_cdb_execute_cmd+0x1fc/0x330 ? __pfx_status_fail+0x10/0x10 cmis_cdb_module_features_get+0x6d/0xd0 ethtool_cmis_cdb_init+0x8a/0xd0 ethtool_cmis_fw_update+0x46/0x1d0 module_flash_fw_work+0x17/0xa0 process_one_work+0x179/0x390 worker_thread+0x239/0x340 ? __pfx_worker_thread+0x10/0x10 kthread+0xcc/0x100 ? __pfx_kthread+0x10/0x10 ret_from_fork+0x2d/0x50 ? __pfx_kthread+0x10/0x10 ret_from_fork_asm+0x1a/0x30 "}], "references": [{"url": "https://git.kernel.org/stable/c/61765e1b417a23371c3735e3cddf4ad9354ed2e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7eb0a0072f966bb0b01d8b7d529d9743a7187bd1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f3fdd4fba16c74697d8bc730b82fb7c1eff7fab3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
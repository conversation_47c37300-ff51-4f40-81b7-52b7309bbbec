{"cve_id": "CVE-2025-3879", "published_date": "2025-05-02T17:15:51.273", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Vault Community, Vault Enterprise (“Vault”) Azure Auth method did not correctly validate the claims in the Azure-issued token, resulting in the potential bypass of the bound_locations parameter on login. Fixed in Vault Community Edition 1.19.1 and Vault Enterprise 1.19.1, 1.18.7, 1.17.14, 1.16.18."}, {"lang": "es", "value": "El método de autenticación de Azure de Vault Community, Vault Enterprise (\"Vault\") no validaba correctamente las notificaciones en el token emitido por Azure, lo que podía provocar la omisión del parámetro bound_locations al iniciar sesión. Corregido en Vault Community Edition 1.19.1 y Vault Enterprise 1.19.1, 1.18.7, 1.17.14 y 1.16.18."}], "references": [{"url": "https://discuss.hashicorp.com/t/hcsec-2025-07-vault-s-azure-authentication-method-bound-location-restriction-could-be-bypassed-on-login/74716", "source": "<EMAIL>", "tags": []}]}
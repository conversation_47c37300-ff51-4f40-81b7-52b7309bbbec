{"cve_id": "CVE-2022-49854", "published_date": "2025-05-01T15:16:08.997", "last_modified_date": "2025-05-07T13:32:09.350", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmctp: Fix an error handling path in mctp_init()\n\nIf mctp_neigh_init() return error, the routes resources should\nbe released in the error handling path. Otherwise some resources\nleak."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mctp: Se corrige una ruta de gestión de errores en mctp_init(). Si mctp_neigh_init() devuelve un error, los recursos de las rutas deben liberarse en la ruta de gestión de errores. De lo contrario, se producen fugas de recursos."}], "references": [{"url": "https://git.kernel.org/stable/c/216c83222d2eb24b0e63df56e8740b02c33286e8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/49d8a6e24a3496d86e8d8ae748375df984fb6d6f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d4072058af4fd8fb4658e7452289042a406a9398", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
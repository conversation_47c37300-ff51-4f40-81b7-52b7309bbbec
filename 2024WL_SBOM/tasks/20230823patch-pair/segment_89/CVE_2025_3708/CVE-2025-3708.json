{"cve_id": "CVE-2025-3708", "published_date": "2025-05-02T04:15:55.507", "last_modified_date": "2025-05-07T16:50:44.930", "descriptions": [{"lang": "en", "value": "Le-show medical practice management system from Le-yan has a SQL Injection vulnerability, allowing unauthenticated remote attackers to inject arbitrary SQL commands to read, modify, and delete database contents."}, {"lang": "es", "value": "Le-show medical practice management system de Le-yan tiene una vulnerabilidad de inyección SQL, lo que permite a atacantes remotos no autenticados inyectar comandos SQL arbitrarios para leer, modificar y eliminar contenidos de la base de datos."}], "references": [{"url": "https://www.twcert.org.tw/en/cp-139-10086-dbfd0-2.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.twcert.org.tw/tw/cp-132-10085-69e16-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-49923", "published_date": "2025-05-01T15:16:17.900", "last_modified_date": "2025-05-07T13:28:19.467", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfc: nxp-nci: Fix potential memory leak in nxp_nci_send()\n\nnxp_nci_send() will call nxp_nci_i2c_write(), and only free skb when\nnxp_nci_i2c_write() failed. However, even if the nxp_nci_i2c_write()\nrun succeeds, the skb will not be freed in nxp_nci_i2c_write(). As the\nresult, the skb will memleak. nxp_nci_send() should also free the skb\nwhen nxp_nci_i2c_write() succeeds."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfc: nxp-nci: Se corrige una posible fuga de memoria en nxp_nci_send(). nxp_nci_send() llamará a nxp_nci_i2c_write() y solo liberará skb cuando nxp_nci_i2c_write() falle. Sin embargo, incluso si la ejecución de nxp_nci_i2c_write() tiene éxito, skb no se liberará en nxp_nci_i2c_write(). Como resultado, skb sufrirá una fuga de memoria. nxp_nci_send() también debería liberar skb cuando nxp_nci_i2c_write() tenga éxito."}], "references": [{"url": "https://git.kernel.org/stable/c/3cba1f061bfe23fece2841129ca2862cdec29d5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3ecf0f4227029b2c42e036b10ff6e5d09e20821e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7bf1ed6aff0f70434bd0cdd45495e83f1dffb551", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9ae2c9a91ff068f4c3e392f47e8e26a1c9f85ebb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-46630", "published_date": "2025-05-01T20:15:38.803", "last_modified_date": "2025-05-27T14:24:30.130", "descriptions": [{"lang": "en", "value": "Improper access controls in the web management portal of the Tenda RX2 Pro *********** allows an unauthenticated remote attacker to enable 'ate' (a remote system management binary) by sending a /goform/ate web request."}, {"lang": "es", "value": "Los controles de acceso inadecuados en el portal de administración web de Tenda RX2 Pro *********** permiten que un atacante remoto no autenticado habilite 'ate' (un binario de administración del sistema remoto) enviando una solicitud web /goform/ate."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46630-enable-ate-unauthenticated-through-httpd", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
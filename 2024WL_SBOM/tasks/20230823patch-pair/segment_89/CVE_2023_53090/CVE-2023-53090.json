{"cve_id": "CVE-2023-53090", "published_date": "2025-05-02T16:15:27.957", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amdkfd: Fix an illegal memory access\n\nIn the kfd_wait_on_events() function, the kfd_event_waiter structure is\nallocated by alloc_event_waiters(), but the event field of the waiter\nstructure is not initialized; When copy_from_user() fails in the\nkfd_wait_on_events() function, it will enter exception handling to\nrelease the previously allocated memory of the waiter structure;\nDue to the event field of the waiters structure being accessed\nin the free_waiters() function, this results in illegal memory access\nand system crash, here is the crash log:\n\nlocalhost kernel: RIP: 0010:native_queued_spin_lock_slowpath+0x185/0x1e0\nlocalhost kernel: RSP: 0018:ffffaa53c362bd60 EFLAGS: 00010082\nlocalhost kernel: RAX: ff3d3d6bff4007cb RBX: 0000000000000282 RCX: 00000000002c0000\nlocalhost kernel: RDX: ffff9e855eeacb80 RSI: 000000000000279c RDI: ffffe7088f6a21d0\nlocalhost kernel: RBP: ffffe7088f6a21d0 R08: 00000000002c0000 R09: ffffaa53c362be64\nlocalhost kernel: R10: ffffaa53c362bbd8 R11: 0000000000000001 R12: 0000000000000002\nlocalhost kernel: R13: ffff9e7ead15d600 R14: 0000000000000000 R15: ffff9e7ead15d698\nlocalhost kernel: FS:  0000152a3d111700(0000) GS:ffff9e855ee80000(0000) knlGS:0000000000000000\nlocalhost kernel: CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nlocalhost kernel: CR2: 0000152938000010 CR3: 000000044d7a4000 CR4: 00000000003506e0\nlocalhost kernel: Call Trace:\nlocalhost kernel: _raw_spin_lock_irqsave+0x30/0x40\nlocalhost kernel: remove_wait_queue+0x12/0x50\nlocalhost kernel: kfd_wait_on_events+0x1b6/0x490 [hydcu]\nlocalhost kernel: ? ftrace_graph_caller+0xa0/0xa0\nlocalhost kernel: kfd_ioctl+0x38c/0x4a0 [hydcu]\nlocalhost kernel: ? kfd_ioctl_set_trap_handler+0x70/0x70 [hydcu]\nlocalhost kernel: ? kfd_ioctl_create_queue+0x5a0/0x5a0 [hydcu]\nlocalhost kernel: ? ftrace_graph_caller+0xa0/0xa0\nlocalhost kernel: __x64_sys_ioctl+0x8e/0xd0\nlocalhost kernel: ? syscall_trace_enter.isra.18+0x143/0x1b0\nlocalhost kernel: do_syscall_64+0x33/0x80\nlocalhost kernel: entry_SYSCALL_64_after_hwframe+0x44/0xa9\nlocalhost kernel: RIP: 0033:0x152a4dff68d7\n\nAllocate the structure with kcalloc, and remove redundant 0-initialization\nand a redundant loop condition check."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amdkfd: Se corrige un acceso ilegal a memoria En la función kfd_wait_on_events(), la estructura kfd_event_waiter es asignada por alloc_event_waiters(), pero el campo de evento de la estructura waiter no se inicializa; Cuando copy_from_user() falla en la función kfd_wait_on_events(), ingresará al control de excepciones para liberar la memoria previamente asignada de la estructura waiter; Debido a que se accede al campo de evento de la estructura waiters en la función free_waiters(), esto da como resultado un acceso ilegal a la memoria y un bloqueo del sistema. Aquí está el registro de bloqueo: kernel localhost: RIP: 0010:native_queued_spin_lock_slowpath+0x185/0x1e0 kernel localhost: RSP: 0018:ffffaa53c362bd60 EFLAGS: 00010082 kernel localhost: RAX: ff3d3d6bff4007cb RBX: 0000000000000282 RCX: 00000000002c0000 kernel localhost: RDX: ffff9e855eeacb80 RSI: 000000000000279c RDI: ffffe7088f6a21d0 kernel localhost: RBP: ffffe7088f6a21d0 R08: 00000000002c0000 R09: ffffaa53c362be64 núcleo del host local: R10: ffffaa53c362bbd8 R11: 0000000000000001 R12: 0000000000000002 núcleo del host local: R13: ffff9e7ead15d600 R14: 0000000000000000 R15: ffff9e7ead15d698 núcleo del host local: FS: 0000152a3d111700(0000) GS:ffff9e855ee80000(0000) knlGS:0000000000000000 núcleo localhost: CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 núcleo localhost: CR2: 0000152938000010 CR3: 000000044d7a4000 CR4: 00000000003506e0 núcleo localhost: Seguimiento de llamadas: núcleo localhost: _raw_spin_lock_irqsave+0x30/0x40 núcleo localhost: remove_wait_queue+0x12/0x50 núcleo localhost: kfd_wait_on_events+0x1b6/0x490 [hydcu] núcleo localhost: ? ftrace_graph_caller+0xa0/0xa0 núcleo local del host: kfd_ioctl+0x38c/0x4a0 [hydcu] núcleo local del host: ? kfd_ioctl_set_trap_handler+0x70/0x70 [hydcu] núcleo local del host: ? kfd_ioctl_create_queue+0x5a0/0x5a0 [hydcu] núcleo local del host: ? ftrace_graph_caller+0xa0/0xa0 núcleo local del host: __x64_sys_ioctl+0x8e/0xd0 núcleo local del host: ? syscall_trace_enter.isra.18+0x143/0x1b0 kernel localhost: do_syscall_64+0x33/0x80 kernel localhost: entry_SYSCALL_64_after_hwframe+0x44/0xa9 kernel localhost: RIP: 0033:0x152a4dff68d7 Asigne la estructura con kcalloc y elimine la inicialización 0 redundante y una verificación de condición de bucle redundante."}], "references": [{"url": "https://git.kernel.org/stable/c/2fece63b55c5d74cd6f5de51159e2cde37e10555", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4fc8fff378b2f2039f2a666d9f8c570f4e58352c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5a3fb3b745af0ce46ec2e0c8e507bae45b937334", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/61f306f8df0d5559659c5578cf6d95236bcdcb25", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6936525142a015e854d0a23e9ad9ea0a28b3843d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bbf5eada4334a96e3a204b2307ff5b14dc380b0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d9923e7214a870b312bf61f6a89c7554d0966985", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
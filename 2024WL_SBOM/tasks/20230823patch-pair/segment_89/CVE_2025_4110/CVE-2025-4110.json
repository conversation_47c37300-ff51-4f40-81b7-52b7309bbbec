{"cve_id": "CVE-2025-4110", "published_date": "2025-04-30T11:15:49.983", "last_modified_date": "2025-05-13T20:26:02.303", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Pre-School Enrollment System 1.0 and classified as critical. Affected by this issue is some unknown functionality of the file /admin/edit-teacher.php. The manipulation of the argument mobilenumber leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Pre-School Enrollment System 1.0, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/edit-teacher.php. La manipulación del argumento \"mobilenumber\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306590", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306590", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560703", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
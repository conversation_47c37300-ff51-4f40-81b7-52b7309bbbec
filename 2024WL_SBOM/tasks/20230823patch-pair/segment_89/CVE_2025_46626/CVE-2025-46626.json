{"cve_id": "CVE-2025-46626", "published_date": "2025-05-01T20:15:38.190", "last_modified_date": "2025-05-27T14:22:51.570", "descriptions": [{"lang": "en", "value": "Reuse of a static AES key and initialization vector for encrypted traffic to the 'ate' management service of the Tenda RX2 Pro *********** allows an attacker to decrypt, replay, and/or forge traffic to the service."}, {"lang": "es", "value": "La reutilización de una clave AES estática y un vector de inicialización para el tráfico cifrado hacia el servicio de gestión 'ate' del Tenda RX2 Pro *********** permite a un atacante descifrar, reproducir y/o falsificar el tráfico hacia el servicio."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46625-command-injection-through-setlancfg-in-httpd", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
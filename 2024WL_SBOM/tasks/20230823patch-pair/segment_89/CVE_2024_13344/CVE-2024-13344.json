{"cve_id": "CVE-2024-13344", "published_date": "2025-05-02T04:15:45.430", "last_modified_date": "2025-05-06T15:50:33.660", "descriptions": [{"lang": "en", "value": "The Advance Seat Reservation Management for WooCommerce plugin for WordPress is vulnerable to SQL Injection via the 'profileId' parameter in all versions up to, and including, 3.3 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Advance Seat Reservation Management para WooCommerce de WordPress es vulnerable a la inyección SQL a través del parámetro 'profileId' en todas las versiones hasta la 3.3 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/advance-seat-reservation-management-for-woocommerce/19984266", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/35acdb85-e463-46b1-aea7-a6d4c3581499?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
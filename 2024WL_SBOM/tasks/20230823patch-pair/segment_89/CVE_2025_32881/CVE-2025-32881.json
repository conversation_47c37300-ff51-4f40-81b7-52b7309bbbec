{"cve_id": "CVE-2025-32881", "published_date": "2025-05-01T18:15:54.970", "last_modified_date": "2025-06-20T16:53:44.603", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna v1 devices with app 5.5.3 and firmware 0.25.5. By default, the GID is the user's phone number unless they specifically opt out. A phone number is very sensitive information because it can be tied back to individuals. The app does not encrypt the GID in messages."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna v1 con la aplicación 5.5.3 y el firmware 0.25.5. <PERSON><PERSON> defecto, el GID es el número de teléfono del usuario, a menos que este lo desactive específicamente. Un número de teléfono es información muy sensible, ya que puede asociarse con personas. La aplicación no cifra el GID en los mensajes."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
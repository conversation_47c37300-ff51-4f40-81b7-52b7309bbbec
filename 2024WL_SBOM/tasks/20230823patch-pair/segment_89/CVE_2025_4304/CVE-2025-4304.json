{"cve_id": "CVE-2025-4304", "published_date": "2025-05-06T02:15:14.420", "last_modified_date": "2025-05-13T19:21:33.627", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Cyber Cafe Management System 1.0. This affects an unknown part of the file /adminprofile.php. The manipulation of the argument mobilenumber leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Cyber Cafe Management System 1.0. Esta afecta a una parte desconocida del archivo /adminprofile.php. La manipulación del argumento \"mobilenumber\" provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/3507998897/myCVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307407", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307407", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563723", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
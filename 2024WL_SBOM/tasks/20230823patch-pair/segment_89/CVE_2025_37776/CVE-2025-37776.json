{"cve_id": "CVE-2025-37776", "published_date": "2025-05-01T14:15:41.373", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix use-after-free in smb_break_all_levII_oplock()\n\nThere is a room in smb_break_all_levII_oplock that can cause racy issues\nwhen unlocking in the middle of the loop. This patch use read lock\nto protect whole loop."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: se corrige el error \"use-after-free\" en smb_break_all_levII_oplock(). Existe una zona en smb_break_all_levII_oplock que puede causar problemas de velocidad al desbloquear en medio del bucle. Este parche utiliza un bloqueo de lectura para proteger todo el bucle."}], "references": [{"url": "https://git.kernel.org/stable/c/18b4fac5ef17f77fed9417d22210ceafd6525fc7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/296cb5457cc6f4a754c4ae29855f8a253d52bcc6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d54ab1520d43e95f9b2e22d7a05fc9614192e5a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d73686367ad68534257cd88a36ca3c52cb8b81d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
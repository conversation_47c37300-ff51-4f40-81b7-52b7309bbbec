{"cve_id": "CVE-2025-4109", "published_date": "2025-04-30T10:15:18.813", "last_modified_date": "2025-05-13T20:26:18.360", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Pre-School Enrollment System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /admin/edit-subadmin.php. The manipulation of the argument mobilenumber leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Pre-School Enrollment System 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/edit-subadmin.php. La manipulación del argumento \"mobilenumber\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306589", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306589", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560700", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
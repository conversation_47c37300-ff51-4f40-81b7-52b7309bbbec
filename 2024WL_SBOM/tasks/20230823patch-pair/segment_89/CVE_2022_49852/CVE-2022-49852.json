{"cve_id": "CVE-2022-49852", "published_date": "2025-05-01T15:16:08.787", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv: process: fix kernel info leakage\n\nthread_struct's s[12] may contain random kernel memory content, which\nmay be finally leaked to userspace. This is a security hole. Fix it\nby clearing the s[12] array in thread_struct when fork.\n\nAs for kthread case, it's better to clear the s[12] array as well."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv: proceso: corrección de fuga de información del kernel. El array s[12] de thread_struct puede contener contenido aleatorio de memoria del kernel, que podría filtrarse al espacio de usuario. Esto representa una vulnerabilidad de seguridad. Para solucionarlo, borre la matriz s[12] de thread_struct al bifurcar. En el caso de kthread, es recomendable borrar también la matriz s[12]."}], "references": [{"url": "https://git.kernel.org/stable/c/358a68f98304b40b201ba5afe94c20355aa3dc68", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6510c78490c490a6636e48b61eeaa6fb65981f4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c4601d30f7d989b4f354df899ab85b5f7a750d30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c5c0b3167537793a7cf936fb240366eefd2fc7fb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cc36c7fa5d9384602529ba3eea8c5daee7be4dbc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e56d18a976dda653194218df6d40d8122c775712", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
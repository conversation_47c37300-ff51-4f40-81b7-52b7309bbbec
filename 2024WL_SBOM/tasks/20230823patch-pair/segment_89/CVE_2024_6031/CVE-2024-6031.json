{"cve_id": "CVE-2024-6031", "published_date": "2025-04-30T20:15:21.153", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Tesla Model S oFono AT Command Heap-based Buffer Overflow Code Execution Vulnerability. This vulnerability allows local attackers to execute arbitrary code on affected Tesla Model S vehicles. An attacker must first obtain the ability to execute code on the target modem in order to exploit this vulnerability.\n \nThe specific flaw exists within the parsing of responses from AT commands. The issue results from the lack of proper validation of the length of user-supplied data prior to copying it to a heap-based buffer. An attacker can leverage this vulnerability to execute code in the context of the device. Was ZDI-CAN-23198."}, {"lang": "es", "value": "Vulnerabilidad de ejecución de código por desbordamiento de búfer basado en el montón del comando AT oFono del Tesla Model S. Esta vulnerabilidad permite a atacantes locales ejecutar código arbitrario en los vehículos Tesla Model S afectados. Para explotar esta vulnerabilidad, un atacante debe primero obtener la capacidad de ejecutar código en el módem objetivo. La falla específica se encuentra en el análisis de las respuestas de los comandos AT. El problema se debe a la falta de una validación adecuada de la longitud de los datos proporcionados por el usuario antes de copiarlos a un búfer basado en el montón. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del dispositivo. Anteriormente, se denominaba ZDI-CAN-23198."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-261/", "source": "<EMAIL>", "tags": []}]}
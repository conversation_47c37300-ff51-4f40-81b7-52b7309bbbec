{"cve_id": "CVE-2022-49924", "published_date": "2025-05-01T15:16:18.170", "last_modified_date": "2025-05-07T13:28:24.500", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfc: fdp: Fix potential memory leak in fdp_nci_send()\n\nfdp_nci_send() will call fdp_nci_i2c_write that will not free skb in\nthe function. As a result, when fdp_nci_i2c_write() finished, the skb\nwill memleak. fdp_nci_send() should free skb after fdp_nci_i2c_write()\nfinished."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfc: fdp: Se corrige una posible fuga de memoria en fdp_nci_send(). fdp_nci_send() llamará a fdp_nci_i2c_write, lo que no liberará skb en la función. Como resultado, al finalizar fdp_nci_i2c_write(), skb sufrirá una fuga de memoria. fdp_nci_send() debería liberar skb después de finalizar fdp_nci_i2c_write()."}], "references": [{"url": "https://git.kernel.org/stable/c/1a7a898f8f7b56c0eaa2baf67a0c96235a30bc29", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/44bc1868a4f542502ea2221fe5ad88ca66d1c6b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8e4aae6b8ca76afb1fb64dcb24be44ba814e7f8a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e8c11ee2d07f7c4dfa2ac0ea8efc4f627e58ea57", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-4177", "published_date": "2025-05-02T03:15:21.397", "last_modified_date": "2025-05-06T15:40:15.200", "descriptions": [{"lang": "en", "value": "The Flynax Bridge plugin for WordPress is vulnerable to unauthorized loss of data due to a missing capability check on the deleteUser() function in all versions up to, and including, 2.2.0. This makes it possible for unauthenticated attackers to delete arbitrary users."}, {"lang": "es", "value": "El complemento Flynax Bridge para WordPress es vulnerable a la pérdida no autorizada de datos debido a la falta de comprobación de la función deleteUser() en todas las versiones hasta la 2.2.0 incluida. Esto permite que atacantes no autenticados eliminen usuarios arbitrarios."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/flynax-bridge/trunk/src/API.php#L386", "source": "<EMAIL>", "tags": ["Product", "Technical Description"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/dcb33d02-d384-4dff-91e1-c49e86b97d6e?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-4151", "published_date": "2025-05-01T06:15:35.130", "last_modified_date": "2025-05-07T20:08:05.920", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Curfew e-Pass Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /admin/pass-bwdates-reports-details.php. The manipulation of the argument fromdate leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Curfew e-Pass Management System 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/pass-bwdates-reports-details.php. La manipulación del argumento \"fromdate\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/faithhard/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306683", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306683", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560806", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
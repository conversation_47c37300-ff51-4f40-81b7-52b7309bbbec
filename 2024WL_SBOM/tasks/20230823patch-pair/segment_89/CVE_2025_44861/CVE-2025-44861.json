{"cve_id": "CVE-2025-44861", "published_date": "2025-05-01T18:15:56.647", "last_modified_date": "2025-05-21T19:47:17.270", "descriptions": [{"lang": "en", "value": "TOTOLINK CA300-POE V6.2c.884_B20180522 was found to contain a command injection vulnerability in the CloudSrvUserdataVersionCheck function via the url parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA300-POE V6.2c.884_B20180522 contenía una vulnerabilidad de inyección de comandos en la función CloudSrvUserdataVersionCheck mediante el parámetro url. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA300-POE/CloudSrvUserdataVersionCheck/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
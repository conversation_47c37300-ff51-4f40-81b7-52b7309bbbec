{"cve_id": "CVE-2023-53075", "published_date": "2025-05-02T16:15:26.510", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nftrace: Fix invalid address access in lookup_rec() when index is 0\n\nKASAN reported follow problem:\n\n BUG: KASAN: use-after-free in lookup_rec\n Read of size 8 at addr ffff000199270ff0 by task modprobe\n CPU: 2 Comm: modprobe\n Call trace:\n  kasan_report\n  __asan_load8\n  lookup_rec\n  ftrace_location\n  arch_check_ftrace_location\n  check_kprobe_address_safe\n  register_kprobe\n\nWhen checking pg->records[pg->index - 1].ip in lookup_rec(), it can get a\npg which is newly added to ftrace_pages_start in ftrace_process_locs().\nBefore the first pg->index++, index is 0 and accessing pg->records[-1].ip\nwill cause this problem.\n\nDon't check the ip when pg->index is 0."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ftrace: Se corrige el acceso a direcciones no válidas en lookup_rec() cuando el índice es 0 KASAN informó el siguiente problema: BUG: KASAN: use-after-free en lookup_rec Lectura de tamaño 8 en la dirección ffff000199270ff0 por la tarea modprobe CPU: 2 Comm: modprobe Rastreo de llamadas: kasan_report __asan_load8 lookup_rec ftrace_location arch_check_ftrace_location check_kprobe_address_safe register_kprobe Al verificar pg-&gt;records[pg-&gt;index - 1].ip en lookup_rec(), puede obtener un pg que se agregó recientemente a ftrace_pages_start en ftrace_process_locs(). Antes del primer pg-&gt;index++, el índice es 0 y acceder a pg-&gt;records[-1].ip causará este problema. No verifique la IP cuando pg-&gt;index sea 0."}], "references": [{"url": "https://git.kernel.org/stable/c/2a0d71fabfeb349216d33f001a6421b1768bd3a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2de28e5ce34b22b73b833a21e2c45ae3aade3964", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4f84f31f63416b0f02fc146ffdc4ab32723eb7e8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7569ee04b0e3b32df79f64db3a7138573edad9bc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/83c3b2f4e7c61367c7b24551f4c6eb94bbdda283", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ac58b88ccbbb8e9fb83e137cee04a856b1ea6635", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee92fa443358f4fc0017c1d0d325c27b37802504", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1bd8b7fd890d87d0dc4dedc6287ea34dd07c0b4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
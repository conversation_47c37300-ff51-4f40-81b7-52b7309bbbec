{"cve_id": "CVE-2023-53103", "published_date": "2025-05-02T16:15:29.223", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbonding: restore bond's IFF_SLAVE flag if a non-eth dev enslave fails\n\nsyzbot reported a warning[1] where the bond device itself is a slave and\nwe try to enslave a non-ethernet device as the first slave which fails\nbut then in the error path when ether_setup() restores the bond device\nit also clears all flags. In my previous fix[2] I restored the\nIFF_MASTER flag, but I didn't consider the case that the bond device\nitself might also be a slave with IFF_SLAVE set, so we need to restore\nthat flag as well. Use the bond_ether_setup helper which does the right\nthing and restores the bond's flags properly.\n\nSteps to reproduce using a nlmon dev:\n $ ip l add nlmon0 type nlmon\n $ ip l add bond1 type bond\n $ ip l add bond2 type bond\n $ ip l set bond1 master bond2\n $ ip l set dev nlmon0 master bond1\n $ ip -d l sh dev bond1\n 22: bond1: <BROADCAST,MULTICAST,MASTER> mtu 1500 qdisc noqueue master bond2 state DOWN mode DEFAULT group default qlen 1000\n (now bond1's IFF_SLAVE flag is gone and we'll hit a warning[3] if we\n  try to delete it)\n\n[1] https://syzkaller.appspot.com/bug?id=****************************************\n[2] commit 7d5cd2ce5292 (\"bonding: correctly handle bonding type change on enslave failure\")\n[3] example warning:\n [   27.008664] bond1: (slave nlmon0): The slave device specified does not support setting the MAC address\n [   27.008692] bond1: (slave nlmon0): Error -95 calling set_mac_address\n [   32.464639] bond1 (unregistering): Released all slaves\n [   32.464685] ------------[ cut here ]------------\n [   32.464686] WARNING: CPU: 1 PID: 2004 at net/core/dev.c:10829 unregister_netdevice_many+0x72a/0x780\n [   32.464694] Modules linked in: br_netfilter bridge bonding virtio_net\n [   32.464699] CPU: 1 PID: 2004 Comm: ip Kdump: loaded Not tainted 5.18.0-rc3+ #47\n [   32.464703] Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.1-2.fc37 04/01/2014\n [   32.464704] RIP: 0010:unregister_netdevice_many+0x72a/0x780\n [   32.464707] Code: 99 fd ff ff ba 90 1a 00 00 48 c7 c6 f4 02 66 96 48 c7 c7 20 4d 35 96 c6 05 fa c7 2b 02 01 e8 be 6f 4a 00 0f 0b e9 73 fd ff ff <0f> 0b e9 5f fd ff ff 80 3d e3 c7 2b 02 00 0f 85 3b fd ff ff ba 59\n [   32.464710] RSP: 0018:ffffa006422d7820 EFLAGS: 00010206\n [   32.464712] RAX: ffff8f6e077140a0 RBX: ffffa006422d7888 RCX: 0000000000000000\n [   32.464714] RDX: ffff8f6e12edbe58 RSI: 0000000000000296 RDI: ffffffff96d4a520\n [   32.464716] RBP: ffff8f6e07714000 R08: ffffffff96d63600 R09: ffffa006422d7728\n [   32.464717] R10: 0000000000000ec0 R11: ffffffff9698c988 R12: ffff8f6e12edb140\n [   32.464719] R13: dead000000000122 R14: dead000000000100 R15: ffff8f6e12edb140\n [   32.464723] FS:  00007f297c2f1740(0000) GS:ffff8f6e5d900000(0000) knlGS:0000000000000000\n [   32.464725] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n [   32.464726] CR2: 00007f297bf1c800 CR3: 00000000115e8000 CR4: 0000000000350ee0\n [   32.464730] Call Trace:\n [   32.464763]  <TASK>\n [   32.464767]  rtnl_dellink+0x13e/0x380\n [   32.464776]  ? cred_has_capability.isra.0+0x68/0x100\n [   32.464780]  ? __rtnl_unlock+0x33/0x60\n [   32.464783]  ? bpf_lsm_capset+0x10/0x10\n [   32.464786]  ? security_capable+0x36/0x50\n [   32.464790]  rtnetlink_rcv_msg+0x14e/0x3b0\n [   32.464792]  ? _copy_to_iter+0xb1/0x790\n [   32.464796]  ? post_alloc_hook+0xa0/0x160\n [   32.464799]  ? rtnl_calcit.isra.0+0x110/0x110\n [   32.464802]  netlink_rcv_skb+0x50/0xf0\n [   32.464806]  netlink_unicast+0x216/0x340\n [   32.464809]  netlink_sendmsg+0x23f/0x480\n [   32.464812]  sock_sendmsg+0x5e/0x60\n [   32.464815]  ____sys_sendmsg+0x22c/0x270\n [   32.464818]  ? import_iovec+0x17/0x20\n [   32.464821]  ? sendmsg_copy_msghdr+0x59/0x90\n [   32.464823]  ? do_set_pte+0xa0/0xe0\n [   32.464828]  ___sys_sendmsg+0x81/0xc0\n [   32.464832]  ? mod_objcg_state+0xc6/0x300\n [   32.464835]  ? refill_obj_stock+0xa9/0x160\n [   32.464838]  ? memcg_slab_free_hook+0x1a5/0x1f0\n [   32.464842]  __sys_sendm\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: enlace: restaurar el indicador IFF_SLAVE del enlace si falla la ejecución de un dispositivo no ethernet. syzbot reportó una advertencia[1] donde el dispositivo de enlace es esclavo e intentamos ejecutar un dispositivo no ethernet como primer esclavo, lo cual falla. Sin embargo, en la ruta de error, cuando ether_setup() restaura el dispositivo de enlace, también borra todos los indicadores. En mi corrección anterior[2], restauré el indicador IFF_MASTER, pero no consideré la posibilidad de que el dispositivo de enlace también sea esclavo con IFF_SLAVE activado, por lo que también debemos restaurar ese indicador. Use el asistente bond_ether_setup, que realiza la acción correcta y restaura los indicadores del enlace correctamente. Pasos para reproducir usando un dev nlmon: $ ip l add nlmon0 type nlmon $ ip l add bond1 type bond $ ip l add bond2 type bond $ ip l set bond1 master bond2 $ ip l set dev nlmon0 master bond1 $ ip -dl sh dev bond1 22: bond1:  mtu 1500 qdisc noqueue master bond2 state DOWN mode DEFAULT group default qlen 1000 (ahora el indicador IFF_SLAVE de bond1 desapareció y recibiremos una advertencia[3] si intentamos eliminarlo) [1] https://syzkaller.appspot.com/bug?id=**************************************** [2] commit 7d5cd2ce5292 (\"bonding: \"Manejar correctamente el cambio de tipo de enlace en caso de fallo de esclavización\") [3] Ejemplo de advertencia: [27.008664] bond1: (esclavo nlmon0): El dispositivo esclavo especificado no admite la configuración de la dirección MAC [27.008692] bond1: (esclavo nlmon0): Error -95 al llamar a set_mac_address [32.464639] bond1 (anulando registro): Se liberaron todos los esclavos [32.464685] ------------[cortar aquí]------------ [32.464686] ADVERTENCIA: CPU: 1 PID: 2004 en net/core/dev.c:10829 unregister_netdevice_many+0x72a/0x780 [32.464694] Módulos vinculados: br_netfilter puente enlace virtio_net [32.464699] CPU: 1 PID: 2004 Comm: ip Kdump: cargado No contaminado 5.18.0-rc3+ #47 [ 32.464703] Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.1-2.fc37 01/04/2014 [ 32.464704] RIP: 0010:unregister_netdevice_many+0x72a/0x780 [ 32.464707] Código: 99 fd ff ff ba 90 1a 00 00 48 c7 c6 f4 02 66 96 48 c7 c7 20 4d 35 96 c6 05 fa c7 2b 02 01 e8 be 6f 4a 00 0f 0b e9 73 fd ff ff &lt;0f&gt; 0b e9 5f fd ff ff 80 3d e3 c7 2b 02 00 0f 85 3b fd ff ff ba 59 [ 32.464710] RSP: 0018:ffffa006422d7820 EFLAGS: 00010206 [ 32.464712] RAX: ffff8f6e077140a0 RBX: ffffa006422d7888 RCX: 0000000000000000 [ 32.464714] RDX: ffff8f6e12edbe58 RSI: 0000000000000296 RDI: ffffffff96d4a520 [32.464716] RBP: ffff8f6e07714000 R08: ffffffff96d63600 R09: ffffa006422d7728 [32.464717] R10: 000000000000ec0 R11: ffffffff9698c988 R12: ffff8f6e12edb140 [32.464719] R13: muerto000000000122 R14: muerto000000000100 R15: ffff8f6e12edb140 [32.464723] FS: 00007f297c2f1740(0000) GS:ffff8f6e5d900000(0000) knlGS:0000000000000000 [ 32.464725] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [ 32.464726] CR2: 00007f297bf1c800 CR3: 00000000115e8000 CR4: 0000000000350ee0 [ 32.464730] Rastreo de llamadas: [ 32.464763]  [ 32.464767] rtnl_dellink+0x13e/0x380 [ 32.464776] ? cred_has_capability.isra.0+0x68/0x100 [ 32.464780] ? __rtnl_unlock+0x33/0x60 [ 32.464783] ? bpf_lsm_capset+0x10/0x10 [ 32.464786] ? security_capable+0x36/0x50 [ 32.464790] rtnetlink_rcv_msg+0x14e/0x3b0 [ 32.464792] ? _copy_to_iter+0xb1/0x790 [ 32.464796] ? post_alloc_hook+0xa0/0x160 [ 32.464799] ? rtnl_calcit.isra.0+0x110/0x110 [ 32.464802] netlink_rcv_skb+0x50/0xf0 [ 32.464806] netlink_unicast+0x216/0x340 [ 32.464809] netlink_sendmsg+0x23f/0x480 [ 32.464812] sock_sendmsg+0x5e/0x60 [ 32.464815] ____sys_sendmsg+0x22c/0x270 [ 32.464818] ? import_iovec+0x17/0x20 [ 32.464821] ? sendmsg_copy_msghdr+0x59/0x90 [ 32.464823] ? do_set_pte+0xa0/0xe0 [ 32.464828] ___sys_sendmsg+0x81/0xc0 [ 32.464832] ? mod_objcg_state+0xc6/0x300 [ 32.464835] ? refill_obj_stock+0xa9/0x160 [ 32.464838] ? memcg_slab_free_hook+0x1a5/0x1f0 [ 32.464842] __sys_sendm ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/0276813b8ab08d9bf5ca4159f301d0829ecf13fc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/93c8cbeb1b2b8ff670b3dfd01b3abd843995c80f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e667d469098671261d558be0cd93dca4d285ce1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ecb1b5135bd3f232d5335b3935e2c2ac11bfa02f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-44073", "published_date": "2025-05-06T21:16:19.067", "last_modified_date": "2025-06-12T17:09:21.733", "descriptions": [{"lang": "en", "value": "SeaCMS v13.3 was discovered to contain a SQL injection vulnerability via the component admin_comment_news.php."}, {"lang": "es", "value": "Se descubrió que SeaCMS v13.3 contiene una vulnerabilidad de inyección SQL a través del componente admin_comment_news.php."}], "references": [{"url": "https://github.com/202110420106/CVE/blob/master/seacms/seacms_comment_news_sql.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
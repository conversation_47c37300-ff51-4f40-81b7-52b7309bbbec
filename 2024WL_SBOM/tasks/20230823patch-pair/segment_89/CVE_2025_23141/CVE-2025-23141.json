{"cve_id": "CVE-2025-23141", "published_date": "2025-05-01T13:15:49.910", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: x86: Acquire SRCU in KVM_GET_MP_STATE to protect guest memory accesses\n\nAcquire a lock on kvm->srcu when userspace is getting MP state to handle a\nrather extreme edge case where \"accepting\" APIC events, i.e. processing\npending INIT or SIPI, can trigger accesses to guest memory.  If the vCPU\nis in L2 with INIT *and* a TRIPLE_FAULT request pending, then getting MP\nstate will trigger a nested VM-Exit by way of ->check_nested_events(), and\nemuating the nested VM-Exit can access guest memory.\n\nThe splat was originally hit by <PERSON><PERSON><PERSON><PERSON><PERSON> on a Google-internal kernel, and\nreproduced on an upstream kernel by hacking the triple_fault_event_test\nselftest to stuff a pending INIT, store an MSR on VM-Exit (to generate a\nmemory access on VMX), and do vcpu_mp_state_get() to trigger the scenario.\n\n  =============================\n  WARNING: suspicious RCU usage\n  6.14.0-rc3-b112d356288b-vmx/pi_lockdep_false_pos-lock #3 Not tainted\n  -----------------------------\n  include/linux/kvm_host.h:1058 suspicious rcu_dereference_check() usage!\n\n  other info that might help us debug this:\n\n  rcu_scheduler_active = 2, debug_locks = 1\n  1 lock held by triple_fault_ev/1256:\n   #0: ffff88810df5a330 (&vcpu->mutex){+.+.}-{4:4}, at: kvm_vcpu_ioctl+0x8b/0x9a0 [kvm]\n\n  stack backtrace:\n  CPU: 11 UID: 1000 PID: 1256 Comm: triple_fault_ev Not tainted 6.14.0-rc3-b112d356288b-vmx #3\n  Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 0.0.0 02/06/2015\n  Call Trace:\n   <TASK>\n   dump_stack_lvl+0x7f/0x90\n   lockdep_rcu_suspicious+0x144/0x190\n   kvm_vcpu_gfn_to_memslot+0x156/0x180 [kvm]\n   kvm_vcpu_read_guest+0x3e/0x90 [kvm]\n   read_and_check_msr_entry+0x2e/0x180 [kvm_intel]\n   __nested_vmx_vmexit+0x550/0xde0 [kvm_intel]\n   kvm_check_nested_events+0x1b/0x30 [kvm]\n   kvm_apic_accept_events+0x33/0x100 [kvm]\n   kvm_arch_vcpu_ioctl_get_mpstate+0x30/0x1d0 [kvm]\n   kvm_vcpu_ioctl+0x33e/0x9a0 [kvm]\n   __x64_sys_ioctl+0x8b/0xb0\n   do_syscall_64+0x6c/0x170\n   entry_SYSCALL_64_after_hwframe+0x4b/0x53\n   </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: x86: Adquisición de SRCU en KVM_GET_MP_STATE para proteger los accesos a la memoria del invitado. Adquisición de un bloqueo en kvm-&gt;srcu cuando el espacio de usuario obtiene el estado MP para gestionar un caso extremo en el que la \"aceptación\" de eventos APIC, es decir, el procesamiento de INIT o SIPI pendientes, puede desencadenar accesos a la memoria del invitado. Si la vCPU está en L2 con INIT *y* una solicitud TRIPLE_FAULT pendiente, obtener el estado MP activará una salida de máquina virtual anidada mediante -&gt;check_nested_events(), y la emulación de la salida de máquina virtual anidada puede acceder a la memoria del invitado. El splat fue alcanzado originalmente por syzkaller en un kernel interno de Google, y reproducido en un kernel ascendente hackeando la autoprueba triple_fault_event_test para rellenar un INIT pendiente, almacenar un MSR en VM-Exit (para generar un acceso a memoria en VMX), y hacer vcpu_mp_state_get() para activar el escenario. ============================== ADVERTENCIA: uso sospechoso de RCU 6.14.0-rc3-b112d356288b-vmx/pi_lockdep_false_pos-lock #3 No contaminado ----------------------------- include/linux/kvm_host.h:1058 ¡uso sospechoso de rcu_dereference_check()! Otra información que podría ayudarnos a depurar esto: rcu_scheduler_active = 2, debug_locks = 1 1 bloqueo mantenido por triple_fault_ev/1256: #0: ffff88810df5a330 (&amp;vcpu-&gt;mutex){+.+.}-{4:4}, en: kvm_vcpu_ioctl+0x8b/0x9a0 [kvm] seguimiento de pila: CPU: 11 UID: 1000 PID: 1256 Comm: triple_fault_ev No contaminado 6.14.0-rc3-b112d356288b-vmx #3 Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 0.0.0 02/06/2015 Seguimiento de llamadas:   dump_stack_lvl+0x7f/0x90 lockdep_rcu_suspicious+0x144/0x190 kvm_vcpu_gfn_to_memslot+0x156/0x180 [kvm] kvm_vcpu_read_guest+0x3e/0x90 [kvm] read_and_check_msr_entry+0x2e/0x180 [kvm_intel] __nested_vmx_vmexit+0x550/0xde0 [kvm_intel] kvm_check_nested_events+0x1b/0x30 [kvm] kvm_apic_accept_events+0x33/0x100 [kvm] kvm_arch_vcpu_ioctl_get_mpstate+0x30/0x1d0 [kvm] kvm_vcpu_ioctl+0x33e/0x9a0 [kvm] __x64_sys_ioctl+0x8b/0xb0 do_syscall_64+0x6c/0x170 entry_SYSCALL_64_after_hwframe+0x4b/0x53  "}], "references": [{"url": "https://git.kernel.org/stable/c/0357c8406dfa09430dd9858ebe813feb65524b6e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/592e040572f216d916f465047c8ce4a308fcca44", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7bc5c360375d28ba5ef6298b0d53e735c81d66a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8a3df0aa1087a89f5ce55f4aba816bfcb1ecf1be", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ef01cac401f18647d62720cf773d7bb0541827da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f5cbe725b7477b4cd677be1b86b4e08f90572997", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
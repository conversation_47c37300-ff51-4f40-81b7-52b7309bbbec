{"cve_id": "CVE-2025-44872", "published_date": "2025-05-02T15:15:49.227", "last_modified_date": "2025-05-27T14:21:40.517", "descriptions": [{"lang": "en", "value": "Tenda AC9 V15.03.06.42_multi was found to contain a command injection vulnerability in the formsetUsbUnload function via the deviceName parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se detectó que Tenda AC9 V15.03.06.42_multi contiene una vulnerabilidad de inyección de comandos en la función formsetUsbUnload mediante el parámetro deviceName. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Tenda_AC/AC9_formsetUsbUnload", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
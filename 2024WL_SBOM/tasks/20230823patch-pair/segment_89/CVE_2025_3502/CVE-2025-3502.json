{"cve_id": "CVE-2025-3502", "published_date": "2025-05-01T06:15:34.820", "last_modified_date": "2025-05-07T16:30:24.910", "descriptions": [{"lang": "en", "value": "The WP Maps  WordPress plugin before 4.7.2 does not sanitise and escape some of its Map settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento WP Maps para WordPress anterior a la versión 4.7.2 no depura ni escapa de algunas de sus configuraciones de mapas, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting Almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/dd436064-e611-4a4b-a873-67ed6029c46f/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
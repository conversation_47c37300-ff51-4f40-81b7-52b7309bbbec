{"cve_id": "CVE-2025-3748", "published_date": "2025-05-02T04:15:55.883", "last_modified_date": "2025-05-06T15:19:46.247", "descriptions": [{"lang": "en", "value": "The Taxonomy Chain Menu plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's pn_chain_menu shortcode in all versions up to, and including, 1.0.8 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Taxonomy Chain Menu para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del shortcode pn_chain_menu en todas las versiones hasta la 1.0.8 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/taxonomy-chain-menu/trunk/index.php#L190", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3284354/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/taxonomy-chain-menu/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/25afc28c-2814-4b49-add5-1d0ce5ff3a07?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
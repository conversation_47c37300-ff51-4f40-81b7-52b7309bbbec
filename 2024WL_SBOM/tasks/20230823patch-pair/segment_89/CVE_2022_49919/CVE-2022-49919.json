{"cve_id": "CVE-2022-49919", "published_date": "2025-05-01T15:16:17.380", "last_modified_date": "2025-05-07T13:27:09.437", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetfilter: nf_tables: release flow rule object from commit path\n\nNo need to postpone this to the commit release path, since no packets\nare walking over this object, this is accessed from control plane only.\nThis helped uncovered UAF triggered by races with the netlink notifier."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netfilter: nf_tables: liberación del objeto de regla de flujo desde la ruta de confirmación. No es necesario posponer esto hasta la ruta de liberación de la confirmación, ya que ningún paquete pasa por este objeto; solo se accede a él desde el plano de control. Esto ayudó a descubrir el UAF activado por ejecuciones con el notificador netlink."}], "references": [{"url": "https://git.kernel.org/stable/c/26b5934ff4194e13196bedcba373cd4915071d0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/4ab6f96444e936f5e4a936d5c0bc948144bcded3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6044791b7be707fd0e709f26e961a446424e5051", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/74fd5839467054cd9c4d050614d3ee8788386171", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b2d7a92aff0fbd93c29d2aa6451fb99f050e2c4e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
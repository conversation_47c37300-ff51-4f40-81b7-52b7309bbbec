{"cve_id": "CVE-2025-32886", "published_date": "2025-05-01T18:15:55.800", "last_modified_date": "2025-06-20T16:45:10.870", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna v1 devices with app 5.5.3 and firmware 0.25.5. All packets sent over RF are also sent over UART with USB Shell, allowing someone with local access to gain information about the protocol and intercept sensitive data."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna v1 con la aplicación 5.5.3 y el firmware 0.25.5. Todos los paquetes enviados por RF también se envían por UART con USB Shell, lo que permite que alguien con acceso local obtenga información sobre el protocolo e intercepte datos confidenciales."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
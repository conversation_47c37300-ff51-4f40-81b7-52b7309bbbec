{"cve_id": "CVE-2025-37773", "published_date": "2025-05-01T14:15:40.703", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nvirtiofs: add filesystem context source name check\n\nIn certain scenarios, for example, during fuzz testing, the source\nname may be NULL, which could lead to a kernel panic. Therefore, an\nextra check for the source name should be added."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: virtiofs: añadir comprobación del nombre de la fuente en el contexto del sistema de archivos. En ciertos escenarios, por ejemplo, durante las pruebas fuzz, el nombre de la fuente puede ser nulo, lo que podría provocar un pánico del kernel. Por lo tanto, se debe añadir una comprobación adicional del nombre de la fuente."}], "references": [{"url": "https://git.kernel.org/stable/c/599d1e2a6aecc44acf22fe7ea6f5e84a7e526abe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5ee09cdaf3414f6c92960714af46d3d90eede2f3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9d6dcf18a1b49990295ac8a05fd9bdfd27ccbf88", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a648d80f8d9b208beee03a2d9aa690cfacf1d41e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a94fd938df2b1628da66b498aa0eeb89593bc7a2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b84f13fdad10a543e2e65bab7e81b3f0bceabd67", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c3e31d613951c299487844c4d1686a933e8ee291", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f6ec52710dc5e156b774cbef5d0f5c99b1c53a80", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
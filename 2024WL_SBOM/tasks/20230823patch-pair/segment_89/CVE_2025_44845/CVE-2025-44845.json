{"cve_id": "CVE-2025-44845", "published_date": "2025-05-01T17:15:50.807", "last_modified_date": "2025-05-22T15:31:31.950", "descriptions": [{"lang": "en", "value": "TOTOLINK CA600-PoE V5.3c.6665_B20180820 was found to contain a command injection vulnerability in the NTPSyncWithHost function via the hostTime parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA600-PoE V5.3c.6665_B20180820 contenía una vulnerabilidad de inyección de comandos en la función NTPSyncWithHost mediante el parámetro hostTime. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA600-PoE/NTPSyncWithHost/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37787", "published_date": "2025-05-01T14:15:43.040", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: dsa: mv88e6xxx: avoid unregistering devlink regions which were never registered\n\n<PERSON> King reports that a system with mv88e6xxx dereferences a NULL\npointer when unbinding this driver:\nhttps://lore.kernel.org/netdev/<EMAIL>/\n\nThe crash seems to be in devlink_region_destroy(), which is not NULL\ntolerant but is given a NULL devlink global region pointer.\n\nAt least on some chips, some devlink regions are conditionally registered\nsince the blamed commit, see mv88e6xxx_setup_devlink_regions_global():\n\n\t\tif (cond && !cond(chip))\n\t\t\tcontinue;\n\nThese are MV88E6XXX_REGION_STU and MV88E6XXX_REGION_PVT. If the chip\ndoes not have an STU or PVT, it should crash like this.\n\nTo fix the issue, avoid unregistering those regions which are NULL, i.e.\nwere skipped at mv88e6xxx_setup_devlink_regions_global() time."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: dsa: mv88e6xxx: evitar anular el registro de regiones devlink que nunca se registraron Russell King informa que un sistema con mv88e6xxx desreferencia un puntero NULL al desvincular este controlador: https://lore.kernel.org/netdev/<EMAIL>/ El fallo parece estar en devlink_region_destroy(), que no tolera NULL pero se le asigna un puntero de región global devlink NULL. Al menos en algunos chips, algunas regiones devlink se registran condicionalmente desde la confirmación culpable, consulte mv88e6xxx_setup_devlink_regions_global(): if (cond &amp;&amp; !cond(chip)) continue; Estos son MV88E6XXX_REGION_STU y MV88E6XXX_REGION_PVT. Si el chip no tiene una STU o PVT, debería fallar de esta manera. Para solucionar el problema, evite anular el registro de las regiones nulas, es decir, las que se omitieron al ejecutar mv88e6xxx_setup_devlink_regions_global()."}], "references": [{"url": "https://git.kernel.org/stable/c/3665695e3572239dc233216f06b41f40cc771889", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5f5e95945bb1e08be7655da6acba648274db457d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ccdf5e24b276848eefb2755e05ff0f005a0c4a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b3c70dfe51f10df60db2646c08cebd24bcdc5247", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bbb80f004f7a90c3dcaacc982c59967457254a05", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c84f6ce918a9e6f4996597cbc62536bbf2247c96", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-58134", "published_date": "2025-05-03T16:15:19.310", "last_modified_date": "2025-06-17T14:15:38.223", "descriptions": [{"lang": "en", "value": "Mojolicious versions from 0.999922 through 9.40 for Perl uses a hard coded string, or the application's class name, as a HMAC session secret by default.\n\nThese predictable default secrets can be exploited to forge session cookies. An attacker who knows or guesses the secret could compute valid HMAC signatures for the session cookie, allowing them to tamper with or hijack another user’s session."}, {"lang": "es", "value": "Las versiones de Mojolicious de la 0.999922 a la 9.39 para Perl utilizan una cadena de código fijo, o el nombre de la clase de la aplicación, como secreto de sesión HMAC por defecto. Estos secretos predeterminados predecibles pueden explotarse para falsificar cookies de sesión. Un atacante que conozca o adivine el secreto podría calcular firmas HMAC válidas para la cookie de sesión, lo que le permitiría manipular o secuestrar la sesión de otro usuario."}], "references": [{"url": "https://github.com/hashcat/hashcat/pull/4090", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/mojolicious/mojo/pull/1791", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/mojolicious/mojo/pull/2200", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://medium.com/securing/baking-mojolicious-cookies-revisited-a-case-study-of-solving-security-problems-through-security-by-13da7c225802", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Third Party Advisory"]}, {"url": "https://metacpan.org/release/SRI/Mojolicious-9.39/source/lib/Mojolicious.pm#L51", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Product"]}, {"url": "https://www.synacktiv.com/publications/baking-mojolicious-cookies", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Exploit"]}]}
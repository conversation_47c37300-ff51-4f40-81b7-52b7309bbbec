{"cve_id": "CVE-2022-49908", "published_date": "2025-05-01T15:16:15.920", "last_modified_date": "2025-05-07T13:30:18.757", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: L2CAP: Fix memory leak in vhci_write\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> reports a memory leak as follows:\n====================================\nBUG: memory leak\nunreferenced object 0xffff88810d81ac00 (size 240):\n  [...]\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n  backtrace:\n    [<ffffffff838733d9>] __alloc_skb+0x1f9/0x270 net/core/skbuff.c:418\n    [<ffffffff833f742f>] alloc_skb include/linux/skbuff.h:1257 [inline]\n    [<ffffffff833f742f>] bt_skb_alloc include/net/bluetooth/bluetooth.h:469 [inline]\n    [<ffffffff833f742f>] vhci_get_user drivers/bluetooth/hci_vhci.c:391 [inline]\n    [<ffffffff833f742f>] vhci_write+0x5f/0x230 drivers/bluetooth/hci_vhci.c:511\n    [<ffffffff815e398d>] call_write_iter include/linux/fs.h:2192 [inline]\n    [<ffffffff815e398d>] new_sync_write fs/read_write.c:491 [inline]\n    [<ffffffff815e398d>] vfs_write+0x42d/0x540 fs/read_write.c:578\n    [<ffffffff815e3cdd>] ksys_write+0x9d/0x160 fs/read_write.c:631\n    [<ffffffff845e0645>] do_syscall_x64 arch/x86/entry/common.c:50 [inline]\n    [<ffffffff845e0645>] do_syscall_64+0x35/0xb0 arch/x86/entry/common.c:80\n    [<ffffffff84600087>] entry_SYSCALL_64_after_hwframe+0x63/0xcd\n====================================\n\nHCI core will uses hci_rx_work() to process frame, which is queued to\nthe hdev->rx_q tail in hci_recv_frame() by HCI driver.\n\nYet the problem is that, HCI core may not free the skb after handling\nACL data packets. To be more specific, when start fragment does not\ncontain the L2CAP length, HCI core just copies skb into conn->rx_skb and\nfinishes frame process in l2cap_recv_acldata(), without freeing the skb,\nwhich triggers the above memory leak.\n\nThis patch solves it by releasing the relative skb, after processing\nthe above case in l2cap_recv_acldata()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: L2CAP: Se corrige la pérdida de memoria en vhci_write Syzkaller informa de una pérdida de memoria de la siguiente manera: ===================================== ERROR: pérdida de memoria del objeto no referenciado 0xffff88810d81ac00 (tamaño 240): [...] volcado hexadecimal (primeros 32 bytes): 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ backtrace: [] __alloc_skb+0x1f9/0x270 net/core/skbuff.c:418 [] alloc_skb include/linux/skbuff.h:1257 [en línea] [] bt_skb_alloc include/net/bluetooth/bluetooth.h:469 [en línea] [] vhci_get_user drivers/bluetooth/hci_vhci.c:391 [en línea] [] vhci_write+0x5f/0x230 drivers/bluetooth/hci_vhci.c:511 [] call_write_iter include/linux/fs.h:2192 [en línea] [] new_sync_write fs/read_write.c:491 [en línea] [] vfs_write+0x42d/0x540 fs/read_write.c:578 [] ksys_write+0x9d/0x160 fs/read_write.c:631 [] do_syscall_x64 arch/x86/entry/common.c:50 [en línea] [] do_syscall_64+0x35/0xb0 arch/x86/entry/common.c:80 [] entry_SYSCALL_64_after_hwframe+0x63/0xcd ====================================== El núcleo HCI utiliza hci_rx_work() para procesar la trama, que el controlador HCI pone en cola en la cola hdev-&gt;rx_q en hci_recv_frame(). Sin embargo, el problema es que el núcleo HCI puede no liberar el skb después de procesar los paquetes de datos ACL. Para ser más específicos, cuando el fragmento de inicio no contiene la longitud L2CAP, el núcleo HCI simplemente copia el skb en conn-&gt;rx_skb y finaliza el procesamiento de la trama en l2cap_recv_acldata(), sin liberar el skb, lo que desencadena la pérdida de memoria mencionada anteriormente. Este parche lo resuelve liberando el skb relativo, después de procesar el caso mencionado en l2cap_recv_acldata()."}], "references": [{"url": "https://git.kernel.org/stable/c/5b4f039a2f487c5edae681d763fe1af505f84c13", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7c9524d929648935bac2bbb4c20437df8f9c3f42", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/aa16cac06b752e5f609c106735bd7838f444784c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
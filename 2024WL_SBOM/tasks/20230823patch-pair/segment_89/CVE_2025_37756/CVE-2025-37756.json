{"cve_id": "CVE-2025-37756", "published_date": "2025-05-01T13:15:54.370", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: tls: explicitly disallow disconnect\n\nsyz<PERSON> discovered that it can disconnect a TLS socket and then\nrun into all sort of unexpected corner cases. I have a vague\nrecollection of <PERSON> pointing this out to us a long time ago.\nSupporting disconnect is really hard, for one thing if offload\nis enabled we'd need to wait for all packets to be _acked_.\nDisconnect is not commonly used, disallow it.\n\nThe immediate problem syzbot run into is the warning in the strp,\nbut that's just the easiest bug to trigger:\n\n  WARNING: CPU: 0 PID: 5834 at net/tls/tls_strp.c:486 tls_strp_msg_load+0x72e/0xa80 net/tls/tls_strp.c:486\n  RIP: 0010:tls_strp_msg_load+0x72e/0xa80 net/tls/tls_strp.c:486\n  Call Trace:\n   <TASK>\n   tls_rx_rec_wait+0x280/0xa60 net/tls/tls_sw.c:1363\n   tls_sw_recvmsg+0x85c/0x1c30 net/tls/tls_sw.c:2043\n   inet6_recvmsg+0x2c9/0x730 net/ipv6/af_inet6.c:678\n   sock_recvmsg_nosec net/socket.c:1023 [inline]\n   sock_recvmsg+0x109/0x280 net/socket.c:1045\n   __sys_recvfrom+0x202/0x380 net/socket.c:2237"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: tls: explícitamente deshabilitar la desconexión. syzbot descubrió que puede desconectar un socket TLS y luego encontrarse con todo tipo de casos inesperados. Recuerdo vagamente que Eric nos lo señaló hace mucho tiempo. Admitir la desconexión es muy difícil; por ejemplo, si la descarga está habilitada, tendríamos que esperar a que se ackearan todos los paquetes. La desconexión no se usa comúnmente; deshabilitarla. El problema inmediato con el que se encuentra syzbot es la advertencia en el strp, pero ese es simplemente el error más fácil de activar: ADVERTENCIA: CPU: 0 PID: 5834 en net/tls/tls_strp.c:486 tls_strp_msg_load+0x72e/0xa80 net/tls/tls_strp.c:486 RIP: 0010:tls_strp_msg_load+0x72e/0xa80 net/tls/tls_strp.c:486 Rastreo de llamada:  tls_rx_rec_wait+0x280/0xa60 net/tls/tls_sw.c:1363 tls_sw_recvmsg+0x85c/0x1c30 net/tls/tls_sw.c:2043 inet6_recvmsg+0x2c9/0x730 net/ipv6/af_inet6.c:678 sock_recvmsg_nosec net/socket.c:1023 [en línea] sock_recvmsg+0x109/0x280 net/socket.c:1045 __sys_recvfrom+0x202/0x380 net/socket.c:2237"}], "references": [{"url": "https://git.kernel.org/stable/c/2bcad8fefcecdd5f005d8c550b25d703c063c34a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5071a1e606b30c0c11278d3c6620cd6a24724cf6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7bdcf5bc35ae59fc4a0fa23276e84b4d1534a3cf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8513411ec321942bd3cfed53d5bb700665c67d86", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9fcbca0f801580cbb583e9cb274e2c7fbe766ca6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ac91c6125468be720eafde9c973994cb45b61d44", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c665bef891e8972e1d3ce5bbc0d42a373346a2c3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f3ce4d3f874ab7919edca364c147ac735f9f1d04", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
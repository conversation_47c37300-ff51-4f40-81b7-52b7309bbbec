{"cve_id": "CVE-2022-49890", "published_date": "2025-05-01T15:16:14.000", "last_modified_date": "2025-05-07T13:19:54.100", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncapabilities: fix potential memleak on error path from vfs_getxattr_alloc()\n\nIn cap_inode_getsecurity(), we will use vfs_getxattr_alloc() to\ncomplete the memory allocation of tmpbuf, if we have completed\nthe memory allocation of tmpbuf, but failed to call handler->get(...),\nthere will be a memleak in below logic:\n\n  |-- ret = (int)vfs_getxattr_alloc(mnt_userns, ...)\n    |           /* ^^^ alloc for tmpbuf */\n    |-- value = krealloc(*xattr_value, error + 1, flags)\n    |           /* ^^^ alloc memory */\n    |-- error = handler->get(handler, ...)\n    |           /* error! */\n    |-- *xattr_value = value\n    |           /* xattr_value is &tmpbuf (memory leak!) */\n\nSo we will try to free(tmpbuf) after vfs_getxattr_alloc() fails to fix it.\n\n[PM: subject line and backtrace tweaks]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: capacidades: reparar posible fuga de memoria en la ruta de error de vfs_getxattr_alloc() En cap_inode_getsecurity(), utilizaremos vfs_getxattr_alloc() para completar la asignación de memoria de tmpbuf, si hemos completado la asignación de memoria de tmpbuf, pero no pudimos llamar a handler-&gt;get(...), habrá una fuga de memoria en la siguiente lógica: |-- ret = (int)vfs_getxattr_alloc(mnt_userns, ...) | /* ^^^ asignar para tmpbuf */ |-- valor = krealloc(*xattr_value, error + 1, flags) | /* ^^^ asignar memoria */ |-- error = handler-&gt;get(handler, ...) | /* ¡error! */ |-- *xattr_value = valor | /* xattr_value es &amp;tmpbuf (¡pérdida de memoria!) */ Intentaremos liberar (tmpbuf) después de que vfs_getxattr_alloc() no pueda solucionarlo. [MP: línea de asunto y ajustes de backtrace]"}], "references": [{"url": "https://git.kernel.org/stable/c/0c3e6288da650d1ec911a259c77bc2d88e498603", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2de8eec8afb75792440b8900a01d52b8f6742fd1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6bb00eb21c0fbf18e5d3538c9ff0cf63fd0ace85", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7480aeff0093d8c54377553ec6b31110bea37b4d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8cf0a1bc12870d148ae830a4ba88cfdf0e879cee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/90577bcc01c4188416a47269f8433f70502abe98", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cdf01c807e974048c43c7fd3ca574f6086a57906", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
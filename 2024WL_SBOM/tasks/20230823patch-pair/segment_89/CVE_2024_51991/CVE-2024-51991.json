{"cve_id": "CVE-2024-51991", "published_date": "2025-05-05T17:18:44.853", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "October is a Content Management System (CMS) and web platform. A vulnerability in versions prior to 3.7.5 affects authenticated administrators with sites that have the `media.clean_vectors` configuration enabled. This configuration will sanitize SVG files uploaded using the media manager. This vulnerability allows an authenticated user to bypass this protection by uploading it with a permitted extension (for example, .jpg or .png) and later modifying it to the .svg extension. This vulnerability assumes a trusted user will attack another trusted user and cannot be actively exploited without access to the administration panel and interaction from the other user. This issue has been patched in v3.7.5."}, {"lang": "es", "value": "October es un Sistema de Gestión de Contenido (CMS) y una plataforma web. Una vulnerabilidad en versiones anteriores a la 3.7.5 afecta a los administradores autenticados de sitios que tienen habilitada la configuración `media.clean_vectors`. Esta configuración depura los archivos SVG subidos mediante el gestor de contenido multimedia. Esta vulnerabilidad permite a un usuario autenticado eludir esta protección subiéndolos con una extensión permitida (por ejemplo, .jpg o .png) y posteriormente modificándolos a la extensión .svg. Esta vulnerabilidad presupone que un usuario de confianza atacará a otro usuario de confianza y no puede explotarse activamente sin acceso al panel de administración ni interacción del otro usuario. Este problema se ha corregido en la v3.7.5."}], "references": [{"url": "https://github.com/octobercms/october/security/advisories/GHSA-96hh-8hx5-cpw7", "source": "<EMAIL>", "tags": []}]}
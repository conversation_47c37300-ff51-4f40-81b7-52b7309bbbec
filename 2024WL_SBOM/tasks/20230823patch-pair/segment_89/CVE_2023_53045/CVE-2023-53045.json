{"cve_id": "CVE-2023-53045", "published_date": "2025-05-02T16:15:23.590", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: gadget: u_audio: don't let userspace block driver unbind\n\nIn the unbind callback for f_uac1 and f_uac2, a call to snd_card_free()\nvia g_audio_cleanup() will disconnect the card and then wait for all\nresources to be released, which happens when the refcount falls to zero.\nSince userspace can keep the refcount incremented by not closing the\nrelevant file descriptor, the call to unbind may block indefinitely.\nThis can cause a deadlock during reboot, as evidenced by the following\nblocked task observed on my machine:\n\n  task:reboot  state:D stack:0   pid:2827  ppid:569    flags:0x0000000c\n  Call trace:\n   __switch_to+0xc8/0x140\n   __schedule+0x2f0/0x7c0\n   schedule+0x60/0xd0\n   schedule_timeout+0x180/0x1d4\n   wait_for_completion+0x78/0x180\n   snd_card_free+0x90/0xa0\n   g_audio_cleanup+0x2c/0x64\n   afunc_unbind+0x28/0x60\n   ...\n   kernel_restart+0x4c/0xac\n   __do_sys_reboot+0xcc/0x1ec\n   __arm64_sys_reboot+0x28/0x30\n   invoke_syscall+0x4c/0x110\n   ...\n\nThe issue can also be observed by opening the card with arecord and\nthen stopping the process through the shell before unbinding:\n\n  # arecord -D hw:UAC2Gadget -f S32_LE -c 2 -r 48000 /dev/null\n  Recording WAVE '/dev/null' : Signed 32 bit Little Endian, Rate 48000 Hz, Stereo\n  ^Z[1]+  Stopped                    arecord -D hw:UAC2Gadget -f S32_LE -c 2 -r 48000 /dev/null\n  # echo gadget.0 > /sys/bus/gadget/drivers/configfs-gadget/unbind\n  (observe that the unbind command never finishes)\n\nFix the problem by using snd_card_free_when_closed() instead, which will\nstill disconnect the card as desired, but defer the task of freeing the\nresources to the core once userspace closes its file descriptor."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: gadget: u_audio: no permitir que el espacio de usuario bloquee la desvinculación del controlador. En la llamada de desvinculación para f_uac1 y f_uac2, una llamada a snd_card_free() mediante g_audio_cleanup() desconectará la tarjeta y esperará a que se liberen todos los recursos, lo que ocurre cuando el recuento de referencias llega a cero. Dado que el espacio de usuario puede mantener el recuento de referencias incrementado al no cerrar el descriptor de archivo correspondiente, la llamada a desvinculación podría bloquearse indefinidamente. Esto puede causar un bloqueo durante el reinicio, como lo demuestra la siguiente tarea bloqueada observada en mi máquina: task:reboot state:D stack:0 pid:2827 ppid:569 flags:0x0000000c Rastreo de llamadas: __switch_to+0xc8/0x140 __schedule+0x2f0/0x7c0 schedule+0x60/0xd0 schedule_timeout+0x180/0x1d4 wait_for_completion+0x78/0x180 snd_card_free+0x90/0xa0 g_audio_cleanup+0x2c/0x64 afunc_unbind+0x28/0x60 ... kernel_restart+0x4c/0xac __do_sys_reboot+0xcc/0x1ec __arm64_sys_reboot+0x28/0x30 invoke_syscall+0x4c/0x110 ... El problema también se puede observar al abrir la tarjeta con arecord y luego detener el proceso a través del shell antes de desvincular: # arecord -D hw:UAC2Gadget -f S32_LE -c 2 -r 48000 /dev/null Recording WAVE '/dev/null' : Signed 32 bit Little Endian, Rate 48000 Hz, Stereo ^Z[1]+ Stopped arecord -D hw:UAC2Gadget -f S32_LE -c 2 -r 48000 /dev/null # echo gadget.0 &gt; /sys/bus/gadget/drivers/configfs-gadget/unbind (observe que el comando de desvinculación nunca finaliza) Corrija el problema usando snd_card_free_when_closed() en su lugar, que aún desconectará la tarjeta como se desea, pero pospondrá la tarea de liberar los recursos al núcleo una vez que el espacio de usuario cierre su descriptor de archivo."}], "references": [{"url": "https://git.kernel.org/stable/c/0eda2004f38d95ef5715d62be884cd344260535b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3256e152b645fc1e788ba44c2d8ced690113e3e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/33f341c1fc60e172a3515c51bdabee11e83d1ee9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3bc7324e4911351e39c54a62e6ca46321cb10faf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3e016ef2e72da93a2ea7afbb45de1b481b44d761", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/43ca70753dfffd517d2af126da28690f8f615605", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c67ed9ad9b83e453e808f9b31a931a20a25629b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b131989797f7287d7fdadb2bababc05a15d44750", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
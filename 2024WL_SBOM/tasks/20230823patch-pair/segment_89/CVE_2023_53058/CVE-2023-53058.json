{"cve_id": "CVE-2023-53058", "published_date": "2025-05-02T16:15:24.867", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/mlx5: E-Switch, Fix an Oops in error handling code\n\nThe error handling dereferences \"vport\".  There is nothing we can do if\nit is an error pointer except returning the error code."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/mlx5: E-Switch. Se corrige un error en el código de gestión de errores. El código de gestión de errores desreferencia \"vport\". Si se trata de un puntero de error, no podemos hacer nada más que devolver el código de error."}], "references": [{"url": "https://git.kernel.org/stable/c/1a9853a7437a22fd849347008fb3c85087906b56", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/388188fb58bef9e7f3ca4f8970f03d493b66909f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5eadc80328298ef7beaaf0cd96791667d3b485ca", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/640fcdbcf27fc62de9223f958ceb4e897a00e791", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c4c977935b2fc60084b3735737d17a06e7ba1bd0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
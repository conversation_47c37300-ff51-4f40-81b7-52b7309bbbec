{"cve_id": "CVE-2025-23161", "published_date": "2025-05-01T13:15:52.060", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nPCI: vmd: Make vmd_dev::cfg_lock a raw_spinlock_t type\n\nThe access to the PCI config space via pci_ops::read and pci_ops::write is\na low-level hardware access. The functions can be accessed with disabled\ninterrupts even on PREEMPT_RT. The pci_lock is a raw_spinlock_t for this\npurpose.\n\nA spinlock_t becomes a sleeping lock on PREEMPT_RT, so it cannot be\nacquired with disabled interrupts. The vmd_dev::cfg_lock is accessed in\nthe same context as the pci_lock.\n\nMake vmd_dev::cfg_lock a raw_spinlock_t type so it can be used with\ninterrupts disabled.\n\nThis was reported as:\n\n  BUG: sleeping function called from invalid context at kernel/locking/spinlock_rt.c:48\n  Call Trace:\n   rt_spin_lock+0x4e/0x130\n   vmd_pci_read+0x8d/0x100 [vmd]\n   pci_user_read_config_byte+0x6f/0xe0\n   pci_read_config+0xfe/0x290\n   sysfs_kf_bin_read+0x68/0x90\n\n[bigeasy: reword commit message]\nTested-off-by: <PERSON> <<EMAIL>>\n[k<PERSON><PERSON><PERSON><PERSON><PERSON>: commit log]\n[bhelgaas: add back report info from\nhttps://lore.kernel.org/lkml/<EMAIL>/]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: PCI: vmd: Convertir vmd_dev::cfg_lock en tipo raw_spinlock_t El acceso al espacio de configuración PCI mediante pci_ops::read y pci_ops::write es un acceso de hardware de bajo nivel. Se puede acceder a las funciones con interrupciones deshabilitadas incluso en PREEMPT_RT. El pci_lock es un raw_spinlock_t para este propósito. Un spinlock_t se convierte en un bloqueo inactivo en PREEMPT_RT, por lo que no se puede adquirir con interrupciones deshabilitadas. Se accede a vmd_dev::cfg_lock en el mismo contexto que el pci_lock. Convertir vmd_dev::cfg_lock en un tipo raw_spinlock_t para que pueda usarse con interrupciones deshabilitadas. Esto se informó como: ERROR: función inactiva llamada desde un contexto no válido en kernel/locking/spinlock_rt.c:48 Rastreo de llamadas: rt_spin_lock+0x4e/0x130 vmd_pci_read+0x8d/0x100 [vmd] pci_user_read_config_byte+0x6f/0xe0 pci_read_config+0xfe/0x290 sysfs_kf_bin_read+0x68/0x90 [bigeasy: reformular el mensaje de confirmación] Probado por: Luis Claudio R. Goncalves  [kwilczynski: registro de confirmaciones] [bhelgaas: agregar información del informe de https://lore.kernel.org/lkml/<EMAIL>/]"}], "references": [{"url": "https://git.kernel.org/stable/c/13e5148f70e81991acbe0bab5b1b50ba699116e7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/18056a48669a040bef491e63b25896561ee14d90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/20d0a9062c031068fa39f725a32f182b709b5525", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2358046ead696ca5c7c628d6c0e2c6792619a3e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5c3cfcf0b4bf43530788b08a8eaf7896ec567484", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c250262d6485ca333e9821f85b07eb383ec546b1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c2968c812339593ac6e2bdd5cc3adabe3f05fa53", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
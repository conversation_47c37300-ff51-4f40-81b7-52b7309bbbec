{"cve_id": "CVE-2025-44072", "published_date": "2025-05-05T22:15:17.077", "last_modified_date": "2025-05-13T20:05:16.493", "descriptions": [{"lang": "en", "value": "SeaCMS v13.3 was discovered to contain a SQL injection vulnerability via the component admin_manager.php."}, {"lang": "es", "value": "Se descubrió que SeaCMS v13.3 contiene una vulnerabilidad de inyección SQL a través del componente admin_manager.php."}], "references": [{"url": "https://github.com/202110420106/CVE/blob/master/seacms/seacms_manage_sql.md", "source": "<EMAIL>", "tags": ["Exploit"]}]}
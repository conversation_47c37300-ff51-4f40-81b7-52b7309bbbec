{"cve_id": "CVE-2025-4191", "published_date": "2025-05-02T00:15:19.440", "last_modified_date": "2025-06-06T07:15:27.087", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Employee Record Management System 1.3 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /editmyeducation.php. The manipulation of the argument coursepg/yophsc leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Employee Record Management System 1.3, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /editmyeducation.php. La manipulación del argumento coursepg provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ideal-valli/myCVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306805", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.306805", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561816", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.591204", "source": "<EMAIL>", "tags": []}]}
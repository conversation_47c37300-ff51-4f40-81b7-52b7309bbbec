{"cve_id": "CVE-2023-53073", "published_date": "2025-05-02T16:15:26.330", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nperf/x86/amd/core: Always clear status for idx\n\nThe variable 'status' (which contains the unhandled overflow bits) is\nnot being properly masked in some cases, displaying the following\nwarning:\n\n  WARNING: CPU: 156 PID: 475601 at arch/x86/events/amd/core.c:972 amd_pmu_v2_handle_irq+0x216/0x270\n\nThis seems to be happening because the loop is being continued before\nthe status bit being unset, in case x86_perf_event_set_period()\nreturns 0. This is also causing an inconsistency because the \"handled\"\ncounter is incremented, but the status bit is not cleaned.\n\nMove the bit cleaning together above, together when the \"handled\"\ncounter is incremented."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: perf/x86/amd/core: Siempre borrar el estado de idx. La variable 'status' (que contiene los bits de desbordamiento no controlados) no se enmascara correctamente en algunos casos, mostrando la siguiente advertencia: ADVERTENCIA: CPU: 156 PID: 475601 en arch/x86/events/amd/core.c:972 amd_pmu_v2_handle_irq+0x216/0x270. Esto parece estar sucediendo porque el bucle continúa antes de que se desactive el bit de estado, en caso de que x86_perf_event_set_period() devuelva 0. Esto también causa una inconsistencia porque el contador \"controlado\" se incrementa, pero el bit de estado no se limpia. Mueva la limpieza de bits junto arriba, junto cuando se incrementa el contador \"controlado\"."}], "references": [{"url": "https://git.kernel.org/stable/c/263f5ecaf7080513efc248ec739b6d9e00f4129f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9d4c7b1f12e101d6d6253092588b127416ddfb6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab33a8f7649b0324639a336e1081aaea51a4523e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37749", "published_date": "2025-05-01T13:15:53.633", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: ppp: Add bound checking for skb data on ppp_sync_txmung\n\nEnsure we have enough data in linear buffer from skb before accessing\ninitial bytes. This prevents potential out-of-bounds accesses\nwhen processing short packets.\n\nWhen ppp_sync_txmung receives an incoming package with an empty\npayload:\n(remote) gef➤  p *(struct pppoe_hdr *) (skb->head + skb->network_header)\n$18 = {\n\ttype = 0x1,\n\tver = 0x1,\n\tcode = 0x0,\n\tsid = 0x2,\n        length = 0x0,\n\ttag = 0xffff8880371cdb96\n}\n\nfrom the skb struct (trimmed)\n      tail = 0x16,\n      end = 0x140,\n      head = 0xffff88803346f400 \"4\",\n      data = 0xffff88803346f416 \":\\377\",\n      truesize = 0x380,\n      len = 0x0,\n      data_len = 0x0,\n      mac_len = 0xe,\n      hdr_len = 0x0,\n\nit is not safe to access data[2].\n\n[<EMAIL>: fixed subj typo]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: ppp: Se ha añadido la comprobación de los límites para datos de skb en ppp_sync_txmung. Se garantiza que haya suficientes datos en el búfer lineal de skb antes de acceder a los bytes iniciales. Esto evita posibles accesos fuera de los límites al procesar paquetes cortos. Cuando ppp_sync_txmung recibe un paquete entrante con un payload vacía: (remoto) gef? p *(struct pppoe_hdr *) (skb-&gt;head + skb-&gt;network_header) $18 = { type = 0x1, ver = 0x1, code = 0x0, sid = 0x2, length = 0x0, tag = 0xffff8880371cdb96 } de la estructura skb (recortada) tail = 0x16, end = 0x140, head = 0xffff88803346f400 \"4\", data = 0xffff88803346f416 \":\\377\", truesize = 0x380, len = 0x0, data_len = 0x0, mac_len = 0xe, hdr_len = 0x0, no es seguro acceder a los datos[2]. [<EMAIL>: error tipográfico corregido]"}], "references": [{"url": "https://git.kernel.org/stable/c/1f6eb9fa87a781d5370c0de7794ae242f1a95ee5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/529401c8f12ecc35f9ea5d946d5a5596cf172b48", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6e8a6bf43cea4347121ab21bb1ed8d7bef7e732e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/99aa698dec342a07125d733e39aab4394b3b7e05", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aabc6596ffb377c4c9c8f335124b92ea282c9821", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b4c836d33ca888695b2f2665f948bc1b34fbd533", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b78f2b458f56a5a4d976c8e01c43dbf58d3ea2ca", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de5a4f0cba58625e88b7bebd88f780c8c0150997", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fbaffe8bccf148ece8ad67eb5d7aa852cabf59c8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
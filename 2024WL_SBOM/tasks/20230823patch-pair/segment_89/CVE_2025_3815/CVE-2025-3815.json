{"cve_id": "CVE-2025-3815", "published_date": "2025-05-03T08:15:31.040", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The SurveyJS plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘id’ parameter in all versions up to, and including, 1.12.32 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento SurveyJS para WordPress es vulnerable a Cross-Site Scripting almacenado a través del parámetro 'id' en todas las versiones hasta la 1.12.32 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://github.com/surveyjs/surveyjs-wordpress", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/surveyjs/surveyjs-wordpress/commit/6c332319c82c32d7148f77ed7ee20a9c6a5dc179", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/surveyjs/trunk/initializer.php#L165", "source": "<EMAIL>", "tags": []}, {"url": "https://surveyjs.io/stay-updated/release-notes", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c4285590-9c2f-4189-8b47-09378d8a2432?source=cve", "source": "<EMAIL>", "tags": []}]}
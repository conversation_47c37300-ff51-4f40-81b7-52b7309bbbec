{"cve_id": "CVE-2023-53065", "published_date": "2025-05-02T16:15:25.580", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nperf/core: Fix perf_output_begin parameter is incorrectly invoked in perf_event_bpf_output\n\ns<PERSON><PERSON><PERSON><PERSON> reportes a KASAN issue with stack-out-of-bounds.\nThe call trace is as follows:\n  dump_stack+0x9c/0xd3\n  print_address_description.constprop.0+0x19/0x170\n  __kasan_report.cold+0x6c/0x84\n  kasan_report+0x3a/0x50\n  __perf_event_header__init_id+0x34/0x290\n  perf_event_header__init_id+0x48/0x60\n  perf_output_begin+0x4a4/0x560\n  perf_event_bpf_output+0x161/0x1e0\n  perf_iterate_sb_cpu+0x29e/0x340\n  perf_iterate_sb+0x4c/0xc0\n  perf_event_bpf_event+0x194/0x2c0\n  __bpf_prog_put.constprop.0+0x55/0xf0\n  __cls_bpf_delete_prog+0xea/0x120 [cls_bpf]\n  cls_bpf_delete_prog_work+0x1c/0x30 [cls_bpf]\n  process_one_work+0x3c2/0x730\n  worker_thread+0x93/0x650\n  kthread+0x1b8/0x210\n  ret_from_fork+0x1f/0x30\n\ncommit 267fb27352b6 (\"perf: Reduce stack usage of perf_output_begin()\")\nuse on-stack struct perf_sample_data of the caller function.\n\nHowever, perf_event_bpf_output uses incorrect parameter to convert\nsmall-sized data (struct perf_bpf_event) into large-sized data\n(struct perf_sample_data), which causes memory overwriting occurs in\n__perf_event_header__init_id."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: perf/core: Se solucionó que el parámetro perf_output_begin se invocara incorrectamente en perf_event_bpf_output syzkaller informa un problema de KASAN con una pila fuera de los límites. El seguimiento de la llamada es el siguiente: dump_stack+0x9c/0xd3 print_address_description.constprop.0+0x19/0x170 __kasan_report.cold+0x6c/0x84 kasan_report+0x3a/0x50 __perf_event_header__init_id+0x34/0x290 perf_event_header__init_id+0x48/0x60 perf_output_begin+0x4a4/0x560 perf_event_bpf_output+0x161/0x1e0 perf_iterate_sb_cpu+0x29e/0x340 perf_iterate_sb+0x4c/0xc0 perf_event_bpf_event+0x194/0x2c0 __bpf_prog_put.constprop.0+0x55/0xf0 __cls_bpf_delete_prog+0xea/0x120 [cls_bpf] cls_bpf_delete_prog_work+0x1c/0x30 [cls_bpf] process_one_work+0x3c2/0x730 workers_thread+0x93/0x650 kthread+0x1b8/0x210 ret_from_fork+0x1f/0x30 commit 267fb27352b6 (\"perf: Reducir el uso de la pila de perf_output_begin()\") usa la estructura en pila perf_sample_data de la función que llama. Sin embargo, perf_event_bpf_output utiliza un parámetro incorrecto para convertir datos de tamaño pequeño (struct perf_bpf_event) en datos de tamaño grande (struct perf_sample_data), lo que provoca que se sobrescriba la memoria en __perf_event_header__init_id."}], "references": [{"url": "https://git.kernel.org/stable/c/3a776fddb4e5598c8bfcd4ad094fba34f9856fc9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ac5f88642cb211152041f84a985309e9af4baf59", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ddcf8320003638a06eb1e46412e045d0c5701575", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb81a2ed4f52be831c9fb879752d89645a312c13", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ff8137727a2af4ad5f6e6c8b9f7ec5e8db9da86c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
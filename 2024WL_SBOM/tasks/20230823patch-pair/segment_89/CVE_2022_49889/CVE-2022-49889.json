{"cve_id": "CVE-2022-49889", "published_date": "2025-05-01T15:16:13.897", "last_modified_date": "2025-05-07T13:19:59.030", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nring-buffer: Check for NULL cpu_buffer in ring_buffer_wake_waiters()\n\nOn some machines the number of listed CPUs may be bigger than the actual\nCPUs that exist. The tracing subsystem allocates a per_cpu directory with\naccess to the per CPU ring buffer via a cpuX file. But to save space, the\nring buffer will only allocate buffers for online CPUs, even though the\nCPU array will be as big as the nr_cpu_ids.\n\nWith the addition of waking waiters on the ring buffer when closing the\nfile, the ring_buffer_wake_waiters() now needs to make sure that the\nbuffer is allocated (with the irq_work allocated with it) before trying to\nwake waiters, as it will cause a NULL pointer dereference.\n\nWhile debugging this, I added a NULL check for the buffer itself (which is\nOK to do), and also NULL pointer checks against buffer->buffers (which is\nnot fine, and will WARN) as well as making sure the CPU number passed in\nis within the nr_cpu_ids (which is also not fine if it isn't).\n\n\nBugzilla: https://bugzilla.opensuse.org/show_bug.cgi?id=1204705"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ring-buffer: Comprobar si hay un cpu_buffer nulo en ring_buffer_wake_waiters() En algunas máquinas, el número de CPU listadas puede ser mayor que el de CPU reales existentes. El subsistema de rastreo asigna un directorio per_cpu con acceso al búfer de anillo por CPU a través de un archivo cpuX. Pero para ahorrar espacio, el búfer de anillo solo asignará búferes para las CPU en línea, aunque la matriz de CPU será tan grande como nr_cpu_ids. Con la adición de despertar a los que esperan en el búfer de anillo al cerrar el archivo, ring_buffer_wake_waiters() ahora debe asegurarse de que el búfer esté asignado (con el irq_work asignado con él) antes de intentar despertar a los que esperan, ya que provocará una desreferencia de puntero nulo. Durante la depuración, añadí una comprobación de valores nulos para el propio búfer (lo cual es correcto), así como comprobaciones de punteros nulos contra buffer-&gt;buffers (lo cual no es correcto y generará una advertencia), además de asegurarme de que el número de CPU introducido esté dentro del nr_cpu_ids (lo cual tampoco es correcto si no lo está). Bugzilla: https://bugzilla.opensuse.org/show_bug.cgi?id=1204705"}], "references": [{"url": "https://git.kernel.org/stable/c/49ca992f6e50d0f46ec9608f44e011cf3121f389", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7433632c9ff68a991bd0bc38cabf354e9d2de410", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b5074df412bf3df9d6ce096b6fa03eb1082d05c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
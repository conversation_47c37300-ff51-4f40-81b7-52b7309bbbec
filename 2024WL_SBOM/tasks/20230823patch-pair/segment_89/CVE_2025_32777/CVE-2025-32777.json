{"cve_id": "CVE-2025-32777", "published_date": "2025-04-30T19:15:55.353", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Volcano is a Kubernetes-native batch scheduling system. Prior to versions 1.11.2, 1.10.2, 1.9.1, 1.11.0-network-topology-preview.3, and 1.12.0-alpha.2, attacker compromise of either the Elastic service or the extender plugin can cause denial of service of the scheduler. This is a privilege escalation, because Volcano users may run their Elastic service and extender plugins in separate pods or nodes from the scheduler. In the Kubernetes security model, node isolation is a security boundary, and as such an attacker is able to cross that boundary in Volcano's case if they have compromised either the vulnerable services or the pod/node in which they are deployed. The scheduler will become unavailable to other users and workloads in the cluster. The scheduler will either crash with an unrecoverable OOM panic or freeze while consuming excessive amounts of memory. This issue has been patched in versions 1.11.2, 1.10.2, 1.9.1, 1.11.0-network-topology-preview.3, and 1.12.0-alpha.2."}, {"lang": "es", "value": "Volcano es un sistema de programación por lotes nativo de Kubernetes. En versiones anteriores a las 1.11.2, 1.10.2, 1.9.1, 1.11.0-network-topology-preview.3 y 1.12.0-alpha.2, si un atacante vulneraba el servicio Elastic o el complemento de extensión, podía provocar una denegación de servicio del programador. Esto supone una escalada de privilegios, ya que los usuarios de Volcano pueden ejecutar su servicio Elastic y los complementos de extensión en pods o nodos separados del programador. En el modelo de seguridad de Kubernetes, el aislamiento de nodos es un límite de seguridad y, por lo tanto, un atacante puede cruzarlo en el caso de Volcano si ha comprometido los servicios vulnerables o el pod/nodo en el que están implementados. El programador dejará de estar disponible para otros usuarios y cargas de trabajo del clúster. El programador se bloqueará con un pánico de OOM irrecuperable o se congelará consumiendo cantidades excesivas de memoria. Este problema se ha solucionado en las versiones 1.11.2, 1.10.2, 1.9.1, 1.11.0-network-topology-preview.3 y 1.12.0-alpha.2."}], "references": [{"url": "https://github.com/volcano-sh/volcano/releases/tag/v1.10.2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/volcano-sh/volcano/releases/tag/v1.11.0-network-topology-preview.3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/volcano-sh/volcano/releases/tag/v1.11.2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/volcano-sh/volcano/releases/tag/v1.12.0-alpha.2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/volcano-sh/volcano/releases/tag/v1.9.1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/volcano-sh/volcano/security/advisories/GHSA-hg79-fw4p-25p8", "source": "<EMAIL>", "tags": []}]}
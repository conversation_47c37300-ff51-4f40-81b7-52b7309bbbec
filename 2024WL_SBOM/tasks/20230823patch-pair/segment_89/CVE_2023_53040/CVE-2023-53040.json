{"cve_id": "CVE-2023-53040", "published_date": "2025-05-02T16:15:23.113", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nca8210: fix mac_len negative array access\n\nThis patch fixes a buffer overflow access of skb->data if\nieee802154_hdr_peek_addrs() fails."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ca8210: corrige el acceso negativo a la matriz mac_len. Este parche corrige un acceso de desbordamiento de búfer de skb-&gt;data si falla ieee802154_hdr_peek_addrs()."}], "references": [{"url": "https://git.kernel.org/stable/c/55d836f75778d2e2cafe37e023f9c106400bad4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5da4469a7aa011de614c3e2ae383c35a353a382e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c993779ea1d0cccdb3a5d7d45446dd229e610a3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7df72bedbdd1d02bb216e1f6eca0a16900238c4e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/918944526a386f186dd818ea6b0bcbed75d8c16b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d143e327c97241599c958d1ba9fbaa88c37db721", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d2b3bd0d4cadfdb7f3454d2aef9d5d9e8b48aae4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fd176a18db96d574d8c4763708abcec4444a08b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
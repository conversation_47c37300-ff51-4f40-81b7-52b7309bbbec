{"cve_id": "CVE-2023-53050", "published_date": "2025-05-02T16:15:24.093", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nthunderbolt: Fix memory leak in margining\n\nMemory for the usb4->margining needs to be relased for the upstream port\nof the router as well, even though the debugfs directory gets released\nwith the router device removal. Fix this."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Thunderbolt: Se corrige la pérdida de memoria en el margining. La memoria para usb4-&gt;margining también debe liberarse para el puerto ascendente del router, aunque el directorio debugfs se libera al eliminar el dispositivo del router. Se soluciona."}], "references": [{"url": "https://git.kernel.org/stable/c/0b357b360e671688f9bf38ff94300515b68bc247", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/acec726473822bc6b585961f4ca2a11fa7f28341", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f390095bbd131ec2dfb29792d9f6fd0f0656bfc0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-47154", "published_date": "2025-05-01T08:15:17.950", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "LibJS in Ladybird before f5a6704 mishandles the freeing of the vector that arguments_list references, leading to a use-after-free, and allowing remote attackers to execute arbitrary code via a crafted .js file. NOTE: the GitHub README says \"Ladybird is in a pre-alpha state, and only suitable for use by developers.\""}, {"lang": "es", "value": "LibJS en Ladybird anterior a f5a6704 gestiona incorrectamente la liberación del vector al que hace referencia arguments_list, lo que provoca un use-after-free y permite a atacantes remotos ejecutar código arbitrario mediante un archivo .js manipulado. NOTA: El README de GitHub indica que «Ladybird se encuentra en estado pre-alfa y solo es apto para desarrolladores»."}], "references": [{"url": "https://github.com/LadybirdBrowser/ladybird/commit/f5a670421954fc7130c3685b713c621b29516669", "source": "<EMAIL>", "tags": []}, {"url": "https://jessie.cafe/posts/pwning-ladybirds-libjs/", "source": "<EMAIL>", "tags": []}, {"url": "https://news.ycombinator.com/item?id=43852096", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-4176", "published_date": "2025-05-01T22:15:17.950", "last_modified_date": "2025-05-09T13:42:13.533", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Blood Bank & Donor Management System 2.4 and classified as critical. This vulnerability affects unknown code of the file /admin/request-received-bydonar.php. The manipulation of the argument searchdata leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Blood Bank &amp; Donor Management System 2.4, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/request-received-bydonar.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bluechips-zhao/myCVE/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306796", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.306796", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561764", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
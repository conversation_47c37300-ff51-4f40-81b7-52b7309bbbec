{"cve_id": "CVE-2023-53097", "published_date": "2025-05-02T16:15:28.643", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\npowerpc/iommu: fix memory leak with using debugfs_lookup()\n\nWhen calling debugfs_lookup() the result must have dput() called on it,\notherwise the memory will leak over time.  To make things simpler, just\ncall debugfs_lookup_and_remove() instead which handles all of the logic\nat once."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: powerpc/iommu: se corrige una fuga de memoria con debugfs_lookup(). Al llamar a debugfs_lookup(), se debe ejecutar dput() en el resultado; de lo contrario, la fuga de memoria se producirá con el tiempo. Para simplificar, simplemente llame a debugfs_lookup_and_remove(), que gestiona toda la lógica a la vez."}], "references": [{"url": "https://git.kernel.org/stable/c/24c1bd1cd0d1ff821fd7d2f01a1e648c7882dfc2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4050498c0ae3946c223fc63e9dd7b878b76611e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b505063910c134778202dfad9332dfcecb76bab3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3a62a35f903fd8be5b44542fe3901ec45f16757", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4270", "published_date": "2025-05-05T08:15:15.607", "last_modified_date": "2025-05-07T16:38:30.767", "descriptions": [{"lang": "en", "value": "A vulnerability was found in TOTOLINK A720R 4.1.5cu.374. It has been classified as problematic. Affected is an unknown function of the file /cgi-bin/cstecgi.cgi of the component Config Handler. The manipulation of the argument topicurl with the input getInitCfg/getSysStatusCfg leads to information disclosure. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en TOTOLINK A720R 4.1.5cu.374. Se ha clasificado como problemática. Se ve afectada una función desconocida del archivo /cgi-bin/cstecgi.cgi del componente Config Handler. La manipulación del argumento topicurl con la entrada getInitCfg/getSysStatusCfg provoca la divulgación de información. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/at0de/my_vulns/blob/main/TOTOLINK/A720R/getInitCfg.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/at0de/my_vulns/blob/main/TOTOLINK/A720R/getSysStatusCfg.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307374", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307374", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563442", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}
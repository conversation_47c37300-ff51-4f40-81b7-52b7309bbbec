{"cve_id": "CVE-2025-37765", "published_date": "2025-05-01T14:15:39.417", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/nouveau: prime: fix ttm_bo_delayed_delete oops\n\nFix an oops in ttm_bo_delayed_delete which results from dererencing a\ndangling pointer:\n\nOops: general protection fault, probably for non-canonical address 0x6b6b6b6b6b6b6b7b: 0000 [#1] PREEMPT SMP\nCPU: 4 UID: 0 PID: 1082 Comm: kworker/u65:2 Not tainted 6.14.0-rc4-00267-g505460b44513-dirty #216\nHardware name: LENOVO 82N6/LNVNB161216, BIOS GKCN65WW 01/16/2024\nWorkqueue: ttm ttm_bo_delayed_delete [ttm]\nRIP: 0010:dma_resv_iter_first_unlocked+0x55/0x290\nCode: 31 f6 48 c7 c7 00 2b fa aa e8 97 bd 52 ff e8 a2 c1 53 00 5a 85 c0 74 48 e9 88 01 00 00 4c 89 63 20 4d 85 e4 0f 84 30 01 00 00 <41> 8b 44 24 10 c6 43 2c 01 48 89 df 89 43 28 e8 97 fd ff ff 4c 8b\nRSP: 0018:ffffbf9383473d60 EFLAGS: 00010202\nRAX: 0000000000000001 RBX: ffffbf9383473d88 RCX: 0000000000000000\nRDX: 0000000000000000 RSI: 0000000000000000 RDI: 0000000000000000\nRBP: ffffbf9383473d78 R08: 0000000000000000 R09: 0000000000000000\nR10: 0000000000000000 R11: 0000000000000000 R12: 6b6b6b6b6b6b6b6b\nR13: ffffa003bbf78580 R14: ffffa003a6728040 R15: 00000000000383cc\nFS:  0000000000000000(0000) GS:ffffa00991c00000(0000) knlGS:0000000000000000\nCS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 0000758348024dd0 CR3: 000000012c259000 CR4: 0000000000f50ef0\nPKRU: 55555554\nCall Trace:\n <TASK>\n ? __die_body.cold+0x19/0x26\n ? die_addr+0x3d/0x70\n ? exc_general_protection+0x159/0x460\n ? asm_exc_general_protection+0x27/0x30\n ? dma_resv_iter_first_unlocked+0x55/0x290\n dma_resv_wait_timeout+0x56/0x100\n ttm_bo_delayed_delete+0x69/0xb0 [ttm]\n process_one_work+0x217/0x5c0\n worker_thread+0x1c8/0x3d0\n ? apply_wqattrs_cleanup.part.0+0xc0/0xc0\n kthread+0x10b/0x240\n ? kthreads_online_cpu+0x140/0x140\n ret_from_fork+0x40/0x70\n ? kthreads_online_cpu+0x140/0x140\n ret_from_fork_asm+0x11/0x20\n </TASK>\n\nThe cause of this is:\n\n- drm_prime_gem_destroy calls dma_buf_put(dma_buf) which releases the\n  reference to the shared dma_buf. The reference count is 0, so the\n  dma_buf is destroyed, which in turn decrements the corresponding\n  amdgpu_bo reference count to 0, and the amdgpu_bo is destroyed -\n  calling drm_gem_object_release then dma_resv_fini (which destroys the\n  reservation object), then finally freeing the amdgpu_bo.\n\n- nouveau_bo obj->bo.base.resv is now a dangling pointer to the memory\n  formerly allocated to the amdgpu_bo.\n\n- nouveau_gem_object_del calls ttm_bo_put(&nvbo->bo) which calls\n  ttm_bo_release, which schedules ttm_bo_delayed_delete.\n\n- ttm_bo_delayed_delete runs and dereferences the dangling resv pointer,\n  resulting in a general protection fault.\n\nFix this by moving the drm_prime_gem_destroy call from\nnouveau_gem_object_del to nouveau_bo_del_ttm. This ensures that it will\nbe run after ttm_bo_delayed_delete."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/nouveau: prime: fix ttm_bo_delayed_delete oops Corrige un oops en ttm_bo_delayed_delete que resulta de la desreferenciación de un puntero colgante: Oops: fallo de protección general, probablemente para dirección no canónica 0x6b6b6b6b6b6b6b7b: 0000 [#1] PREEMPT SMP CPU: 4 UID: 0 PID: 1082 Comm: kworker/u65:2 No contaminado 6.14.0-rc4-00267-g505460b44513-dirty #216 Nombre del hardware: LENOVO 82N6/LNVNB161216, BIOS GKCN65WW 16/01/2024 Cola de trabajo: ttm ttm_bo_delayed_delete [ttm] RIP: 0010:dma_resv_iter_first_unlocked+0x55/0x290 Código: 31 f6 48 c7 c7 00 2b fa aa e8 97 bd 52 ff e8 a2 c1 53 00 5a 85 c0 74 48 e9 88 01 00 00 4c 89 63 20 4d 85 e4 0f 84 30 01 00 00 &lt;41&gt; 8b 44 24 10 c6 43 2c 01 48 89 df 89 43 28 e8 97 fd ff ff 4c 8b RSP: 0018:ffffbf9383473d60 EFLAGS: 00010202 RAX: 0000000000000001 RBX: ffffbf9383473d88 RCX: 0000000000000000 RDX: 0000000000000000 RSI: 0000000000000000 RDI: 0000000000000000 RBP: ffffbf9383473d78 R08: 000000000000000 R09: 0000000000000000 R10: 000000000000000 R11: 000000000000000 R12: 6b6b6b6b6b6b6b6b R13: ffffa003bbf78580 R14: ffffa003a6728040 R15: 00000000000383cc FS: 0000000000000000(0000) GS:ffffa00991c00000(0000) knlGS:0000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 0000758348024dd0 CR3: 000000012c259000 CR4: 0000000000f50ef0 PKRU: 55555554 Rastreo de llamadas:  ? __die_body.cold+0x19/0x26 ? die_addr+0x3d/0x70 ? exc_general_protection+0x159/0x460 ? asm_exc_general_protection+0x27/0x30 ? dma_resv_iter_first_unlocked+0x55/0x290 dma_resv_wait_timeout+0x56/0x100 ttm_bo_delayed_delete+0x69/0xb0 [ttm] process_one_work+0x217/0x5c0 worker_thread+0x1c8/0x3d0 ? apply_wqattrs_cleanup.part.0+0xc0/0xc0 kthread+0x10b/0x240 ? kthreads_online_cpu+0x140/0x140 ret_from_fork+0x40/0x70 ? kthreads_online_cpu+0x140/0x140 ret_from_fork_asm+0x11/0x20  La causa es: - drm_prime_gem_destroy llama a dma_buf_put(dma_buf), que libera la referencia al dma_buf compartido. El recuento de referencias es 0, por lo que el dma_buf se destruye, lo que a su vez reduce el recuento de referencias amdgpu_bo correspondiente a 0, y el amdgpu_bo se destruye. - Se llama a drm_gem_object_release y luego a dma_resv_fini (que destruye el objeto de reserva), liberando finalmente el amdgpu_bo. - nouveau_bo obj-&gt;bo.base.resv ahora es un puntero colgante a la memoria anteriormente asignada a amdgpu_bo. - nouveau_gem_object_del llama a ttm_bo_put(&amp;nvbo-&gt;bo), que a su vez llama a ttm_bo_release, que programa ttm_bo_delayed_delete. - ttm_bo_delayed_delete se ejecuta y desreferencia el puntero colgante resv, lo que genera un fallo de protección general. Para solucionar esto, mueva la llamada drm_prime_gem_destroy de nouveau_gem_object_del a nouveau_bo_del_ttm. Esto garantiza que se ejecute después de ttm_bo_delayed_delete."}], "references": [{"url": "https://git.kernel.org/stable/c/12b038d521c75e3521522503becf3bc162628469", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/31e94c7989572f96926673614a3b958915a13ca9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/47761deabb69a5df0c2c4ec400d80bb3e072bd2e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6b95947ee780f4e1fb26413a1437d05bcb99712b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6e2c805996a49998d31ac522beb1534ca417e761", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/706868a1a1072cffd8bd63f7e161d79141099849", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ec0fbb28d049273bfd4f1e7a5ae4c74884beed3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ada78110b2d3ec88b398a49703bd336d4cee7a08", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
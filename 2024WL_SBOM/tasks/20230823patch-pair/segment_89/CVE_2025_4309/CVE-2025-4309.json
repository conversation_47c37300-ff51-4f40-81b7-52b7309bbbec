{"cve_id": "CVE-2025-4309", "published_date": "2025-05-06T04:16:25.387", "last_modified_date": "2025-05-09T13:37:21.743", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.1. It has been rated as critical. Affected by this issue is some unknown functionality of the file /admin/add-art-type.php. The manipulation of the argument arttype leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Art Gallery Management System 1.1. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/add-art-type.php. La manipulación del argumento arttype provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/lierran1/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307412", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307412", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564207", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
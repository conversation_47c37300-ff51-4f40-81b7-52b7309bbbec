{"cve_id": "CVE-2024-13381", "published_date": "2025-05-01T06:15:33.553", "last_modified_date": "2025-05-07T20:09:19.373", "descriptions": [{"lang": "en", "value": "The Calculated Fields Form WordPress plugin before 5.2.62 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Calculated Fields Form de WordPress anterior a la versión 5.2.62 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/da099e52-7f7b-4d76-a0bc-a46315510e0a/", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}]}
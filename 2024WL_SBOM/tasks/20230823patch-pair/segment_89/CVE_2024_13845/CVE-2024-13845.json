{"cve_id": "CVE-2024-13845", "published_date": "2025-05-01T05:15:51.583", "last_modified_date": "2025-05-19T11:53:32.777", "descriptions": [{"lang": "en", "value": "The Gravity Forms WebHooks plugin for WordPress is vulnerable to Server-Side Request Forgery in all versions up to, and including, 1.6.0 via the 'process_feed' method of the GF_Webhooks class This makes it possible for authenticated attackers, with Administrator-level access and above, to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Gravity Forms WebHooks para WordPress es vulnerable a Server-Side Request Forgery en todas las versiones hasta la 1.6.0 incluida, a través del método 'process_feed' de la clase GF_Webhooks. Esto hace posible que atacantes autenticados, con acceso de nivel de administrador y superior, realicen solicitudes web a ubicaciones arbitrarias que se originan en la aplicación web y pueden usarse para consultar y modificar información de servicios internos."}], "references": [{"url": "https://www.gravityforms.com/blog/brand-new-release-webhooks-add-on-1-7/", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9311b20b-daad-408f-a1a0-d1e42573ab97?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
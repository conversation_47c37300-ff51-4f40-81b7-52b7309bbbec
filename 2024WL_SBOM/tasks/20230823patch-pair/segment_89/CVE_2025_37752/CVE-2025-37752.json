{"cve_id": "CVE-2025-37752", "published_date": "2025-05-01T13:15:53.933", "last_modified_date": "2025-06-27T11:15:24.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet_sched: sch_sfq: move the limit validation\n\nIt is not sufficient to directly validate the limit on the data that\nthe user passes as it can be updated based on how the other parameters\nare changed.\n\nMove the check at the end of the configuration update process to also\ncatch scenarios where the limit is indirectly updated, for example\nwith the following configurations:\n\ntc qdisc add dev dummy0 handle 1: root sfq limit 2 flows 1 depth 1\ntc qdisc add dev dummy0 handle 1: root sfq limit 2 flows 1 divisor 1\n\nThis fixes the following syzkaller reported crash:\n\n------------[ cut here ]------------\nUBSAN: array-index-out-of-bounds in net/sched/sch_sfq.c:203:6\nindex 65535 is out of range for type 'struct sfq_head[128]'\nCPU: 1 UID: 0 PID: 3037 Comm: syz.2.16 Not tainted 6.14.0-rc2-syzkaller #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 12/27/2024\nCall Trace:\n <TASK>\n __dump_stack lib/dump_stack.c:94 [inline]\n dump_stack_lvl+0x201/0x300 lib/dump_stack.c:120\n ubsan_epilogue lib/ubsan.c:231 [inline]\n __ubsan_handle_out_of_bounds+0xf5/0x120 lib/ubsan.c:429\n sfq_link net/sched/sch_sfq.c:203 [inline]\n sfq_dec+0x53c/0x610 net/sched/sch_sfq.c:231\n sfq_dequeue+0x34e/0x8c0 net/sched/sch_sfq.c:493\n sfq_reset+0x17/0x60 net/sched/sch_sfq.c:518\n qdisc_reset+0x12e/0x600 net/sched/sch_generic.c:1035\n tbf_reset+0x41/0x110 net/sched/sch_tbf.c:339\n qdisc_reset+0x12e/0x600 net/sched/sch_generic.c:1035\n dev_reset_queue+0x100/0x1b0 net/sched/sch_generic.c:1311\n netdev_for_each_tx_queue include/linux/netdevice.h:2590 [inline]\n dev_deactivate_many+0x7e5/0xe70 net/sched/sch_generic.c:1375"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net_sched: sch_sfq: mover la validación del límite No es suficiente validar directamente el límite en los datos que pasa el usuario, ya que se puede actualizar en función de cómo se modifiquen los demás parámetros. Mueva la comprobación al final del proceso de actualización de configuración para que también detecte los escenarios en los que el límite se actualiza indirectamente, por ejemplo, con las siguientes configuraciones: tc qdisc add dev dummy0 handle 1: root sfq limit 2 flows 1 Depth 1 tc qdisc add dev dummy0 handle 1: root sfq limit 2 flows 1 divisor 1 Esto corrige el siguiente fallo informado por syzkaller: ------------[ cortar aquí ]------------ UBSAN: array-index-out-of-bounds en net/sched/sch_sfq.c:203:6 el índice 65535 está fuera de rango para el tipo 'struct sfq_head[128]' CPU: 1 UID: 0 PID: 3037 Comm: syz.2.16 No contaminado 6.14.0-rc2-syzkaller #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 27/12/2024 Seguimiento de llamadas:  __dump_stack lib/dump_stack.c:94 [en línea] dump_stack_lvl+0x201/0x300 lib/dump_stack.c:120 ubsan_epilogue lib/ubsan.c:231 [en línea] __ubsan_handle_out_of_bounds+0xf5/0x120 lib/ubsan.c:429 sfq_link net/sched/sch_sfq.c:203 [en línea] sfq_dec+0x53c/0x610 net/sched/sch_sfq.c:231 sfq_dequeue+0x34e/0x8c0 net/sched/sch_sfq.c:493 sfq_reset+0x17/0x60 net/sched/sch_sfq.c:518 qdisc_reset+0x12e/0x600 net/sched/sch_generic.c:1035 tbf_reset+0x41/0x110 net/sched/sch_tbf.c:339 qdisc_reset+0x12e/0x600 net/sched/sch_generic.c:1035 dev_reset_queue+0x100/0x1b0 net/sched/sch_generic.c:1311 netdev_for_each_tx_queue include/linux/netdevice.h:2590 [en línea] dev_deactivate_many+0x7e5/0xe70 net/sched/sch_generic.c:1375"}], "references": [{"url": "https://git.kernel.org/stable/c/1348214fa042a71406964097e743c87a42c85a49", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e5e1fcc1b8ed57f902c424c5d9b328a3a19073d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c589aa318023690f1606c666a7fb5f4c1c9c219", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d62ded97db6b7c94c891f704151f372b1ba4688", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b36a68192037d1614317a09b0d78c7814e2eecf9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b3bf8f63e6179076b57c9de660c9f80b5abefe70", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d2718324f9e329b10ddc091fba5a0ba2b9d4d96a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f86293adce0c201cfabb283ef9d6f21292089bb8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-46331", "published_date": "2025-04-30T19:15:55.490", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "OpenFGA is a high-performance and flexible authorization/permission engine built for developers and inspired by Google Zanzibar. OpenFGA v1.8.10 to v1.3.6 (Helm chart <= openfga-0.2.28, docker <= v.1.8.10) are vulnerable to authorization bypass when certain Check and ListObject calls are executed. This issue has been patched in version 1.8.11."}, {"lang": "es", "value": "OpenFGA es un motor de autorización y permisos flexible y de alto rendimiento, diseñado para desarrolladores e inspirado en Google Zanzibar. Las versiones de OpenFGA v1.8.10 a v1.3.6 (Helm chart &lt;= openfga-0.2.28, docker &lt;= v.1.8.10) son vulnerables a la omisión de la autorización al ejecutar ciertas llamadas a Check y ListObject. Este problema se ha corregido en la versión 1.8.11."}], "references": [{"url": "https://github.com/openfga/openfga/commit/244302e7a8b979d66cc1874a3899cdff7d47862f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openfga/openfga/security/advisories/GHSA-w222-m46c-mgh6", "source": "<EMAIL>", "tags": []}]}
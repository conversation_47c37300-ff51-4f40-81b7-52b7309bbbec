{"cve_id": "CVE-2023-53110", "published_date": "2025-05-02T16:15:29.930", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/smc: fix NULL sndbuf_desc in smc_cdc_tx_handler()\n\nWhen performing a stress test on SMC-R by rmmod mlx5_ib driver\nduring the wrk/nginx test, we found that there is a probability\nof triggering a panic while terminating all link groups.\n\nThis issue dues to the race between smc_smcr_terminate_all()\nand smc_buf_create().\n\n\t\t\tsmc_smcr_terminate_all\n\nsmc_buf_create\n/* init */\nconn->sndbuf_desc = NULL;\n...\n\n\t\t\t__smc_lgr_terminate\n\t\t\t\tsmc_conn_kill\n\t\t\t\t\tsmc_close_abort\n\t\t\t\t\t\tsmc_cdc_get_slot_and_msg_send\n\n\t\t\t__softirqentry_text_start\n\t\t\t\tsmc_wr_tx_process_cqe\n\t\t\t\t\tsmc_cdc_tx_handler\n\t\t\t\t\t\tREAD(conn->sndbuf_desc->len);\n\t\t\t\t\t\t/* panic dues to NULL sndbuf_desc */\n\nconn->sndbuf_desc = xxx;\n\nThis patch tries to fix the issue by always to check the sndbuf_desc\nbefore send any cdc msg, to make sure that no null pointer is\nseen during cqe processing."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/smc: corrección de sndbuf_desc NULL en smc_cdc_tx_handler(). Al realizar una prueba de estrés en SMC-R con el controlador rmmod mlx5_ib durante la prueba wrk/nginx, se observó la probabilidad de generar un pánico al terminar todos los grupos de enlaces. Este problema se debe a la competencia entre smc_smcr_terminate_all() y smc_buf_create(). smc_smcr_terminate_all smc_buf_create /* init */ conn-&gt;sndbuf_desc = NULL; ... __smc_lgr_terminate smc_conn_kill smc_close_abort smc_cdc_get_slot_and_msg_send __softirqentry_text_start smc_wr_tx_process_cqe smc_cdc_tx_handler READ(conn-&gt;sndbuf_desc-&gt;len); /* pánico debido a NULL sndbuf_desc */ conn-&gt;sndbuf_desc = xxx; Este parche intenta solucionar el problema verificando siempre sndbuf_desc antes de enviar cualquier mensaje de cdc, para asegurarse de que no se vea ningún puntero nulo durante el procesamiento de cqe."}], "references": [{"url": "https://git.kernel.org/stable/c/22a825c541d775c1dbe7b2402786025acad6727b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/31817c530768b0199771ec6019571b4f0ddbf230", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3c270435db8aa34929263dddae8fd050f5216ecb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3ebac7cf0a184a8102821a7a00203f02bebda83c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b108bd9e6be000492ebebe867daa699285978a10", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
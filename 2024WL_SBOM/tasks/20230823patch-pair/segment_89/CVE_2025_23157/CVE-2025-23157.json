{"cve_id": "CVE-2025-23157", "published_date": "2025-05-01T13:15:51.623", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: venus: hfi_parser: add check to avoid out of bound access\n\nThere is a possibility that init_codecs is invoked multiple times during\nmanipulated payload from video firmware. In such case, if codecs_count\ncan get incremented to value more than MAX_CODEC_NUM, there can be OOB\naccess. Reset the count so that it always starts from beginning."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: venus: hfi_parser: añadir comprobación para evitar accesos fuera de los límites. Existe la posibilidad de que init_codecs se invoque varias veces durante la manipulación de la carga útil del firmware de vídeo. En tal caso, si codecs_count se incrementa a un valor superior a MAX_CODEC_NUM, puede haber accesos fuera de los límites. Restablezca el contador para que siempre comience desde el principio."}], "references": [{"url": "https://git.kernel.org/stable/c/172bf5a9ef70a399bb227809db78442dc01d9e48", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1ad6aa1464b8a5ce5c194458315021e8d216108e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/26bbedd06d85770581fda5d78e78539bb088fad1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2b8b9ea4e26a501eb220ea189e42b4527e65bdfa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/53e376178ceacca3ef1795038b22fc9ef45ff1d3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b2541e29d82da8a0df728aadec3e0a8db55d517b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cb5be9039f91979f8a2fac29f529f746d7848f3e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d4d88ece4ba91df5b02f1d3f599650f9e9fc0f45", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e5133a0b25463674903fdc0528e0a29b7267130e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4204", "published_date": "2025-05-02T13:15:47.423", "last_modified_date": "2025-06-04T22:39:20.750", "descriptions": [{"lang": "en", "value": "The Ultimate Auction Pro plugin for WordPress is vulnerable to SQL Injection via the ‘auction_id’ parameter in all versions up to, and including, 1.5.2 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Ultimate Auction Pro para WordPress es vulnerable a la inyección SQL mediante el parámetro 'auction_id' en todas las versiones hasta la 1.5.2 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas ya existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://auctionplugin.net/changelog/ultimate-woo-auction-pro/", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e492029d-6613-4881-b986-9fe14cb2cf74?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
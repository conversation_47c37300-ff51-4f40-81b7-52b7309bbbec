{"cve_id": "CVE-2022-49858", "published_date": "2025-05-01T15:16:09.410", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocteontx2-pf: Fix SQE threshold checking\n\nCurrent way of checking available SQE count which is based on\nHW updated SQB count could result in driver submitting an SQE\neven before CQE for the previously transmitted SQE at the same\nindex is processed in NAPI resulting losing SKB pointers,\nhence a leak. Fix this by checking a consumer index which\nis updated once CQE is processed."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: octeontx2-pf: Se corrige la comprobación del umbral de SQE. La forma actual de comprobar el recuento de SQE disponibles, basada en el recuento de SQB actualizado por hardware, podría provocar que el controlador envíe un SQE incluso antes de que se procese en NAPI el CQE del SQE previamente transmitido en el mismo índice, lo que resulta en la pérdida de punteros SKB y, por lo tanto, una fuga. Se soluciona esto comprobando un índice de consumidor que se actualiza una vez procesado el CQE."}], "references": [{"url": "https://git.kernel.org/stable/c/015e3c0a3b16193aab23beefe4719484b9984c2d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f0dfc4c88ef39be0ba736aa0ce6119263fc19aeb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49872", "published_date": "2025-05-01T15:16:12.133", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: gso: fix panic on frag_list with mixed head alloc types\n\nSince commit 3dcbdb134f32 (\"net: gso: Fix skb_segment splat when\nsplitting gso_size mangled skb having linear-headed frag_list\"), it is\nallowed to change gso_size of a GRO packet. However, that commit assumes\nthat \"checking the first list_skb member suffices; i.e if either of the\nlist_skb members have non head_frag head, then the first one has too\".\n\nIt turns out this assumption does not hold. We've seen BUG_ON being hit\nin skb_segment when skbs on the frag_list had differing head_frag with\nthe vmxnet3 driver. This happens because __netdev_alloc_skb and\n__napi_alloc_skb can return a skb that is page backed or kmalloced\ndepending on the requested size. As the result, the last small skb in\nthe GRO packet can be kmalloced.\n\nThere are three different locations where this can be fixed:\n\n(1) We could check head_frag in GRO and not allow GROing skbs with\n    different head_frag. However, that would lead to performance\n    regression on normal forward paths with unmodified gso_size, where\n    !head_frag in the last packet is not a problem.\n\n(2) Set a flag in bpf_skb_net_grow and bpf_skb_net_shrink indicating\n    that NETIF_F_SG is undesirable. That would need to eat a bit in\n    sk_buff. Furthermore, that flag can be unset when all skbs on the\n    frag_list are page backed. To retain good performance,\n    bpf_skb_net_grow/shrink would have to walk the frag_list.\n\n(3) Walk the frag_list in skb_segment when determining whether\n    NETIF_F_SG should be cleared. This of course slows things down.\n\nThis patch implements (3). To limit the performance impact in\nskb_segment, the list is walked only for skbs with SKB_GSO_DODGY set\nthat have gso_size changed. Normal paths thus will not hit it.\n\nWe could check only the last skb but since we need to walk the whole\nlist anyway, let's stay on the safe side."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: gso: corrección del pánico en frag_list con tipos de asignación de encabezado mixtos. Desde el commit 3dcbdb134f32 (\"net: gso: corrección del error skb_segment al dividir gso_size de un skb alterado con frag_list de encabezado lineal\"), se permite cambiar gso_size de un paquete GRO. Sin embargo, esta confirmación asume que basta con comprobar el primer miembro de list_skb; es decir, si alguno de los miembros de list_skb tiene un encabezado distinto de head_frag, el primero también lo tendrá. Resulta que esta suposición no se cumple. Hemos observado que se ha alcanzado BUG_ON en skb_segment cuando los skbs de frag_list tenían un head_frag distinto con el controlador vmxnet3. Esto se debe a que __netdev_alloc_skb y __napi_alloc_skb pueden devolver un skb con respaldo de página o asignado a km, según el tamaño solicitado. Como resultado, el último skb pequeño en el paquete GRO puede ser asignado a km. Hay tres ubicaciones diferentes donde esto puede ser corregido: (1) Podríamos revisar head_frag en GRO y no permitir que GROing skbs con diferente head_frag. Sin embargo, eso llevaría a una regresión del rendimiento en rutas de avance normales con gso_size sin modificar, donde !head_frag en el último paquete no es un problema. (2) Establecer un indicador en bpf_skb_net_grow y bpf_skb_net_shrink que indique que NETIF_F_SG no es deseable. Esto requeriría consumir un poco en sk_buff. Además, ese indicador puede ser desactivado cuando todos los skbs en la frag_list están en retroceso de página. Para mantener un buen rendimiento, bpf_skb_net_grow/shrink tendría que recorrer la frag_list. (3) Recorrer la frag_list en skb_segment al determinar si NETIF_F_SG debe ser borrado. Esto, por supuesto, ralentiza el proceso. Este parche implementa (3). Para limitar el impacto en el rendimiento de skb_segment, la lista solo se recorre para skb con SKB_GSO_DODGY configurado y gso_size modificado. Por lo tanto, las rutas normales no la alcanzarán. Podríamos revisar solo el último skb, pero como necesitamos recorrer la lista completa de todos modos, mejor optemos por lo seguro."}], "references": [{"url": "https://git.kernel.org/stable/c/0a9f56e525ea871d3950b90076912f5c7494f00f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/50868de7dc4e7f0fcadd6029f32bf4387c102ee6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5876b7f249a1ecbbcc8e35072c3828d6526d1c3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/598d9e30927b15731e83797fbd700ecf399f42dd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/65ad047fd83502447269fda8fd26c99077a9af47", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e4b7a99a03aefd37ba7bb1f022c8efab5019165", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad25a115f50800c6847e0d841c5c7992a9f7c1b3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bd5362e58721e4d0d1a37796593bd6e51536ce7a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
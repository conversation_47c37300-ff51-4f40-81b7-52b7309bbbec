{"cve_id": "CVE-2024-58100", "published_date": "2025-05-05T15:15:53.913", "last_modified_date": "2025-05-09T08:15:18.937", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: check changes_pkt_data property for extension programs\n\nWhen processing calls to global sub-programs, verifier decides whether\nto invalidate all packet pointers in current state depending on the\nchanges_pkt_data property of the global sub-program.\n\nBecause of this, an extension program replacing a global sub-program\nmust be compatible with changes_pkt_data property of the sub-program\nbeing replaced.\n\nThis commit:\n- adds changes_pkt_data flag to struct bpf_prog_aux:\n  - this flag is set in check_cfg() for main sub-program;\n  - in jit_subprogs() for other sub-programs;\n- modifies bpf_check_attach_btf_id() to check changes_pkt_data flag;\n- moves call to check_attach_btf_id() after the call to check_cfg(),\n  because it needs changes_pkt_data flag to be set:\n\n    bpf_check:\n      ...                             ...\n    - check_attach_btf_id             resolve_pseudo_ldimm64\n      resolve_pseudo_ldimm64   -->    bpf_prog_is_offloaded\n      bpf_prog_is_offloaded           check_cfg\n      check_cfg                     + check_attach_btf_id\n      ...                             ...\n\nThe following fields are set by check_attach_btf_id():\n- env->ops\n- prog->aux->attach_btf_trace\n- prog->aux->attach_func_name\n- prog->aux->attach_func_proto\n- prog->aux->dst_trampoline\n- prog->aux->mod\n- prog->aux->saved_dst_attach_type\n- prog->aux->saved_dst_prog_type\n- prog->expected_attach_type\n\nNeither of these fields are used by resolve_pseudo_ldimm64() or\nbpf_prog_offload_verifier_prep() (for netronome and netdevsim\ndrivers), so the reordering is safe."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: comprobar la propiedad changes_pkt_data para programas de extensión Al procesar llamadas a subprogramas globales, el verificador decide si invalidar todos los punteros de paquete en el estado actual dependiendo de la propiedad changes_pkt_data del subprograma global. Debido a esto, un programa de extensión que reemplaza un subprograma global debe ser compatible con la propiedad changes_pkt_data del subprograma que se está reemplazando. Esta confirmación: - añade el indicador changes_pkt_data a struct bpf_prog_aux: - este indicador se establece en check_cfg() para el subprograma principal; - en jit_subprogs() para otros subprogramas; - modifica bpf_check_attach_btf_id() para comprobar el indicador changes_pkt_data; - mueve la llamada a check_attach_btf_id() después de la llamada a check_cfg(), porque necesita que se configure el indicador changes_pkt_data: bpf_check: ... ... - check_attach_btf_id resolve_pseudo_ldimm64 resolve_pseudo_ldimm64 --&gt; bpf_prog_is_offloaded bpf_prog_is_offloaded check_cfg check_cfg + check_attach_btf_id ... ... Los siguientes campos se configuran mediante check_attach_btf_id(): - env-&gt;ops - prog-&gt;aux-&gt;attach_btf_trace - prog-&gt;aux-&gt;attach_func_name - prog-&gt;aux-&gt;attach_func_proto - prog-&gt;aux-&gt;dst_trampoline - prog-&gt;aux-&gt;mod - prog-&gt;aux-&gt;saved_dst_attach_type - prog-&gt;aux-&gt;saved_dst_prog_type - prog-&gt;expected_attach_type Ninguno de estos campos es utilizado por resolve_pseudo_ldimm64() o bpf_prog_offload_verifier_prep() (para los controladores netronome y netdevsim), por lo que el reordenamiento es seguro."}], "references": [{"url": "https://git.kernel.org/stable/c/3846e2bea565ee1c5195dcc625fda9868fb0e3b3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7197fc4acdf238ec8ad06de5a8235df0c1f9c7d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/81f6d0530ba031b5f038a091619bf2ff29568852", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-30422", "published_date": "2025-04-30T21:15:54.700", "last_modified_date": "2025-05-12T19:41:01.360", "descriptions": [{"lang": "en", "value": "A buffer overflow was addressed with improved input validation. This issue is fixed in AirPlay audio SDK 2.7.1, AirPlay video SDK *********, CarPlay Communication Plug-in R18.1. An attacker on the local network may cause an unexpected app termination."}, {"lang": "es", "value": "Se solucionó un desbordamiento de búfer mejorando la validación de entrada. Este problema se solucionó en el SDK de audio de AirPlay 2.7.1, el SDK de vídeo de AirPlay ********* y el complemento de comunicación de CarPlay R18.1. Un atacante en la red local podría provocar el cierre inesperado de la aplicación."}], "references": [{"url": "https://support.apple.com/en-us/122403", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
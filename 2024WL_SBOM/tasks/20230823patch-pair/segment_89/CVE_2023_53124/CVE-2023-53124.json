{"cve_id": "CVE-2023-53124", "published_date": "2025-05-02T16:15:31.453", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: mpt3sas: Fix NULL pointer access in mpt3sas_transport_port_add()\n\nPort is allocated by sas_port_alloc_num() and rphy is allocated by either\nsas_end_device_alloc() or sas_expander_alloc(), all of which may return\nNULL. So we need to check the rphy to avoid possible NULL pointer access.\n\nIf sas_rphy_add() returned with failure, rphy is set to NULL. We would\naccess the rphy in the following lines which would also result NULL pointer\naccess."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: mpt3sas: Se corrige el acceso a puntero nulo en mpt3sas_transport_port_add(). El puerto se asigna mediante sas_port_alloc_num() y el rphy se asigna mediante sas_end_device_alloc() o sas_expander_alloc(), lo que puede devolver un valor nulo. Por lo tanto, es necesario comprobar el rphy para evitar un posible acceso a puntero nulo. Si sas_rphy_add() falla, el rphy se establece en nulo. Accederíamos al rphy en las siguientes líneas, lo que también resultaría en un acceso a puntero nulo."}], "references": [{"url": "https://git.kernel.org/stable/c/090305c36185c0547e4441d4c08f1cf096b32134", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6f0c2f70d9929208d8427ec72c3ed91e2251e289", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9937f784a608944107dcc2ba9a9c3333f8330b9e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a26c775ccc4cfe46f9b718b51bd24313053c7e0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b5e5bbb3fa5f8412e96c5eda7f4a4af6241d6bd3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3c57724f1569311e4b81e98fad0931028b9bdcd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
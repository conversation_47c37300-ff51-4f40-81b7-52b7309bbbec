{"cve_id": "CVE-2025-46719", "published_date": "2025-05-05T19:15:57.197", "last_modified_date": "2025-06-17T20:18:16.003", "descriptions": [{"lang": "en", "value": "Open WebUI is a self-hosted artificial intelligence platform designed to operate entirely offline. Prior to version 0.6.6, a vulnerability in the way certain html tags in chat messages are rendered allows attackers to inject JavaScript code into a chat transcript. The JavaScript code will be executed in the user's browser every time that chat transcript is opened, allowing attackers to retrieve the user's access token and gain full control over their account. Chat transcripts can be shared with other users in the same server, or with the whole open-webui community if \"Enable Community Sharing\" is enabled in the admin panel. If this exploit is used against an admin user, it is possible to achieve Remote Code Execution on the server where the open-webui backend is hosted. This can be done by creating a new function which contains malicious python code. This vulnerability also affects chat transcripts uploaded to `https://openwebui.com/c/<user>/<chat_id>`, allowing for wormable stored XSS in https[:]//openwebui[.]com. Version 0.6.6 contains a patch for the issue."}, {"lang": "es", "value": "Open WebUI es una plataforma de inteligencia artificial autoalojada, diseñada para operar completamente sin conexión. Antes de la versión 0.6.6, una vulnerabilidad en la representación de ciertas etiquetas HTML en los mensajes de chat permitía a los atacantes inyectar código JavaScript en una transcripción de chat. El código JavaScript se ejecutaba en el navegador del usuario cada vez que se abría la transcripción, lo que permitía a los atacantes recuperar su token de acceso y obtener control total sobre su cuenta. Las transcripciones de chat se pueden compartir con otros usuarios del mismo servidor o con toda la comunidad de Open WebUI si la opción \"Habilitar uso compartido de la comunidad\" está activada en el panel de administración. Si se utiliza este exploit contra un usuario administrador, es posible lograr la ejecución remota de código en el servidor donde se aloja el backend de Open WebUI. Esto se puede lograr mediante la creación de una nueva función que contenga código Python malicioso. Esta vulnerabilidad también afecta a las transcripciones de chat subidas a `https://openwebui.com/c//`, lo que permite XSS almacenado susceptible de ser atacado por gusanos en https[:]//openwebui[.]com. La versión 0.6.6 contiene un parche para el problema."}], "references": [{"url": "https://github.com/open-webui/open-webui/blob/main/src/lib/components/chat/Messages/Markdown/MarkdownTokens.svelte#L269-L279", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/open-webui/open-webui/commit/6fd082d55ffaf6eb226efdeebc7155e3693d2d01", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/open-webui/open-webui/security/advisories/GHSA-9f4f-jv96-8766", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
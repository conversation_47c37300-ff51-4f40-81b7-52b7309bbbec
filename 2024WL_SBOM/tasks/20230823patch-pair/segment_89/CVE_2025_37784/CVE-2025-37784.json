{"cve_id": "CVE-2025-37784", "published_date": "2025-05-01T14:15:42.770", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: ti: icss-iep: Fix possible NULL pointer dereference for perout request\n\nThe ICSS IEP driver tracks perout and pps enable state with flags.\nCurrently when disabling pps and perout signals during icss_iep_exit(),\nresults in NULL pointer dereference for perout.\n\nTo fix the null pointer dereference issue, the icss_iep_perout_enable_hw\nfunction can be modified to directly clear the IEP CMP registers when\ndisabling PPS or PEROUT, without referencing the ptp_perout_request\nstructure, as its contents are irrelevant in this case."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: ti: icss-iep: Se corrige una posible desreferencia de puntero nulo para la solicitud perout. El controlador IEP de ICSS rastrea el estado de habilitación de perout y pps mediante indicadores. Actualmente, al deshabilitar las señales pps y perout durante icss_iep_exit(), se produce una desreferencia de puntero nulo para perout. Para solucionar el problema de desreferencia de puntero nulo, se puede modificar la función icss_iep_perout_enable_hw para borrar directamente los registros CMP de IEP al deshabilitar PPS o PEROUT, sin referenciar la estructura ptp_perout_request, ya que su contenido es irrelevante en este caso."}], "references": [{"url": "https://git.kernel.org/stable/c/7349c9e9979333abfce42da5f9025598083b59c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7891619d21f07a88e0275d6d43db74035aa74f69", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da5035d7aeadcfa44096dd34689bfed6c657f559", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eeec66327001421531b3fb1a2ac32efc8a2493b0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
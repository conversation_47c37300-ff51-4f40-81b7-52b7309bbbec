{"cve_id": "CVE-2025-44867", "published_date": "2025-05-01T18:15:57.353", "last_modified_date": "2025-05-27T16:31:11.790", "descriptions": [{"lang": "en", "value": "Tenda W20E V15.11.0.6 was found to contain a command injection vulnerability in the formSetNetCheckTools function via the hostName parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que Tenda W20E V15.11.0.6 contenía una vulnerabilidad de inyección de comandos en la función formSetNetCheckTools mediante el parámetro hostName. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Tenda_W20E/formSetNetCheckTools/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-23149", "published_date": "2025-05-01T13:15:50.780", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntpm: do not start chip while suspended\n\nChecking TPM_CHIP_FLAG_SUSPENDED after the call to tpm_find_get_ops() can\nlead to a spurious tpm_chip_start() call:\n\n[35985.503771] i2c i2c-1: Transfer while suspended\n[35985.503796] WARNING: CPU: 0 PID: 74 at drivers/i2c/i2c-core.h:56 __i2c_transfer+0xbe/0x810\n[35985.503802] Modules linked in:\n[35985.503808] CPU: 0 UID: 0 PID: 74 Comm: hwrng Tainted: G        W          6.13.0-next-20250203-00005-gfa0cb5642941 #19 9c3d7f78192f2d38e32010ac9c90fdc71109ef6f\n[35985.503814] Tainted: [W]=WARN\n[35985.503817] Hardware name: Google Morphius/Morphius, BIOS Google_Morphius.13434.858.0 10/26/2023\n[35985.503819] RIP: 0010:__i2c_transfer+0xbe/0x810\n[35985.503825] Code: 30 01 00 00 4c 89 f7 e8 40 fe d8 ff 48 8b 93 80 01 00 00 48 85 d2 75 03 49 8b 16 48 c7 c7 0a fb 7c a7 48 89 c6 e8 32 ad b0 fe <0f> 0b b8 94 ff ff ff e9 33 04 00 00 be 02 00 00 00 83 fd 02 0f 5\n[35985.503828] RSP: 0018:ffffa106c0333d30 EFLAGS: 00010246\n[35985.503833] RAX: 074ba64aa20f7000 RBX: ffff8aa4c1167120 RCX: 0000000000000000\n[35985.503836] RDX: 0000000000000000 RSI: ffffffffa77ab0e4 RDI: 0000000000000001\n[35985.503838] RBP: 0000000000000001 R08: 0000000000000001 R09: 0000000000000000\n[35985.503841] R10: 0000000000000004 R11: 00000001000313d5 R12: ffff8aa4c10f1820\n[35985.503843] R13: ffff8aa4c0e243c0 R14: ffff8aa4c1167250 R15: ffff8aa4c1167120\n[35985.503846] FS:  0000000000000000(0000) GS:ffff8aa4eae00000(0000) knlGS:0000000000000000\n[35985.503849] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[35985.503852] CR2: 00007fab0aaf1000 CR3: 0000000105328000 CR4: 00000000003506f0\n[35985.503855] Call Trace:\n[35985.503859]  <TASK>\n[35985.503863]  ? __warn+0xd4/0x260\n[35985.503868]  ? __i2c_transfer+0xbe/0x810\n[35985.503874]  ? report_bug+0xf3/0x210\n[35985.503882]  ? handle_bug+0x63/0xb0\n[35985.503887]  ? exc_invalid_op+0x16/0x50\n[35985.503892]  ? asm_exc_invalid_op+0x16/0x20\n[35985.503904]  ? __i2c_transfer+0xbe/0x810\n[35985.503913]  tpm_cr50_i2c_transfer_message+0x24/0xf0\n[35985.503920]  tpm_cr50_i2c_read+0x8e/0x120\n[35985.503928]  tpm_cr50_request_locality+0x75/0x170\n[35985.503935]  tpm_chip_start+0x116/0x160\n[35985.503942]  tpm_try_get_ops+0x57/0x90\n[35985.503948]  tpm_find_get_ops+0x26/0xd0\n[35985.503955]  tpm_get_random+0x2d/0x80\n\nDon't move forward with tpm_chip_start() inside tpm_try_get_ops(), unless\nTPM_CHIP_FLAG_SUSPENDED is not set. tpm_find_get_ops() will return NULL in\nsuch a failure case."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tpm: no iniciar el chip mientras está suspendido. Comprobar TPM_CHIP_FLAG_SUSPENDED después de la llamada a tpm_find_get_ops() puede provocar una llamada falsa a tpm_chip_start(): [35985.503771] i2c i2c-1: Transferencia mientras está suspendido [35985.503796] ADVERTENCIA: CPU: 0 PID: 74 en drivers/i2c/i2c-core.h:56 __i2c_transfer+0xbe/0x810 [35985.503802] Módulos vinculados en: [35985.503808] CPU: 0 UID: 0 PID: 74 Comm: hwrng Tainted: GW 6.13.0-next-20250203-00005-gfa0cb5642941 #19 9c3d7f78192f2d38e32010ac9c90fdc71109ef6f [35985.503814] Contaminado: [W]=WARN [35985.503817] Nombre del hardware: Google Morphius/Morphius, BIOS Google_Morphius.13434.858.0 26/10/2023 [35985.503819] RIP: 0010:__i2c_transfer+0xbe/0x810 [35985.503825] Código: 30 01 00 00 4c 89 f7 e8 40 fe d8 ff 48 8b 93 80 01 00 00 48 85 d2 75 03 49 8b 16 48 c7 c7 0a fb 7c a7 48 89 c6 e8 32 ad b0 fe &lt;0f&gt; 0b b8 94 ff ff ff e9 33 04 00 00 be 02 00 00 00 83 fd 02 0f 5 [35985.503828] RSP: 0018:ffffa106c0333d30 EFLAGS: 00010246 [35985.503833] RAX: 074ba64aa20f7000 RBX: ffff8aa4c1167120 RCX: 0000000000000000 [35985.503836] RDX: 0000000000000000 RSI: ffffffffa77ab0e4 RDI: 0000000000000001 [35985.503838] RBP: 0000000000000001 R08: 0000000000000001 R09: 0000000000000000 [35985.503841] R10: 0000000000000004 R11: 00000001000313d5 R12: ffff8aa4c10f1820 [35985.503843] R13: ffff8aa4c0e243c0 R14: ffff8aa4c1167250 R15: ffff8aa4c1167120 [35985.503846] FS: 0000000000000000(0000) GS:ffff8aa4eae00000(0000) knlGS:0000000000000000 [35985.503849] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [35985.503852] CR2: 00007fab0aaf1000 CR3: 0000000105328000 CR4: 00000000003506f0 [35985.503855] Rastreo de llamadas: [35985.503859]  [35985.503863] ? __warn+0xd4/0x260 [35985.503868] ? __i2c_transfer+0xbe/0x810 [35985.503874] ? report_bug+0xf3/0x210 [35985.503882] ? handle_bug+0x63/0xb0 [35985.503887] ? exc_invalid_op+0x16/0x50 [35985.503892] ? asm_exc_invalid_op+0x16/0x20 [35985.503904] ? __i2c_transfer+0xbe/0x810 [35985.503913] tpm_cr50_i2c_transfer_message+0x24/0xf0 [35985.503920] tpm_cr50_i2c_read+0x8e/0x120 [35985.503928] tpm_cr50_request_locality+0x75/0x170 [35985.503935] tpm_chip_start+0x116/0x160 [35985.503942] tpm_try_get_ops+0x57/0x90 [35985.503948] tpm_find_get_ops+0x26/0xd0 [35985.503955] tpm_get_random+0x2d/0x80 No avance con tpm_chip_start() dentro de tpm_try_get_ops(), a menos que TPM_CHIP_FLAG_SUSPENDED no esté configurado. tpm_find_get_ops() devolverá NULL en tal caso de falla."}], "references": [{"url": "https://git.kernel.org/stable/c/1404dff1e11bf927b70ac25e1de97bed9742ede4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/17d253af4c2c8a2acf84bb55a0c2045f150b7dfd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e74e2394eed90aff5c3a08c1f51f476d4de71d02", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1044e995b64d70ef90ef6f2b89955b127497702", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f3cb81cb96d587f9f235a11789d1ec0992643078", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
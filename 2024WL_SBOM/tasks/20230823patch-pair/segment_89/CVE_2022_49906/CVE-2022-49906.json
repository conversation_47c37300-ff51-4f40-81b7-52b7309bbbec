{"cve_id": "CVE-2022-49906", "published_date": "2025-05-01T15:16:15.703", "last_modified_date": "2025-05-07T13:30:25.537", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nibmvnic: Free rwi on reset success\n\nFree the rwi structure in the event that the last rwi in the list\nprocessed successfully. The logic in commit 4f408e1fa6e1 (\"ibmvnic:\nretry reset if there are no other resets\") introduces an issue that\nresults in a 32 byte memory leak whenever the last rwi in the list\ngets processed."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ibmvnic: Liberar rwi al reiniciarse correctamente. Libera la estructura rwi si el último rwi de la lista se ha procesado correctamente. La lógica del commit 4f408e1fa6e1 (\"ibmvnic: reintentar reiniciar si no hay otros reinicios\") genera un problema que provoca una pérdida de memoria de 32 bytes cada vez que se procesa el último rwi de la lista."}], "references": [{"url": "https://git.kernel.org/stable/c/535b78739ae75f257c894a05b1afa86ad9a3669e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c3543a287cfba9105dcc4bb41eb817f51266caaf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d6dd2fe71153f0ff748bf188bd4af076fe09a0a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2023-53059", "published_date": "2025-05-02T16:15:24.963", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nplatform/chrome: cros_ec_chardev: fix kernel data leak from ioctl\n\nIt is possible to peep kernel page's data by providing larger `insize`\nin struct cros_ec_command[1] when invoking EC host commands.\n\nFix it by using zeroed memory.\n\n[1]: https://elixir.bootlin.com/linux/v6.2/source/include/linux/platform_data/cros_ec_proto.h#L74"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: platform/chrome: cros_ec_chardev: se corrige la fuga de datos del kernel desde ioctl. Es posible acceder a los datos de la página del kernel proporcionando un valor `insize` mayor en la estructura cros_ec_command[1] al invocar comandos del host EC. Se corrige utilizando memoria a cero. [1]: https://elixir.bootlin.com/linux/v6.2/source/include/linux/platform_data/cros_ec_proto.h#L74"}], "references": [{"url": "https://git.kernel.org/stable/c/13493ad6a220cb3f6f3552a16b4f2753a118b633", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a0d8644784f73fa39f57f72f374eefaba2bf48a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b20cf3f89c56b5f6a38b7f76a8128bf9f291bbd3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eab28bfafcd1245a3510df9aa9eb940589956ea6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ebea2e16504f40d2c2bac42ad5c5a3de5ce034b4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f86ff88a1548ccf5a13960c0e7625ca787ea0993", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
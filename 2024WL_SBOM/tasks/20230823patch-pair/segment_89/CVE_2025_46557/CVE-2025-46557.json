{"cve_id": "CVE-2025-46557", "published_date": "2025-04-30T19:15:55.783", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "XWiki is a generic wiki platform. In versions starting from 15.3-rc-1 to before 15.10.14, from 16.0.0-rc-1 to before 16.4.6, and from 16.5.0-rc-1 to before 16.10.0-rc-1, a user who can access pages located in the XWiki space (by default, anyone) can access the page XWiki.Authentication.Administration and (unless an authenticator is set in xwiki.cfg) switch to another installed authenticator. Note that, by default, there is only one authenticator available (Standard XWiki Authenticator). So, if no authenticator extension was installed, it's not really possible to do anything for an attacker. Also, in most cases, if an SSO authenticator is installed and utilized (like OIDC or LDAP for example), the worst an attacker can do is break authentication by switching back to the standard authenticator (that's because it's impossible to login to a user which does not have a stored password, and that's usually what SSO authenticator produce). This issue has been patched in versions 15.10.14, 16.4.6, and 16.10.0-rc-1."}, {"lang": "es", "value": "XWiki es una plataforma wiki genérica. En las versiones 15.3-rc-1 y anteriores a la 15.10.14, 16.0.0-rc-1 y anteriores a la 16.4.6, y 16.5.0-rc-1 y anteriores a la 16.10.0-rc-1, un usuario con acceso a páginas del espacio XWiki (por defecto, cualquiera) puede acceder a la página XWiki.Authentication.Administration y (a menos que se configure un autenticador en xwiki.cfg) cambiar a otro autenticador instalado. Tenga en cuenta que, por defecto, solo hay un autenticador disponible (el autenticador estándar de XWiki). Por lo tanto, si no se instaló ninguna extensión de autenticador, no es posible hacer nada contra un atacante. Además, en la mayoría de los casos, si se instala y utiliza un autenticador SSO (como OIDC o LDAP, por ejemplo), lo peor que puede hacer un atacante es interrumpir la autenticación volviendo al autenticador estándar (esto se debe a que es imposible iniciar sesión con un usuario sin una contraseña guardada, y eso es lo que suele producir el autenticador SSO). Este problema se ha corregido en las versiones 15.10.14, 16.4.6 y 16.10.0-rc-1."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/5efc31cea1501c9a5cb593566fea8b558ff32a2a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-f9c6-2f9p-82jj", "source": "<EMAIL>", "tags": []}, {"url": "https://jira.xwiki.org/browse/XWIKI-22604", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-13858", "published_date": "2025-05-02T07:15:50.860", "last_modified_date": "2025-05-22T20:15:26.890", "descriptions": [{"lang": "en", "value": "The BuddyBoss Platform plugin and BuddyBoss Theme for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘invitee_name’ parameter in all versions up to, and including, 2.8.50 and 2.8.41, respectively, due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. The vulnerability was partially patched in the BuddyBoss Platform plugin in version 2.8.41."}, {"lang": "es", "value": "El complemento Buddyboss Platform para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del parámetro 'invitee_name' en todas las versiones hasta la 2.8.50 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada. La vulnerabilidad se corrigió parcialmente en la versión 2.8.41."}], "references": [{"url": "https://www.buddyboss.com/platform/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.buddyboss.com/resources/buddyboss-platform-releases/2-8-51/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.buddyboss.com/resources/buddyboss-theme-releases/2-8-50/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5f50e293-aebd-44dd-a692-64dea8f6622f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
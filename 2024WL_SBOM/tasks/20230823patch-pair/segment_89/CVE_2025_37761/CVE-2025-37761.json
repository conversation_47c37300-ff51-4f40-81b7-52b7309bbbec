{"cve_id": "CVE-2025-37761", "published_date": "2025-05-01T14:15:38.377", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/xe: Fix an out-of-bounds shift when invalidating TLB\n\nWhen the size of the range invalidated is larger than\nrounddown_pow_of_two(ULONG_MAX),\nThe function macro roundup_pow_of_two(length) will hit an out-of-bounds\nshift [1].\n\nUse a full TLB invalidation for such cases.\nv2:\n- Use a define for the range size limit over which we use a full\n  TLB invalidation. (<PERSON>)\n- Use a better calculation of the limit.\n\n[1]:\n[   39.202421] ------------[ cut here ]------------\n[   39.202657] UBSAN: shift-out-of-bounds in ./include/linux/log2.h:57:13\n[   39.202673] shift exponent 64 is too large for 64-bit type 'long unsigned int'\n[   39.202688] CPU: 8 UID: 0 PID: 3129 Comm: xe_exec_system_ Tainted: G     U             6.14.0+ #10\n[   39.202690] Tainted: [U]=USER\n[   39.202690] Hardware name: ASUS System Product Name/PRIME B560M-A AC, BIOS 2001 02/01/2023\n[   39.202691] Call Trace:\n[   39.202692]  <TASK>\n[   39.202695]  dump_stack_lvl+0x6e/0xa0\n[   39.202699]  ubsan_epilogue+0x5/0x30\n[   39.202701]  __ubsan_handle_shift_out_of_bounds.cold+0x61/0xe6\n[   39.202705]  xe_gt_tlb_invalidation_range.cold+0x1d/0x3a [xe]\n[   39.202800]  ? find_held_lock+0x2b/0x80\n[   39.202803]  ? mark_held_locks+0x40/0x70\n[   39.202806]  xe_svm_invalidate+0x459/0x700 [xe]\n[   39.202897]  drm_gpusvm_notifier_invalidate+0x4d/0x70 [drm_gpusvm]\n[   39.202900]  __mmu_notifier_release+0x1f5/0x270\n[   39.202905]  exit_mmap+0x40e/0x450\n[   39.202912]  __mmput+0x45/0x110\n[   39.202914]  exit_mm+0xc5/0x130\n[   39.202916]  do_exit+0x21c/0x500\n[   39.202918]  ? lockdep_hardirqs_on_prepare+0xdb/0x190\n[   39.202920]  do_group_exit+0x36/0xa0\n[   39.202922]  get_signal+0x8f8/0x900\n[   39.202926]  arch_do_signal_or_restart+0x35/0x100\n[   39.202930]  syscall_exit_to_user_mode+0x1fc/0x290\n[   39.202932]  do_syscall_64+0xa1/0x180\n[   39.202934]  ? do_user_addr_fault+0x59f/0x8a0\n[   39.202937]  ? lock_release+0xd2/0x2a0\n[   39.202939]  ? do_user_addr_fault+0x5a9/0x8a0\n[   39.202942]  ? trace_hardirqs_off+0x4b/0xc0\n[   39.202944]  ? clear_bhb_loop+0x25/0x80\n[   39.202946]  ? clear_bhb_loop+0x25/0x80\n[   39.202947]  ? clear_bhb_loop+0x25/0x80\n[   39.202950]  entry_SYSCALL_64_after_hwframe+0x76/0x7e\n[   39.202952] RIP: 0033:0x7fa945e543e1\n[   39.202961] Code: Unable to access opcode bytes at 0x7fa945e543b7.\n[   39.202962] RSP: 002b:00007ffca8fb4170 EFLAGS: 00000293\n[   39.202963] RAX: 000000000000003d RBX: 0000000000000000 RCX: 00007fa945e543e3\n[   39.202964] RDX: 0000000000000000 RSI: 00007ffca8fb41ac RDI: 00000000ffffffff\n[   39.202964] RBP: 00007ffca8fb4190 R08: 0000000000000000 R09: 00007fa945f600a0\n[   39.202965] R10: 0000000000000000 R11: 0000000000000293 R12: 0000000000000000\n[   39.202966] R13: 00007fa9460dd310 R14: 00007ffca8fb41ac R15: 0000000000000000\n[   39.202970]  </TASK>\n[   39.202970] ---[ end trace ]---\n\n(cherry picked from commit b88f48f86500bc0b44b4f73ac66d500a40d320ad)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/xe: corrige un cambio fuera de los límites al invalidar TLB Cuando el tamaño del rango invalidado es mayor que rounddown_pow_of_two(ULONG_MAX), la macro de función roundup_pow_of_two(length) alcanzará un cambio fuera de los límites [1]. Use una invalidación de TLB completa para tales casos. v2: - Use una definición para el límite de tamaño de rango sobre el cual usamos una invalidación de TLB completa. (Lucas) - Use un mejor cálculo del límite. [1]: [ 39.202421] ------------[ cortar aquí ]------------ [ 39.202657] UBSAN: desplazamiento fuera de los límites en ./include/linux/log2.h:57:13 [ 39.202673] el exponente de desplazamiento 64 es demasiado grande para el tipo de 64 bits 'long unsigned int' [ 39.202688] CPU: 8 UID: 0 PID: 3129 Comm: xe_exec_system_ Tainted: GU 6.14.0+ #10 [ 39.202690] Tainted: [U]=USER [ 39.202690] Nombre del hardware: Nombre del producto del sistema ASUS/PRIME B560M-A AC, BIOS 2001 02/01/2023 [ 39.202691] Llamada Rastreo: [ 39.202692]  [ 39.202695] dump_stack_lvl+0x6e/0xa0 [ 39.202699] ubsan_epilogue+0x5/0x30 [ 39.202701] __ubsan_handle_shift_out_of_bounds.cold+0x61/0xe6 [ 39.202705] xe_gt_tlb_invalidation_range.cold+0x1d/0x3a [xe] [ 39.202800] ? find_held_lock+0x2b/0x80 [ 39.202803] ? mark_held_locks+0x40/0x70 [ 39.202806] xe_svm_invalidate+0x459/0x700 [xe] [ 39.202897] drm_gpusvm_notifier_invalidate+0x4d/0x70 [drm_gpusvm] [ 39.202900] __mmu_notifier_release+0x1f5/0x270 [ 39.202905] exit_mmap+0x40e/0x450 [ 39.202912] __mmput+0x45/0x110 [ 39.202914] exit_mm+0xc5/0x130 [ 39.202916] do_exit+0x21c/0x500 [ 39.202918] ? lockdep_hardirqs_on_prepare+0xdb/0x190 [ 39.202920] do_group_exit+0x36/0xa0 [ 39.202922] get_signal+0x8f8/0x900 [ 39.202926] arch_do_signal_or_restart+0x35/0x100 [ 39.202930] syscall_exit_to_user_mode+0x1fc/0x290 [ 39.202932] do_syscall_64+0xa1/0x180 [ 39.202934] ? do_user_addr_fault+0x59f/0x8a0 [ 39.202937] ? lock_release+0xd2/0x2a0 [ 39.202939] ? do_user_addr_fault+0x5a9/0x8a0 [ 39.202942] ? trace_hardirqs_off+0x4b/0xc0 [ 39.202944] ? clear_bhb_loop+0x25/0x80 [ 39.202946] ? clear_bhb_loop+0x25/0x80 [ 39.202947] ? clear_bhb_loop+0x25/0x80 [ 39.202950] entry_SYSCALL_64_after_hwframe+0x76/0x7e [ 39.202952] RIP: 0033:0x7fa945e543e1 [ 39.202961] Code: Unable to access opcode bytes at 0x7fa945e543b7. [ 39.202962] RSP: 002b:00007ffca8fb4170 EFLAGS: 00000293 [ 39.202963] RAX: 000000000000003d RBX: 0000000000000000 RCX: 00007fa945e543e3 [ 39.202964] RDX: 0000000000000000 RSI: 00007ffca8fb41ac RDI: 00000000ffffffff [ 39.202964] RBP: 00007ffca8fb4190 R08: 0000000000000000 R09: 00007fa945f600a0 [ 39.202965] R10: 0000000000000000 R11: 0000000000000293 R12: 0000000000000000 [ 39.202966] R13: 00007fa9460dd310 R14: 00007ffca8fb41ac R15: 0000000000000000 [ 39.202970]  [ 39.202970] ---[ fin del seguimiento ]--- (seleccionado de la confirmación b88f48f86500bc0b44b4f73ac66d500a40d320ad)"}], "references": [{"url": "https://git.kernel.org/stable/c/28477f701b63922ff88e9fb13f5519c11cd48b86", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7bcfeddb36b77f9fe3b010bb0b282b7618420bba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e4715858f87b78ce58cfa03bbe140321edbbaf20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
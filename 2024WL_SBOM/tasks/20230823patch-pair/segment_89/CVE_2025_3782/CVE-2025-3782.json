{"cve_id": "CVE-2025-3782", "published_date": "2025-05-06T10:15:15.897", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "The Cision Block plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘id’ parameter in all versions up to, and including, 4.3.0 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Cision Block para WordPress es vulnerable a Cross-Site Scripting almacenado a través del parámetro 'id' en todas las versiones hasta la 4.3.0 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/cision-block/tags/4.3.0/src/Frontend/templates/cision-block.php#L51", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3288041/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/cision-block/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c5b8268a-f3a3-4576-b235-962de37cc388?source=cve", "source": "<EMAIL>", "tags": []}]}
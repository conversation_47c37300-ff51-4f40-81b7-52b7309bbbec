{"cve_id": "CVE-2025-0855", "published_date": "2025-05-06T23:15:50.350", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "The PGS Core plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 5.8.0 via deserialization of untrusted input in the 'import_header' function. This makes it possible for unauthenticated attackers to inject a PHP Object. No known POP chain is present in the vulnerable software. If a POP chain is present via an additional plugin or theme installed on the target system, it could allow the attacker to delete arbitrary files, retrieve sensitive data, or execute code."}, {"lang": "es", "value": "El complemento PGS Core para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 5.8.0 incluida, mediante la deserialización de entradas no confiables en la función 'import_header'. Esto permite a atacantes no autenticados inyectar un objeto PHP. No se conoce ninguna cadena POP presente en el software vulnerable. Si existe una cadena POP presente a través de un complemento o tema adicional instalado en el sistema objetivo, podría permitir al atacante eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código."}], "references": [{"url": "https://docs.potenzaglobalsolutions.com/docs/ciyashop-wp/changelog/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5dfc2249-3761-49c6-966e-73c33be74c0e?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0427", "published_date": "2025-05-02T10:15:16.637", "last_modified_date": "2025-05-12T16:15:21.760", "descriptions": [{"lang": "en", "value": "Use After Free vulnerability in Arm Ltd Bifrost GPU Kernel Driver, Arm Ltd Valhall GPU Kernel Driver, Arm Ltd Arm 5th Gen GPU Architecture Kernel Driver allows a local non-privileged user process to perform valid GPU processing operations to gain access to already freed memory.This issue affects Bifrost GPU Kernel Driver: from r8p0 through r49p3, from r50p0 through r51p0; Valhall GPU Kernel Driver: from r19p0 through r49p3, from r50p0 through r53p0; Arm 5th Gen GPU Architecture Kernel Driver: from r41p0 through r49p3, from r50p0 through r53p0."}, {"lang": "es", "value": "La vulnerabilidad \"Use After Free\" en los controladores del kernel de GPU Bifrost de Arm Ltd, Valhall de Arm Ltd y Arm 5th Gen GPU Architecture Kernel Driver de Arm Ltd permite que un proceso de usuario local sin privilegios realice operaciones de procesamiento de GPU válidas para obtener acceso a memoria ya liberada. Este problema afecta a los controladores del kernel de GPU Bifrost: de r8p0 a r49p3 y de r50p0 a r51p0; Valhall de Arm Ltd: de r19p0 a r49p3 y de r50p0 a r53p0; Arm 5th Gen GPU Architecture Kernel Driver de Arm Ltd: de r41p0 a r49p3 y de r50p0 a r53p0."}], "references": [{"url": "https://developer.arm.com/documentation/110465/latest/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
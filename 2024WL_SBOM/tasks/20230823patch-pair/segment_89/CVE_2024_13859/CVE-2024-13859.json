{"cve_id": "CVE-2024-13859", "published_date": "2025-05-02T07:15:51.067", "last_modified_date": "2025-05-09T18:16:02.687", "descriptions": [{"lang": "en", "value": "The Buddyboss Platform plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘bp_nouveau_ajax_media_save’ function in all versions up to, and including, 2.8.50 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. The vulnerability was partially patched in version 2.8.41."}, {"lang": "es", "value": "El complemento Buddyboss Platform para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de la función 'bp_nouveau_ajax_media_save' en todas las versiones hasta la 2.8.50 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada. La vulnerabilidad se corrigió parcialmente en la versión 2.8.41."}], "references": [{"url": "https://www.buddyboss.com/platform/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.buddyboss.com/resources/buddyboss-platform-releases/2-8-51/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d77c8096-40b1-4ac7-881f-6aed98da6752?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
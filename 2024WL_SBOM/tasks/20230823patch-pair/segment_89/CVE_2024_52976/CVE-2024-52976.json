{"cve_id": "CVE-2024-52976", "published_date": "2025-05-01T14:15:35.527", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "Inclusion of functionality from an untrusted control sphere in Elastic Agent subprocess, osqueryd, allows local attackers to execute arbitrary code via parameter injection.\n\nAn attacker requires local access and the ability to modify osqueryd configurations."}, {"lang": "es", "value": "La inclusión de funcionalidad de una esfera de control no confiable en el subproceso de Elastic Agent, osqueryd, permite a atacantes locales ejecutar código arbitrario mediante la inyección de parámetros. Un atacante requiere acceso local y la capacidad de modificar las configuraciones de osqueryd."}], "references": [{"url": "https://discuss.elastic.co/t/elastic-agent-7-17-25-and-8-15-4-security-update-esa-2024-39/377708", "source": "<EMAIL>", "tags": []}]}
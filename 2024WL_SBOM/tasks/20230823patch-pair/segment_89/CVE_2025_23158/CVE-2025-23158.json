{"cve_id": "CVE-2025-23158", "published_date": "2025-05-01T13:15:51.733", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: venus: hfi: add check to handle incorrect queue size\n\nqsize represents size of shared queued between driver and video\nfirmware. Firmware can modify this value to an invalid large value. In\nsuch situation, empty_space will be bigger than the space actually\navailable. Since new_wr_idx is not checked, so the following code will\nresult in an OOB write.\n...\nqsize = qhdr->q_size\n\nif (wr_idx >= rd_idx)\n empty_space = qsize - (wr_idx - rd_idx)\n....\nif (new_wr_idx < qsize) {\n memcpy(wr_ptr, packet, dwords << 2) --> OOB write\n\nAdd check to ensure qsize is within the allocated size while\nreading and writing packets into the queue."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: venus: hfi: se ha añadido una comprobación para gestionar el tamaño incorrecto de queue size qsize representa el tamaño de la cola compartida entre el controlador y el firmware de vídeo. El firmware puede modificar este valor a un valor grande no válido. En tal situación, el espacio vacío será mayor que el espacio realmente disponible. Dado que new_wr_idx no se comprueba, el siguiente código resultará en una escritura fuera de banda (OOB). ... qsize = qhdr-&gt;q_size if (wr_idx &gt;= rd_idx) empty_space = qsize - (wr_idx - rd_idx) .... if (new_wr_idx &lt; qsize) { memcpy(wr_ptr, packet, dwords &lt;&lt; 2) --&gt; Escritura fuera de banda (OOB). Se ha añadido una comprobación para garantizar que qsize se encuentre dentro del tamaño asignado al leer y escribir paquetes en la cola."}], "references": [{"url": "https://git.kernel.org/stable/c/101a86619aab42bb61f2253bbf720121022eab86", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1b86c1917e16bafbbb08ab90baaff533aa36c62d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/32af5c1fdb9bc274f52ee0472d3b060b18e4aab4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/40084302f639b3fe954398c5ba5ee556b7242b54", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/679424f8b31446f90080befd0300ea915485b096", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/69baf245b23e20efda0079238b27fc63ecf13de1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a45957bcde529169188929816775a575de77d84f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cf5f7bb4e0d786f4d9d50ae6b5963935eab71d75", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/edb89d69b1438681daaf5ca90aed3242df94cc96", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
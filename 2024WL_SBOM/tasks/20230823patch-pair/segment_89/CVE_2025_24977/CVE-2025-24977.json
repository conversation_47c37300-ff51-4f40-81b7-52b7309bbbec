{"cve_id": "CVE-2025-24977", "published_date": "2025-05-05T17:18:47.397", "last_modified_date": "2025-05-22T15:52:33.763", "descriptions": [{"lang": "en", "value": "OpenCTI is an open cyber threat intelligence (CTI) platform. Prior to version 6.4.11 any user with the capability `manage customizations` can execute commands on the underlying infrastructure where OpenCTI is hosted and can access internal server side secrets by misusing the web-hooks. Since the malicious user gets a root shell inside a container this opens up the the infrastructure environment for further attacks and exposures. Version 6.4.11 fixes the issue."}, {"lang": "es", "value": "OpenCTI es una plataforma abierta de inteligencia de ciberamenazas (CTI). Antes de la versión 6.4.11, cualquier usuario con la capacidad de \"gestionar personalizaciones\" podía ejecutar comandos en la infraestructura subyacente donde se alojaba OpenCTI y acceder a secretos internos del servidor mediante el uso indebido de webhooks. Dado que el usuario malicioso obtiene un shell root dentro de un contenedor, esto expone la infraestructura a nuevos ataques y exposiciones. La versión 6.4.11 soluciona este problema."}], "references": [{"url": "https://github.com/OpenCTI-Platform/opencti/security/advisories/GHSA-mf88-g2wq-p7qm", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
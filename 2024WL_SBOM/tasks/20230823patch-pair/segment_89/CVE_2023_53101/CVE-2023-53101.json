{"cve_id": "CVE-2023-53101", "published_date": "2025-05-02T16:15:29.023", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: zero i_disksize when initializing the bootloader inode\n\nIf the boot loader inode has never been used before, the\nEXT4_IOC_SWAP_BOOT inode will initialize it, including setting the\ni_size to 0.  However, if the \"never before used\" boot loader has a\nnon-zero i_size, then i_disksize will be non-zero, and the\ninconsistency between i_size and i_disksize can trigger a kernel\nwarning:\n\n WARNING: CPU: 0 PID: 2580 at fs/ext4/file.c:319\n CPU: 0 PID: 2580 Comm: bb Not tainted 6.3.0-rc1-00004-g703695902cfa\n RIP: 0010:ext4_file_write_iter+0xbc7/0xd10\n Call Trace:\n  vfs_write+0x3b1/0x5c0\n  ksys_write+0x77/0x160\n  __x64_sys_write+0x22/0x30\n  do_syscall_64+0x39/0x80\n\nReproducer:\n 1. create corrupted image and mount it:\n       mke2fs -t ext4 /tmp/foo.img 200\n       debugfs -wR \"sif <5> size 25700\" /tmp/foo.img\n       mount -t ext4 /tmp/foo.img /mnt\n       cd /mnt\n       echo 123 > file\n 2. Run the reproducer program:\n       posix_memalign(&buf, 1024, 1024)\n       fd = open(\"file\", O_RDWR | O_DIRECT);\n       ioctl(fd, EXT4_IOC_SWAP_BOOT);\n       write(fd, buf, 1024);\n\nFix this by setting i_disksize as well as i_size to zero when\ninitiaizing the boot loader inode."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: i_disksize cero al inicializar el inodo del cargador de arranque. Si el inodo del cargador de arranque nunca se ha usado antes, el inodo EXT4_IOC_SWAP_BOOT lo inicializará, incluyendo el establecimiento de i_size a 0. Sin embargo, si el cargador de arranque \"nunca usado antes\" tiene un i_size distinto de cero, entonces i_disksize será distinto de cero, y la inconsistencia entre i_size e i_disksize puede activar una advertencia del kernel: ADVERTENCIA: CPU: 0 PID: 2580 en fs/ext4/file.c:319 CPU: 0 PID: 2580 Comm: bb No contaminado 6.3.0-rc1-00004-g703695902cfa RIP: 0010:ext4_file_write_iter+0xbc7/0xd10 Rastreo de llamadas: vfs_write+0x3b1/0x5c0 ksys_write+0x77/0x160 __x64_sys_write+0x22/0x30 do_syscall_64+0x39/0x80 Reproductor: 1. crear una imagen dañada y montarla: mke2fs -t ext4 /tmp/foo.img 200 debugfs -wR \"sif &lt;5&gt; size 25700\" /tmp/foo.img mount -t ext4 /tmp/foo.img /mnt cd /mnt echo 123 &gt; file 2. Ejecutar el programa reproductor: posix_memalign(&amp;buf, 1024, 1024) fd = open(\"file\", O_RDWR | Solucione esto configurando i_disksize e i_size en cero al iniciar el inodo del cargador de arranque."}], "references": [{"url": "https://git.kernel.org/stable/c/01a821aacc64d4b05dafd239dbc9b7856686002f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0d8a6c9a6415999fee1259ccf1796480c026b7d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3f00c476da8fe7c4c34ea16abb55d74127120413", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/59eee0cdf8c036f554add97a4da7c06d7a9ff34a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9cb27b1e76f0cc886ac09055bc41c0ab3f205167", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e9a4cc5486356158554f6ad73027d8635a48b34", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d6c1447e483c05dbcfb3ff77ac04237a82070b8c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f5361da1e60d54ec81346aee8e3d8baf1be0b762", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
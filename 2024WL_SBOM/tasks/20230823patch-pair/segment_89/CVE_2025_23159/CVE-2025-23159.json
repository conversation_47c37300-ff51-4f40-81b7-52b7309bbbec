{"cve_id": "CVE-2025-23159", "published_date": "2025-05-01T13:15:51.843", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: venus: hfi: add a check to handle OOB in sfr region\n\nsfr->buf_size is in shared memory and can be modified by malicious user.\nOOB write is possible when the size is made higher than actual sfr data\nbuffer. Cap the size to allocated size for such cases."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: venus: hfi: se ha añadido una comprobación para gestionar la escritura OOB en la región sfr. sfr-&gt;buf_size se encuentra en memoria compartida y puede ser modificado por un usuario malintencionado. Es posible escribir OOB cuando el tamaño es mayor que el del búfer de datos sfr. En estos casos, limite el tamaño al tamaño asignado."}], "references": [{"url": "https://git.kernel.org/stable/c/1b8fb257234e7d2d4b3f48af07c5aa5e11c71634", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4dd109038d513b92d4d33524ffc89ba32e02ba48", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4e95233af57715d81830fe82b408c633edff59f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/530f623f56a6680792499a8404083e17f8ec51f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5af611c70fb889d46d2f654b8996746e59556750", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8879397c0da5e5ec1515262995e82cdfd61b282a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a062d8de0be5525ec8c52f070acf7607ec8cbfe4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d78a8388a27b265fcb2b8d064f088168ac9356b0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f4b211714bcc70effa60c34d9fa613d182e3ef1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
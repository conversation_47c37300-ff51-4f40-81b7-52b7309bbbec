{"cve_id": "CVE-2023-53105", "published_date": "2025-05-02T16:15:29.430", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/mlx5e: Fix cleanup null-ptr deref on encap lock\n\nDuring module is unloaded while a peer tc flow is still offloaded,\nfirst the peer uplink rep profile is changed to a nic profile, and so\nneigh encap lock is destroyed. Next during unload, the VF reps netdevs\nare unregistered which causes the original non-peer tc flow to be deleted,\nwhich deletes the peer flow. The peer flow deletion detaches the encap\nentry and try to take the already destroyed encap lock, causing the\nbelow trace.\n\nFix this by clearing peer flows during tc eswitch cleanup\n(mlx5e_tc_esw_cleanup()).\n\nRelevant trace:\n[ 4316.837128] BUG: kernel NULL pointer dereference, address: 00000000000001d8\n[ 4316.842239] RIP: 0010:__mutex_lock+0xb5/0xc40\n[ 4316.851897] Call Trace:\n[ 4316.852481]  <TASK>\n[ 4316.857214]  mlx5e_rep_neigh_entry_release+0x93/0x790 [mlx5_core]\n[ 4316.858258]  mlx5e_rep_encap_entry_detach+0xa7/0xf0 [mlx5_core]\n[ 4316.859134]  mlx5e_encap_dealloc+0xa3/0xf0 [mlx5_core]\n[ 4316.859867]  clean_encap_dests.part.0+0x5c/0xe0 [mlx5_core]\n[ 4316.860605]  mlx5e_tc_del_fdb_flow+0x32a/0x810 [mlx5_core]\n[ 4316.862609]  __mlx5e_tc_del_fdb_peer_flow+0x1a2/0x250 [mlx5_core]\n[ 4316.863394]  mlx5e_tc_del_flow+0x(/0x630 [mlx5_core]\n[ 4316.864090]  mlx5e_flow_put+0x5f/0x100 [mlx5_core]\n[ 4316.864771]  mlx5e_delete_flower+0x4de/0xa40 [mlx5_core]\n[ 4316.865486]  tc_setup_cb_reoffload+0x20/0x80\n[ 4316.865905]  fl_reoffload+0x47c/0x510 [cls_flower]\n[ 4316.869181]  tcf_block_playback_offloads+0x91/0x1d0\n[ 4316.869649]  tcf_block_unbind+0xe7/0x1b0\n[ 4316.870049]  tcf_block_offload_cmd.isra.0+0x1ee/0x270\n[ 4316.879266]  tcf_block_offload_unbind+0x61/0xa0\n[ 4316.879711]  __tcf_block_put+0xa4/0x310"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/mlx5e: Se corrige la limpieza de null-ptr deref en el bloqueo de encap. Durante la descarga del módulo mientras un flujo tc de igual aún está descargado, primero se cambia el perfil de representante de enlace ascendente de igual a un perfil NIC, y así se destruye el bloqueo de encap vecino. A continuación, durante la descarga, se anula el registro de los representantes VF netdevs, lo que provoca la eliminación del flujo tc original no par, lo que elimina el flujo par. La eliminación del flujo par separa la entrada de encap e intenta tomar el bloqueo de encap ya destruido, causando el siguiente rastro. Solucione esto borrando los flujos de igual durante la limpieza del conmutador de eswitch de tc (mlx5e_tc_esw_cleanup()). Rastreo relevante: [ 4316.837128] ERROR: desreferencia de puntero NULL del núcleo, dirección: 00000000000001d8 [ 4316.842239] RIP: 0010:__mutex_lock+0xb5/0xc40 [ 4316.851897] Rastreo de llamada: [ 4316.852481]  [ 4316.857214] mlx5e_rep_neigh_entry_release+0x93/0x790 [mlx5_core] [ 4316.858258] mlx5e_rep_encap_entry_detach+0xa7/0xf0 [mlx5_core] [ 4316.859134] mlx5e_encap_dealloc+0xa3/0xf0 [mlx5_core] [ 4316.859867] clean_encap_dests.part.0+0x5c/0xe0 [mlx5_core] [ 4316.860605] mlx5e_tc_del_fdb_flow+0x32a/0x810 [mlx5_core] [ 4316.862609] __mlx5e_tc_del_fdb_peer_flow+0x1a2/0x250 [mlx5_core] [ 4316.863394] mlx5e_tc_del_flow+0x(/0x630 [mlx5_core] [ 4316.864090] mlx5e_flow_put+0x5f/0x100 [mlx5_core] [ 4316.864771] mlx5e_delete_flower+0x4de/0xa40 [mlx5_core] [ 4316.865486] tc_setup_cb_reoffload+0x20/0x80 [ 4316.865905] fl_reoffload+0x47c/0x510 [cls_flower] [ 4316.869181] tcf_block_playback_offloads+0x91/0x1d0 [ 4316.869649] tcf_block_unbind+0xe7/0x1b0 [ 4316.870049] tcf_block_offload_cmd.isra.0+0x1ee/0x270 [ 4316.879266] tcf_block_offload_unbind+0x61/0xa0 [ 4316.879711] __tcf_block_put+0xa4/0x310 "}], "references": [{"url": "https://git.kernel.org/stable/c/01fdaea410787fe372daeaeda93a29ed0606d334", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b7350f8dbe0c2a1d4d3ad7c35b610abd3cb91750", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c9668f0b1d28570327dbba189f2c61f6f9e43ae7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
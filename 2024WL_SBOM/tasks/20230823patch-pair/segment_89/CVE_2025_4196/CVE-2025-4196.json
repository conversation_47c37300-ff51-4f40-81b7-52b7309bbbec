{"cve_id": "CVE-2025-4196", "published_date": "2025-05-02T02:15:17.320", "last_modified_date": "2025-05-16T20:00:51.990", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Patient Record Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /birthing.php. The manipulation of the argument comp_id leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester Patient Record Management System 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /birthing.php. La manipulación del argumento comp_id provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/zhxu147/CVE/blob/main/hcpms_birthing.php_sqli.pdf", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.306809", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306809", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561880", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.sourcecodester.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2022-49900", "published_date": "2025-05-01T15:16:15.060", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ni2c: piix4: Fix adapter not be removed in piix4_remove()\n\nIn piix4_probe(), the piix4 adapter will be registered in:\n\n   piix4_probe()\n     piix4_add_adapters_sb800() / piix4_add_adapter()\n       i2c_add_adapter()\n\nBased on the probed device type, piix4_add_adapters_sb800() or single\npiix4_add_adapter() will be called.\nFor the former case, piix4_adapter_count is set as the number of adapters,\nwhile for antoher case it is not set and kept default *zero*.\n\nWhen piix4 is removed, piix4_remove() removes the adapters added in\npiix4_probe(), basing on the piix4_adapter_count value.\nBecause the count is zero for the single adapter case, the adapter won't\nbe removed and makes the sources allocated for adapter leaked, such as\nthe i2c client and device.\n\nThese sources can still be accessed by i2c or bus and cause problems.\nAn easily reproduced case is that if a new adapter is registered, i2c\nwill get the leaked adapter and try to call smbus_algorithm, which was\nalready freed:\n\nTriggered by: rmmod i2c_piix4 && modprobe max31730\n\n BUG: unable to handle page fault for address: ffffffffc053d860\n #PF: supervisor read access in kernel mode\n #PF: error_code(0x0000) - not-present page\n Oops: 0000 [#1] PREEMPT SMP KASAN\n CPU: 0 PID: 3752 Comm: modprobe Tainted: G\n Hardware name: QEMU Standard PC (i440FX + PIIX, 1996)\n RIP: 0010:i2c_default_probe (drivers/i2c/i2c-core-base.c:2259) i2c_core\n RSP: 0018:ffff888107477710 EFLAGS: 00000246\n ...\n <TASK>\n  i2c_detect (drivers/i2c/i2c-core-base.c:2302) i2c_core\n  __process_new_driver (drivers/i2c/i2c-core-base.c:1336) i2c_core\n  bus_for_each_dev (drivers/base/bus.c:301)\n  i2c_for_each_dev (drivers/i2c/i2c-core-base.c:1823) i2c_core\n  i2c_register_driver (drivers/i2c/i2c-core-base.c:1861) i2c_core\n  do_one_initcall (init/main.c:1296)\n  do_init_module (kernel/module/main.c:2455)\n  ...\n </TASK>\n ---[ end trace 0000000000000000 ]---\n\nFix this problem by correctly set piix4_adapter_count as 1 for the\nsingle adapter so it can be normally removed."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: i2c: piix4: Adaptador de corrección que no se eliminará en piix4_remove() En piix4_probe(), el adaptador piix4 se registrará en: piix4_probe() piix4_add_adapters_sb800() / piix4_add_adapter() i2c_add_adapter() En función del tipo de dispositivo sondeado, se llamará a piix4_add_adapters_sb800() o a un solo piix4_add_adapter(). Para el primer caso, piix4_adapter_count se establece como el número de adaptadores, mientras que para otro caso no se establece y se mantiene predeterminado *cero*. Cuando se elimina piix4, piix4_remove() elimina los adaptadores agregados en piix4_probe(), basándose en el valor de piix4_adapter_count. Dado que el conteo es cero en el caso de un solo adaptador, este no se eliminará y se filtrarán las fuentes asignadas, como el cliente y el dispositivo i2c. Estas fuentes aún pueden ser accedidas por i2c o el bus, lo que puede causar problemas. Un caso que se reproduce fácilmente es que si se registra un nuevo adaptador, i2c obtendrá el adaptador filtrado e intentará llamar a smbus_algorithm, que ya se había liberado: Activado por: rmmod i2c_piix4 y modprobe max31730 ERROR: no se puede controlar el error de página para la dirección: ffffffffc053d860 #PF: acceso de lectura del supervisor en modo kernel #PF: error_code(0x0000) - página no presente Oops: 0000 [#1] PREEMPT SMP KASAN CPU: 0 PID: 3752 Comm: modprobe Tainted: G Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996) RIP: 0010:i2c_default_probe (drivers/i2c/i2c-core-base.c:2259) i2c_core RSP: 0018:ffff888107477710 EFLAGS: 00000246 ...  i2c_detect (controladores/i2c/i2c-core-base.c:2302) i2c_core __process_new_driver (controladores/i2c/i2c-core-base.c:1336) i2c_core bus_for_each_dev (controladores/base/bus.c:301) i2c_for_each_dev (controladores/i2c/i2c-core-base.c:1823) i2c_core i2c_register_driver (controladores/i2c/i2c-core-base.c:1861) i2c_core do_one_initcall (init/main.c:1296) do_init_module (kernel/module/main.c:2455) ...  ---[ fin del seguimiento 0000000000000000 ]--- Solucione este problema configurando correctamente piix4_adapter_count como 1 para el adaptador único de modo que se pueda quitar normalmente."}], "references": [{"url": "https://git.kernel.org/stable/c/569bea74c94d37785682b11bab76f557520477cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bfd5e62f9a7ee214661cb6f143a3b40ccc63317f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d78ccdce662e88f41e87e90cf2bee63c1715d2a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fe51636fffc8108c7c4da6aa393010e786530ad9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
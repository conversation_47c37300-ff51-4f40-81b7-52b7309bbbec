{"cve_id": "CVE-2025-27611", "published_date": "2025-04-30T20:15:21.430", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "base-x is a base encoder and decoder of any given alphabet using bitcoin style leading zero compression. Versions 4.0.0, 5.0.0, and all prior to 3.0.11, are vulnerable to attackers potentially deceiving users into sending funds to an unintended address. This issue has been patched in versions 3.0.11, 4.0.1, and 5.0.1."}, {"lang": "es", "value": "base-x es un codificador y decodificador base de cualquier alfabeto que utiliza compresión de ceros a la izquierda, al estilo de Bitcoin. Las versiones 4.0.0, 5.0.0 y todas las anteriores a la 3.0.11 son vulnerables a que los atacantes engañen a los usuarios para que envíen fondos a una dirección no deseada. Este problema se ha corregido en las versiones 3.0.11, 4.0.1 y 5.0.1."}], "references": [{"url": "https://github.com/cryptocoinjs/base-x/pull/86", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cryptocoinjs/base-x/security/advisories/GHSA-xq7p-g2vc-g82p", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37751", "published_date": "2025-05-01T13:15:53.843", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nx86/cpu: Avoid running off the end of an AMD erratum table\n\nThe NULL array terminator at the end of erratum_1386_microcode was\nremoved during the switch from x86_cpu_desc to x86_cpu_id. This\ncauses readers to run off the end of the array.\n\nReplace the NULL."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: x86/cpu: Evitar la ejecución al final de una tabla de erratas de AMD. El terminador de matriz NULL al final de erratum_1386_microcode se eliminó durante la migración de x86_cpu_desc a x86_cpu_id. Esto provoca que los lectores se ejecuten al final de la matriz. Reemplace el NULL."}], "references": [{"url": "https://git.kernel.org/stable/c/1b518f73f1b6f59e083ec33dea22d9a1a275a970", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f0df00ebc57f803603f2a2e0df197e51f06fbe90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
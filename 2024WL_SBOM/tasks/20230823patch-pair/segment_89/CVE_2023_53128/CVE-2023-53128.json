{"cve_id": "CVE-2023-53128", "published_date": "2025-05-02T16:15:31.820", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: mpi3mr: Fix throttle_groups memory leak\n\nAdd a missing kfree()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: mpi3mr: Se corrige la pérdida de memoria de throttle_groups. Se agrega un kfree() faltante."}], "references": [{"url": "https://git.kernel.org/stable/c/574cc10edaa7dba833764efed8c57ee0e6bf7574", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/85349a227eb4a56520adc190c666075f80d4ae70", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f305a7b6ca21a665e8d0cf70b5936991a298c93c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
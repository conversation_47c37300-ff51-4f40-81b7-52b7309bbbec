{"cve_id": "CVE-2025-46820", "published_date": "2025-05-06T19:16:00.223", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "phpgt/Dom provides access to modern DOM APIs. Versions of phpgt/Dom prior to 4.1.8 expose the GITHUB_TOKEN in the Dom workflow run artifact. The ci.yml workflow file uses actions/upload-artifact@v4 to upload the build artifact. This artifact is a zip of the current directory, which includes the automatically generated .git/config file containing the run's GITHUB_TOKEN. Seeing as the artifact can be downloaded prior to the end of the workflow, there is a few seconds where an attacker can extract the token from the artifact and use it with the GitHub API to push malicious code or rewrite release commits in your repository. Any downstream user of the repository may be affected, but the token should only be valid for the duration of the workflow run, limiting the time during which exploitation could occur. Version 4.1.8 fixes the issue."}, {"lang": "es", "value": "phpgt/Dom proporciona acceso a las API modernas de DOM. Las versiones de phpgt/Dom anteriores a la 4.1.8 exponen el token GITHUB_TOKEN en el artefacto de ejecución del flujo de trabajo de Dom. El archivo de flujo de trabajo ci.yml utiliza actions/upload-artifact@v4 para cargar el artefacto de compilación. Este artefacto es un archivo zip del directorio actual, que incluye el archivo .git/config generado automáticamente que contiene el token GITHUB_TOKEN de la ejecución. Dado que el artefacto se puede descargar antes de que finalice el flujo de trabajo, un atacante puede extraer el token del artefacto durante unos segundos y usarlo con la API de GitHub para enviar código malicioso o reescribir las confirmaciones de versiones en el repositorio. Cualquier usuario intermedio del repositorio puede verse afectado, pero el token solo debería ser válido mientras dure la ejecución del flujo de trabajo, lo que limita el tiempo de explotación. La versión 4.1.8 soluciona el problema."}], "references": [{"url": "https://github.com/phpgt/Dom/commit/205cddcc82c002dfa48e874494efbf4c49497394", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/phpgt/Dom/security/advisories/GHSA-cwj7-6v67-2cm4", "source": "<EMAIL>", "tags": []}]}
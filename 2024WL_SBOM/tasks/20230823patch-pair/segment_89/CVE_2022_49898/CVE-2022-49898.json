{"cve_id": "CVE-2022-49898", "published_date": "2025-05-01T15:16:14.850", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: fix tree mod log mishandling of reallocated nodes\n\nWe have been seeing the following panic in production\n\n  kernel BUG at fs/btrfs/tree-mod-log.c:677!\n  invalid opcode: 0000 [#1] SMP\n  RIP: 0010:tree_mod_log_rewind+0x1b4/0x200\n  RSP: 0000:ffffc9002c02f890 EFLAGS: 00010293\n  RAX: 0000000000000003 RBX: ffff8882b448c700 RCX: 0000000000000000\n  RDX: 0000000000008000 RSI: 00000000000000a7 RDI: ffff88877d831c00\n  RBP: 0000000000000002 R08: 000000000000009f R09: 0000000000000000\n  R10: 0000000000000000 R11: 0000000000100c40 R12: 0000000000000001\n  R13: ffff8886c26d6a00 R14: ffff88829f5424f8 R15: ffff88877d831a00\n  FS:  00007fee1d80c780(0000) GS:ffff8890400c0000(0000) knlGS:0000000000000000\n  CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n  CR2: 00007fee1963a020 CR3: 0000000434f33002 CR4: 00000000007706e0\n  DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000\n  DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400\n  PKRU: 55555554\n  Call Trace:\n   btrfs_get_old_root+0x12b/0x420\n   btrfs_search_old_slot+0x64/0x2f0\n   ? tree_mod_log_oldest_root+0x3d/0xf0\n   resolve_indirect_ref+0xfd/0x660\n   ? ulist_alloc+0x31/0x60\n   ? kmem_cache_alloc_trace+0x114/0x2c0\n   find_parent_nodes+0x97a/0x17e0\n   ? ulist_alloc+0x30/0x60\n   btrfs_find_all_roots_safe+0x97/0x150\n   iterate_extent_inodes+0x154/0x370\n   ? btrfs_search_path_in_tree+0x240/0x240\n   iterate_inodes_from_logical+0x98/0xd0\n   ? btrfs_search_path_in_tree+0x240/0x240\n   btrfs_ioctl_logical_to_ino+0xd9/0x180\n   btrfs_ioctl+0xe2/0x2ec0\n   ? __mod_memcg_lruvec_state+0x3d/0x280\n   ? do_sys_openat2+0x6d/0x140\n   ? kretprobe_dispatcher+0x47/0x70\n   ? kretprobe_rethook_handler+0x38/0x50\n   ? rethook_trampoline_handler+0x82/0x140\n   ? arch_rethook_trampoline_callback+0x3b/0x50\n   ? kmem_cache_free+0xfb/0x270\n   ? do_sys_openat2+0xd5/0x140\n   __x64_sys_ioctl+0x71/0xb0\n   do_syscall_64+0x2d/0x40\n\nWhich is this code in tree_mod_log_rewind()\n\n\tswitch (tm->op) {\n        case BTRFS_MOD_LOG_KEY_REMOVE_WHILE_FREEING:\n\t\tBUG_ON(tm->slot < n);\n\nThis occurs because we replay the nodes in order that they happened, and\nwhen we do a REPLACE we will log a REMOVE_WHILE_FREEING for every slot,\nstarting at 0.  'n' here is the number of items in this block, which in\nthis case was 1, but we had 2 REMOVE_WHILE_FREEING operations.\n\nThe actual root cause of this was that we were replaying operations for\na block that shouldn't have been replayed.  Consider the following\nsequence of events\n\n1. We have an already modified root, and we do a btrfs_get_tree_mod_seq().\n2. We begin removing items from this root, triggering KEY_REPLACE for\n   it's child slots.\n3. We remove one of the 2 children this root node points to, thus triggering\n   the root node promotion of the remaining child, and freeing this node.\n4. We modify a new root, and re-allocate the above node to the root node of\n   this other root.\n\nThe tree mod log looks something like this\n\n\tlogical 0\top KEY_REPLACE (slot 1)\t\t\tseq 2\n\tlogical 0\top KEY_REMOVE (slot 1)\t\t\tseq 3\n\tlogical 0\top KEY_REMOVE_WHILE_FREEING (slot 0)\tseq 4\n\tlogical 4096\top LOG_ROOT_REPLACE (old logical 0)\tseq 5\n\tlogical 8192\top KEY_REMOVE_WHILE_FREEING (slot 1)\tseq 6\n\tlogical 8192\top KEY_REMOVE_WHILE_FREEING (slot 0)\tseq 7\n\tlogical 0\top LOG_ROOT_REPLACE (old logical 8192)\tseq 8\n\n>From here the bug is triggered by the following steps\n\n1.  Call btrfs_get_old_root() on the new_root.\n2.  We call tree_mod_log_oldest_root(btrfs_root_node(new_root)), which is\n    currently logical 0.\n3.  tree_mod_log_oldest_root() calls tree_mod_log_search_oldest(), which\n    gives us the KEY_REPLACE seq 2, and since that's not a\n    LOG_ROOT_REPLACE we incorrectly believe that we don't have an old\n    root, because we expect that the most recent change should be a\n    LOG_ROOT_REPLACE.\n4.  Back in tree_mod_log_oldest_root() we don't have a LOG_ROOT_REPLACE,\n    so we don't set old_root, we simply use our e\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: se corrige el mal manejo del registro de mod de árbol de nodos reasignados ¡Hemos estado viendo el siguiente ERROR de pánico en el kernel de producción en fs/btrfs/tree-mod-log.c:677! código de operación no válido: 0000 [#1] SMP RIP: 0010:tree_mod_log_rewind+0x1b4/0x200 RSP: 0000:ffffc9002c02f890 EFLAGS: 00010293 RAX: 0000000000000003 RBX: ffff8882b448c700 RCX: 0000000000000000 RDX: 0000000000008000 RSI: 00000000000000a7 RDI: ffff88877d831c00 RBP: 0000000000000002 R08: 000000000000009f R09: 00000000000000000 R10: 0000000000000000 R11: 00000000000100c40 R12: 000000000000001 R13: ffff8886c26d6a00 R14: ffff88829f5424f8 R15: ffff88877d831a00 FS: 00007fee1d80c780(0000) GS:ffff8890400c0000(0000) knlGS:000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 00007fee1963a020 CR3: 0000000434f33002 CR4: 00000000007706e0 DR0: 00000000000000000 DR1: 0000000000000000 DR2: 0000000000000000 DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400 PKRU: 55555554 Rastreo de llamadas: btrfs_get_old_root+0x12b/0x420 btrfs_search_old_slot+0x64/0x2f0 ? tree_mod_log_oldest_root+0x3d/0xf0 resolve_indirect_ref+0xfd/0x660 ? ulist_alloc+0x31/0x60 ? kmem_cache_alloc_trace+0x114/0x2c0 find_parent_nodes+0x97a/0x17e0 ? ulist_alloc+0x30/0x60 btrfs_find_all_roots_safe+0x97/0x150 iterate_extent_inodes+0x154/0x370 ? btrfs_search_path_in_tree+0x240/0x240 iterate_inodes_from_logical+0x98/0xd0 ? btrfs_search_path_in_tree+0x240/0x240 btrfs_ioctl_logical_to_ino+0xd9/0x180 btrfs_ioctl+0xe2/0x2ec0 ? __mod_memcg_lruvec_state+0x3d/0x280 ? do_sys_openat2+0x6d/0x140 ? kretprobe_dispatcher+0x47/0x70 ? kretprobe_rethook_handler+0x38/0x50 ? rethook_trampoline_handler+0x82/0x140 ? arch_rethook_trampoline_callback+0x3b/0x50 ? kmem_cache_free+0xfb/0x270 ? do_sys_openat2+0xd5/0x140 __x64_sys_ioctl+0x71/0xb0 do_syscall_64+0x2d/0x40 Which is this code in tree_mod_log_rewind() switch (tm-&gt;op) { case BTRFS_MOD_LOG_KEY_REMOVE_WHILE_FREEING: BUG_ON(tm-&gt;slot &lt; n); Esto ocurre porque reproducimos los nodos en el orden en que sucedieron, y cuando hacemos un REPLACE registraremos un REMOVE_WHILE_FREEING para cada ranura, comenzando en 0. 'n' aquí es el número de elementos en este bloque, que en este caso era 1, pero teníamos 2 operaciones REMOVE_WHILE_FREEING. La causa raíz real de esto fue que estábamos reproduciendo operaciones para un bloque que no debería haberse reproducido. Considere la siguiente secuencia de eventos: 1. Tenemos una raíz ya modificada y ejecutamos btrfs_get_tree_mod_seq(). 2. Comenzamos a eliminar elementos de esta raíz, activando KEY_REPLACE para sus ranuras secundarias. 3. Eliminamos uno de los dos hijos a los que apunta este nodo raíz, lo que activa la promoción del hijo restante y libera este nodo. 4. Modificamos una nueva raíz y reasignamos el nodo anterior al nodo raíz de esta otra raíz. El registro de mod del árbol se parece a esto lógico 0 op KEY_REPLACE (ranura 1) seq 2 lógico 0 op KEY_REMOVE (ranura 1) seq 3 lógico 0 op KEY_REMOVE_WHILE_FREEING (ranura 0) seq 4 lógico 4096 op LOG_ROOT_REPLACE (antiguo lógico 0) seq 5 lógico 8192 op KEY_REMOVE_WHILE_FREEING (ranura 1) seq 6 lógico 8192 op KEY_REMOVE_WHILE_FREEING (ranura 0) seq 7 lógico 0 op LOG_ROOT_REPLACE (antiguo lógico 8192) seq 8 &gt;A partir de aquí el error se activa con los siguientes pasos 1. Llama a btrfs_get_old_root() en new_root. 2. Llamamos a tree_mod_log_oldest_root(btrfs_root_node(new_root)), que actualmente tiene un valor lógico 0. 3. tree_mod_log_oldest_root() llama a tree_mod_log_search_oldest(), que nos proporciona la secuencia KEY_REPLACE 2. Dado que no es un LOG_ROOT_REPLACE, creemos erróneamente que no tenemos una raíz antigua, ya que esperamos que el cambio más reciente sea un LOG_ROOT_REPLACE. 4. En tree_mod_log_oldest_root(), no tenemos un LOG_ROOT_REPLACE, por lo que no establecemos old_root; simplemente usamos nuestra e ---truncated---"}], "references": [{"url": "https://git.kernel.org/stable/c/007058eb8292efc4c88f921752194b83269da085", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/52b2b65c9eb56fd829dda323786db828627ff7e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/968b71583130b6104c9f33ba60446d598e327a8b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-46735", "published_date": "2025-05-06T17:16:12.527", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "Terraform WinDNS Provider allows users to manage their Windows DNS server resources through Terraform. A security issue has been found in Terraform WinDNS Provider before version `1.0.5`. The `windns_record` resource did not sanitize the input variables. This could lead to authenticated command injection in the underlyding powershell command prompt. Version 1.0.5 contains a fix for the issue."}, {"lang": "es", "value": "Terraform WinDNS Provider permite a los usuarios administrar los recursos de su servidor DNS de Windows a través de Terraform. Se detectó un problema de seguridad en e Terraform WinDNS Provider anterior a la versión 1.0.5. El recurso `windns_record` no depuraba las variables de entrada. Esto podría provocar la inyección de comandos autenticados en el símbolo del sistema de PowerShell subyacente. La versión 1.0.5 contiene una solución para este problema."}], "references": [{"url": "https://github.com/nrkno/terraform-provider-windns/commit/c76f69610c1b502f90aaed8c4f102194530b5bce", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nrkno/terraform-provider-windns/security/advisories/GHSA-4vgf-2cm4-mp7c", "source": "<EMAIL>", "tags": []}]}
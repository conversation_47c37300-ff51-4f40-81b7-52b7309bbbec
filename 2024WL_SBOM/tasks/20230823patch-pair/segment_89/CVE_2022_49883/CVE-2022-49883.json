{"cve_id": "CVE-2022-49883", "published_date": "2025-05-01T15:16:13.287", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: x86: smm: number of GPRs in the SMRAM image depends on the image format\n\nOn 64 bit host, if the guest doesn't have X86_FEATURE_LM, KVM will\naccess 16 gprs to 32-bit smram image, causing out-ouf-bound ram\naccess.\n\nOn 32 bit host, the rsm_load_state_64/enter_smm_save_state_64\nis compiled out, thus access overflow can't happen."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: x86: smm: el número de GPR en la imagen SMRAM depende del formato de la imagen. En un host de 64 bits, si el invitado no tiene X86_FEATURE_LM, KVM accederá a 16 GPRS en la imagen SMRAM de 32 bits, lo que provocará un acceso a la RAM fuera de los límites. En un host de 32 bits, rsm_load_state_64/enter_smm_save_state_64 se compila, por lo que no se puede producir un desbordamiento de acceso."}], "references": [{"url": "https://git.kernel.org/stable/c/696db303e54f7352623d9f640e6c51d8fa9d5588", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a7ebfbea0f52550d7cdf12c38f3f5eaa7b2b6494", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
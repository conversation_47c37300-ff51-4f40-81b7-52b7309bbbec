{"cve_id": "CVE-2025-4316", "published_date": "2025-05-05T14:15:29.930", "last_modified_date": "2025-06-17T14:13:42.267", "descriptions": [{"lang": "en", "value": "Improper access control in PAM feature in Devolutions Server allows a PAM user to self approve their PAM requests even if disallowed by the configured policy via specific user interface actions.\n\n\n\n\n\nThis issue affects Devolutions Server versions from 2025.1.3.0 through 2025.1.6.0, and all versions up to 2024.3.15.0."}, {"lang": "es", "value": "El control de acceso inadecuado en la función PAM en Devolutions Server 2025.1.6.0 y versiones anteriores permite que un usuario de PAM apruebe automáticamente sus solicitudes de PAM incluso si la política configurada no lo permite a través de acciones específicas de la interfaz de usuario."}], "references": [{"url": "https://devolutions.net/security/advisories/DEVO-2025-0007/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
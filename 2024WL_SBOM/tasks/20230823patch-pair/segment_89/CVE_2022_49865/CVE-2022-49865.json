{"cve_id": "CVE-2022-49865", "published_date": "2025-05-01T15:16:11.420", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipv6: addrlabel: fix infoleak when sending struct ifaddrlblmsg to network\n\nWhen copying a `struct ifaddrlblmsg` to the network, __ifal_reserved\nremained uninitialized, resulting in a 1-byte infoleak:\n\n  BUG: KMSAN: kernel-network-infoleak in __netdev_start_xmit ./include/linux/netdevice.h:4841\n   __netdev_start_xmit ./include/linux/netdevice.h:4841\n   netdev_start_xmit ./include/linux/netdevice.h:4857\n   xmit_one net/core/dev.c:3590\n   dev_hard_start_xmit+0x1dc/0x800 net/core/dev.c:3606\n   __dev_queue_xmit+0x17e8/0x4350 net/core/dev.c:4256\n   dev_queue_xmit ./include/linux/netdevice.h:3009\n   __netlink_deliver_tap_skb net/netlink/af_netlink.c:307\n   __netlink_deliver_tap+0x728/0xad0 net/netlink/af_netlink.c:325\n   netlink_deliver_tap net/netlink/af_netlink.c:338\n   __netlink_sendskb net/netlink/af_netlink.c:1263\n   netlink_sendskb+0x1d9/0x200 net/netlink/af_netlink.c:1272\n   netlink_unicast+0x56d/0xf50 net/netlink/af_netlink.c:1360\n   nlmsg_unicast ./include/net/netlink.h:1061\n   rtnl_unicast+0x5a/0x80 net/core/rtnetlink.c:758\n   ip6addrlbl_get+0xfad/0x10f0 net/ipv6/addrlabel.c:628\n   rtnetlink_rcv_msg+0xb33/0x1570 net/core/rtnetlink.c:6082\n  ...\n  Uninit was created at:\n   slab_post_alloc_hook+0x118/0xb00 mm/slab.h:742\n   slab_alloc_node mm/slub.c:3398\n   __kmem_cache_alloc_node+0x4f2/0x930 mm/slub.c:3437\n   __do_kmalloc_node mm/slab_common.c:954\n   __kmalloc_node_track_caller+0x117/0x3d0 mm/slab_common.c:975\n   kmalloc_reserve net/core/skbuff.c:437\n   __alloc_skb+0x27a/0xab0 net/core/skbuff.c:509\n   alloc_skb ./include/linux/skbuff.h:1267\n   nlmsg_new ./include/net/netlink.h:964\n   ip6addrlbl_get+0x490/0x10f0 net/ipv6/addrlabel.c:608\n   rtnetlink_rcv_msg+0xb33/0x1570 net/core/rtnetlink.c:6082\n   netlink_rcv_skb+0x299/0x550 net/netlink/af_netlink.c:2540\n   rtnetlink_rcv+0x26/0x30 net/core/rtnetlink.c:6109\n   netlink_unicast_kernel net/netlink/af_netlink.c:1319\n   netlink_unicast+0x9ab/0xf50 net/netlink/af_netlink.c:1345\n   netlink_sendmsg+0xebc/0x10f0 net/netlink/af_netlink.c:1921\n  ...\n\nThis patch ensures that the reserved field is always initialized."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipv6: addrlabel: corregir fuga de información al enviar struct ifaddrlblmsg a la red Al copiar un `struct ifaddrlblmsg` a la red, __ifal_reserved permaneció sin inicializar, lo que resultó en una fuga de información de 1 byte: ERROR: KMSAN: kernel-network-infoleak en __netdev_start_xmit ./include/linux/netdevice.h:4841 __netdev_start_xmit ./include/linux/netdevice.h:4841 netdev_start_xmit ./include/linux/netdevice.h:4857 xmit_one net/core/dev.c:3590 dev_hard_start_xmit+0x1dc/0x800 net/core/dev.c:3606 __dev_queue_xmit+0x17e8/0x4350 net/core/dev.c:4256 dev_queue_xmit ./include/linux/netdevice.h:3009 __netlink_deliver_tap_skb net/netlink/af_netlink.c:307 __netlink_deliver_tap+0x728/0xad0 net/netlink/af_netlink.c:325 netlink_deliver_tap net/netlink/af_netlink.c:338 __netlink_sendskb net/netlink/af_netlink.c:1263 netlink_sendskb+0x1d9/0x200 net/netlink/af_netlink.c:1272 netlink_unicast+0x56d/0xf50 net/netlink/af_netlink.c:1360 nlmsg_unicast ./include/net/netlink.h:1061 rtnl_unicast+0x5a/0x80 net/core/rtnetlink.c:758 ip6addrlbl_get+0xfad/0x10f0 net/ipv6/addrlabel.c:628 rtnetlink_rcv_msg+0xb33/0x1570 net/core/rtnetlink.c:6082 ... Uninit se creó en: slab_post_alloc_hook+0x118/0xb00 mm/slab.h:742 slab_alloc_node mm/slub.c:3398 __kmem_cache_alloc_node+0x4f2/0x930 mm/slub.c:3437 __do_kmalloc_node mm/slab_common.c:954 __kmalloc_node_track_caller+0x117/0x3d0 mm/slab_common.c:975 kmalloc_reserve net/core/skbuff.c:437 __alloc_skb+0x27a/0xab0 net/core/skbuff.c:509 alloc_skb ./include/linux/skbuff.h:1267 nlmsg_new ./include/net/netlink.h:964 ip6addrlbl_get+0x490/0x10f0 net/ipv6/addrlabel.c:608 rtnetlink_rcv_msg+0xb33/0x1570 net/core/rtnetlink.c:6082 netlink_rcv_skb+0x299/0x550 net/netlink/af_netlink.c:2540 rtnetlink_rcv+0x26/0x30 net/core/rtnetlink.c:6109 netlink_unicast_kernel net/netlink/af_netlink.c:1319 netlink_unicast+0x9ab/0xf50 net/netlink/af_netlink.c:1345 netlink_sendmsg+0xebc/0x10f0 net/netlink/af_netlink.c:1921 ... Este parche garantiza que el campo reservado siempre se inicialice."}], "references": [{"url": "https://git.kernel.org/stable/c/0f85b7ae7c4b5d7b4bbf7ac653a733c181a8a2bf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2acb2779b147decd300c117683d5a32ce61c75d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/49e92ba5ecd7d72ba369dde2ccff738edd028a47", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/568a47ff756f913e8b374c2af9d22cd2c772c744", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/58cd7fdc8c1e6c7873acc08f190069fed88d1c12", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6d26d0587abccb9835382a0b53faa7b9b1cd83e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a033b86c7f7621fde31f0364af8986f43b44914f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c23fb2c82267638f9d206cb96bb93e1f93ad7828", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
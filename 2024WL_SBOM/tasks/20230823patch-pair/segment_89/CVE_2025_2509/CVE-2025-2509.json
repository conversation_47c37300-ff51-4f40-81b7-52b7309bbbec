{"cve_id": "CVE-2025-2509", "published_date": "2025-05-06T01:15:50.563", "last_modified_date": "2025-05-07T14:13:35.980", "descriptions": [{"lang": "en", "value": "Out-of-Bounds Read in Virglrenderer in ChromeOS  16093.57.0 allows a malicious guest <PERSON><PERSON> to achieve arbitrary address access within the crosvm sandboxed process, potentially leading to \nVM escape via crafted vertex elements data triggering an out-of-bounds read in util_format_description."}, {"lang": "es", "value": "La lectura fuera de los límites en Virglrenderer en ChromeOS 16093.57.0 permite que una VM invitada maliciosa logre acceso a una dirección arbitraria dentro del proceso aislado crosvm, lo que potencialmente conduce a un escape de la VM a través de datos de elementos de vértice manipulados que desencadenan una lectura fuera de los límites en util_format_description."}], "references": [{"url": "https://issues.chromium.org/issues/b/385851796", "source": "7f6e188d-c52a-4a19-8674-3c3fa7d1fc7f", "tags": []}, {"url": "https://issuetracker.google.com/issues/385851796", "source": "7f6e188d-c52a-4a19-8674-3c3fa7d1fc7f", "tags": []}]}
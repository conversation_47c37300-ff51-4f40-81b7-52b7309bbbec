{"cve_id": "CVE-2025-37769", "published_date": "2025-05-01T14:15:40.190", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/pm/smu11: Prevent division by zero\n\nThe user can set any speed value.\nIf speed is greater than UINT_MAX/8, division by zero is possible.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE.\n\n(cherry picked from commit da7dc714a8f8e1c9fc33c57cd63583779a3bef71)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/pm/smu11: Impide la división por cero. El usuario puede establecer cualquier valor de velocidad. Si la velocidad es superior a UINT_MAX/8, es posible la división por cero. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE. (Seleccionada de la confirmación da7dc714a8f8e1c9fc33c57cd63583779a3bef71)"}], "references": [{"url": "https://git.kernel.org/stable/c/63a150400194592206817124268ff6f43947e8c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7ba88b5cccc1a99c1afb96e31e7eedac9907704c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de2cba068c9c648503973b57696d035cfe58a9f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de6f8e0534cfabc528c969d453150ca90b24fb01", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fc9d55377353321e78f9e108d15f72a17e8c6ee2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-45009", "published_date": "2025-04-30T14:15:28.890", "last_modified_date": "2025-05-09T13:45:16.187", "descriptions": [{"lang": "en", "value": "A HTML Injection vulnerability was discovered in the normal-search.php file of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary code via the searchdata parameter."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección HTML en el archivo normal-search.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario mediante el parámetro searchdata."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/normal-search-html-injection.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
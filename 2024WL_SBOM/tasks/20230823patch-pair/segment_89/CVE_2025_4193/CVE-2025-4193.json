{"cve_id": "CVE-2025-4193", "published_date": "2025-05-02T01:15:54.743", "last_modified_date": "2025-05-16T17:37:40.313", "descriptions": [{"lang": "en", "value": "A vulnerability was found in itsourcecode Restaurant Management System 1.0 and classified as critical. Affected by this issue is some unknown functionality of the file /admin/category_update.php. The manipulation of the argument Category leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en itsourcecode Restaurant Management System 1.0, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/category_update.php. La manipulación del argumento \"Category\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cybersecurity/vuldb/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://itsourcecode.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306806", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306806", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561849", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://github.com/XuepengZhao-insp/vuldb/issues/5", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}
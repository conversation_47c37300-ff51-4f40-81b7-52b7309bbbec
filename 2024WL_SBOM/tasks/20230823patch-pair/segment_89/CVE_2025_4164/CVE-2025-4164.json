{"cve_id": "CVE-2025-4164", "published_date": "2025-05-01T12:15:18.410", "last_modified_date": "2025-05-16T17:45:46.460", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Employee Record Management System 1.3. Affected is an unknown function of the file changepassword.php. The manipulation of the argument currentpassword leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Employee Record Management System 1.3. La vulnerabilidad afecta a una función desconocida del archivo changepassword.php. La manipulación del argumento currentpassword provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ideal-valli/myCVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306696", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306696", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561140", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
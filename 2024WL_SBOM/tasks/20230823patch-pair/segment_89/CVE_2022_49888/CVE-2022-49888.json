{"cve_id": "CVE-2022-49888", "published_date": "2025-05-01T15:16:13.790", "last_modified_date": "2025-05-07T13:20:08.403", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64: entry: avoid kprobe recursion\n\nThe cortex_a76_erratum_1463225_debug_handler() function is called when\nhandling debug exceptions (and synchronous exceptions from BRK\ninstructions), and so is called when a probed function executes. If the\ncompiler does not inline cortex_a76_erratum_1463225_debug_handler(), it\ncan be probed.\n\nIf cortex_a76_erratum_1463225_debug_handler() is probed, any debug\nexception or software breakpoint exception will result in recursive\nexceptions leading to a stack overflow. This can be triggered with the\nftrace multiple_probes selftest, and as per the example splat below.\n\nThis is a regression caused by commit:\n\n  6459b8469753e9fe (\"arm64: entry: consolidate Cortex-A76 erratum 1463225 workaround\")\n\n... which removed the NOKPROBE_SYMBOL() annotation associated with the\nfunction.\n\nMy intent was that cortex_a76_erratum_1463225_debug_handler() would be\ninlined into its caller, el1_dbg(), which is marked noinstr and cannot\nbe probed. Mark cortex_a76_erratum_1463225_debug_handler() as\n__always_inline to ensure this.\n\nExample splat prior to this patch (with recursive entries elided):\n\n| # echo p cortex_a76_erratum_1463225_debug_handler > /sys/kernel/debug/tracing/kprobe_events\n| # echo p do_el0_svc >> /sys/kernel/debug/tracing/kprobe_events\n| # echo 1 > /sys/kernel/debug/tracing/events/kprobes/enable\n| Insufficient stack space to handle exception!\n| ESR: 0x0000000096000047 -- DABT (current EL)\n| FAR: 0xffff800009cefff0\n| Task stack:     [0xffff800009cf0000..0xffff800009cf4000]\n| IRQ stack:      [0xffff800008000000..0xffff800008004000]\n| Overflow stack: [0xffff00007fbc00f0..0xffff00007fbc10f0]\n| CPU: 0 PID: 145 Comm: sh Not tainted 6.0.0 #2\n| Hardware name: linux,dummy-virt (DT)\n| pstate: 604003c5 (nZCv DAIF +PAN -UAO -TCO -DIT -SSBS BTYPE=--)\n| pc : arm64_enter_el1_dbg+0x4/0x20\n| lr : el1_dbg+0x24/0x5c\n| sp : ffff800009cf0000\n| x29: ffff800009cf0000 x28: ffff000002c74740 x27: 0000000000000000\n| x26: 0000000000000000 x25: 0000000000000000 x24: 0000000000000000\n| x23: 00000000604003c5 x22: ffff80000801745c x21: 0000aaaac95ac068\n| x20: 00000000f2000004 x19: ffff800009cf0040 x18: 0000000000000000\n| x17: 0000000000000000 x16: 0000000000000000 x15: 0000000000000000\n| x14: 0000000000000000 x13: 0000000000000000 x12: 0000000000000000\n| x11: 0000000000000010 x10: ffff800008c87190 x9 : ffff800008ca00d0\n| x8 : 000000000000003c x7 : 0000000000000000 x6 : 0000000000000000\n| x5 : 0000000000000000 x4 : 0000000000000000 x3 : 00000000000043a4\n| x2 : 00000000f2000004 x1 : 00000000f2000004 x0 : ffff800009cf0040\n| Kernel panic - not syncing: kernel stack overflow\n| CPU: 0 PID: 145 Comm: sh Not tainted 6.0.0 #2\n| Hardware name: linux,dummy-virt (DT)\n| Call trace:\n|  dump_backtrace+0xe4/0x104\n|  show_stack+0x18/0x4c\n|  dump_stack_lvl+0x64/0x7c\n|  dump_stack+0x18/0x38\n|  panic+0x14c/0x338\n|  test_taint+0x0/0x2c\n|  panic_bad_stack+0x104/0x118\n|  handle_bad_stack+0x34/0x48\n|  __bad_stack+0x78/0x7c\n|  arm64_enter_el1_dbg+0x4/0x20\n|  el1h_64_sync_handler+0x40/0x98\n|  el1h_64_sync+0x64/0x68\n|  cortex_a76_erratum_1463225_debug_handler+0x0/0x34\n...\n|  el1h_64_sync_handler+0x40/0x98\n|  el1h_64_sync+0x64/0x68\n|  cortex_a76_erratum_1463225_debug_handler+0x0/0x34\n...\n|  el1h_64_sync_handler+0x40/0x98\n|  el1h_64_sync+0x64/0x68\n|  cortex_a76_erratum_1463225_debug_handler+0x0/0x34\n|  el1h_64_sync_handler+0x40/0x98\n|  el1h_64_sync+0x64/0x68\n|  do_el0_svc+0x0/0x28\n|  el0t_64_sync_handler+0x84/0xf0\n|  el0t_64_sync+0x18c/0x190\n| Kernel Offset: disabled\n| CPU features: 0x0080,00005021,19001080\n| Memory Limit: none\n| ---[ end Kernel panic - not syncing: kernel stack overflow ]---\n\nWith this patch, cortex_a76_erratum_1463225_debug_handler() is inlined\ninto el1_dbg(), and el1_dbg() cannot be probed:\n\n| # echo p cortex_a76_erratum_1463225_debug_handler > /sys/kernel/debug/tracing/kprobe_events\n| sh: write error: No such file or directory\n| # grep -w cortex_a76_errat\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64: entrada: evitar la recursión de kprobe. La función cortex_a76_erratum_1463225_debug_handler() se llama al gestionar excepciones de depuración (y excepciones síncronas de instrucciones BRK), y por lo tanto se llama cuando se ejecuta una función sondeada. Si el compilador no inserta en línea cortex_a76_erratum_1463225_debug_handler(), se puede sondear. Si se sondea cortex_a76_erratum_1463225_debug_handler(), cualquier excepción de depuración o excepción de punto de interrupción de software resultará en excepciones recursivas que conducen a un desbordamiento de pila. Esto se puede activar con la autoprueba ftrace multiple_probes y como se muestra en el ejemplo a continuación. Esta es una regresión causada por el commit 6459b8469753e9fe (\"arm64: entry: consolidate Cortex-A76 erratum 1463225 workaround\"), que eliminó la anotación NOKPROBE_SYMBOL() asociada a la función. Mi intención era que cortex_a76_erratum_1463225_debug_handler() would be inlined into its caller, el1_dbg(), which is marked noinstr and cannot be probed. Mark cortex_a76_erratum_1463225_debug_handler() as __always_inline to ensure this. Example splat prior to this patch (with recursive entries elided): | # echo p cortex_a76_erratum_1463225_debug_handler &gt; /sys/kernel/debug/tracing/kprobe_events | # echo p do_el0_svc &gt;&gt; /sys/kernel/debug/tracing/kprobe_events | # echo 1 &gt; /sys/kernel/debug/tracing/events/kprobes/enable | Insufficient stack space to handle exception! | ESR: 0x0000000096000047 -- DABT (current EL) | FAR: 0xffff800009cefff0 | Task stack: [0xffff800009cf0000..0xffff800009cf4000] | IRQ stack: [0xffff800008000000..0xffff800008004000] | Overflow stack: [0xffff00007fbc00f0..0xffff00007fbc10f0] | CPU: 0 PID: 145 Comm: sh Not tainted 6.0.0 #2 | Hardware name: linux,dummy-virt (DT) | pstate: 604003c5 (nZCv DAIF +PAN -UAO -TCO -DIT -SSBS BTYPE=--) | pc : arm64_enter_el1_dbg+0x4/0x20 | lr : el1_dbg+0x24/0x5c | sp : ffff800009cf0000 | x29: ffff800009cf0000 x28: ffff000002c74740 x27: 0000000000000000 | x26: 0000000000000000 x25: 0000000000000000 x24: 0000000000000000 | x23: 00000000604003c5 x22: ffff80000801745c x21: 0000aaaac95ac068 | x20: 00000000f2000004 x19: ffff800009cf0040 x18: 0000000000000000 | x17: 0000000000000000 x16: 0000000000000000 x15: 0000000000000000 | x14: 0000000000000000 x13: 0000000000000000 x12: 0000000000000000 | x11: 0000000000000010 x10: ffff800008c87190 x9 : ffff800008ca00d0 | x8 : 000000000000003c x7 : 0000000000000000 x6 : 0000000000000000 | x5 : 0000000000000000 x4 : 0000000000000000 x3 : 00000000000043a4 | x2 : 00000000f2000004 x1 : 00000000f2000004 x0 : ffff800009cf0040 | Kernel panic - not syncing: kernel stack overflow | CPU: 0 PID: 145 Comm: sh Not tainted 6.0.0 #2 | Hardware name: linux,dummy-virt (DT) | Call trace: | dump_backtrace+0xe4/0x104 | show_stack+0x18/0x4c | dump_stack_lvl+0x64/0x7c | dump_stack+0x18/0x38 | panic+0x14c/0x338 | test_taint+0x0/0x2c | panic_bad_stack+0x104/0x118 | handle_bad_stack+0x34/0x48 | __bad_stack+0x78/0x7c | arm64_enter_el1_dbg+0x4/0x20 | el1h_64_sync_handler+0x40/0x98 | el1h_64_sync+0x64/0x68 | cortex_a76_erratum_1463225_debug_handler+0x0/0x34 ... | el1h_64_sync_handler+0x40/0x98 | el1h_64_sync+0x64/0x68 | cortex_a76_erratum_1463225_debug_handler+0x0/0x34 ... | el1h_64_sync_handler+0x40/0x98 | el1h_64_sync+0x64/0x68 | cortex_a76_erratum_1463225_debug_handler+0x0/0x34 | el1h_64_sync_handler+0x40/0x98 | el1h_64_sync+0x64/0x68 | do_el0_svc+0x0/0x28 | el0t_64_sync_handler+0x84/0xf0 | el0t_64_sync+0x18c/0x190 | Kernel Offset: disabled | CPU features: 0x0080,00005021,19001080 | Memory Limit: none | ---[ end Kernel panic - not syncing: kernel stack overflow ]--- With this patch, cortex_a76_erratum_1463225_debug_handler() is inlined into el1_dbg(), and el1_dbg() cannot be probed: | # echo p cortex_a76_erratum_1463225_debug_handler &gt; /sys/kernel/debug/tracing/kprobe_events | sh: write error: No such file or directory | # grep -w cortex_a76_errat ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/024f4b2e1f874934943eb2d3d288ebc52c79f55c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/71d6c33fe223255f4416a01514da2c0bc3e283e7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/db66629d43b2d12cb43b004a4ca6be1d03228e97", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
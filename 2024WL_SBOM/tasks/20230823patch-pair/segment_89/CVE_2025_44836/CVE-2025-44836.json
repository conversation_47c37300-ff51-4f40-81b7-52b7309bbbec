{"cve_id": "CVE-2025-44836", "published_date": "2025-05-01T15:16:20.723", "last_modified_date": "2025-05-22T15:29:21.067", "descriptions": [{"lang": "en", "value": "TOTOLINK CPE CP900 V6.3c.1144_B20190715 was discovered to contain a command injection vulnerability in the setApRebootScheCfg function via the hour or minute parameters. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CPE CP900 V6.3c.1144_B20190715 contiene una vulnerabilidad de inyección de comandos en la función setApRebootScheCfg mediante los parámetros de hora o minuto. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/n0wstr/IOTVuln/tree/main/CP900/setApRebootScheCfg", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
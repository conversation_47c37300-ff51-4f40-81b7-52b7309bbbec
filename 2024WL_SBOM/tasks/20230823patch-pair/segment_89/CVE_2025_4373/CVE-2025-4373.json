{"cve_id": "CVE-2025-4373", "published_date": "2025-05-06T15:16:05.320", "last_modified_date": "2025-07-23T20:15:27.360", "descriptions": [{"lang": "en", "value": "A flaw was found in GLib, which is vulnerable to an integer overflow in the g_string_insert_unichar() function. When the position at which to insert the character is large, the position will overflow, leading to a buffer underwrite."}, {"lang": "es", "value": "Se encontró una falla en GLib que causa un desbordamiento de enteros en la función g_string_insert_unichar(). Cuando la posición donde se inserta el carácter es grande, esta se desborda, lo que provoca una subscritura del búfer."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2025:10855", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2025:11140", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2025:11327", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2025:11373", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2025:11374", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2025:11662", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2025-4373", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2364265", "source": "<EMAIL>", "tags": []}]}
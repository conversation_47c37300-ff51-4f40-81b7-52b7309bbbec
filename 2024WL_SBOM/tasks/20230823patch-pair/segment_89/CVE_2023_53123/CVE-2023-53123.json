{"cve_id": "CVE-2023-53123", "published_date": "2025-05-02T16:15:31.360", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nPCI: s390: Fix use-after-free of PCI resources with per-function hotplug\n\nOn s390 PCI functions may be hotplugged individually even when they\nbelong to a multi-function device. In particular on an SR-IOV device VFs\nmay be removed and later re-added.\n\nIn commit a50297cf8235 (\"s390/pci: separate zbus creation from\nscanning\") it was missed however that struct pci_bus and struct\nzpci_bus's resource list retained a reference to the PCI functions MMIO\nresources even though those resources are released and freed on\nhot-unplug. These stale resources may subsequently be claimed when the\nPCI function re-appears resulting in use-after-free.\n\nOne idea of fixing this use-after-free in s390 specific code that was\ninvestigated was to simply keep resources around from the moment a PCI\nfunction first appeared until the whole virtual PCI bus created for\na multi-function device disappears. The problem with this however is\nthat due to the requirement of artificial MMIO addreesses (address\ncookies) extra logic is then needed to keep the address cookies\ncompatible on re-plug. At the same time the MMIO resources semantically\nbelong to the PCI function so tying their lifecycle to the function\nseems more logical.\n\nInstead a simpler approach is to remove the resources of an individually\nhot-unplugged PCI function from the PCI bus's resource list while\nkeeping the resources of other PCI functions on the PCI bus untouched.\n\nThis is done by introducing pci_bus_remove_resource() to remove an\nindividual resource. Similarly the resource also needs to be removed\nfrom the struct zpci_bus's resource list. It turns out however, that\nthere is really no need to add the MMIO resources to the struct\nzpci_bus's resource list at all and instead we can simply use the\nzpci_bar_struct's resource pointer directly."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: PCI: s390: Se corrige el problema de use-after-free de recursos PCI con la conexión en caliente por función. En s390, las funciones PCI pueden conectarse en caliente individualmente, incluso si pertenecen a un dispositivo multifunción. En particular, en un dispositivo SR-IOV, las funciones virtuales (VF) pueden eliminarse y volver a añadirse posteriormente. Sin embargo, en el commit a50297cf8235 (\"s390/pci: separar la creación de zbus del escaneo\") se omitió que la lista de recursos de struct pci_bus y struct zpci_bus conservaba una referencia a los recursos MMIO de las funciones PCI, incluso si estos recursos se liberan al desconectar en caliente. Estos recursos obsoletos pueden reclamarse posteriormente cuando la función PCI reaparece, lo que resulta en un problema de use-after-free. Una idea para corregir este problema de use-after-free en el código específico de s390 que se investigó fue simplemente mantener los recursos desde el momento en que aparece una función PCI hasta que desaparece todo el bus PCI virtual creado para un dispositivo multifunción. El problema con esto, sin embargo, es que debido al requisito de direcciones MMIO artificiales (cookies de dirección), se necesita lógica adicional para mantener las cookies de dirección compatibles al volver a conectar. Al mismo tiempo, los recursos MMIO pertenecen semánticamente a la función PCI, por lo que vincular su ciclo de vida a la función parece más lógico. En cambio, un enfoque más simple es eliminar los recursos de una función PCI individualmente desconectada en caliente de la lista de recursos del bus PCI, mientras que se mantienen intactos los recursos de otras funciones PCI en el bus PCI. Esto se hace introduciendo pci_bus_remove_resource() para eliminar un recurso individual. De manera similar, el recurso también debe eliminarse de la lista de recursos de struct zpci_bus. Sin embargo, resulta que realmente no hay necesidad de agregar los recursos MMIO a la lista de recursos de struct zpci_bus en absoluto y, en su lugar, podemos simplemente usar el puntero de recursos de zpci_bar_struct directamente."}], "references": [{"url": "https://git.kernel.org/stable/c/437bb839e36cc9f35adc6d2a2bf113b7a0fc9985", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a2410d0c3d2d714ed968a135dfcbed6aa3ff7027", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab909509850b27fd39b8ba99e44cda39dbc3858c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b99ebf4b62774e690e73a551cf5fbf6f219bdd96", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
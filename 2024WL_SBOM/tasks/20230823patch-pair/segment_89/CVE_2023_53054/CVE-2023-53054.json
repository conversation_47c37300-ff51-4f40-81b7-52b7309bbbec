{"cve_id": "CVE-2023-53054", "published_date": "2025-05-02T16:15:24.483", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: dwc2: fix a devres leak in hw_enable upon suspend resume\n\nEach time the platform goes to low power, PM suspend / resume routines\ncall: __dwc2_lowlevel_hw_enable -> devm_add_action_or_reset().\nThis adds a new devres each time.\nThis may also happen at runtime, as dwc2_lowlevel_hw_enable() can be\ncalled from udc_start().\n\nThis can be seen with tracing:\n- echo 1 > /sys/kernel/debug/tracing/events/dev/devres_log/enable\n- go to low power\n- cat /sys/kernel/debug/tracing/trace\n\nA new \"ADD\" entry is found upon each low power cycle:\n... devres_log: 49000000.usb-otg ADD 82a13bba devm_action_release (8 bytes)\n... devres_log: 49000000.usb-otg ADD 49889daf devm_action_release (8 bytes)\n...\n\nA second issue is addressed here:\n- regulator_bulk_enable() is called upon each PM cycle (suspend/resume).\n- regulator_bulk_disable() never gets called.\n\nSo the reference count for these regulators constantly increase, by one\nupon each low power cycle, due to missing regulator_bulk_disable() call\nin __dwc2_lowlevel_hw_disable().\n\nThe original fix that introduced the devm_add_action_or_reset() call,\nfixed an issue during probe, that happens due to other errors in\ndwc2_driver_probe() -> dwc2_core_reset(). Then the probe fails without\ndisabling regulators, when dr_mode == USB_DR_MODE_PERIPHERAL.\n\nRather fix the error path: disable all the low level hardware in the\nerror path, by using the \"hsotg->ll_hw_enabled\" flag. Checking dr_mode\nhas been introduced to avoid a dual call to dwc2_lowlevel_hw_disable().\n\"ll_hw_enabled\" should achieve the same (and is used currently in the\nremove() routine)."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: dwc2: se corrige una fuga de devres en hw_enable al reanudar la suspensión. Cada vez que la plataforma pasa a bajo consumo, las rutinas de suspensión/reinicio de PM llaman a __dwc2_lowlevel_hw_enable -&gt; devm_add_action_or_reset(). Esto agrega un nuevo devres cada vez. Esto también puede ocurrir en tiempo de ejecución, ya que dwc2_lowlevel_hw_enable() puede llamarse desde udc_start(). Esto se puede ver con el seguimiento: - echo 1 &gt; /sys/kernel/debug/tracing/events/dev/devres_log/enable - ir a bajo consumo - cat /sys/kernel/debug/tracing/trace Se encuentra una nueva entrada \"ADD\" en cada ciclo de bajo consumo: ... devres_log: 49000000.usb-otg ADD 82a13bba devm_action_release (8 bytes) ... devres_log: 49000000.usb-otg ADD 49889daf devm_action_release (8 bytes) ... Aquí se aborda un segundo problema: - regulator_bulk_enable() se llama en cada ciclo de PM (suspender/reanudar). - regulator_bulk_disable() nunca se llama. Por lo tanto, el recuento de referencias para estos reguladores aumenta constantemente, en uno con cada ciclo de bajo consumo, debido a la falta de la llamada a regulator_bulk_disable() en __dwc2_lowlevel_hw_disable(). La corrección original, que introdujo la llamada a devm_add_action_or_reset(), solucionó un problema durante el sondeo que se produce debido a otros errores en dwc2_driver_probe() -&gt; dwc2_core_reset(). En ese caso, el sondeo falla sin deshabilitar los reguladores cuando dr_mode == USB_DR_MODE_PERIPHERAL. Mejor solución: deshabilite todo el hardware de bajo nivel en la ruta de error mediante el indicador \"hsotg-&gt;ll_hw_enabled\". Se ha introducido la comprobación de dr_mode para evitar una llamada dual a dwc2_lowlevel_hw_disable(). \"ll_hw_enabled\" debería lograr el mismo efecto (y se utiliza actualmente en la rutina remove())."}], "references": [{"url": "https://git.kernel.org/stable/c/1f01027c51eb16145e8e07fafea3ca07ef102d06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6485fc381b6528b6f547ee1ff10bdbcbe31a6e4c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cba76e1fb896b573f09f51aa299223276a77bc90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f747313249b74f323ddf841a9c8db14d989f296a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ffb8ab6f87bd28d700ab5c20d9d3a7e75067630d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4268", "published_date": "2025-05-05T07:15:47.073", "last_modified_date": "2025-05-07T16:38:18.700", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in TOTOLINK A720R 4.1.5cu.374 and classified as critical. This vulnerability affects unknown code of the file /cgi-bin/cstecgi.cgi. The manipulation of the argument topicurl with the input RebootSystem leads to missing authentication. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en TOTOLINK A720R 4.1.5cu.374, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /cgi-bin/cstecgi.cgi. La manipulación del argumento topicurl con la entrada RebootSystem provoca la omisión de la autenticación. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/at0de/my_vulns/blob/main/TOTOLINK/A720R/RebootSystem.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307372", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307372", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563429", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}
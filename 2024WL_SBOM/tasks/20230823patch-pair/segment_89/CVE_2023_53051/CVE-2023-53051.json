{"cve_id": "CVE-2023-53051", "published_date": "2025-05-02T16:15:24.180", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndm crypt: add cond_resched() to dmcrypt_write()\n\nThe loop in dmcrypt_write may be running for unbounded amount of time,\nthus we need cond_resched() in it.\n\nThis commit fixes the following warning:\n\n[ 3391.153255][   C12] watchdog: BUG: soft lockup - CPU#12 stuck for 23s! [dmcrypt_write/2:2897]\n...\n[ 3391.387210][   C12] Call trace:\n[ 3391.390338][   C12]  blk_attempt_bio_merge.part.6+0x38/0x158\n[ 3391.395970][   C12]  blk_attempt_plug_merge+0xc0/0x1b0\n[ 3391.401085][   C12]  blk_mq_submit_bio+0x398/0x550\n[ 3391.405856][   C12]  submit_bio_noacct+0x308/0x380\n[ 3391.410630][   C12]  dmcrypt_write+0x1e4/0x208 [dm_crypt]\n[ 3391.416005][   C12]  kthread+0x130/0x138\n[ 3391.419911][   C12]  ret_from_fork+0x10/0x18"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dm crypt: añadir cond_resched() a dmcrypt_write(). El bucle en dmcrypt_write podría estar ejecutándose durante un tiempo ilimitado, por lo que necesitamos cond_resched(). Esta confirmación corrige la siguiente advertencia: [3391.153255][C12] watchdog: BUG: soft lockup - CPU#12 atascada durante 23 s. [dmcrypt_write/2:2897] ... [3391.387210][C12] Rastreo de llamadas: [ 3391.390338][ C12] blk_attempt_bio_merge.part.6+0x38/0x158 [ 3391.395970][ C12] blk_attempt_plug_merge+0xc0/0x1b0 [ 3391.401085][ C12] blk_mq_submit_bio+0x398/0x550 [ 3391.405856][ C12] submit_bio_noacct+0x308/0x380 [ 3391.410630][ C12] dmcrypt_write+0x1e4/0x208 [dm_crypt] [ 3391.416005][ C12] kthread+0x130/0x138 [ 3391.419911][ C12] ret_from_fork+0x10/0x18 "}], "references": [{"url": "https://git.kernel.org/stable/c/2c743db1193bf0e76c73d71ede08bd9b96e6c31d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/66ff37993dd7e9954b6446237fe2453b380ce40d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7b9f8efb5fc888dd938d2964e705b8e00f1dc0f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/885c28ceae7dab2b18c2cc0eb95f1f82b1f629d1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e87cd83f70504f1cd2e428966f353c007d6d2d7f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb485b7404a281d974bd445ddc5b0b8d5958f371", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f0eb61b493dbbc32529fbd0d2e945b71b0e47306", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fb294b1c0ba982144ca467a75e7d01ff26304e2b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-45800", "published_date": "2025-05-02T17:15:52.423", "last_modified_date": "2025-06-04T17:26:02.743", "descriptions": [{"lang": "en", "value": "TOTOLINK A950RG V4.1.2cu.5204_B20210112 contains a command execution vulnerability in the setDeviceName interface of the /lib/cste_modules/global.so library, specifically in the processing of the deviceMac parameter."}, {"lang": "es", "value": "TOTOLINK A950RG V4.1.2cu.5204_B20210112 contiene una vulnerabilidad de ejecución de comandos en la interfaz setDeviceName de la librería /lib/cste_modules/global.so, específicamente en el procesamiento del parámetro deviceMac."}], "references": [{"url": "https://github.com/SunnyYANGyaya/cuicuishark-sheep-fishIOT/blob/main/ToTolink/A950RG/5024-setDeviceName-deviceMac-command.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2022-49875", "published_date": "2025-05-01T15:16:12.450", "last_modified_date": "2025-05-07T13:21:37.030", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpftool: Fix NULL pointer dereference when pin {PROG, MAP, LINK} without FILE\n\nWhen using bpftool to pin {PROG, MAP, LINK} without FILE,\nsegmentation fault will occur. The reson is that the lack\nof FILE will cause str<PERSON> to trigger NULL pointer dereference.\nThe corresponding stacktrace is shown below:\n\ndo_pin\n  do_pin_any\n    do_pin_fd\n      mount_bpffs_for_pin\n        strlen(name) <- NULL pointer dereference\n\nFix it by adding validation to the common process."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpftool: Se corrige la desreferencia de puntero nulo al anclar {PROG, MAP, LINK} sin archivo. Al usar bpftool para anclar {PROG, MAP, LINK} sin archivo, se produce un fallo de segmentación. La razón es que la falta de archivo provoca que strlen active la desreferencia de puntero nulo. El seguimiento de pila correspondiente se muestra a continuación: do_pin do_pin_any do_pin_fd mount_bpffs_for_pin strlen(name) &lt;- Desreferencia de puntero nulo. Se corrige añadiendo validación al proceso común."}], "references": [{"url": "https://git.kernel.org/stable/c/34de8e6e0e1f66e431abf4123934a2581cb5f133", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6dcdd1b68b7f9333d48d48fc77b75e7f235f6a4a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8c80b2fca4112d724dde477aed13f7b0510a2792", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/da5161ba94c5e9182c301dd4f09c94f715c068bd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
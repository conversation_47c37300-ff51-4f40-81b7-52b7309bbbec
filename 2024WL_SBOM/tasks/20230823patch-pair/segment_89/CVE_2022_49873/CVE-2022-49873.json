{"cve_id": "CVE-2022-49873", "published_date": "2025-05-01T15:16:12.240", "last_modified_date": "2025-05-07T13:21:56.420", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: Fix wrong reg type conversion in release_reference()\n\nSome helper functions will allocate memory. To avoid memory leaks, the\nverifier requires the eBPF program to release these memories by calling\nthe corresponding helper functions.\n\nWhen a resource is released, all pointer registers corresponding to the\nresource should be invalidated. The verifier use release_references() to\ndo this job, by apply  __mark_reg_unknown() to each relevant register.\n\nIt will give these registers the type of SCALAR_VALUE. A register that\nwill contain a pointer value at runtime, but of type SCALAR_VALUE, which\nmay allow the unprivileged user to get a kernel pointer by storing this\nregister into a map.\n\nUsing __mark_reg_not_init() while NOT allow_ptr_leaks can mitigate this\nproblem."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: Arreglar conversión de tipo de registro incorrecta en release_reference() Algunas funciones auxiliares asignarán memoria. Para evitar fugas de memoria, el verificador requiere que el programa eBPF libere estas memorias llamando a las funciones auxiliares correspondientes. Cuando se libera un recurso, todos los registros de puntero correspondientes al recurso deben invalidarse. El verificador usa release_references() para realizar este trabajo, aplicando __mark_reg_unknown() a cada registro relevante. Les dará a estos registros el tipo SCALAR_VALUE. Un registro que contendrá un valor de puntero en tiempo de ejecución, pero de tipo SCALAR_VALUE, puede permitir que el usuario sin privilegios obtenga un puntero del kernel almacenando este registro en un mapa. Usar __mark_reg_not_init() mientras NO allow_ptr_leaks puede mitigar este problema."}], "references": [{"url": "https://git.kernel.org/stable/c/466ce46f251dfb259a8cbaa895ab9edd6fb56240", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ae5ccad6c711db0f2ca1231be051935dd128b8f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cedd4f01f67be94735f15123158f485028571037", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f1db20814af532f85e091231223e5e4818e8464b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
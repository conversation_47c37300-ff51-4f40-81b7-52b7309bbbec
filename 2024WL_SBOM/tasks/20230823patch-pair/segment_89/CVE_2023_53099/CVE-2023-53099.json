{"cve_id": "CVE-2023-53099", "published_date": "2025-05-02T16:15:28.830", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfirmware: xilinx: don't make a sleepable memory allocation from an atomic context\n\nThe following issue was discovered using lockdep:\n[    6.691371] BUG: sleeping function called from invalid context at include/linux/sched/mm.h:209\n[    6.694602] in_atomic(): 1, irqs_disabled(): 128, non_block: 0, pid: 1, name: swapper/0\n[    6.702431] 2 locks held by swapper/0/1:\n[    6.706300]  #0: ffffff8800f6f188 (&dev->mutex){....}-{3:3}, at: __device_driver_lock+0x4c/0x90\n[    6.714900]  #1: ffffffc009a2abb8 (enable_lock){....}-{2:2}, at: clk_enable_lock+0x4c/0x140\n[    6.723156] irq event stamp: 304030\n[    6.726596] hardirqs last  enabled at (304029): [<ffffffc008d17ee0>] _raw_spin_unlock_irqrestore+0xc0/0xd0\n[    6.736142] hardirqs last disabled at (304030): [<ffffffc00876bc5c>] clk_enable_lock+0xfc/0x140\n[    6.744742] softirqs last  enabled at (303958): [<ffffffc0080904f0>] _stext+0x4f0/0x894\n[    6.752655] softirqs last disabled at (303951): [<ffffffc0080e53b8>] irq_exit+0x238/0x280\n[    6.760744] CPU: 1 PID: 1 Comm: swapper/0 Tainted: G     U            5.15.36 #2\n[    6.768048] Hardware name: xlnx,zynqmp (DT)\n[    6.772179] Call trace:\n[    6.774584]  dump_backtrace+0x0/0x300\n[    6.778197]  show_stack+0x18/0x30\n[    6.781465]  dump_stack_lvl+0xb8/0xec\n[    6.785077]  dump_stack+0x1c/0x38\n[    6.788345]  ___might_sleep+0x1a8/0x2a0\n[    6.792129]  __might_sleep+0x6c/0xd0\n[    6.795655]  kmem_cache_alloc_trace+0x270/0x3d0\n[    6.800127]  do_feature_check_call+0x100/0x220\n[    6.804513]  zynqmp_pm_invoke_fn+0x8c/0xb0\n[    6.808555]  zynqmp_pm_clock_getstate+0x90/0xe0\n[    6.813027]  zynqmp_pll_is_enabled+0x8c/0x120\n[    6.817327]  zynqmp_pll_enable+0x38/0xc0\n[    6.821197]  clk_core_enable+0x144/0x400\n[    6.825067]  clk_core_enable+0xd4/0x400\n[    6.828851]  clk_core_enable+0xd4/0x400\n[    6.832635]  clk_core_enable+0xd4/0x400\n[    6.836419]  clk_core_enable+0xd4/0x400\n[    6.840203]  clk_core_enable+0xd4/0x400\n[    6.843987]  clk_core_enable+0xd4/0x400\n[    6.847771]  clk_core_enable+0xd4/0x400\n[    6.851555]  clk_core_enable_lock+0x24/0x50\n[    6.855683]  clk_enable+0x24/0x40\n[    6.858952]  fclk_probe+0x84/0xf0\n[    6.862220]  platform_probe+0x8c/0x110\n[    6.865918]  really_probe+0x110/0x5f0\n[    6.869530]  __driver_probe_device+0xcc/0x210\n[    6.873830]  driver_probe_device+0x64/0x140\n[    6.877958]  __driver_attach+0x114/0x1f0\n[    6.881828]  bus_for_each_dev+0xe8/0x160\n[    6.885698]  driver_attach+0x34/0x50\n[    6.889224]  bus_add_driver+0x228/0x300\n[    6.893008]  driver_register+0xc0/0x1e0\n[    6.896792]  __platform_driver_register+0x44/0x60\n[    6.901436]  fclk_driver_init+0x1c/0x28\n[    6.905220]  do_one_initcall+0x104/0x590\n[    6.909091]  kernel_init_freeable+0x254/0x2bc\n[    6.913390]  kernel_init+0x24/0x130\n[    6.916831]  ret_from_fork+0x10/0x20\n\nFix it by passing the GFP_ATOMIC gfp flag for the corresponding\nmemory allocation."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: firmware: xilinx: no realice una asignación de memoria inactiva desde un contexto atómico El siguiente problema se descubrió utilizando lockdep: [ 6.691371] ERROR: función inactiva llamada desde un contexto no válido en include/linux/sched/mm.h:209 [ 6.694602] in_atomic(): 1, irqs_disabled(): 128, non_block: 0, pid: 1, name: swapper/0 [ 6.702431] 2 bloqueos mantenidos por swapper/0/1: [ 6.706300] #0: ffffff8800f6f188 (&amp;dev-&gt;mutex){....}-{3:3}, en: __device_driver_lock+0x4c/0x90 [ 6.714900] #1: ffffffc009a2abb8 (enable_lock){....}-{2:2}, en: clk_enable_lock+0x4c/0x140 [ 6.723156] marca de evento irq: 304030 [ 6.726596] hardirqs se habilitaron por última vez en (304029): [] _raw_spin_unlock_irqrestore+0xc0/0xd0 [ 6.736142] hardirqs se deshabilitaron por última vez en (304030): [] clk_enable_lock+0xfc/0x140 [ 6.744742] softirqs se habilitaron por última vez en (303958): [] _stext+0x4f0/0x894 [ 6.752655] Última desactivación de softirqs en (303951): [] irq_exit+0x238/0x280 [ 6.760744] CPU: 1 PID: 1 Comm: swapper/0 Contaminado: GU 5.15.36 #2 [ 6.768048] Nombre del hardware: xlnx,zynqmp (DT) [ 6.772179] Rastreo de llamadas: [ 6.774584] dump_backtrace+0x0/0x300 [ 6.778197] show_stack+0x18/0x30 [ 6.781465] dump_stack_lvl+0xb8/0xec [ 6.785077] dump_stack+0x1c/0x38 [ 6.788345] ___might_sleep+0x1a8/0x2a0 [ 6.792129] __might_sleep+0x6c/0xd0 [ 6.795655] kmem_cache_alloc_trace+0x270/0x3d0 [ 6.800127] do_feature_check_call+0x100/0x220 [ 6.804513] zynqmp_pm_invoke_fn+0x8c/0xb0 [ 6.808555] zynqmp_pm_clock_getstate+0x90/0xe0 [ 6.813027] zynqmp_pll_is_enabled+0x8c/0x120 [ 6.817327] zynqmp_pll_enable+0x38/0xc0 [ 6.821197] clk_core_enable+0x144/0x400 [ 6.825067] clk_core_enable+0xd4/0x400 [ 6.828851] clk_core_enable+0xd4/0x400 [ 6.832635] clk_core_enable+0xd4/0x400 [ 6.836419] clk_core_enable+0xd4/0x400 [ 6.840203] clk_core_enable+0xd4/0x400 [ 6.843987] clk_core_enable+0xd4/0x400 [ 6.847771] clk_core_enable+0xd4/0x400 [ 6.851555] clk_core_enable_lock+0x24/0x50 [ 6.855683] clk_enable+0x24/0x40 [ 6.858952] fclk_probe+0x84/0xf0 [ 6.862220] platform_probe+0x8c/0x110 [ 6.865918] really_probe+0x110/0x5f0 [ 6.869530] __driver_probe_device+0xcc/0x210 [ 6.873830] driver_probe_device+0x64/0x140 [ 6.877958] __driver_attach+0x114/0x1f0 [ 6.881828] bus_for_each_dev+0xe8/0x160 [ 6.885698] driver_attach+0x34/0x50 [ 6.889224] bus_add_driver+0x228/0x300 [ 6.893008] driver_register+0xc0/0x1e0 [ 6.896792] __platform_driver_register+0x44/0x60 [ 6.901436] fclk_driver_init+0x1c/0x28 [ 6.905220] do_one_initcall+0x104/0x590 [ 6.909091] kernel_init_freeable+0x254/0x2bc [ 6.913390] kernel_init+0x24/0x130 [ 6.916831] ret_from_fork+0x10/0x20 Arréglelo pasando el indicador gfp GFP_ATOMIC para la asignación de memoria correspondiente."}], "references": [{"url": "https://git.kernel.org/stable/c/162049c31eb64308afa22e341a257a723526eb5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/38ed310c22e7a0fc978b1f8292136a4a4a8b3051", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/86afb633beaa02ee95b5126a14c9f22cfade4fd9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9bbab2843f2d1337a268499a1c02b435d2985a17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b37d3ccbd549494890672136a0e623eb010d46a7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
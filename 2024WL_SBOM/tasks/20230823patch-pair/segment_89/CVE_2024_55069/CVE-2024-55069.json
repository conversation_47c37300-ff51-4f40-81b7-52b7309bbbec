{"cve_id": "CVE-2024-55069", "published_date": "2025-05-02T22:15:16.750", "last_modified_date": "2025-06-03T18:13:05.697", "descriptions": [{"lang": "en", "value": "ffmpeg 7.1 is vulnerable to Null Pointer Dereference in function iamf_read_header in /libavformat/iamfdec.c."}, {"lang": "es", "value": "ffmpeg 7.1 es vulnerable a la desreferencia de puntero nulo en la función iamf_read_header en /libavformat/iamfdec.c."}], "references": [{"url": "https://git.ffmpeg.org/gitweb/ffmpeg.git/commit/4cc1495aca45445181a107a682c32cfe31459929", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://trac.ffmpeg.org/ticket/11326", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}
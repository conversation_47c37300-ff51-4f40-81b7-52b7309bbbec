{"cve_id": "CVE-2023-53094", "published_date": "2025-05-02T16:15:28.363", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntty: serial: fsl_lpuart: fix race on RX DMA shutdown\n\nFrom time to time DMA completion can come in the middle of DMA shutdown:\n\n<process ctx>:\t\t\t\t<IRQ>:\nlpuart32_shutdown()\n  lpuart_dma_shutdown()\n    del_timer_sync()\n\t\t\t\t\tlpuart_dma_rx_complete()\n\t\t\t\t\t  lpuart_copy_rx_to_tty()\n\t\t\t\t\t    mod_timer()\n    lpuart_dma_rx_free()\n\nWhen the timer fires a bit later, sport->dma_rx_desc is NULL:\n\nUnable to handle kernel NULL pointer dereference at virtual address 0000000000000004\npc : lpuart_copy_rx_to_tty+0xcc/0x5bc\nlr : lpuart_timer_func+0x1c/0x2c\nCall trace:\n lpuart_copy_rx_to_tty\n lpuart_timer_func\n call_timer_fn\n __run_timers.part.0\n run_timer_softirq\n __do_softirq\n __irq_exit_rcu\n irq_exit\n handle_domain_irq\n gic_handle_irq\n call_on_irq_stack\n do_interrupt_handler\n ...\n\nTo fix this fold del_timer_sync() into lpuart_dma_rx_free() after\ndmaengine_terminate_sync() to make sure timer will not be re-started in\nlpuart_copy_rx_to_tty() <= lpuart_dma_rx_complete()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tty: serial: fsl_lpuart: fix race on RX DMA shutting De vez en cuando, la finalización de DMA puede llegar en medio del shutting de DMA: : : lpuart32_shutdown() lpuart_dma_shutdown() del_timer_sync() lpuart_dma_rx_complete() lpuart_copy_rx_to_tty() mod_timer() lpuart_dma_rx_free() Cuando el temporizador se activa un poco más tarde, sport-&gt;dma_rx_desc es NULL: No se puede manejar la desreferencia del puntero NULL del kernel en la dirección virtual 0000000000000004 pc : lpuart_copy_rx_to_tty+0xcc/0x5bc lr : lpuart_timer_func+0x1c/0x2c Rastreo de llamadas: lpuart_copy_rx_to_tty lpuart_timer_func call_timer_fn __run_timers.part.0 run_timer_softirq __do_softirq __irq_exit_rcu irq_exit handle_domain_irq gic_handle_irq call_on_irq_stack do_interrupt_handler ... Para solucionar esto, incorpore del_timer_sync() en lpuart_dma_rx_free() después de dmaengine_terminate_sync() para asegurarse de que el temporizador no se reinicie en lpuart_copy_rx_to_tty() &lt;= lpuart_dma_rx_complete()."}], "references": [{"url": "https://git.kernel.org/stable/c/19a98d56dfedafb25652bdb9cd48a4e73ceba702", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1be6f2b15f902c02e055ae0b419ca789200473c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2a36b444cace9580380467fd1183bb5e85bcc80a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90530e7214c8a04dcdde57502d93fa96af288c38", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/954fc9931f0aabf272b5674cf468affdd88d3a36", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
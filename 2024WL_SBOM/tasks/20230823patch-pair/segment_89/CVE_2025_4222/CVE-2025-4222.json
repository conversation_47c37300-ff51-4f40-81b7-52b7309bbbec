{"cve_id": "CVE-2025-4222", "published_date": "2025-05-03T03:15:29.217", "last_modified_date": "2025-05-13T18:15:41.657", "descriptions": [{"lang": "en", "value": "The Database Toolset plugin for WordPress is vulnerable to Sensitive Information Exposure in all versions up to, and including, 1.8.4 via backup files stored in a publicly accessible location. This makes it possible for unauthenticated attackers to extract sensitive data from database backup files. An index file is present, so a brute force attack would need to be successful in order to compromise any data."}, {"lang": "es", "value": "El complemento Database Toolset para WordPress es vulnerable a la exposición de información confidencial en todas las versiones hasta la 1.8.4 incluida, a través de archivos de copia de seguridad almacenados en una ubicación de acceso público. Esto permite a atacantes no autenticados extraer información confidencial de los archivos de copia de seguridad de la base de datos. Existe un archivo de índice, por lo que un ataque de fuerza bruta tendría que tener éxito para comprometer los datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/database-toolset/trunk/admin/class-database-toolset-admin.php#L247", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/database-toolset/trunk/admin/class-database-toolset-backup.php#L76", "source": "<EMAIL>", "tags": []}, {"url": "https://www.guyshavit.com/post/cve-2025-4222", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fa452a9a-9e26-41a1-8dea-4bafaf735bee?source=cve", "source": "<EMAIL>", "tags": []}]}
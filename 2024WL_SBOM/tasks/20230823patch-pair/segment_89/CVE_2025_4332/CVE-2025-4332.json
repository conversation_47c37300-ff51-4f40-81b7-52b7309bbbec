{"cve_id": "CVE-2025-4332", "published_date": "2025-05-06T08:15:17.990", "last_modified_date": "2025-06-05T06:15:26.503", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Company Visitor Management System 2.0 and classified as critical. Affected by this issue is some unknown functionality of the file /visitor-detail.php. The manipulation of the argument editid/remark leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Company Visitor Management System 2.0, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /visitor-detail.php. La manipulación del argumento editid provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ctf2868/pjwww/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307436", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307436", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564339", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564729", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.568338", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.590559", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Pjwww13447/pjwww/issues/1", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit", "Third Party Advisory"]}]}
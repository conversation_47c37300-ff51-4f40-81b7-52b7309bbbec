{"cve_id": "CVE-2025-24344", "published_date": "2025-04-30T12:15:18.120", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "A vulnerability in the error notification messages of the web application of ctrlX OS allows a remote unauthenticated attacker to inject arbitrary HTML tags and, possibly, execute arbitrary client-side code in the context of another user's browser via a crafted HTTP request."}, {"lang": "es", "value": "Una vulnerabilidad en los mensajes de notificación de errores de la aplicación web de ctrlX OS permite a un atacante remoto no autenticado inyectar etiquetas HTML arbitrarias y, posiblemente, ejecutar código arbitrario del lado del cliente en el contexto del navegador de otro usuario a través de una solicitud HTTP manipulada."}], "references": [{"url": "https://psirt.bosch.com/security-advisories/BOSCH-SA-640452.html", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-4263", "published_date": "2025-05-05T04:16:20.583", "last_modified_date": "2025-05-07T16:32:04.363", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online DJ Booking Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /admin/booking-search.php. The manipulation of the argument searchdata leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online DJ Booking Management System 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/booking-search.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/MoshangChunfeng/CVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307367", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307367", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562991", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
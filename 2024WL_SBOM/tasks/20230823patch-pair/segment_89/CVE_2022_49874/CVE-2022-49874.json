{"cve_id": "CVE-2022-49874", "published_date": "2025-05-01T15:16:12.340", "last_modified_date": "2025-05-07T13:21:49.873", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nHID: hyperv: fix possible memory leak in mousevsc_probe()\n\nIf hid_add_device() returns error, it should call hid_destroy_device()\nto free hid_dev which is allocated in hid_allocate_device()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: HID: hyperv: corrige posible pérdida de memoria en mousevsc_probe() Si hid_add_device() devuelve un error, debe llamar a hid_destroy_device() para liberar hid_dev que está asignado en hid_allocate_device()."}], "references": [{"url": "https://git.kernel.org/stable/c/249b743801c00542e9324f87b380032e957a43e8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5ad95d71344b7ffec360d62591633b3c465dc049", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5f3aba6566b866f5b0a4916f0b2e8a6ae66a6451", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8597b59e3d22b27849bd3e4f92a3d466774bfb04", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a6d2fb1874c52ace1f5cf1966ee558829c5c19b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b5bcb94b0954a026bbd671741fdb00e7141f9c91", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e29289d0d8193fca6d2c1f0a1de75cfc80edec00", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ed75d1a1c31a0cae8ecc8bcea710b25c0be68da0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
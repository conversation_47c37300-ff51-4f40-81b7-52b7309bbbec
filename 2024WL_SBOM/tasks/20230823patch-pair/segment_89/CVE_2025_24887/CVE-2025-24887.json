{"cve_id": "CVE-2025-24887", "published_date": "2025-04-30T19:15:55.070", "last_modified_date": "2025-05-19T11:51:33.870", "descriptions": [{"lang": "en", "value": "OpenCTI is an open-source cyber threat intelligence platform. In versions starting from 6.4.8 to before 6.4.10, the allow/deny lists can be bypassed, allowing a user to change attributes that are intended to be unmodifiable by the user. It is possible to toggle the `external` flag on/off and change the own token value for a user. It is also possible to edit attributes that are not in the allow list, such as `otp_qr` and `otp_activated`. If external users exist in the OpenCTI setup and the information about these users identities is sensitive, the above vulnerabilities can be used to enumerate existing user accounts as a standard low privileged user. This issue has been patched in version 6.4.10."}, {"lang": "es", "value": "OpenCTI es una plataforma de inteligencia de ciberamenazas de código abierto. En versiones desde la 6.4.8 hasta anteriores a la 6.4.10, se pueden omitir las listas de permitidos/denegados, lo que permite al usuario cambiar atributos que no se deben modificar. Es posible activar o desactivar la opción \"external\" y cambiar el valor del token de un usuario. También es posible editar atributos que no están en la lista de permitidos, como \"otp_qr\" y \"otp_activated\". Si existen usuarios externos en la configuración de OpenCTI y la información sobre sus identidades es confidencial, las vulnerabilidades mencionadas anteriormente se pueden utilizar para enumerar las cuentas de usuario existentes como un usuario estándar con privilegios bajos. Este problema se ha corregido en la versión 6.4.10."}], "references": [{"url": "https://github.com/OpenCTI-Platform/opencti/security/advisories/GHSA-8262-pw2q-5qc3", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2025-4262", "published_date": "2025-05-05T04:16:17.080", "last_modified_date": "2025-05-07T16:32:54.260", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online DJ Booking Management System 1.0. It has been declared as critical. This vulnerability affects unknown code of the file /admin/user-search.php. The manipulation of the argument searchdata leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online DJ Booking Management System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/user-search.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/MoshangChunfeng/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307366", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307366", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562965", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
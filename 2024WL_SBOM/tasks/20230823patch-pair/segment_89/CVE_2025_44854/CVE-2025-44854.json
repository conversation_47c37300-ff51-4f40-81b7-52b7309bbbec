{"cve_id": "CVE-2025-44854", "published_date": "2025-05-01T14:15:45.593", "last_modified_date": "2025-05-22T15:32:20.913", "descriptions": [{"lang": "en", "value": "TOTOLINK CP900 V6.3c.1144_B20190715 was found to contain a command injection vulnerability in the setUpgradeUboot function via the FileName parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CP900 V6.3c.1144_B20190715 contenía una vulnerabilidad de inyección de comandos en la función setUpgradeUboot mediante el parámetro FileName. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CP900/setUpgradeUboot/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
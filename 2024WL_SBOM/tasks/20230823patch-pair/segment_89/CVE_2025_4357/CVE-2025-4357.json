{"cve_id": "CVE-2025-4357", "published_date": "2025-05-06T14:15:42.287", "last_modified_date": "2025-05-13T20:24:04.577", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Tenda RX3 16.03.13.11_multi. It has been rated as critical. This issue affects some unknown processing of the file /goform/telnet. The manipulation leads to command injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Tenda RX3 16.03.13.11_multi. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /goform/telnet. La manipulación provoca la inyección de comandos. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Ghostsuzhijian/Iot-/blob/main/RX3_telnetd/rx3_telnetd.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307475", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307475", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564727", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}
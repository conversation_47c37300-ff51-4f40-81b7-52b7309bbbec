{"cve_id": "CVE-2025-46814", "published_date": "2025-05-06T15:16:02.217", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "FastAPI Guard is a security library for FastAPI that provides middleware to control IPs, log requests, and detect penetration attempts. An HTTP header injection vulnerability has been identified in versions prior to 2.0.0. By manipulating the X-Forwarded-For header, an attacker can potentially inject arbitrary IP addresses into the request. This vulnerability can allow attackers to bypass IP-based access controls, mislead logging systems, and impersonate trusted clients. It is especially impactful when the application relies on the X-Forwarded-For header for IP-based authorization or authentication. Users should upgrade to FastAPI Guard version 2.0.0 to receive a fix."}, {"lang": "es", "value": "FastAPI Guard es una librería de seguridad para FastAPI que proporciona middleware para controlar IP, registrar solicitudes y detectar intentos de penetración. Se ha identificado una vulnerabilidad de inyección de encabezado HTTP en versiones anteriores a la 2.0.0. Al manipular el encabezado X-Forwarded-For, un atacante podría inyectar direcciones IP arbitrarias en la solicitud. Esta vulnerabilidad puede permitir a los atacantes eludir los controles de acceso basados en IP, engañar a los sistemas de registro y suplantar la identidad de clientes de confianza. Es especialmente grave cuando la aplicación utiliza el encabezado X-Forwarded-For para la autorización o autenticación basadas en IP. Los usuarios deben actualizar a la versión 2.0.0 de FastAPI Guard para obtener una corrección."}], "references": [{"url": "https://github.com/rennf93/fastapi-guard/commit/0b003fda4c678c1b514e95f319aee88113e9bf4b", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/rennf93/fastapi-guard/security/advisories/GHSA-77q8-qmj7-x7pp", "source": "<EMAIL>", "tags": []}]}
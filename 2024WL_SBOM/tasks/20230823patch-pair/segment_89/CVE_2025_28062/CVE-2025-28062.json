{"cve_id": "CVE-2025-28062", "published_date": "2025-05-05T16:15:51.310", "last_modified_date": "2025-06-17T14:13:04.563", "descriptions": [{"lang": "en", "value": "A Cross-Site Request Forgery (CSRF) vulnerability was discovered in ERPNEXT 14.82.1 and 14.74.3. The vulnerability allows an attacker to perform unauthorized actions such as user deletion, password resets, and privilege escalation due to missing CSRF protections."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de Cross-Site Request Forgery (CSRF) en ERPNEXT 14.82.1 y 14.74.3. Esta vulnerabilidad permite a un atacante realizar acciones no autorizadas, como la eliminación de usuarios, el restablecimiento de contraseñas y la escalada de privilegios, debido a la falta de protección CSRF."}], "references": [{"url": "https://github.com/Thvt0ne/CVE-2025-28062", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/frappe/erpnext", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2025-4195", "published_date": "2025-05-02T01:15:54.917", "last_modified_date": "2025-05-16T17:35:00.933", "descriptions": [{"lang": "en", "value": "A vulnerability was found in itsourcecode Gym Management System 1.0. It has been declared as critical. This vulnerability affects unknown code of the file /ajax.php?action=save_member. The manipulation of the argument umember_id leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en itsourcecode Gym Management System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /ajax.php?action=save_member. La manipulación del argumento umember_id provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cybersecurity/vuldb/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://itsourcecode.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306808", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306808", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561876", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://github.com/XuepengZhao-insp/vuldb/issues/6", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-23147", "published_date": "2025-05-01T13:15:50.563", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ni3c: Add NULL pointer check in i3c_master_queue_ibi()\n\nThe I3C master driver may receive an IBI from a target device that has not\nbeen probed yet. In such cases, the master calls `i3c_master_queue_ibi()`\nto queue an IBI work task, leading to \"Unable to handle kernel read from\nunreadable memory\" and resulting in a kernel panic.\n\nTypical IBI handling flow:\n1. The I3C master scans target devices and probes their respective drivers.\n2. The target device driver calls `i3c_device_request_ibi()` to enable IBI\n   and assigns `dev->ibi = ibi`.\n3. The I3C master receives an IBI from the target device and calls\n   `i3c_master_queue_ibi()` to queue the target device driver’s IBI\n   handler task.\n\nHowever, since target device events are asynchronous to the I3C probe\nsequence, step 3 may occur before step 2, causing `dev->ibi` to be `NULL`,\nleading to a kernel panic.\n\nAdd a NULL pointer check in `i3c_master_queue_ibi()` to prevent accessing\nan uninitialized `dev->ibi`, ensuring stability."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: i3c: Añadir comprobación de puntero nulo en i3c_master_queue_ibi(). El controlador maestro I3C puede recibir una IBI de un dispositivo de destino que aún no se ha sondeado. En tales casos, el maestro llama a `i3c_master_queue_ibi()` para poner en cola una tarea de trabajo IBI, lo que genera el error \"No se puede manejar la lectura del kernel desde memoria ilegible\" y provoca un pánico del kernel. Flujo típico de manejo de IBI: 1. El maestro I3C escanea los dispositivos de destino y sondea sus respectivos controladores. 2. El controlador del dispositivo de destino llama a `i3c_device_request_ibi()` para habilitar IBI y asigna `dev-&gt;ibi = ibi`. 3. El maestro I3C recibe una IBI del dispositivo de destino y llama a `i3c_master_queue_ibi()` para poner en cola la tarea de manejo de IBI del controlador del dispositivo de destino. Sin embargo, dado que los eventos del dispositivo de destino son asíncronos a la secuencia de sondeo I3C, el paso 3 puede ocurrir antes del paso 2, lo que provoca que `dev-&gt;ibi` sea `NULL` y provoque un pánico del kernel. Agregue una comprobación de puntero NULL en `i3c_master_queue_ibi()` para evitar el acceso a un `dev-&gt;ibi` sin inicializar, garantizando así la estabilidad."}], "references": [{"url": "https://git.kernel.org/stable/c/09359e7c8751961937cb5fc50220969b0a4e1058", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1b54faa5f47fa7c642179744aeff03f0810dc62e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3ba402610843d7d15c7f3966a461deeeaff7fba4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6871a676aa534e8f218279672e0445c725f81026", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bd496a44f041da9ef3afe14d1d6193d460424e91", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d83b0c03ef8fbea2f03029a1cc1f5041f0e1d47f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e6bba328578feb58c614c11868c259b40484c5fa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fe4a4fc179b7898055555a11685915473588392e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ff9d61db59bb27d16d3f872bff2620d50856b80c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
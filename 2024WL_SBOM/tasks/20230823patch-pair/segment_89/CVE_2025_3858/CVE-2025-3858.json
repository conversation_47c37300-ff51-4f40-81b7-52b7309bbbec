{"cve_id": "CVE-2025-3858", "published_date": "2025-05-02T04:15:56.040", "last_modified_date": "2025-05-06T15:20:09.163", "descriptions": [{"lang": "en", "value": "The Formality plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘align’ parameter in all versions up to, and including, 1.5.8 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Formality para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del parámetro 'align' en todas las versiones hasta la 1.5.8 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/formality/trunk/public/class-formality-form.php#L137", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3285036/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/formality/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e25157d3-42ac-4dd6-a736-5623a16e5629?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
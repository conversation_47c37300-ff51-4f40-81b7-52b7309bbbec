{"cve_id": "CVE-2025-4174", "published_date": "2025-05-01T19:15:59.333", "last_modified_date": "2025-05-09T13:42:33.097", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul COVID19 Testing Management System 1.0. Affected by this issue is some unknown functionality of the file /login.php. The manipulation of the argument Username leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul COVID19 Testing Management System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /login.php. La manipulación del argumento \"Username\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/FLYFISH567/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306794", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.306794", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561746", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
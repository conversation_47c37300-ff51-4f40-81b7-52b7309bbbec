{"cve_id": "CVE-2022-49863", "published_date": "2025-05-01T15:16:11.203", "last_modified_date": "2025-05-07T13:22:40.637", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncan: af_can: fix NULL pointer dereference in can_rx_register()\n\nIt causes NULL pointer dereference when testing as following:\n(a) use syscall(__NR_socket, 0x10ul, 3ul, 0) to create netlink socket.\n(b) use syscall(__NR_sendmsg, ...) to create bond link device and vxcan\n    link device, and bind vxcan device to bond device (can also use\n    ifenslave command to bind vxcan device to bond device).\n(c) use syscall(__NR_socket, 0x1dul, 3ul, 1) to create CAN socket.\n(d) use syscall(__NR_bind, ...) to bind the bond device to CAN socket.\n\nThe bond device invokes the can-raw protocol registration interface to\nreceive CAN packets. However, ml_priv is not allocated to the dev,\ndev_rcv_lists is assigned to NULL in can_rx_register(). In this case,\nit will occur the NULL pointer dereference issue.\n\nThe following is the stack information:\nBUG: kernel NULL pointer dereference, address: 0000000000000008\nPGD 122a4067 P4D 122a4067 PUD 1223c067 PMD 0\nOops: 0000 [#1] PREEMPT SMP\nRIP: 0010:can_rx_register+0x12d/0x1e0\nCall Trace:\n<TASK>\nraw_enable_filters+0x8d/0x120\nraw_enable_allfilters+0x3b/0x130\nraw_bind+0x118/0x4f0\n__sys_bind+0x163/0x1a0\n__x64_sys_bind+0x1e/0x30\ndo_syscall_64+0x35/0x80\nentry_SYSCALL_64_after_hwframe+0x63/0xcd\n</TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: can: af_can: corrige la desreferencia del puntero NULL en can_rx_register() Provoca la desreferencia del puntero NULL cuando se prueba lo siguiente: (a) use syscall(__NR_socket, 0x10ul, 3ul, 0) para crear el socket netlink. (b) use syscall(__NR_sendmsg, ...) para crear el dispositivo de enlace de enlace y el dispositivo de enlace vxcan, y enlace el dispositivo vxcan al dispositivo de enlace (también puede usar el comando ifenslave para enlazar el dispositivo vxcan al dispositivo de enlace). (c) use syscall(__NR_socket, 0x1dul, 3ul, 1) para crear el socket CAN. (d) use syscall(__NR_bind, ...) para enlazar el dispositivo de enlace al socket CAN. El dispositivo de enlace invoca la interfaz de registro del protocolo can-raw para recibir paquetes CAN. Sin embargo, ml_priv no está asignado a dev, y dev_rcv_lists está asignado a NULL en can_rx_register(). En este caso, se producirá el problema de desreferencia del puntero NULL. La siguiente es la información de la pila: ERROR: desreferencia de puntero NULL del núcleo, dirección: 0000000000000008 PGD 122a4067 P4D 122a4067 PUD 1223c067 PMD 0 Oops: 0000 [#1] PREEMPT SMP RIP: 0010:can_rx_register+0x12d/0x1e0 Seguimiento de llamadas:  raw_enable_filters+0x8d/0x120 raw_enable_allfilters+0x3b/0x130 raw_bind+0x118/0x4f0 __sys_bind+0x163/0x1a0 __x64_sys_bind+0x1e/0x30 do_syscall_64+0x35/0x80 entry_SYSCALL_64_after_hwframe+0x63/0xcd  "}], "references": [{"url": "https://git.kernel.org/stable/c/261178a1c2623077d62e374a75c195e6c99a6f05", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8aa59e355949442c408408c2d836e561794c40a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a8055677b054bc2bb78beb1080fdc2dc5158c2fe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/afab4655750fcb3fca359bc7d7214e3d634cdf9c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d68fa77ee3d03bad6fe84e89759ddf7005f9e9c6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
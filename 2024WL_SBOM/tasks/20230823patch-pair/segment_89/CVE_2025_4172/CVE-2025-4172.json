{"cve_id": "CVE-2025-4172", "published_date": "2025-05-03T03:15:28.640", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The VerticalResponse Newsletter Widget plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'verticalresponse' shortcode in all versions up to, and including, 1.6 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento VerticalResponse Newsletter Widget para WordPress es vulnerable a Cross-Site Scripting almacenado a través del shortcode \"verticalresponse\" en todas las versiones hasta la 1.6 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wordpress.org/plugins/vertical-response-newsletter-widget/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0565cdf1-55fe-4676-8529-8c79be5e8b01?source=cve", "source": "<EMAIL>", "tags": []}]}
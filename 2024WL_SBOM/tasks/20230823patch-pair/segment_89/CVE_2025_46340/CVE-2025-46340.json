{"cve_id": "CVE-2025-46340", "published_date": "2025-05-05T19:15:56.627", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Misskey is an open source, federated social media platform. Starting in version 12.0.0 and prior to version 2025.4.1, due to an oversight in the validation performed in `UrlPreviewService` and `MkUrlPreview`, it is possible for an attacker to inject arbitrary CSS into the `MkUrlPreview` component. `UrlPreviewService.wrap` falls back to returning the original URL if it's using a protocol that is likely to not be understood by <PERSON><PERSON>, IE something other than `http` or `https`. This both can de-anonymize users and_allow further attacks in the client. Additionally, `MkUrlPreview` doesn't escape CSS when applying a `background-image` property, allowing an attacker to craft a URL that applies arbitrary styles to the preview element. Theoretically, an attacker can craft a CSS injection payload to create a fake error message that can deceive the user into giving away their credentials or similar sensitive information. Version 2025.4.1 contains a patch for the issue."}, {"lang": "es", "value": "Misskey es una plataforma de redes sociales federada de código abierto. A partir de la versión 12.0.0 y anteriores a la 2025.4.1, debido a un descuido en la validación de `UrlPreviewService` y `MkUrlPreview`, un atacante puede inyectar CSS arbitrario en el componente `MkUrlPreview`. `UrlPreviewService.wrap` recurre a la URL original si utiliza un protocolo que probablemente Misskey no entienda, es decir, algo distinto de `http` o `https`. Esto puede desanonimizar a los usuarios y permitir futuros ataques en el cliente. Además, `MkUrlPreview` no escapa el CSS al aplicar la propiedad `background-image`, lo que permite a un atacante manipular una URL que aplique estilos arbitrarios al elemento de vista previa. En teoría, un atacante puede crear un payload de inyección de CSS para manipular un mensaje de error falso que pueda engañar al usuario para que revele sus credenciales o información confidencial similar. La versión 2025.4.1 contiene un parche para el problema."}], "references": [{"url": "https://github.com/misskey-dev/misskey/commit/d10fdfe9738b17a9d81037c031b40a2cc4cb8038", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/misskey-dev/misskey/security/advisories/GHSA-3p2w-xmv5-jm95", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-4178", "published_date": "2025-05-01T22:15:18.140", "last_modified_date": "2025-06-17T14:18:28.283", "descriptions": [{"lang": "en", "value": "A vulnerability was found in xiaowei1118 java_server up to 11a5bac8f4ba1c17e4bc1b27cad6d24868500e3a on Windows and classified as critical. This issue affects some unknown processing of the file /src/main/java/com/changyu/foryou/controller/FoodController.java of the component File Upload API. The manipulation leads to path traversal. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en xiaowei1118 java_server hasta 11a5bac8f4ba1c17e4bc1b27cad6d24868500e3a en Windows, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /src/main/java/com/changyu/foryou/controller/FoodController.java del componente File Upload API. La manipulación provoca un path traversal. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza el enfoque de lanzamiento continuo para garantizar una entrega continua. Por lo tanto, no se dispone de detalles de las versiones afectadas ni de las actualizadas."}], "references": [{"url": "https://github.com/ShenxiuSec/cve-proofs/blob/main/POC-20250418-02.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.306797", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306797", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561794", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}]}
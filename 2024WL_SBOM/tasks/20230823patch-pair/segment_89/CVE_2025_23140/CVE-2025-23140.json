{"cve_id": "CVE-2025-23140", "published_date": "2025-05-01T13:15:49.807", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmisc: pci_endpoint_test: Avoid issue of interrupts remaining after request_irq error\n\nAfter devm_request_irq() fails with error in pci_endpoint_test_request_irq(),\nthe pci_endpoint_test_free_irq_vectors() is called assuming that all IRQs\nhave been released.\n\nHowever, some requested IRQs remain unreleased, so there are still\n/proc/irq/* entries remaining, and this results in WARN() with the\nfollowing message:\n\n  remove_proc_entry: removing non-empty directory 'irq/30', leaking at least 'pci-endpoint-test.0'\n  WARNING: CPU: 0 PID: 202 at fs/proc/generic.c:719 remove_proc_entry +0x190/0x19c\n\nTo solve this issue, set the number of remaining IRQs to test->num_irqs,\nand release IRQs in advance by calling pci_endpoint_test_release_irq().\n\n[kwi<PERSON><PERSON><PERSON><PERSON>: commit log]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: misc: pci_endpoint_test: evitar el problema de las interrupciones restantes después del error request_irq Después de que devm_request_irq() falla con un error en pci_endpoint_test_request_irq(), se llama a pci_endpoint_test_free_irq_vectors() asumiendo que se han liberado todas las IRQ. Sin embargo, algunas IRQ solicitadas permanecen sin liberar, por lo que aún quedan entradas /proc/irq/* restantes y esto genera un WARN() con el siguiente mensaje: remove_proc_entry: eliminando el directorio no vacío 'irq/30', filtrando al menos 'pci-endpoint-test.0' ADVERTENCIA: CPU: 0 PID: 202 en fs/proc/generic.c:719 remove_proc_entry +0x190/0x19c Para resolver este problema, establezca el número de IRQ restantes en test-&gt;num_irqs y libere las IRQ con anticipación llamando a pci_endpoint_test_release_irq(). [kwi<PERSON><PERSON><PERSON>ski: registro de confirmaciones]"}], "references": [{"url": "https://git.kernel.org/stable/c/0557e70e2aeba8647bf5a950820b67cfb86533db", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/54c9f299ad7d7c4be5d271ed12d01a59e95b8907", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5a4b7181213268c9b07bef8800905528435db44a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/705be96504779e4a333ea042b4779ea941f0ace9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/770407f6173f4f39f4e2c1b54422b79ce6c98bdb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9d5118b107b1a2353ed0dff24404aee2e6b7ca0a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e516e187bf32d8decc7c7d0025ae4857cad13c0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f6cb7828c8e17520d4f5afb416515d3fae1af9a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
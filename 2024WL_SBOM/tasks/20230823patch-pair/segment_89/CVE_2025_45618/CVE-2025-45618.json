{"cve_id": "CVE-2025-45618", "published_date": "2025-05-05T20:15:21.003", "last_modified_date": "2025-05-06T04:16:18.347", "descriptions": [{"lang": "en", "value": "Incorrect access control in the component /admin/sys/datasource/ajaxList of jeeweb-mybatis-springboot v0.0.1.RELEASE allows attackers to access sensitive information via a crafted payload."}, {"lang": "es", "value": "El control de acceso incorrecto en el componente /admin/sys/datasource/ajaxList de jeeweb-mybatis-springboot v0.0.1.RELEASE permite a los atacantes acceder a información confidencial a través de un payload manipulado."}], "references": [{"url": "https://github.com/huangjian888/jeeweb-mybatis-springboot/issues/31", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2023-53118", "published_date": "2025-05-02T16:15:30.880", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: core: Fix a procfs host directory removal regression\n\nscsi_proc_hostdir_rm() decreases a reference counter and hence must only be\ncalled once per host that is removed. This change does not require a\nscsi_add_host_with_dma() change since scsi_add_host_with_dma() will return\n0 (success) if scsi_proc_host_add() is called."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: core: Se corrige una regresión de eliminación del directorio del host procfs. scsi_proc_hostdir_rm() disminuye un contador de referencias y, por lo tanto, solo debe llamarse una vez por cada host eliminado. Este cambio no requiere modificar scsi_add_host_with_dma(), ya que scsi_add_host_with_dma() devolverá 0 (éxito) si se llama a scsi_proc_host_add()."}], "references": [{"url": "https://git.kernel.org/stable/c/2a764d55e938743efa7c2cba7305633bcf227f09", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/68c665bb185037e7eb66fb792c61da9d7151e99c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/73f030d4ef6d1ad17f824a0a2eb637ef7a9c7d51", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7e0ae8667fcdd99d1756922e1140cac75f5fa279", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/88c3d3bb6469cea929ac68fd326bdcbefcdfdd83", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be03df3d4bfe7e8866d4aa43d62e648ffe884f5f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
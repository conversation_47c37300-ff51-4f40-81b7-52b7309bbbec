{"cve_id": "CVE-2025-30165", "published_date": "2025-05-06T17:16:11.660", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "vLLM is an inference and serving engine for large language models. In a multi-node vLLM deployment using the V0 engine, vLLM uses ZeroMQ for some multi-node communication purposes. The secondary vLLM hosts open a `SUB` ZeroMQ socket and connect to an `XPUB` socket on the primary vLLM host. When data is received on this `SUB` socket, it is deserialized with `pickle`. This is unsafe, as it can be abused to execute code on a remote machine. Since the vulnerability exists in a client that connects to the primary vLLM host, this vulnerability serves as an escalation point. If the primary vLLM host is compromised, this vulnerability could be used to compromise the rest of the hosts in the vLLM deployment. Attackers could also use other means to exploit the vulnerability without requiring access to the primary vLLM host. One example would be the use of ARP cache poisoning to redirect traffic to a malicious endpoint used to deliver a payload with arbitrary code to execute on the target machine. Note that this issue only affects the V0 engine, which has been off by default since v0.8.0. Further, the issue only applies to a deployment using tensor parallelism across multiple hosts, which we do not expect to be a common deployment pattern. Since V0 is has been off by default since v0.8.0 and the fix is fairly invasive, the maintainers of vLLM have decided not to fix this issue. Instead, the maintainers recommend that users ensure their environment is on a secure network in case this pattern is in use. The V1 engine is not affected by this issue."}, {"lang": "es", "value": "vLLM es un motor de inferencia y servicio para modelos de lenguaje extensos. En una implementación de vLLM multinodo con el motor V0, vLLM utiliza ZeroMQ para la comunicación multinodo. Los hosts secundarios de vLLM abren un socket \"SUB\" de ZeroMQ y se conectan a un socket \"XPUB\" en el host principal de vLLM. Cuando se reciben datos en este socket \"SUB\", se deserializan con \"pickle\". Esto es peligroso, ya que puede utilizarse para ejecutar código en una máquina remota. Dado que la vulnerabilidad existe en un cliente que se conecta al host principal de vLLM, sirve como punto de escalada. Si el host principal de vLLM se ve comprometido, esta vulnerabilidad podría utilizarse para comprometer el resto de los hosts de la implementación de vLLM. Los atacantes también podrían utilizar otros medios para explotar la vulnerabilidad sin necesidad de acceder al host principal de vLLM. Un ejemplo sería el uso de envenenamiento de caché ARP para redirigir el tráfico a un endpoint malicioso utilizado para entregar un payload con código arbitrario que se ejecuta en la máquina objetivo. Tenga en cuenta que este problema solo afecta al motor V0, que ha estado desactivado por defecto desde la versión v0.8.0. Además, el problema solo se aplica a implementaciones que utilizan paralelismo tensorial en varios hosts, lo cual no esperamos que sea un patrón de implementación común. Dado que V0 ha estado desactivado por defecto desde la versión v0.8.0 y la solución es bastante invasiva, los responsables de vLLM han decidido no corregir este problema. En su lugar, recomiendan a los usuarios que se aseguren de que su entorno esté en una red segura en caso de que se utilice este patrón. El motor V1 no se ve afectado por este problema."}], "references": [{"url": "https://github.com/vllm-project/vllm/blob/c21b99b91241409c2fdf9f3f8c542e8748b317be/vllm/distributed/device_communicators/shm_broadcast.py#L295-L301", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vllm-project/vllm/blob/c21b99b91241409c2fdf9f3f8c542e8748b317be/vllm/distributed/device_communicators/shm_broadcast.py#L468-L470", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-9pcc-gvx5-r5wm", "source": "<EMAIL>", "tags": []}]}
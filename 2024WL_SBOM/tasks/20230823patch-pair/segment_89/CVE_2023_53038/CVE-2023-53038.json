{"cve_id": "CVE-2023-53038", "published_date": "2025-05-02T16:15:22.920", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: lpfc: Check kzalloc() in lpfc_sli4_cgn_params_read()\n\nIf kzalloc() fails in lpfc_sli4_cgn_params_read(), then we rely on\nlpfc_read_object()'s routine to NULL check pdata.\n\nCurrently, an early return error is thrown from lpfc_read_object() to\nprotect us from NULL ptr dereference, but the errno code is -ENODEV.\n\nChange the errno code to a more appropriate -ENOMEM."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: lpfc: Comprobación de kzalloc() en lpfc_sli4_cgn_params_read(). Si kzalloc() falla en lpfc_sli4_cgn_params_read(), dependemos de la rutina de lpfc_read_object() para comprobar si pdata es nulo. Actualmente, lpfc_read_object() genera un error de retorno anticipado para protegernos de la desreferencia de ptr nulo, pero el código de error es -ENODEV. Cambie el código de error a -ENOMEM, que es más apropiado."}], "references": [{"url": "https://git.kernel.org/stable/c/312320b0e0ec21249a17645683fe5304d796aec1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4829a1e1171536978b240a1438789c2e4d5c9715", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/67b8343998b84418bc5b5206aa01fe9b461a80ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/908dd9a0853a88155a5a36018c7e2b32ccf20379", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4324", "published_date": "2025-05-06T06:15:35.563", "last_modified_date": "2025-06-17T20:16:31.177", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, was found in MRCMS 3.1.2. This affects an unknown part of the file /admin/link/edit.do of the component External Link Management Page. The manipulation leads to cross site scripting. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en MRCMS 3.1.2. Esta afecta a una parte desconocida del archivo /admin/link/edit.do del componente External Link Management Page. La manipulación provoca ataques de cross site scripting. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bdkuzma/vuln/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307425", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307425", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563543", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
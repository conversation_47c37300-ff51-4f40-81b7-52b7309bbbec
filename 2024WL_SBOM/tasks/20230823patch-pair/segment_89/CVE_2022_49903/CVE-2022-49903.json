{"cve_id": "CVE-2022-49903", "published_date": "2025-05-01T15:16:15.373", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipv6: fix WARNING in ip6_route_net_exit_late()\n\nDuring the initialization of ip6_route_net_init_late(), if file\nipv6_route or rt6_stats fails to be created, the initialization is\nsuccessful by default. Therefore, the ipv6_route or rt6_stats file\ndoesn't be found during the remove in ip6_route_net_exit_late(). It\nwill cause WRNING.\n\nThe following is the stack information:\nname 'rt6_stats'\nWARNING: CPU: 0 PID: 9 at fs/proc/generic.c:712 remove_proc_entry+0x389/0x460\nModules linked in:\nWorkqueue: netns cleanup_net\nRIP: 0010:remove_proc_entry+0x389/0x460\nPKRU: 55555554\nCall Trace:\n<TASK>\nops_exit_list+0xb0/0x170\ncleanup_net+0x4ea/0xb00\nprocess_one_work+0x9bf/0x1710\nworker_thread+0x665/0x1080\nkthread+0x2e4/0x3a0\nret_from_fork+0x1f/0x30\n</TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipv6: corrección de una advertencia en ip6_route_net_exit_late(). Durante la inicialización de ip6_route_net_init_late(), si no se crea el archivo ipv6_route o rt6_stats, la inicialización se realiza correctamente por defecto. Por lo tanto, no se encuentra el archivo ipv6_route o rt6_stats durante la eliminación en ip6_route_net_exit_late(). Esto provocará una advertencia. La siguiente es la información de la pila: nombre 'rt6_stats' ADVERTENCIA: CPU: 0 PID: 9 en fs/proc/generic.c:712 remove_proc_entry+0x389/0x460 Módulos vinculados: Cola de trabajo: netns cleanup_net RIP: 0010:remove_proc_entry+0x389/0x460 PKRU: 55555554 Seguimiento de llamadas:  ops_exit_list+0xb0/0x170 cleanup_net+0x4ea/0xb00 process_one_work+0x9bf/0x1710 workers_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30 "}], "references": [{"url": "https://git.kernel.org/stable/c/080589287127838046077904f34d5054ea0f895c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0ed71af4d017d2bd2cbb8f7254f613a4914def26", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/381453770f731f0f43616a1cd4c759b7807a1517", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5dbb47ee89762da433cd8458788d7640c85f1a07", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/768b3c745fe5789f2430bdab02f35a9ad1148d97", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/83fbf246ced54dadd7b9adc2a16efeff30ba944d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
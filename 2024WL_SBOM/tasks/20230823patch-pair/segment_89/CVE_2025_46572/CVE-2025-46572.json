{"cve_id": "CVE-2025-46572", "published_date": "2025-05-06T21:16:20.400", "last_modified_date": "2025-05-07T17:15:58.323", "descriptions": [{"lang": "en", "value": "passport-wsfed-saml2 provides passport strategy for both WS-fed and SAML2 protocol. A vulnerability present starting in version 3.0.5 up to and including version 4.6.3 allows an attacker to impersonate any user during SAML authentication by crafting a SAMLResponse. This can be done by using a valid SAML object that was signed by the configured IdP. Users are affected specifically when the service provider is using passport-wsfed-saml2 and a valid SAML document signed by the Identity Provider can be obtained. Version 4.6.4 contains a fix for the vulnerability."}, {"lang": "es", "value": "passport-wsfed-saml2 proporciona una estrategia de pasaporte para los protocolos WS-fed y SAML2. Una vulnerabilidad, presente desde la versión 3.0.5 hasta la 4.6.3 incluida, permite a un atacante suplantar la identidad de cualquier usuario del inquilino Auth0 durante la autenticación SAML mediante la manipulación de una respuesta SAML. Esto se puede lograr utilizando un objeto SAML válido firmado por el IdP configurado. Los usuarios se ven afectados específicamente cuando el proveedor de servicios utiliza passport-wsfed-saml2 y se puede obtener un documento SAML válido firmado por el proveedor de identidad. La versión 4.6.4 contiene una corrección para esta vulnerabilidad."}], "references": [{"url": "https://github.com/auth0/passport-wsfed-saml2/commit/e5cf3cc2a53748207f7a81bfba9195c8efa94181", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/auth0/passport-wsfed-saml2/security/advisories/GHSA-wjmp-wphq-jvqf", "source": "<EMAIL>", "tags": []}]}
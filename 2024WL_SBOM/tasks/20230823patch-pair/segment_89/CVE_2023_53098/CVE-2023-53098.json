{"cve_id": "CVE-2023-53098", "published_date": "2025-05-02T16:15:28.733", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: rc: gpio-ir-recv: add remove function\n\nIn case runtime PM is enabled, do runtime PM clean up to remove\ncpu latency qos request, otherwise driver removal may have below\nkernel dump:\n\n[   19.463299] Unable to handle kernel NULL pointer dereference at\nvirtual address 0000000000000048\n[   19.472161] Mem abort info:\n[   19.474985]   ESR = 0x0000000096000004\n[   19.478754]   EC = 0x25: DABT (current EL), IL = 32 bits\n[   19.484081]   SET = 0, FnV = 0\n[   19.487149]   EA = 0, S1PTW = 0\n[   19.490361]   FSC = 0x04: level 0 translation fault\n[   19.495256] Data abort info:\n[   19.498149]   ISV = 0, ISS = 0x00000004\n[   19.501997]   CM = 0, WnR = 0\n[   19.504977] user pgtable: 4k pages, 48-bit VAs, pgdp=0000000049f81000\n[   19.511432] [0000000000000048] pgd=0000000000000000,\np4d=0000000000000000\n[   19.518245] Internal error: Oops: 0000000096000004 [#1] PREEMPT SMP\n[   19.524520] Modules linked in: gpio_ir_recv(+) rc_core [last\nunloaded: rc_core]\n[   19.531845] CPU: 0 PID: 445 Comm: insmod Not tainted\n6.2.0-rc1-00028-g2c397a46d47c #72\n[   19.531854] Hardware name: FSL i.MX8MM EVK board (DT)\n[   19.531859] pstate: 80000005 (Nzcv daif -PAN -UAO -TCO -DIT -SSBS\nBTYPE=--)\n[   19.551777] pc : cpu_latency_qos_remove_request+0x20/0x110\n[   19.557277] lr : gpio_ir_recv_runtime_suspend+0x18/0x30\n[gpio_ir_recv]\n[   19.557294] sp : ffff800008ce3740\n[   19.557297] x29: ffff800008ce3740 x28: 0000000000000000 x27:\nffff800008ce3d50\n[   19.574270] x26: ffffc7e3e9cea100 x25: 00000000000f4240 x24:\nffffc7e3f9ef0e30\n[   19.574284] x23: 0000000000000000 x22: ffff0061803820f4 x21:\n0000000000000008\n[   19.574296] x20: ffffc7e3fa75df30 x19: 0000000000000020 x18:\nffffffffffffffff\n[   19.588570] x17: 0000000000000000 x16: ffffc7e3f9efab70 x15:\nffffffffffffffff\n[   19.595712] x14: ffff800008ce37b8 x13: ffff800008ce37aa x12:\n0000000000000001\n[   19.602853] x11: 0000000000000001 x10: ffffcbe3ec0dff87 x9 :\n0000000000000008\n[   19.609991] x8 : 0101010101010101 x7 : 0000000000000000 x6 :\n000000000f0bfe9f\n[   19.624261] x5 : 00ffffffffffffff x4 : 0025ab8e00000000 x3 :\nffff006180382010\n[   19.631405] x2 : ffffc7e3e9ce8030 x1 : ffffc7e3fc3eb810 x0 :\n0000000000000020\n[   19.638548] Call trace:\n[   19.640995]  cpu_latency_qos_remove_request+0x20/0x110\n[   19.646142]  gpio_ir_recv_runtime_suspend+0x18/0x30 [gpio_ir_recv]\n[   19.652339]  pm_generic_runtime_suspend+0x2c/0x44\n[   19.657055]  __rpm_callback+0x48/0x1dc\n[   19.660807]  rpm_callback+0x6c/0x80\n[   19.664301]  rpm_suspend+0x10c/0x640\n[   19.667880]  rpm_idle+0x250/0x2d0\n[   19.671198]  update_autosuspend+0x38/0xe0\n[   19.675213]  pm_runtime_set_autosuspend_delay+0x40/0x60\n[   19.680442]  gpio_ir_recv_probe+0x1b4/0x21c [gpio_ir_recv]\n[   19.685941]  platform_probe+0x68/0xc0\n[   19.689610]  really_probe+0xc0/0x3dc\n[   19.693189]  __driver_probe_device+0x7c/0x190\n[   19.697550]  driver_probe_device+0x3c/0x110\n[   19.701739]  __driver_attach+0xf4/0x200\n[   19.705578]  bus_for_each_dev+0x70/0xd0\n[   19.709417]  driver_attach+0x24/0x30\n[   19.712998]  bus_add_driver+0x17c/0x240\n[   19.716834]  driver_register+0x78/0x130\n[   19.720676]  __platform_driver_register+0x28/0x34\n[   19.725386]  gpio_ir_recv_driver_init+0x20/0x1000 [gpio_ir_recv]\n[   19.731404]  do_one_initcall+0x44/0x2ac\n[   19.735243]  do_init_module+0x48/0x1d0\n[   19.739003]  load_module+0x19fc/0x2034\n[   19.742759]  __do_sys_finit_module+0xac/0x12c\n[   19.747124]  __arm64_sys_finit_module+0x20/0x30\n[   19.751664]  invoke_syscall+0x48/0x114\n[   19.755420]  el0_svc_common.constprop.0+0xcc/0xec\n[   19.760132]  do_el0_svc+0x38/0xb0\n[   19.763456]  el0_svc+0x2c/0x84\n[   19.766516]  el0t_64_sync_handler+0xf4/0x120\n[   19.770789]  el0t_64_sync+0x190/0x194\n[   19.774460] Code: 910003fd a90153f3 aa0003f3 91204021 (f9401400)\n[   19.780556] ---[ end trace 0000000000000000 ]---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: rc: gpio-ir-recv: agregar función de eliminación En caso de que PM en tiempo de ejecución esté habilitado, realice una limpieza de PM en tiempo de ejecución para eliminar la solicitud de calidad de servicio de latencia de la CPU; de lo contrario, la eliminación del controlador puede tener el siguiente volcado de kernel: [19.463299] No se puede manejar la desreferencia del puntero NULL del kernel en la dirección virtual 0000000000000048 [19.472161] Información de aborto de memoria: [19.474985] ESR = 0x0000000096000004 [19.478754] EC = 0x25: DABT (EL actual), IL = 32 bits [19.484081] SET = 0, FnV = 0 [19.487149] EA = 0, S1PTW = 0 [ [19.490361] FSC = 0x04: error de traducción de nivel 0 [19.495256] Información de cancelación de datos: [19.498149] ISV = 0, ISS = 0x00000004 [19.501997] CM = 0, WnR = 0 [19.504977] usuario pgtable: páginas de 4k, VA de 48 bits, pgdp=0000000049f81000 [ 19.511432] [0000000000000048] pgd=0000000000000000, p4d=0000000000000000 [ 19.518245] Internal error: Oops: 0000000096000004 [#1] PREEMPT SMP [ 19.524520] Modules linked in: gpio_ir_recv(+) rc_core [last unloaded: rc_core] [ 19.531845] CPU: 0 PID: 445 Comm: insmod Not tainted 6.2.0-rc1-00028-g2c397a46d47c #72 [ 19.531854] Hardware name: FSL i.MX8MM EVK board (DT) [ 19.531859] pstate: 80000005 (Nzcv daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--) [ 19.551777] pc : cpu_latency_qos_remove_request+0x20/0x110 [ 19.557277] lr : gpio_ir_recv_runtime_suspend+0x18/0x30 [gpio_ir_recv] [ 19.557294] sp : ffff800008ce3740 [ 19.557297] x29: ffff800008ce3740 x28: 0000000000000000 x27: ffff800008ce3d50 [ 19.574270] x26: ffffc7e3e9cea100 x25: 00000000000f4240 x24: ffffc7e3f9ef0e30 [ 19.574284] x23: 0000000000000000 x22: ffff0061803820f4 x21: 0000000000000008 [ 19.574296] x20: ffffc7e3fa75df30 x19: 0000000000000020 x18: ffffffffffffffff [ 19.588570] x17: 0000000000000000 x16: ffffc7e3f9efab70 x15: ffffffffffffffff [ 19.595712] x14: ffff800008ce37b8 x13: ffff800008ce37aa x12: 0000000000000001 [ 19.602853] x11: 0000000000000001 x10: ffffcbe3ec0dff87 x9 : 0000000000000008 [ 19.609991] x8 : 0101010101010101 x7 : 0000000000000000 x6 : 000000000f0bfe9f [ 19.624261] x5 : 00ffffffffffffff x4 : 0025ab8e00000000 x3 : ffff006180382010 [ 19.631405] x2 : ffffc7e3e9ce8030 x1 : ffffc7e3fc3eb810 x0 : 0000000000000020 [ 19.638548] Call trace: [ 19.640995] cpu_latency_qos_remove_request+0x20/0x110 [ 19.646142] gpio_ir_recv_runtime_suspend+0x18/0x30 [gpio_ir_recv] [ 19.652339] pm_generic_runtime_suspend+0x2c/0x44 [ 19.657055] __rpm_callback+0x48/0x1dc [ 19.660807] rpm_callback+0x6c/0x80 [ 19.664301] rpm_suspend+0x10c/0x640 [ 19.667880] rpm_idle+0x250/0x2d0 [ 19.671198] update_autosuspend+0x38/0xe0 [ 19.675213] pm_runtime_set_autosuspend_delay+0x40/0x60 [ 19.680442] gpio_ir_recv_probe+0x1b4/0x21c [gpio_ir_recv] [ 19.685941] platform_probe+0x68/0xc0 [ 19.689610] really_probe+0xc0/0x3dc [ 19.693189] __driver_probe_device+0x7c/0x190 [ 19.697550] driver_probe_device+0x3c/0x110 [ 19.701739] __driver_attach+0xf4/0x200 [ 19.705578] bus_for_each_dev+0x70/0xd0 [ 19.709417] driver_attach+0x24/0x30 [ 19.712998] bus_add_driver+0x17c/0x240 [ 19.716834] driver_register+0x78/0x130 [ 19.720676] __platform_driver_register+0x28/0x34 [ 19.725386] gpio_ir_recv_driver_init+0x20/0x1000 [gpio_ir_recv] [ 19.731404] do_one_initcall+0x44/0x2ac [ 19.735243] do_init_module+0x48/0x1d0 [ 19.739003] load_module+0x19fc/0x2034 [ 19.742759] __do_sys_finit_module+0xac/0x12c [ 19.747124] __arm64_sys_finit_module+0x20/0x30 [ 19.751664] invoke_syscall+0x48/0x114 [ 19.755420] el0_svc_common.constprop.0+0xcc/0xec [ 19.760132] do_el0_svc+0x38/0xb0 [ 19.763456] el0_svc+0x2c/0x84 [ 19.766516] el0t_64_sync_handler+0xf4/0x120 [ 19.770789] el0t_64_sync+0x190/0x194 [ 19.774460] Code: 910003fd a90153f3 aa0003f3 91204021 (f9401400) [ 19.780556] ---[ fin de seguimiento 0000000000000000 ]---"}], "references": [{"url": "https://git.kernel.org/stable/c/00e81f191bc00cb6faabf468960e96ebf0404a6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2ece4d2f7eac1cb51dc0e9859e09bfdb00faa28e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/30040818b338b8ebc956ce0ebd198f8d593586a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/513572bb89e8075f5d2a2bb4c89f1152e44da9d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a5c140d88a69eb43de2a030f1d7ff7b16bff3b1a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
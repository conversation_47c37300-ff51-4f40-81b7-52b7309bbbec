{"cve_id": "CVE-2025-45236", "published_date": "2025-05-05T18:15:43.163", "last_modified_date": "2025-06-16T20:36:27.817", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in the Edit Profile feature of DBSyncer v2.0.6 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the Nickname parameter."}, {"lang": "es", "value": "Una vulnerabilidad de cross-site scripting (XSS) almacenado en la función Editar perfil de DBSyncer v2.0.6 permite a los atacantes ejecutar scripts web o HTML arbitrarios mediante la inyección de un payload manipulado en el parámetro Nickname."}], "references": [{"url": "http://dbsyncer.com", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://gist.github.com/chao112122/504e224e63c9a966ba233df1d523ce4f", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://github.com/86dbs/dbsyncer", "source": "<EMAIL>", "tags": ["Product"]}]}
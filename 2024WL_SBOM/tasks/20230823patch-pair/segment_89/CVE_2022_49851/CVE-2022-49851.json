{"cve_id": "CVE-2022-49851", "published_date": "2025-05-01T15:16:08.680", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv: fix reserved memory setup\n\nCurrently, RISC-V sets up reserved memory using the \"early\" copy of the\ndevice tree. As a result, when trying to get a reserved memory region\nusing of_reserved_mem_lookup(), the pointer to reserved memory regions\nis using the early, pre-virtual-memory address which causes a kernel\npanic when trying to use the buffer's name:\n\n Unable to handle kernel paging request at virtual address 00000000401c31ac\n Oops [#1]\n Modules linked in:\n CPU: 0 PID: 0 Comm: swapper Not tainted 6.0.0-rc1-00001-g0d9d6953d834 #1\n Hardware name: Microchip PolarFire-SoC Icicle Kit (DT)\n epc : string+0x4a/0xea\n  ra : vsnprintf+0x1e4/0x336\n epc : ffffffff80335ea0 ra : ffffffff80338936 sp : ffffffff81203be0\n  gp : ffffffff812e0a98 tp : ffffffff8120de40 t0 : 0000000000000000\n  t1 : ffffffff81203e28 t2 : 7265736572203a46 s0 : ffffffff81203c20\n  s1 : ffffffff81203e28 a0 : ffffffff81203d22 a1 : 0000000000000000\n  a2 : ffffffff81203d08 a3 : 0000000081203d21 a4 : ffffffffffffffff\n  a5 : 00000000401c31ac a6 : ffff0a00ffffff04 a7 : ffffffffffffffff\n  s2 : ffffffff81203d08 s3 : ffffffff81203d00 s4 : 0000000000000008\n  s5 : ffffffff000000ff s6 : 0000000000ffffff s7 : 00000000ffffff00\n  s8 : ffffffff80d9821a s9 : ffffffff81203d22 s10: 0000000000000002\n  s11: ffffffff80d9821c t3 : ffffffff812f3617 t4 : ffffffff812f3617\n  t5 : ffffffff812f3618 t6 : ffffffff81203d08\n status: 0000000200000100 badaddr: 00000000401c31ac cause: 000000000000000d\n [<ffffffff80338936>] vsnprintf+0x1e4/0x336\n [<ffffffff80055ae2>] vprintk_store+0xf6/0x344\n [<ffffffff80055d86>] vprintk_emit+0x56/0x192\n [<ffffffff80055ed8>] vprintk_default+0x16/0x1e\n [<ffffffff800563d2>] vprintk+0x72/0x80\n [<ffffffff806813b2>] _printk+0x36/0x50\n [<ffffffff8068af48>] print_reserved_mem+0x1c/0x24\n [<ffffffff808057ec>] paging_init+0x528/0x5bc\n [<ffffffff808031ae>] setup_arch+0xd0/0x592\n [<ffffffff8080070e>] start_kernel+0x82/0x73c\n\nearly_init_fdt_scan_reserved_mem() takes no arguments as it operates on\ninitial_boot_params, which is populated by early_init_dt_verify(). On\nRISC-V, early_init_dt_verify() is called twice. Once, directly, in\nsetup_arch() if CONFIG_BUILTIN_DTB is not enabled and once indirectly,\nvery early in the boot process, by parse_dtb() when it calls\nearly_init_dt_scan_nodes().\n\nThis first call uses dtb_early_va to set initial_boot_params, which is\nnot usable later in the boot process when\nearly_init_fdt_scan_reserved_mem() is called. On arm64 for example, the\ncorresponding call to early_init_dt_scan_nodes() uses fixmap addresses\nand doesn't suffer the same fate.\n\nMove early_init_fdt_scan_reserved_mem() further along the boot sequence,\nafter the direct call to early_init_dt_verify() in setup_arch() so that\nthe names use the correct virtual memory addresses. The above supposed\nthat CONFIG_BUILTIN_DTB was not set, but should work equally in the case\nwhere it is - unflatted_and_copy_device_tree() also updates\ninitial_boot_params."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv: corrección de la configuración de memoria reservada Actualmente, RISC-V configura la memoria reservada utilizando la copia \"temprana\" del árbol de dispositivos. Como resultado, al intentar obtener una región de memoria reservada usando of_reserved_mem_lookup(), el puntero a las regiones de memoria reservadas usa la dirección anterior a la memoria virtual, lo que causa un pánico del kernel al intentar usar el nombre del búfer: Unable to handle kernel paging request at virtual address 00000000401c31ac Oops [#1] Módulos vinculados: CPU: 0 PID: 0 Comm: swapper Not tainted 6.0.0-rc1-00001-g0d9d6953d834 #1 Nombre del hardware: Microchip PolarFire-SoC Icicle Kit (DT) epc : string+0x4a/0xea ra : vsnprintf+0x1e4/0x336 epc : ffffffff80335ea0 ra : ffffffff80338936 sp : ffffffff81203be0 gp: ffffffff812e0a98 tp: ffffffff8120de40 t0: 0000000000000000 t1: ffffffff81203e28 t2: 7265736572203a46 s0: ffffffff81203c20 s1: ffffffff81203e28 a0: ffffffff81203d22 a1: 0000000000000000 a2: ffffffff81203d08 a3: 0000000081203d21 a4: ffffffffffffffff a5: 00000000401c31ac a6 : ffff0a00ffffff04 a7 : ffffffffffffffff s2 : ffffffff81203d08 s3 : ffffffff81203d00 s4 : 0000000000000008 s5 : ffffffff000000ff s6 : 0000000000ffffff s7 : 00000000ffffff00 s8 : ffffffff80d9821a s9 : ffffffff81203d22 s10: 000000000000002 s11: ffffffff80d9821c t3 : ffffffff812f3617 t4: ffffffff812f3617 t5: ffffffff812f3618 t6: ffffffff81203d08 estado: 0000000200000100 dirección incorrecta: 00000000401c31ac causa: 000000000000000d [] vsnprintf+0x1e4/0x336 [] vprintk_store+0xf6/0x344 [] vprintk_emit+0x56/0x192 [] vprintk_default+0x16/0x1e [] vprintk+0x72/0x80 [] _printk+0x36/0x50 [] print_reserved_mem+0x1c/0x24 [] paging_init+0x528/0x5bc [] setup_arch+0xd0/0x592 [] start_kernel+0x82/0x73c early_init_fdt_scan_reserved_mem() no toma argumentos ya que opera en initial_boot_params, que se completa con early_init_dt_verify(). En RISC-V, early_init_dt_verify() se llama dos veces: una directamente, en setup_arch() si CONFIG_BUILTIN_DTB no está habilitado, y otra indirectamente, en una fase muy temprana del proceso de arranque, mediante parse_dtb() al llamar a early_init_dt_scan_nodes(). Esta primera llamada utiliza dtb_early_va para establecer initial_boot_params, que no se puede utilizar posteriormente en el proceso de arranque cuando se llama a early_init_fdt_scan_reserved_mem(). En arm64, por ejemplo, la llamada correspondiente a early_init_dt_scan_nodes() utiliza direcciones fixmap y no sufre el mismo problema. Desplace early_init_fdt_scan_reserved_mem() más adelante en la secuencia de arranque, después de la llamada directa a early_init_dt_verify() en setup_arch() para que los nombres utilicen las direcciones de memoria virtual correctas. Lo anterior supuso que CONFIG_BUILTIN_DTB no estaba configurado, pero debería funcionar igualmente en el caso en que lo esté: unflatted_and_copy_device_tree() también actualiza initial_boot_params."}], "references": [{"url": "https://git.kernel.org/stable/c/50e63dd8ed92045eb70a72d7ec725488320fb68b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/518e49f0590de66555503aabe199ba8d3f2e24ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/93598deb101540c4f9e7de15099ea8255b965fc2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/94ab8f88feb75e3b1486102c0c9c550f37d9d137", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
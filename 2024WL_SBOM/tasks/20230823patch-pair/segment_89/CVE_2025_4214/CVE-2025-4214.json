{"cve_id": "CVE-2025-4214", "published_date": "2025-05-02T20:15:20.003", "last_modified_date": "2025-05-28T21:09:07.673", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGuruku Online DJ Booking Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /admin/booking-bwdates-reports-details.php. The manipulation of the argument fromdate leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGuruku Online DJ Booking Management System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/booking-bwdates-reports-details.php. La manipulación del argumento \"fromdate\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/LoovvvE18/CVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307193", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307193", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562295", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
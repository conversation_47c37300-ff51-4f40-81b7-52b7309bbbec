{"cve_id": "CVE-2025-1305", "published_date": "2025-05-01T04:16:47.947", "last_modified_date": "2025-05-06T15:38:55.647", "descriptions": [{"lang": "en", "value": "The NewsBlogger theme for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, *******. This is due to missing or incorrect nonce validation on the newsblogger_install_and_activate_plugin() function. This makes it possible for unauthenticated attackers to upload arbitrary files and achieve remote code execution via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El tema NewsBlogger para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la ******* incluida. Esto se debe a la falta o a una validación incorrecta de nonce en la función newsblogger_install_and_activate_plugin(). Esto permite que atacantes no autenticados carguen archivos arbitrarios y ejecuten código remoto mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://themes.trac.wordpress.org/browser/newsblogger/0.2/functions.php#L440", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://themes.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=269615%40newsblogger&new=269615%40newsblogger&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/7b2cac27-4a36-490f-b2d8-3c6f32843a38?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-53036", "published_date": "2025-05-02T16:15:22.733", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amdgpu: Fix call trace warning and hang when removing amdgpu device\n\nOn GPUs with RAS enabled, below call trace and hang are observed when\nshutting down device.\n\nv2: use DRM device unplugged flag instead of shutdown flag as the check to\nprevent memory wipe in shutdown stage.\n\n[ +0.000000] RIP: 0010:amdgpu_vram_mgr_fini+0x18d/0x1c0 [amdgpu]\n[ +0.000001] PKRU: 55555554\n[ +0.000001] Call Trace:\n[ +0.000001] <TASK>\n[ +0.000002] amdgpu_ttm_fini+0x140/0x1c0 [amdgpu]\n[ +0.000183] amdgpu_bo_fini+0x27/0xa0 [amdgpu]\n[ +0.000184] gmc_v11_0_sw_fini+0x2b/0x40 [amdgpu]\n[ +0.000163] amdgpu_device_fini_sw+0xb6/0x510 [amdgpu]\n[ +0.000152] amdgpu_driver_release_kms+0x16/0x30 [amdgpu]\n[ +0.000090] drm_dev_release+0x28/0x50 [drm]\n[ +0.000016] devm_drm_dev_init_release+0x38/0x60 [drm]\n[ +0.000011] devm_action_release+0x15/0x20\n[ +0.000003] release_nodes+0x40/0xc0\n[ +0.000001] devres_release_all+0x9e/0xe0\n[ +0.000001] device_unbind_cleanup+0x12/0x80\n[ +0.000003] device_release_driver_internal+0xff/0x160\n[ +0.000001] driver_detach+0x4a/0x90\n[ +0.000001] bus_remove_driver+0x6c/0xf0\n[ +0.000001] driver_unregister+0x31/0x50\n[ +0.000001] pci_unregister_driver+0x40/0x90\n[ +0.000003] amdgpu_exit+0x15/0x120 [amdgpu]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amdgpu: Se corrige la advertencia de seguimiento de llamadas y el bloqueo al quitar el dispositivo amdgpu. En las GPU con RAS habilitado, se observan el siguiente seguimiento de llamadas y bloqueo al apagar el dispositivo. v2: use el indicador de dispositivo DRM desconectado en lugar del indicador de apagado como verificación para evitar el borrado de memoria en la etapa de apagado. [ +0.000000] RIP: 0010:amdgpu_vram_mgr_fini+0x18d/0x1c0 [amdgpu] [ +0.000001] PKRU: 55555554 [ +0.000001] Rastreo de llamadas: [ +0.000001]  [ +0.000002] amdgpu_ttm_fini+0x140/0x1c0 [amdgpu] [ +0.000183] amdgpu_bo_fini+0x27/0xa0 [amdgpu] [ +0.000184] gmc_v11_0_sw_fini+0x2b/0x40 [amdgpu] [ +0.000163] amdgpu_device_fini_sw+0xb6/0x510 [amdgpu] [ +0.000152] amdgpu_driver_release_kms+0x16/0x30 [amdgpu] [ +0.000090] drm_dev_release+0x28/0x50 [drm] [ +0.000016] devm_drm_dev_init_release+0x38/0x60 [drm] [ +0.000011] devm_action_release+0x15/0x20 [ +0.000003] release_nodes+0x40/0xc0 [ +0.000001] devres_release_all+0x9e/0xe0 [ +0.000001] device_unbind_cleanup+0x12/0x80 [ +0.000003] device_release_driver_internal+0xff/0x160 [ +0.000001] driver_detach+0x4a/0x90 [ +0.000001] bus_remove_driver+0x6c/0xf0 [ +0.000001] driver_unregister+0x31/0x50 [ +0.000001] pci_unregister_driver+0x40/0x90 [ +0.000003] amdgpu_exit+0x15/0x120 [amdgpu] "}], "references": [{"url": "https://git.kernel.org/stable/c/93bb18d2a873d2fa9625c8ea927723660a868b95", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9a02dae3bbfe2df8e1c81e61a08695709e9588f9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f06b902511ea05526f405ee64da54a8313d91831", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
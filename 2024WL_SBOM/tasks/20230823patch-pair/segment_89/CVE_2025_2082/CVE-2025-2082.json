{"cve_id": "CVE-2025-2082", "published_date": "2025-04-30T20:15:21.563", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Tesla Model 3 VCSEC Integer Overflow Remote Code Execution Vulnerability. This vulnerability allows network-adjacent attackers to execute arbitrary code on affected Tesla Model 3 vehicles. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the VCSEC module. By manipulating the certificate response sent from the Tire Pressure Monitoring System (TPMS), an attacker can trigger an integer overflow before writing to memory. An attacker can leverage this vulnerability to execute code in the context of the VCSEC module and send arbitrary messages to the vehicle CAN bus. Was ZDI-CAN-23800."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por desbordamiento de enteros en el VCSEC del Tesla Model 3. Esta vulnerabilidad permite a atacantes adyacentes a la red ejecutar código arbitrario en los vehículos Tesla Model 3 afectados. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en el módulo VCSEC. Al manipular la respuesta del certificado enviada desde el Tire Pressure Monitoring System (TPMS), un atacante puede provocar un desbordamiento de enteros antes de escribir en memoria. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del módulo VCSEC y enviar mensajes arbitrarios al bus CAN del vehículo. Anteriormente, se denominaba ZDI-CAN-23800."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-265/", "source": "<EMAIL>", "tags": []}]}
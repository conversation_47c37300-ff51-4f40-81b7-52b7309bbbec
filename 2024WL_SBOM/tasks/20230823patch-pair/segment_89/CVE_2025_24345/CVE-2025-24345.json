{"cve_id": "CVE-2025-24345", "published_date": "2025-04-30T12:15:18.310", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "A vulnerability in the “Hosts” functionality of the web application of ctrlX OS allows a remote authenticated (low-privileged) attacker to manipulate the “hosts” file in an unintended manner via a crafted HTTP request."}, {"lang": "es", "value": "Una vulnerabilidad en la funcionalidad “Hosts” de la aplicación web de ctrlX OS permite a un atacante remoto autenticado (con privilegios bajos) manipular el archivo “hosts” de manera no intencionada a través de una solicitud HTTP manipulada."}], "references": [{"url": "https://psirt.bosch.com/security-advisories/BOSCH-SA-640452.html", "source": "<EMAIL>", "tags": []}]}
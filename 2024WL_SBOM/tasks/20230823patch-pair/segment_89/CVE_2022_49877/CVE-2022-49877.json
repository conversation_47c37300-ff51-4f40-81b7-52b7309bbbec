{"cve_id": "CVE-2022-49877", "published_date": "2025-05-01T15:16:12.653", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf, sockmap: Fix the sk->sk_forward_alloc warning of sk_stream_kill_queues\n\nWhen running `test_sockmap` selftests, the following warning appears:\n\n  WARNING: CPU: 2 PID: 197 at net/core/stream.c:205 sk_stream_kill_queues+0xd3/0xf0\n  Call Trace:\n  <TASK>\n  inet_csk_destroy_sock+0x55/0x110\n  tcp_rcv_state_process+0xd28/0x1380\n  ? tcp_v4_do_rcv+0x77/0x2c0\n  tcp_v4_do_rcv+0x77/0x2c0\n  __release_sock+0x106/0x130\n  __tcp_close+0x1a7/0x4e0\n  tcp_close+0x20/0x70\n  inet_release+0x3c/0x80\n  __sock_release+0x3a/0xb0\n  sock_close+0x14/0x20\n  __fput+0xa3/0x260\n  task_work_run+0x59/0xb0\n  exit_to_user_mode_prepare+0x1b3/0x1c0\n  syscall_exit_to_user_mode+0x19/0x50\n  do_syscall_64+0x48/0x90\n  entry_SYSCALL_64_after_hwframe+0x44/0xae\n\nThe root case is in commit 84472b436e76 (\"bpf, sockmap: Fix more uncharged\nwhile msg has more_data\"), where I used msg->sg.size to replace the tosend,\ncausing breakage:\n\n  if (msg->apply_bytes && msg->apply_bytes < tosend)\n    tosend = psock->apply_bytes;"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf, sockmap: corrige la advertencia sk-&gt;sk_forward_alloc de sk_stream_kill_queues Al ejecutar las pruebas automáticas `test_sockmap`, aparece la siguiente advertencia: ADVERTENCIA: CPU: 2 PID: 197 en net/core/stream.c:205 sk_stream_kill_queues+0xd3/0xf0 Seguimiento de llamadas:   inet_csk_destroy_sock+0x55/0x110 tcp_rcv_state_process+0xd28/0x1380 ? tcp_v4_do_rcv+0x77/0x2c0 tcp_v4_do_rcv+0x77/0x2c0 __release_sock+0x106/0x130 __tcp_close+0x1a7/0x4e0 tcp_close+0x20/0x70 inet_release+0x3c/0x80 __sock_release+0x3a/0xb0 sock_close+0x14/0x20 __fput+0xa3/0x260 task_work_run+0x59/0xb0 exit_to_user_mode_prepare+0x1b3/0x1c0 syscall_exit_to_user_mode+0x19/0x50 do_syscall_64+0x48/0x90 entry_SYSCALL_64_after_hwframe+0x44/0xae El caso raíz está en el commit 84472b436e76 (\"bpf, sockmap: Arreglar más archivos no cargados mientras msg tiene more_data\"), donde utilicé msg-&gt;sg.size para reemplazar tosend, lo que causó fallas: if (msg-&gt;apply_bytes &amp;&amp; msg-&gt;apply_bytes &lt; tosend) tosend = psock-&gt;apply_bytes;"}], "references": [{"url": "https://git.kernel.org/stable/c/14e8bc3bf7bd6af64d7538a0684c8238d96cdfd7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ec95b94716a1e4d126edc3fb2bc426a717e2dba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/95adbd2ac8de82e43fd6b347e7e1b47f74dc1abb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cc21dc48a78cc9e5af9a4d039cd456446a6e73ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d975bec1eaeb52341acc9273db79ddb078220399", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
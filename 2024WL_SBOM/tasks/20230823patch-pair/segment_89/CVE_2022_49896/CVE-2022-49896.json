{"cve_id": "CVE-2022-49896", "published_date": "2025-05-01T15:16:14.643", "last_modified_date": "2025-05-07T13:19:15.073", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncxl/pmem: Fix cxl_pmem_region and cxl_memdev leak\n\nWhen a cxl_nvdimm object goes through a ->remove() event (device\nphysically removed, nvdimm-bridge disabled, or nvdimm device disabled),\nthen any associated regions must also be disabled. As highlighted by the\ncxl-create-region.sh test [1], a single device may host multiple\nregions, but the driver was only tracking one region at a time. This\nleads to a situation where only the last enabled region per nvdimm\ndevice is cleaned up properly. Other regions are leaked, and this also\ncauses cxl_memdev reference leaks.\n\nFix the tracking by allowing cxl_nvdimm objects to track multiple region\nassociations."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cxl/pmem: Arregla la fuga de cxl_pmem_region y cxl_memdev Cuando un objeto cxl_nvdimm pasa por un evento -&gt;remove() (dispositivo eliminado físicamente, nvdimm-bridge deshabilitado o dispositivo nvdimm deshabilitado), entonces cualquier región asociada también debe deshabilitarse. Como se destaca en la prueba cxl-create-region.sh [1], un solo dispositivo puede albergar múltiples regiones, pero el controlador solo rastreaba una región a la vez. Esto lleva a una situación en la que solo la última región habilitada por dispositivo nvdimm se limpia correctamente. Se filtran otras regiones, y esto también causa fugas de referencia de cxl_memdev. Arregla el rastreo permitiendo que los objetos cxl_nvdimm rastreen múltiples asociaciones de regiones."}], "references": [{"url": "https://git.kernel.org/stable/c/4d07ae22e79ebc2d7528bbc69daa53b86981cb3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f43b6bfdbab78606735ba81185cf0602b81e40b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2022-49909", "published_date": "2025-05-01T15:16:16.030", "last_modified_date": "2025-05-07T13:30:13.010", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: L2CAP: fix use-after-free in l2cap_conn_del()\n\nWhen l2cap_recv_frame() is invoked to receive data, and the cid is\nL2CAP_CID_A2MP, if the channel does not exist, it will create a channel.\nHowever, after a channel is created, the hold operation of the channel\nis not performed. In this case, the value of channel reference counting\nis 1. As a result, after hci_error_reset() is triggered, l2cap_conn_del()\ninvokes the close hook function of A2MP to release the channel. Then\n l2cap_chan_unlock(chan) will trigger UAF issue.\n\nThe process is as follows:\nReceive data:\nl2cap_data_channel()\n    a2mp_channel_create()  --->channel ref is 2\n    l2cap_chan_put()       --->channel ref is 1\n\nTriger event:\n    hci_error_reset()\n        hci_dev_do_close()\n        ...\n        l2cap_disconn_cfm()\n            l2cap_conn_del()\n                l2cap_chan_hold()    --->channel ref is 2\n                l2cap_chan_del()     --->channel ref is 1\n                a2mp_chan_close_cb() --->channel ref is 0, release channel\n                l2cap_chan_unlock()  --->UAF of channel\n\nThe detailed Call Trace is as follows:\nBUG: KASAN: use-after-free in __mutex_unlock_slowpath+0xa6/0x5e0\nRead of size 8 at addr ffff8880160664b8 by task kworker/u11:1/7593\nWorkqueue: hci0 hci_error_reset\nCall Trace:\n <TASK>\n dump_stack_lvl+0xcd/0x134\n print_report.cold+0x2ba/0x719\n kasan_report+0xb1/0x1e0\n kasan_check_range+0x140/0x190\n __mutex_unlock_slowpath+0xa6/0x5e0\n l2cap_conn_del+0x404/0x7b0\n l2cap_disconn_cfm+0x8c/0xc0\n hci_conn_hash_flush+0x11f/0x260\n hci_dev_close_sync+0x5f5/0x11f0\n hci_dev_do_close+0x2d/0x70\n hci_error_reset+0x9e/0x140\n process_one_work+0x98a/0x1620\n worker_thread+0x665/0x1080\n kthread+0x2e4/0x3a0\n ret_from_fork+0x1f/0x30\n </TASK>\n\nAllocated by task 7593:\n kasan_save_stack+0x1e/0x40\n __kasan_kmalloc+0xa9/0xd0\n l2cap_chan_create+0x40/0x930\n amp_mgr_create+0x96/0x990\n a2mp_channel_create+0x7d/0x150\n l2cap_recv_frame+0x51b8/0x9a70\n l2cap_recv_acldata+0xaa3/0xc00\n hci_rx_work+0x702/0x1220\n process_one_work+0x98a/0x1620\n worker_thread+0x665/0x1080\n kthread+0x2e4/0x3a0\n ret_from_fork+0x1f/0x30\n\nFreed by task 7593:\n kasan_save_stack+0x1e/0x40\n kasan_set_track+0x21/0x30\n kasan_set_free_info+0x20/0x30\n ____kasan_slab_free+0x167/0x1c0\n slab_free_freelist_hook+0x89/0x1c0\n kfree+0xe2/0x580\n l2cap_chan_put+0x22a/0x2d0\n l2cap_conn_del+0x3fc/0x7b0\n l2cap_disconn_cfm+0x8c/0xc0\n hci_conn_hash_flush+0x11f/0x260\n hci_dev_close_sync+0x5f5/0x11f0\n hci_dev_do_close+0x2d/0x70\n hci_error_reset+0x9e/0x140\n process_one_work+0x98a/0x1620\n worker_thread+0x665/0x1080\n kthread+0x2e4/0x3a0\n ret_from_fork+0x1f/0x30\n\nLast potentially related work creation:\n kasan_save_stack+0x1e/0x40\n __kasan_record_aux_stack+0xbe/0xd0\n call_rcu+0x99/0x740\n netlink_release+0xe6a/0x1cf0\n __sock_release+0xcd/0x280\n sock_close+0x18/0x20\n __fput+0x27c/0xa90\n task_work_run+0xdd/0x1a0\n exit_to_user_mode_prepare+0x23c/0x250\n syscall_exit_to_user_mode+0x19/0x50\n do_syscall_64+0x42/0x80\n entry_SYSCALL_64_after_hwframe+0x63/0xcd\n\nSecond to last potentially related work creation:\n kasan_save_stack+0x1e/0x40\n __kasan_record_aux_stack+0xbe/0xd0\n call_rcu+0x99/0x740\n netlink_release+0xe6a/0x1cf0\n __sock_release+0xcd/0x280\n sock_close+0x18/0x20\n __fput+0x27c/0xa90\n task_work_run+0xdd/0x1a0\n exit_to_user_mode_prepare+0x23c/0x250\n syscall_exit_to_user_mode+0x19/0x50\n do_syscall_64+0x42/0x80\n entry_SYSCALL_64_after_hwframe+0x63/0xcd"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: L2CAP: corrección de use-after-free en l2cap_conn_del(). Cuando se invoca l2cap_recv_frame() para recibir datos y el CID es L2CAP_CID_A2MP, si el canal no existe, se crea uno. Sin embargo, una vez creado el canal, no se realiza la operación de retención. En este caso, el valor del recuento de referencias del canal es 1. Como resultado, tras la activación de hci_error_reset(), l2cap_conn_del() invoca la función de cierre de A2MP para liberar el canal. Entonces, l2cap_chan_unlock(chan) activará el problema de UAF. El proceso es el siguiente: Recibir datos: l2cap_data_channel() a2mp_channel_create() ---&gt;la referencia del canal es 2 l2cap_chan_put() ---&gt;la referencia del canal es 1 Evento desencadenador: hci_error_reset() hci_dev_do_close() ... l2cap_disconn_cfm() l2cap_conn_del() l2cap_chan_hold() ---&gt;la referencia del canal es 2 l2cap_chan_del() ---&gt;la referencia del canal es 1 a2mp_chan_close_cb() ---&gt;la referencia del canal es 0, liberar el canal l2cap_chan_unlock() ---&gt;UAF del canal El seguimiento de llamadas detallado es el siguiente: ERROR: KASAN: use-after-free en __mutex_unlock_slowpath+0xa6/0x5e0 Lectura de tamaño 8 en la dirección ffff8880160664b8 por la tarea kworker/u11:1/7593 Cola de trabajo: hci0 Rastreo de llamadas hci_error_reset:   dump_stack_lvl+0xcd/0x134 print_report.cold+0x2ba/0x719 kasan_report+0xb1/0x1e0 kasan_check_range+0x140/0x190 __mutex_unlock_slowpath+0xa6/0x5e0 l2cap_conn_del+0x404/0x7b0 l2cap_disconn_cfm+0x8c/0xc0 hci_conn_hash_flush+0x11f/0x260 hci_dev_close_sync+0x5f5/0x11f0 hci_dev_do_close+0x2d/0x70 hci_error_reset+0x9e/0x140 process_one_work+0x98a/0x1620 worker_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30  Allocated by task 7593: kasan_save_stack+0x1e/0x40 __kasan_kmalloc+0xa9/0xd0 l2cap_chan_create+0x40/0x930 amp_mgr_create+0x96/0x990 a2mp_channel_create+0x7d/0x150 l2cap_recv_frame+0x51b8/0x9a70 l2cap_recv_acldata+0xaa3/0xc00 hci_rx_work+0x702/0x1220 process_one_work+0x98a/0x1620 worker_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30 Freed by task 7593: kasan_save_stack+0x1e/0x40 kasan_set_track+0x21/0x30 kasan_set_free_info+0x20/0x30 ____kasan_slab_free+0x167/0x1c0 slab_free_freelist_hook+0x89/0x1c0 kfree+0xe2/0x580 l2cap_chan_put+0x22a/0x2d0 l2cap_conn_del+0x3fc/0x7b0 l2cap_disconn_cfm+0x8c/0xc0 hci_conn_hash_flush+0x11f/0x260 hci_dev_close_sync+0x5f5/0x11f0 hci_dev_do_close+0x2d/0x70 hci_error_reset+0x9e/0x140 process_one_work+0x98a/0x1620 worker_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30 Last potentially related work creation: kasan_save_stack+0x1e/0x40 __kasan_record_aux_stack+0xbe/0xd0 call_rcu+0x99/0x740 netlink_release+0xe6a/0x1cf0 __sock_release+0xcd/0x280 sock_close+0x18/0x20 __fput+0x27c/0xa90 task_work_run+0xdd/0x1a0 exit_to_user_mode_prepare+0x23c/0x250 syscall_exit_to_user_mode+0x19/0x50 do_syscall_64+0x42/0x80 entry_SYSCALL_64_after_hwframe+0x63/0xcd Second to last potentially related work creation: kasan_save_stack+0x1e/0x40 __kasan_record_aux_stack+0xbe/0xd0 call_rcu+0x99/0x740 netlink_release+0xe6a/0x1cf0 __sock_release+0xcd/0x280 sock_close+0x18/0x20 __fput+0x27c/0xa90 task_work_run+0xdd/0x1a0 exit_to_user_mode_prepare+0x23c/0x250 syscall_exit_to_user_mode+0x19/0x50 do_syscall_64+0x42/0x80 entry_SYSCALL_64_after_hwframe+0x63/0xcd "}], "references": [{"url": "https://git.kernel.org/stable/c/0d0e2d032811280b927650ff3c15fe5020e82533", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/17c6164854f8bb80bf76f32b2c2f199c16b53703", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7f7bfdd9a9af3b12c33d9da9a012e7f4d5c91f4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8f7e4cf0694149a5d999d676ebd9ecf1b4cb2cc9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a3a7b2ac64de232edb67279e804932cb42f0b52a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c1f594dddd9ffd747c39f49cc5b67a9b7677d2ab", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d9ec6e2fbd4a565b2345d4852f586b7ae3ab41fd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/db4a0783ed78beb2ebaa32f5f785bfd79c580689", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
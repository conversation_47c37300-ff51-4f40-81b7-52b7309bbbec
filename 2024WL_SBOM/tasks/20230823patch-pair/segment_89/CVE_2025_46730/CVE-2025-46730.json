{"cve_id": "CVE-2025-46730", "published_date": "2025-05-05T20:15:21.313", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "MobSF is a mobile application security testing tool used. Typically, MobSF is deployed on centralized internal or cloud-based servers that also host other security tools and web applications. Access to the MobSF web interface is often granted to internal security teams, audit teams, and external vendors.  MobSF provides a feature that allows users to upload ZIP files for static analysis. Upon upload, these ZIP files are automatically extracted and stored within the MobSF directory. However, in versions up to and including 4.3.2, this functionality lacks a check on the total uncompressed size of the ZIP file, making it vulnerable to a ZIP of Death (zip bomb) attack. Due to the absence of safeguards against oversized extractions, an attacker can craft a specially prepared ZIP file that is small in compressed form but expands to a massive size upon extraction. Exploiting this, an attacker can exhaust the server's disk space, leading to a complete denial of service (DoS) not just for MobSF, but also for any other applications or websites hosted on the same server. This vulnerability can lead to complete server disruption in an organization which can affect other internal portals and tools too (which are hosted on the same server). If some organization has created their customized cloud based mobile security tool using MobSF core then an attacker can exploit this vulnerability to crash their servers. Commit 6987a946485a795f4fd38cebdb4860b368a1995d fixes this issue. As an additional mitigation, it is recommended to implement a safeguard that checks the total uncompressed size of any uploaded ZIP file before extraction. If the estimated uncompressed size exceeds a safe threshold (e.g., 100 MB), MobSF should reject the file and notify the user."}, {"lang": "es", "value": "MobSF es una herramienta de pruebas de seguridad para aplicaciones móviles. Normalmente, MobSF se implementa en servidores centralizados internos o en la nube que también alojan otras herramientas de seguridad y aplicaciones web. El acceso a la interfaz web de MobSF suele concederse a equipos de seguridad internos, equipos de auditoría y proveedores externos. MobSF ofrece una función que permite a los usuarios cargar archivos ZIP para análisis estático. Tras la carga, estos archivos ZIP se extraen y almacenan automáticamente en el directorio de MobSF. Sin embargo, en versiones hasta la 4.3.2 incluida, esta función carece de una comprobación del tamaño total sin comprimir del archivo ZIP, lo que lo hace vulnerable a un ataque ZIP de la Muerte (bomba zip). Debido a la ausencia de protecciones contra extracciones de gran tamaño, un atacante puede crear un archivo ZIP especialmente preparado que, aunque pequeño en su forma comprimida, se expande enormemente al extraerlo. Aprovechando esto, un atacante puede agotar el espacio de disco del servidor, lo que provoca una denegación de servicio (DoS) completa no solo para MobSF, sino también para cualquier otra aplicación o sitio web alojado en el mismo servidor. Esta vulnerabilidad puede provocar la interrupción total del servidor de una organización, lo que puede afectar también a otros portales y herramientas internos (alojados en el mismo servidor). Si una organización ha creado su herramienta de seguridad móvil personalizada en la nube utilizando el núcleo de MobSF, un atacante puede explotar esta vulnerabilidad para bloquear sus servidores. El commit 6987a946485a795f4fd38cebdb4860b368a1995d corrige este problema. Como mitigación adicional, se recomienda implementar una protección que verifique el tamaño total sin comprimir de cualquier archivo ZIP subido antes de la extracción. Si el tamaño estimado sin comprimir supera un umbral seguro (p. ej., 100 MB), MobSF debería rechazar el archivo y notificar al usuario."}], "references": [{"url": "https://github.com/MobSF/Mobile-Security-Framework-MobSF/commit/6987a946485a795f4fd38cebdb4860b368a1995d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/MobSF/Mobile-Security-Framework-MobSF/security/advisories/GHSA-c5vg-26p8-q8cr", "source": "<EMAIL>", "tags": []}]}
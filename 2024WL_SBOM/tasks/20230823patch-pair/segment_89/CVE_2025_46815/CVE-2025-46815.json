{"cve_id": "CVE-2025-46815", "published_date": "2025-05-06T18:15:38.697", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "The identity infrastructure software ZITADEL offers developers the ability to manage user sessions using the Session API. This API enables the use of IdPs for authentication, known as idp intents. Following a successful idp intent, the client receives an id and token on a predefined URI. These id and token can then be used to authenticate the user or their session. However, prior to versions 3.0.0, 2.71.9, and 2.70.10, it was possible to exploit this feature by repeatedly using intents. This allowed an attacker with access to the application’s URI to retrieve the id and token, enabling them to authenticate on behalf of the user. It's important to note that the use of additional factors (MFA) prevents a complete authentication process and, consequently, access to the ZITADEL API. Versions 3.0.0, 2.71.9, and 2.70.10 contain a fix for the issue. No known workarounds other than upgrading are available."}, {"lang": "es", "value": "El software de infraestructura de identidad ZITADEL ofrece a los desarrolladores la posibilidad de gestionar sesiones de usuario mediante la API de sesión. Esta API permite el uso de proveedores de identidad (IdP) para la autenticación, conocidos como intentos de IdP. Tras un intento de IdP exitoso, el cliente recibe un ID y un token en una URI predefinida. Estos ID y token pueden utilizarse para autenticar al usuario o su sesión. Sin embargo, antes de las versiones 3.0.0, 2.71.9 y 2.70.10, era posible explotar esta función mediante el uso repetido de intentos. Esto permitía a un atacante con acceso a la URI de la aplicación recuperar el ID y el token, lo que le permitía autenticarse en nombre del usuario. Es importante tener en cuenta que el uso de factores adicionales (MFA) impide un proceso de autenticación completo y, en consecuencia, el acceso a la API de ZITADEL. Las versiones 3.0.0, 2.71.9 y 2.70.10 contienen una solución para este problema. No se conocen workarounds aparte de la actualización."}], "references": [{"url": "https://github.com/zitadel/zitadel/commit/b1e60e7398d677f08b06fd7715227f70b7ca1162", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/releases/tag/v2.70.10", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/releases/tag/v2.71.9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/releases/tag/v3.0.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/security/advisories/GHSA-g4r8-mp7g-85fq", "source": "<EMAIL>", "tags": []}]}
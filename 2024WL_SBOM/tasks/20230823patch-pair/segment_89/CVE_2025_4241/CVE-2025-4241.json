{"cve_id": "CVE-2025-4241", "published_date": "2025-05-03T18:15:16.087", "last_modified_date": "2025-05-09T13:40:25.717", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Teacher Subject Allocation Management System 1.0. Affected is an unknown function of the file /admin/search.php. The manipulation of the argument searchdata leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Teacher Subject Allocation Management System 1.0. Se ve afectada una función desconocida del archivo /admin/search.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bluechips-zhao/myCVE/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307332", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307332", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562452", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-4131", "published_date": "2025-05-02T03:15:21.257", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "The GmapsMania plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's gmap shortcode in all versions up to, and including, 1.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento GmapsMania para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del shortcode de Gmap en todas las versiones hasta la 1.1 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/gmapsmania/trunk/gmapsmania.php#L14", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/30d39718-945a-43a2-be08-70be1af55965?source=cve", "source": "<EMAIL>", "tags": []}]}
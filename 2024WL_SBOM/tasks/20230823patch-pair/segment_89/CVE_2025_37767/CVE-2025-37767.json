{"cve_id": "CVE-2025-37767", "published_date": "2025-05-01T14:15:39.723", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/pm: Prevent division by zero\n\nThe user can set any speed value.\nIf speed is greater than UINT_MAX/8, division by zero is possible.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/pm: Impide la división por cero. El usuario puede establecer cualquier valor de velocidad. Si la velocidad es superior a UINT_MAX/8, es posible la división por cero. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/327107bd7f052f4ee2d0c966c7ae879822f1814f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8f7b5987e21e003cafac28f0e4d323e6496f83ba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c3ff73e3bddf1a6c30d7effe4018d12ba0cadd2e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f23e9116ebb71b63fe9cec0dcac792aa9af30b0c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f2904fa2b9da943db6bef7c0f8b3fb4fc14acbc4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fb803d4bb9ea0a61c21c4987505e4d4ae18f9fdc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53108", "published_date": "2025-05-02T16:15:29.713", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet/iucv: Fix size of interrupt data\n\niucv_irq_data needs to be 4 bytes larger.\nThese bytes are not used by the iucv module, but written by\nthe z/VM hypervisor in case a CPU is deconfigured.\n\nReported as:\nBUG dma-kmalloc-64 (Not tainted): kmalloc Redzone overwritten\n-----------------------------------------------------------------------------\n0x0000000000400564-0x0000000000400567 @offset=1380. First byte 0x80 instead of 0xcc\nAllocated in iucv_cpu_prepare+0x44/0xd0 age=167839 cpu=2 pid=1\n__kmem_cache_alloc_node+0x166/0x450\nkmalloc_node_trace+0x3a/0x70\niucv_cpu_prepare+0x44/0xd0\ncpuhp_invoke_callback+0x156/0x2f0\ncpuhp_issue_call+0xf0/0x298\n__cpuhp_setup_state_cpuslocked+0x136/0x338\n__cpuhp_setup_state+0xf4/0x288\niucv_init+0xf4/0x280\ndo_one_initcall+0x78/0x390\ndo_initcalls+0x11a/0x140\nkernel_init_freeable+0x25e/0x2a0\nkernel_init+0x2e/0x170\n__ret_from_fork+0x3c/0x58\nret_from_fork+0xa/0x40\nFreed in iucv_init+0x92/0x280 age=167839 cpu=2 pid=1\n__kmem_cache_free+0x308/0x358\niucv_init+0x92/0x280\ndo_one_initcall+0x78/0x390\ndo_initcalls+0x11a/0x140\nkernel_init_freeable+0x25e/0x2a0\nkernel_init+0x2e/0x170\n__ret_from_fork+0x3c/0x58\nret_from_fork+0xa/0x40\nSlab 0x0000037200010000 objects=32 used=30 fp=0x0000000000400640 flags=0x1ffff00000010200(slab|head|node=0|zone=0|\nObject 0x0000000000400540 @offset=1344 fp=0x0000000000000000\nRedzone  0000000000400500: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc  ................\nRedzone  0000000000400510: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc  ................\nRedzone  0000000000400520: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc  ................\nRedzone  0000000000400530: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc  ................\nObject   0000000000400540: 00 01 00 03 00 00 00 00 00 00 00 00 00 00 00 00  ................\nObject   0000000000400550: f3 86 81 f2 f4 82 f8 82 f0 f0 f0 f0 f0 f0 f0 f2  ................\nObject   0000000000400560: 00 00 00 00 80 00 00 00 cc cc cc cc cc cc cc cc  ................\nObject   0000000000400570: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc  ................\nRedzone  0000000000400580: cc cc cc cc cc cc cc cc                          ........\nPadding  00000000004005d4: 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a  ZZZZZZZZZZZZZZZZ\nPadding  00000000004005e4: 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a  ZZZZZZZZZZZZZZZZ\nPadding  00000000004005f4: 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a              ZZZZZZZZZZZZ\nCPU: 6 PID: 121030 Comm: 116-pai-crypto. Not tainted 6.3.0-20230221.rc0.git4.99b8246b2d71.300.fc37.s390x+debug #1\nHardware name: IBM 3931 A01 704 (z/VM 7.3.0)\nCall Trace:\n[<000000032aa034ec>] dump_stack_lvl+0xac/0x100\n[<0000000329f5a6cc>] check_bytes_and_report+0x104/0x140\n[<0000000329f5aa78>] check_object+0x370/0x3c0\n[<0000000329f5ede6>] free_debug_processing+0x15e/0x348\n[<0000000329f5f06a>] free_to_partial_list+0x9a/0x2f0\n[<0000000329f5f4a4>] __slab_free+0x1e4/0x3a8\n[<0000000329f61768>] __kmem_cache_free+0x308/0x358\n[<000000032a91465c>] iucv_cpu_dead+0x6c/0x88\n[<0000000329c2fc66>] cpuhp_invoke_callback+0x156/0x2f0\n[<000000032aa062da>] _cpu_down.constprop.0+0x22a/0x5e0\n[<0000000329c3243e>] cpu_device_down+0x4e/0x78\n[<000000032a61dee0>] device_offline+0xc8/0x118\n[<000000032a61e048>] online_store+0x60/0xe0\n[<000000032a08b6b0>] kernfs_fop_write_iter+0x150/0x1e8\n[<0000000329fab65c>] vfs_write+0x174/0x360\n[<0000000329fab9fc>] ksys_write+0x74/0x100\n[<000000032aa03a5a>] __do_syscall+0x1da/0x208\n[<000000032aa177b2>] system_call+0x82/0xb0\nINFO: lockdep is turned off.\nFIX dma-kmalloc-64: Restoring kmalloc Redzone 0x0000000000400564-0x0000000000400567=0xcc\nFIX dma-kmalloc-64: Object at 0x0000000000400540 not freed"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net/iucv: Se corrige que el tamaño de los datos de interrupción iucv_irq_data deba ser 4 bytes mayor. Estos bytes no son utilizados por el módulo iucv, sino por el hipervisor z/VM en caso de desconfiguración de una CPU. Reportado como: BUG dma-kmalloc-64 (No contaminado): kmalloc Redzone sobrescrito ----------------------------------------------------------------------------- 0x0000000000400564-0x0000000000400567 @offset=1380. First byte 0x80 instead of 0xcc Allocated in iucv_cpu_prepare+0x44/0xd0 age=167839 cpu=2 pid=1 __kmem_cache_alloc_node+0x166/0x450 kmalloc_node_trace+0x3a/0x70 iucv_cpu_prepare+0x44/0xd0 cpuhp_invoke_callback+0x156/0x2f0 cpuhp_issue_call+0xf0/0x298 __cpuhp_setup_state_cpuslocked+0x136/0x338 __cpuhp_setup_state+0xf4/0x288 iucv_init+0xf4/0x280 do_one_initcall+0x78/0x390 do_initcalls+0x11a/0x140 kernel_init_freeable+0x25e/0x2a0 kernel_init+0x2e/0x170 __ret_from_fork+0x3c/0x58 ret_from_fork+0xa/0x40 Freed in iucv_init+0x92/0x280 age=167839 cpu=2 pid=1 __kmem_cache_free+0x308/0x358 iucv_init+0x92/0x280 do_one_initcall+0x78/0x390 do_initcalls+0x11a/0x140 kernel_init_freeable+0x25e/0x2a0 kernel_init+0x2e/0x170 __ret_from_fork+0x3c/0x58 ret_from_fork+0xa/0x40 Slab 0x0000037200010000 objects=32 used=30 fp=0x0000000000400640 flags=0x1ffff00000010200(slab|head|node=0|zone=0| Object 0x0000000000400540 @offset=1344 fp=0x0000000000000000 Redzone 0000000000400500: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc ................ Redzone 0000000000400510: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc ................ Redzone 0000000000400520: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc ................ Redzone 0000000000400530: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc ................ Object 0000000000400540: 00 01 00 03 00 00 00 00 00 00 00 00 00 00 00 00 ................ Object 0000000000400550: f3 86 81 f2 f4 82 f8 82 f0 f0 f0 f0 f0 f0 f0 f2 ................ Object 0000000000400560: 00 00 00 00 80 00 00 00 cc cc cc cc cc cc cc cc ................ Object 0000000000400570: cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc cc ................ Redzone 0000000000400580: cc cc cc cc cc cc cc cc ........ Padding 00000000004005d4: 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a ZZZZZZZZZZZZZZZZ Padding 00000000004005e4: 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a ZZZZZZZZZZZZZZZZ Padding 00000000004005f4: 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a 5a ZZZZZZZZZZZZ CPU: 6 PID: 121030 Comm: 116-pai-crypto. Not tainted 6.3.0-20230221.rc0.git4.99b8246b2d71.300.fc37.s390x+debug #1 Hardware name: IBM 3931 A01 704 (z/VM 7.3.0) Rastreo de llamadas: [&lt;000000032aa034ec&gt;] dump_stack_lvl+0xac/0x100 [&lt;0000000329f5a6cc&gt;] check_bytes_and_report+0x104/0x140 [&lt;0000000329f5aa78&gt;] check_object+0x370/0x3c0 [&lt;0000000329f5ede6&gt;] free_debug_processing+0x15e/0x348 [&lt;0000000329f5f06a&gt;] free_to_partial_list+0x9a/0x2f0 [&lt;0000000329f5f4a4&gt;] __slab_free+0x1e4/0x3a8 [&lt;0000000329f61768&gt;] __kmem_cache_free+0x308/0x358 [&lt;000000032a91465c&gt;] iucv_cpu_dead+0x6c/0x88 [&lt;0000000329c2fc66&gt;] cpuhp_invoke_callback+0x156/0x2f0 [&lt;000000032aa062da&gt;] _cpu_down.constprop.0+0x22a/0x5e0 [&lt;0000000329c3243e&gt;] cpu_device_down+0x4e/0x78 [&lt;000000032a61dee0&gt;] device_offline+0xc8/0x118 [&lt;000000032a61e048&gt;] online_store+0x60/0xe0 [&lt;000000032a08b6b0&gt;] kernfs_fop_write_iter+0x150/0x1e8 [&lt;0000000329fab65c&gt;] vfs_write+0x174/0x360 [&lt;0000000329fab9fc&gt;] ksys_write+0x74/0x100 [&lt;000000032aa03a5a&gt;] __do_syscall+0x1da/0x208 [&lt;000000032aa177b2&gt;] system_call+0x82/0xb0 INFORMACIÓN: LockDep está desactivado. CORRECCIÓN dma-kmalloc-64: Restaurando la zona roja de kmalloc 0x0000000000400564-0x0000000000400567=0xcc CORRECCIÓN dma-kmalloc-64: Objeto en 0x0000000000400540 no liberado."}], "references": [{"url": "https://git.kernel.org/stable/c/3cfdefdaaa4b2a77e84d0db5e0a47a7aa3bb615a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3d87debb8ed2649608ff432699e7c961c0c6f03b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/71da5991b6438ad6da13ceb25465ee2760a1c52f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/93a970494881004c348d8feb38463ee72496e99a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a908eae0f71811afee86be7088692f1aa5855c3b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b0d2bb5e31a693ebc8888eb407f8a257a3680efa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bd2e78462ae18484e55ae4d285df2c86b86bdd12", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c78f1345db4e4b3b78f9b768f4074ebd60abe966", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
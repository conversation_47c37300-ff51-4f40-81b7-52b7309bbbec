{"cve_id": "CVE-2025-37799", "published_date": "2025-05-03T12:15:14.950", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nvmxnet3: Fix malformed packet sizing in vmxnet3_process_xdp\n\nvmxnet3 driver's XDP handling is buggy for packet sizes using ring0 (that\nis, packet sizes between 128 - 3k bytes).\n\nWe noticed MTU-related connectivity issues with Cilium's service load-\nbalancing in case of vmxnet3 as NIC underneath. A simple curl to a HTTP\nbackend service where the XDP LB was doing IPIP encap led to overly large\npacket sizes but only for *some* of the packets (e.g. HTTP GET request)\nwhile others (e.g. the prior TCP 3WHS) looked completely fine on the wire.\n\nIn fact, the pcap recording on the backend node actually revealed that the\nnode with the XDP LB was leaking uninitialized kernel data onto the wire\nfor the affected packets, for example, while the packets should have been\n152 bytes their actual size was 1482 bytes, so the remainder after 152 bytes\nwas padded with whatever other data was in that page at the time (e.g. we\nsaw user/payload data from prior processed packets).\n\nWe only noticed this through an MTU issue, e.g. when the XDP LB node and\nthe backend node both had the same MTU (e.g. 1500) then the curl request\ngot dropped on the backend node's NIC given the packet was too large even\nthough the IPIP-encapped packet normally would never even come close to\nthe MTU limit. Lowering the MTU on the XDP LB (e.g. 1480) allowed to let\nthe curl request succeed (which also indicates that the kernel ignored the\npadding, and thus the issue wasn't very user-visible).\n\nCommit e127ce7699c1 (\"vmxnet3: Fix missing reserved tailroom\") was too eager\nto also switch xdp_prepare_buff() from rcd->len to rbi->len. It really needs\nto stick to rcd->len which is the actual packet length from the descriptor.\nThe latter we also feed into vmxnet3_process_xdp_small(), by the way, and\nit indicates the correct length needed to initialize the xdp->{data,data_end}\nparts. For e127ce7699c1 (\"vmxnet3: Fix missing reserved tailroom\") the\nrelevant part was adapting xdp_init_buff() to address the warning given the\nxdp_data_hard_end() depends on xdp->frame_sz. With that fixed, traffic on\nthe wire looks good again."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: vmxnet3: Se ha corregido un tamaño de paquete incorrecto en vmxnet3_process_xdp. El manejo de XDP del controlador vmxnet3 presenta errores para tamaños de paquete que utilizan ring0 (es decir, tamaños de paquete entre 128 y 3&#xa0;k bytes). Observamos problemas de conectividad relacionados con la MTU con el balanceo de carga del servicio de Cilium en el caso de vmxnet3 como NIC subyacente. Una simple conexión curl a un servicio HTTP backend donde el LB XDP realizaba encapsulado IPIP generó tamaños de paquete excesivamente grandes, pero solo para *algunos* paquetes (p. ej., una solicitud HTTP GET), mientras que otros (p. ej., el TCP 3WHS anterior) funcionaron correctamente en la red. De hecho, la grabación de pcap en el nodo backend reveló que el nodo con el LB XDP estaba filtrando datos de kernel sin inicializar en la red para los paquetes afectados. Por ejemplo, si bien los paquetes deberían haber tenido 152 bytes, su tamaño real era de 1482 bytes, por lo que el resto después de 152 bytes se rellenó con cualquier otro dato que hubiera en esa página en ese momento (por ejemplo, vimos datos de usuario/carga útil de paquetes procesados previamente). Solo notamos esto a través de un problema de MTU; por ejemplo, cuando el nodo LB XDP y el nodo backend tenían la misma MTU (por ejemplo, 1500), la solicitud curl se descartó en la NIC del nodo backend debido a que el paquete era demasiado grande, aunque el paquete encapsulado en IPIP normalmente ni siquiera se acercaría al límite de MTU. Reducir la MTU en el LB XDP (por ejemplo, 1480) permitió que la solicitud curl se ejecutara correctamente (lo que también indica que el kernel ignoró el relleno y, por lo tanto, el problema no era muy visible para el usuario). el commit e127ce7699c1 (\"vmxnet3: Corrección de la falta de espacio reservado para la cola\") estaba demasiado ansiosa por cambiar xdp_prepare_buff() de rcd-&gt;len a rbi-&gt;len. Es necesario que se mantenga en rcd-&gt;len, que es la longitud real del paquete del descriptor. Por cierto, esta última también se introduce en vmxnet3_process_xdp_small(), e indica la longitud correcta necesaria para inicializar las partes xdp-&gt;{data,data_end}. Para e127ce7699c1 (\"vmxnet3: Corrección de la falta de espacio reservado para la cola\"), la parte relevante fue adaptar xdp_init_buff() para abordar la advertencia, dado que xdp_data_hard_end() depende de xdp-&gt;frame_sz. Con esto corregido, el tráfico en la red se ve bien de nuevo."}], "references": [{"url": "https://git.kernel.org/stable/c/33e131a10459d16f181c8184d3f17f1c318c7002", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4c2227656d9003f4d77afc76f34dd81b95e4c2c4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c4312c4d244aa58e811ff0297e013124d115e793", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3ad76e36a37b0ff4a71b06d5b33530ee8c3a177", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
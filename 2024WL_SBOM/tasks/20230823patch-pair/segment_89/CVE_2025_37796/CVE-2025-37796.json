{"cve_id": "CVE-2025-37796", "published_date": "2025-05-01T14:15:44.173", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: at76c50x: fix use after free access in at76_disconnect\n\nThe memory pointed to by priv is freed at the end of at76_delete_device\nfunction (using ieee80211_free_hw). But the code then accesses the udev\nfield of the freed object to put the USB device. This may also lead to a\nmemory leak of the usb device. Fix this by using udev from interface."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: at76c50x: se corrige el use after free en at76_disconnect. La memoria a la que apunta priv se libera al final de la función at76_delete_device (mediante ieee80211_free_hw). Sin embargo, el código accede al campo udev del objeto liberado para colocar el dispositivo USB. Esto también puede provocar una fuga de memoria del dispositivo USB. Se soluciona usando udev desde la interfaz."}], "references": [{"url": "https://git.kernel.org/stable/c/152721cbae42713ecfbca6847e0f102ee6b19546", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/27c7e63b3cb1a20bb78ed4a36c561ea4579fd7da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3c619aec1f538333b56746d2f796aab1bca5c9a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e7df74745700f059dc117a620e566964a2e8f2c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6e4ab3e574c2a335b40fa1f70d1c54fcb58ab33f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7ca513631fa6ad3011b8b9197cdde0f351103704", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a9682bfef2cf3802515a902e964d774e137be1b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c731cdfddcf1be1590d5ba8c9b508f98e3a2b3d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
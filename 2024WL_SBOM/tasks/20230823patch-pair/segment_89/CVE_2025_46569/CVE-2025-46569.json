{"cve_id": "CVE-2025-46569", "published_date": "2025-05-01T20:15:37.887", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "Open Policy Agent (OPA) is an open source, general-purpose policy engine. Prior to version 1.4.0, when run as a server, OPA exposes an HTTP Data API for reading and writing documents. Requesting a virtual document through the Data API entails policy evaluation, where a Rego query containing a single data document reference is constructed from the requested path. This query is then used for policy evaluation. A HTTP request path can be crafted in a way that injects Rego code into the constructed query. The evaluation result cannot be made to return any other data than what is generated by the requested path, but this path can be misdirected, and the injected Rego code can be crafted to make the query succeed or fail; opening up for oracle attacks or, given the right circumstances, erroneous policy decision results. Furthermore, the injected code can be crafted to be computationally expensive, resulting in a Denial Of Service (DoS) attack. This issue has been patched in version 1.4.0. A workaround involves having network access to OPA’s RESTful APIs being limited to `localhost` and/or trusted networks, unless necessary for production reasons."}, {"lang": "es", "value": "Open Policy Agent (OPA) es un motor de políticas de código abierto y propósito general. Antes de la versión 1.4.0, al ejecutarse como servidor, OPA exponía una API de datos HTTP para leer y escribir documentos. Solicitar un documento virtual a través de la API de datos implicaba la evaluación de políticas, donde se generaba una consulta Rego con una única referencia al documento de datos a partir de la ruta solicitada. Esta consulta se utiliza para la evaluación de políticas. Una ruta de solicitud HTTP puede configurarse de forma que inyecte código Rego en la consulta construida. El resultado de la evaluación no puede devolver datos distintos a los generados por la ruta solicitada, pero esta puede desviarse y el código Rego inyectado puede configurarse para que la consulta tenga éxito o fracase, lo que expone a ataques de oráculo o, en las circunstancias adecuadas, a decisiones de política erróneas. Además, el código inyectado puede configurarse para que sea computacionalmente costoso, lo que resulta en un ataque de denegación de servicio (DoS). Este problema se ha corregido en la versión 1.4.0. Una solución alternativa implica que el acceso de red a las API RESTful de OPA se limite a `localhost` y/o redes confiables, a menos que sea necesario por razones de producción."}], "references": [{"url": "https://github.com/open-policy-agent/opa/commit/ad2063247a14711882f18c387a511fc8094aa79c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/open-policy-agent/opa/security/advisories/GHSA-6m8w-jc87-6cr7", "source": "<EMAIL>", "tags": []}]}
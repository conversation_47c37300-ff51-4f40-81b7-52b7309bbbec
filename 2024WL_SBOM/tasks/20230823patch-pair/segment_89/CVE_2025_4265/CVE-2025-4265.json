{"cve_id": "CVE-2025-4265", "published_date": "2025-05-05T05:15:16.060", "last_modified_date": "2025-05-07T16:33:20.067", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Emergency Ambulance Hiring Portal 1.0. Affected by this vulnerability is an unknown functionality of the file /admin/contact-us.php. The manipulation of the argument mobnum leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad crítica en PHPGurukul Emergency Ambulance Hiring Portal 1.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/contact-us.php. La manipulación del argumento mobnum provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/xiguala123/myCVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307369", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307369", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562993", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
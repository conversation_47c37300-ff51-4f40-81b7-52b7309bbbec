{"cve_id": "CVE-2025-2890", "published_date": "2025-04-30T09:15:14.503", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "The tagDiv Opt-In Builder plugin for WordPress is vulnerable to time-based SQL Injection via the ‘subscriptionCouponId’ parameter in all versions up to, and including, 1.7 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento tagDiv Opt-In Builder para WordPress es vulnerable a la inyección SQL basada en tiempo mediante el parámetro 'subscriptionCouponId' en todas las versiones hasta la 1.7 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, añadir consultas SQL adicionales a consultas ya existentes que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://tagdiv.com/newspaper-changelog/", "source": "<EMAIL>", "tags": []}, {"url": "https://tagdiv.com/tagdiv-opt-in-builder/", "source": "<EMAIL>", "tags": []}, {"url": "https://themeforest.net/item/newspaper/5489609", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fff1cff1-6745-4124-ba93-8b0749eae61a?source=cve", "source": "<EMAIL>", "tags": []}]}
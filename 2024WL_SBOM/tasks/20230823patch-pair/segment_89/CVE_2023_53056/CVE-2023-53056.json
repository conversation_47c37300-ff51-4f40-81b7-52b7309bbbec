{"cve_id": "CVE-2023-53056", "published_date": "2025-05-02T16:15:24.680", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: qla2xxx: Synchronize the IOCB count to be in order\n\nA system hang was observed with the following call trace:\n\nBUG: kernel NULL pointer dereference, address: 0000000000000000\nPGD 0 P4D 0\nOops: 0000 [#1] PREEMPT SMP NOPTI\nCPU: 15 PID: 86747 Comm: nvme Kdump: loaded Not tainted 6.2.0+ #1\nHardware name: Dell Inc. PowerEdge R6515/04F3CJ, BIOS 2.7.3 03/31/2022\nRIP: 0010:__wake_up_common+0x55/0x190\nCode: 41 f6 01 04 0f 85 b2 00 00 00 48 8b 43 08 4c 8d\n      40 e8 48 8d 43 08 48 89 04 24 48 89 c6\\\n      49 8d 40 18 48 39 c6 0f 84 e9 00 00 00 <49> 8b 40 18 89 6c 24 14 31\n      ed 4c 8d 60 e8 41 8b 18 f6 c3 04 75 5d\nRSP: 0018:ffffb05a82afbba0 EFLAGS: 00010082\nRAX: 0000000000000000 RBX: ffff8f9b83a00018 RCX: 0000000000000000\nRDX: 0000000000000001 RSI: ffff8f9b83a00020 RDI: ffff8f9b83a00018\nRBP: 0000000000000001 R08: ffffffffffffffe8 R09: ffffb05a82afbbf8\nR10: 70735f7472617473 R11: 5f30307832616c71 R12: 0000000000000001\nR13: 0000000000000003 R14: 0000000000000000 R15: 0000000000000000\nFS:  00007f815cf4c740(0000) GS:ffff8f9eeed80000(0000)\n\tknlGS:0000000000000000\nCS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 0000000000000000 CR3: 000000010633a000 CR4: 0000000000350ee0\nCall Trace:\n    <TASK>\n    __wake_up_common_lock+0x83/0xd0\n    qla_nvme_ls_req+0x21b/0x2b0 [qla2xxx]\n    __nvme_fc_send_ls_req+0x1b5/0x350 [nvme_fc]\n    nvme_fc_xmt_disconnect_assoc+0xca/0x110 [nvme_fc]\n    nvme_fc_delete_association+0x1bf/0x220 [nvme_fc]\n    ? nvme_remove_namespaces+0x9f/0x140 [nvme_core]\n    nvme_do_delete_ctrl+0x5b/0xa0 [nvme_core]\n    nvme_sysfs_delete+0x5f/0x70 [nvme_core]\n    kernfs_fop_write_iter+0x12b/0x1c0\n    vfs_write+0x2a3/0x3b0\n    ksys_write+0x5f/0xe0\n    do_syscall_64+0x5c/0x90\n    ? syscall_exit_work+0x103/0x130\n    ? syscall_exit_to_user_mode+0x12/0x30\n    ? do_syscall_64+0x69/0x90\n    ? exit_to_user_mode_loop+0xd0/0x130\n    ? exit_to_user_mode_prepare+0xec/0x100\n    ? syscall_exit_to_user_mode+0x12/0x30\n    ? do_syscall_64+0x69/0x90\n    ? syscall_exit_to_user_mode+0x12/0x30\n    ? do_syscall_64+0x69/0x90\n    entry_SYSCALL_64_after_hwframe+0x72/0xdc\n    RIP: 0033:0x7f815cd3eb97\n\nThe IOCB counts are out of order and that would block any commands from\ngoing out and subsequently hang the system. Synchronize the IOCB count to\nbe in correct order."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: qla2xxx: Sincronizar el recuento de IOCB para que esté en orden Se observó un bloqueo del sistema con el siguiente seguimiento de llamada: ERROR: desreferencia de puntero NULL del kernel, dirección: 0000000000000000 PGD 0 P4D 0 Oops: 0000 [#1] PREEMPT SMP NOPTI CPU: 15 PID: 86747 Comm: nvme Kdump: cargado No contaminado 6.2.0+ #1 Nombre del hardware: Dell Inc. PowerEdge R6515/04F3CJ, BIOS 2.7.3 31/03/2022 RIP: 0010:__wake_up_common+0x55/0x190 Código: 41 f6 01 04 0f 85 b2 00 00 00 48 8b 43 08 4c 8d 40 e8 48 8d 43 08 48 89 04 24 48 89 c6\\ 49 8d 40 18 48 39 c6 0f 84 e9 00 00 00 &lt;49&gt; 8b 40 18 89 6c 24 14 31 ed 4c 8d 60 e8 41 8b 18 f6 c3 04 75 5d RSP: 0018:ffffb05a82afbba0 EFLAGS: 00010082 RAX: 00000000000000000 RBX: ffff8f9b83a00018 RCX: 0000000000000000 RDX: 0000000000000001 RSI: ffff8f9b83a00020 RDI: ffff8f9b83a00018 RBP: 0000000000000001 R08: ffffffffffffffe8 R09: ffffb05a82afbbf8 R10: 70735f7472617473 R11: 5f30307832616c71 R12: 0000000000000001 R13: 0000000000000003 R14: 0000000000000000 R15: 0000000000000000 FS: 00007f815cf4c740(0000) GS:ffff8f9eeed80000(0000) knlGS:000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 000000000000000 CR3: 000000010633a000 CR4: 0000000000350ee0 Rastreo de llamadas:  __wake_up_common_lock+0x83/0xd0 qla_nvme_ls_req+0x21b/0x2b0 [qla2xxx] __nvme_fc_send_ls_req+0x1b5/0x350 [nvme_fc] nvme_fc_xmt_disconnect_assoc+0xca/0x110 [nvme_fc] nvme_fc_delete_association+0x1bf/0x220 [nvme_fc] ? nvme_remove_namespaces+0x9f/0x140 [núcleo_nvme] nvme_do_delete_ctrl+0x5b/0xa0 [núcleo_nvme] nvme_sysfs_delete+0x5f/0x70 [núcleo_nvme] kernfs_fop_write_iter+0x12b/0x1c0 vfs_write+0x2a3/0x3b0 ksys_write+0x5f/0xe0 do_syscall_64+0x5c/0x90 ? syscall_exit_work+0x103/0x130 ? syscall_exit_to_user_mode+0x12/0x30 ? do_syscall_64+0x69/0x90 ? exit_to_user_mode_loop+0xd0/0x130 ? exit_to_user_mode_prepare+0xec/0x100 ? syscall_exit_to_user_mode+0x12/0x30 ? do_syscall_64+0x69/0x90 ? syscall_exit_to_user_mode+0x12/0x30 ? do_syscall_64+0x69/0x90 entry_SYSCALL_64_after_hwframe+0x72/0xdc RIP: 0033:0x7f815cd3eb97 Los conteos de IOCB están desordenados, lo que impediría la salida de cualquier comando y, posteriormente, bloquearía el sistema. Sincronice el conteo de IOCB para que esté en el orden correcto."}], "references": [{"url": "https://git.kernel.org/stable/c/6295b3ec64a3623fa96869ffb7cf17d0b3c92035", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6d57b77d7369ed73836c82b25f785b34923eef84", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3affdeb400f3adc925bd996f3839481f5291839", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ffd7831841d3c56c655531fc8c5acafaaf20e1bb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37786", "published_date": "2025-05-01T14:15:42.890", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: dsa: free routing table on probe failure\n\nIf complete = true in dsa_tree_setup(), it means that we are the last\nswitch of the tree which is successfully probing, and we should be\nsetting up all switches from our probe path.\n\nAfter \"complete\" becomes true, dsa_tree_setup_cpu_ports() or any\nsubsequent function may fail. If that happens, the entire tree setup is\nin limbo: the first N-1 switches have successfully finished probing\n(doing nothing but having allocated persistent memory in the tree's\ndst->ports, and maybe dst->rtable), and switch N failed to probe, ending\nthe tree setup process before anything is tangible from the user's PoV.\n\nIf switch N fails to probe, its memory (ports) will be freed and removed\nfrom dst->ports. However, the dst->rtable elements pointing to its ports,\nas created by dsa_link_touch(), will remain there, and will lead to\nuse-after-free if dereferenced.\n\nIf dsa_tree_setup_switches() returns -EPROBE_DEFER, which is entirely\npossible because that is where ds->ops->setup() is, we get a kasan\nreport like this:\n\n==================================================================\nBUG: KASAN: slab-use-after-free in mv88e6xxx_setup_upstream_port+0x240/0x568\nRead of size 8 at addr ffff000004f56020 by task kworker/u8:3/42\n\nCall trace:\n __asan_report_load8_noabort+0x20/0x30\n mv88e6xxx_setup_upstream_port+0x240/0x568\n mv88e6xxx_setup+0xebc/0x1eb0\n dsa_register_switch+0x1af4/0x2ae0\n mv88e6xxx_register_switch+0x1b8/0x2a8\n mv88e6xxx_probe+0xc4c/0xf60\n mdio_probe+0x78/0xb8\n really_probe+0x2b8/0x5a8\n __driver_probe_device+0x164/0x298\n driver_probe_device+0x78/0x258\n __device_attach_driver+0x274/0x350\n\nAllocated by task 42:\n __kasan_kmalloc+0x84/0xa0\n __kmalloc_cache_noprof+0x298/0x490\n dsa_switch_touch_ports+0x174/0x3d8\n dsa_register_switch+0x800/0x2ae0\n mv88e6xxx_register_switch+0x1b8/0x2a8\n mv88e6xxx_probe+0xc4c/0xf60\n mdio_probe+0x78/0xb8\n really_probe+0x2b8/0x5a8\n __driver_probe_device+0x164/0x298\n driver_probe_device+0x78/0x258\n __device_attach_driver+0x274/0x350\n\nFreed by task 42:\n __kasan_slab_free+0x48/0x68\n kfree+0x138/0x418\n dsa_register_switch+0x2694/0x2ae0\n mv88e6xxx_register_switch+0x1b8/0x2a8\n mv88e6xxx_probe+0xc4c/0xf60\n mdio_probe+0x78/0xb8\n really_probe+0x2b8/0x5a8\n __driver_probe_device+0x164/0x298\n driver_probe_device+0x78/0x258\n __device_attach_driver+0x274/0x350\n\nThe simplest way to fix the bug is to delete the routing table in its\nentirety. dsa_tree_setup_routing_table() has no problem in regenerating\nit even if we deleted links between ports other than those of switch N,\nbecause dsa_link_touch() first checks whether the port pair already\nexists in dst->rtable, allocating if not.\n\nThe deletion of the routing table in its entirety already exists in\ndsa_tree_teardown(), so refactor that into a function that can also be\ncalled from the tree setup error path.\n\nIn my analysis of the commit to blame, it is the one which added\ndsa_link elements to dst->rtable. Prior to that, each switch had its own\nds->rtable which is freed when the switch fails to probe. But the tree\nis potentially persistent memory."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: dsa: tabla de enrutamiento libre en caso de fallo de sondeo. Si \"complete\" es verdadero en dsa_tree_setup(), significa que somos el último conmutador del árbol que está sondeando correctamente, y deberíamos estar configurando todos los conmutadores desde nuestra ruta de sondeo. Una vez que \"complete\" es verdadero, dsa_tree_setup_cpu_ports() o cualquier función posterior puede fallar. Si esto ocurre, toda la configuración del árbol queda en el limbo: los primeros N-1 conmutadores han finalizado el sondeo correctamente (sin hacer nada más que asignar memoria persistente en dst-&gt;ports del árbol, y quizás en dst-&gt;rtable), y el conmutador N no ha podido sondear, finalizando el proceso de configuración del árbol antes de que el usuario pueda ver nada tangible. Si el conmutador N no puede sondear, su memoria (puertos) se liberará y se eliminará de dst-&gt;ports. Sin embargo, los elementos dst-&gt;rtable que apuntan a sus puertos, tal como los creó dsa_link_touch(), permanecerán allí y darán lugar a un use-after-free si se desreferencian. Si dsa_tree_setup_switches() devuelve -EPROBE_DEFER, lo cual es completamente posible porque es donde está ds-&gt;ops-&gt;setup(), obtenemos un informe de kasan como este: ===================================================================== ERROR: KASAN: slab-use-after-free en mv88e6xxx_setup_upstream_port+0x240/0x568 Lectura de tamaño 8 en la dirección ffff000004f56020 por la tarea kworker/u8:3/42 Rastreo de llamadas: __asan_report_load8_noabort+0x20/0x30 mv88e6xxx_setup_upstream_port+0x240/0x568 mv88e6xxx_setup+0xebc/0x1eb0 dsa_register_switch+0x1af4/0x2ae0 mv88e6xxx_register_switch+0x1b8/0x2a8 mv88e6xxx_probe+0xc4c/0xf60 mdio_probe+0x78/0xb8 really_probe+0x2b8/0x5a8 __driver_probe_device+0x164/0x298 driver_probe_device+0x78/0x258 __device_attach_driver+0x274/0x350 Allocated by task 42: __kasan_kmalloc+0x84/0xa0 __kmalloc_cache_noprof+0x298/0x490 dsa_switch_touch_ports+0x174/0x3d8 dsa_register_switch+0x800/0x2ae0 mv88e6xxx_register_switch+0x1b8/0x2a8 mv88e6xxx_probe+0xc4c/0xf60 mdio_probe+0x78/0xb8 really_probe+0x2b8/0x5a8 __driver_probe_device+0x164/0x298 driver_probe_device+0x78/0x258 __device_attach_driver+0x274/0x350 Freed by task 42: __kasan_slab_free+0x48/0x68 kfree+0x138/0x418 dsa_register_switch+0x2694/0x2ae0 mv88e6xxx_register_switch+0x1b8/0x2a8 mv88e6xxx_probe+0xc4c/0xf60 mdio_probe+0x78/0xb8 really_probe+0x2b8/0x5a8 __driver_probe_device+0x164/0x298 driver_probe_device+0x78/0x258 __device_attach_driver+0x274/0x350 La forma más sencilla de corregir el error es eliminar la tabla de enrutamiento en su totalidad. dsa_tree_setup_routing_table() no tiene problemas para regenerarla incluso si eliminamos enlaces entre puertos distintos a los del switch N, ya que dsa_link_touch() primero comprueba si el par de puertos ya existe en dst-&gt;rtable, y la asigna en caso contrario. La eliminación completa de la tabla de enrutamiento ya existe en dsa_tree_teardown(), por lo que se debe refactorizar en una función que también pueda llamarse desde la ruta de error de configuración del árbol. En mi análisis de la confirmación responsable, es la que añadió elementos dsa_link a dst-&gt;rtable. Antes de eso, cada switch tenía su propia ds-&gt;rtable, que se libera cuando el switch falla al sondear. Sin embargo, el árbol es potencialmente memoria persistente."}], "references": [{"url": "https://git.kernel.org/stable/c/5c8066fbdb9653c6e9a224bdcd8f9c91a484f0de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8bf108d7161ffc6880ad13a0cc109de3cf631727", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a038f5f15af455dfe35bc68549e02b950978700a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fb12b460ec46c9efad98de6d9ba349691db51dc7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
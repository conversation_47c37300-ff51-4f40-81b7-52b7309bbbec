{"cve_id": "CVE-2025-32887", "published_date": "2025-05-01T18:15:55.957", "last_modified_date": "2025-06-20T16:39:45.180", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna v1 devices with app 5.5.3 and firmware 0.25.5. A command channel includes the next hop. which can be intercepted and used to break frequency hopping."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna v1 con la aplicación 5.5.3 y el firmware 0.25.5. Un canal de comando incluye el siguiente salto, que puede interceptarse y utilizarse para interrumpir el salto de frecuencia."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
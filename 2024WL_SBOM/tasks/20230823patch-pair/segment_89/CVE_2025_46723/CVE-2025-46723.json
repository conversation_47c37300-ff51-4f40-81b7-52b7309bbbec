{"cve_id": "CVE-2025-46723", "published_date": "2025-05-02T23:15:16.580", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "OpenVM is a performant and modular zkVM framework built for customization and extensibility. In version 1.0.0, OpenVM is vulnerable to overflow through byte decomposition of pc in AUIPC chip. A typo results in the highest limb of pc being range checked to 8-bits instead of 6-bits. This results in the if statement never being triggered because the enumeration gives i=0,1,2, when instead the enumeration should give i=1,2,3, leaving pc_limbs[3] range checked to 8-bits instead of 6-bits. This leads to a vulnerability where the pc_limbs decomposition differs from the true pc, which means a malicious prover can make the destination register take a different value than the AUIPC instruction dictates, by making the decomposition overflow the BabyBear field. This issue has been patched in version 1.1.0."}, {"lang": "es", "value": "OpenVM es un framework zkVM modular y de alto rendimiento, diseñado para la personalización y la extensibilidad. En la versión 1.0.0, OpenVM es vulnerable a un desbordamiento debido a la descomposición de bytes de pc en el chip AUIPC. Un error tipográfico provoca que la rama más alta de pc se compruebe a 8 bits en lugar de 6. Esto provoca que la instrucción if nunca se active, ya que la enumeración devuelve i=0,1,2, cuando debería dar i=1,2,3, dejando el rango de pc_limbs[3] comprobado a 8 bits en lugar de 6. Esto genera una vulnerabilidad donde la descomposición de pc_limbs difiere de la verdadera pc, lo que significa que un probador malicioso puede hacer que el registro de destino tome un valor diferente al que dicta la instrucción AUIPC, provocando que la descomposición desborde el campo BabyBear. Este problema se ha corregido en la versión 1.1.0."}], "references": [{"url": "https://cantina.xyz/code/c486d600-bed0-4fc6-aed1-de759fd29fa2/findings/21", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openvm-org/openvm/blob/0f94c8a3dfa7536c1231465d1bdee5fc607a5993/extensions/rv32im/circuit/src/auipc/core.rs#L135", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openvm-org/openvm/commit/68da4b50c033da5603517064aa0a08e1bbf70a01", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openvm-org/openvm/releases/tag/v1.1.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openvm-org/openvm/security/advisories/GHSA-jf2r-x3j4-23m7", "source": "<EMAIL>", "tags": []}]}
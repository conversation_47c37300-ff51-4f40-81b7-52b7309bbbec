{"cve_id": "CVE-2022-49885", "published_date": "2025-05-01T15:16:13.480", "last_modified_date": "2025-05-07T13:21:04.487", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nACPI: APEI: Fix integer overflow in ghes_estatus_pool_init()\n\nChange num_ghes from int to unsigned int, preventing an overflow\nand causing subsequent vmalloc() to fail.\n\nThe overflow happens in ghes_estatus_pool_init() when calculating\nlen during execution of the statement below as both multiplication\noperands here are signed int:\n\nlen += (num_ghes * GHES_ESOURCE_PREALLOC_MAX_SIZE);\n\nThe following call trace is observed because of this bug:\n\n[    9.317108] swapper/0: vmalloc error: size 18446744071562596352, exceeds total pages, mode:0xcc0(GFP_KERNEL), nodemask=(null),cpuset=/,mems_allowed=0-1\n[    9.317131] Call Trace:\n[    9.317134]  <TASK>\n[    9.317137]  dump_stack_lvl+0x49/0x5f\n[    9.317145]  dump_stack+0x10/0x12\n[    9.317146]  warn_alloc.cold+0x7b/0xdf\n[    9.317150]  ? __device_attach+0x16a/0x1b0\n[    9.317155]  __vmalloc_node_range+0x702/0x740\n[    9.317160]  ? device_add+0x17f/0x920\n[    9.317164]  ? dev_set_name+0x53/0x70\n[    9.317166]  ? platform_device_add+0xf9/0x240\n[    9.317168]  __vmalloc_node+0x49/0x50\n[    9.317170]  ? ghes_estatus_pool_init+0x43/0xa0\n[    9.317176]  vmalloc+0x21/0x30\n[    9.317177]  ghes_estatus_pool_init+0x43/0xa0\n[    9.317179]  acpi_hest_init+0x129/0x19c\n[    9.317185]  acpi_init+0x434/0x4a4\n[    9.317188]  ? acpi_sleep_proc_init+0x2a/0x2a\n[    9.317190]  do_one_initcall+0x48/0x200\n[    9.317195]  kernel_init_freeable+0x221/0x284\n[    9.317200]  ? rest_init+0xe0/0xe0\n[    9.317204]  kernel_init+0x1a/0x130\n[    9.317205]  ret_from_fork+0x22/0x30\n[    9.317208]  </TASK>\n\n[ rjw: Subject and changelog edits ]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ACPI: APEI: Se corrige el desbordamiento de enteros en ghes_estatus_pool_init(). Se cambia num_ghes de int a unsigned int, lo que evita un desbordamiento y provoca el fallo de vmalloc(). El desbordamiento ocurre en ghes_estatus_pool_init() al calcular len durante la ejecución de la siguiente instrucción, ya que ambos operandos de multiplicación son int con signo: len += (num_ghes * GHES_ESOURCE_PREALLOC_MAX_SIZE); El siguiente seguimiento de llamadas se observa debido a este error: [ 9.317108] swapper/0: error de vmalloc: tamaño 18446744071562596352, excede el total de páginas, modo: 0xcc0 (GFP_KERNEL), nodemask = (null), cpuset = /, mems_allowed = 0-1 [ 9.317131] Seguimiento de llamadas: [ 9.317134]  [ 9.317137] dump_stack_lvl + 0x49/0x5f [ 9.317145] dump_stack + 0x10/0x12 [ 9.317146] warn_alloc.cold + 0x7b/0xdf [ 9.317150] ? __adjunto_dispositivo+0x16a/0x1b0 [ 9.317155] __rango_nodo_vmalloc+0x702/0x740 [ 9.317160] ? agregado_dispositivo+0x17f/0x920 [ 9.317164] ? nombre_conjunto_dispositivo+0x53/0x70 [ 9.317166] ? agregado_dispositivo_plataforma+0xf9/0x240 [ 9.317168] __nodo_vmalloc+0x49/0x50 [ 9.317170] ? ghes_estatus_pool_init+0x43/0xa0 [ 9.317176] vmalloc+0x21/0x30 [ 9.317177] ghes_estatus_pool_init+0x43/0xa0 [ 9.317179] acpi_hest_init+0x129/0x19c [ 9.317185] acpi_init+0x434/0x4a4 [9.317188]? acpi_sleep_proc_init+0x2a/0x2a [9.317190] do_one_initcall+0x48/0x200 [9.317195] kernel_init_freeable+0x221/0x284 [9.317200] ? rest_init+0xe0/0xe0 [ 9.317204] kernel_init+0x1a/0x130 [ 9.317205] ret_from_fork+0x22/0x30 [ 9.317208]  [ rjw: Asunto y ediciones del registro de cambios ]"}], "references": [{"url": "https://git.kernel.org/stable/c/43d2748394c3feb86c0c771466f5847e274fc043", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/4c10c854113720cbfe75d4f51db79b700a629e73", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9edf20e5a1d805855e78f241cf221d741b50d482", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c50ec15725e005e9fb20bce69b6c23b135a4a9b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
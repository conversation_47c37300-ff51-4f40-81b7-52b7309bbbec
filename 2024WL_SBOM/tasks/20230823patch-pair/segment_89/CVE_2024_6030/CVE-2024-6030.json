{"cve_id": "CVE-2024-6030", "published_date": "2025-04-30T20:15:21.030", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Tesla Model S oFono Unnecessary Privileges Sandbox Escape Vulnerability. This vulnerability allows local attackers to escape the sandbox on affected Tesla Model S vehicles. An attacker must first obtain the ability to execute code within the sandbox on the target system in order to exploit this vulnerability.\n \nThe specific flaw exists within the oFono process. The process allows an attacker to modify interfaces. An attacker can leverage this vulnerability to bypass the iptables network sandbox. Was ZDI-CAN-23200."}, {"lang": "es", "value": "Vulnerabilidad de escape del entorno de pruebas de oFono con privilegios innecesarios en el Tesla Model S. Esta vulnerabilidad permite a atacantes locales escapar del entorno de pruebas en los vehículos Tesla Model S afectados. Para explotar esta vulnerabilidad, un atacante debe primero ejecutar código dentro del entorno de pruebas en el sistema objetivo. La falla específica se encuentra en el proceso oFono, que permite a un atacante modificar interfaces. Un atacante puede aprovechar esta vulnerabilidad para eludir el entorno de pruebas de red de iptables. Anteriormente, se denominaba ZDI-CAN-23200."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-263/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0853", "published_date": "2025-05-06T22:15:16.910", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "The PGS Core plugin for WordPress is vulnerable to SQL Injection via the 'event' parameter in the 'save_header_builder' function in all versions up to, and including, 5.8.0 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query. This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento PGS Core para WordPress es vulnerable a la inyección SQL mediante el parámetro 'event' de la función 'save_header_builder' en todas las versiones hasta la 5.8.0 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas ya existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://docs.potenzaglobalsolutions.com/docs/ciyashop-wp/changelog/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/ca41c951-318f-47a7-9a30-c1d4eea1b1b5?source=cve", "source": "<EMAIL>", "tags": []}]}
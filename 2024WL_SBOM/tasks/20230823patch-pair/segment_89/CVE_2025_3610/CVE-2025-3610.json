{"cve_id": "CVE-2025-3610", "published_date": "2025-05-06T03:15:17.777", "last_modified_date": "2025-05-07T14:13:35.980", "descriptions": [{"lang": "en", "value": "The Reales WP STPT plugin for WordPress is vulnerable to privilege escalation via account takeover in all versions up to, and including, 2.1.2. This is due to the plugin not properly validating a user's identity prior to updating their details like password. This makes it possible for authenticated attackers, with subscriber-level access and above, to change arbitrary user's passwords and email addresses, including administrators, and leverage that to gain access to their account. This can be combined with CVE-2025-3609 to achieve remote code execution as an originally unauthenticated user with no account."}, {"lang": "es", "value": "El complemento Reales WP STPT para WordPress es vulnerable a la escalada de privilegios mediante robo de cuenta en todas las versiones hasta la 2.1.2 incluida. Esto se debe a que el complemento no valida correctamente la identidad del usuario antes de actualizar sus datos, como la contraseña. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, cambiar las contraseñas y direcciones de correo electrónico de usuarios arbitrarios, incluidos los administradores, y aprovecharlo para acceder a sus cuentas. Esto puede combinarse con CVE-2025-3609 para lograr la ejecución remota de código como un usuario no autenticado sin cuenta."}], "references": [{"url": "https://themeforest.net/item/reales-wp-real-estate-wordpress-theme/********", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/38c6b149-39d7-491a-9f3a-261087a52a03?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-3513", "published_date": "2025-05-02T06:15:48.887", "last_modified_date": "2025-05-28T16:02:00.560", "descriptions": [{"lang": "en", "value": "The SureForms  WordPress plugin before 1.4.4 does not sanitise and escape some of its Form settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento SureForms para WordPress anterior a la versión 1.4.4 no depura ni escapa de algunas de sus configuraciones de formulario, lo que podría permitir que usuarios con privilegios elevados como el administrador realicen ataques de Cross-Site Scripting Almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/dd7e0bb3-4a98-4f62-bd2e-f30b27d71226/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
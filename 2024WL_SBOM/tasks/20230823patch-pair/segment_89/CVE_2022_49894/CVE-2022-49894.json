{"cve_id": "CVE-2022-49894", "published_date": "2025-05-01T15:16:14.417", "last_modified_date": "2025-05-07T13:19:31.730", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncxl/region: Fix region HPA ordering validation\n\nSome regions may not have any address space allocated. Skip them when\nvalidating HPA order otherwise a crash like the following may result:\n\n devm_cxl_add_region: cxl_acpi cxl_acpi.0: decoder3.4: created region9\n BUG: kernel NULL pointer dereference, address: 0000000000000000\n [..]\n RIP: 0010:store_targetN+0x655/0x1740 [cxl_core]\n [..]\n Call Trace:\n  <TASK>\n  kernfs_fop_write_iter+0x144/0x200\n  vfs_write+0x24a/0x4d0\n  ksys_write+0x69/0xf0\n  do_syscall_64+0x3a/0x90\n\nstore_targetN+0x655/0x1740:\nalloc_region_ref at drivers/cxl/core/region.c:676\n(inlined by) cxl_port_attach_region at drivers/cxl/core/region.c:850\n(inlined by) cxl_region_attach at drivers/cxl/core/region.c:1290\n(inlined by) attach_target at drivers/cxl/core/region.c:1410\n(inlined by) store_targetN at drivers/cxl/core/region.c:1453"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cxl/region: Corregir la validación del ordenamiento HPA de la región. Algunas regiones pueden no tener ningún espacio de direcciones asignado. Omítalos al validar el orden de HPA; de lo contrario, puede producirse un fallo como el siguiente: devm_cxl_add_region: cxl_acpi cxl_acpi.0: decoder3.4: created region9 BUG: desreferencia de puntero NULL del kernel, dirección: 0000000000000000 [..] RIP: 0010:store_targetN+0x655/0x1740 [cxl_core] [..] Seguimiento de llamadas:  kernfs_fop_write_iter+0x144/0x200 vfs_write+0x24a/0x4d0 ksys_write+0x69/0xf0 do_syscall_64+0x3a/0x90 store_targetN+0x655/0x1740: alloc_region_ref at drivers/cxl/core/region.c:676 (integrado por) cxl_port_attach_region en drivers/cxl/core/region.c:850 (integrado por) cxl_region_attach en drivers/cxl/core/region.c:1290 (integrado por) attached_target en drivers/cxl/core/region.c:1410 (integrado por) store_targetN en drivers/cxl/core/region.c:1453"}], "references": [{"url": "https://git.kernel.org/stable/c/12316b9f7c18138ae656050cfd716728e27b7e2f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a90accb358ae33ea982a35595573f7a045993f8b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
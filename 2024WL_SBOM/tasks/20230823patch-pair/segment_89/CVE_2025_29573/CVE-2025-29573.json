{"cve_id": "CVE-2025-29573", "published_date": "2025-05-05T19:15:55.653", "last_modified_date": "2025-06-16T20:19:43.233", "descriptions": [{"lang": "en", "value": "Cross-Site Scripting (XSS) vulnerability exists in Mezzanine CMS 6.0.0 in the \"View Entries\" feature within the Forms module."}, {"lang": "es", "value": "Existe una vulnerabilidad de cross-site scripting (XSS) en Mezzanine CMS 6.0.0 en la función \"View Entries\" dentro del módulo Formularios."}], "references": [{"url": "https://github.com/stephenmcd/mezzanine", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.squadappsec.com/post/cve-2025-29573-persistent-xss-in-mezzanine-cms-6-0-0-via-malicious-filename", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
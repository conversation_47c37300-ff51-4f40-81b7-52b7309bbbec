{"cve_id": "CVE-2023-53081", "published_date": "2025-05-02T16:15:27.117", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocfs2: fix data corruption after failed write\n\nWhen buffered write fails to copy data into underlying page cache page,\nocfs2_write_end_nolock() just zeroes out and dirties the page.  This can\nleave dirty page beyond EOF and if page writeback tries to write this page\nbefore write succeeds and expands i_size, page gets into inconsistent\nstate where page dirty bit is clear but buffer dirty bits stay set\nresulting in page data never getting written and so data copied to the\npage is lost.  Fix the problem by invalidating page beyond EOF after\nfailed write."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ocfs2: se corrige la corrupción de datos tras una escritura fallida. Cuando una escritura en búfer no copia los datos en la página de caché de la página subyacente, ocfs2_write_end_nolock() simplemente pone a cero y contamina la página. Esto puede dejar una página contaminada más allá del EOF. Si la escritura diferida intenta escribir en esta página antes de que la escritura tenga éxito y expande i_size, la página entra en un estado inconsistente donde el bit de página contaminada se borra, pero los bits de búfer contaminados permanecen activos, lo que resulta en que los datos de la página nunca se escriban y, por lo tanto, se pierdan los datos copiados. Se soluciona el problema invalidando la página más allá del EOF tras una escritura fallida."}], "references": [{"url": "https://git.kernel.org/stable/c/1629f6f522b2d058019710466a84b240683bbee3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/205759c6c18f54659b0b5976b14a52d1b3eb9f57", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/47eb055ad3588fc96d34e9e1dd87b210ce62906b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4c24eb49ab44351424ac8fe8567f91ea48a06089", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90410bcf873cf05f54a32183afff0161f44f9715", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/91d7a4bd5656552d6259e2d0f8859f9e8cc5ef68", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a9e53869cb43c96d6d851c491fd4e26430ab6ba6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c26f3ff4c0be590c1250f945ac2e4fc5fcdc5f45", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
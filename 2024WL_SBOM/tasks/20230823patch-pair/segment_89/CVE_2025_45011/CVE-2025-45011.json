{"cve_id": "CVE-2025-45011", "published_date": "2025-04-30T14:15:29.193", "last_modified_date": "2025-05-09T13:45:02.817", "descriptions": [{"lang": "en", "value": "A HTML Injection vulnerability was discovered in the foreigner-search.php file of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary code via the searchdata POST request parameter."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección HTML en el archivo foreigner-search.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario mediante el parámetro de solicitud POST searchdata."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/foreigner-search-html-injection.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
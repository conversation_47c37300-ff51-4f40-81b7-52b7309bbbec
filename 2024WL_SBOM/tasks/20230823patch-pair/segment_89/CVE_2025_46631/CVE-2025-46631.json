{"cve_id": "CVE-2025-46631", "published_date": "2025-05-01T20:15:39.010", "last_modified_date": "2025-05-27T14:24:42.853", "descriptions": [{"lang": "en", "value": "Improper access controls in the web management portal of the Tenda RX2 Pro *********** allows an unauthenticated remote attacker to enable telnet access to the router's OS by sending a /goform/telnet web request."}, {"lang": "es", "value": "Los controles de acceso inadecuados en el portal de administración web del Tenda RX2 Pro *********** permiten que un atacante remoto no autenticado habilite el acceso telnet al sistema operativo del enrutador mediante el envío de una solicitud web /goform/telnet."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46631-enable-telnet-unauthenticated-through-httpd", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
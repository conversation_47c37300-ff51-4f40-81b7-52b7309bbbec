{"cve_id": "CVE-2022-49918", "published_date": "2025-05-01T15:16:17.060", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipvs: fix WARNING in __ip_vs_cleanup_batch()\n\nDuring the initialization of ip_vs_conn_net_init(), if file ip_vs_conn\nor ip_vs_conn_sync fails to be created, the initialization is successful\nby default. Therefore, the ip_vs_conn or ip_vs_conn_sync file doesn't\nbe found during the remove.\n\nThe following is the stack information:\nname 'ip_vs_conn_sync'\nWARNING: CPU: 3 PID: 9 at fs/proc/generic.c:712\nremove_proc_entry+0x389/0x460\nModules linked in:\nWorkqueue: netns cleanup_net\nRIP: 0010:remove_proc_entry+0x389/0x460\nCall Trace:\n<TASK>\n__ip_vs_cleanup_batch+0x7d/0x120\nops_exit_list+0x125/0x170\ncleanup_net+0x4ea/0xb00\nprocess_one_work+0x9bf/0x1710\nworker_thread+0x665/0x1080\nkthread+0x2e4/0x3a0\nret_from_fork+0x1f/0x30\n</TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipvs: corrección de una ADVERTENCIA en __ip_vs_cleanup_batch(). Durante la inicialización de ip_vs_conn_net_init(), si no se crea el archivo ip_vs_conn o ip_vs_conn_sync, la inicialización se realiza correctamente por defecto. Por lo tanto, no se encuentra el archivo ip_vs_conn o ip_vs_conn_sync durante la eliminación. La siguiente es la información de la pila: nombre 'ip_vs_conn_sync' ADVERTENCIA: CPU: 3 PID: 9 en fs/proc/generic.c:712 remove_proc_entry+0x389/0x460 Módulos vinculados en: Cola de trabajo: netns cleanup_net RIP: 0010:remove_proc_entry+0x389/0x460 Seguimiento de llamadas:  __ip_vs_cleanup_batch+0x7d/0x120 ops_exit_list+0x125/0x170 cleanup_net+0x4ea/0xb00 process_one_work+0x9bf/0x1710 worker_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30 "}], "references": [{"url": "https://git.kernel.org/stable/c/3d00c6a0da8ddcf75213e004765e4a42acc71d5d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5ee2d6b726b0ce339e36569e5849692f4cf4595e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7effc4ce3d1434ce6ff286866585a6e905fdbfc1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/931f56d59c854263b32075bfac56fdb3b1598d1b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e724220b826e008764309d2a1f55a9434a4e1530", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f08ee2aa24c076f81d84e26e213d8c6f4efd9f50", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4327", "published_date": "2025-05-06T07:15:48.607", "last_modified_date": "2025-06-12T17:29:58.950", "descriptions": [{"lang": "en", "value": "A vulnerability was found in MRCMS 3.1.2. It has been classified as problematic. Affected is an unknown function. The manipulation leads to cross-site request forgery. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Multiple endpoints might be affected."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en MRCMS 3.1.2. Se ha clasificado como problemática. Se trata de una función desconocida. La manipulación provoca cross-site request forgery. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. <PERSON><PERSON><PERSON><PERSON> endpoints podrían verse afectados."}], "references": [{"url": "https://github.com/bdkuzma/vuln/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307428", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307428", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563555", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
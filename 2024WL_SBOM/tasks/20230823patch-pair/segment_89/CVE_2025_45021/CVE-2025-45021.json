{"cve_id": "CVE-2025-45021", "published_date": "2025-04-30T14:15:29.777", "last_modified_date": "2025-05-09T13:43:27.980", "descriptions": [{"lang": "en", "value": "A SQL Injection vulnerability was identified in the admin/edit-directory.php file of the PHPGurukul Directory Management System v2.0. Attackers can exploit this vulnerability via the email parameter in a POST request to execute arbitrary SQL commands."}, {"lang": "es", "value": "Se identificó una vulnerabilidad de inyección SQL en el archivo admin/edit-directory.php de PHPGurukul Directory Management System v2.0. Los atacantes pueden explotar esta vulnerabilidad mediante el parámetro de correo electrónico en una solicitud POST para ejecutar comandos SQL arbitrarios."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Directory%20Management%20System/SQL/SQl_Injection_in_edit-directory.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
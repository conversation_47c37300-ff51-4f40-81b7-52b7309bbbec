{"cve_id": "CVE-2025-44839", "published_date": "2025-05-01T17:15:50.127", "last_modified_date": "2025-05-22T15:30:04.307", "descriptions": [{"lang": "en", "value": "TOTOLINK CA600-PoE V5.3c.6665_B20180820 was found to contain a command injection vulnerability in the CloudSrvUserdataVersionCheck function via the magicid parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA600-PoE V5.3c.6665_B20180820 contenía una vulnerabilidad de inyección de comandos en la función CloudSrvUserdataVersionCheck mediante el parámetro magicid. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA600-PoE/CloudSrvUserdataVersionCheck_magicid/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
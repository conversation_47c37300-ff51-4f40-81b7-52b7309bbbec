{"cve_id": "CVE-2025-44877", "published_date": "2025-05-02T15:15:49.323", "last_modified_date": "2025-05-27T14:21:50.710", "descriptions": [{"lang": "en", "value": "Tenda AC9 V15.03.06.42_multi was found to contain a command injection vulnerability in the formSetSambaConf function via the usbname parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se detectó que Tenda AC9 V15.03.06.42_multi contenía una vulnerabilidad de inyección de comandos en la función formSetSambaConf mediante el parámetro usbname. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Tenda_AC/AC9_formSetSambaConf", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2022-49916", "published_date": "2025-05-01T15:16:16.820", "last_modified_date": "2025-05-07T13:27:03.267", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nrose: Fix NULL pointer dereference in rose_send_frame()\n\nThe syzkaller reported an issue:\n\nKASAN: null-ptr-deref in range [0x0000000000000380-0x0000000000000387]\nCPU: 0 PID: 4069 Comm: kworker/0:15 Not tainted 6.0.0-s<PERSON><PERSON><PERSON><PERSON>-02734-g0326074ff465 #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 09/22/2022\nWorkqueue: rcu_gp srcu_invoke_callbacks\nRIP: 0010:rose_send_frame+0x1dd/0x2f0 net/rose/rose_link.c:101\nCall Trace:\n <IRQ>\n rose_transmit_clear_request+0x1d5/0x290 net/rose/rose_link.c:255\n rose_rx_call_request+0x4c0/0x1bc0 net/rose/af_rose.c:1009\n rose_loopback_timer+0x19e/0x590 net/rose/rose_loopback.c:111\n call_timer_fn+0x1a0/0x6b0 kernel/time/timer.c:1474\n expire_timers kernel/time/timer.c:1519 [inline]\n __run_timers.part.0+0x674/0xa80 kernel/time/timer.c:1790\n __run_timers kernel/time/timer.c:1768 [inline]\n run_timer_softirq+0xb3/0x1d0 kernel/time/timer.c:1803\n __do_softirq+0x1d0/0x9c8 kernel/softirq.c:571\n [...]\n </IRQ>\n\nIt triggers NULL pointer dereference when 'neigh->dev->dev_addr' is\ncalled in the rose_send_frame(). It's the first occurrence of the\n`neigh` is in rose_loopback_timer() as `rose_loopback_neigh', and\nthe 'dev' in 'rose_loopback_neigh' is initialized sa nullptr.\n\nIt had been fixed by commit 3b3fd068c56e3fbea30090859216a368398e39bf\n(\"rose: Fix Null pointer dereference in rose_send_frame()\") ever.\nBut it's introduced by commit 3c53cd65dece47dd1f9d3a809f32e59d1d87b2b8\n(\"rose: check NULL rose_loopback_neigh->loopback\") again.\n\nWe fix it by add NULL check in rose_transmit_clear_request(). When\nthe 'dev' in 'neigh' is NULL, we don't reply the request and just\nclear it.\n\nsyzkaller don't provide repro, and I provide a syz repro like:\nr0 = syz_init_net_socket$bt_sco(0x1f, 0x5, 0x2)\nioctl$sock_inet_SIOCSIFFLAGS(r0, 0x8914, &(0x7f0000000180)={'rose0\\x00', 0x201})\nr1 = syz_init_net_socket$rose(0xb, 0x5, 0x0)\nbind$rose(r1, &(0x7f00000000c0)=@full={0xb, @dev, @null, 0x0, [@null, @null, @netrom, @netrom, @default, @null]}, 0x40)\nconnect$rose(r1, &(0x7f0000000240)=@short={0xb, @dev={0xbb, 0xbb, 0xbb, 0x1, 0x0}, @remote={0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x1}, 0x1, @netrom={0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x0, 0x0}}, 0x1c)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rose: Se corrige la desreferencia del puntero NULL en rose_send_frame() Syzkaller informó un problema: KASAN: null-ptr-deref en el rango [0x0000000000000380-0x0000000000000387] CPU: 0 PID: 4069 Comm: kworker/0:15 No contaminado 6.0.0-s<PERSON>z<PERSON>ler-02734-g0326074ff465 #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 22/09/2022 Cola de trabajo: rcu_gp srcu_invoke_callbacks RIP: 0010:rose_send_frame+0x1dd/0x2f0 net/rose/rose_link.c:101 Rastreo de llamadas:  rose_transmit_clear_request+0x1d5/0x290 net/rose/rose_link.c:255 rose_rx_call_request+0x4c0/0x1bc0 net/rose/af_rose.c:1009 rose_loopback_timer+0x19e/0x590 net/rose/rose_loopback.c:111 call_timer_fn+0x1a0/0x6b0 kernel/time/timer.c:1474 expire_timers kernel/time/timer.c:1519 [en línea] __run_timers.part.0+0x674/0xa80 kernel/time/timer.c:1790 __run_timers kernel/time/timer.c:1768 [en línea] run_timer_softirq+0xb3/0x1d0 kernel/time/timer.c:1803 __do_softirq+0x1d0/0x9c8 kernel/softirq.c:571 [...]  Activa la desreferencia de puntero nulo cuando se llama a 'neigh-&gt;dev-&gt;dev_addr' en rose_send_frame(). Es la primera vez que `neigh` aparece en rose_loopback_timer() como `rose_loopback_neigh', y `dev' en `rose_loopback_neigh' se inicializa como nullptr. Se corrigió con el commit 3b3fd068c56e3fbea30090859216a368398e39bf (\"rose: Corregir la desreferencia de puntero nulo en rose_send_frame()\"). Pero esto se introduce de nuevo con el commit 3c53cd65dece47dd1f9d3a809f32e59d1d87b2b8 (\"rose: check NULL rose_loopback_neigh-&gt;loopback\"). Lo solucionamos añadiendo una comprobación NULL en rose_transmit_clear_request(). Cuando el valor de \"dev\" en \"neigh\" es NULL, no respondemos a la solicitud y simplemente la borramos. syzkaller no proporciona reproducción, y yo proporciono una reproducción syz como: r0 = syz_init_net_socket$bt_sco(0x1f, 0x5, 0x2) ioctl$sock_inet_SIOCSIFFLAGS(r0, 0x8914, &amp;(0x7f0000000180)={'rose0\\x00', 0x201}) r1 = syz_init_net_socket$rose(0xb, 0x5, 0x0) bind$rose(r1, &amp;(0x7f00000000c0)=@full={0xb, @dev, @null, 0x0, [@null, @null, @netrom, @netrom, @default, @null]}, 0x40) conectar$rosa(r1, &amp;(0x7f0000000240)=@corto={0xb, @dev={0xbb, 0xbb, 0xbb, 0x1, 0x0}, @remoto={0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x1}, 0x1, @netrom={0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x0, 0x0}}, 0x1c)"}], "references": [{"url": "https://git.kernel.org/stable/c/01b9c68c121847d05a4ccef68244dadf82bfa331", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3e2129c67daca21043a26575108f6286c85e71f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5b46adfbee1e429f33b10a88d6c00fa88f3d6c77", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a601e5eded33bb88b8a42743db8fef3ad41dd97e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b13be5e852b03f376058027e462fad4230240891", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bbc03d74e641e824754443b908454ca9e203773e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e97c089d7a49f67027395ddf70bf327eeac2611e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f06186e5271b980bac03f5c97276ed0146ddc9b0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
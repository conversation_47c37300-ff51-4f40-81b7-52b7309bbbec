{"cve_id": "CVE-2023-53039", "published_date": "2025-05-02T16:15:23.017", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nHID: intel-ish-hid: ipc: Fix potential use-after-free in work function\n\nWhen a reset notify IPC message is received, the ISR schedules a work\nfunction and passes the ISHTP device to it via a global pointer\nishtp_dev. If ish_probe() fails, the devm-managed device resources\nincluding ishtp_dev are freed, but the work is not cancelled, causing a\nuse-after-free when the work function tries to access ishtp_dev. Use\ndevm_work_autocancel() instead, so that the work is automatically\ncancelled if probe fails."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: HID: intel-ish-hid: ipc: Se corrige un posible fallo de use after free en la función de trabajo. Cuando se recibe un mensaje de notificación de reinicio de IPC, el ISR programa una función de trabajo y le transfiere el dispositivo ISHTP mediante un puntero global ishtp_dev. Si ish_probe() falla, se liberan los recursos del dispositivo administrados por devm, incluyendo ishtp_dev, pero el trabajo no se cancela, lo que provoca un fallo de use after free cuando la función de trabajo intenta acceder a ishtp_dev. En su lugar, utilice devm_work_autocancel() para que el trabajo se cancele automáticamente si falla la sonda."}], "references": [{"url": "https://git.kernel.org/stable/c/0a594cb490ca6232671fc09e2dc1a0fc7ccbb0b5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ae2f2b0a28416ed2f6d8478ac8b9f7862f36785", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8c1d378b8c224fd50247625255f09fc01dcc5836", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3ce3afd9f791dd1b7daedfcf8c396b60af5dec0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53143", "published_date": "2025-05-02T16:15:33.240", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: fix another off-by-one fsmap error on 1k block filesystems\n\nApparently s<PERSON><PERSON><PERSON> figured out that issuing this FSMAP call:\n\nstruct fsmap_head cmd = {\n\t.fmh_count\t= ...;\n\t.fmh_keys\t= {\n\t\t{ .fmr_device = /* ext4 dev */, .fmr_physical = 0, },\n\t\t{ .fmr_device = /* ext4 dev */, .fmr_physical = 0, },\n\t},\n...\n};\nret = ioctl(fd, FS_IOC_GETFSMAP, &cmd);\n\nProduces this crash if the underlying filesystem is a 1k-block ext4\nfilesystem:\n\nkernel BUG at fs/ext4/ext4.h:3331!\ninvalid opcode: 0000 [#1] PREEMPT SMP\nCPU: 3 PID: 3227965 Comm: xfs_io Tainted: G        W  O       6.2.0-rc8-achx\nHardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.15.0-1 04/01/2014\nRIP: 0010:ext4_mb_load_buddy_gfp+0x47c/0x570 [ext4]\nRSP: 0018:ffffc90007c03998 EFLAGS: 00010246\nRAX: ffff888004978000 RBX: ffffc90007c03a20 RCX: ffff888041618000\nRDX: 0000000000000000 RSI: 00000000000005a4 RDI: ffffffffa0c99b11\nRBP: ffff888012330000 R08: ffffffffa0c2b7d0 R09: 0000000000000400\nR10: ffffc90007c03950 R11: 0000000000000000 R12: 0000000000000001\nR13: 00000000ffffffff R14: 0000000000000c40 R15: ffff88802678c398\nFS:  00007fdf2020c880(0000) GS:ffff88807e100000(0000) knlGS:0000000000000000\nCS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 00007ffd318a5fe8 CR3: 000000007f80f001 CR4: 00000000001706e0\nCall Trace:\n <TASK>\n ext4_mballoc_query_range+0x4b/0x210 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80]\n ext4_getfsmap_datadev+0x713/0x890 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80]\n ext4_getfsmap+0x2b7/0x330 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80]\n ext4_ioc_getfsmap+0x153/0x2b0 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80]\n __ext4_ioctl+0x2a7/0x17e0 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80]\n __x64_sys_ioctl+0x82/0xa0\n do_syscall_64+0x2b/0x80\n entry_SYSCALL_64_after_hwframe+0x46/0xb0\nRIP: 0033:0x7fdf20558aff\nRSP: 002b:00007ffd318a9e30 EFLAGS: 00000246 ORIG_RAX: 0000000000000010\nRAX: ffffffffffffffda RBX: 00000000000200c0 RCX: 00007fdf20558aff\nRDX: 00007fdf1feb2010 RSI: 00000000c0c0583b RDI: 0000000000000003\nRBP: 00005625c0634be0 R08: 00005625c0634c40 R09: 0000000000000001\nR10: 0000000000000000 R11: 0000000000000246 R12: 00007fdf1feb2010\nR13: 00005625be70d994 R14: 0000000000000800 R15: 0000000000000000\n\nFor GETFSMAP calls, the caller selects a physical block device by\nwriting its block number into fsmap_head.fmh_keys[01].fmr_device.\nTo query mappings for a subrange of the device, the starting byte of the\nrange is written to fsmap_head.fmh_keys[0].fmr_physical and the last\nbyte of the range goes in fsmap_head.fmh_keys[1].fmr_physical.\n\nIOWs, to query what mappings overlap with bytes 3-14 of /dev/sda, you'd\nset the inputs as follows:\n\n\tfmh_keys[0] = { .fmr_device = major(8, 0), .fmr_physical = 3},\n\tfmh_keys[1] = { .fmr_device = major(8, 0), .fmr_physical = 14},\n\nWhich would return you whatever is mapped in the 12 bytes starting at\nphysical offset 3.\n\nThe crash is due to insufficient range validation of keys[1] in\next4_getfsmap_datadev.  On 1k-block filesystems, block 0 is not part of\nthe filesystem, which means that s_first_data_block is nonzero.\next4_get_group_no_and_offset subtracts this quantity from the blocknr\nargument before cracking it into a group number and a block number\nwithin a group.  IOWs, block group 0 spans blocks 1-8192 (1-based)\ninstead of 0-8191 (0-based) like what happens with larger blocksizes.\n\nThe net result of this encoding is that blocknr < s_first_data_block is\nnot a valid input to this function.  The end_fsb variable is set from\nthe keys that are copied from userspace, which means that in the above\nexample, its value is zero.  That leads to an underflow here:\n\n\tblocknr = blocknr - le32_to_cpu(es->s_first_data_block);\n\nThe division then operates on -1:\n\n\toffset = do_div(blocknr, EXT4_BLOCKS_PER_GROUP(sb)) >>\n\t\tEXT4_SB(sb)->s_cluster_bits;\n\nLeaving an impossibly large group number (2^32-1) in blocknr.\next4_getfsmap_check_keys checked that keys[0\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: corrige otro error de fsmap de un bloque en sistemas de archivos de 1k Aparentemente, syzbot descubrió que emitir esta llamada FSMAP: Produce este fallo si el sistema de archivos subyacente es un sistema de archivos ext4 de 1k bloques: ¡ERROR del kernel en fs/ext4/ext4.h:3331! Código de operación no válido: 0000 [#1] PREEMPT SMP CPU: 3 PID: 3227965 Comm: xfs_io Contaminado: GWO 6.2.0-rc8-achx Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.15.0-1 01/04/2014 RIP: 0010:ext4_mb_load_buddy_gfp+0x47c/0x570 [ext4] RSP: 0018:ffffc90007c03998 EFLAGS: 00010246 RAX: ffff888004978000 RBX: ffffc90007c03a20 RCX: ffff888041618000 RDX: 0000000000000000 RSI: 00000000000005a4 RDI: ffffffffa0c99b11 RBP: ffff888012330000 R08: ffffffffa0c2b7d0 R09: 0000000000000400 R10: ffffc90007c03950 R11: 0000000000000000 R12: 000000000000001 R13: 00000000ffffffff R14: 0000000000000c40 R15: ffff88802678c398 FS: 00007fdf2020c880(0000) GS:ffff88807e100000(0000) knlGS:0000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 00007ffd318a5fe8 CR3: 000000007f80f001 CR4: 00000000001706e0 Rastreo de llamadas:  ext4_mballoc_query_range+0x4b/0x210 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80] ext4_getfsmap_datadev+0x713/0x890 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80] ext4_getfsmap+0x2b7/0x330 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80] ext4_ioc_getfsmap+0x153/0x2b0 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80] __ext4_ioctl+0x2a7/0x17e0 [ext4 dfa189daddffe8fecd3cdfd00564e0f265a8ab80] __x64_sys_ioctl+0x82/0xa0 hacer_llamada_al_sistema_64+0x2b/0x80 entrada_LLAMADA_AL_SISTEMA_64_después_de_hwframe+0x46/0xb0 RIP: 0033:0x7fdf20558aff RSP: 002b:00007ffd318a9e30 EFLAGS: 00000246 ORIG_RAX: 0000000000000010 RAX: ffffffffffffffda RBX: 00000000000200c0 RCX: 00007fdf20558aff RDX: 00007fdf1feb2010 RSI: 00000000c0c0583b RDI: 0000000000000003 RBP: 00005625c0634be0 R08: 00005625c0634c40 R09: 0000000000000001 R10: 0000000000000000 R11: 0000000000000246 R12: 00007fdf1feb2010 R13: 00005625be70d994 R14: 000000000000800 R15: 000000000000000 Para las llamadas GETFSMAP, el llamador selecciona un dispositivo de bloque físico escribiendo su número de bloque en fsmap_head.fmh_keys[01].fmr_device. Para consultar las asignaciones de un subrango del dispositivo, el byte inicial del rango se escribe en fsmap_head.fmh_keys[0].fmr_physical y el último byte en fsmap_head.fmh_keys[1].fmr_physical. IOWs, para consultar qué asignaciones se superponen con los bytes 3-14 de /dev/sda, debe configurar las entradas de la siguiente manera: fmh_keys[0] = { .fmr_device = major(8, 0), .fmr_physical = 3}, fmh_keys[1] = { .fmr_device = major(8, 0), .fmr_physical = 14}, lo que le devolvería lo que esté asignado en los 12 bytes a partir del desplazamiento físico 3. El fallo se debe a una validación de rango insuficiente de keys[1] en ext4_getfsmap_datadev. En sistemas de archivos de 1k bloques, el bloque 0 no forma parte del sistema de archivos, lo que significa que s_first_data_block es distinto de cero. ext4_get_group_no_and_offset resta esta cantidad del argumento blocknr antes de descomponerlo en un número de grupo y un número de bloque dentro de un grupo. En las IOW, el grupo de bloques 0 abarca los bloques 1-8192 (basado en 1) en lugar de 0-8191 (basado en 0), como ocurre con tamaños de bloque mayores. El resultado final de esta codificación es que blocknr &lt; s_first_data_block no es una entrada válida para esta función. La variable end_fsb se establece a partir de las claves copiadas del espacio de usuario, lo que significa que, en el ejemplo anterior, su valor es cero. Esto genera un desbordamiento por debajo de la capacidad: blocknr = blocknr - le32_to_cpu(es-&gt;s_first_data_block); La división opera entonces sobre -1: offset = do_div(blocknr, EXT4_BLOCKS_PER_GROUP(sb)) &gt;&gt; EXT4_SB(sb)-&gt;s_cluster_bits; De esta manera, se deja un número de grupo imposiblemente grande (2^32-1) en blocknr. ext4_getfsmap_check_keys verificó que keys[0 ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/15ebade3266b300da9cd1edce4004fe8fd6a2b88", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1d2366624b4c19a2ba6baf67fe57f4a1b0f67c05", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a70b49dc7eee5dbe3775a650ce598e3557ff5475", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c24f838493792b5e78a3596b4ca96375aa0af4c2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c5d7c31e17224d847a330180ec1b03bf390632b2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c993799baf9c5861f8df91beb80e1611b12efcbd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb3a695aa71a514f2e7f5778e05faba3733b70a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f16054ac1774915160ca4e1c73ff7a269465a1b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
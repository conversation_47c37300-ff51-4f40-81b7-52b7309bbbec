{"cve_id": "CVE-2022-49929", "published_date": "2025-05-01T15:16:18.887", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRDMA/rxe: Fix mr leak in RESPST_ERR_RNR\n\nrxe_recheck_mr() will increase mr's ref_cnt, so we should call rxe_put(mr)\nto drop mr's ref_cnt in RESPST_ERR_RNR to avoid below warning:\n\n  WARNING: CPU: 0 PID: 4156 at drivers/infiniband/sw/rxe/rxe_pool.c:259 __rxe_cleanup+0x1df/0x240 [rdma_rxe]\n...\n  Call Trace:\n   rxe_dereg_mr+0x4c/0x60 [rdma_rxe]\n   ib_dereg_mr_user+0xa8/0x200 [ib_core]\n   ib_mr_pool_destroy+0x77/0xb0 [ib_core]\n   nvme_rdma_destroy_queue_ib+0x89/0x240 [nvme_rdma]\n   nvme_rdma_free_queue+0x40/0x50 [nvme_rdma]\n   nvme_rdma_teardown_io_queues.part.0+0xc3/0x120 [nvme_rdma]\n   nvme_rdma_error_recovery_work+0x4d/0xf0 [nvme_rdma]\n   process_one_work+0x582/0xa40\n   ? pwq_dec_nr_in_flight+0x100/0x100\n   ? rwlock_bug.part.0+0x60/0x60\n   worker_thread+0x2a9/0x700\n   ? process_one_work+0xa40/0xa40\n   kthread+0x168/0x1a0\n   ? kthread_complete_and_exit+0x20/0x20\n   ret_from_fork+0x22/0x30"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: RDMA/rxe: Reparar fuga de mr en RESPST_ERR_RNR rxe_recheck_mr() aumentará el ref_cnt de mr, por lo que debemos llamar a rxe_put(mr) para eliminar el ref_cnt de mr en RESPST_ERR_RNR para evitar la siguiente advertencia: ADVERTENCIA: CPU: 0 PID: 4156 en drivers/infiniband/sw/rxe/rxe_pool.c:259 __rxe_cleanup+0x1df/0x240 [rdma_rxe] ... Rastreo de llamadas:  rxe_dereg_mr+0x4c/0x60 [rdma_rxe] ib_dereg_mr_user+0xa8/0x200 [ib_core] ib_mr_pool_destroy+0x77/0xb0 [ib_core] nvme_rdma_destroy_queue_ib+0x89/0x240 [nvme_rdma] nvme_rdma_free_queue+0x40/0x50 [nvme_rdma] nvme_rdma_teardown_io_queues.part.0+0xc3/0x120 [nvme_rdma] nvme_rdma_error_recovery_work+0x4d/0xf0 [nvme_rdma] process_one_work+0x582/0xa40 ? pwq_dec_nr_in_flight+0x100/0x100 ? rwlock_bug.part.0+0x60/0x60 worker_thread+0x2a9/0x700 ? process_one_work+0xa40/0xa40 kthread+0x168/0x1a0 ? kthread_complete_and_exit+0x20/0x20 ret_from_fork+0x22/0x30 "}], "references": [{"url": "https://git.kernel.org/stable/c/50b35ad2864a9d66f802f9ce193d99bbef64e219", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b5f9a01fae42684648c2ee3cd9985f80c67ab9f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
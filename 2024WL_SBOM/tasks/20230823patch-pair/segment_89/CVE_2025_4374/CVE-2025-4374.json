{"cve_id": "CVE-2025-4374", "published_date": "2025-05-06T15:16:05.463", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "A flaw was found in Quay. When an organization acts as a proxy cache, and a user or robot pulls an image that hasn't been mirrored yet, they are granted \"Admin\" permissions on the newly created repository."}, {"lang": "es", "value": "Se detectó una falla en Quay. Cuando una organización actúa como caché proxy y un usuario o robot extrae una imagen que aún no se ha replicado, se le otorgan permisos de administrador en el repositorio recién creado."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2025-4374", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2364267", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2023-53125", "published_date": "2025-05-02T16:15:31.547", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: usb: smsc75xx: Limit packet length to skb->len\n\nPacket length retrieved from skb data may be larger than\nthe actual socket buffer length (up to 9026 bytes). In such\ncase the cloned skb passed up the network stack will leak\nkernel memory contents."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: usb: smsc75xx: Limitar la longitud del paquete a skb-&gt;len. La longitud del paquete recuperada de los datos de skb puede ser mayor que la longitud real del búfer del socket (hasta 9026 bytes). En tal caso, el skb clonado que se pasa a la pila de red filtrará el contenido de la memoria del kernel."}], "references": [{"url": "https://git.kernel.org/stable/c/105db6574281e1e03fcbf87983f4fee111682306", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4a4de0a68b18485c68ab4f0cfa665b1633c6d277", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/53966d572d056d6b234cfe76a5f9d60049d3c178", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ee5df9c039e37b9d8eb5e3de08bfb7f53d31cb6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9fabdd79051a9fe51388df099aff6e4b660fedd2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c7bdc137ca163b90917c1eeba4f1937684bd4f8b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d8b228318935044dafe3a5bc07ee71a1f1424b8d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e294f0aa47e4844f3d3c8766c02accd5a76a7d4e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
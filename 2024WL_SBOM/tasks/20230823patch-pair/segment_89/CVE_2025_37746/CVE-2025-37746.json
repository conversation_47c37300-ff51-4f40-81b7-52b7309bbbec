{"cve_id": "CVE-2025-37746", "published_date": "2025-05-01T13:15:53.313", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nperf/dwc_pcie: fix duplicate pci_dev devices\n\nDuring platform_device_register, wrongly using struct device\npci_dev as platform_data caused a kmemdup copy of pci_dev. Worse\nstill, accessing the duplicated device leads to list corruption as its\nmutex content (e.g., list, magic) remains the same as the original."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: perf/dwc_pcie: se corrigen dispositivos pci_dev duplicados. Durante platform_device_register, el uso incorrecto de struct device pci_dev como platform_data provocaba una copia kmemdup de pci_dev. Peor aún, acceder al dispositivo duplicado provoca la corrupción de la lista, ya que su contenido mutex (p. ej., list, magic) permanece igual que el original."}], "references": [{"url": "https://git.kernel.org/stable/c/7f35b429802a8065aa61e2a3f567089649f4d98e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a71c6fc87b2b9905dc2e38887fe4122287216be9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
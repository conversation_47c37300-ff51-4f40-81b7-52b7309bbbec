{"cve_id": "CVE-2023-53064", "published_date": "2025-05-02T16:15:25.480", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niavf: fix hang on reboot with ice\n\nWhen a system with E810 with existing VFs gets rebooted the following\nhang may be observed.\n\n Pid 1 is hung in iavf_remove(), part of a network driver:\n PID: 1        TASK: ffff965400e5a340  CPU: 24   COMMAND: \"systemd-shutdow\"\n  #0 [ffffaad04005fa50] __schedule at ffffffff8b3239cb\n  #1 [ffffaad04005fae8] schedule at ffffffff8b323e2d\n  #2 [ffffaad04005fb00] schedule_hrtimeout_range_clock at ffffffff8b32cebc\n  #3 [ffffaad04005fb80] usleep_range_state at ffffffff8b32c930\n  #4 [ffffaad04005fbb0] iavf_remove at ffffffffc12b9b4c [iavf]\n  #5 [ffffaad04005fbf0] pci_device_remove at ffffffff8add7513\n  #6 [ffffaad04005fc10] device_release_driver_internal at ffffffff8af08baa\n  #7 [ffffaad04005fc40] pci_stop_bus_device at ffffffff8adcc5fc\n  #8 [ffffaad04005fc60] pci_stop_and_remove_bus_device at ffffffff8adcc81e\n  #9 [ffffaad04005fc70] pci_iov_remove_virtfn at ffffffff8adf9429\n #10 [ffffaad04005fca8] sriov_disable at ffffffff8adf98e4\n #11 [ffffaad04005fcc8] ice_free_vfs at ffffffffc04bb2c8 [ice]\n #12 [ffffaad04005fd10] ice_remove at ffffffffc04778fe [ice]\n #13 [ffffaad04005fd38] ice_shutdown at ffffffffc0477946 [ice]\n #14 [ffffaad04005fd50] pci_device_shutdown at ffffffff8add58f1\n #15 [ffffaad04005fd70] device_shutdown at ffffffff8af05386\n #16 [ffffaad04005fd98] kernel_restart at ffffffff8a92a870\n #17 [ffffaad04005fda8] __do_sys_reboot at ffffffff8a92abd6\n #18 [ffffaad04005fee0] do_syscall_64 at ffffffff8b317159\n #19 [ffffaad04005ff08] __context_tracking_enter at ffffffff8b31b6fc\n #20 [ffffaad04005ff18] syscall_exit_to_user_mode at ffffffff8b31b50d\n #21 [ffffaad04005ff28] do_syscall_64 at ffffffff8b317169\n #22 [ffffaad04005ff50] entry_SYSCALL_64_after_hwframe at ffffffff8b40009b\n     RIP: 00007f1baa5c13d7  RSP: 00007fffbcc55a98  RFLAGS: 00000202\n     RAX: ffffffffffffffda  RBX: 0000000000000000  RCX: 00007f1baa5c13d7\n     RDX: 0000000001234567  RSI: 0000000028121969  RDI: 00000000fee1dead\n     RBP: 00007fffbcc55ca0   R8: 0000000000000000   R9: 00007fffbcc54e90\n     R10: 00007fffbcc55050  R11: 0000000000000202  R12: 0000000000000005\n     R13: 0000000000000000  R14: 00007fffbcc55af0  R15: 0000000000000000\n     ORIG_RAX: 00000000000000a9  CS: 0033  SS: 002b\n\nDuring reboot all drivers PM shutdown callbacks are invoked.\nIn iavf_shutdown() the adapter state is changed to __IAVF_REMOVE.\nIn ice_shutdown() the call chain above is executed, which at some point\ncalls iavf_remove(). However iavf_remove() expects the VF to be in one\nof the states __IAVF_RUNNING, __IAVF_DOWN or __IAVF_INIT_FAILED. If\nthat's not the case it sleeps forever.\nSo if iavf_shutdown() gets invoked before iavf_remove() the system will\nhang indefinitely because the adapter is already in state __IAVF_REMOVE.\n\nFix this by returning from iavf_remove() if the state is __IAVF_REMOVE,\nas we already went through iavf_shutdown()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iavf: se corrige el bloqueo al reiniciar con hielo Cuando se reinicia un sistema con E810 con VF existentes, se puede observar el siguiente bloqueo. El Pid 1 está colgado en iavf_remove(), parte de un controlador de red: PID: 1 TAREA: ffff965400e5a340 CPU: 24 COMANDO: \"systemd-shutdow\" #0 [ffffaad04005fa50] __schedule en ffffffff8b3239cb #1 [ffffaad04005fae8] schedule en ffffffff8b323e2d #2 [ffffaad04005fb00] schedule_hrtimeout_range_clock en ffffffff8b32cebc #3 [ffffaad04005fb80] usleep_range_state en ffffffff8b32c930 #4 [ffffaad04005fbb0] iavf_remove en ffffffffc12b9b4c [iavf] #5 [ffffaad04005fbf0] pci_device_remove en ffffffff8add7513 #6 [ffffaad04005fc10] device_release_driver_internal en ffffffff8af08baa #7 [ffffaad04005fc40] pci_stop_bus_device en ffffffff8adcc5fc #8 [ffffaad04005fc60] pci_stop_and_remove_bus_device en ffffffff8adcc81e #9 [ffffaad04005fc70] pci_iov_remove_virtfn en ffffffff8adf9429 #10 [ffffaad04005fca8] sriov_disable en ffffffff8adf98e4 #11 [ffffaad04005fcc8] ice_free_vfs en ffffffffc04bb2c8 [ice] #12 [ffffaad04005fd10] ice_remove en ffffffffc04778fe [ice] #13 [ffffaad04005fd38] ice_shutdown en ffffffffc0477946 [ice] #14 [ffffaad04005fd50] pci_device_shutdown en ffffffff8add58f1 #15 [ffffaad04005fd70] device_shutdown en ffffffff8af05386 #16 [ffffaad04005fd98] kernel_restart en ffffffff8a92a870 #17 [ffffaad04005fda8] __do_sys_reboot en ffffffff8a92abd6 #18 [ffffaad04005fee0] do_syscall_64 en ffffffff8b317159 #19 [ffffaad04005ff08] __context_tracking_enter en ffffffff8b31b6fc #20 [ffffaad04005ff18] syscall_exit_to_user_mode en ffffffff8b31b50d #21 [ffffaad04005ff28] do_syscall_64 en ffffffff8b317169 #22 [ffffaad04005ff50] entry_SYSCALL_64_after_hwframe en ffffffff8b40009b RIP: 00007f1baa5c13d7 RSP: 00007fffbcc55a98 RFLAGS: 00000202 RAX: ffffffffffffffda RBX: 0000000000000000 RCX: 00007f1baa5c13d7 RDX: 0000000001234567 RSI: 0000000028121969 RDI: 00000000fee1dead RBP: 00007fffbcc55ca0 R8: 000000000000000 R9: 00007fffbcc54e90 R10: 00007fffbcc55050 R11: 00000000000000202 R12: 0000000000000005 R13: 0000000000000000 R14: 00007fffbcc55af0 R15: 0000000000000000 ORIG_RAX: 00000000000000a9 CS: 0033 SS: 002b Durante el reinicio, se invocan las devoluciones de llamada de apagado de PM de todos los controladores. En iavf_shutdown(), el estado del adaptador cambia a __IAVF_REMOVE. En ice_shutdown() se ejecuta la cadena de llamadas anterior, que en algún momento llama a iavf_remove(). Sin embargo, iavf_remove() espera que el VF esté en uno de los estados __IAVF_RUNNING, __IAVF_DOWN o __IAVF_INIT_FAILED. De lo contrario, se suspende indefinidamente. Por lo tanto, si se invoca iavf_shutdown() antes que iavf_remove(), el sistema se bloqueará indefinidamente porque el adaptador ya está en el estado __IAVF_REMOVE. Para solucionar esto, regrese de iavf_remove() si el estado es __IAVF_REMOVE, como ya se explicó con iavf_shutdown()."}], "references": [{"url": "https://git.kernel.org/stable/c/4e264be98b88a6d6f476c11087fe865696e8bef5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/502b898235f06130750c91512c86dd0e9efe28e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7a29799fc141ba9e6cf921fc8e958e3398ad1a4f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f752ace58867de3c063512b21e0f1694fc27f043", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-23150", "published_date": "2025-05-01T13:15:50.893", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: fix off-by-one error in do_split\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> detected a use-after-free issue in ext4_insert_dentry that was\ncaused by out-of-bounds access due to incorrect splitting in do_split.\n\nBUG: KASAN: use-after-free in ext4_insert_dentry+0x36a/0x6d0 fs/ext4/namei.c:2109\nWrite of size 251 at addr ffff888074572f14 by task syz-executor335/5847\n\nCPU: 0 UID: 0 PID: 5847 Comm: syz-executor335 Not tainted 6.12.0-rc6-syzkaller-00318-ga9cda7c0ffed #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 10/30/2024\nCall Trace:\n <TASK>\n __dump_stack lib/dump_stack.c:94 [inline]\n dump_stack_lvl+0x241/0x360 lib/dump_stack.c:120\n print_address_description mm/kasan/report.c:377 [inline]\n print_report+0x169/0x550 mm/kasan/report.c:488\n kasan_report+0x143/0x180 mm/kasan/report.c:601\n kasan_check_range+0x282/0x290 mm/kasan/generic.c:189\n __asan_memcpy+0x40/0x70 mm/kasan/shadow.c:106\n ext4_insert_dentry+0x36a/0x6d0 fs/ext4/namei.c:2109\n add_dirent_to_buf+0x3d9/0x750 fs/ext4/namei.c:2154\n make_indexed_dir+0xf98/0x1600 fs/ext4/namei.c:2351\n ext4_add_entry+0x222a/0x25d0 fs/ext4/namei.c:2455\n ext4_add_nondir+0x8d/0x290 fs/ext4/namei.c:2796\n ext4_symlink+0x920/0xb50 fs/ext4/namei.c:3431\n vfs_symlink+0x137/0x2e0 fs/namei.c:4615\n do_symlinkat+0x222/0x3a0 fs/namei.c:4641\n __do_sys_symlink fs/namei.c:4662 [inline]\n __se_sys_symlink fs/namei.c:4660 [inline]\n __x64_sys_symlink+0x7a/0x90 fs/namei.c:4660\n do_syscall_x64 arch/x86/entry/common.c:52 [inline]\n do_syscall_64+0xf3/0x230 arch/x86/entry/common.c:83\n entry_SYSCALL_64_after_hwframe+0x77/0x7f\n </TASK>\n\nThe following loop is located right above 'if' statement.\n\nfor (i = count-1; i >= 0; i--) {\n\t/* is more than half of this entry in 2nd half of the block? */\n\tif (size + map[i].size/2 > blocksize/2)\n\t\tbreak;\n\tsize += map[i].size;\n\tmove++;\n}\n\n'i' in this case could go down to -1, in which case sum of active entries\nwouldn't exceed half the block size, but previous behaviour would also do\nsplit in half if sum would exceed at the very last block, which in case of\nhaving too many long name files in a single block could lead to\nout-of-bounds access and following use-after-free.\n\nFound by Linux Verification Center (linuxtesting.org) with Syzkaller."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: se corrige el error de uno en uno en do_split Syzkaller detectó un problema de use-after-free en ext4_insert_dentry que fue causado por un acceso fuera de los límites debido a una división incorrecta en do_split. ERROR: KASAN: use-after-free en ext4_insert_dentry+0x36a/0x6d0 fs/ext4/namei.c:2109 Escritura de tamaño 251 en la dirección ffff888074572f14 por la tarea syz-executor335/5847 CPU: 0 UID: 0 PID: 5847 Comm: syz-executor335 No contaminado 6.12.0-rc6-syzkaller-00318-ga9cda7c0ffed #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 30/10/2024 Rastreo de llamadas:   __dump_stack lib/dump_stack.c:94 [inline] dump_stack_lvl+0x241/0x360 lib/dump_stack.c:120 print_address_description mm/kasan/report.c:377 [inline] print_report+0x169/0x550 mm/kasan/report.c:488 kasan_report+0x143/0x180 mm/kasan/report.c:601 kasan_check_range+0x282/0x290 mm/kasan/generic.c:189 __asan_memcpy+0x40/0x70 mm/kasan/shadow.c:106 ext4_insert_dentry+0x36a/0x6d0 fs/ext4/namei.c:2109 add_dirent_to_buf+0x3d9/0x750 fs/ext4/namei.c:2154 make_indexed_dir+0xf98/0x1600 fs/ext4/namei.c:2351 ext4_add_entry+0x222a/0x25d0 fs/ext4/namei.c:2455 ext4_add_nondir+0x8d/0x290 fs/ext4/namei.c:2796 ext4_symlink+0x920/0xb50 fs/ext4/namei.c:3431 vfs_symlink+0x137/0x2e0 fs/namei.c:4615 do_symlinkat+0x222/0x3a0 fs/namei.c:4641 __do_sys_symlink fs/namei.c:4662 [inline] __se_sys_symlink fs/namei.c:4660 [inline] __x64_sys_symlink+0x7a/0x90 fs/namei.c:4660 do_syscall_x64 arch/x86/entry/common.c:52 [inline] do_syscall_64+0xf3/0x230 arch/x86/entry/common.c:83 entry_SYSCALL_64_after_hwframe+0x77/0x7f  El siguiente bucle se encuentra justo encima de la declaración 'if'. for (i = count-1; i &gt;= 0; i--) { /* ¿hay más de la mitad de esta entrada en la 2da mitad del bloque? */ if (size + map[i].size/2 &gt; blocksize/2) break; size += map[i].size; move++; } En este caso, la 'i' podría bajar a -1, en cuyo caso la suma de las entradas activas no superaría la mitad del tamaño del bloque. Sin embargo, el comportamiento anterior también se dividiría por la mitad si la suma superara el tamaño del último bloque. Esto, al tener demasiados archivos con nombres largos en un solo bloque, podría provocar un acceso fuera de los límites y el consiguiente uso después de la liberación. Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con Syzkaller."}], "references": [{"url": "https://git.kernel.org/stable/c/16d9067f00e3a7d1df7c3aa9c20d214923d27e10", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/17df39f455f1289319d4d09e4826aa46852ffd17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2883e9e74f73f9265e5f8d1aaaa89034b308e433", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2eeb1085bf7bd5c7ba796ca4119925fa5d336a3f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/35d0aa6db9d93307085871ceab8a729594a98162", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/515c34cff899eb5dae6aa7eee01c1295b07d81af", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/94824ac9a8aaf2fb3c54b4bdde842db80ffa555d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab0cc5c25552ae0d20eae94b40a93be11b080fc5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b96bd2c3db26ad0daec5b78c85c098b53900e2e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
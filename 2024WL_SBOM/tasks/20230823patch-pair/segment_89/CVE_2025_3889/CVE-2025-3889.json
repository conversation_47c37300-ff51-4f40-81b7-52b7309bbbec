{"cve_id": "CVE-2025-3889", "published_date": "2025-05-01T12:15:17.630", "last_modified_date": "2025-05-06T15:39:43.323", "descriptions": [{"lang": "en", "value": "The WordPress Simple Shopping Cart plugin for WordPress is vulnerable to Insecure Direct Object Reference in all versions up to, and including, 5.1.3 via the 'process_payment_data' due to missing validation on a user controlled key. This makes it possible for unauthenticated attackers to change the quantity of a product to a negative number, which subtracts the product cost from the total order cost. The attack will only work with Manual Checkout mode, as PayPal and Stripe will not process payments for a negative quantity."}, {"lang": "es", "value": "El complemento Simple Shopping Cart para WordPress es vulnerable a una Referencia Directa a Objetos Insegura en todas las versiones hasta la 5.1.3 incluida, a través de 'process_payment_data', debido a la falta de validación en una clave controlada por el usuario. Esto permite a atacantes no autenticados cambiar la cantidad de un producto a un número negativo, lo que resta el coste del producto del coste total del pedido. El ataque solo funciona con el modo de pago manual, ya que PayPal y Stripe no procesan pagos por cantidades negativas."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/wp_shopping_cart.php#L324", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3284572/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.tipsandtricks-hq.com/ecommerce/simple-shopping-cart-enabling-manual-offline-checkout", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.tipsandtricks-hq.com/ecommerce/wp-shopping-cart", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/41212533-535e-4a9e-a9b8-1240021a3752?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
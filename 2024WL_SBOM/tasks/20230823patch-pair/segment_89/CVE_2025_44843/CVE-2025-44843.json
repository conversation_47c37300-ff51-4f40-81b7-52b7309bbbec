{"cve_id": "CVE-2025-44843", "published_date": "2025-05-01T17:15:50.583", "last_modified_date": "2025-05-22T15:31:09.980", "descriptions": [{"lang": "en", "value": "TOTOLINK CA600-PoE V5.3c.6665_B20180820 was found to contain a command injection vulnerability in the CloudSrvUserdataVersionCheck function via the url parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CA600-PoE V5.3c.6665_B20180820 contenía una vulnerabilidad de inyección de comandos en la función CloudSrvUserdataVersionCheck mediante el parámetro url. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Totolink_CA600-PoE/CloudSrvUserdataVersionCheck_url/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.totolink.net", "source": "<EMAIL>", "tags": ["Product"]}]}
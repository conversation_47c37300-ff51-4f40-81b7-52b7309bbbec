{"cve_id": "CVE-2022-49887", "published_date": "2025-05-01T15:16:13.690", "last_modified_date": "2025-05-07T13:20:20.687", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: meson: vdec: fix possible refcount leak in vdec_probe()\n\nv4l2_device_unregister need to be called to put the refcount got by\nv4l2_device_register when vdec_probe fails or vdec_remove is called."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: meson: vdec: corrige posible pérdida de recuento de referencias en vdec_probe(). Se debe llamar a v4l2_device_unregister para colocar el recuento de referencias obtenido por v4l2_device_register cuando vdec_probe falla o se llama a vdec_remove."}], "references": [{"url": "https://git.kernel.org/stable/c/0457e7b12ece1a7e41fa0ae8b7e47c0a72a83bef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/70119756311a0be3b95bec2e1ba714673e90feba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7718999356234d9cc6a11b4641bb773928f1390f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/be6e22f54623d8a856a4f167b25be73c2ff1ff80", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f96ad391d054bd5c36994f98afd6a12cbb5600bf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-24341", "published_date": "2025-04-30T12:15:15.493", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "A vulnerability in the web application of ctrlX OS allows a remote authenticated (low-privileged) attacker to induce a Denial-of-Service (DoS) condition on the device via multiple crafted HTTP requests. In the worst case, a full power cycle is needed to regain control of the device."}, {"lang": "es", "value": "Una vulnerabilidad en la aplicación web de ctrlX OS permite a un atacante remoto autenticado (con privilegios bajos) inducir una denegación de servicio (DoS) en el dispositivo mediante múltiples solicitudes HTTP manipuladas. En el peor de los casos, se requiere un reinicio completo para recuperar el control del dispositivo."}], "references": [{"url": "https://psirt.bosch.com/security-advisories/BOSCH-SA-640452.html", "source": "<EMAIL>", "tags": []}]}
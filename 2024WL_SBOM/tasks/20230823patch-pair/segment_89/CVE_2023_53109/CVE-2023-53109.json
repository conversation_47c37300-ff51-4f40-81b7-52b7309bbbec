{"cve_id": "CVE-2023-53109", "published_date": "2025-05-02T16:15:29.823", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: tunnels: annotate lockless accesses to dev->needed_headroom\n\nIP tunnels can apparently update dev->needed_headroom\nin their xmit path.\n\nThis patch takes care of three tunnels xmit, and also the\ncore LL_RESERVED_SPACE() and LL_RESERVED_SPACE_EXTRA()\nhelpers.\n\nMore changes might be needed for completeness.\n\nBUG: KCSAN: data-race in ip_tunnel_xmit / ip_tunnel_xmit\n\nread to 0xffff88815b9da0ec of 2 bytes by task 888 on cpu 1:\nip_tunnel_xmit+0x1270/0x1730 net/ipv4/ip_tunnel.c:803\n__gre_xmit net/ipv4/ip_gre.c:469 [inline]\nipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661\n__netdev_start_xmit include/linux/netdevice.h:4881 [inline]\nnetdev_start_xmit include/linux/netdevice.h:4895 [inline]\nxmit_one net/core/dev.c:3580 [inline]\ndev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596\n__dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246\ndev_queue_xmit include/linux/netdevice.h:3051 [inline]\nneigh_direct_output+0x17/0x20 net/core/neighbour.c:1623\nneigh_output include/net/neighbour.h:546 [inline]\nip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228\nip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316\nNF_HOOK_COND include/linux/netfilter.h:291 [inline]\nip_output+0xe5/0x1b0 net/ipv4/ip_output.c:430\ndst_output include/net/dst.h:444 [inline]\nip_local_out+0x64/0x80 net/ipv4/ip_output.c:126\niptunnel_xmit+0x34a/0x4b0 net/ipv4/ip_tunnel_core.c:82\nip_tunnel_xmit+0x1451/0x1730 net/ipv4/ip_tunnel.c:813\n__gre_xmit net/ipv4/ip_gre.c:469 [inline]\nipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661\n__netdev_start_xmit include/linux/netdevice.h:4881 [inline]\nnetdev_start_xmit include/linux/netdevice.h:4895 [inline]\nxmit_one net/core/dev.c:3580 [inline]\ndev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596\n__dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246\ndev_queue_xmit include/linux/netdevice.h:3051 [inline]\nneigh_direct_output+0x17/0x20 net/core/neighbour.c:1623\nneigh_output include/net/neighbour.h:546 [inline]\nip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228\nip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316\nNF_HOOK_COND include/linux/netfilter.h:291 [inline]\nip_output+0xe5/0x1b0 net/ipv4/ip_output.c:430\ndst_output include/net/dst.h:444 [inline]\nip_local_out+0x64/0x80 net/ipv4/ip_output.c:126\niptunnel_xmit+0x34a/0x4b0 net/ipv4/ip_tunnel_core.c:82\nip_tunnel_xmit+0x1451/0x1730 net/ipv4/ip_tunnel.c:813\n__gre_xmit net/ipv4/ip_gre.c:469 [inline]\nipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661\n__netdev_start_xmit include/linux/netdevice.h:4881 [inline]\nnetdev_start_xmit include/linux/netdevice.h:4895 [inline]\nxmit_one net/core/dev.c:3580 [inline]\ndev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596\n__dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246\ndev_queue_xmit include/linux/netdevice.h:3051 [inline]\nneigh_direct_output+0x17/0x20 net/core/neighbour.c:1623\nneigh_output include/net/neighbour.h:546 [inline]\nip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228\nip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316\nNF_HOOK_COND include/linux/netfilter.h:291 [inline]\nip_output+0xe5/0x1b0 net/ipv4/ip_output.c:430\ndst_output include/net/dst.h:444 [inline]\nip_local_out+0x64/0x80 net/ipv4/ip_output.c:126\niptunnel_xmit+0x34a/0x4b0 net/ipv4/ip_tunnel_core.c:82\nip_tunnel_xmit+0x1451/0x1730 net/ipv4/ip_tunnel.c:813\n__gre_xmit net/ipv4/ip_gre.c:469 [inline]\nipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661\n__netdev_start_xmit include/linux/netdevice.h:4881 [inline]\nnetdev_start_xmit include/linux/netdevice.h:4895 [inline]\nxmit_one net/core/dev.c:3580 [inline]\ndev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596\n__dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246\ndev_queue_xmit include/linux/netdevice.h:3051 [inline]\nneigh_direct_output+0x17/0x20 net/core/neighbour.c:1623\nneigh_output include/net/neighbour.h:546 [inline]\nip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228\nip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316\nNF_HOOK_COND include/linux/netfilter.h:291 [inline]\nip_output+0xe5/0x1b0 net/i\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: tunnels: annotate, los accesos sin bloqueo a los túneles IP dev-&gt;needed_headroom aparentemente pueden actualizar dev-&gt;needed_headroom en su ruta de transmisión. Este parche soluciona la transmisión de tres túneles y también los ayudantes principales LL_RESERVED_SPACE() y LL_RESERVED_SPACE_EXTRA(). Es posible que se requieran más cambios para completar la solución. ERROR: KCSAN: ejecución de datos en ip_tunnel_xmit / ip_tunnel_xmit leído a 0xffff88815b9da0ec de 2 bytes por la tarea 888 en la CPU 1: ip_tunnel_xmit+0x1270/0x1730 net/ipv4/ip_tunnel.c:803 __gre_xmit net/ipv4/ip_gre.c:469 [inline] ipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661 __netdev_start_xmit include/linux/netdevice.h:4881 [inline] netdev_start_xmit include/linux/netdevice.h:4895 [inline] xmit_one net/core/dev.c:3580 [inline] dev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596 __dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246 dev_queue_xmit include/linux/netdevice.h:3051 [inline] neigh_direct_output+0x17/0x20 net/core/neighbour.c:1623 neigh_output include/net/neighbour.h:546 [inline] ip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228 ip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316 NF_HOOK_COND include/linux/netfilter.h:291 [inline] ip_output+0xe5/0x1b0 net/ipv4/ip_output.c:430 dst_output include/net/dst.h:444 [inline] ip_local_out+0x64/0x80 net/ipv4/ip_output.c:126 iptunnel_xmit+0x34a/0x4b0 net/ipv4/ip_tunnel_core.c:82 ip_tunnel_xmit+0x1451/0x1730 net/ipv4/ip_tunnel.c:813 __gre_xmit net/ipv4/ip_gre.c:469 [inline] ipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661 __netdev_start_xmit include/linux/netdevice.h:4881 [inline] netdev_start_xmit include/linux/netdevice.h:4895 [inline] xmit_one net/core/dev.c:3580 [inline] dev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596 __dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246 dev_queue_xmit include/linux/netdevice.h:3051 [inline] neigh_direct_output+0x17/0x20 net/core/neighbour.c:1623 neigh_output include/net/neighbour.h:546 [inline] ip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228 ip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316 NF_HOOK_COND include/linux/netfilter.h:291 [inline] ip_output+0xe5/0x1b0 net/ipv4/ip_output.c:430 dst_output include/net/dst.h:444 [inline] ip_local_out+0x64/0x80 net/ipv4/ip_output.c:126 iptunnel_xmit+0x34a/0x4b0 net/ipv4/ip_tunnel_core.c:82 ip_tunnel_xmit+0x1451/0x1730 net/ipv4/ip_tunnel.c:813 __gre_xmit net/ipv4/ip_gre.c:469 [inline] ipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661 __netdev_start_xmit include/linux/netdevice.h:4881 [inline] netdev_start_xmit include/linux/netdevice.h:4895 [inline] xmit_one net/core/dev.c:3580 [inline] dev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596 __dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246 dev_queue_xmit include/linux/netdevice.h:3051 [inline] neigh_direct_output+0x17/0x20 net/core/neighbour.c:1623 neigh_output include/net/neighbour.h:546 [inline] ip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228 ip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316 NF_HOOK_COND include/linux/netfilter.h:291 [inline] ip_output+0xe5/0x1b0 net/ipv4/ip_output.c:430 dst_output include/net/dst.h:444 [inline] ip_local_out+0x64/0x80 net/ipv4/ip_output.c:126 iptunnel_xmit+0x34a/0x4b0 net/ipv4/ip_tunnel_core.c:82 ip_tunnel_xmit+0x1451/0x1730 net/ipv4/ip_tunnel.c:813 __gre_xmit net/ipv4/ip_gre.c:469 [inline] ipgre_xmit+0x516/0x570 net/ipv4/ip_gre.c:661 __netdev_start_xmit include/linux/netdevice.h:4881 [inline] netdev_start_xmit include/linux/netdevice.h:4895 [inline] xmit_one net/core/dev.c:3580 [inline] dev_hard_start_xmit+0x127/0x400 net/core/dev.c:3596 __dev_queue_xmit+0x1007/0x1eb0 net/core/dev.c:4246 dev_queue_xmit include/linux/netdevice.h:3051 [inline] neigh_direct_output+0x17/0x20 net/core/neighbour.c:1623 neigh_output include/net/neighbour.h:546 [inline] ip_finish_output2+0x740/0x840 net/ipv4/ip_output.c:228 ip_finish_output+0xf4/0x240 net/ipv4/ip_output.c:316 NF_HOOK_COND include/linux/netfilter.h:291 [inline] ip_output+0xe5/0x1b0 net/i ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/4b397c06cb987935b1b097336532aa6b4210e091", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/51f3bd3765bc5ca4583af07a00833da00d2ace1d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5aaab217c8f5387b9c5fff9e940d80f135e04366", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e206f66d824b3b28a7f9ee1366dfc79a937bb46", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9b86a8702b042ee4e15d2d46375be873a6a8834f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a69b72b57b7d269e833e520ba7500d556e8189b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be59b87ee4aed81db7c10e44f603866a0ac3ca5d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e0a557fc1daf5c1086e47150a4571aebadbb62be", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
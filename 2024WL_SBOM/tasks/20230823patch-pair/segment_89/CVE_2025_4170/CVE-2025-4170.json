{"cve_id": "CVE-2025-4170", "published_date": "2025-05-03T03:15:28.493", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The Xavin&#039;s Review Ratings plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'xrr' shortcode in all versions up to, and including, 1.4.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Xavin's Review Ratings para WordPress es vulnerable a Cross-Site Scripting almacenado a través del shortcode 'xrr' del complemento en todas las versiones hasta la 1.4.0 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/xavins-review-ratings/trunk/xavins-review-ratings.php#L293", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6c057a98-4a8d-408a-b6a4-3c322bfa0cdf?source=cve", "source": "<EMAIL>", "tags": []}]}
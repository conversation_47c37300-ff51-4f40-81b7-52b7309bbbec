{"cve_id": "CVE-2025-45010", "published_date": "2025-04-30T14:15:29.080", "last_modified_date": "2025-05-09T13:45:09.930", "descriptions": [{"lang": "en", "value": "A HTML Injection vulnerability was discovered in the normal-bwdates-reports-details.php file of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary code via the fromdate and todate POST request parameters."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección HTML en el archivo normal-bwdates-reports-details.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario mediante los parámetros de solicitud POST fromdate y todate."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/normal-bwdates-reports-details-html-injection.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
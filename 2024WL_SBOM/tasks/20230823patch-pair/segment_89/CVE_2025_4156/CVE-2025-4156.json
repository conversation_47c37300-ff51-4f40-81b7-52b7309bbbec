{"cve_id": "CVE-2025-4156", "published_date": "2025-05-01T08:15:18.303", "last_modified_date": "2025-05-07T19:41:53.480", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Boat Booking System 1.0 and classified as critical. This vulnerability affects unknown code of the file /admin/change-image.php. The manipulation of the argument ID leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Boat Booking System 1.0, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/change-image.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306688", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306688", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560856", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
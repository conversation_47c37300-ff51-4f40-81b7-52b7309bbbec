{"cve_id": "CVE-2025-46345", "published_date": "2025-05-01T18:15:57.657", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "Auth0 Account Link Extension is an extension aimed to help link accounts easily. Versions 2.3.4 to 2.6.6 do not verify the signature of the provided JWT. This allows the user the ability to supply a forged token and the potential to access user information without proper authorization. This issue has been patched in versions 2.6.7, 2.7.0, and 3.0.0. It is recommended to upgrade to version 3.0.0 or greater."}, {"lang": "es", "value": "Auth0 Account Link Extension facilita la vinculación de cuentas. Las versiones 2.3.4 a 2.6.6 no verifican la firma del JWT proporcionado. Esto permite al usuario proporcionar un token falsificado y acceder a la información del usuario sin la debida autorización. Este problema se ha corregido en las versiones 2.6.7, 2.7.0 y 3.0.0. Se recomienda actualizar a la versión 3.0.0 o superior."}], "references": [{"url": "https://github.com/auth0-extensions/auth0-account-link-extension/pull/187", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/auth0-extensions/auth0-account-link-extension/security/advisories/GHSA-j2jh-rqff-7vmg", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-46558", "published_date": "2025-04-30T19:15:55.930", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "XWiki Contrib's Syntax Markdown allows importing Markdown content into wiki pages and creating wiki content in Markdown. In versions starting from 8.2 to before 8.9, the Markdown syntax is vulnerable to cross-site scripting (XSS) through HTML. In particular, using Markdown syntax, it's possible for any user to embed Javascript code that will then be executed on the browser of any other user visiting either the document or the comment that contains it. In the instance that this code is executed by a user with admins or programming rights, this issue compromises the confidentiality, integrity and availability of the whole XWiki installation. This issue has been patched in version 8.9."}, {"lang": "es", "value": "XWiki Contrib's Syntax Markdown permite importar contenido Markdown a páginas wiki y crear contenido wiki en Markdown. En versiones desde la 8.2 hasta anteriores a la 8.9, la sintaxis Markdown es vulnerable a ataques de cross-site scripting (XSS) a través de HTML. En particular, al usar la sintaxis Markdown, cualquier usuario puede incrustar código Javascript que se ejecutará en el navegador de cualquier otro usuario que visite el documento o el comentario que lo contiene. Si este código lo ejecuta un usuario con permisos de administrador o programación, este problema compromete la confidencialidad, integridad y disponibilidad de toda la instalación de XWiki. Este problema se ha corregido en la versión 8.9."}], "references": [{"url": "https://github.com/xwiki-contrib/syntax-markdown/commit/d136472d6e8a47981a0ede420a9096f88ffa5035", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/xwiki-contrib/syntax-markdown/security/advisories/GHSA-8g2j-rhfh-hq3r", "source": "<EMAIL>", "tags": []}, {"url": "https://jira.xwiki.org/browse/MARKDOWN-80", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-46720", "published_date": "2025-05-05T19:15:57.330", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Keystone is a content management system for Node.js. Prior to version 6.5.0, `{field}.isFilterable` access control can be bypassed in `update` and `delete` mutations by adding additional unique filters. These filters can be used as an oracle to probe the existence or value of otherwise unreadable fields. Specifically, when a mutation includes a `where` clause with multiple unique filters (e.g. `id` and `email`), Keystone will attempt to match records even if filtering by the latter fields would normally be rejected by `field.isFilterable` or `list.defaultIsFilterable`. This can allow malicious actors to infer the presence of a particular field value when a filter is successful in returning a result. This affects any project relying on the default or dynamic `isFilterable` behavior (at the list or field level) to prevent external users from using the filtering of fields as a discovery mechanism. While this access control is respected during `findMany` operations, it was not completely enforced during `update` and `delete` mutations when accepting more than one unique `where` values in filters. This has no impact on projects using `isFilterable: false` or `defaultIsFilterable: false` for sensitive fields, or for those who have otherwise omitted filtering by these fields from their GraphQL schema. This issue has been patched in `@keystone-6/core` version 6.5.0. To mitigate this issue in older versions where patching is not a viable pathway, set `isFilterable: false` statically for relevant fields to prevent filtering by them earlier in the access control pipeline (that is, don't use functions); set `{field}.graphql.omit.read: true` for relevant fields, which implicitly removes filtering by these fields from the GraphQL schema; and/or deny `update` and `delete` operations for the relevant lists completely."}, {"lang": "es", "value": "Keystone es un sistema de gestión de contenido para Node.js. Antes de la versión 6.5.0, el control de acceso `{field}.isFilterable` se podía omitir en las mutaciones `update` y `delete` añadiendo filtros únicos adicionales. Estos filtros se pueden usar como un oráculo para sondear la existencia o el valor de campos que de otro modo serían ilegibles. Específicamente, cuando una mutación incluye una cláusula `where` con múltiples filtros únicos (p. ej., `id` y `email`), Keystone intentará encontrar coincidencias en los registros incluso si el filtrado por estos últimos campos normalmente sería rechazado por `field.isFilterable` o `list.defaultIsFilterable`. Esto puede permitir que actores maliciosos infieran la presencia de un valor de campo específico cuando un filtro devuelve un resultado correctamente. Esto afecta a cualquier proyecto que dependa del comportamiento predeterminado o dinámico de `isFilterable` (a nivel de lista o campo) para evitar que usuarios externos utilicen el filtrado de campos como mecanismo de descubrimiento. Aunque este control de acceso se respeta durante las operaciones `findMany`, no se aplicó completamente durante las mutaciones `update` y `delete` al aceptar más de un valor `where` único en los filtros. Esto no tiene impacto en los proyectos que usan `isFilterable: false` o `defaultIsFilterable: false` para campos sensibles, o para aquellos que de otra manera han omitido el filtrado por estos campos de su esquema GraphQL. Este problema se ha corregido en `@keystone-6/core` versión 6.5.0. Para mitigar este problema en versiones anteriores donde la aplicación de parches no es una ruta viable, configure `isFilterable: false` estáticamente para los campos relevantes para evitar el filtrado por ellos anteriormente en el flujo de trabajo de control de acceso (es decir, no use funciones); configure `{field}.graphql.omit.read: true` para los campos relevantes, lo que implícitamente elimina el filtrado por estos campos del esquema GraphQL; y/o deniegue las operaciones `update` y `delete` para las listas relevantes por completo."}], "references": [{"url": "https://github.com/keystonejs/keystone/security/advisories/GHSA-hg9m-67mm-7pg3", "source": "<EMAIL>", "tags": []}]}
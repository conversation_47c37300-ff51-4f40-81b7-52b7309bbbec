{"cve_id": "CVE-2023-53053", "published_date": "2025-05-02T16:15:24.373", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nerspan: do not use skb_mac_header() in ndo_start_xmit()\n\nDrivers should not assume skb_mac_header(skb) == skb->data in their\nndo_start_xmit().\n\nUse skb_network_offset() and skb_transport_offset() which\nbetter describe what is needed in erspan_fb_xmit() and\nip6erspan_tunnel_xmit()\n\nsyzbot reported:\nWARNING: CPU: 0 PID: 5083 at include/linux/skbuff.h:2873 skb_mac_header include/linux/skbuff.h:2873 [inline]\nWARNING: CPU: 0 PID: 5083 at include/linux/skbuff.h:2873 ip6erspan_tunnel_xmit+0x1d9c/0x2d90 net/ipv6/ip6_gre.c:962\nModules linked in:\nCPU: 0 PID: 5083 Comm: syz-executor406 Not tainted 6.3.0-rc2-s<PERSON><PERSON><PERSON>ler-00866-gd4671cb96fa3 #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 03/02/2023\nRIP: 0010:skb_mac_header include/linux/skbuff.h:2873 [inline]\nRIP: 0010:ip6erspan_tunnel_xmit+0x1d9c/0x2d90 net/ipv6/ip6_gre.c:962\nCode: 04 02 41 01 de 84 c0 74 08 3c 03 0f 8e 1c 0a 00 00 45 89 b4 24 c8 00 00 00 c6 85 77 fe ff ff 01 e9 33 e7 ff ff e8 b4 27 a1 f8 <0f> 0b e9 b6 e7 ff ff e8 a8 27 a1 f8 49 8d bf f0 0c 00 00 48 b8 00\nRSP: 0018:ffffc90003b2f830 EFLAGS: 00010293\nRAX: 0000000000000000 RBX: 000000000000ffff RCX: 0000000000000000\nRDX: ffff888021273a80 RSI: ffffffff88e1bd4c RDI: 0000000000000003\nRBP: ffffc90003b2f9d8 R08: 0000000000000003 R09: 000000000000ffff\nR10: 000000000000ffff R11: 0000000000000000 R12: ffff88802b28da00\nR13: 00000000000000d0 R14: ffff88807e25b6d0 R15: ffff888023408000\nFS: 0000555556a61300(0000) GS:ffff8880b9800000(0000) knlGS:0000000000000000\nCS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 000055e5b11eb6e8 CR3: 0000000027c1b000 CR4: 00000000003506f0\nDR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000\nDR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400\nCall Trace:\n<TASK>\n__netdev_start_xmit include/linux/netdevice.h:4900 [inline]\nnetdev_start_xmit include/linux/netdevice.h:4914 [inline]\n__dev_direct_xmit+0x504/0x730 net/core/dev.c:4300\ndev_direct_xmit include/linux/netdevice.h:3088 [inline]\npacket_xmit+0x20a/0x390 net/packet/af_packet.c:285\npacket_snd net/packet/af_packet.c:3075 [inline]\npacket_sendmsg+0x31a0/0x5150 net/packet/af_packet.c:3107\nsock_sendmsg_nosec net/socket.c:724 [inline]\nsock_sendmsg+0xde/0x190 net/socket.c:747\n__sys_sendto+0x23a/0x340 net/socket.c:2142\n__do_sys_sendto net/socket.c:2154 [inline]\n__se_sys_sendto net/socket.c:2150 [inline]\n__x64_sys_sendto+0xe1/0x1b0 net/socket.c:2150\ndo_syscall_x64 arch/x86/entry/common.c:50 [inline]\ndo_syscall_64+0x39/0xb0 arch/x86/entry/common.c:80\nentry_SYSCALL_64_after_hwframe+0x63/0xcd\nRIP: 0033:0x7f123aaa1039\nCode: 28 00 00 00 75 05 48 83 c4 28 c3 e8 b1 14 00 00 90 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 4c 8b 4c 24 08 0f 05 <48> 3d 01 f0 ff ff 73 01 c3 48 c7 c1 c0 ff ff ff f7 d8 64 89 01 48\nRSP: 002b:00007ffc15d12058 EFLAGS: 00000246 ORIG_RAX: 000000000000002c\nRAX: ffffffffffffffda RBX: 0000000000000000 RCX: 00007f123aaa1039\nRDX: 0000000000000000 RSI: 0000000000000000 RDI: 0000000000000003\nRBP: 0000000000000000 R08: 0000000020000040 R09: 0000000000000014\nR10: 0000000000000000 R11: 0000000000000246 R12: 00007f123aa648c0\nR13: 431bde82d7b634db R14: 0000000000000000 R15: 0000000000000000"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: erspan: no utilice skb_mac_header() en ndo_start_xmit() Los controladores no deben asumir que skb_mac_header(skb) == skb-&gt;data en su ndo_start_xmit(). Utilice skb_network_offset() y skb_transport_offset() que describen mejor lo que se necesita en erspan_fb_xmit() e ip6erspan_tunnel_xmit() syzbot informó: ADVERTENCIA: CPU: 0 PID: 5083 en include/linux/skbuff.h:2873 skb_mac_header include/linux/skbuff.h:2873 [en línea] ADVERTENCIA: CPU: 0 PID: 5083 en include/linux/skbuff.h:2873 ip6erspan_tunnel_xmit+0x1d9c/0x2d90 net/ipv6/ip6_gre.c:962 Módulos vinculados: CPU: 0 PID: 5083 Comm: syz-executor406 No contaminado 6.3.0-rc2-syzkaller-00866-gd4671cb96fa3 #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 02/03/2023 RIP: 0010:skb_mac_header include/linux/skbuff.h:2873 [en línea] RIP: 0010:ip6erspan_tunnel_xmit+0x1d9c/0x2d90 net/ipv6/ip6_gre.c:962 Código: 04 02 41 01 de 84 c0 74 08 3c 03 0f 8e 1c 0a 00 00 45 89 b4 24 c8 00 00 00 c6 85 77 fe ff ff 01 e9 33 e7 ff ff e8 b4 27 a1 f8 &lt;0f&gt; 0b e9 b6 e7 ff ff e8 a8 27 a1 f8 49 8d bf f0 0c 00 00 48 b8 00 RSP: 0018:ffffc90003b2f830 EFLAGS: 00010293 RAX: 0000000000000000 RBX: 000000000000ffff RCX: 0000000000000000 RDX: ffff888021273a80 RSI: ffffffff88e1bd4c RDI: 0000000000000003 RBP: ffffc90003b2f9d8 R08: 00000000000000003 R09: 000000000000ffff R10: 000000000000ffff R11: 0000000000000000 R12: ffff88802b28da00 R13: 00000000000000d0 R14: ffff88807e25b6d0 R15: ffff888023408000 FS: 0000555556a61300(0000) GS:ffff8880b9800000(0000) knlGS:000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 000055e5b11eb6e8 CR3: 0000000027c1b000 CR4: 000000000003506f0 DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000 DR3: 000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400 Rastreo de llamadas:  __netdev_start_xmit include/linux/netdevice.h:4900 [en línea] netdev_start_xmit include/linux/netdevice.h:4914 [en línea] __dev_direct_xmit+0x504/0x730 net/core/dev.c:4300 dev_direct_xmit include/linux/netdevice.h:3088 [en línea] packet_xmit+0x20a/0x390 net/packet/af_packet.c:285 packet_snd net/packet/af_packet.c:3075 [en línea] packet_sendmsg+0x31a0/0x5150 net/packet/af_packet.c:3107 sock_sendmsg_nosec net/socket.c:724 [en línea] sock_sendmsg+0xde/0x190 net/socket.c:747 __sys_sendto+0x23a/0x340 net/socket.c:2142 __do_sys_sendto net/socket.c:2154 [en línea] __se_sys_sendto net/socket.c:2150 [en línea] __x64_sys_sendto+0xe1/0x1b0 net/socket.c:2150 do_syscall_x64 arch/x86/entry/common.c:50 [en línea] do_syscall_64+0x39/0xb0 arch/x86/entry/common.c:80 entry_SYSCALL_64_after_hwframe+0x63/0xcd RIP: 0033:0x7f123aaa1039 Código: 28 00 00 00 75 05 48 83 c4 28 c3 e8 b1 14 00 00 90 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 4c 8b 4c 24 08 0f 05 &lt;48&gt; 3d 01 f0 ff ff 73 01 c3 48 c7 c1 c0 ff ff ff f7 d8 64 89 01 48 RSP: 002b:00007ffc15d12058 EFLAGS: 00000246 ORIG_RAX: 000000000000002c RAX: ffffffffffffffda RBX: 000000000000000 RCX: 00007f123aaa1039 RDX: 0000000000000000 RSI: 0000000000000000 RDI: 0000000000000003 RBP: 0000000000000000 R08: 0000000020000040 R09: 0000000000000014 R10: 0000000000000000 R11: 0000000000000246 R12: 00007f123aa648c0 R13: 431bde82d7b634db R14: 000000000000000 R15: 0000000000000000"}], "references": [{"url": "https://git.kernel.org/stable/c/5d4172732f0ee1639a361a6cc5c3114bbb397386", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e50ed774554f93d55426039b27b1e38d7fa64d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9c7d6803689c99d55bbb862260d0ba486ff23c0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b41f37dbd9cdb60000e3b0dfad6df787591c2265", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b72f453e886af532bde1fd049a2d2421999630d3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da149daf821a3c05cd04f7c60776c86c5ee9685c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f8cec30541f5c5cc218e9a32138d45d227727f2f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49886", "published_date": "2025-05-01T15:16:13.590", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nx86/tdx: Panic on bad configs that #VE on \"private\" memory access\n\nAll normal kernel memory is \"TDX private memory\".  This includes\neverything from kernel stacks to kernel text.  Handling\nexceptions on arbitrary accesses to kernel memory is essentially\nimpossible because they can happen in horribly nasty places like\nkernel entry/exit.  But, TDX hardware can theoretically _deliver_\na virtualization exception (#VE) on any access to private memory.\n\nBut, it's not as bad as it sounds.  TDX can be configured to never\ndeliver these exceptions on private memory with a \"TD attribute\"\ncalled ATTR_SEPT_VE_DISABLE.  The guest has no way to *set* this\nattribute, but it can check it.\n\nEnsure ATTR_SEPT_VE_DISABLE is set in early boot.  panic() if it\nis unset.  There is no sane way for Linux to run with this\nattribute clear so a panic() is appropriate.\n\nThere's small window during boot before the check where kernel\nhas an early #VE handler. But the handler is only for port I/O\nand will also panic() as soon as it sees any other #VE, such as\na one generated by a private memory access.\n\n[ dhansen: Rewrite changelog and rebase on new tdx_parse_tdinfo().\n\t   Add <PERSON>'s tested-by because I made changes since\n\t   he wrote this. ]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: x86/tdx: Pánico en configuraciones incorrectas que generan #VE al acceder a memoria \"privada\". Toda la memoria normal del kernel es \"memoria privada TDX\". Esto incluye todo, desde las pilas del kernel hasta el texto del kernel. Gestionar excepciones en accesos arbitrarios a la memoria del kernel es prácticamente imposible, ya que pueden ocurrir en lugares muy peligrosos, como la entrada/salida del kernel. Sin embargo, el hardware TDX, en teoría, puede generar una excepción de virtualización (#VE) en cualquier acceso a memoria privada. Sin embargo, no es tan grave como parece. TDX se puede configurar para que nunca genere estas excepciones en memoria privada con un \"atributo TD\" llamado ATTR_SEPT_VE_DISABLE. El invitado no tiene forma de *configurar* este atributo, pero puede comprobarlo. Asegúrese de que ATTR_SEPT_VE_DISABLE esté configurado en el arranque inicial. Si no está configurado, utilice panic(). No hay una forma sensata de que Linux se ejecute con este atributo sin configurar, por lo que es apropiado usar panic(). Hay una pequeña ventana durante el arranque antes de la comprobación donde el kernel tiene un controlador de #VE temprano. Sin embargo, este controlador solo sirve para la E/S de puerto y también se pondrá en pánico() en cuanto detecte cualquier otro #VE, como uno generado por un acceso a memoria privada. [dhansen: Reescribir el registro de cambios y reorganizar con el nuevo tdx_parse_tdinfo(). Añadir la prueba de Kirill porque hice cambios desde que escribió esto.]"}], "references": [{"url": "https://git.kernel.org/stable/c/373e715e31bf4e0f129befe87613a278fac228d3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/895c168c8f78079f21ad50fead7593ffa352f795", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
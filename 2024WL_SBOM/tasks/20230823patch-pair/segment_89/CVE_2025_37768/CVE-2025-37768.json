{"cve_id": "CVE-2025-37768", "published_date": "2025-05-01T14:15:39.977", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/pm: Prevent division by zero\n\nThe user can set any speed value.\nIf speed is greater than UINT_MAX/8, division by zero is possible.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/pm: Impide la división por cero. El usuario puede establecer cualquier valor de velocidad. Si la velocidad es superior a UINT_MAX/8, es posible la división por cero. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/3cdd02cb70682d7d205ca6dc02a4d1eb76758d24", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5fc4fb54f6f064c25bfbbfd443aa861d3422dd4c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7c246a05df51c52fe0852ce56ba10c41e6ed1f39", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e9c4f8d197d5709c75effa5d58e80b4fa01981a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e4f1e21fe7b93a8ef57db433071266c2590e260", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b0742a709be7979c7a480772046a1f36d09dab00", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be0fffc4152aac4f0291ed2d793f3cfee788449d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
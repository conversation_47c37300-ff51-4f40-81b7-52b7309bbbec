{"cve_id": "CVE-2023-53106", "published_date": "2025-05-02T16:15:29.520", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfc: st-nci: Fix use after free bug in ndlc_remove due to race condition\n\nThis bug influences both st_nci_i2c_remove and st_nci_spi_remove.\nTake st_nci_i2c_remove as an example.\n\nIn st_nci_i2c_probe, it called ndlc_probe and bound &ndlc->sm_work\nwith llt_ndlc_sm_work.\n\nWhen it calls ndlc_recv or timeout handler, it will finally call\nschedule_work to start the work.\n\nWhen we call st_nci_i2c_remove to remove the driver, there\nmay be a sequence as follows:\n\nFix it by finishing the work before cleanup in ndlc_remove\n\nCPU0                  CPU1\n\n                    |llt_ndlc_sm_work\nst_nci_i2c_remove   |\n  ndlc_remove       |\n     st_nci_remove  |\n     nci_free_device|\n     kfree(ndev)    |\n//free ndlc->ndev   |\n                    |llt_ndlc_rcv_queue\n                    |nci_recv_frame\n                    |//use ndlc->ndev"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfc: st-nci: Fix use after free bug en ndlc_remove debido a una condición de ejecución Este error afecta tanto a st_nci_i2c_remove como a st_nci_spi_remove. Tomemos st_nci_i2c_remove como ejemplo. En st_nci_i2c_probe, llamó a ndlc_probe y vinculó &amp;ndlc-&gt;sm_work con llt_ndlc_sm_work. Cuando llama a ndlc_recv o al controlador de tiempo de espera, finalmente llamará a schedule_work para iniciar el trabajo. Cuando llamamos a st_nci_i2c_remove para eliminar el controlador, puede haber una secuencia como la siguiente: Arréglelo finalizando el trabajo antes de la limpieza en ndlc_remove CPU0 CPU1 |llt_ndlc_sm_work st_nci_i2c_remove | ndlc_remove | st_nci_remove | nci_free_device| kfree(ndev) | //liberar ndlc-&gt;ndev | |llt_ndlc_rcv_queue |nci_recv_frame |//usar ndlc-&gt;ndev"}], "references": [{"url": "https://git.kernel.org/stable/c/2156490c4b7cacda9a18ec99929940b8376dc0e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3405eb641dafcc8b28d174784b203c1622c121bf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/43aa468df246175207a7d5d7d6d31b231f15b49c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5000fe6c27827a61d8250a7e4a1d26c3298ef4f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e331022b448fbc5e76f24349cd0246844dcad25", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/84dd9cc34014e3a3dcce0eb6d54b8a067e97676b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b0c202a8dc63008205a5d546559736507a9aae66", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f589e5b56c562d99ea74e05b1c3f0eab78aa17a3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
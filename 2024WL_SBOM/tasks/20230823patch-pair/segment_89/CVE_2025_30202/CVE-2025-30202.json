{"cve_id": "CVE-2025-30202", "published_date": "2025-04-30T01:15:51.800", "last_modified_date": "2025-05-14T19:59:42.390", "descriptions": [{"lang": "en", "value": "vLLM is a high-throughput and memory-efficient inference and serving engine for LLMs. Versions starting from 0.5.2 and prior to 0.8.5 are vulnerable to denial of service and data exposure via ZeroMQ on multi-node vLLM deployment. In a multi-node vLLM deployment, vLLM uses ZeroMQ for some multi-node communication purposes. The primary vLLM host opens an XPUB ZeroMQ socket and binds it to ALL interfaces. While the socket is always opened for a multi-node deployment, it is only used when doing tensor parallelism across multiple hosts. Any client with network access to this host can connect to this XPUB socket unless its port is blocked by a firewall. Once connected, these arbitrary clients will receive all of the same data broadcasted to all of the secondary vLLM hosts. This data is internal vLLM state information that is not useful to an attacker. By potentially connecting to this socket many times and not reading data published to them, an attacker can also cause a denial of service by slowing down or potentially blocking the publisher. This issue has been patched in version 0.8.5."}, {"lang": "es", "value": "vLLM es un motor de inferencia y servicio de alto rendimiento y eficiente en memoria para LLM. Las versiones a partir de la 0.5.2 y anteriores a la 0.8.5 son vulnerables a denegación de servicio y exposición de datos a través de ZeroMQ en implementaciones de vLLM multinodo. En una implementación de vLLM multinodo, vLLM utiliza ZeroMQ para algunos fines de comunicación multinodo. El host vLLM principal abre un socket XPUB ZeroMQ y lo vincula a TODAS las interfaces. Si bien el socket siempre está abierto para implementaciones multinodo, solo se utiliza al realizar paralelismo tensorial en varios hosts. Cualquier cliente con acceso de red a este host puede conectarse a este socket XPUB a menos que su puerto esté bloqueado por un firewall. Una vez conectados, estos clientes arbitrarios recibirán los mismos datos transmitidos a todos los hosts vLLM secundarios. Estos datos son información interna del estado de vLLM que no es útil para un atacante. Al conectarse a este socket muchas veces y no leer los datos publicados, un atacante también puede causar una denegación de servicio al ralentizar o incluso bloquear al publicador. Este problema se ha corregido en la versión 0.8.5."}], "references": [{"url": "https://github.com/vllm-project/vllm/commit/a0304dc504c85f421d38ef47c64f83046a13641c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/vllm-project/vllm/pull/6183", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-9f8f-2vmf-885j", "source": "<EMAIL>", "tags": ["Vendor Advisory", "Exploit"]}]}
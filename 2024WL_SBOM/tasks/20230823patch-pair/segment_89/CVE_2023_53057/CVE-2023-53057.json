{"cve_id": "CVE-2023-53057", "published_date": "2025-05-02T16:15:24.773", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: HCI: Fix global-out-of-bounds\n\nTo loop a variable-length array, hci_init_stage_sync(stage) considers\nthat stage[i] is valid as long as stage[i-1].func is valid.\nThus, the last element of stage[].func should be intentionally invalid\nas hci_init0[], le_init2[], and others did.\nHowever, amp_init1[] and amp_init2[] have no invalid element, letting\nhci_init_stage_sync() keep accessing amp_init1[] over its valid range.\nThis patch fixes this by adding {} in the last of amp_init1[] and\namp_init2[].\n\n==================================================================\nBUG: KASAN: global-out-of-bounds in hci_dev_open_sync (\n/v6.2-bzimage/net/bluetooth/hci_sync.c:3154\n/v6.2-bzimage/net/bluetooth/hci_sync.c:3343\n/v6.2-bzimage/net/bluetooth/hci_sync.c:4418\n/v6.2-bzimage/net/bluetooth/hci_sync.c:4609\n/v6.2-bzimage/net/bluetooth/hci_sync.c:4689)\nRead of size 8 at addr ffffffffaed1ab70 by task kworker/u5:0/1032\nCPU: 0 PID: 1032 Comm: kworker/u5:0 Not tainted 6.2.0 #3\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS 1.15.0-1 04\nWorkqueue: hci1 hci_power_on\nCall Trace:\n <TASK>\ndump_stack_lvl (/v6.2-bzimage/lib/dump_stack.c:107 (discriminator 1))\nprint_report (/v6.2-bzimage/mm/kasan/report.c:307\n  /v6.2-bzimage/mm/kasan/report.c:417)\n? hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:3154\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:3343\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4418\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4609\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4689)\nkasan_report (/v6.2-bzimage/mm/kasan/report.c:184\n  /v6.2-bzimage/mm/kasan/report.c:519)\n? hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:3154\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:3343\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4418\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4609\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4689)\nhci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:3154\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:3343\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4418\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4609\n  /v6.2-bzimage/net/bluetooth/hci_sync.c:4689)\n? __pfx_hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:4635)\n? mutex_lock (/v6.2-bzimage/./arch/x86/include/asm/atomic64_64.h:190\n  /v6.2-bzimage/./include/linux/atomic/atomic-long.h:443\n  /v6.2-bzimage/./include/linux/atomic/atomic-instrumented.h:1781\n  /v6.2-bzimage/kernel/locking/mutex.c:171\n  /v6.2-bzimage/kernel/locking/mutex.c:285)\n? __pfx_mutex_lock (/v6.2-bzimage/kernel/locking/mutex.c:282)\nhci_power_on (/v6.2-bzimage/net/bluetooth/hci_core.c:485\n  /v6.2-bzimage/net/bluetooth/hci_core.c:984)\n? __pfx_hci_power_on (/v6.2-bzimage/net/bluetooth/hci_core.c:969)\n? read_word_at_a_time (/v6.2-bzimage/./include/asm-generic/rwonce.h:85)\n? strscpy (/v6.2-bzimage/./arch/x86/include/asm/word-at-a-time.h:62\n  /v6.2-bzimage/lib/string.c:161)\nprocess_one_work (/v6.2-bzimage/kernel/workqueue.c:2294)\nworker_thread (/v6.2-bzimage/./include/linux/list.h:292\n  /v6.2-bzimage/kernel/workqueue.c:2437)\n? __pfx_worker_thread (/v6.2-bzimage/kernel/workqueue.c:2379)\nkthread (/v6.2-bzimage/kernel/kthread.c:376)\n? __pfx_kthread (/v6.2-bzimage/kernel/kthread.c:331)\nret_from_fork (/v6.2-bzimage/arch/x86/entry/entry_64.S:314)\n </TASK>\nThe buggy address belongs to the variable:\namp_init1+0x30/0x60\nThe buggy address belongs to the physical page:\npage:000000003a157ec6 refcount:1 mapcount:0 mapping:0000000000000000 ia\nflags: 0x200000000001000(reserved|node=0|zone=2)\nraw: 0200000000001000 ffffea0005054688 ffffea0005054688 000000000000000\nraw: 0000000000000000 0000000000000000 00000001ffffffff 000000000000000\npage dumped because: kasan: bad access detected\nMemory state around the buggy address:\n ffffffffaed1aa00: f9 f9 f9 f9 00 00 00 00 f9 f9 f9 f9 00 00 00 00\n ffffffffaed1aa80: 00 00 00 00 f9 f9 f9 f9 00 00 00 00 00 00 00 00\n>ffffffffaed1ab00: 00 f9 f9 f9 f9 f9 f9 f9 00 00 00 00 00 00 f9 f9\n                  \n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: HCI: Corrección de un error global fuera de los límites. Para repetir una matriz de longitud variable, hci_init_stage_sync(stage) considera que stage[i] es válido siempre que stage[i-1].func lo sea. Por lo tanto, el último elemento de stage[].func debería ser intencionalmente inválido, como hicieron hci_init0[], le_init2[] y otros. Sin embargo, amp_init1[] y amp_init2[] no tienen ningún elemento inválido, lo que permite que hci_init_stage_sync() siga accediendo a amp_init1[] por encima de su rango válido. Este parche corrige esto añadiendo {} al final de amp_init1[] y amp_init2[]. ====================================================================== ERROR: KASAN: global fuera de los límites en hci_dev_open_sync ( /v6.2-bzimage/net/bluetooth/hci_sync.c:3154 /v6.2-bzimage/net/bluetooth/hci_sync.c:3343 /v6.2-bzimage/net/bluetooth/hci_sync.c:4418 /v6.2-bzimage/net/bluetooth/hci_sync.c:4609 /v6.2-bzimage/net/bluetooth/hci_sync.c:4689) Lectura de tamaño 8 en la dirección ffffffffaed1ab70 por tarea kworker/u5:0/1032 CPU: 0 PID: 1032 Comm: kworker/u5:0 No contaminado 6.2.0 #3 Nombre del hardware: PC estándar QEMU (i440FX + PIIX, 1996), BIOS 1.15.0-1 04 Cola de trabajo: hci1 hci_power_on Rastreo de llamadas:  dump_stack_lvl (/v6.2-bzimage/lib/dump_stack.c:107 (discriminator 1)) print_report (/v6.2-bzimage/mm/kasan/report.c:307 /v6.2-bzimage/mm/kasan/report.c:417) ? hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:3154 /v6.2-bzimage/net/bluetooth/hci_sync.c:3343 /v6.2-bzimage/net/bluetooth/hci_sync.c:4418 /v6.2-bzimage/net/bluetooth/hci_sync.c:4609 /v6.2-bzimage/net/bluetooth/hci_sync.c:4689) kasan_report (/v6.2-bzimage/mm/kasan/report.c:184 /v6.2-bzimage/mm/kasan/report.c:519) ? hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:3154 /v6.2-bzimage/net/bluetooth/hci_sync.c:3343 /v6.2-bzimage/net/bluetooth/hci_sync.c:4418 /v6.2-bzimage/net/bluetooth/hci_sync.c:4609 /v6.2-bzimage/net/bluetooth/hci_sync.c:4689) hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:3154 /v6.2-bzimage/net/bluetooth/hci_sync.c:3343 /v6.2-bzimage/net/bluetooth/hci_sync.c:4418 /v6.2-bzimage/net/bluetooth/hci_sync.c:4609 /v6.2-bzimage/net/bluetooth/hci_sync.c:4689) ? __pfx_hci_dev_open_sync (/v6.2-bzimage/net/bluetooth/hci_sync.c:4635) ? bloqueo mutex (/v6.2-bzimage/./arch/x86/include/asm/atomic64_64.h:190 /v6.2-bzimage/./include/linux/atomic/atomic-long.h:443 /v6.2-bzimage/./include/linux/atomic/atomic-instrumented.h:1781 /v6.2-bzimage/kernel/locking/mutex.c:171 /v6.2-bzimage/kernel/locking/mutex.c:285) ? __pfx_mutex_lock (/v6.2-bzimage/kernel/locking/mutex.c:282) hci_power_on (/v6.2-bzimage/net/bluetooth/hci_core.c:485 /v6.2-bzimage/net/bluetooth/hci_core.c:984) ? __pfx_hci_power_on (/v6.2-bzimage/net/bluetooth/hci_core.c:969) ? leer_palabra_a_la_vez (/v6.2-bzimage/./include/asm-generic/rwonce.h:85) ? strscpy (/v6.2-bzimage/./arch/x86/include/asm/word-at-a-time.h:62 /v6.2-bzimage/lib/string.c:161) process_one_work (/v6.2-bzimage/kernel/workqueue.c:2294) work_thread (/v6.2-bzimage/./include/linux/list.h:292 /v6.2-bzimage/kernel/workqueue.c:2437) ? __pfx_worker_thread (/v6.2-bzimage/kernel/workqueue.c:2379) kthread (/v6.2-bzimage/kernel/kthread.c:376) ? __pfx_kthread (/v6.2-bzimage/kernel/kthread.c:331) ret_from_fork (/v6.2-bzimage/arch/x86/entry/entry_64.S:314)  La dirección con errores pertenece a la variable: amp_init1+0x30/0x60 La dirección con errores pertenece a la página física: page:000000003a157ec6 refcount:1 mapcount:0 mapping:000000000000000 ia flags: 0x200000000001000(reserved|node=0|zone=2) raw: 0200000000001000 ffffea0005054688 ffffea0005054688 000000000000000 raw: 0000000000000000 0000000000000000 00000001ffffffff 0000000000000000 página volcada porque: kasan: mal acceso detectado Estado de la memoria alrededor de la dirección con errores: fffffffaed1aa00: f9 f9 f9 f9 00 00 00 00 f9 f9 f9 f9 00 00 00 00 ffffffffaed1aa80: 00 00 00 00 f9 f9 f9 f9 00 00 00 00 00 00 00 00 &gt;ffffffffaed1ab00 ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/8497222b22b591c6b2d106e0e3c1672ffe4e10e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b3168abd24245aa0775c5a387dcf94d36ca7e738", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bce56405201111807cc8e4f47c6de3e10b17c1ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
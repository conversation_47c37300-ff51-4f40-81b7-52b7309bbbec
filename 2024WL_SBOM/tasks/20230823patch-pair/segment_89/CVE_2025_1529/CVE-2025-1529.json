{"cve_id": "CVE-2025-1529", "published_date": "2025-05-01T12:15:16.093", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "The AM LottiePlayer plugin for WordPress is vulnerable to Stored Cross-Site Scripting via uploaded lottie files in all versions up to, and including, 3.5.3 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Author-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento AM LottiePlayer para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de archivos Lottie subidos en todas las versiones hasta la 3.5.3 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de autor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3278523/am-lottieplayer", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0f44fcc8-c5e0-44f5-92c3-6603b19a06fe?source=cve", "source": "<EMAIL>", "tags": []}]}
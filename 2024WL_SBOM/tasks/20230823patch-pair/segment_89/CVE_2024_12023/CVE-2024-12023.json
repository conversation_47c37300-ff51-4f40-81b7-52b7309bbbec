{"cve_id": "CVE-2024-12023", "published_date": "2025-05-02T04:15:43.097", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "The FULL – Cliente plugin for WordPress is vulnerable to SQL Injection via the 'formId' parameter in all versions 3.1.5 to 3.1.25 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database. This is only exploitable when the PRO version of the plugin is activated, along with Elementor Pro and  Elementor CRM."}, {"lang": "es", "value": "El complemento FULL – Cliente para WordPress es vulnerable a la inyección SQL a través del parámetro 'formId' en todas las versiones, de la 3.1.5 a la 3.1.25, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, añadir consultas SQL adicionales a las consultas existentes, las cuales pueden utilizarse para extraer información confidencial de la base de datos. Esto solo es explotable cuando se activa la versión PRO del complemento, junto con Elementor Pro y Elementor CRM."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/full-customer/tags/3.1.25/app/controller/elementor-crm/Hooks.php#L181", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/full-customer/tags/3.1.26/app/controller/elementor-crm/Hooks.php#L181", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/86e990ae-6bfe-4f2b-8c37-b0675430a638?source=cve", "source": "<EMAIL>", "tags": []}]}
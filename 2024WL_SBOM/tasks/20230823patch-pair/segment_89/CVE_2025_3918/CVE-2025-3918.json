{"cve_id": "CVE-2025-3918", "published_date": "2025-05-03T03:15:28.040", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The Job Listings plugin for WordPress is vulnerable to Privilege Escalation due to improper authorization within the register_action() function in versions 0.1 to 0.1.1. The plugin’s registration handler reads the client-supplied $_POST['user_role'] and passes it directly to wp_insert_user() without restricting to a safe set of roles.  This makes it possible for unauthenticated attackers to elevate their privileges to that of an administrator."}, {"lang": "es", "value": "El complemento Job Listings para WordPress es vulnerable a la escalada de privilegios debido a una autorización incorrecta en la función register_action() en las versiones 0.1 a 0.1.1. El controlador de registro del complemento lee el $_POST['user_role'] proporcionado por el cliente y lo pasa directamente a wp_insert_user() sin restringirlo a un conjunto seguro de roles. Esto permite que atacantes no autenticados eleven sus privilegios a los de administrador."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/job-listings/trunk/includes/forms/class-jlt-form-member.php#L68", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/job-listings/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c9cd43f5-c3d0-4eb2-9c18-1af2edca37ff?source=cve", "source": "<EMAIL>", "tags": []}]}
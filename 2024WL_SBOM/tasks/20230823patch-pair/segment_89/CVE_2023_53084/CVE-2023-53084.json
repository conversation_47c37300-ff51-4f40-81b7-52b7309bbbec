{"cve_id": "CVE-2023-53084", "published_date": "2025-05-02T16:15:27.403", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/shmem-helper: Remove another errant put in error path\n\ndrm_gem_shmem_mmap() doesn't own reference in error code path, resulting\nin the dma-buf shmem GEM object getting prematurely freed leading to a\nlater use-after-free."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/shmem-helper: Eliminar otro objeto errante en la ruta de error drm_gem_shmem_mmap() no posee una referencia en la ruta del código de error, lo que da como resultado que el objeto GEM shmem dma-buf se libere prematuramente y genere un use-after-free posterior."}], "references": [{"url": "https://git.kernel.org/stable/c/5cfb617967b05f8f27e862c97db1fabd8485f4db", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/684c7372bbd6447c2e86a2a84e97a1478604d21f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/77d26c824aa5a7e0681ef1d5b75fe538d746addc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dede8c14a37a7ac458f9add56154a074ed78e7cf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee9adb7a45516cfa536ca92253d7ae59d56db9e4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
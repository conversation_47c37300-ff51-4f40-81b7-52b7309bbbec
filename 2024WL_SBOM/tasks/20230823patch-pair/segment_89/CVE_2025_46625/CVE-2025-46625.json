{"cve_id": "CVE-2025-46625", "published_date": "2025-05-01T20:15:38.037", "last_modified_date": "2025-05-27T14:22:39.907", "descriptions": [{"lang": "en", "value": "Lack of input validation/sanitization in the 'setLanCfg' API endpoint in httpd in the Tenda RX2 Pro *********** allows a remote attacker that is authorized to the web management portal to gain root shell access to the device by sending a crafted web request. This is persistent because the command injection is saved in the configuration of the device."}, {"lang": "es", "value": "La falta de validación/depuración de entrada en el endpoint de la API 'setLanCfg' en httpd en Tenda RX2 Pro *********** permite que un atacante remoto autorizado al portal de administración web obtenga acceso root al dispositivo mediante una solicitud web manipulada. Esto es persistente porque la inyección de comandos se guarda en la configuración del dispositivo."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46625-command-injection-through-setlancfg-in-httpd", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
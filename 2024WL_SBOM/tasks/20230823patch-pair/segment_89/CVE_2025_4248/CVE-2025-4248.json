{"cve_id": "CVE-2025-4248", "published_date": "2025-05-04T06:15:14.667", "last_modified_date": "2025-05-13T15:06:14.810", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in SourceCodester Simple To-Do List System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /complete_task.php. The manipulation of the argument ID leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en SourceCodester Simple To-Do List System 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /complete_task.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado.\n"}], "references": [{"url": "https://github.com/zonesec0/findcve/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307345", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307345", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562700", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.sourcecodester.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
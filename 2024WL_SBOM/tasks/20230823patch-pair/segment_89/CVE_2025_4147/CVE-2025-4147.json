{"cve_id": "CVE-2025-4147", "published_date": "2025-05-01T02:15:17.947", "last_modified_date": "2025-05-12T19:38:34.013", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Netgear EX6200 ******** and classified as critical. Affected by this vulnerability is the function sub_47F7C. The manipulation of the argument host leads to buffer overflow. The attack can be launched remotely. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en Netgear EX6200 ********, clasificada como crítica. Esta vulnerabilidad afecta a la función sub_47F7C. La manipulación del argumento \"host\" provoca un desbordamiento del búfer. El ataque puede ejecutarse remotamente. Se contactó al proveedor con antelación para informarle sobre esta vulnerabilidad, pero no respondió."}], "references": [{"url": "https://github.com/jylsec/vuldb/blob/main/Netgear/netgear_ex6200/Buffer_overflow-sub_47F7C-media_name/README.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.306679", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306679", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560801", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.netgear.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
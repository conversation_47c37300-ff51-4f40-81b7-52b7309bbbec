{"cve_id": "CVE-2025-2168", "published_date": "2025-05-01T04:16:53.127", "last_modified_date": "2025-05-12T19:38:07.983", "descriptions": [{"lang": "en", "value": "The Ultimate Store Kit Elementor Addons, Woocommerce Builder, EDD Builder, Elementor Store Builder, Product Grid, Product Table, Woocommerce Slider plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 2.4.1. This is due to missing or incorrect nonce validation on the dismiss() function. This makes it possible for unauthenticated attackers to set arbitrary user meta values to `1` which can be leveraged to lock and administrator out of their site via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "Los complementos Ultimate Store Kit para Elementor, Woocommerce Builder, EDD Builder, Elementor Store Builder, Product Grid, Product Table y Woocommerce Slider para WordPress son vulnerables a Cross-Site Request Forgery en todas las versiones hasta la 2.4.1 (incluida). Esto se debe a la falta o a una validación de nonce incorrecta en la función dismiss(). Esto permite a atacantes no autenticados establecer valores meta de usuario arbitrarios en `1`, lo que puede utilizarse para bloquear el acceso de un administrador a su sitio mediante una solicitud falsificada, ya que pueden engañar al administrador para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/ultimate-store-kit/tags/2.3.6/admin/admin-notice.php#L43", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3255125/ultimate-store-kit/trunk/admin/admin-notice.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3283438/ultimate-store-kit/trunk/admin/admin-notice.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a48634d7-30c9-4124-87dd-93a303a969eb?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-46571", "published_date": "2025-05-05T19:15:57.050", "last_modified_date": "2025-06-17T20:18:30.580", "descriptions": [{"lang": "en", "value": "Open WebUI is a self-hosted artificial intelligence platform designed to operate entirely offline. Prior to version 0.6.6, low privileged users can upload HTML files which contain JavaScript code via the `/api/v1/files/` backend endpoint. This endpoint returns a file id, which can be used to open the file in the browser and trigger the JavaScript code in the user's browser. Under the default settings, files uploaded by low-privileged users can only be viewed by admins or themselves, limiting the impact of this vulnerability. A link to such a file can be sent to an admin, and if clicked, will give the low-privileged user complete control over the admin's account, ultimately enabling RCE via functions. Version 0.6.6 contains a fix for the issue."}, {"lang": "es", "value": "Open WebUI es una plataforma de inteligencia artificial autoalojada, diseñada para operar completamente sin conexión. Antes de la versión 0.6.6, los usuarios con privilegios bajos podían subir archivos HTML con código JavaScript a través del endpoint `/api/v1/files/`. Este endpoint devuelve un ID de archivo, que permite abrirlo en el navegador y activar el código JavaScript. Con la configuración predeterminada, los archivos subidos por usuarios con privilegios bajos solo pueden ser vistos por los administradores o por ellos mismos, lo que limita el impacto de esta vulnerabilidad. Se puede enviar un enlace a dicho archivo a un administrador y, al hacer clic en él, le otorga control total sobre su cuenta, lo que permite el acceso remoto a través de funciones. La versión 0.6.6 incluye una solución para este problema."}], "references": [{"url": "https://github.com/open-webui/open-webui/blob/main/backend/open_webui/routers/files.py#L434-L438", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/open-webui/open-webui/commit/ef2aeb7c0eb976bac759e59ac359c94a5b8dc7e0", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/open-webui/open-webui/security/advisories/GHSA-8gh5-qqh8-hq3x", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
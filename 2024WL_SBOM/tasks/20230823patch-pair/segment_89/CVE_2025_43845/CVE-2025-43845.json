{"cve_id": "CVE-2025-43845", "published_date": "2025-05-05T18:15:42.300", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Retrieval-based-Voice-Conversion-WebUI is a voice changing framework based on VITS. Versions 2.2.231006 and prior are vulnerable to code injection. The ckpt_path2 variable takes user input (e.g. a path to a model) and passes it to change_info_ function, which opens and reads the file on the given path (except it changes the final on the path to train.log), and passes the contents of the file to eval, which can lead to remote code execution. As of time of publication, no known patches exist."}, {"lang": "es", "value": "Retrieval-based-Voice-Conversion-WebUI es un framework de modificación de voz basado en VITS. Las versiones 2.2.231006 y anteriores son vulnerables a la inyección de código. La variable ckpt_path2 recibe la entrada del usuario (por ejemplo, la ruta a un modelo) y la pasa a la función change_info_, que abre y lee el archivo en la ruta dada (excepto que cambia el final en la ruta a train.log) y pasa el contenido del archivo a eval, lo que puede provocar la ejecución remota de código. Al momento de la publicación, no se conocían parches."}], "references": [{"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L1452", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L1484", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L761", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2025-012_GHSL-2025-022_Retrieval-based-Voice-Conversion-WebUI/", "source": "<EMAIL>", "tags": []}]}
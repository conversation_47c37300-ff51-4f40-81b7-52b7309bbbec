{"cve_id": "CVE-2025-4368", "published_date": "2025-05-06T16:15:32.037", "last_modified_date": "2025-05-13T20:19:54.440", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in Tenda AC8 ***********. Affected is the function formGetRouterStatus of the file /goform/MtuSetMacWan. The manipulation of the argument shareSpeed leads to buffer overflow. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en Tenda AC8 ***********. La función formGetRouterStatus del archivo /goform/MtuSetMacWan está afectada. La manipulación del argumento shareSpeed provoca un desbordamiento del búfer. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/fjl1113/cve/blob/main/Tenda.md", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://vuldb.com/?ctiid.307488", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307488", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564812", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}
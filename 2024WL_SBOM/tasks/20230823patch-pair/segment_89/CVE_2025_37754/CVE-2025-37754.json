{"cve_id": "CVE-2025-37754", "published_date": "2025-05-01T13:15:54.157", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915/huc: Fix fence not released on early probe errors\n\nHuC delayed loading fence, introduced with commit 27536e03271da\n(\"drm/i915/huc: track delayed HuC load with a fence\"), is registered with\nobject tracker early on driver probe but unregistered only from driver\nremove, which is not called on early probe errors.  Since its memory is\nallocated under devres, then released anyway, it may happen to be\nallocated again to the fence and reused on future driver probes, resulting\nin kernel warnings that taint the kernel:\n\n<4> [309.731371] ------------[ cut here ]------------\n<3> [309.731373] ODEBUG: init destroyed (active state 0) object: ffff88813d7dd2e0 object type: i915_sw_fence hint: sw_fence_dummy_notify+0x0/0x20 [i915]\n<4> [309.731575] WARNING: CPU: 2 PID: 3161 at lib/debugobjects.c:612 debug_print_object+0x93/0xf0\n...\n<4> [309.731693] CPU: 2 UID: 0 PID: 3161 Comm: i915_module_loa Tainted: G     U             6.14.0-CI_DRM_16362-gf0fd77956987+ #1\n...\n<4> [309.731700] RIP: 0010:debug_print_object+0x93/0xf0\n...\n<4> [309.731728] Call Trace:\n<4> [309.731730]  <TASK>\n...\n<4> [309.731949]  __debug_object_init+0x17b/0x1c0\n<4> [309.731957]  debug_object_init+0x34/0x50\n<4> [309.732126]  __i915_sw_fence_init+0x34/0x60 [i915]\n<4> [309.732256]  intel_huc_init_early+0x4b/0x1d0 [i915]\n<4> [309.732468]  intel_uc_init_early+0x61/0x680 [i915]\n<4> [309.732667]  intel_gt_common_init_early+0x105/0x130 [i915]\n<4> [309.732804]  intel_root_gt_init_early+0x63/0x80 [i915]\n<4> [309.732938]  i915_driver_probe+0x1fa/0xeb0 [i915]\n<4> [309.733075]  i915_pci_probe+0xe6/0x220 [i915]\n<4> [309.733198]  local_pci_probe+0x44/0xb0\n<4> [309.733203]  pci_device_probe+0xf4/0x270\n<4> [309.733209]  really_probe+0xee/0x3c0\n<4> [309.733215]  __driver_probe_device+0x8c/0x180\n<4> [309.733219]  driver_probe_device+0x24/0xd0\n<4> [309.733223]  __driver_attach+0x10f/0x220\n<4> [309.733230]  bus_for_each_dev+0x7d/0xe0\n<4> [309.733236]  driver_attach+0x1e/0x30\n<4> [309.733239]  bus_add_driver+0x151/0x290\n<4> [309.733244]  driver_register+0x5e/0x130\n<4> [309.733247]  __pci_register_driver+0x7d/0x90\n<4> [309.733251]  i915_pci_register_driver+0x23/0x30 [i915]\n<4> [309.733413]  i915_init+0x34/0x120 [i915]\n<4> [309.733655]  do_one_initcall+0x62/0x3f0\n<4> [309.733667]  do_init_module+0x97/0x2a0\n<4> [309.733671]  load_module+0x25ff/0x2890\n<4> [309.733688]  init_module_from_file+0x97/0xe0\n<4> [309.733701]  idempotent_init_module+0x118/0x330\n<4> [309.733711]  __x64_sys_finit_module+0x77/0x100\n<4> [309.733715]  x64_sys_call+0x1f37/0x2650\n<4> [309.733719]  do_syscall_64+0x91/0x180\n<4> [309.733763]  entry_SYSCALL_64_after_hwframe+0x76/0x7e\n<4> [309.733792]  </TASK>\n...\n<4> [309.733806] ---[ end trace 0000000000000000 ]---\n\nThat scenario is most easily reproducible with\nigt@i915_module_load@reload-with-fault-injection.\n\nFix the issue by moving the cleanup step to driver release path.\n\n(cherry picked from commit 795dbde92fe5c6996a02a5b579481de73035e7bf)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915/huc: corrección de la valla no liberada en errores de sondeo tempranos La valla de carga retrasada de HuC, introducida con la confirmación 27536e03271da (\"drm/i915/huc: rastrear la carga retrasada de HuC con una valla\"), se registra con el rastreador de objetos en las primeras etapas del sondeo del controlador, pero se desregistra solo desde la eliminación del controlador, que no se llama en errores de sondeo tempranos. Dado que su memoria se asigna en devres y luego se libera de todos modos, puede suceder que se asigne nuevamente a la valla y se reutilice en futuras sondas del controlador, lo que genera advertencias del kernel que contaminan el kernel: &lt;4&gt; [309.731371] ------------[ cortar aquí ]------------ &lt;3&gt; [309.731373] ODEBUG: init destroyed (active state 0) object: ffff88813d7dd2e0 object type: i915_sw_fence hint: sw_fence_dummy_notify+0x0/0x20 [i915] &lt;4&gt; [309.731575] WARNING: CPU: 2 PID: 3161 at lib/debugobjects.c:612 debug_print_object+0x93/0xf0 ... &lt;4&gt; [309.731693] CPU: 2 UID: 0 PID: 3161 Comm: i915_module_loa Tainted: G U 6.14.0-CI_DRM_16362-gf0fd77956987+ #1 ... &lt;4&gt; [309.731700] RIP: 0010:debug_print_object+0x93/0xf0 ... &lt;4&gt; [309.731728] Call Trace: &lt;4&gt; [309.731730]  ... &lt;4&gt; [309.731949] __debug_object_init+0x17b/0x1c0 &lt;4&gt; [309.731957] debug_object_init+0x34/0x50 &lt;4&gt; [309.732126] __i915_sw_fence_init+0x34/0x60 [i915] &lt;4&gt; [309.732256] intel_huc_init_early+0x4b/0x1d0 [i915] &lt;4&gt; [309.732468] intel_uc_init_early+0x61/0x680 [i915] &lt;4&gt; [309.732667] intel_gt_common_init_early+0x105/0x130 [i915] &lt;4&gt; [309.732804] intel_root_gt_init_early+0x63/0x80 [i915] &lt;4&gt; [309.732938] i915_driver_probe+0x1fa/0xeb0 [i915] &lt;4&gt; [309.733075] i915_pci_probe+0xe6/0x220 [i915] &lt;4&gt; [309.733198] local_pci_probe+0x44/0xb0 &lt;4&gt; [309.733203] pci_device_probe+0xf4/0x270 &lt;4&gt; [309.733209] really_probe+0xee/0x3c0 &lt;4&gt; [309.733215] __driver_probe_device+0x8c/0x180 &lt;4&gt; [309.733219] driver_probe_device+0x24/0xd0 &lt;4&gt; [309.733223] __driver_attach+0x10f/0x220 &lt;4&gt; [309.733230] bus_for_each_dev+0x7d/0xe0 &lt;4&gt; [309.733236] driver_attach+0x1e/0x30 &lt;4&gt; [309.733239] bus_add_driver+0x151/0x290 &lt;4&gt; [309.733244] driver_register+0x5e/0x130 &lt;4&gt; [309.733247] __pci_register_driver+0x7d/0x90 &lt;4&gt; [309.733251] i915_pci_register_driver+0x23/0x30 [i915] &lt;4&gt; [309.733413] i915_init+0x34/0x120 [i915] &lt;4&gt; [309.733655] do_one_initcall+0x62/0x3f0 &lt;4&gt; [309.733667] do_init_module+0x97/0x2a0 &lt;4&gt; [309.733671] load_module+0x25ff/0x2890 &lt;4&gt; [309.733688] init_module_from_file+0x97/0xe0 &lt;4&gt; [309.733701] idempotent_init_module+0x118/0x330 &lt;4&gt; [309.733711] __x64_sys_finit_module+0x77/0x100 &lt;4&gt; [309.733715] x64_sys_call+0x1f37/0x2650 &lt;4&gt; [309.733719] do_syscall_64+0x91/0x180 &lt;4&gt; [309.733763] entry_SYSCALL_64_after_hwframe+0x76/0x7e &lt;4&gt; [309.733792]  ... &lt;4&gt; [309.733806] ---[ fin de seguimiento 000000000000000 ]--- Este escenario se reproduce con mayor facilidad con igt@i915_module_load@reload-with-fault-injection. Solucione el problema trasladando el paso de limpieza a la ruta de la versión del controlador. (Seleccionado de la confirmación 795dbde92fe5c6996a02a5b579481de73035e7bf)"}], "references": [{"url": "https://git.kernel.org/stable/c/4bd4bf79bcfe101f0385ab81dbabb6e3f7d96c00", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9f5ef4a5eaa61a7a4ed31231da45deb85065397a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c5a906806162aea62dbe5d327760ce3b7117ca17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3ea2eae70692a455e256787e4f54153fb739b90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f104ef4db9f8f3923cc06ed1fafb3da38df6006d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
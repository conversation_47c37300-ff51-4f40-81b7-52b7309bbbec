{"cve_id": "CVE-2025-24351", "published_date": "2025-04-30T12:15:21.937", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "A vulnerability in the “Remote Logging” functionality of the web application of ctrlX OS allows a remote authenticated (low-privileged) attacker to execute arbitrary OS commands in the context of user “root” via a crafted HTTP request."}, {"lang": "es", "value": "Una vulnerabilidad en la funcionalidad de “Registro remoto” de la aplicación web de ctrlX OS permite que un atacante remoto autenticado (con privilegios bajos) ejecute comandos arbitrarios del sistema operativo en el contexto del usuario “root” a través de una solicitud HTTP manipulada."}], "references": [{"url": "https://psirt.bosch.com/security-advisories/BOSCH-SA-640452.html", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37781", "published_date": "2025-05-01T14:15:42.020", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ni2c: cros-ec-tunnel: defer probe if parent EC is not present\n\nWhen i2c-cros-ec-tunnel and the EC driver are built-in, the EC parent\ndevice will not be found, leading to NULL pointer dereference.\n\nThat can also be reproduced by unbinding the controller driver and then\nloading i2c-cros-ec-tunnel module (or binding the device).\n\n[  271.991245] BUG: kernel NULL pointer dereference, address: 0000000000000058\n[  271.998215] #PF: supervisor read access in kernel mode\n[  272.003351] #PF: error_code(0x0000) - not-present page\n[  272.008485] PGD 0 P4D 0\n[  272.011022] Oops: Oops: 0000 [#1] SMP NOPTI\n[  272.015207] CPU: 0 UID: 0 PID: 3859 Comm: insmod Tainted: G S                  6.15.0-rc1-00004-g44722359ed83 #30 PREEMPT(full)  3c7fb39a552e7d949de2ad921a7d6588d3a4fdc5\n[  272.030312] Tainted: [S]=CPU_OUT_OF_SPEC\n[  272.034233] Hardware name: HP Berknip/Berknip, BIOS Google_Berknip.13434.356.0 05/17/2021\n[  272.042400] RIP: 0010:ec_i2c_probe+0x2b/0x1c0 [i2c_cros_ec_tunnel]\n[  272.048577] Code: 1f 44 00 00 41 57 41 56 41 55 41 54 53 48 83 ec 10 65 48 8b 05 06 a0 6c e7 48 89 44 24 08 4c 8d 7f 10 48 8b 47 50 4c 8b 60 78 <49> 83 7c 24 58 00 0f 84 2f 01 00 00 48 89 fb be 30 06 00 00 4c 9\n[  272.067317] RSP: 0018:ffffa32082a03940 EFLAGS: 00010282\n[  272.072541] RAX: ffff969580b6a810 RBX: ffff969580b68c10 RCX: 0000000000000000\n[  272.079672] RDX: 0000000000000000 RSI: 0000000000000282 RDI: ffff969580b68c00\n[  272.086804] RBP: 00000000fffffdfb R08: 0000000000000000 R09: 0000000000000000\n[  272.093936] R10: 0000000000000000 R11: ffffffffc0600000 R12: 0000000000000000\n[  272.101067] R13: ffffffffa666fbb8 R14: ffffffffc05b5528 R15: ffff969580b68c10\n[  272.108198] FS:  00007b930906fc40(0000) GS:ffff969603149000(0000) knlGS:0000000000000000\n[  272.116282] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[  272.122024] CR2: 0000000000000058 CR3: 000000012631c000 CR4: 00000000003506f0\n[  272.129155] Call Trace:\n[  272.131606]  <TASK>\n[  272.133709]  ? acpi_dev_pm_attach+0xdd/0x110\n[  272.137985]  platform_probe+0x69/0xa0\n[  272.141652]  really_probe+0x152/0x310\n[  272.145318]  __driver_probe_device+0x77/0x110\n[  272.149678]  driver_probe_device+0x1e/0x190\n[  272.153864]  __driver_attach+0x10b/0x1e0\n[  272.157790]  ? driver_attach+0x20/0x20\n[  272.161542]  bus_for_each_dev+0x107/0x150\n[  272.165553]  bus_add_driver+0x15d/0x270\n[  272.169392]  driver_register+0x65/0x110\n[  272.173232]  ? cleanup_module+0xa80/0xa80 [i2c_cros_ec_tunnel 3a00532f3f4af4a9eade753f86b0f8dd4e4e5698]\n[  272.182617]  do_one_initcall+0x110/0x350\n[  272.186543]  ? security_kernfs_init_security+0x49/0xd0\n[  272.191682]  ? __kernfs_new_node+0x1b9/0x240\n[  272.195954]  ? security_kernfs_init_security+0x49/0xd0\n[  272.201093]  ? __kernfs_new_node+0x1b9/0x240\n[  272.205365]  ? kernfs_link_sibling+0x105/0x130\n[  272.209810]  ? kernfs_next_descendant_post+0x1c/0xa0\n[  272.214773]  ? kernfs_activate+0x57/0x70\n[  272.218699]  ? kernfs_add_one+0x118/0x160\n[  272.222710]  ? __kernfs_create_file+0x71/0xa0\n[  272.227069]  ? sysfs_add_bin_file_mode_ns+0xd6/0x110\n[  272.232033]  ? internal_create_group+0x453/0x4a0\n[  272.236651]  ? __vunmap_range_noflush+0x214/0x2d0\n[  272.241355]  ? __free_frozen_pages+0x1dc/0x420\n[  272.245799]  ? free_vmap_area_noflush+0x10a/0x1c0\n[  272.250505]  ? load_module+0x1509/0x16f0\n[  272.254431]  do_init_module+0x60/0x230\n[  272.258181]  __se_sys_finit_module+0x27a/0x370\n[  272.262627]  do_syscall_64+0x6a/0xf0\n[  272.266206]  ? do_syscall_64+0x76/0xf0\n[  272.269956]  ? irqentry_exit_to_user_mode+0x79/0x90\n[  272.274836]  entry_SYSCALL_64_after_hwframe+0x55/0x5d\n[  272.279887] RIP: 0033:0x7b9309168d39\n[  272.283466] Code: 5b 41 5c 5d c3 66 2e 0f 1f 84 00 00 00 00 00 66 90 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 4c 8b 4c 24 08 0f 05 <48> 3d 01 f0 ff ff 73 01 c3 48 8b 0d af 40 0c 00 f7 d8 64 89 01 8\n[  272.302210] RSP: 002b:00007fff50f1a288 EFLAGS: 00000246 ORIG_RAX: 000\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: i2c: cros-ec-tunnel: aplazar la sonda si el EC principal no está presente. Cuando i2c-cros-ec-tunnel y el controlador EC están integrados, no se encuentra el dispositivo principal EC, lo que provoca una desreferencia de puntero nulo. Esto también se puede reproducir desvinculando el controlador y cargando el módulo i2c-cros-ec-tunnel (o vinculando el dispositivo). [ 271.991245] ERROR: desreferencia de puntero NULL del núcleo, dirección: 0000000000000058 [ 271.998215] #PF: acceso de lectura del supervisor en modo núcleo [ 272.003351] #PF: error_code(0x0000) - página no presente [ 272.008485] PGD 0 P4D 0 [ 272.011022] Oops: Oops: 0000 [#1] SMP NOPTI [ 272.015207] CPU: 0 UID: 0 PID: 3859 Comm: insmod Tainted: G S 6.15.0-rc1-00004-g44722359ed83 #30 PREEMPT(full) 3c7fb39a552e7d949de2ad921a7d6588d3a4fdc5 [ 272.030312] Tainted: [S]=CPU_OUT_OF_SPEC [ 272.034233] Hardware name: HP Berknip/Berknip, BIOS Google_Berknip.13434.356.0 05/17/2021 [ 272.042400] RIP: 0010:ec_i2c_probe+0x2b/0x1c0 [i2c_cros_ec_tunnel] [ 272.048577] Code: 1f 44 00 00 41 57 41 56 41 55 41 54 53 48 83 ec 10 65 48 8b 05 06 a0 6c e7 48 89 44 24 08 4c 8d 7f 10 48 8b 47 50 4c 8b 60 78 &lt;49&gt; 83 7c 24 58 00 0f 84 2f 01 00 00 48 89 fb be 30 06 00 00 4c 9 [ 272.067317] RSP: 0018:ffffa32082a03940 EFLAGS: 00010282 [ 272.072541] RAX: ffff969580b6a810 RBX: ffff969580b68c10 RCX: 0000000000000000 [ 272.079672] RDX: 0000000000000000 RSI: 0000000000000282 RDI: ffff969580b68c00 [ 272.086804] RBP: 00000000fffffdfb R08: 0000000000000000 R09: 0000000000000000 [ 272.093936] R10: 0000000000000000 R11: ffffffffc0600000 R12: 0000000000000000 [ 272.101067] R13: ffffffffa666fbb8 R14: ffffffffc05b5528 R15: ffff969580b68c10 [ 272.108198] FS: 00007b930906fc40(0000) GS:ffff969603149000(0000) knlGS:0000000000000000 [ 272.116282] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [ 272.122024] CR2: 0000000000000058 CR3: 000000012631c000 CR4: 00000000003506f0 [ 272.129155] Call Trace: [ 272.131606]  [ 272.133709] ? acpi_dev_pm_attach+0xdd/0x110 [ 272.137985] platform_probe+0x69/0xa0 [ 272.141652] really_probe+0x152/0x310 [ 272.145318] __driver_probe_device+0x77/0x110 [ 272.149678] driver_probe_device+0x1e/0x190 [ 272.153864] __driver_attach+0x10b/0x1e0 [ 272.157790] ? driver_attach+0x20/0x20 [ 272.161542] bus_for_each_dev+0x107/0x150 [ 272.165553] bus_add_driver+0x15d/0x270 [ 272.169392] driver_register+0x65/0x110 [ 272.173232] ? cleanup_module+0xa80/0xa80 [i2c_cros_ec_tunnel 3a00532f3f4af4a9eade753f86b0f8dd4e4e5698] [ 272.182617] do_one_initcall+0x110/0x350 [ 272.186543] ? security_kernfs_init_security+0x49/0xd0 [ 272.191682] ? __kernfs_new_node+0x1b9/0x240 [ 272.195954] ? security_kernfs_init_security+0x49/0xd0 [ 272.201093] ? __kernfs_new_node+0x1b9/0x240 [ 272.205365] ? kernfs_link_sibling+0x105/0x130 [ 272.209810] ? kernfs_next_descendant_post+0x1c/0xa0 [ 272.214773] ? kernfs_activate+0x57/0x70 [ 272.218699] ? kernfs_add_one+0x118/0x160 [ 272.222710] ? __kernfs_create_file+0x71/0xa0 [ 272.227069] ? sysfs_add_bin_file_mode_ns+0xd6/0x110 [ 272.232033] ? internal_create_group+0x453/0x4a0 [ 272.236651] ? __vunmap_range_noflush+0x214/0x2d0 [ 272.241355] ? __free_frozen_pages+0x1dc/0x420 [ 272.245799] ? free_vmap_area_noflush+0x10a/0x1c0 [ 272.250505] ? load_module+0x1509/0x16f0 [ 272.254431] do_init_module+0x60/0x230 [ 272.258181] __se_sys_finit_module+0x27a/0x370 [ 272.262627] do_syscall_64+0x6a/0xf0 [ 272.266206] ? do_syscall_64+0x76/0xf0 [ 272.269956] ? irqentry_exit_to_user_mode+0x79/0x90 [ 272.274836] entry_SYSCALL_64_after_hwframe+0x55/0x5d [ 272.279887] RIP: 0033:0x7b9309168d39 [ 272.283466] Code: 5b 41 5c 5d c3 66 2e 0f 1f 84 00 00 00 00 00 66 90 48 89 f8 48 89 f7 48 89 d6 48 89 ca 4d 89 c2 4d 89 c8 4c 8b 4c 24 08 0f 05 &lt;48&gt; 3d 01 f0 ff ff 73 01 c3 48 8b 0d af 40 0c 00 f7 d8 64 89 01 8 [ 272.302210] RSP: 002b:00007fff50f1a288 EFLAGS: 00000246 ORIG_RAX: 000 ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/092de5ac8cb2eaa9593a765fa92ba39d8173f984", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1355b5ca4782be85a2ef7275e4c508f770d0fb27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3090cad5ccff8963b95160f4060068048a1e4c4c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/424eafe65647a8d6c690284536e711977153195a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b66d4910a608427367c4e21499e149f085782df7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cd83035b6f2a102c2d5acd3bfb2a11ff967aaba6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da8edc9eb2516aface7f86be5fa6d09c0d07b9f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e89bf1311d4497c6743f3021e9c481b16c3a41c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
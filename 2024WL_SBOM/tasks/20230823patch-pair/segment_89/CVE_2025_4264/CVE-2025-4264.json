{"cve_id": "CVE-2025-4264", "published_date": "2025-05-05T05:15:15.860", "last_modified_date": "2025-05-07T16:33:07.823", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Emergency Ambulance Hiring Portal 1.0. Affected is an unknown function of the file /admin/edit-ambulance.php. The manipulation of the argument dconnum leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Emergency Ambulance Hiring Portal 1.0. La vulnerabilidad afecta a una función desconocida del archivo /admin/edit-ambulance.php. La manipulación del argumento dconnum provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/xiguala123/myCVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307368", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307368", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562992", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
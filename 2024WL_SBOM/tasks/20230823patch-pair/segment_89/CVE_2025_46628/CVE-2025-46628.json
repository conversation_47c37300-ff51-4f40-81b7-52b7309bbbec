{"cve_id": "CVE-2025-46628", "published_date": "2025-05-01T20:15:38.510", "last_modified_date": "2025-05-27T14:24:08.060", "descriptions": [{"lang": "en", "value": "Lack of input validation/sanitization in the 'ate' management service in the Tenda RX2 Pro *********** allows an unauthorized remote attacker to gain root shell access to the device by sending a crafted UDP packet to the 'ate' service when it is enabled. Authentication is not needed."}, {"lang": "es", "value": "La falta de validación/depuración de entrada en el servicio de gestión \"ate\" del Tenda RX2 Pro *********** permite que un atacante remoto no autorizado obtenga acceso root al dispositivo mediante el envío de un paquete UDP manipulado al servicio \"ate\" cuando este está habilitado. No se requiere autenticación."}], "references": [{"url": "https://blog.uturn.dev/#/writeups/iot-village/tenda-rx2pro/README?id=cve-2025-46628-command-injection-through-ifconfig-command-in-ate", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://www.tendacn.com/us/default.html", "source": "<EMAIL>", "tags": ["Product"]}]}
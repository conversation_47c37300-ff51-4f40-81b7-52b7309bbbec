{"cve_id": "CVE-2025-4096", "published_date": "2025-05-05T18:15:44.240", "last_modified_date": "2025-05-28T20:07:18.943", "descriptions": [{"lang": "en", "value": "Heap buffer overflow in HTML in Google Chrome prior to 136.0.7103.59 allowed a remote attacker to potentially exploit heap corruption via a crafted HTML page. (Chromium security severity: High)"}, {"lang": "es", "value": "Un desbordamiento del búfer de pila en HTML en Google Chrome anterior a la versión 136.0.7103.59 permitía a un atacante remoto explotar la corrupción de pila mediante una página HTML manipulada. (Gravedad de seguridad de Chromium: Alta)"}], "references": [{"url": "https://chromereleases.googleblog.com/2025/04/stable-channel-update-for-desktop_29.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://issues.chromium.org/issues/409911705", "source": "<EMAIL>", "tags": ["Permissions Required"]}]}
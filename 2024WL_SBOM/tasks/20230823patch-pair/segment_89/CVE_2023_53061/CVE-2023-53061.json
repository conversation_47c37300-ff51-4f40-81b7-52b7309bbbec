{"cve_id": "CVE-2023-53061", "published_date": "2025-05-02T16:15:25.163", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix possible refcount leak in smb2_open()\n\nReference count of acls will leak when memory allocation fails. Fix this\nby adding the missing posix_acl_release()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: se corrige una posible fuga de recuento de referencias en smb2_open(). El recuento de referencias de las ACL se filtra cuando falla la asignación de memoria. Se soluciona añadiendo la función posix_acl_release() que faltaba."}], "references": [{"url": "https://git.kernel.org/stable/c/2624b445544ffc1472ccabfb6ec867c199d4c95c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/303f8e58cc3ace744801dcdcabfc06ffc72ed62d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a22c49a05e5e7aa2c414fbc42c49c4c01a5c9a78", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c33344b7972225b232966f95d31f6312dcc6273d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
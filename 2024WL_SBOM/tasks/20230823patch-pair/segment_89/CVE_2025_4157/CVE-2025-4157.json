{"cve_id": "CVE-2025-4157", "published_date": "2025-05-01T09:15:15.513", "last_modified_date": "2025-05-07T19:39:10.090", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Boat Booking System 1.0 and classified as critical. This issue affects some unknown processing of the file /admin/booking-details.php. The manipulation of the argument Status leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Boat Booking System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/booking-details.php. La manipulación del argumento \"Status\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306689", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306689", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560863", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2023-53120", "published_date": "2025-05-02T16:15:31.083", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: mpi3mr: Fix config page DMA memory leak\n\nA fix for:\n\nDMA-API: pci 0000:83:00.0: device driver has pending DMA allocations while released from device [count=1]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: mpi3mr: Se corrige la pérdida de memoria DMA en la página de configuración. Una solución para: DMA-API: pci 0000:83:00.0: el controlador del dispositivo tiene asignaciones DMA pendientes mientras se libera del dispositivo [count=1]"}], "references": [{"url": "https://git.kernel.org/stable/c/5fc4d698ed4b6507be2eb36d040a678adcb89da4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d2b02172b6a2ae6aecd7ef6480b9c4bf3dc59f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dca06ccf13de14e144d34f158f73ae0032f80e63", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53044", "published_date": "2025-05-02T16:15:23.490", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndm stats: check for and propagate alloc_percpu failure\n\nCheck alloc_precpu()'s return value and return an error from\ndm_stats_init() if it fails. Update alloc_dev() to fail if\ndm_stats_init() does.\n\nOtherwise, a NULL pointer dereference will occur in dm_stats_cleanup()\neven if dm-stats isn't being actively used."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dm stats: comprobar y propagar el fallo de alloc_percpu. Comprueba el valor de retorno de alloc_precpu() y devuelve un error de dm_stats_init() si falla. Actualice alloc_dev() para que falle si dm_stats_init() falla. De lo contrario, se producirá una desreferencia de puntero nulo en dm_stats_cleanup(), incluso si dm-stats no se está utilizando activamente."}], "references": [{"url": "https://git.kernel.org/stable/c/0d96bd507ed7e7d565b6d53ebd3874686f123b2e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2287d7b721471a3d58bcd829250336e3cdf1635e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/443c9d522397511a4328dc2ec3c9c63c73049756", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4a32a9a818a895671bd43e0c40351e60e4e9140b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5b66e36a3efd24041b7374432bfa4dec2ff01e95", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a42180dd361584816bfe15c137b665699b994d90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c68f08cc745675a17894e1b4a5b5b9700ace6da4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d3aa3e060c4a80827eb801fc448debc9daa7c46b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
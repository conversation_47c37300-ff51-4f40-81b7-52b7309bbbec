{"cve_id": "CVE-2025-21468", "published_date": "2025-05-06T09:15:24.007", "last_modified_date": "2025-05-09T19:09:01.703", "descriptions": [{"lang": "en", "value": "Memory corruption while reading response from FW, when buffer size is changed by FW while driver is using this size to write null character at the end of buffer."}, {"lang": "es", "value": "Corrupción de memoria al leer la respuesta del FW, cuando el tamaño del búfer es modificado por el FW mientras el controlador usa este tamaño para escribir un carácter nulo al final del búfer."}], "references": [{"url": "https://docs.qualcomm.com/product/publicresources/securitybulletin/may-2025-bulletin.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}
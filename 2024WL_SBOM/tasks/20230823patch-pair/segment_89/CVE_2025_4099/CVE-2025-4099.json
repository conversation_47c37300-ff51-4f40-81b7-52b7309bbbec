{"cve_id": "CVE-2025-4099", "published_date": "2025-05-01T05:15:52.167", "last_modified_date": "2025-05-19T11:49:08.097", "descriptions": [{"lang": "en", "value": "The List Children plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'list_children' shortcode in all versions up to, and including, 2.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento List Children para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del shortcode 'list_children' en todas las versiones hasta la 2.1 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/list-children/trunk/list_children.php#L26", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3284430/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/538b4d4b-f8c6-44db-89d2-d345bfbfecb2?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
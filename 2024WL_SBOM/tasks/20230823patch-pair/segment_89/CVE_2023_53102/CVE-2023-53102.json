{"cve_id": "CVE-2023-53102", "published_date": "2025-05-02T16:15:29.127", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nice: xsk: disable txq irq before flushing hw\n\nice_qp_dis() intends to stop a given queue pair that is a target of xsk\npool attach/detach. One of the steps is to disable interrupts on these\nqueues. It currently is broken in a way that txq irq is turned off\n*after* HW flush which in turn takes no effect.\n\nice_qp_dis():\n-> ice_qvec_dis_irq()\n--> disable rxq irq\n--> flush hw\n-> ice_vsi_stop_tx_ring()\n-->disable txq irq\n\nBelow splat can be triggered by following steps:\n- start xdpsock WITHOUT loading xdp prog\n- run xdp_rxq_info with XDP_TX action on this interface\n- start traffic\n- terminate xdpsock\n\n[  256.312485] BUG: kernel NULL pointer dereference, address: 0000000000000018\n[  256.319560] #PF: supervisor read access in kernel mode\n[  256.324775] #PF: error_code(0x0000) - not-present page\n[  256.329994] PGD 0 P4D 0\n[  256.332574] Oops: 0000 [#1] PREEMPT SMP NOPTI\n[  256.337006] CPU: 3 PID: 32 Comm: ksoftirqd/3 Tainted: G           OE      6.2.0-rc5+ #51\n[  256.345218] Hardware name: Intel Corporation S2600WFT/S2600WFT, BIOS SE5C620.86B.02.01.0008.031920191559 03/19/2019\n[  256.355807] RIP: 0010:ice_clean_rx_irq_zc+0x9c/0x7d0 [ice]\n[  256.361423] Code: b7 8f 8a 00 00 00 66 39 ca 0f 84 f1 04 00 00 49 8b 47 40 4c 8b 24 d0 41 0f b7 45 04 66 25 ff 3f 66 89 04 24 0f 84 85 02 00 00 <49> 8b 44 24 18 0f b7 14 24 48 05 00 01 00 00 49 89 04 24 49 89 44\n[  256.380463] RSP: 0018:ffffc900088bfd20 EFLAGS: 00010206\n[  256.385765] RAX: 000000000000003c RBX: 0000000000000035 RCX: 000000000000067f\n[  256.393012] RDX: 0000000000000775 RSI: 0000000000000000 RDI: ffff8881deb3ac80\n[  256.400256] RBP: 000000000000003c R08: ffff889847982710 R09: 0000000000010000\n[  256.407500] R10: ffffffff82c060c0 R11: 0000000000000004 R12: 0000000000000000\n[  256.414746] R13: ffff88811165eea0 R14: ffffc9000d255000 R15: ffff888119b37600\n[  256.421990] FS:  0000000000000000(0000) GS:ffff8897e0cc0000(0000) knlGS:0000000000000000\n[  256.430207] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[  256.436036] CR2: 0000000000000018 CR3: 0000000005c0a006 CR4: 00000000007706e0\n[  256.443283] DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000\n[  256.450527] DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400\n[  256.457770] PKRU: 55555554\n[  256.460529] Call Trace:\n[  256.463015]  <TASK>\n[  256.465157]  ? ice_xmit_zc+0x6e/0x150 [ice]\n[  256.469437]  ice_napi_poll+0x46d/0x680 [ice]\n[  256.473815]  ? _raw_spin_unlock_irqrestore+0x1b/0x40\n[  256.478863]  __napi_poll+0x29/0x160\n[  256.482409]  net_rx_action+0x136/0x260\n[  256.486222]  __do_softirq+0xe8/0x2e5\n[  256.489853]  ? smpboot_thread_fn+0x2c/0x270\n[  256.494108]  run_ksoftirqd+0x2a/0x50\n[  256.497747]  smpboot_thread_fn+0x1c1/0x270\n[  256.501907]  ? __pfx_smpboot_thread_fn+0x10/0x10\n[  256.506594]  kthread+0xea/0x120\n[  256.509785]  ? __pfx_kthread+0x10/0x10\n[  256.513597]  ret_from_fork+0x29/0x50\n[  256.517238]  </TASK>\n\nIn fact, irqs were not disabled and napi managed to be scheduled and run\nwhile xsk_pool pointer was still valid, but SW ring of xdp_buff pointers\nwas already freed.\n\nTo fix this, call ice_qvec_dis_irq() after ice_vsi_stop_tx_ring(). Also\nwhile at it, remove redundant ice_clean_rx_ring() call - this is handled\nin ice_qp_clean_rings()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ice: xsk: deshabilitar la IRQ de txq antes de vaciar el hardware. ice_qp_dis() intenta detener un par de colas determinado que es objetivo de la conexión/desconexión del grupo xsk. Uno de los pasos consiste en deshabilitar las interrupciones en estas colas. Actualmente, el problema es que la IRQ de txq se desactiva *después* de vaciar el hardware, lo que no tiene efecto. ice_qp_dis(): -&gt; ice_qvec_dis_irq() --&gt; deshabilitar irq rxq --&gt; vaciar hw -&gt; ice_vsi_stop_tx_ring() --&gt; deshabilitar irq txq El splat que aparece a continuación se puede activar siguiendo los pasos: - iniciar xdpsock SIN cargar el programa xdp - ejecutar xdp_rxq_info con la acción XDP_TX en esta interfaz - iniciar tráfico - finalizar xdpsock [ 256.312485] ERROR: desreferencia de puntero NULL del kernel, dirección: 0000000000000018 [ 256.319560] #PF: acceso de lectura del supervisor en modo kernel [ 256.324775] #PF: error_code(0x0000) - página no presente [ 256.329994] PGD 0 P4D 0 [ 256.332574] Oops: 0000 [#1] PREEMPT SMP NOPTI [ 256.337006] CPU: 3 PID: 32 Comm: ksoftirqd/3 Contaminado: G OE 6.2.0-rc5+ #51 [ 256.345218] Nombre del hardware: Intel Corporation S2600WFT/S2600WFT, BIOS SE5C620.86B.02.01.0008.031920191559 03/19/2019 [ 256.355807] RIP: 0010:ice_clean_rx_irq_zc+0x9c/0x7d0 [ice] [ 256.361423] Code: b7 8f 8a 00 00 00 66 39 ca 0f 84 f1 04 00 00 49 8b 47 40 4c 8b 24 d0 41 0f b7 45 04 66 25 ff 3f 66 89 04 24 0f 84 85 02 00 00 &lt;49&gt; 8b 44 24 18 0f b7 14 24 48 05 00 01 00 00 49 89 04 24 49 89 44 [ 256.380463] RSP: 0018:ffffc900088bfd20 EFLAGS: 00010206 [ 256.385765] RAX: 000000000000003c RBX: 0000000000000035 RCX: 000000000000067f [ 256.393012] RDX: 0000000000000775 RSI: 0000000000000000 RDI: ffff8881deb3ac80 [ 256.400256] RBP: 000000000000003c R08: ffff889847982710 R09: 0000000000010000 [ 256.407500] R10: ffffffff82c060c0 R11: 0000000000000004 R12: 0000000000000000 [ 256.414746] R13: ffff88811165eea0 R14: ffffc9000d255000 R15: ffff888119b37600 [ 256.421990] FS: 0000000000000000(0000) GS:ffff8897e0cc0000(0000) knlGS:0000000000000000 [ 256.430207] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [ 256.436036] CR2: 0000000000000018 CR3: 0000000005c0a006 CR4: 00000000007706e0 [ 256.443283] DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000 [ 256.450527] DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400 [ 256.457770] PKRU: 55555554 [ 256.460529] Call Trace: [ 256.463015]  [ 256.465157] ? ice_xmit_zc+0x6e/0x150 [ice] [ 256.469437] ice_napi_poll+0x46d/0x680 [ice] [ 256.473815] ? _raw_spin_unlock_irqrestore+0x1b/0x40 [ 256.478863] __napi_poll+0x29/0x160 [ 256.482409] net_rx_action+0x136/0x260 [ 256.486222] __do_softirq+0xe8/0x2e5 [ 256.489853] ? smpboot_thread_fn+0x2c/0x270 [ 256.494108] run_ksoftirqd+0x2a/0x50 [ 256.497747] smpboot_thread_fn+0x1c1/0x270 [ 256.501907] ? __pfx_smpboot_thread_fn+0x10/0x10 [ 256.506594] kthread+0xea/0x120 [ 256.509785] ? __pfx_kthread+0x10/0x10 [ 256.513597] ret_from_fork+0x29/0x50 [ 256.517238]   De hecho, las IRQ no se deshabilitaron y napi logró programarse y ejecutarse mientras el puntero xsk_pool aún era válido, pero el anillo de SW de punteros xdp_buff ya estaba liberado. Para solucionar esto, llame a ice_qvec_dis_irq() después de ice_vsi_stop_tx_ring(). Además, elimine la llamada redundante a ice_clean_rx_ring(); esto se gestiona en ice_qp_clean_rings()."}], "references": [{"url": "https://git.kernel.org/stable/c/243cde8de10894d7812c8a6b62653bf04d8f9700", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2ecc6e44959382f95c9d427cd8da85121a9cecda", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b830c9642386867863ac64295185f896ff2928ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b89a453c6918e0f346fb0562e8c7812b94d28c73", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cccba1ff0798a27f7b8d0c06762ef977400a2afb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49922", "published_date": "2025-05-01T15:16:17.747", "last_modified_date": "2025-05-07T13:28:14.680", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfc: nfcmrvl: Fix potential memory leak in nfcmrvl_i2c_nci_send()\n\nnfcmrvl_i2c_nci_send() will be called by nfcmrvl_nci_send(), and skb\nshould be freed in nfcmrvl_i2c_nci_send(). However, nfcmrvl_nci_send()\nwill only free skb when i2c_master_send() return >=0, which means skb\nwill memleak when i2c_master_send() failed. Free skb no matter whether\ni2c_master_send() succeeds."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfc: nfcmrvl: Se corrige una posible fuga de memoria en nfcmrvl_i2c_nci_send(). nfcmrvl_i2c_nci_send() será llamado por nfcmrvl_nci_send(), y skb debería liberarse en nfcmrvl_i2c_nci_send(). Sin embargo, nfcmrvl_nci_send() solo liberará skb cuando i2c_master_send() devuelva &gt;=0, lo que significa que skb sufrirá fugas de memoria si i2c_master_send() falla. Skb se libera independientemente de si i2c_master_send() tiene éxito."}], "references": [{"url": "https://git.kernel.org/stable/c/52438e734c1566f5e2bcd9a065d2d65e306c0555", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5dfdac5e3f8db5f4445228c44f64091045644a3b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/825656ae61e73ddc05f585e6258d284c87064b10", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/92a1df9c6da20c02cf9872f8b025a66ddb307aeb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/93d904a734a74c54d945a9884b4962977f1176cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c8e7d4a1166f063703955f1b2e765a6db5bf1771", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/dd0ee55ead91fbb16889dbe7ff0b0f7c9e4e849d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f30060efcf18883748a0541aa41acef183cd9c0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
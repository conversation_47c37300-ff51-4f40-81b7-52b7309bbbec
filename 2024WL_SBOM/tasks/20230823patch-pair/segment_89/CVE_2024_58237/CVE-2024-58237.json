{"cve_id": "CVE-2024-58237", "published_date": "2025-05-05T15:15:54.010", "last_modified_date": "2025-05-09T08:15:19.030", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: consider that tail calls invalidate packet pointers\n\nTail-called programs could execute any of the helpers that invalidate\npacket pointers. Hence, conservatively assume that each tail call\ninvalidates packet pointers.\n\nMaking the change in bpf_helper_changes_pkt_data() automatically makes\nuse of check_cfg() logic that computes 'changes_pkt_data' effect for\nglobal sub-programs, such that the following program could be\nrejected:\n\n    int tail_call(struct __sk_buff *sk)\n    {\n    \tbpf_tail_call_static(sk, &jmp_table, 0);\n    \treturn 0;\n    }\n\n    SEC(\"tc\")\n    int not_safe(struct __sk_buff *sk)\n    {\n    \tint *p = (void *)(long)sk->data;\n    \t... make p valid ...\n    \ttail_call(sk);\n    \t*p = 42; /* this is unsafe */\n    \t...\n    }\n\nThe tc_bpf2bpf.c:subprog_tc() needs change: mark it as a function that\ncan invalidate packet pointers. Otherwise, it can't be freplaced with\ntailcall_freplace.c:entry_freplace() that does a tail call."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: considerar que las llamadas de cola invalidan los punteros de paquete. Los programas con llamadas de cola podrían ejecutar cualquiera de los ayudantes que invalidan los punteros de paquete. Por lo tanto, se asume, de forma conservadora, que cada llamada de cola invalida los punteros de paquete. Al realizar el cambio en bpf_helper_changes_pkt_data(), se utiliza automáticamente la lógica check_cfg(), que calcula el efecto de 'changes_pkt_data' para los subprogramas globales, de modo que el siguiente programa podría ser rechazado: int tail_call(struct __sk_buff *sk) { bpf_tail_call_static(sk, &amp;jmp_table, 0); return 0; } SEC(\"tc\") int not_safe(struct __sk_buff *sk) { int *p = (void *)(long)sk-&gt;data; ... make p valid ... tail_call(sk); *p = 42; /* esto no es seguro */ ... } La función tc_bpf2bpf.c:subprog_tc() debe modificarse: márquela como una función que puede invalidar punteros de paquetes. De lo contrario, no se puede reemplazar con tailcall_freplace.c:entry_freplace(), que realiza una llamada de cola."}], "references": [{"url": "https://git.kernel.org/stable/c/1a4607ffba35bf2a630aab299e34dd3f6e658d70", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1c2244437f9ad3dd91215f920401a14f2542dbfc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1692ee23dcaaddc24ba407b269707ee5df1301f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
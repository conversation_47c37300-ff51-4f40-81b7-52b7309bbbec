{"cve_id": "CVE-2025-4197", "published_date": "2025-05-02T02:15:17.687", "last_modified_date": "2025-05-28T16:02:23.317", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in code-projects Patient Record Management System 1.0. Affected is an unknown function of the file /edit_xpatient.php. The manipulation of the argument lastname leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en code-projects Patient Record Management System 1.0. Se ve afectada una función desconocida del archivo /edit_xpatient.php. La manipulación del argumento lastname provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/zhxu147/CVE/blob/main/hcpms_edit_xpatient.php_sqli.pdf", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.306810", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306810", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561890", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
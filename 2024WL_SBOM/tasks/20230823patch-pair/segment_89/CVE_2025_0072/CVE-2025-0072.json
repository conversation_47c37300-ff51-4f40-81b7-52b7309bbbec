{"cve_id": "CVE-2025-0072", "published_date": "2025-05-02T10:15:15.947", "last_modified_date": "2025-05-12T16:15:21.587", "descriptions": [{"lang": "en", "value": "Use After Free vulnerability in Arm Ltd Valhall GPU Kernel Driver, Arm Ltd Arm 5th Gen GPU Architecture Kernel Driver allows a local non-privileged user process to perform improper GPU memory processing operations to gain access to already freed memory.\n\nThis issue affects Valhall GPU Kernel Driver: from r29p0 through r49p3, from r50p0 through r53p0; Arm 5th Gen GPU Architecture Kernel Driver: from r41p0 through r49p3, from r50p0 through r53p0."}, {"lang": "es", "value": "La vulnerabilidad \"Use After Free\" en el controlador del kernel de GPU Valhall de Arm Ltd. El controlador del kernel de la arquitectura de GPU Arm de 5.ª generación de Arm Ltd permite que un proceso de usuario local sin privilegios realice operaciones incorrectas de procesamiento de memoria de GPU para acceder a la memoria ya liberada. Este problema afecta al controlador del kernel de GPU Valhall: de r29p0 a r49p3 y de r50p0 a r53p0; y al controlador del kernel de la arquitectura de GPU Arm de 5.ª generación: de r41p0 a r49p3 y de r50p0 a r53p0."}], "references": [{"url": "https://developer.arm.com/documentation/110465/latest/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
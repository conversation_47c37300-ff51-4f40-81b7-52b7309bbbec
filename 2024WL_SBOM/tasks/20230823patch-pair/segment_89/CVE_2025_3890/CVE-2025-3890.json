{"cve_id": "CVE-2025-3890", "published_date": "2025-05-01T12:15:17.830", "last_modified_date": "2025-05-06T14:55:31.320", "descriptions": [{"lang": "en", "value": "The WordPress Simple Shopping Cart plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'wp_cart_button' shortcode in all versions up to, and including, 5.1.3 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Simple Shopping Cart para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del shortcode 'wp_cart_button' en todas las versiones hasta la 5.1.3 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wordpress-simple-paypal-shopping-cart/tags/5.1.2/includes/wpsc-shortcodes-related.php#L20", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3284572/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.tipsandtricks-hq.com/ecommerce/simple-wp-shopping-cart-installation-usage-290#step-1-inserting-an-add-to-cart-button", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/93898bf8-cfed-44bf-9d68-a0167beba86a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
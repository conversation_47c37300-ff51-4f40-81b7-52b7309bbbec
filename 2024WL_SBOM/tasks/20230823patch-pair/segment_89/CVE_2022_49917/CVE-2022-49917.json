{"cve_id": "CVE-2022-49917", "published_date": "2025-05-01T15:16:16.927", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipvs: fix WARNING in ip_vs_app_net_cleanup()\n\nDuring the initialization of ip_vs_app_net_init(), if file ip_vs_app\nfails to be created, the initialization is successful by default.\nTherefore, the ip_vs_app file doesn't be found during the remove in\nip_vs_app_net_cleanup(). It will cause WRNING.\n\nThe following is the stack information:\nname 'ip_vs_app'\nWARNING: CPU: 1 PID: 9 at fs/proc/generic.c:712 remove_proc_entry+0x389/0x460\nModules linked in:\nWorkqueue: netns cleanup_net\nRIP: 0010:remove_proc_entry+0x389/0x460\nCall Trace:\n<TASK>\nops_exit_list+0x125/0x170\ncleanup_net+0x4ea/0xb00\nprocess_one_work+0x9bf/0x1710\nworker_thread+0x665/0x1080\nkthread+0x2e4/0x3a0\nret_from_fork+0x1f/0x30\n</TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipvs: corrección de una advertencia en ip_vs_app_net_cleanup(). Durante la inicialización de ip_vs_app_net_init(), si no se crea el archivo ip_vs_app, la inicialización se realiza correctamente por defecto. Por lo tanto, el archivo ip_vs_app no se encuentra durante la eliminación en ip_vs_app_net_cleanup(). Esto provocará un error WRNING. La siguiente es la información de la pila: nombre 'ip_vs_app' ADVERTENCIA: CPU: 1 PID: 9 en fs/proc/generic.c:712 remove_proc_entry+0x389/0x460 Módulos vinculados en: Cola de trabajo: netns cleanup_net RIP: 0010:remove_proc_entry+0x389/0x460 Seguimiento de llamadas:  ops_exit_list+0x125/0x170 cleanup_net+0x4ea/0xb00 process_one_work+0x9bf/0x1710 worker_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30  "}], "references": [{"url": "https://git.kernel.org/stable/c/06d7596d18725f1a93cf817662d36050e5afb989", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2c8d81bdb2684d53d6cedad7410ba4cf9090e343", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5663ed63adb9619c98ab7479aa4606fa9b7a548c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8457a00c981fe1a799ce34123908856b0f5973b8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/97f872b00937f2689bff2dab4ad9ed259482840f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/adc76740ccd52e4a1d910767cd1223e134a7078b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
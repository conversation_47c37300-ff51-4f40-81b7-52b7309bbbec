{"cve_id": "CVE-2023-53138", "published_date": "2025-05-02T16:15:32.720", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: caif: Fix use-after-free in cfusbl_device_notify()\n\nsyzbot reported use-after-free in cfusbl_device_notify() [1].  This\ncauses a stack trace like below:\n\nBUG: KASAN: use-after-free in cfusbl_device_notify+0x7c9/0x870 net/caif/caif_usb.c:138\nRead of size 8 at addr ffff88807ac4e6f0 by task kworker/u4:6/1214\n\nCPU: 0 PID: 1214 Comm: kworker/u4:6 Not tainted 5.19.0-rc3-syzkaller-00146-g92f20ff72066 #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 01/01/2011\nWorkqueue: netns cleanup_net\nCall Trace:\n <TASK>\n __dump_stack lib/dump_stack.c:88 [inline]\n dump_stack_lvl+0xcd/0x134 lib/dump_stack.c:106\n print_address_description.constprop.0.cold+0xeb/0x467 mm/kasan/report.c:313\n print_report mm/kasan/report.c:429 [inline]\n kasan_report.cold+0xf4/0x1c6 mm/kasan/report.c:491\n cfusbl_device_notify+0x7c9/0x870 net/caif/caif_usb.c:138\n notifier_call_chain+0xb5/0x200 kernel/notifier.c:87\n call_netdevice_notifiers_info+0xb5/0x130 net/core/dev.c:1945\n call_netdevice_notifiers_extack net/core/dev.c:1983 [inline]\n call_netdevice_notifiers net/core/dev.c:1997 [inline]\n netdev_wait_allrefs_any net/core/dev.c:10227 [inline]\n netdev_run_todo+0xbc0/0x10f0 net/core/dev.c:10341\n default_device_exit_batch+0x44e/0x590 net/core/dev.c:11334\n ops_exit_list+0x125/0x170 net/core/net_namespace.c:167\n cleanup_net+0x4ea/0xb00 net/core/net_namespace.c:594\n process_one_work+0x996/0x1610 kernel/workqueue.c:2289\n worker_thread+0x665/0x1080 kernel/workqueue.c:2436\n kthread+0x2e9/0x3a0 kernel/kthread.c:376\n ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:302\n </TASK>\n\nWhen unregistering a net device, unregister_netdevice_many_notify()\nsets the device's reg_state to NETREG_UNREGISTERING, calls notifiers\nwith NETDEV_UNREGISTER, and adds the device to the todo list.\n\nLater on, devices in the todo list are processed by netdev_run_todo().\nnetdev_run_todo() waits devices' reference count become 1 while\nrebdoadcasting NETDEV_UNREGISTER notification.\n\nWhen cfusbl_device_notify() is called with NETDEV_UNREGISTER multiple\ntimes, the parent device might be freed.  This could cause UAF.\nProcessing NETDEV_UNREGISTER multiple times also causes inbalance of\nreference count for the module.\n\nThis patch fixes the issue by accepting only first NETDEV_UNREGISTER\nnotification."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: caif: Se corrige el use-after-free en cfusbl_device_notify() syzbot informó el use-after-free en cfusbl_device_notify() [1]. Esto provoca un seguimiento de pila como el siguiente: ERROR: KASAN: use-after-free en cfusbl_device_notify+0x7c9/0x870 net/caif/caif_usb.c:138 Lectura de tamaño 8 en la dirección ffff88807ac4e6f0 por la tarea kworker/u4:6/1214 CPU: 0 PID: 1214 Comm: kworker/u4:6 No contaminado 5.19.0-rc3-syzkaller-00146-g92f20ff72066 #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 01/01/2011 Cola de trabajo: netns cleanup_net Seguimiento de llamadas:  __dump_stack lib/dump_stack.c:88 [inline] dump_stack_lvl+0xcd/0x134 lib/dump_stack.c:106 print_address_description.constprop.0.cold+0xeb/0x467 mm/kasan/report.c:313 print_report mm/kasan/report.c:429 [inline] kasan_report.cold+0xf4/0x1c6 mm/kasan/report.c:491 cfusbl_device_notify+0x7c9/0x870 net/caif/caif_usb.c:138 notifier_call_chain+0xb5/0x200 kernel/notifier.c:87 call_netdevice_notifiers_info+0xb5/0x130 net/core/dev.c:1945 call_netdevice_notifiers_extack net/core/dev.c:1983 [inline] call_netdevice_notifiers net/core/dev.c:1997 [inline] netdev_wait_allrefs_any net/core/dev.c:10227 [inline] netdev_run_todo+0xbc0/0x10f0 net/core/dev.c:10341 default_device_exit_batch+0x44e/0x590 net/core/dev.c:11334 ops_exit_list+0x125/0x170 net/core/net_namespace.c:167 cleanup_net+0x4ea/0xb00 net/core/net_namespace.c:594 process_one_work+0x996/0x1610 kernel/workqueue.c:2289 worker_thread+0x665/0x1080 kernel/workqueue.c:2436 kthread+0x2e9/0x3a0 kernel/kthread.c:376 ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:302  Al anular el registro de un dispositivo de red, unregister_netdevice_many_notify() establece el estado del dispositivo en NETREG_UNREGISTERING, llama a los notificadores con NETDEV_UNREGISTER y añade el dispositivo a la lista de tareas pendientes. Posteriormente, netdev_run_todo() procesa los dispositivos de la lista de tareas pendientes. netdev_run_todo() espera a que el recuento de referencias de los dispositivos llegue a 1 mientras retransmite la notificación NETDEV_UNREGISTER. Si se llama a cfusbl_device_notify() con NETDEV_UNREGISTER varias veces, el dispositivo principal podría liberarse. Esto podría causar un UAF. Procesar NETDEV_UNREGISTER varias veces también provoca un desequilibrio en el recuento de referencias del módulo. Este parche soluciona el problema aceptando solo la primera notificación NETDEV_UNREGISTER."}], "references": [{"url": "https://git.kernel.org/stable/c/1793da97a23e31c5bf06631f3f3e5a25f368fd64", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/287027d8a567168a5d8ce5cb0cba16a34791a48c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3f14457e1584224f4296af613bbd99deb60b5d91", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/68a45c3cf0e2242a533657f4f535d9b6a7447a79", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9781e98a97110f5e76999058368b4be76a788484", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9dc16be373b382ddd4c274052a6e870a95e76c01", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c3aaec463a632cf4187dc017e421bfa69d7834a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d1a11bbdbb5ea9f172019c5a4a3e9d8eabd72179", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
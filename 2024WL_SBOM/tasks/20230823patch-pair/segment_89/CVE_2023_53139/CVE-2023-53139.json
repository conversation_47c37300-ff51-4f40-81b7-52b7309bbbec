{"cve_id": "CVE-2023-53139", "published_date": "2025-05-02T16:15:32.817", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfc: fdp: add null check of devm_kmalloc_array in fdp_nci_i2c_read_device_properties\n\ndevm_kmalloc_array may fails, *fw_vsc_cfg might be null and cause\nout-of-bounds write in device_property_read_u8_array later."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfc: fdp: agregar comprobación nula de devm_kmalloc_array en fdp_nci_i2c_read_device_properties devm_kmalloc_array puede fallar, *fw_vsc_cfg puede ser nulo y provocar una escritura fuera de los límites en device_property_read_u8_array más adelante."}], "references": [{"url": "https://git.kernel.org/stable/c/0a3664a1058d4b2b1ea2112cc275ca47fba7fc08", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/11f180a5d62a51b484e9648f9b310e1bd50b1a57", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/27824b2f98818215adc9661e563252c48dab1a13", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4357bbb921fe9e81d0fd9f70d669d1f177d8380e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/80be62358fa5507cefbaa067c7e6648401f2c3da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/98f49e693e02c1dafd5786be3468657840dd6f06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad11b872bc9b5d27e56183c6b01f9218c85395d2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ce93f1afc05941a572f5a69e2ed4012af905a693", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
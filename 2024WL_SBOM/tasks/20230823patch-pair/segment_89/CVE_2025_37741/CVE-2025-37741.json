{"cve_id": "CVE-2025-37741", "published_date": "2025-05-01T13:15:52.723", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\njfs: Prevent copying of nlink with value 0 from disk inode\n\nsyzbot report a deadlock in diFree. [1]\n\nWhen calling \"ioctl$LOOP_SET_STATUS64\", the offset value passed in is 4,\nwhich does not match the mounted loop device, causing the mapping of the\nmounted loop device to be invalidated.\n\nWhen creating the directory and creating the inode of iag in diReadSpecial(),\nread the page of fixed disk inode (AIT) in raw mode in read_metapage(), the\nmetapage data it returns is corrupted, which causes the nlink value of 0 to be\nassigned to the iag inode when executing copy_from_dinode(), which ultimately\ncauses a deadlock when entering diFree().\n\nTo avoid this, first check the nlink value of dinode before setting iag inode.\n\n[1]\nWARNING: possible recursive locking detected\n6.12.0-rc7-syzkaller-00212-g4a5df3796467 #0 Not tainted\n--------------------------------------------\nsyz-executor301/5309 is trying to acquire lock:\nffff888044548920 (&(imap->im_aglock[index])){+.+.}-{3:3}, at: diFree+0x37c/0x2fb0 fs/jfs/jfs_imap.c:889\n\nbut task is already holding lock:\nffff888044548920 (&(imap->im_aglock[index])){+.+.}-{3:3}, at: diAlloc+0x1b6/0x1630\n\nother info that might help us debug this:\n Possible unsafe locking scenario:\n\n       CPU0\n       ----\n  lock(&(imap->im_aglock[index]));\n  lock(&(imap->im_aglock[index]));\n\n *** DEADLOCK ***\n\n May be due to missing lock nesting notation\n\n5 locks held by syz-executor301/5309:\n #0: ffff8880422a4420 (sb_writers#9){.+.+}-{0:0}, at: mnt_want_write+0x3f/0x90 fs/namespace.c:515\n #1: ffff88804755b390 (&type->i_mutex_dir_key#6/1){+.+.}-{3:3}, at: inode_lock_nested include/linux/fs.h:850 [inline]\n #1: ffff88804755b390 (&type->i_mutex_dir_key#6/1){+.+.}-{3:3}, at: filename_create+0x260/0x540 fs/namei.c:4026\n #2: ffff888044548920 (&(imap->im_aglock[index])){+.+.}-{3:3}, at: diAlloc+0x1b6/0x1630\n #3: ffff888044548890 (&imap->im_freelock){+.+.}-{3:3}, at: diNewIAG fs/jfs/jfs_imap.c:2460 [inline]\n #3: ffff888044548890 (&imap->im_freelock){+.+.}-{3:3}, at: diAllocExt fs/jfs/jfs_imap.c:1905 [inline]\n #3: ffff888044548890 (&imap->im_freelock){+.+.}-{3:3}, at: diAllocAG+0x4b7/0x1e50 fs/jfs/jfs_imap.c:1669\n #4: ffff88804755a618 (&jfs_ip->rdwrlock/1){++++}-{3:3}, at: diNewIAG fs/jfs/jfs_imap.c:2477 [inline]\n #4: ffff88804755a618 (&jfs_ip->rdwrlock/1){++++}-{3:3}, at: diAllocExt fs/jfs/jfs_imap.c:1905 [inline]\n #4: ffff88804755a618 (&jfs_ip->rdwrlock/1){++++}-{3:3}, at: diAllocAG+0x869/0x1e50 fs/jfs/jfs_imap.c:1669\n\nstack backtrace:\nCPU: 0 UID: 0 PID: 5309 Comm: syz-executor301 Not tainted 6.12.0-rc7-syzkaller-00212-g4a5df3796467 #0\nHardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-debian-1.16.3-2~bpo12+1 04/01/2014\nCall Trace:\n <TASK>\n __dump_stack lib/dump_stack.c:94 [inline]\n dump_stack_lvl+0x241/0x360 lib/dump_stack.c:120\n print_deadlock_bug+0x483/0x620 kernel/locking/lockdep.c:3037\n check_deadlock kernel/locking/lockdep.c:3089 [inline]\n validate_chain+0x15e2/0x5920 kernel/locking/lockdep.c:3891\n __lock_acquire+0x1384/0x2050 kernel/locking/lockdep.c:5202\n lock_acquire+0x1ed/0x550 kernel/locking/lockdep.c:5825\n __mutex_lock_common kernel/locking/mutex.c:608 [inline]\n __mutex_lock+0x136/0xd70 kernel/locking/mutex.c:752\n diFree+0x37c/0x2fb0 fs/jfs/jfs_imap.c:889\n jfs_evict_inode+0x32d/0x440 fs/jfs/inode.c:156\n evict+0x4e8/0x9b0 fs/inode.c:725\n diFreeSpecial fs/jfs/jfs_imap.c:552 [inline]\n duplicateIXtree+0x3c6/0x550 fs/jfs/jfs_imap.c:3022\n diNewIAG fs/jfs/jfs_imap.c:2597 [inline]\n diAllocExt fs/jfs/jfs_imap.c:1905 [inline]\n diAllocAG+0x17dc/0x1e50 fs/jfs/jfs_imap.c:1669\n diAlloc+0x1d2/0x1630 fs/jfs/jfs_imap.c:1590\n ialloc+0x8f/0x900 fs/jfs/jfs_inode.c:56\n jfs_mkdir+0x1c5/0xba0 fs/jfs/namei.c:225\n vfs_mkdir+0x2f9/0x4f0 fs/namei.c:4257\n do_mkdirat+0x264/0x3a0 fs/namei.c:4280\n __do_sys_mkdirat fs/namei.c:4295 [inline]\n __se_sys_mkdirat fs/namei.c:4293 [inline]\n __x64_sys_mkdirat+0x87/0xa0 fs/namei.c:4293\n do_syscall_x64 arch/x86/en\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: jfs: Impide la copia de nlink con valor 0 desde el inodo de disco. syzbot informa de un bloqueo en diFree. [1] Al llamar a \"ioctl$LOOP_SET_STATUS64\", el valor de desplazamiento pasado es 4, que no coincide con el dispositivo de bucle montado, lo que invalida la asignación de dicho dispositivo. Al crear el directorio y el inodo de iag en diReadSpecial(), al leer la página del inodo de disco fijo (AIT) en modo sin procesar en read_metapage(), los datos de metapágina que devuelve están dañados, lo que provoca que el valor nlink de 0 se asigne al inodo iag al ejecutar copy_from_dinode(), lo que finalmente provoca un bloqueo al acceder a diFree(). Para evitar esto, compruebe primero el valor nlink de dinode antes de configurar el inodo iag. [1] ADVERTENCIA: se detectó un posible bloqueo recursivo 6.12.0-rc7-syzkaller-00212-g4a5df3796467 #0 No contaminado -------------------------------------------- syz-executor301/5309 está intentando adquirir el bloqueo: ffff888044548920 (&amp;(imap-&gt;im_aglock[index])){+.+.}-{3:3}, en: diFree+0x37c/0x2fb0 fs/jfs/jfs_imap.c:889 pero la tarea ya tiene el bloqueo: ffff888044548920 (&amp;(imap-&gt;im_aglock[index])){+.+.}-{3:3}, en: diAlloc+0x1b6/0x1630 Otra información que podría ayudarnos a depurar esto: Posible escenario de bloqueo inseguro: CPU0 ---- bloqueo(&amp;(imap-&gt;im_aglock[índice])); bloqueo(&amp;(imap-&gt;im_aglock[índice])); *** BLOQUEO INTERMEDIO *** Puede deberse a la falta de notación de anidamiento de bloqueos. 5 bloqueos mantenidos por syz-executor301/5309: #0: ffff8880422a4420 (sb_writers#9){.+.+}-{0:0}, en: mnt_want_write+0x3f/0x90 fs/namespace.c:515 #1: ffff88804755b390 (&amp;type-&gt;i_mutex_dir_key#6/1){+.+.}-{3:3}, en: inode_lock_nested include/linux/fs.h:850 [en línea] #1: ffff88804755b390 (&amp;type-&gt;i_mutex_dir_key#6/1){+.+.}-{3:3}, en: nombre_archivo_crear+0x260/0x540 fs/nombrei.c:4026 #2: ffff888044548920 (&amp;(imap-&gt;im_aglock[índice])){+.+.}-{3:3}, en: diAlloc+0x1b6/0x1630 #3: ffff888044548890 (&amp;imap-&gt;im_freelock){+.+.}-{3:3}, en: diNewIAG fs/jfs/jfs_imap.c:2460 [en línea] #3: ffff888044548890 (&amp;imap-&gt;im_freelock){+.+.}-{3:3}, en: diAllocExt fs/jfs/jfs_imap.c:1905 [en línea] #3: ffff888044548890 (&amp;imap-&gt;im_freelock){+.+.}-{3:3}, en: diAllocAG+0x4b7/0x1e50 fs/jfs/jfs_imap.c:1669 #4: ffff88804755a618 (&amp;jfs_ip-&gt;rdwrlock/1){++++}-{3:3}, en: diNewIAG fs/jfs/jfs_imap.c:2477 [en línea] #4: ffff88804755a618 (&amp;jfs_ip-&gt;rdwrlock/1){++++}-{3:3}, en: diAllocExt fs/jfs/jfs_imap.c:1905 [en línea] #4: ffff88804755a618 (&amp;jfs_ip-&gt;rdwrlock/1){++++}-{3:3}, en: diAllocAG+0x869/0x1e50 fs/jfs/jfs_imap.c:1669 seguimiento de pila: CPU: 0 UID: 0 PID: 5309 Comm: syz-executor301 No contaminado 6.12.0-rc7-syzkaller-00212-g4a5df3796467 #0 Nombre del hardware: PC estándar QEMU (Q35 + ICH9, 2009), BIOS 1.16.3-debian-1.16.3-2~bpo12+1 01/04/2014 Seguimiento de llamadas:  __dump_stack lib/dump_stack.c:94 [en línea] dump_stack_lvl+0x241/0x360 lib/dump_stack.c:120 print_deadlock_bug+0x483/0x620 kernel/locking/lockdep.c:3037 check_deadlock kernel/locking/lockdep.c:3089 [en línea] validate_chain+0x15e2/0x5920 kernel/locking/lockdep.c:3891 __lock_acquire+0x1384/0x2050 kernel/locking/lockdep.c:5202 lock_acquire+0x1ed/0x550 kernel/locking/lockdep.c:5825 __mutex_lock_common kernel/locking/mutex.c:608 [en línea] __mutex_lock+0x136/0xd70 kernel/locking/mutex.c:752 diFree+0x37c/0x2fb0 fs/jfs/jfs_imap.c:889 jfs_evict_inode+0x32d/0x440 fs/jfs/inode.c:156 evict+0x4e8/0x9b0 fs/inode.c:725 diFreeSpecial fs/jfs/jfs_imap.c:552 [en línea] duplicateIXtree+0x3c6/0x550 fs/jfs/jfs_imap.c:3022 diNewIAG fs/jfs/jfs_imap.c:2597 [en línea] diAllocExt fs/jfs/jfs_imap.c:1905 [en línea] diAllocAG+0x17dc/0x1e50 fs/jfs/jfs_imap.c:1669 diAlloc+0x1d2/0x1630 fs/jfs/jfs_imap.c:1590 ialloc+0x8f/0x900 fs/jfs/jfs_inode.c:56 jfs_mkdir+0x1c5/0xba0 fs/jfs/namei.c:225 vfs_mkdir+0x2f9/0x4f0 fs/namei.c:4257 do_mkdirat+0x264/0x3a0 fs/namei.c:4280 __do_sys_mkdirat fs/namei.c:4295 [en línea] __se_sys_mkdirat fs/namei.c:4293 [en "}], "references": [{"url": "https://git.kernel.org/stable/c/5b2f26d3fba4e9aac314f8bc0963b3fc28c0e456", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/86bfeaa18f9e4615b97f2d613e0fcc4ced196527", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8b5ce75f8bd3ddf480cc0a240d7ff5cdea0444f9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/994787341358816d91b2fded288ecb7f129f2b27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a2b560815528ae8e266fca6038bb5585d13aaef4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aeb926e605f97857504bdf748f575e40617e2ef9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b3c4884b987e5d8d0ec061a4d52653c4f4b9c37e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b61e69bb1c049cf507e3c654fa3dc1568231bd07", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c9541c2bd0edbdbc5c1148a84d3b48dc8d1b8af2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
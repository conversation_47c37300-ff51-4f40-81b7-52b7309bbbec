{"cve_id": "CVE-2023-53134", "published_date": "2025-05-02T16:15:32.353", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbnxt_en: Avoid order-5 memory allocation for TPA data\n\nThe driver needs to keep track of all the possible concurrent TPA (GRO/LRO)\ncompletions on the aggregation ring.  On P5 chips, the maximum number\nof concurrent TPA is 256 and the amount of memory we allocate is order-5\non systems using 4K pages.  Memory allocation failure has been reported:\n\nNetworkManager: page allocation failure: order:5, mode:0x40dc0(GFP_KERNEL|__GFP_COMP|__GFP_ZERO), nodemask=(null),cpuset=/,mems_allowed=0-1\nCPU: 15 PID: 2995 Comm: NetworkManager Kdump: loaded Not tainted 5.10.156 #1\nHardware name: Dell Inc. PowerEdge R660/0M1CC5, BIOS 0.2.25 08/12/2022\nCall Trace:\n dump_stack+0x57/0x6e\n warn_alloc.cold.120+0x7b/0xdd\n ? _cond_resched+0x15/0x30\n ? __alloc_pages_direct_compact+0x15f/0x170\n __alloc_pages_slowpath.constprop.108+0xc58/0xc70\n __alloc_pages_nodemask+0x2d0/0x300\n kmalloc_order+0x24/0xe0\n kmalloc_order_trace+0x19/0x80\n bnxt_alloc_mem+0x1150/0x15c0 [bnxt_en]\n ? bnxt_get_func_stat_ctxs+0x13/0x60 [bnxt_en]\n __bnxt_open_nic+0x12e/0x780 [bnxt_en]\n bnxt_open+0x10b/0x240 [bnxt_en]\n __dev_open+0xe9/0x180\n __dev_change_flags+0x1af/0x220\n dev_change_flags+0x21/0x60\n do_setlink+0x35c/0x1100\n\nInstead of allocating this big chunk of memory and dividing it up for the\nconcurrent TPA instances, allocate each small chunk separately for each\nTPA instance.  This will reduce it to order-0 allocations."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bnxt_en: Evita la asignación de memoria de orden 5 para datos TPA. El controlador debe registrar todas las posibles finalizaciones simultáneas de TPA (GRO/LRO) en el anillo de agregación. En chips P5, el número máximo de TPA simultáneas es de 256 y la cantidad de memoria que asignamos es de orden 5 en sistemas que utilizan páginas de 4K. Se informó un error de asignación de memoria: NetworkManager: error de asignación de página: orden: 5, modo: 0x40dc0 (GFP_KERNEL | __GFP_COMP | __GFP_ZERO), máscara de nodo = (null), conjunto de CPU = /, mems_allowed = 0-1 CPU: 15 PID: 2995 Comm: NetworkManager Kdump: cargado No contaminado 5.10.156 # 1 Nombre del hardware: Dell Inc. PowerEdge R660 / 0M1CC5, BIOS 0.2.25 12/08/2022 Seguimiento de llamadas: dump_stack + 0x57 / 0x6e warn_alloc.cold.120 + 0x7b / 0xdd ? _cond_resched + 0x15 / 0x30 ? __alloc_pages_direct_compact+0x15f/0x170 __alloc_pages_slowpath.constprop.108+0xc58/0xc70 __alloc_pages_nodemask+0x2d0/0x300 kmalloc_order+0x24/0xe0 kmalloc_order_trace+0x19/0x80 bnxt_alloc_mem+0x1150/0x15c0 [bnxt_es] ? bnxt_get_func_stat_ctxs+0x13/0x60 [bnxt_es] __bnxt_open_nic+0x12e/0x780 [bnxt_es] bnxt_open+0x10b/0x240 [bnxt_es] __dev_open+0xe9/0x180 __dev_change_flags+0x1af/0x220 dev_change_flags+0x21/0x60 do_setlink+0x35c/0x1100 En lugar de asignar esta gran cantidad de memoria y dividirla para las instancias de TPA simultáneas, asigne cada pequeña cantidad por separado para cada instancia de TPA. Esto reducirá las asignaciones a un orden de 0."}], "references": [{"url": "https://git.kernel.org/stable/c/16f3aae1aa2dd89bc8d073a67f190af580386ae9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/20fd0607acbf9770db9b99e3418dd75614f80b6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/accd7e23693aaaa9aa0d3e9eca0ae77d1be80ab3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad529d1fae1565d38f929479d4ea8aea90054bd2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d16701a385b54f44bf41ff1d7485e7a11080deb3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fcae40e65802547def39b4deaa2ae38a29864d81", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-23160", "published_date": "2025-05-01T13:15:51.957", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: mediatek: vcodec: Fix a resource leak related to the scp device in FW initialization\n\nOn Mediatek devices with a system companion processor (SCP) the mtk_scp\nstructure has to be removed explicitly to avoid a resource leak.\nFree the structure in case the allocation of the firmware structure fails\nduring the firmware initialization."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: mediatek: vcodec: Se corrige una fuga de recursos relacionada con el dispositivo scp durante la inicialización del firmware. En dispositivos Mediatek con un procesador complementario del sistema (SCP), la estructura mtk_scp debe eliminarse explícitamente para evitar una fuga de recursos. Libere la estructura en caso de que la asignación de la estructura del firmware falle durante la inicialización."}], "references": [{"url": "https://git.kernel.org/stable/c/4936cd5817af35d23e4d283f48fa59a18ef481e4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9f009fa823c54ca0857c81f7525ea5a5d32de29c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ac94e1db4b2053059779472eb58a64d504964240", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d6cb086aa52bd51378a4c9e2b25d2def97770205", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fd7bb97ede487b9f075707b7408a9073e0d474b1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
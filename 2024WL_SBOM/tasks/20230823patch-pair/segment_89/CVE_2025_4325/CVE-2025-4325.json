{"cve_id": "CVE-2025-4325", "published_date": "2025-05-06T06:15:36.930", "last_modified_date": "2025-06-17T19:49:24.983", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in MRCMS 3.1.2 and classified as problematic. This vulnerability affects unknown code of the file /admin/category/add.do of the component Category Management Page. The manipulation of the argument Name leads to cross site scripting. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en MRCMS 3.1.2, clasificada como problemática. Esta vulnerabilidad afecta al código desconocido del archivo /admin/category/add.do del componente Category Management Page. La manipulación del argumento \"Name\" provoca ataques de cross site scripting. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bdkuzma/vuln/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307426", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307426", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563545", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
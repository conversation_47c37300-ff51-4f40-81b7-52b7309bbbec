{"cve_id": "CVE-2025-45015", "published_date": "2025-04-30T14:15:29.313", "last_modified_date": "2025-05-09T13:44:44.760", "descriptions": [{"lang": "en", "value": "A Cross-Site Scripting (XSS) vulnerability was discovered in the foreigner-bwdates-reports-details.php file of PHPGurukul Park Ticketing Management System v2.0. The vulnerability allows remote attackers to inject arbitrary JavaScript code via the fromdate and todate parameters."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de Cross-Site Scripting (XSS) en el archivo foreigner-bwdates-reports-details.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos inyectar código JavaScript arbitrario mediante los parámetros fromdate y todate."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/XSS/foreigner-bwdates-reports-details-XSS-injection.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
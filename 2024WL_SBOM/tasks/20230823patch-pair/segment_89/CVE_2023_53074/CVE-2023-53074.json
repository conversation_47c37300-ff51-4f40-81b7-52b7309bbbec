{"cve_id": "CVE-2023-53074", "published_date": "2025-05-02T16:15:26.420", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amdgpu: fix ttm_bo calltrace warning in psp_hw_fini\n\nThe call trace occurs when the amdgpu is removed after\nthe mode1 reset. During mode1 reset, from suspend to resume,\nthere is no need to reinitialize the ta firmware buffer\nwhich caused the bo pin_count increase redundantly.\n\n[  489.885525] Call Trace:\n[  489.885525]  <TASK>\n[  489.885526]  amdttm_bo_put+0x34/0x50 [amdttm]\n[  489.885529]  amdgpu_bo_free_kernel+0xe8/0x130 [amdgpu]\n[  489.885620]  psp_free_shared_bufs+0xb7/0x150 [amdgpu]\n[  489.885720]  psp_hw_fini+0xce/0x170 [amdgpu]\n[  489.885815]  amdgpu_device_fini_hw+0x2ff/0x413 [amdgpu]\n[  489.885960]  ? blocking_notifier_chain_unregister+0x56/0xb0\n[  489.885962]  amdgpu_driver_unload_kms+0x51/0x60 [amdgpu]\n[  489.886049]  amdgpu_pci_remove+0x5a/0x140 [amdgpu]\n[  489.886132]  ? __pm_runtime_resume+0x60/0x90\n[  489.886134]  pci_device_remove+0x3e/0xb0\n[  489.886135]  __device_release_driver+0x1ab/0x2a0\n[  489.886137]  driver_detach+0xf3/0x140\n[  489.886138]  bus_remove_driver+0x6c/0xf0\n[  489.886140]  driver_unregister+0x31/0x60\n[  489.886141]  pci_unregister_driver+0x40/0x90\n[  489.886142]  amdgpu_exit+0x15/0x451 [amdgpu]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amdgpu: se corrige la advertencia de seguimiento de llamadas ttm_bo en psp_hw_fini. El seguimiento de llamadas se produce al eliminar amdgpu tras el reinicio en modo 1. Durante el reinicio en modo 1, desde la suspensión hasta la reanudación, no es necesario reinicializar el búfer de firmware ta, lo que provocaba un aumento redundante en el recuento de pines de bo. [ 489.885525] Seguimiento de llamadas: [ 489.885525]  [ 489.885526] amdttm_bo_put+0x34/0x50 [amdttm] [ 489.885529] amdgpu_bo_free_kernel+0xe8/0x130 [amdgpu] [ 489.885620] psp_free_shared_bufs+0xb7/0x150 [amdgpu] [ 489.885720] psp_hw_fini+0xce/0x170 [amdgpu] [ 489.885815] amdgpu_device_fini_hw+0x2ff/0x413 [amdgpu] [ 489.885960] ? blocking_notifier_chain_unregister+0x56/0xb0 [ 489.885962] amdgpu_driver_unload_kms+0x51/0x60 [amdgpu] [ 489.886049] amdgpu_pci_remove+0x5a/0x140 [amdgpu] [ 489.886132] ? __pm_runtime_resume+0x60/0x90 [ 489.886134] pci_device_remove+0x3e/0xb0 [ 489.886135] __device_release_driver+0x1ab/0x2a0 [ 489.886137] driver_detach+0xf3/0x140 [ 489.886138] bus_remove_driver+0x6c/0xf0 [ 489.886140] driver_unregister+0x31/0x60 [ 489.886141] pci_unregister_driver+0x40/0x90 [ 489.886142] amdgpu_exit+0x15/0x451 [amdgpu] "}], "references": [{"url": "https://git.kernel.org/stable/c/23f4a2d29ba57bf88095f817de5809d427fcbe7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/55a7c647ebf6e376c45d8322568dd6eb71937139", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7be9a2f8c5179520a7d5570e648e0c97d09e4fae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
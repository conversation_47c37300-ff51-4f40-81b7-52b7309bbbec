{"cve_id": "CVE-2025-4198", "published_date": "2025-05-03T03:15:28.923", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "The Alink Tap plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.3.1. This is due to missing or incorrect nonce validation on the 'alink-tap' page. This makes it possible for unauthenticated attackers to update settings and inject malicious web scripts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Alink Tap para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.3.1 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la página \"alink-tap\". Esto permite que atacantes no autenticados actualicen la configuración e inyecten scripts web maliciosos mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/alink-tap/trunk/admin/views/admin.php", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/alink-tap/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c856e06d-34f7-42e9-a72c-3d4e9207e07e?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2022-49902", "published_date": "2025-05-01T15:16:15.270", "last_modified_date": "2025-05-07T13:30:42.970", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblock: Fix possible memory leak for rq_wb on add_disk failure\n\nkmemleak reported memory leaks in device_add_disk():\n\nkmemleak: 3 new suspected memory leaks\n\nunreferenced object 0xffff88800f420800 (size 512):\n  comm \"modprobe\", pid 4275, jiffies 4295639067 (age 223.512s)\n  hex dump (first 32 bytes):\n    04 00 00 00 08 00 00 00 01 00 00 00 00 00 00 00  ................\n    00 e1 f5 05 00 00 00 00 00 00 00 00 00 00 00 00  ................\n  backtrace:\n    [<00000000d3662699>] kmalloc_trace+0x26/0x60\n    [<00000000edc7aadc>] wbt_init+0x50/0x6f0\n    [<0000000069601d16>] wbt_enable_default+0x157/0x1c0\n    [<0000000028fc393f>] blk_register_queue+0x2a4/0x420\n    [<000000007345a042>] device_add_disk+0x6fd/0xe40\n    [<0000000060e6aab0>] nbd_dev_add+0x828/0xbf0 [nbd]\n    ...\n\nIt is because the memory allocated in wbt_enable_default() is not\nreleased in device_add_disk() error path.\nNormally, these memory are freed in:\n\ndel_gendisk()\n  rq_qos_exit()\n    rqos->ops->exit(rqos);\n      wbt_exit()\n\nSo rq_qos_exit() is called to free the rq_wb memory for wbt_init().\nHowever in the error path of device_add_disk(), only\nblk_unregister_queue() is called and make rq_wb memory leaked.\n\nAdd rq_qos_exit() to the error path to fix it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bloque: Reparar posible pérdida de memoria para rq_wb en caso de error de add_disk kmemleak informó pérdidas de memoria en device_add_disk(): kmemleak: 3 nuevas pérdidas de memoria sospechosas objeto no referenciado 0xffff88800f420800 (tamaño 512): comm \"modprobe\", pid 4275, jiffies 4295639067 (edad 223.512s) volcado hexadecimal (primeros 32 bytes): 04 00 00 00 08 00 00 00 01 00 00 00 00 00 00 00 00 ................ 00 e1 f5 05 00 00 00 00 00 00 00 00 00 00 00 00 ................ backtrace: [&lt;00000000d3662699&gt;] kmalloc_trace+0x26/0x60 [&lt;00000000edc7aadc&gt;] wbt_init+0x50/0x6f0 [&lt;0000000069601d16&gt;] wbt_enable_default+0x157/0x1c0 [&lt;0000000028fc393f&gt;] blk_register_queue+0x2a4/0x420 [&lt;000000007345a042&gt;] device_add_disk+0x6fd/0xe40 [&lt;0000000060e6aab0&gt;] nbd_dev_add+0x828/0xbf0 [nbd] ... Esto se debe a que la memoria asignada en wbt_enable_default() no se libera en la ruta de error device_add_disk(). Normalmente, esta memoria se libera en: del_gendisk() rq_qos_exit() rqos-&gt;ops-&gt;exit(rqos); wbt_exit(). Por lo tanto, se llama a rq_qos_exit() para liberar la memoria rq_wb para wbt_init(). Sin embargo, en la ruta de error de device_add_disk(), solo se llama a blk_unregister_queue(), lo que provoca una fuga de memoria en rq_wb. Agregue rq_qos_exit() a la ruta de error para corregirlo."}], "references": [{"url": "https://git.kernel.org/stable/c/4e68c5da60cd79950bd56287ae80b39d6261f995", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/528677d3b4af985445bd4ac667485ded1ed11220", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fa81cbafbf5764ad5053512152345fab37a1fe18", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
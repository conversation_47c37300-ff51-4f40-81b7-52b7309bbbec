{"cve_id": "CVE-2025-37771", "published_date": "2025-05-01T14:15:40.453", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/pm: Prevent division by zero\n\nThe user can set any speed value.\nIf speed is greater than UINT_MAX/8, division by zero is possible.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/pm: Impide la división por cero. El usuario puede establecer cualquier valor de velocidad. Si la velocidad es superior a UINT_MAX/8, es posible la división por cero. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/402964994e8ece29702383b234fabcf04791ff95", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5096174074114f83c700a27869c54362cbb10f3e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6413fed016208171592c88b5df002af8a1387e24", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d641c2b83275d3b0424127b2e0d2d0f7dd82aef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b7c41df4913789ebfe73cc1e17c6401d4c5eab69", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/baa54adb5e0599299b8f088efb5544d876a3eb62", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49907", "published_date": "2025-05-01T15:16:15.810", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: mdio: fix undefined behavior in bit shift for __mdiobus_register\n\nShifting signed 32-bit value by 31 bits is undefined, so changing\nsignificant bit to unsigned. The UBSAN warning calltrace like below:\n\nUBSAN: shift-out-of-bounds in drivers/net/phy/mdio_bus.c:586:27\nleft shift of 1 by 31 places cannot be represented in type 'int'\nCall Trace:\n <TASK>\n dump_stack_lvl+0x7d/0xa5\n dump_stack+0x15/0x1b\n ubsan_epilogue+0xe/0x4e\n __ubsan_handle_shift_out_of_bounds+0x1e7/0x20c\n __mdiobus_register+0x49d/0x4e0\n fixed_mdio_bus_init+0xd8/0x12d\n do_one_initcall+0x76/0x430\n kernel_init_freeable+0x3b3/0x422\n kernel_init+0x24/0x1e0\n ret_from_fork+0x1f/0x30\n </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: mdio: corrige comportamiento indefinido en el desplazamiento de bits para __mdiobus_register El desplazamiento de un valor de 32 bits con signo en 31 bits no está definido, por lo que se cambia el bit significativo a sin signo. El seguimiento de llamadas de advertencia de UBSAN como se muestra a continuación: UBSAN: desplazamiento fuera de los límites en drivers/net/phy/mdio_bus.c:586:27 El desplazamiento a la izquierda de 1 por 31 lugares no se puede representar en el tipo 'int' Seguimiento de llamadas:   dump_stack_lvl+0x7d/0xa5 dump_stack+0x15/0x1b ubsan_epilogue+0xe/0x4e __ubsan_handle_shift_out_of_bounds+0x1e7/0x20c __mdiobus_register+0x49d/0x4e0 fixed_mdio_bus_init+0xd8/0x12d do_one_initcall+0x76/0x430 kernel_init_freeable+0x3b3/0x422 kernel_init+0x24/0x1e0 ret_from_fork+0x1f/0x30  "}], "references": [{"url": "https://git.kernel.org/stable/c/20ed01a7b9af6e6a3c33761eebbb710ea6dd49b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/40e4eb324c59e11fcb927aa46742d28aba6ecb8a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4954b5359eb141499492fadfab891e28905509e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/634f066d02bdb22a26da7deb0c7617ab1a65fc9d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ce6f8f8f6316da6f92afe7490bc2f0b654d68e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7006176a3c863e3e353ce1b8a349ef5bb1b9320e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/985a88bf0b27193522bba7856b1763f428cef19d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a3fafc974be37319679f36dc4e7cca7db1e02973", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
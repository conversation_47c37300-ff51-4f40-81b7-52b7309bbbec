{"cve_id": "CVE-2023-53133", "published_date": "2025-05-02T16:15:32.260", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf, sockmap: Fix an infinite loop error when len is 0 in tcp_bpf_recvmsg_parser()\n\nWhen the buffer length of the recvmsg system call is 0, we got the\nflollowing soft lockup problem:\n\nwatchdog: BUG: soft lockup - CPU#3 stuck for 27s! [a.out:6149]\nCPU: 3 PID: 6149 Comm: a.out Kdump: loaded Not tainted 6.2.0+ #30\nHardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.15.0-1 04/01/2014\nRIP: 0010:remove_wait_queue+0xb/0xc0\nCode: 5e 41 5f c3 cc cc cc cc 0f 1f 80 00 00 00 00 90 90 90 90 90 90 90 90 90 90 90 90 90 90 90 90 f3 0f 1e fa 0f 1f 44 00 00 41 57 <41> 56 41 55 41 54 55 48 89 fd 53 48 89 f3 4c 8d 6b 18 4c 8d 73 20\nRSP: 0018:ffff88811b5978b8 EFLAGS: 00000246\nRAX: 0000000000000000 RBX: ffff88811a7d3780 RCX: ffffffffb7a4d768\nRDX: dffffc0000000000 RSI: ffff88811b597908 RDI: ffff888115408040\nRBP: 1ffff110236b2f1b R08: 0000000000000000 R09: ffff88811a7d37e7\nR10: ffffed10234fa6fc R11: 0000000000000001 R12: ffff88811179b800\nR13: 0000000000000001 R14: ffff88811a7d38a8 R15: ffff88811a7d37e0\nFS:  00007f6fb5398740(0000) GS:ffff888237180000(0000) knlGS:0000000000000000\nCS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 0000000020000000 CR3: 000000010b6ba002 CR4: 0000000000370ee0\nDR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000\nDR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400\nCall Trace:\n <TASK>\n tcp_msg_wait_data+0x279/0x2f0\n tcp_bpf_recvmsg_parser+0x3c6/0x490\n inet_recvmsg+0x280/0x290\n sock_recvmsg+0xfc/0x120\n ____sys_recvmsg+0x160/0x3d0\n ___sys_recvmsg+0xf0/0x180\n __sys_recvmsg+0xea/0x1a0\n do_syscall_64+0x3f/0x90\n entry_SYSCALL_64_after_hwframe+0x72/0xdc\n\nThe logic in tcp_bpf_recvmsg_parser is as follows:\n\nmsg_bytes_ready:\n\tcopied = sk_msg_recvmsg(sk, psock, msg, len, flags);\n\tif (!copied) {\n\t\twait data;\n\t\tgoto msg_bytes_ready;\n\t}\n\nIn this case, \"copied\" always is 0, the infinite loop occurs.\n\nAccording to the Linux system call man page, 0 should be returned in this\ncase. Therefore, in tcp_bpf_recvmsg_parser(), if the length is 0, directly\nreturn. Also modify several other functions with the same problem."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf, sockmap: corrige un error de bucle infinito cuando len es 0 en tcp_bpf_recvmsg_parser() Cuando la longitud del búfer de la llamada del sistema recvmsg es 0, tenemos el siguiente problema de bloqueo suave: watchdog: ERROR: bloqueo suave: ¡CPU n.º 3 bloqueada durante 27 s! [a.out:6149] CPU: 3 PID: 6149 Comm: a.out Kdump: cargado No contaminado 6.2.0+ #30 Nombre del hardware: PC estándar QEMU (Q35 + ICH9, 2009), BIOS 1.15.0-1 04/01/2014 RIP: 0010:remove_wait_queue+0xb/0xc0 Código: 5e 41 5f c3 cc cc cc cc 0f 1f 80 00 00 00 00 90 90 90 90 90 90 90 90 90 90 90 90 90 90 90 f3 0f 1e fa 0f 1f 44 00 00 41 57 &lt;41&gt; 56 41 55 41 54 55 48 89 fd 53 48 89 f3 4c 8d 6b 18 4c 8d 73 20 RSP: 0018:ffff88811b5978b8 EFLAGS: 00000246 RAX: 000000000000000 RBX: ffff88811a7d3780 RCX: ffffffffb7a4d768 RDX: dffffc0000000000 RSI: ffff88811b597908 RDI: ffff888115408040 RBP: 1ffff110236b2f1b R08: 000000000000000 R09: ffff88811a7d37e7 R10: ffffed10234fa6fc R11: 000000000000001 R12: ffff88811179b800 R13: 0000000000000001 R14: ffff88811a7d38a8 R15: ffff88811a7d37e0 FS: 00007f6fb5398740(0000) GS:ffff888237180000(0000) knlGS:000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 000000080050033 CR2: 0000000020000000 CR3: 0000000010b6ba002 CR4: 0000000000370ee0 DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000 DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400 Rastreo de llamadas:  tcp_msg_wait_data+0x279/0x2f0 tcp_bpf_recvmsg_parser+0x3c6/0x490 inet_recvmsg+0x280/0x290 sock_recvmsg+0xfc/0x120 ____sys_recvmsg+0x160/0x3d0 ___sys_recvmsg+0xf0/0x180 __sys_recvmsg+0xea/0x1a0 do_syscall_64+0x3f/0x90 entry_SYSCALL_64_after_hwframe+0x72/0xdc The logic in tcp_bpf_recvmsg_parser is as follows: msg_bytes_ready: copied = sk_msg_recvmsg(sk, psock, msg, len, flags); if (!copied) { wait data; goto msg_bytes_ready; } En este caso, \"copiado\" siempre es 0, se produce el bucle infinito. Según la página del manual de llamadas del sistema de Linux, en este caso se debería devolver 0. Por lo tanto, en tcp_bpf_recvmsg_parser(), si la longitud es 0, se devuelve directamente. Modifique también otras funciones con el mismo problema."}], "references": [{"url": "https://git.kernel.org/stable/c/4a476285f6d2921c3c9faa494eab83b78f78fc55", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bf0579989de64d36e177c0611c685dc4a91457a7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d900f3d20cc3169ce42ec72acc850e662a4d4db2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f45cf3ae3068e70e2c7f3e24a7f8e8aa99511f03", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
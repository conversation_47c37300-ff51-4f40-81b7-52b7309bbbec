{"cve_id": "CVE-2022-49932", "published_date": "2025-05-02T16:15:22.070", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: VMX: Do _all_ initialization before exposing /dev/kvm to userspace\n\nCall kvm_init() only after _all_ setup is complete, as kvm_init() exposes\n/dev/kvm to userspace and thus allows userspace to create VMs (and call\nother ioctls).  E.g. KVM will encounter a NULL pointer when attempting to\nadd a vCPU to the per-CPU loaded_vmcss_on_cpu list if userspace is able to\ncreate a VM before vmx_init() configures said list.\n\n BUG: kernel NULL pointer dereference, address: 0000000000000008\n #PF: supervisor write access in kernel mode\n #PF: error_code(0x0002) - not-present page\n PGD 0 P4D 0\n Oops: 0002 [#1] SMP\n CPU: 6 PID: 1143 Comm: stable Not tainted 6.0.0-rc7+ #988\n Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 0.0.0 02/06/2015\n RIP: 0010:vmx_vcpu_load_vmcs+0x68/0x230 [kvm_intel]\n  <TASK>\n  vmx_vcpu_load+0x16/0x60 [kvm_intel]\n  kvm_arch_vcpu_load+0x32/0x1f0 [kvm]\n  vcpu_load+0x2f/0x40 [kvm]\n  kvm_arch_vcpu_create+0x231/0x310 [kvm]\n  kvm_vm_ioctl+0x79f/0xe10 [kvm]\n  ? handle_mm_fault+0xb1/0x220\n  __x64_sys_ioctl+0x80/0xb0\n  do_syscall_64+0x2b/0x50\n  entry_SYSCALL_64_after_hwframe+0x46/0xb0\n RIP: 0033:0x7f5a6b05743b\n  </TASK>\n Modules linked in: vhost_net vhost vhost_iotlb tap kvm_intel(+) kvm irqbypass"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: VMX: Inicializa _toda_ la instancia antes de exponer /dev/kvm al espacio de usuario. Llama a kvm_init() solo después de que se complete la configuración _toda_, ya que kvm_init() expone /dev/kvm al espacio de usuario y, por lo tanto, permite que este cree máquinas virtuales (y llame a otras ioctl). Por ejemplo, KVM encontrará un puntero nulo al intentar agregar una vCPU a la lista por CPU load_vmcss_on_cpu si el espacio de usuario puede crear una máquina virtual antes de que vmx_init() configure dicha lista. ERROR: desreferencia de puntero NULL del kernel, dirección: 0000000000000008 #PF: acceso de escritura del supervisor en modo kernel #PF: error_code(0x0002) - página no presente PGD 0 P4D 0 Oops: 0002 [#1] CPU SMP: 6 PID: 1143 Comm: estable No contaminado 6.0.0-rc7+ #988 Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 0.0.0 02/06/2015 RIP: 0010:vmx_vcpu_load_vmcs+0x68/0x230 [kvm_intel]  vmx_vcpu_load+0x16/0x60 [kvm_intel] kvm_arch_vcpu_load+0x32/0x1f0 [kvm] vcpu_load+0x2f/0x40 [kvm] kvm_arch_vcpu_create+0x231/0x310 [kvm] kvm_vm_ioctl+0x79f/0xe10 [kvm] ? handle_mm_fault+0xb1/0x220 __x64_sys_ioctl+0x80/0xb0 do_syscall_64+0x2b/0x50 entry_SYSCALL_64_after_hwframe+0x46/0xb0 RIP: 0033:0x7f5a6b05743b  Módulos vinculados en: vhost_net vhost vhost_iotlb tap kvm_intel(+) kvm irqbypass"}], "references": [{"url": "https://git.kernel.org/stable/c/e136e969d268b9b89329c816c002e53f60e82985", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e28533c08023c4b319b7f2cd77f3f7c9204eb517", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e32b120071ea114efc0b4ddd439547750b85f618", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
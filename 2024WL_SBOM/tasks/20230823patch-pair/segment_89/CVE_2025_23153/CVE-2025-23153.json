{"cve_id": "CVE-2025-23153", "published_date": "2025-05-01T13:15:51.210", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm/crc-t10dif: fix use of out-of-scope array in crc_t10dif_arch()\n\nFix a silly bug where an array was used outside of its scope."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm/crc-t10dif: corrige el uso de una matriz fuera de alcance en crc_t10dif_arch() Corrige un error tonto en el que se usaba una matriz fuera de su alcance."}], "references": [{"url": "https://git.kernel.org/stable/c/3371f569223c4e8d36edbb0ba789ee5f5cb7316f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d8eba735be74e74776f9f6d9c691bdb75b08b29c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
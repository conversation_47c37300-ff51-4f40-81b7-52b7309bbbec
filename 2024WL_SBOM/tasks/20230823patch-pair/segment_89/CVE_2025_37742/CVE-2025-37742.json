{"cve_id": "CVE-2025-37742", "published_date": "2025-05-01T13:15:52.870", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\njfs: Fix uninit-value access of imap allocated in the diMount() function\n\nsyzbot reports that hex_dump_to_buffer is using uninit-value:\n\n=====================================================\nBUG: KMSAN: uninit-value in hex_dump_to_buffer+0x888/0x1100 lib/hexdump.c:171\nhex_dump_to_buffer+0x888/0x1100 lib/hexdump.c:171\nprint_hex_dump+0x13d/0x3e0 lib/hexdump.c:276\ndiFree+0x5ba/0x4350 fs/jfs/jfs_imap.c:876\njfs_evict_inode+0x510/0x550 fs/jfs/inode.c:156\nevict+0x723/0xd10 fs/inode.c:796\niput_final fs/inode.c:1946 [inline]\niput+0x97b/0xdb0 fs/inode.c:1972\ntxUpdateMap+0xf3e/0x1150 fs/jfs/jfs_txnmgr.c:2367\ntxLazyCommit fs/jfs/jfs_txnmgr.c:2664 [inline]\njfs_lazycommit+0x627/0x11d0 fs/jfs/jfs_txnmgr.c:2733\nkthread+0x6b9/0xef0 kernel/kthread.c:464\nret_from_fork+0x6d/0x90 arch/x86/kernel/process.c:148\nret_from_fork_asm+0x1a/0x30 arch/x86/entry/entry_64.S:244\n\nUninit was created at:\nslab_post_alloc_hook mm/slub.c:4121 [inline]\nslab_alloc_node mm/slub.c:4164 [inline]\n__kmalloc_cache_noprof+0x8e3/0xdf0 mm/slub.c:4320\nkmalloc_noprof include/linux/slab.h:901 [inline]\ndiMount+0x61/0x7f0 fs/jfs/jfs_imap.c:105\njfs_mount+0xa8e/0x11d0 fs/jfs/jfs_mount.c:176\njfs_fill_super+0xa47/0x17c0 fs/jfs/super.c:523\nget_tree_bdev_flags+0x6ec/0x910 fs/super.c:1636\nget_tree_bdev+0x37/0x50 fs/super.c:1659\njfs_get_tree+0x34/0x40 fs/jfs/super.c:635\nvfs_get_tree+0xb1/0x5a0 fs/super.c:1814\ndo_new_mount+0x71f/0x15e0 fs/namespace.c:3560\npath_mount+0x742/0x1f10 fs/namespace.c:3887\ndo_mount fs/namespace.c:3900 [inline]\n__do_sys_mount fs/namespace.c:4111 [inline]\n__se_sys_mount+0x71f/0x800 fs/namespace.c:4088\n__x64_sys_mount+0xe4/0x150 fs/namespace.c:4088\nx64_sys_call+0x39bf/0x3c30 arch/x86/include/generated/asm/syscalls_64.h:166\ndo_syscall_x64 arch/x86/entry/common.c:52 [inline]\ndo_syscall_64+0xcd/0x1e0 arch/x86/entry/common.c:83\nentry_SYSCALL_64_after_hwframe+0x77/0x7f\n=====================================================\n\nThe reason is that imap is not properly initialized after memory\nallocation. It will cause the snprintf() function to write uninitialized\ndata into linebuf within hex_dump_to_buffer().\n\nFix this by using kzalloc instead of kmalloc to clear its content at the\nbeginning in diMount()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: jfs: Se corrige el acceso a un valor no inicializado de imap asignado en la función diMount() syzbot informa que hex_dump_to_buffer está usando un valor no inicializado: ======================================================== ERROR: KMSAN: un valor no inicializado en hex_dump_to_buffer+0x888/0x1100 lib/hexdump.c:171 hex_dump_to_buffer+0x888/0x1100 lib/hexdump.c:171 print_hex_dump+0x13d/0x3e0 lib/hexdump.c:276 diFree+0x5ba/0x4350 fs/jfs/jfs_imap.c:876 jfs_evict_inode+0x510/0x550 fs/jfs/inode.c:156 evict+0x723/0xd10 fs/inode.c:796 iput_final fs/inode.c:1946 [en línea] iput+0x97b/0xdb0 fs/inode.c:1972 txUpdateMap+0xf3e/0x1150 fs/jfs/jfs_txnmgr.c:2367 txLazyCommit fs/jfs/jfs_txnmgr.c:2664 [en línea] jfs_lazycommit+0x627/0x11d0 fs/jfs/jfs_txnmgr.c:2733 kthread+0x6b9/0xef0 kernel/kthread.c:464 ret_from_fork+0x6d/0x90 arch/x86/kernel/process.c:148 ret_from_fork_asm+0x1a/0x30 arch/x86/entry/entry_64.S:244 Uninit se creó en: slab_post_alloc_hook mm/slub.c:4121 [en línea] slab_alloc_node mm/slub.c:4164 [en línea] __kmalloc_cache_noprof+0x8e3/0xdf0 mm/slub.c:4320 kmalloc_noprof include/linux/slab.h:901 [en línea] diMount+0x61/0x7f0 fs/jfs/jfs_imap.c:105 jfs_mount+0xa8e/0x11d0 fs/jfs/jfs_mount.c:176 jfs_fill_super+0xa47/0x17c0 fs/jfs/super.c:523 obtener_árbol_bdev_flags+0x6ec/0x910 fs/super.c:1636 obtener_árbol_bdev+0x37/0x50 fs/super.c:1659 jfs_obtener_árbol+0x34/0x40 fs/jfs/super.c:635 vfs_obtener_árbol+0xb1/0x5a0 fs/super.c:1814 hacer_nuevo_montaje+0x71f/0x15e0 fs/espacio_de_nombres.c:3560 path_mount+0x742/0x1f10 fs/namespace.c:3887 do_mount fs/namespace.c:3900 [en línea] __do_sys_mount fs/namespace.c:4111 [en línea] __se_sys_mount+0x71f/0x800 fs/namespace.c:4088 __x64_sys_mount+0xe4/0x150 fs/namespace.c:4088 x64_sys_call+0x39bf/0x3c30 arch/x86/include/generated/asm/syscalls_64.h:166 do_syscall_x64 arch/x86/entry/common.c:52 [en línea] do_syscall_64+0xcd/0x1e0 arch/x86/entry/common.c:83 entry_SYSCALL_64_after_hwframe+0x77/0x7f == ..."}], "references": [{"url": "https://git.kernel.org/stable/c/067347e00a3a7d04afed93f080c6c131e5dd15ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4f10732712fce33e53703ffe5ed9155f23814097", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/63148ce4904faa668daffdd1d3c1199ae315ef2c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7057f3aab47629d38e54eae83505813cf0da1e4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9629d7d66c621671d9a47afe27ca9336bfc8a9ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cab1852368dd74d629ee02abdbc559218ca64dde", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d0d7eca253ccd0619b3d2b683ffe32218ebca9ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
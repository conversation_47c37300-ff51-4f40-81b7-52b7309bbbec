{"cve_id": "CVE-2025-32973", "published_date": "2025-04-30T15:16:01.823", "last_modified_date": "2025-05-13T14:58:48.890", "descriptions": [{"lang": "en", "value": "XWiki is a generic wiki platform. In versions starting from 15.9-rc-1 to before 15.10.12, from 16.0.0-rc-1 to before 16.4.3, and from 16.5.0-rc-1 to before 16.8.0-rc-1, when a user with programming rights edits a document in XWiki that was last edited by a user without programming rights and contains an XWiki.ComponentClass, there is no warning that this will grant programming rights to this object. An attacker who created such a malicious object could use this to gain programming rights on the wiki. For this, the attacker needs to have edit rights on at least one page to place this object and then get an admin user to edit that document. This issue has been patched in versions 15.10.12, 16.4.3, and 16.8.0-rc-1."}, {"lang": "es", "value": "XWiki es una plataforma wiki genérica. En las versiones 15.9-rc-1 y anteriores a la 15.10.12, 16.0.0-rc-1 y anteriores a la 16.4.3, y 16.5.0-rc-1 y anteriores a la 16.8.0-rc-1, cuando un usuario con permisos de programación edita un documento en XWiki que fue editado por última vez por un usuario sin permisos de programación y que contiene un objeto XWiki.ComponentClass, no se muestra ninguna advertencia que indique que se otorgarán permisos de programación a este objeto. Un atacante que haya creado un objeto malicioso de este tipo podría usarlo para obtener permisos de programación en la wiki. Para ello, el atacante debe tener permisos de edición en al menos una página para colocar este objeto y, a continuación, conseguir que un usuario administrador edite ese documento. Este problema se ha corregido en las versiones 15.10.12, 16.4.3 y 16.8.0-rc-1."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/1a6f1b2e050770331c9a63d12a3fd8a36d199f62", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-x7wv-5qg4-vmr6", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://jira.xwiki.org/browse/XWIKI-22460", "source": "<EMAIL>", "tags": ["Vendor Advisory", "Exploit", "Issue Tracking"]}]}
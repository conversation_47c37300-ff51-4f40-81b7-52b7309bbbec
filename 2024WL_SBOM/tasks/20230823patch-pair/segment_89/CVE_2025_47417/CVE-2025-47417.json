{"cve_id": "CVE-2025-47417", "published_date": "2025-05-06T20:15:27.333", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "Exposure of Sensitive Information to an Unauthorized Actor vulnerability in Crestron Automate VX allows Functionality Misuse.\n\n\n\nWhen Enable Debug Images in Crestron Automate VX is active, snapshots of the captured video or portions thereof are stored locally on the system, and there is no visible indication that this is being done.\n\n\nThis issue affects Automate VX: from 5.6.8161.21536 through ********."}, {"lang": "es", "value": "Vulnerabilidad de exposición de información sensible a un actor no autorizado en Crestron Automate VX permite el uso indebido de la funcionalidad. Cuando la opción \"Habilitar imágenes de depuración\" en Crestron Automate VX está activa, las instantáneas del vídeo capturado o fragmentos del mismo se almacenan localmente en el sistema, sin que haya indicios visibles de que esto ocurra. Este problema afecta a Automate VX desde la versión 5.6.8161.21536 hasta la ********."}], "references": [{"url": "https://security.crestron.com", "source": "25b0b659-c4b4-483f-aecb-067757d23ef3", "tags": []}, {"url": "https://www.crestron.com/Software-Firmware/Software/Automate-VX-Software/6-4-1-8", "source": "25b0b659-c4b4-483f-aecb-067757d23ef3", "tags": []}, {"url": "https://www.crestron.com/release_notes/automate_vx_6.4.1.8_release_notes.pdf", "source": "25b0b659-c4b4-483f-aecb-067757d23ef3", "tags": []}]}
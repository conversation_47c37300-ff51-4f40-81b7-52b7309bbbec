{"cve_id": "CVE-2023-53137", "published_date": "2025-05-02T16:15:32.633", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: Fix possible corruption when moving a directory\n\nWhen we are renaming a directory to a different directory, we need to\nupdate '..' entry in the moved directory. However nothing prevents moved\ndirectory from being modified and even converted from the inline format\nto the normal format. When such race happens the rename code gets\nconfused and we crash. Fix the problem by locking the moved directory."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: Se corrige la posible corrupción al mover un directorio. Al renombrar un directorio, es necesario actualizar la entrada \"..\" en el directorio movido. Sin embargo, nada impide que el directorio movido se modifique e incluso se convierta del formato en línea al formato normal. <PERSON><PERSON>do esto ocurre, el código de renombrado se confunde y se produce un fallo. Solucione el problema bloqueando el directorio movido."}], "references": [{"url": "https://git.kernel.org/stable/c/0813299c586b175d7edb25f56412c54b812d0379", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0c440f14558bfacd22c6935ae1fd4b2a09e96b5d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/291cd19d107e197306869cb3237c1bba62d13182", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8dac5a63cf79707b547ea3d425fead5f4482198f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b0bb13612292ca90fa4c2a7e425375649bc50d3e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c50fc503ee1b97f12c98e26afc39fdaebebcf04f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
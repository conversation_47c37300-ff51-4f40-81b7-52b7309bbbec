{"cve_id": "CVE-2025-4358", "published_date": "2025-05-06T14:15:42.463", "last_modified_date": "2025-05-15T07:15:50.917", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Company Visitor Management System 2.0. Affected is an unknown function of the file /admin-profile.php. The manipulation of the argument adminname/mobilenumber leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Company Visitor Management System 2.0. Se ve afectada una función desconocida del archivo /admin-profile.php. La manipulación del argumento adminname provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Pjwww13447/pjwww/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307476", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307476", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564733", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-44837", "published_date": "2025-05-01T15:16:20.837", "last_modified_date": "2025-05-22T15:29:38.487", "descriptions": [{"lang": "en", "value": "TOTOLINK CPE CP900 V6.3c.1144_B20190715 was discovered to contain a command injection vulnerability in the CloudSrvUserdataVersionCheck function via the url or magicid parameters. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que TOTOLINK CPE CP900 V6.3c.1144_B20190715 contiene una vulnerabilidad de inyección de comandos en la función CloudSrvUserdataVersionCheck mediante los parámetros url o magicid. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/n0wstr/IOTVuln/tree/main/CP900/CloudSrvUserdataVersionCheck", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
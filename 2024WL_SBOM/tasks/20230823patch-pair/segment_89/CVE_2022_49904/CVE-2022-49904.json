{"cve_id": "CVE-2022-49904", "published_date": "2025-05-01T15:16:15.480", "last_modified_date": "2025-05-07T13:30:34.307", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet, neigh: Fix null-ptr-deref in neigh_table_clear()\n\nWhen IPv6 module gets initialized but hits an error in the middle,\nkenel panic with:\n\nKASAN: null-ptr-deref in range [0x0000000000000598-0x000000000000059f]\nCPU: 1 PID: 361 Comm: insmod\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996)\nRIP: 0010:__neigh_ifdown.isra.0+0x24b/0x370\nRSP: 0018:ffff888012677908 EFLAGS: 00000202\n...\nCall Trace:\n <TASK>\n neigh_table_clear+0x94/0x2d0\n ndisc_cleanup+0x27/0x40 [ipv6]\n inet6_init+0x21c/0x2cb [ipv6]\n do_one_initcall+0xd3/0x4d0\n do_init_module+0x1ae/0x670\n...\nKernel panic - not syncing: Fatal exception\n\nWhen ipv6 initialization fails, it will try to cleanup and calls:\n\nneigh_table_clear()\n  neigh_ifdown(tbl, NULL)\n    pneigh_queue_purge(&tbl->proxy_queue, dev_net(dev == NULL))\n    # dev_net(NULL) triggers null-ptr-deref.\n\nFix it by passing NULL to pneigh_queue_purge() in neigh_ifdown() if dev\nis NULL, to make kernel not panic immediately."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net, neigh: Fix null-ptr-deref en neigh_table_clear() Cuando se inicializa un módulo IPv6 pero se produce un error en el medio, kenel entra en pánico con: KASAN: null-ptr-deref en el rango [0x0000000000000598-0x000000000000059f] CPU: 1 PID: 361 Comm: insmod Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996) RIP: 0010:__neigh_ifdown.isra.0+0x24b/0x370 RSP: 0018:ffff888012677908 EFLAGS: 00000202 ... Seguimiento de llamadas:  neigh_table_clear+0x94/0x2d0 ndisc_cleanup+0x27/0x40 [ipv6] inet6_init+0x21c/0x2cb [ipv6] do_one_initcall+0xd3/0x4d0 do_init_module+0x1ae/0x670 ... Pánico del kernel - no sincroniza: Excepción fatal Cuando falla la inicialización de ipv6, intentará limpiar y llamará a: neigh_table_clear() neigh_ifdown(tbl, NULL) pneigh_queue_purge(&amp;tbl-&gt;proxy_queue, dev_net(dev == NULL)) # dev_net(NULL) activa null-ptr-deref. Corríjalo pasando NULL a pneigh_queue_purge() en neigh_ifdown() si dev es NULL, para que el kernel no entre en pánico inmediatamente."}], "references": [{"url": "https://git.kernel.org/stable/c/0d38b4ca6679e72860ff8730e79bb99d0e9fa3b0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1c89642e7f2b7ecc9635610653f5c2f0276c0051", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2b45d6d0c41cb9593868e476681efb1aae5078a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a99a8ec4c62180c889482a2ff6465033e0743458", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b49f6b2f21f543d4dc88fb7b1ec2adccb822f27c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b736592de2aa53aee2d48d6b129bc0c892007bbe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f8017317cb0b279b8ab98b0f3901a2e0ac880dad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
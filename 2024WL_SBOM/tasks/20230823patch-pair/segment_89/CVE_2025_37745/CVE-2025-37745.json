{"cve_id": "CVE-2025-37745", "published_date": "2025-05-01T13:15:53.207", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nPM: hibernate: Avoid deadlock in hibernate_compressor_param_set()\n\nsyzbot reported a deadlock in lock_system_sleep() (see below).\n\nThe write operation to \"/sys/module/hibernate/parameters/compressor\"\nconflicts with the registration of ieee80211 device, resulting in a deadlock\nwhen attempting to acquire system_transition_mutex under param_lock.\n\nTo avoid this deadlock, change hibernate_compressor_param_set() to use\nmutex_trylock() for attempting to acquire system_transition_mutex and\nreturn -EBUSY when it fails.\n\nTask flags need not be saved or adjusted before calling\nmutex_trylock(&system_transition_mutex) because the caller is not going\nto end up waiting for this mutex and if it runs concurrently with system\nsuspend in progress, it will be frozen properly when it returns to user\nspace.\n\nsyzbot report:\n\nsyz-executor895/5833 is trying to acquire lock:\nffffffff8e0828c8 (system_transition_mutex){+.+.}-{4:4}, at: lock_system_sleep+0x87/0xa0 kernel/power/main.c:56\n\nbut task is already holding lock:\nffffffff8e07dc68 (param_lock){+.+.}-{4:4}, at: kernel_param_lock kernel/params.c:607 [inline]\nffffffff8e07dc68 (param_lock){+.+.}-{4:4}, at: param_attr_store+0xe6/0x300 kernel/params.c:586\n\nwhich lock already depends on the new lock.\n\nthe existing dependency chain (in reverse order) is:\n\n-> #3 (param_lock){+.+.}-{4:4}:\n       __mutex_lock_common kernel/locking/mutex.c:585 [inline]\n       __mutex_lock+0x19b/0xb10 kernel/locking/mutex.c:730\n       ieee80211_rate_control_ops_get net/mac80211/rate.c:220 [inline]\n       rate_control_alloc net/mac80211/rate.c:266 [inline]\n       ieee80211_init_rate_ctrl_alg+0x18d/0x6b0 net/mac80211/rate.c:1015\n       ieee80211_register_hw+0x20cd/0x4060 net/mac80211/main.c:1531\n       mac80211_hwsim_new_radio+0x304e/0x54e0 drivers/net/wireless/virtual/mac80211_hwsim.c:5558\n       init_mac80211_hwsim+0x432/0x8c0 drivers/net/wireless/virtual/mac80211_hwsim.c:6910\n       do_one_initcall+0x128/0x700 init/main.c:1257\n       do_initcall_level init/main.c:1319 [inline]\n       do_initcalls init/main.c:1335 [inline]\n       do_basic_setup init/main.c:1354 [inline]\n       kernel_init_freeable+0x5c7/0x900 init/main.c:1568\n       kernel_init+0x1c/0x2b0 init/main.c:1457\n       ret_from_fork+0x45/0x80 arch/x86/kernel/process.c:148\n       ret_from_fork_asm+0x1a/0x30 arch/x86/entry/entry_64.S:244\n\n-> #2 (rtnl_mutex){+.+.}-{4:4}:\n       __mutex_lock_common kernel/locking/mutex.c:585 [inline]\n       __mutex_lock+0x19b/0xb10 kernel/locking/mutex.c:730\n       wg_pm_notification drivers/net/wireguard/device.c:80 [inline]\n       wg_pm_notification+0x49/0x180 drivers/net/wireguard/device.c:64\n       notifier_call_chain+0xb7/0x410 kernel/notifier.c:85\n       notifier_call_chain_robust kernel/notifier.c:120 [inline]\n       blocking_notifier_call_chain_robust kernel/notifier.c:345 [inline]\n       blocking_notifier_call_chain_robust+0xc9/0x170 kernel/notifier.c:333\n       pm_notifier_call_chain_robust+0x27/0x60 kernel/power/main.c:102\n       snapshot_open+0x189/0x2b0 kernel/power/user.c:77\n       misc_open+0x35a/0x420 drivers/char/misc.c:179\n       chrdev_open+0x237/0x6a0 fs/char_dev.c:414\n       do_dentry_open+0x735/0x1c40 fs/open.c:956\n       vfs_open+0x82/0x3f0 fs/open.c:1086\n       do_open fs/namei.c:3830 [inline]\n       path_openat+0x1e88/0x2d80 fs/namei.c:3989\n       do_filp_open+0x20c/0x470 fs/namei.c:4016\n       do_sys_openat2+0x17a/0x1e0 fs/open.c:1428\n       do_sys_open fs/open.c:1443 [inline]\n       __do_sys_openat fs/open.c:1459 [inline]\n       __se_sys_openat fs/open.c:1454 [inline]\n       __x64_sys_openat+0x175/0x210 fs/open.c:1454\n       do_syscall_x64 arch/x86/entry/common.c:52 [inline]\n       do_syscall_64+0xcd/0x250 arch/x86/entry/common.c:83\n       entry_SYSCALL_64_after_hwframe+0x77/0x7f\n\n-> #1 ((pm_chain_head).rwsem){++++}-{4:4}:\n       down_read+0x9a/0x330 kernel/locking/rwsem.c:1524\n       blocking_notifier_call_chain_robust kerne\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: PM: hibernate: Evitar interbloqueo en hibernate_compressor_param_set(). syzbot reportó un interbloqueo en lock_system_sleep() (ver más abajo). La operación de escritura en \"/sys/module/hibernate/parameters/compressor\" entra en conflicto con el registro del dispositivo ieee80211, lo que resulta en un interbloqueo al intentar adquirir system_transition_mutex bajo param_lock. Para evitar este interbloqueo, modifique hibernate_compressor_param_set() para usar mutex_trylock() al intentar adquirir system_transition_mutex y devolver -EBUSY si falla. No es necesario guardar ni ajustar los indicadores de tarea antes de llamar a mutex_trylock(&amp;system_transition_mutex), ya que el llamador no esperará este mutex y, si se ejecuta simultáneamente con la suspensión del sistema en curso, se congelará correctamente al regresar al espacio de usuario. Informe de syzbot: syz-executor895/5833 está intentando adquirir el bloqueo: ffffffff8e0828c8 (system_transition_mutex){+.+.}-{4:4}, en: lock_system_sleep+0x87/0xa0 kernel/power/main.c:56 pero la tarea ya tiene el bloqueo: ffffffff8e07dc68 (param_lock){+.+.}-{4:4}, en: kernel_param_lock kernel/params.c:607 [en línea] ffffffff8e07dc68 (param_lock){+.+.}-{4:4}, en: param_attr_store+0xe6/0x300 kernel/params.c:586 cuyo bloqueo ya depende del nuevo bloqueo. la cadena de dependencia existente (en orden inverso) es: -&gt; #3 (param_lock){+.+.}-{4:4}: __mutex_lock_common kernel/locking/mutex.c:585 [en línea] __mutex_lock+0x19b/0xb10 kernel/locking/mutex.c:730 ieee80211_rate_control_ops_get net/mac80211/rate.c:220 [en línea] rate_control_alloc net/mac80211/rate.c:266 [en línea] ieee80211_init_rate_ctrl_alg+0x18d/0x6b0 net/mac80211/rate.c:1015 ieee80211_register_hw+0x20cd/0x4060 net/mac80211/main.c:1531 mac80211_hwsim_new_radio+0x304e/0x54e0 drivers/net/wireless/virtual/mac80211_hwsim.c:5558 init_mac80211_hwsim+0x432/0x8c0 drivers/net/wireless/virtual/mac80211_hwsim.c:6910 hacer_una_initcall+0x128/0x700 init/main.c:1257 hacer_nivel_initcall init/main.c:1319 [en línea] hacer_initcalls init/main.c:1335 [en línea] hacer_configuración_básica init/main.c:1354 [en línea] kernel_init_freeable+0x5c7/0x900 init/main.c:1568 kernel_init+0x1c/0x2b0 init/main.c:1457 ret_from_fork+0x45/0x80 arch/x86/kernel/process.c:148 ret_from_fork_asm+0x1a/0x30 arch/x86/entry/entry_64.S:244 -&gt; #2 (rtnl_mutex){+.+.}-{4:4}: __mutex_lock_common kernel/locking/mutex.c:585 [en línea] __mutex_lock+0x19b/0xb10 kernel/locking/mutex.c:730 wg_pm_notification drivers/net/wireguard/device.c:80 [en línea] wg_pm_notification+0x49/0x180 controladores/net/wireguard/device.c:64 cadena_de_llamadas_del_notificador+0xb7/0x410 kernel/notifier.c:85 cadena_de_llamadas_del_notificador_robust kernel/notifier.c:120 [en línea] cadena_de_llamadas_del_notificador_robust kernel/notifier.c:345 [en línea] cadena_de_llamadas_del_notificador_robust+0xc9/0x170 kernel/notifier.c:333 cadena_de_llamadas_del_notificador_robust+0x27/0x60 kernel/power/main.c:102 snapshot_open+0x189/0x2b0 kernel/power/user.c:77 misc_open+0x35a/0x420 controladores/char/misc.c:179 chrdev_open+0x237/0x6a0 fs/char_dev.c:414 do_dentry_open+0x735/0x1c40 fs/open.c:956 vfs_open+0x82/0x3f0 fs/open.c:1086 do_open fs/namei.c:3830 [en línea] path_openat+0x1e88/0x2d80 fs/namei.c:3989 do_filp_open+0x20c/0x470 fs/namei.c:4016 do_sys_openat2+0x17a/0x1e0 fs/open.c:1428 do_sys_open fs/open.c:1443 [en línea] __do_sys_openat fs/open.c:1459 [en línea] __se_sys_openat fs/open.c:1454 [en línea] __x64_sys_openat+0x175/0x210 fs/open.c:1454 do_syscall_x64 arch/x86/entry/common.c:52 [en línea] do_syscall_64+0xcd/0x250 arch/x86/entry/common.c:83 entry_SYSCALL_64_after_hwframe+0x77/0x7f -&gt; #1 ((pm_chain_head).rwsem){++++}-{4:4}: down_read+0x9a/0x330 kernel/locking/rwsem.c:1524 blocking_notifier_call_chain_robust kernel ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/11ae4fec1f4b4ee06770a572c37d89cbaecbf66e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3b2c3806ef4253595dfcb8b58352cfab55c9bfb0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/52323ed1444ea5c2a5f1754ea0a2d9c8c216ccdf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6dbaa8583af74814a5aae03a337cb1722c414808", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
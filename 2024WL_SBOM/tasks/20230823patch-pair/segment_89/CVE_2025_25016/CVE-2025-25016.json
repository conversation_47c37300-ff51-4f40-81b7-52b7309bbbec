{"cve_id": "CVE-2025-25016", "published_date": "2025-05-01T14:15:36.930", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "Unrestricted file upload in Kibana allows an authenticated attacker to compromise software integrity by uploading a crafted malicious file due to insufficient server-side validation."}, {"lang": "es", "value": "La carga de archivos sin restricciones en Kibana permite que un atacante autenticado comprometa la integridad del software al cargar un archivo malicioso manipulado debido a una validación insuficiente del lado del servidor."}], "references": [{"url": "https://discuss.elastic.co/t/kibana-7-17-19-and-8-13-0-security-update-esa-2024-47/377711", "source": "<EMAIL>", "tags": []}]}
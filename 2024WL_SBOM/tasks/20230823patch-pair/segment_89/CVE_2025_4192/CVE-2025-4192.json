{"cve_id": "CVE-2025-4192", "published_date": "2025-05-02T01:15:54.520", "last_modified_date": "2025-05-16T17:39:07.340", "descriptions": [{"lang": "en", "value": "A vulnerability was found in itsourcecode Restaurant Management System 1.0. It has been classified as critical. This affects an unknown part of the file /admin/category_save.php. The manipulation of the argument Category leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en itsourcecode Restaurant Management System 1.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/category_save.php. La manipulación del argumento \"Category\" provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ARPANET-cybersecurity/vuldb/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://itsourcecode.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306807", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306807", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.561838", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://github.com/XuepengZhao-insp/vuldb/issues/4", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}
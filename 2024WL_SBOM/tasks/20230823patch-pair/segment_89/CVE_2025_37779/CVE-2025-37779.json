{"cve_id": "CVE-2025-37779", "published_date": "2025-05-01T14:15:41.733", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nlib/iov_iter: fix to increase non slab folio refcount\n\nWhen testing EROFS file-backed mount over v9fs on qemu, I encountered a\nfolio UAF issue.  The page sanity check reports the following call trace. \nThe root cause is that pages in bvec are coalesced across a folio bounary.\nThe refcount of all non-slab folios should be increased to ensure\np9_releas_pages can put them correctly.\n\nBUG: Bad page state in process md5sum  pfn:18300\npage: refcount:0 mapcount:0 mapping:00000000d5ad8e4e index:0x60 pfn:0x18300\nhead: order:0 mapcount:0 entire_mapcount:0 nr_pages_mapped:0 pincount:0\naops:z_erofs_aops ino:30b0f dentry name(?):\"GoogleExtServicesCn.apk\"\nflags: 0x100000000000041(locked|head|node=0|zone=1)\nraw: 0100000000000041 dead000000000100 dead000000000122 ffff888014b13bd0\nraw: 0000000000000060 0000000000000020 00000000ffffffff 0000000000000000\nhead: 0100000000000041 dead000000000100 dead000000000122 ffff888014b13bd0\nhead: 0000000000000060 0000000000000020 00000000ffffffff 0000000000000000\nhead: 0100000000000000 0000000000000000 ffffffffffffffff 0000000000000000\nhead: 0000000000000010 0000000000000000 00000000ffffffff 0000000000000000\npage dumped because: PAGE_FLAGS_CHECK_AT_FREE flag(s) set\nCall Trace:\n dump_stack_lvl+0x53/0x70\n bad_page+0xd4/0x220\n __free_pages_ok+0x76d/0xf30\n __folio_put+0x230/0x320\n p9_release_pages+0x179/0x1f0\n p9_virtio_zc_request+0xa2a/0x1230\n p9_client_zc_rpc.constprop.0+0x247/0x700\n p9_client_read_once+0x34d/0x810\n p9_client_read+0xf3/0x150\n v9fs_issue_read+0x111/0x360\n netfs_unbuffered_read_iter_locked+0x927/0x1390\n netfs_unbuffered_read_iter+0xa2/0xe0\n vfs_iocb_iter_read+0x2c7/0x460\n erofs_fileio_rq_submit+0x46b/0x5b0\n z_erofs_runqueue+0x1203/0x21e0\n z_erofs_readahead+0x579/0x8b0\n read_pages+0x19f/0xa70\n page_cache_ra_order+0x4ad/0xb80\n filemap_readahead.isra.0+0xe7/0x150\n filemap_get_pages+0x7aa/0x1890\n filemap_read+0x320/0xc80\n vfs_read+0x6c6/0xa30\n ksys_read+0xf9/0x1c0\n do_syscall_64+0x9e/0x1a0\n entry_SYSCALL_64_after_hwframe+0x71/0x79"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: lib/iov_iter: corrección para aumentar el recuento de referencias de folios no slab. Al probar el montaje basado en archivos EROFS sobre v9fs en qemu, se detectó un problema con el UAF de folio. La comprobación de integridad de la página informa el siguiente seguimiento de llamadas. La causa principal es que las páginas en bvec se fusionan en un límite de folio. Se debe aumentar el recuento de referencias de todos los folios no slab para garantizar que p9_relas_pages pueda colocarlos correctamente. ERROR: Estado de página incorrecto en proceso md5sum pfn:18300 página: refcount:0 mapcount:0 mapping:00000000d5ad8e4e índice:0x60 pfn:0x18300 encabezado: orden:0 mapcount:0 entire_mapcount:0 nr_pages_mapped:0 pincount:0 aops:z_erofs_aops ino:30b0f dentry name(?):\"GoogleExtServicesCn.apk\" indicadores: 0x100000000000041(locked|head|node=0|zone=1) sin procesar: 0100000000000041 muerto000000000100 muerto000000000122 ffff888014b13bd0 sin procesar: 0000000000000060 0000000000000020 00000000ffffffff 0000000000000000 cabeza: 0100000000000041 muerto000000000100 muerto000000000122 ffff888014b13bd0 cabeza: 0000000000000060 0000000000000020 00000000ffffffff 000000000000000 cabeza: 010000000000000 000000000000000 ffffffffffffffff 00000000000000000 encabezado: 0000000000000010 0000000000000000 00000000ffffffff 0000000000000000 página volcada porque: PAGE_FLAGS_CHECK_AT_FREE indicador(es) establecido(s) Seguimiento de llamada: dump_stack_lvl+0x53/0x70 bad_page+0xd4/0x220 __free_pages_ok+0x76d/0xf30 __folio_put+0x230/0x320 p9_release_pages+0x179/0x1f0 p9_virtio_zc_request+0xa2a/0x1230 p9_client_zc_rpc.constprop.0+0x247/0x700 p9_client_read_once+0x34d/0x810 p9_client_read+0xf3/0x150 v9fs_issue_read+0x111/0x360 netfs_unbuffered_read_iter_locked+0x927/0x1390 netfs_unbuffered_read_iter+0xa2/0xe0 vfs_iocb_iter_read+0x2c7/0x460 erofs_fileio_rq_submit+0x46b/0x5b0 z_erofs_runqueue+0x1203/0x21e0 z_erofs_readahead+0x579/0x8b0 read_pages+0x19f/0xa70 page_cache_ra_order+0x4ad/0xb80 filemap_readahead.isra.0+0xe7/0x150 filemap_get_pages+0x7aa/0x1890 filemap_read+0x320/0xc80 vfs_read+0x6c6/0xa30 ksys_read+0xf9/0x1c0 do_syscall_64+0x9e/0x1a0 entry_SYSCALL_64_after_hwframe+0x71/0x79 "}], "references": [{"url": "https://git.kernel.org/stable/c/770c8d55c42868239c748a3ebc57c9e37755f842", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d833f21162c4d536d729628f8cf1ee8d4110f2b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
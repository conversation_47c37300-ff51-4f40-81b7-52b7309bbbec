{"cve_id": "CVE-2022-49880", "published_date": "2025-05-01T15:16:12.960", "last_modified_date": "2025-05-07T13:21:17.940", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: fix warning in 'ext4_da_release_space'\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> report issue as follows:\nEXT4-fs (loop0): Free/Dirty block details\nEXT4-fs (loop0): free_blocks=0\nEXT4-fs (loop0): dirty_blocks=0\nEXT4-fs (loop0): Block reservation details\nEXT4-fs (loop0): i_reserved_data_blocks=0\nEXT4-fs warning (device loop0): ext4_da_release_space:1527: ext4_da_release_space: ino 18, to_free 1 with only 0 reserved data blocks\n------------[ cut here ]------------\nWARNING: CPU: 0 PID: 92 at fs/ext4/inode.c:1528 ext4_da_release_space+0x25e/0x370 fs/ext4/inode.c:1524\nModules linked in:\nCPU: 0 PID: 92 Comm: kworker/u4:4 Not tainted 6.0.0-syzkaller-09423-g493ffd6605b2 #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 09/22/2022\nWorkqueue: writeback wb_workfn (flush-7:0)\nRIP: 0010:ext4_da_release_space+0x25e/0x370 fs/ext4/inode.c:1528\nRSP: 0018:ffffc900015f6c90 EFLAGS: 00010296\nRAX: 42215896cd52ea00 RBX: 0000000000000000 RCX: 42215896cd52ea00\nRDX: 0000000000000000 RSI: 0000000080000001 RDI: 0000000000000000\nRBP: 1ffff1100e907d96 R08: ffffffff816aa79d R09: fffff520002bece5\nR10: fffff520002bece5 R11: 1ffff920002bece4 R12: ffff888021fd2000\nR13: ffff88807483ecb0 R14: 0000000000000001 R15: ffff88807483e740\nFS:  0000000000000000(0000) GS:ffff8880b9a00000(0000) knlGS:0000000000000000\nCS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\nCR2: 00005555569ba628 CR3: 000000000c88e000 CR4: 00000000003506f0\nDR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000\nDR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400\nCall Trace:\n <TASK>\n ext4_es_remove_extent+0x1ab/0x260 fs/ext4/extents_status.c:1461\n mpage_release_unused_pages+0x24d/0xef0 fs/ext4/inode.c:1589\n ext4_writepages+0x12eb/0x3be0 fs/ext4/inode.c:2852\n do_writepages+0x3c3/0x680 mm/page-writeback.c:2469\n __writeback_single_inode+0xd1/0x670 fs/fs-writeback.c:1587\n writeback_sb_inodes+0xb3b/0x18f0 fs/fs-writeback.c:1870\n wb_writeback+0x41f/0x7b0 fs/fs-writeback.c:2044\n wb_do_writeback fs/fs-writeback.c:2187 [inline]\n wb_workfn+0x3cb/0xef0 fs/fs-writeback.c:2227\n process_one_work+0x877/0xdb0 kernel/workqueue.c:2289\n worker_thread+0xb14/0x1330 kernel/workqueue.c:2436\n kthread+0x266/0x300 kernel/kthread.c:376\n ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:306\n </TASK>\n\nAbove issue may happens as follows:\next4_da_write_begin\n  ext4_create_inline_data\n    ext4_clear_inode_flag(inode, EXT4_INODE_EXTENTS);\n    ext4_set_inode_flag(inode, EXT4_INODE_INLINE_DATA);\n__ext4_ioctl\n  ext4_ext_migrate -> will lead to eh->eh_entries not zero, and set extent flag\next4_da_write_begin\n  ext4_da_convert_inline_data_to_extent\n    ext4_da_write_inline_data_begin\n      ext4_da_map_blocks\n        ext4_insert_delayed_block\n\t  if (!ext4_es_scan_clu(inode, &ext4_es_is_delonly, lblk))\n\t    if (!ext4_es_scan_clu(inode, &ext4_es_is_mapped, lblk))\n\t      ext4_clu_mapped(inode, EXT4_B2C(sbi, lblk)); -> will return 1\n\t       allocated = true;\n          ext4_es_insert_delayed_block(inode, lblk, allocated);\next4_writepages\n  mpage_map_and_submit_extent(handle, &mpd, &give_up_on_write); -> return -ENOSPC\n  mpage_release_unused_pages(&mpd, give_up_on_write); -> give_up_on_write == 1\n    ext4_es_remove_extent\n      ext4_da_release_space(inode, reserved);\n        if (unlikely(to_free > ei->i_reserved_data_blocks))\n\t  -> to_free == 1  but ei->i_reserved_data_blocks == 0\n\t  -> then trigger warning as above\n\nTo solve above issue, forbid inode do migrate which has inline data."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: corrección de advertencia en 'ext4_da_release_space' Syzkaller informa del problema de la siguiente manera: EXT4-fs (loop0): Detalles del bloque libre/sucio EXT4-fs (loop0): free_blocks=0 EXT4-fs (loop0): dirty_blocks=0 EXT4-fs (loop0): Detalles de la reserva del bloque EXT4-fs (loop0): i_reserved_data_blocks=0 Advertencia de EXT4-fs (dispositivo loop0): ext4_da_release_space:1527: ext4_da_release_space: ino 18, to_free 1 con solo 0 bloques de datos reservados ------------[ cortar aquí ]------------ ADVERTENCIA: CPU: 0 PID: 92 en fs/ext4/inode.c:1528 ext4_da_release_space+0x25e/0x370 fs/ext4/inode.c:1524 Módulos vinculados: CPU: 0 PID: 92 Comm: kworker/u4:4 No contaminado 6.0.0-syz<PERSON>ler-09423-g493ffd6605b2 #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 22/09/2022 Cola de trabajo: escritura diferida wb_workfn (flush-7:0) RIP: 0010:ext4_da_release_space+0x25e/0x370 fs/ext4/inode.c:1528 RSP: 0018:ffffc900015f6c90 EFLAGS: 00010296 RAX: 42215896cd52ea00 RBX: 0000000000000000 RCX: 42215896cd52ea00 RDX: 0000000000000000 RSI: 0000000080000001 RDI: 0000000000000000 RBP: 1ffff1100e907d96 R08: ffffffff816aa79d R09: fffff520002bece5 R10: fffff520002bece5 R11: 1ffff920002bece4 R12: ffff888021fd2000 R13: ffff88807483ecb0 R14: 0000000000000001 R15: ffff88807483e740FS: 0000000000000000(0000) GS:ffff8880b9a00000(0000) knlGS:0000000000000000 CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 CR2: 00005555569ba628 CR3: 000000000c88e000 CR4: 00000000003506f0 DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000 DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400 Rastreo de llamadas:  ext4_es_remove_extent+0x1ab/0x260 fs/ext4/extents_status.c:1461 mpage_release_unused_pages+0x24d/0xef0 fs/ext4/inode.c:1589 ext4_writepages+0x12eb/0x3be0 fs/ext4/inode.c:2852 do_writepages+0x3c3/0x680 mm/page-writeback.c:2469 __writeback_single_inode+0xd1/0x670 fs/fs-writeback.c:1587 writeback_sb_inodes+0xb3b/0x18f0 fs/fs-writeback.c:1870 wb_writeback+0x41f/0x7b0 fs/fs-writeback.c:2044 wb_do_writeback fs/fs-writeback.c:2187 [inline] wb_workfn+0x3cb/0xef0 fs/fs-writeback.c:2227 process_one_work+0x877/0xdb0 kernel/workqueue.c:2289 worker_thread+0xb14/0x1330 kernel/workqueue.c:2436 kthread+0x266/0x300 kernel/kthread.c:376 ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:306  El problema anterior puede ocurrir de la siguiente manera: ext4_da_write_begin ext4_create_inline_data ext4_clear_inode_flag(inode, EXT4_INODE_EXTENTS); ext4_set_inode_flag(inode, EXT4_INODE_INLINE_DATA); __ext4_ioctl ext4_ext_migrate -&gt; hará que eh-&gt;eh_entries no sea cero y establecerá el indicador de extensión ext4_da_write_begin ext4_da_convert_inline_data_to_extent ext4_da_write_inline_data_begin ext4_da_map_blocks ext4_insert_delayed_block si (!ext4_es_scan_clu(inodo, &amp;ext4_es_is_delonly, lblk)) si (!ext4_es_scan_clu(inodo, &amp;ext4_es_is_mapped, lblk)) ext4_clu_mapped(inodo, EXT4_B2C(sbi, lblk)); -&gt; devolverá 1 asignado = verdadero; ext4_es_insert_delayed_block(inodo, lblk, asignado); ext4_writepages mpage_map_and_submit_extent(handle, &amp;mpd, &amp;give_up_on_write); -&gt; return -ENOSPC mpage_release_unused_pages(&amp;mpd, give_up_on_write); -&gt; give_up_on_write == 1 ext4_es_remove_extent ext4_da_release_space(inode, reserved); if (unlikely(to_free &gt; ei-&gt;i_reserved_data_blocks)) -&gt; to_free == 1 but ei-&gt;i_reserved_data_blocks == 0 -&gt; then trigger WARNING Como se indica arriba, para resolver el problema anterior, prohíba migrar los inodes que tienen datos en línea."}], "references": [{"url": "https://git.kernel.org/stable/c/0a43c015e98121c91a76154edf42280ce1a8a883", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/0de5ee103747fd3a24f1c010c79caabe35e8f0bb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1b8f787ef547230a3249bcf897221ef0cc78481b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5370b965b7a945bb8f48b9ee23d83a76a947902e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/72743d5598b9096950bbfd6a9b7f173d156eea97", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/890d738f569fa9412b70ba09f15407f17a52da20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/89bee03d2fb8c54119b38ac6c24e7d60fae036b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c3bf1e95cfa7d950dc3c064d0c2e3d06b427bc63", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
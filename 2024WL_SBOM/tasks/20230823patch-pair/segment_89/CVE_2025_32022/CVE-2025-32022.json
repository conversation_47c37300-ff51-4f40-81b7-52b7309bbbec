{"cve_id": "CVE-2025-32022", "published_date": "2025-05-06T17:16:12.097", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "Finit provides fast init for Linux systems. Finit's urandom plugin has a heap buffer overwrite vulnerability at boot which leads to it overwriting other parts of the heap, possibly causing random instabilities and undefined behavior. The urandom plugin is enabled by default, so this bug affects everyone using Finit 4.2 or later that do not explicitly disable the plugin at build time. This bug is fixed in Finit 4.12. Those who cannot upgrade or backport the fix to urandom.c are strongly recommended to disable the plugin in the call to the `configure` script."}, {"lang": "es", "value": "Finit proporciona un inicio rápido para sistemas Linux. El complemento urandom de Finit presenta una vulnerabilidad de sobrescritura del búfer del montón durante el arranque, lo que provoca que sobrescriba otras partes del montón, lo que podría causar inestabilidades aleatorias y un comportamiento indefinido. El complemento urandom está habilitado por defecto, por lo que este error afecta a todos los usuarios de Finit 4.2 o posterior que no lo desactiven explícitamente durante la compilación. Este error se corrigió en Finit 4.12. Se recomienda encarecidamente a quienes no puedan actualizar o implementar la corrección a urandom.c que desactiven el complemento al llamar al script `configure`."}], "references": [{"url": "https://github.com/troglobit/finit/commit/3feff37ba51fa0a6a0a06f59682a0918aa5b04de", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/troglobit/finit/security/advisories/GHSA-fv6v-vw8h-9x79", "source": "<EMAIL>", "tags": []}]}
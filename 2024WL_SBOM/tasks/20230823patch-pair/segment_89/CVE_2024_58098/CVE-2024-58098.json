{"cve_id": "CVE-2024-58098", "published_date": "2025-05-05T15:15:53.810", "last_modified_date": "2025-05-09T08:15:18.823", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: track changes_pkt_data property for global functions\n\nWhen processing calls to certain helpers, verifier invalidates all\npacket pointers in a current state. For example, consider the\nfollowing program:\n\n    __attribute__((__noinline__))\n    long skb_pull_data(struct __sk_buff *sk, __u32 len)\n    {\n        return bpf_skb_pull_data(sk, len);\n    }\n\n    SEC(\"tc\")\n    int test_invalidate_checks(struct __sk_buff *sk)\n    {\n        int *p = (void *)(long)sk->data;\n        if ((void *)(p + 1) > (void *)(long)sk->data_end) return TCX_DROP;\n        skb_pull_data(sk, 0);\n        *p = 42;\n        return TCX_PASS;\n    }\n\nAfter a call to bpf_skb_pull_data() the pointer 'p' can't be used\nsafely. See function filter.c:bpf_helper_changes_pkt_data() for a list\nof such helpers.\n\nAt the moment verifier invalidates packet pointers when processing\nhelper function calls, and does not traverse global sub-programs when\nprocessing calls to global sub-programs. This means that calls to\nhelpers done from global sub-programs do not invalidate pointers in\nthe caller state. E.g. the program above is unsafe, but is not\nrejected by verifier.\n\nThis commit fixes the omission by computing field\nbpf_subprog_info->changes_pkt_data for each sub-program before main\nverification pass.\nchanges_pkt_data should be set if:\n- subprogram calls helper for which bpf_helper_changes_pkt_data\n  returns true;\n- subprogram calls a global function,\n  for which bpf_subprog_info->changes_pkt_data should be set.\n\nThe verifier.c:check_cfg() pass is modified to compute this\ninformation. The commit relies on depth first instruction traversal\ndone by check_cfg() and absence of recursive function calls:\n- check_cfg() would eventually visit every call to subprogram S in a\n  state when S is fully explored;\n- when S is fully explored:\n  - every direct helper call within S is explored\n    (and thus changes_pkt_data is set if needed);\n  - every call to subprogram S1 called by S was visited with S1 fully\n    explored (and thus S inherits changes_pkt_data from S1).\n\nThe downside of such approach is that dead code elimination is not\ntaken into account: if a helper call inside global function is dead\nbecause of current configuration, verifier would conservatively assume\nthat the call occurs for the purpose of the changes_pkt_data\ncomputation."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: propiedad `track changes_pkt_data` para funciones globales. Al procesar llamadas a ciertos ayudantes, el verificador invalida todos los punteros de paquete en un estado actual. Por ejemplo, considere el siguiente programa: __attribute__((__noinline__)) long skb_pull_data(struct __sk_buff *sk, __u32 len) { return bpf_skb_pull_data(sk, len); } SEC(\"tc\") int test_invalidate_checks(struct __sk_buff *sk) { int *p = (void *)(long)sk-&gt;data; if ((void *)(p + 1) &gt; (void *)(long)sk-&gt;data_end) return TCX_DROP; skb_pull_data(sk, 0); *p = 42; return TCX_PASS; Tras una llamada a bpf_skb_pull_data(), el puntero 'p' no se puede usar de forma segura. Consulte la función filter.c:bpf_helper_changes_pkt_data() para obtener una lista de dichos ayudantes. Actualmente, el verificador invalida los punteros de paquetes al procesar llamadas a funciones de ayuda y no recorre subprogramas globales al procesar llamadas a subprogramas globales. Esto significa que las llamadas a ayudantes realizadas desde subprogramas globales no invalidan los punteros en el estado del llamador. Por ejemplo, el programa anterior es inseguro, pero no es rechazado por el verificador. Esta confirmación corrige la omisión calculando el campo bpf_subprog_info-&gt;changes_pkt_data para cada subprograma antes de la verificación principal. changes_pkt_data debe establecerse si: - el subprograma llama a un ayudante para el cual bpf_helper_changes_pkt_data devuelve verdadero; - el subprograma llama a una función global, para la cual bpf_subprog_info-&gt;changes_pkt_data debe establecerse. El pase verifier.c:check_cfg() se modifica para calcular esta información. el commit se basa en el recorrido de la instrucción de profundidad primero realizado por check_cfg() y la ausencia de llamadas a funciones recursivas: - check_cfg() eventualmente visitaría cada llamada al subprograma S en un estado cuando S está completamente explorado; - cuando S está completamente explorado: - cada llamada de ayuda directa dentro de S es explorada (y por lo tanto changes_pkt_data se establece si es necesario); - cada llamada al subprograma S1 llamada por S fue visitada con S1 completamente explorado (y por lo tanto S hereda changes_pkt_data de S1). La desventaja de este enfoque es que no se tiene en cuenta la eliminación de código muerto: si una llamada de ayuda dentro de una función global está muerta debido a la configuración actual, el verificador asumiría conservadoramente que la llamada ocurre para el propósito del cálculo de changes_pkt_data."}], "references": [{"url": "https://git.kernel.org/stable/c/1d572c60488b52882b719ed273767ee3b280413d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/51081a3f25c742da5a659d7fc6fd77ebfdd555be", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/79751e9227a5910c0e5a2c7186877d91821d957d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
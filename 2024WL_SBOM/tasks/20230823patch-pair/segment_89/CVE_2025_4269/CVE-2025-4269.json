{"cve_id": "CVE-2025-4269", "published_date": "2025-05-05T07:15:48.233", "last_modified_date": "2025-05-07T16:38:25.260", "descriptions": [{"lang": "en", "value": "A vulnerability was found in TOTOLINK A720R 4.1.5cu.374 and classified as critical. This issue affects some unknown processing of the file /cgi-bin/cstecgi.cgi of the component Log Handler. The manipulation of the argument topicurl with the input clearDiagnosisLog/clearSyslog/clearTracerouteLog leads to improper access controls. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en TOTOLINK A720R 4.1.5cu.374, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /cgi-bin/cstecgi.cgi del componente Log Handler. La manipulación del argumento topicurl con la entrada clearDiagnosisLog/clearSyslog/clearTracerouteLog genera controles de acceso inadecuados. El ataque podría iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/at0de/my_vulns/blob/main/TOTOLINK/A720R/clearDiagnosisLog.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/at0de/my_vulns/blob/main/TOTOLINK/A720R/clearSyslog.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.307373", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307373", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.563430", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}
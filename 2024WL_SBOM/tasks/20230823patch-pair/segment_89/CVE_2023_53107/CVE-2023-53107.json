{"cve_id": "CVE-2023-53107", "published_date": "2025-05-02T16:15:29.620", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nveth: Fix use after free in XDP_REDIRECT\n\nCommit 718a18a0c8a6 (\"veth: Rework veth_xdp_rcv_skb in order\nto accept non-linear skb\") introduced a bug where it tried to\nuse pskb_expand_head() if the headroom was less than\nXDP_PACKET_HEADROOM.  This however uses kmalloc to expand the head,\nwhich will later allow consume_skb() to free the skb while is it still\nin use by AF_XDP.\n\nPreviously if the headroom was less than XDP_PACKET_HEADROOM we\ncontinued on to allocate a new skb from pages so this restores that\nbehavior.\n\nBUG: KASAN: use-after-free in __xsk_rcv+0x18d/0x2c0\nRead of size 78 at addr ffff888976250154 by task napi/iconduit-g/148640\n\nCPU: 5 PID: 148640 Comm: napi/iconduit-g Kdump: loaded Tainted: G           O       6.1.4-cloudflare-kasan-2023.1.2 #1\nHardware name: Quanta Computer Inc. QuantaPlex T41S-2U/S2S-MB, BIOS S2S_3B10.03 06/21/2018\nCall Trace:\n  <TASK>\n  dump_stack_lvl+0x34/0x48\n  print_report+0x170/0x473\n  ? __xsk_rcv+0x18d/0x2c0\n  kasan_report+0xad/0x130\n  ? __xsk_rcv+0x18d/0x2c0\n  kasan_check_range+0x149/0x1a0\n  memcpy+0x20/0x60\n  __xsk_rcv+0x18d/0x2c0\n  __xsk_map_redirect+0x1f3/0x490\n  ? veth_xdp_rcv_skb+0x89c/0x1ba0 [veth]\n  xdp_do_redirect+0x5ca/0xd60\n  veth_xdp_rcv_skb+0x935/0x1ba0 [veth]\n  ? __netif_receive_skb_list_core+0x671/0x920\n  ? veth_xdp+0x670/0x670 [veth]\n  veth_xdp_rcv+0x304/0xa20 [veth]\n  ? do_xdp_generic+0x150/0x150\n  ? veth_xdp_rcv_one+0xde0/0xde0 [veth]\n  ? _raw_spin_lock_bh+0xe0/0xe0\n  ? newidle_balance+0x887/0xe30\n  ? __perf_event_task_sched_in+0xdb/0x800\n  veth_poll+0x139/0x571 [veth]\n  ? veth_xdp_rcv+0xa20/0xa20 [veth]\n  ? _raw_spin_unlock+0x39/0x70\n  ? finish_task_switch.isra.0+0x17e/0x7d0\n  ? __switch_to+0x5cf/0x1070\n  ? __schedule+0x95b/0x2640\n  ? io_schedule_timeout+0x160/0x160\n  __napi_poll+0xa1/0x440\n  napi_threaded_poll+0x3d1/0x460\n  ? __napi_poll+0x440/0x440\n  ? __kthread_parkme+0xc6/0x1f0\n  ? __napi_poll+0x440/0x440\n  kthread+0x2a2/0x340\n  ? kthread_complete_and_exit+0x20/0x20\n  ret_from_fork+0x22/0x30\n  </TASK>\n\nFreed by task 148640:\n  kasan_save_stack+0x23/0x50\n  kasan_set_track+0x21/0x30\n  kasan_save_free_info+0x2a/0x40\n  ____kasan_slab_free+0x169/0x1d0\n  slab_free_freelist_hook+0xd2/0x190\n  __kmem_cache_free+0x1a1/0x2f0\n  skb_release_data+0x449/0x600\n  consume_skb+0x9f/0x1c0\n  veth_xdp_rcv_skb+0x89c/0x1ba0 [veth]\n  veth_xdp_rcv+0x304/0xa20 [veth]\n  veth_poll+0x139/0x571 [veth]\n  __napi_poll+0xa1/0x440\n  napi_threaded_poll+0x3d1/0x460\n  kthread+0x2a2/0x340\n  ret_from_fork+0x22/0x30\n\nThe buggy address belongs to the object at ffff888976250000\n  which belongs to the cache kmalloc-2k of size 2048\nThe buggy address is located 340 bytes inside of\n  2048-byte region [ffff888976250000, ffff888976250800)\n\nThe buggy address belongs to the physical page:\npage:00000000ae18262a refcount:2 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x976250\nhead:00000000ae18262a order:3 compound_mapcount:0 compound_pincount:0\nflags: 0x2ffff800010200(slab|head|node=0|zone=2|lastcpupid=0x1ffff)\nraw: 002ffff800010200 0000000000000000 dead000000000122 ffff88810004cf00\nraw: 0000000000000000 0000000080080008 00000002ffffffff 0000000000000000\npage dumped because: kasan: bad access detected\n\nMemory state around the buggy address:\n  ffff888976250000: fa fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n  ffff888976250080: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n> ffff888976250100: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n                                                  ^\n  ffff888976250180: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb\n  ffff888976250200: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: veth: Corrección del use after free en XDP_REDIRECT. el commit 718a18a0c8a6 (\"veth: Reestructurar veth_xdp_rcv_skb para aceptar skb no lineal\") introdujo un error que provocaba que se intentara usar pskb_expand_head() si el espacio libre era inferior a XDP_PACKET_HEADROOM. Sin embargo, esto utiliza kmalloc para expandir el espacio libre, lo que posteriormente permitirá que consuma_skb() libere el skb mientras AF_XDP lo siga utilizando. Anteriormente, si el espacio libre era inferior a XDP_PACKET_HEADROOM, se asignaba un nuevo skb desde las páginas, por lo que esto restaura ese comportamiento. ERROR: KASAN: use-after-free en __xsk_rcv+0x18d/0x2c0 Lectura de tamaño 78 en la dirección ffff888976250154 por la tarea napi/iconduit-g/148640 CPU: 5 PID: 148640 Comm: napi/iconduit-g Kdump: cargado Contaminado: GO 6.1.4-cloudflare-kasan-2023.1.2 #1 Nombre del hardware: Quanta Computer Inc. QuantaPlex T41S-2U/S2S-MB, BIOS S2S_3B10.03 21/06/2018 Seguimiento de llamadas:  dump_stack_lvl+0x34/0x48 print_report+0x170/0x473 ? __xsk_rcv+0x18d/0x2c0 kasan_report+0xad/0x130 ? __xsk_rcv+0x18d/0x2c0 kasan_check_range+0x149/0x1a0 memcpy+0x20/0x60 __xsk_rcv+0x18d/0x2c0 __xsk_map_redirect+0x1f3/0x490 ? veth_xdp_rcv_skb+0x89c/0x1ba0 [veth] xdp_do_redirect+0x5ca/0xd60 veth_xdp_rcv_skb+0x935/0x1ba0 [veth] ? __netif_receive_skb_list_core+0x671/0x920 ? veth_xdp+0x670/0x670 [veth] veth_xdp_rcv+0x304/0xa20 [veth] ? do_xdp_generic+0x150/0x150 ? veth_xdp_rcv_one+0xde0/0xde0 [veth] ? _raw_spin_lock_bh+0xe0/0xe0 ? newidle_balance+0x887/0xe30 ? __perf_event_task_sched_in+0xdb/0x800 veth_poll+0x139/0x571 [veth] ? veth_xdp_rcv+0xa20/0xa20 [veth] ? _raw_spin_unlock+0x39/0x70 ? finish_task_switch.isra.0+0x17e/0x7d0 ? __switch_to+0x5cf/0x1070 ? __schedule+0x95b/0x2640 ? io_schedule_timeout+0x160/0x160 __napi_poll+0xa1/0x440 napi_threaded_poll+0x3d1/0x460 ? __napi_poll+0x440/0x440 ? __kthread_parkme+0xc6/0x1f0 ? __napi_poll+0x440/0x440 kthread+0x2a2/0x340 ? kthread_complete_and_exit+0x20/0x20 ret_from_fork+0x22/0x30  Freed by task 148640: kasan_save_stack+0x23/0x50 kasan_set_track+0x21/0x30 kasan_save_free_info+0x2a/0x40 ____kasan_slab_free+0x169/0x1d0 slab_free_freelist_hook+0xd2/0x190 __kmem_cache_free+0x1a1/0x2f0 skb_release_data+0x449/0x600 consume_skb+0x9f/0x1c0 veth_xdp_rcv_skb+0x89c/0x1ba0 [veth] veth_xdp_rcv+0x304/0xa20 [veth] veth_poll+0x139/0x571 [veth] __napi_poll+0xa1/0x440 napi_threaded_poll+0x3d1/0x460 kthread+0x2a2/0x340 ret_from_fork+0x22/0x30 The buggy address belongs to the object at ffff888976250000 which belongs to the cache kmalloc-2k of size 2048 The buggy address is located 340 bytes inside of 2048-byte region [ffff888976250000, ffff888976250800) The buggy address belongs to the physical page: page:00000000ae18262a refcount:2 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x976250 head:00000000ae18262a order:3 compound_mapcount:0 compound_pincount:0 flags: 0x2ffff800010200(slab|head|node=0|zone=2|lastcpupid=0x1ffff) raw: 002ffff800010200 0000000000000000 dead000000000122 ffff88810004cf00 raw: 0000000000000000 0000000080080008 00000002ffffffff 0000000000000000 page dumped because: kasan: bad access detected Memory state around the buggy address: ffff888976250000: fa fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ffff888976250080: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb &gt; ffff888976250100: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ^ ffff888976250180: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb ffff888976250200: fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb fb "}], "references": [{"url": "https://git.kernel.org/stable/c/6e755b56896df48b0fae0db275e148f8d8aa7d6f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/717d20710596b5b26595ede454d1105fa176f4a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7c10131803e45269ddc6c817f19ed649110f3cae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-4175", "published_date": "2025-05-01T21:15:54.687", "last_modified_date": "2025-05-10T06:15:49.270", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in AlanBinu007 Spring-Boot-Advanced-Projects up to 3.1.3. This affects the function uploadUserProfileImage of the file /Spring-Boot-Advanced-Projects-main/Project-4.SpringBoot-AWS-S3/backend/src/main/java/com/urunov/profile/UserProfileController.java of the component Upload Profile API Endpoint. The manipulation of the argument File leads to path traversal. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en AlanBinu007 Spring-Boot-Advanced-Projects hasta la versión 3.1.3. Esta vulnerabilidad afecta a la función uploadUserProfileImage del archivo /Spring-Boot-Advanced-Projects-main/Project-4.SpringBoot-AWS-S3/backend/src/main/java/com/urunov/profile/UserProfileController.jav del componente Upload Profile API Endpoint. La manipulación del argumento File provoca un path traversal. Es posible iniciar el ataque de forma remota. El exploit se ha divulgado públicamente y podría utilizarse. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/ShenxiuSec/cve-proofs/blob/main/POC-20250418-01.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.306795", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.306795", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.561760", "source": "<EMAIL>", "tags": []}]}
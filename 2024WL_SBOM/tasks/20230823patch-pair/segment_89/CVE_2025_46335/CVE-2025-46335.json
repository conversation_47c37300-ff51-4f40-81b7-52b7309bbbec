{"cve_id": "CVE-2025-46335", "published_date": "2025-05-05T19:15:56.487", "last_modified_date": "2025-05-28T20:06:23.400", "descriptions": [{"lang": "en", "value": "Mobile Security Framework (MobSF) is a security research platform for mobile applications in Android, iOS and Windows Mobile. A Stored Cross-Site Scripting (XSS) vulnerability has been identified in MobSF versions up to and including 4.3.2. The vulnerability arises from improper sanitization of user-supplied SVG files during the Android APK analysis workflow. Version 4.3.3 fixes the issue."}, {"lang": "es", "value": "Mobile Security Framework (MobSF) es una plataforma de investigación de seguridad para aplicaciones móviles en Android, iOS y Windows Mobile. Se ha identificado una vulnerabilidad de cross-site scripting (XSS) almacenado en las versiones de MobSF hasta la 4.3.2. Esta vulnerabilidad surge de la limpieza incorrecta de los archivos SVG proporcionados por el usuario durante el flujo de trabajo de análisis de APK de Android. La versión 4.3.3 corrige el problema."}], "references": [{"url": "https://github.com/MobSF/Mobile-Security-Framework-MobSF/commit/6987a946485a795f4fd38cebdb4860b368a1995d", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/MobSF/Mobile-Security-Framework-MobSF/security/advisories/GHSA-mwfg-948f-2cc5", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
{"cve_id": "CVE-2025-4143", "published_date": "2025-05-01T01:15:54.127", "last_modified_date": "2025-05-12T19:39:43.820", "descriptions": [{"lang": "en", "value": "The OAuth implementation in workers-oauth-provider that is part of  MCP framework https://github.com/cloudflare/workers-mcp , did not correctly validate that redirect_uri was on the allowed list of redirect URIs for the given client registration.\n\nFixed in:  https://github.com/cloudflare/workers-oauth-provider/pull/26 https://github.com/cloudflare/workers-oauth-provider/pull/26 \n\nImpact:\n\n \n\nUnder certain circumstances (see below), if a victim had previously authorized with a server built on workers-oath-provider, and an attacker could later trick the victim into visiting a malicious web site, then attacker could potentially steal the victim's credentials to the same OAuth server and subsequently impersonate them.\n\nIn order for the attack to be possible, the OAuth server's authorized callback must be designed to auto-approve authorizations that appear to come from an OAuth client that the victim has authorized previously. The authorization flow is not implemented by workers-oauth-provider; it is up to the application built on top to decide whether to implement such automatic re-authorization. However, many applications do implement such logic.\n\nNote: It is a basic, well-known requirement that OAuth servers should verify that the redirect URI is among the allowed list for the client, both during the authorization flow and subsequently when exchanging the authorization code for an access token. workers-oauth-provider implemented only the latter check, not the former. Unfortunately, the former is the much more important check. Readers who are familiar with OAuth may recognize that failing to check redirect URIs against the allowed list is a well-known, basic mistake, covered extensively in the RFC and elsewhere. The author of this library would like everyone to know that he was, in fact, well-aware of this requirement, thought about it a lot while designing the library, and then, somehow, forgot to actually make sure the check was in the code. That is, it's not that he didn't know what he was doing, it's that he knew what he was doing but flubbed it."}, {"lang": "es", "value": "La implementación de OAuth en workers-oauth-provider que forma parte del framework MCP https://github.com/cloudflare/workers-mcp , no validó correctamente que redirect_uri estuviera en la lista permitida de URI de redirección para el registro de cliente dado. Corregido en: https://github.com/cloudflare/workers-oauth-provider/pull/26 https://github.com/cloudflare/workers-oauth-provider/pull/26 Impacto: Bajo ciertas circunstancias (ver abajo), si una víctima había autorizado previamente con un servidor construido sobre workers-oath-provider, y un atacante pudiera engañar posteriormente a la víctima para que visitara un sitio web malicioso, entonces el atacante podría potencialmente robar las credenciales de la víctima al mismo servidor OAuth y posteriormente suplantarlas. Para que el ataque sea posible, la devolución de llamada autorizada del servidor OAuth debe estar diseñada para aprobar automáticamente las autorizaciones que parecen provenir de un cliente OAuth que la víctima ha autorizado previamente. El flujo de autorización no lo implementa workers-oauth-provider; la aplicación subyacente decide si implementa o no dicha reautorización automática. Sin embargo, muchas aplicaciones sí implementan esta lógica. Nota: Es un requisito básico y bien conocido que los servidores OAuth verifiquen que la URI de redirección se encuentre en la lista de permitidos para el cliente, tanto durante el flujo de autorización como posteriormente al intercambiar el código de autorización por un token de acceso. workers-oauth-provider implementó solo esta última comprobación, no la primera. Desafortunadamente, la primera es mucho más importante. Los lectores familiarizados con OAuth reconocerán que no comprobar las URI de redirección con la lista de permitidos es un error básico y bien conocido, ampliamente tratado en el RFC y en otras fuentes. El autor de esta librería desea que todos sepan que, de hecho, conocía bien este requisito, lo consideró detenidamente durante su diseño y, por alguna razón, olvidó asegurarse de que la comprobación estuviera incluida en el código. Es decir, no es que no supiera lo que hacía, es que sabía lo que hacía pero lo hizo mal."}], "references": [{"url": "https://github.com/cloudflare/workers-oauth-provider/pull/26", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}]}
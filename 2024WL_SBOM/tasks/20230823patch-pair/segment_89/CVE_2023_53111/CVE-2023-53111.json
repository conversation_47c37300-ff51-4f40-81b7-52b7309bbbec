{"cve_id": "CVE-2023-53111", "published_date": "2025-05-02T16:15:30.027", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nloop: Fix use-after-free issues\n\ndo_req_filebacked() calls blk_mq_complete_request() synchronously or\nasynchronously when using asynchronous I/O unless memory allocation fails.\nHence, modify loop_handle_cmd() such that it does not dereference 'cmd' nor\n'rq' after do_req_filebacked() finished unless we are sure that the request\nhas not yet been completed. This patch fixes the following kernel crash:\n\nUnable to handle kernel NULL pointer dereference at virtual address 0000000000000054\nCall trace:\n css_put.42938+0x1c/0x1ac\n loop_process_work+0xc8c/0xfd4\n loop_rootcg_workfn+0x24/0x34\n process_one_work+0x244/0x558\n worker_thread+0x400/0x8fc\n kthread+0x16c/0x1e0\n ret_from_fork+0x10/0x20"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: loop: Se corrigen los problemas de use after free. do_req_filebacked() llama a blk_mq_complete_request() de forma síncrona o asíncrona al usar E/S asíncrona, a menos que falle la asignación de memoria. Por lo tanto, se debe modificar loop_handle_cmd() para que no desreferencia «cmd» ni «rq» tras la finalización de do_req_filebacked(), a menos que estemos seguros de que la solicitud aún no se ha completado. Este parche corrige el siguiente fallo del kernel: No se puede manejar la desreferencia del puntero NULL del kernel en la dirección virtual 0000000000000054 Seguimiento de llamadas: css_put.42938+0x1c/0x1ac loop_process_work+0xc8c/0xfd4 loop_rootcg_workfn+0x24/0x34 process_one_work+0x244/0x558worker_thread+0x400/0x8fckthread+0x16c/0x1e0ret_from_fork+0x10/0x20"}], "references": [{"url": "https://git.kernel.org/stable/c/407badf73ec9fb0d5744bf2ca1745c1818aa222f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6917395c4667cfb607ed8bf1826205a59414657c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9b0cb770f5d7b1ff40bea7ca385438ee94570eec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3fda704903f6d1fc351412f1bc6620333959ada", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-45018", "published_date": "2025-04-30T14:15:29.547", "last_modified_date": "2025-05-09T13:44:16.990", "descriptions": [{"lang": "en", "value": "A SQL Injection vulnerability was discovered in the foreigner-bwdates-reports-details.php file of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary SQL code via the todate parameter."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección SQL en el archivo foreigner-bwdates-reports-details.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código SQL arbitrario mediante el parámetro \"todate\"."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/SQL/SQl_Injection_in_was_foreigner-bwdates-reports-details.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
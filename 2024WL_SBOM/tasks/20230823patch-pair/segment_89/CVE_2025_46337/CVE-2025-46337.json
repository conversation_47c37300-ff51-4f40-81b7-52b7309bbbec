{"cve_id": "CVE-2025-46337", "published_date": "2025-05-01T18:15:57.510", "last_modified_date": "2025-05-26T18:15:20.450", "descriptions": [{"lang": "en", "value": "ADOdb is a PHP database class library that provides abstractions for performing queries and managing databases. Prior to version 5.22.9, improper escaping of a query parameter may allow an attacker to execute arbitrary SQL statements when the code using ADOdb connects to a PostgreSQL database and calls pg_insert_id() with user-supplied data. This issue has been patched in version 5.22.9."}, {"lang": "es", "value": "ADOdb es una librería de clases de bases de datos PHP que proporciona abstracciones para realizar consultas y administrar bases de datos. Antes de la versión 5.22.9, el escape incorrecto de un parámetro de consulta podía permitir que un atacante ejecutara sentencias SQL arbitrarias cuando el código que usa ADOdb se conecta a una base de datos PostgreSQL e invoca pg_insert_id() con datos proporcionados por el usuario. Este problema se ha corregido en la versión 5.22.9."}], "references": [{"url": "https://github.com/ADOdb/ADOdb/commit/11107d6d6e5160b62e05dff8a3a2678cf0e3a426", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ADOdb/ADOdb/issues/1070", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ADOdb/ADOdb/security/advisories/GHSA-8x27-jwjr-8545", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2025/05/msg00029.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://xaliom.blogspot.com/2025/05/from-sast-to-cve-2025-46337.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
{"cve_id": "CVE-2025-45240", "published_date": "2025-05-05T17:18:49.733", "last_modified_date": "2025-06-12T17:44:27.277", "descriptions": [{"lang": "en", "value": "foxcms v1.2.5 was discovered to contain a SQL injection vulnerability via the executeCommand method in DataBackup.php."}, {"lang": "es", "value": "Se descubrió que foxcms v1.2.5 contiene una vulnerabilidad de inyección SQL a través del método executeCommand en DataBackup.php."}], "references": [{"url": "https://gist.github.com/chao112122/648033972709fb50b3c89363cd64a9a4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://gitee.com/qianfox/foxcms", "source": "<EMAIL>", "tags": ["Product"]}]}
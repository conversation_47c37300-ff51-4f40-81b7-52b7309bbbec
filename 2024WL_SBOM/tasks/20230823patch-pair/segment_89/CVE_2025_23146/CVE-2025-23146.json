{"cve_id": "CVE-2025-23146", "published_date": "2025-05-01T13:15:50.453", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmfd: ene-kb3930: Fix a potential NULL pointer dereference\n\nThe off_gpios could be NULL. Add missing check in the kb3930_probe().\nThis is similar to the issue fixed in commit b1ba8bcb2d1f\n(\"backlight: hx8357: Fix potential NULL pointer dereference\").\n\nThis was detected by our static analysis tool."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mfd: ene-kb3930: Se corrige una posible desreferencia de puntero nulo. El valor off_gpios podría ser nulo. Se añade la comprobación faltante en kb3930_probe(). Esto es similar al problema corregido en el commit b1ba8bcb2d1f (\"backlight: hx8357: Se corrige una posible desreferencia de puntero nulo\"). Esto fue detectado por nuestra herramienta de análisis estático."}], "references": [{"url": "https://git.kernel.org/stable/c/2edb5b29b197d90b4d08cd45e911c0bcf24cb895", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4cdf1d2a816a93fa02f7b6b5492dc7f55af2a199", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6dc88993ee3fa8365ff6a5d6514702f70ba6863a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76d0f4199bc5b51acb7b96c6663a8953543733ad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7b47df6498f223c8956bfe0d994a0e42a520dfcd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90ee23c2514a22a9c2bb39a540cbe1c9acb27d0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b1758417310d2cc77e52cd15103497e52e2614f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ea07760676bba49319d553af80c239da053b5fb1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
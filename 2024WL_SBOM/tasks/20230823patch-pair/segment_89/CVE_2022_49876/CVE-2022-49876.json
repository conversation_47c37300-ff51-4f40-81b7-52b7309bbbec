{"cve_id": "CVE-2022-49876", "published_date": "2025-05-01T15:16:12.550", "last_modified_date": "2025-05-07T13:21:31.450", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: mac80211: fix general-protection-fault in ieee80211_subif_start_xmit()\n\nWhen device is running and the interface status is changed, the gpf issue\nis triggered. The problem triggering process is as follows:\nThread A:                           Thread B\nieee80211_runtime_change_iftype()   process_one_work()\n    ...                                 ...\n    ieee80211_do_stop()                 ...\n    ...                                 ...\n        sdata->bss = NULL               ...\n        ...                             ieee80211_subif_start_xmit()\n                                            ieee80211_multicast_to_unicast\n                                    //!sdata->bss->multicast_to_unicast\n                                      cause gpf issue\n\nWhen the interface status is changed, the sending queue continues to send\npackets. After the bss is set to NULL, the bss is accessed. As a result,\nthis causes a general-protection-fault issue.\n\nThe following is the stack information:\ngeneral protection fault, probably for non-canonical address\n0xdffffc000000002f: 0000 [#1] PREEMPT SMP KASAN\nKASAN: null-ptr-deref in range [0x0000000000000178-0x000000000000017f]\nWorkqueue: mld mld_ifc_work\nRIP: 0010:ieee80211_subif_start_xmit+0x25b/0x1310\nCall Trace:\n<TASK>\ndev_hard_start_xmit+0x1be/0x990\n__dev_queue_xmit+0x2c9a/0x3b60\nip6_finish_output2+0xf92/0x1520\nip6_finish_output+0x6af/0x11e0\nip6_output+0x1ed/0x540\nmld_sendpack+0xa09/0xe70\nmld_ifc_work+0x71c/0xdb0\nprocess_one_work+0x9bf/0x1710\nworker_thread+0x665/0x1080\nkthread+0x2e4/0x3a0\nret_from_fork+0x1f/0x30\n</TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: mac80211: fix general-protection-fault in ieee80211_subif_start_xmit() Cuando el dispositivo está en ejecución y se cambia el estado de la interfaz, se activa el problema gpf. El proceso de activación del problema es el siguiente: Hilo A: Hilo B ieee80211_runtime_change_iftype() process_one_work() ... ... ieee80211_do_stop() ... ... ... sdata-&gt;bss = NULL ... ... ieee80211_subif_start_xmit() ieee80211_multicast_to_unicast //!sdata-&gt;bss-&gt;multicast_to_unicast cause gpf issue Cuando se cambia el estado de la interfaz, la cola de envío continúa enviando paquetes. Después de que bss se establece en NULL, se accede a bss. Como resultado, esto causa un problema de general-protection-fault. La siguiente es la información de la pila: falla de protección general, probablemente para la dirección no canónica 0xdffffc000000002f: 0000 [#1] PREEMPT SMP KASAN KASAN: null-ptr-deref en el rango [0x0000000000000178-0x000000000000017f] Cola de trabajo: mld mld_ifc_work RIP: 0010:ieee80211_subif_start_xmit+0x25b/0x1310 Rastreo de llamadas:  dev_hard_start_xmit+0x1be/0x990 __dev_queue_xmit+0x2c9a/0x3b60 ip6_finish_output2+0xf92/0x1520 ip6_finish_output+0x6af/0x11e0 ip6_output+0x1ed/0x540 mld_sendpack+0xa09/0xe70 mld_ifc_work+0x71c/0xdb0 process_one_work+0x9bf/0x1710 worker_thread+0x665/0x1080 kthread+0x2e4/0x3a0 ret_from_fork+0x1f/0x30  "}], "references": [{"url": "https://git.kernel.org/stable/c/03eb68c72cee249aeb7af7d04a83c033aca3d6d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/780854186946e0de2be192ee7fa5125666533b3a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-21475", "published_date": "2025-05-06T09:15:24.557", "last_modified_date": "2025-05-09T19:08:32.710", "descriptions": [{"lang": "en", "value": "Memory corruption while processing escape code, when DisplayId is passed with large unsigned value."}, {"lang": "es", "value": "Corrupción de memoria durante el procesamiento del código de escape, cuando DisplayId se pasa con un valor grande sin signo."}], "references": [{"url": "https://docs.qualcomm.com/product/publicresources/securitybulletin/may-2025-bulletin.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
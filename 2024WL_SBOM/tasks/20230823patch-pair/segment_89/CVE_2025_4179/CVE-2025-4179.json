{"cve_id": "CVE-2025-4179", "published_date": "2025-05-02T03:15:21.540", "last_modified_date": "2025-05-06T15:41:48.960", "descriptions": [{"lang": "en", "value": "The Flynax Bridge plugin for WordPress is vulnerable to limited Privilege Escalation due to a missing capability check on the registerUser() function in all versions up to, and including, 2.2.0. This makes it possible for unauthenticated attackers to register new user accounts as authors."}, {"lang": "es", "value": "El complemento Flynax Bridge para WordPress es vulnerable a una escalada de privilegios limitada debido a la falta de una comprobación de capacidad en la función registerUser() en todas las versiones hasta la 2.2.0 incluida. Esto permite que atacantes no autenticados registren nuevas cuentas de usuario como autores."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/flynax-bridge/trunk/src/API.php#L288", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a2447cf4-0261-4ef2-98ec-98fa02dc8b87?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-53083", "published_date": "2025-05-02T16:15:27.310", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfsd: don't replace page in rq_pages if it's a continuation of last page\n\nThe splice read calls nfsd_splice_actor to put the pages containing file\ndata into the svc_rqst->rq_pages array. It's possible however to get a\nsplice result that only has a partial page at the end, if (e.g.) the\nfilesystem hands back a short read that doesn't cover the whole page.\n\nnfsd_splice_actor will plop the partial page into its rq_pages array and\nreturn. Then later, when nfsd_splice_actor is called again, the\nremainder of the page may end up being filled out. At this point,\nnfsd_splice_actor will put the page into the array _again_ corrupting\nthe reply. If this is done enough times, rq_next_page will overrun the\narray and corrupt the trailing fields -- the rq_respages and\nrq_next_page pointers themselves.\n\nIf we've already added the page to the array in the last pass, don't add\nit to the array a second time when dealing with a splice continuation.\nThis was originally handled properly in nfsd_splice_actor, but commit\n91e23b1c3982 (\"NFSD: Clean up nfsd_splice_actor()\") removed the check\nfor it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfsd: no reemplace la página en rq_pages si es una continuación de la última página la lectura de empalme llama a nfsd_splice_actor para poner las páginas que contienen datos de archivo en la matriz svc_rqst-&gt;rq_pages. Sin embargo, es posible obtener un resultado de empalme que solo tenga una página parcial al final, si (p. ej.) el sistema de archivos devuelve una lectura corta que no cubre toda la página. nfsd_splice_actor colocará la página parcial en su matriz rq_pages y retornará. Luego, más tarde, cuando se vuelva a llamar a nfsd_splice_actor, el resto de la página puede terminar llenándose. En este punto, nfsd_splice_actor colocará la página en array _again_ corrompiendo la respuesta. Si esto se repite varias veces, rq_next_page saturará el array y corromperá los campos finales: los punteros rq_respages y rq_next_page. Si ya añadimos la página al array en la última pasada, no la añadamos una segunda vez al tratar con una continuación de empalme. Esto se gestionaba correctamente en nfsd_splice_actor, pero el commit 91e23b1c3982 (\"NFSD: Limpiar nfsd_splice_actor()\") eliminó la comprobación."}], "references": [{"url": "https://git.kernel.org/stable/c/0101067f376eb7b9afd00279270f25d5111a091d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/12eca509234acb6b666802edf77408bb70d7bfca", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/27c934dd8832dd40fd34776f916dc201e18b319b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/51ddb84baff6f09ad62b5999ece3ec172e4e3568", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8235cd619db6e67f1d7d26c55f1f3e4e575c947d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-44868", "published_date": "2025-05-02T15:15:49.123", "last_modified_date": "2025-06-13T13:04:52.783", "descriptions": [{"lang": "en", "value": "Wavlink WL-WN530H4 20220801 was found to contain a command injection vulnerability in the ping_test function of the adm.cgi via the pingIp parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que Wavlink WL-WN530H4 20220801 contenía una vulnerabilidad de inyección de comandos en la función ping_test de adm.cgi mediante el parámetro pingIp. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Wavlink_WL-WN530H4/ping_test/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/Summermu/VulnForIoT/blob/main/Wavlink_WL-WN530H4/ping_test/readme.md", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-23142", "published_date": "2025-05-01T13:15:50.017", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsctp: detect and prevent references to a freed transport in sendmsg\n\nsctp_sendmsg() re-uses associations and transports when possible by\ndoing a lookup based on the socket endpoint and the message destination\naddress, and then sctp_sendmsg_to_asoc() sets the selected transport in\nall the message chunks to be sent.\n\nThere's a possible race condition if another thread triggers the removal\nof that selected transport, for instance, by explicitly unbinding an\naddress with setsockopt(SCTP_SOCKOPT_BINDX_REM), after the chunks have\nbeen set up and before the message is sent. This can happen if the send\nbuffer is full, during the period when the sender thread temporarily\nreleases the socket lock in sctp_wait_for_sndbuf().\n\nThis causes the access to the transport data in\nsctp_outq_select_transport(), when the association outqueue is flushed,\nto result in a use-after-free read.\n\nThis change avoids this scenario by having sctp_transport_free() signal\nthe freeing of the transport, tagging it as \"dead\". In order to do this,\nthe patch restores the \"dead\" bit in struct sctp_transport, which was\nremoved in\ncommit 47faa1e4c50e (\"sctp: remove the dead field of sctp_transport\").\n\nThen, in the scenario where the sender thread has released the socket\nlock in sctp_wait_for_sndbuf(), the bit is checked again after\nre-acquiring the socket lock to detect the deletion. This is done while\nholding a reference to the transport to prevent it from being freed in\nthe process.\n\nIf the transport was deleted while the socket lock was relinquished,\nsctp_sendmsg_to_asoc() will return -EAGAIN to let userspace retry the\nsend.\n\nThe bug was found by a private syzbot instance (see the error report [1]\nand the C reproducer that triggers it [2])."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: sctp: detectar y prevenir referencias a un transporte liberado en sendmsg. sctp_sendmsg() reutiliza asociaciones y transportes cuando es posible mediante una búsqueda basada en el endpoint del socket y la dirección de destino del mensaje. A continuación, sctp_sendmsg_to_asoc() establece el transporte seleccionado en todos los fragmentos de mensaje que se enviarán. Existe una posible condición de ejecución si otro subproceso activa la eliminación de ese transporte seleccionado, por ejemplo, desvinculando explícitamente una dirección con setsockopt(SCTP_SOCKOPT_BINDX_REM), después de configurar los fragmentos y antes de enviar el mensaje. Esto puede ocurrir si el búfer de envío está lleno, durante el periodo en que el subproceso emisor libera temporalmente el bloqueo del socket en sctp_wait_for_sndbuf(). Esto provoca que el acceso a los datos de transporte en sctp_outq_select_transport(), al vaciar la cola de salida de la asociación, resulte en una lectura de use-after-free. Este cambio evita este escenario al indicar sctp_transport_free() la liberación del transporte, etiquetándolo como \"muerto\". Para ello, el parche restaura el bit \"muerto\" en la estructura sctp_transport, que se eliminó en el commit 47faa1e4c50e (\"sctp: eliminar el campo muerto de sctp_transport\"). Si el hilo emisor ha liberado el bloqueo del socket en sctp_wait_for_sndbuf(), el bit se vuelve a comprobar tras volver a adquirir el bloqueo para detectar la eliminación. Esto se realiza manteniendo una referencia al transporte para evitar que se libere durante el proceso. Si el transporte se eliminó mientras se liberaba el bloqueo del socket, sctp_sendmsg_to_asoc() devolverá -EAGAIN para que el espacio de usuario pueda reintentar el envío. El error fue detectado por una instancia privada de syzbot (consulte el informe de errores [1] y el reproductor de C que lo activa [2])."}], "references": [{"url": "https://git.kernel.org/stable/c/0f7df4899299ce4662e5f95badb9dbc57cc37fa5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2e5068b7e0ae0a54f6cfd03a2f80977da657f1ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3257386be6a7eb8a8bfc9cbfb746df4eb4fc70e8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/547762250220325d350d0917a7231480e0f4142b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5bc83bdf5f5b8010d1ca5a4555537e62413ab4e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7a63f4fb0efb4e69efd990cbb740a848679ec4b0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e7c37fadb3be1fc33073fcf10aa96d166caa697", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c6fefcb71d246baaf3bacdad1af7ff50ebcfe652", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1a69a940de58b16e8249dff26f74c8cc59b32be", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
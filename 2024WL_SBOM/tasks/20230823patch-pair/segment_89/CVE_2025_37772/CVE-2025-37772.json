{"cve_id": "CVE-2025-37772", "published_date": "2025-05-01T14:15:40.580", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRDMA/cma: Fix workqueue crash in cma_netevent_work_handler\n\nstruct rdma_cm_id has member \"struct work_struct net_work\"\nthat is reused for enqueuing cma_netevent_work_handler()s\nonto cma_wq.\n\nBelow crash[1] can occur if more than one call to\ncma_netevent_callback() occurs in quick succession,\nwhich further enqueues cma_netevent_work_handler()s for the\nsame rdma_cm_id, overwriting any previously queued work-item(s)\nthat was just scheduled to run i.e. there is no guarantee\nthe queued work item may run between two successive calls\nto cma_netevent_callback() and the 2nd INIT_WORK would overwrite\nthe 1st work item (for the same rdma_cm_id), despite grabbing\nid_table_lock during enqueue.\n\nAlso drgn analysis [2] indicates the work item was likely overwritten.\n\nFix this by moving the INIT_WORK() to __rdma_create_id(),\nso that it doesn't race with any existing queue_work() or\nits worker thread.\n\n[1] Trimmed crash stack:\n=============================================\nBUG: kernel NULL pointer dereference, address: 0000000000000008\nkworker/u256:6 ... 6.12.0-0...\nWorkqueue:  cma_netevent_work_handler [rdma_cm] (rdma_cm)\nRIP: 0010:process_one_work+0xba/0x31a\nCall Trace:\n worker_thread+0x266/0x3a0\n kthread+0xcf/0x100\n ret_from_fork+0x31/0x50\n ret_from_fork_asm+0x1a/0x30\n=============================================\n\n[2] drgn crash analysis:\n\n>>> trace = prog.crashed_thread().stack_trace()\n>>> trace\n(0)  crash_setup_regs (./arch/x86/include/asm/kexec.h:111:15)\n(1)  __crash_kexec (kernel/crash_core.c:122:4)\n(2)  panic (kernel/panic.c:399:3)\n(3)  oops_end (arch/x86/kernel/dumpstack.c:382:3)\n...\n(8)  process_one_work (kernel/workqueue.c:3168:2)\n(9)  process_scheduled_works (kernel/workqueue.c:3310:3)\n(10) worker_thread (kernel/workqueue.c:3391:4)\n(11) kthread (kernel/kthread.c:389:9)\n\nLine workqueue.c:3168 for this kernel version is in process_one_work():\n3168\tstrscpy(worker->desc, pwq->wq->name, WORKER_DESC_LEN);\n\n>>> trace[8][\"work\"]\n*(struct work_struct *)0xffff92577d0a21d8 = {\n\t.data = (atomic_long_t){\n\t\t.counter = (s64)536870912,    <=== Note\n\t},\n\t.entry = (struct list_head){\n\t\t.next = (struct list_head *)0xffff924d075924c0,\n\t\t.prev = (struct list_head *)0xffff924d075924c0,\n\t},\n\t.func = (work_func_t)cma_netevent_work_handler+0x0 = 0xffffffffc2cec280,\n}\n\nSuspicion is that pwq is NULL:\n>>> trace[8][\"pwq\"]\n(struct pool_workqueue *)<absent>\n\nIn process_one_work(), pwq is assigned from:\nstruct pool_workqueue *pwq = get_work_pwq(work);\n\nand get_work_pwq() is:\nstatic struct pool_workqueue *get_work_pwq(struct work_struct *work)\n{\n \tunsigned long data = atomic_long_read(&work->data);\n\n \tif (data & WORK_STRUCT_PWQ)\n \t\treturn work_struct_pwq(data);\n \telse\n \t\treturn NULL;\n}\n\nWORK_STRUCT_PWQ is 0x4:\n>>> print(repr(prog['WORK_STRUCT_PWQ']))\nObject(prog, 'enum work_flags', value=4)\n\nBut work->data is 536870912 which is 0x20000000.\nSo, get_work_pwq() returns NULL and we crash in process_one_work():\n3168\tstrscpy(worker->desc, pwq->wq->name, WORKER_DESC_LEN);\n============================================="}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: RDMA/cma: Se corrige el fallo de la cola de trabajo en cma_netevent_work_handler. La estructura rdma_cm_id tiene el miembro \"struct work_struct net_work\", que se reutiliza para encolar cma_netevent_work_handler()s en cma_wq. El fallo [1] puede ocurrir si se realizan varias llamadas a cma_netevent_callback() en rápida sucesión, lo que encola aún más cma_netevent_work_handler()s para el mismo rdma_cm_id, sobrescribiendo cualquier elemento de trabajo previamente en cola que se haya programado para ejecutarse. Es decir, no hay garantía de que el elemento de trabajo en cola se ejecute entre dos llamadas sucesivas a cma_netevent_callback() y el segundo INIT_WORK sobrescribiría el primer elemento de trabajo (para el mismo rdma_cm_id), a pesar de obtener id_table_lock durante la encola. Además, el análisis drgn [2] indica que es probable que el elemento de trabajo se haya sobrescrito. Para solucionarlo, mueva INIT_WORK() a __rdma_create_id(), de modo que no compita con ninguna queue_work() existente ni con su subproceso de trabajo. [1] Pila de fallos recortada: =============================================== ERROR: desreferencia de puntero NULL del núcleo, dirección: 000000000000008 kworker/u256:6 ... 6.12.0-0... Cola de trabajo: cma_netevent_work_handler [rdma_cm] (rdma_cm) RIP: 0010:process_one_work+0xba/0x31a Seguimiento de llamadas: worker_thread+0x266/0x3a0 kthread+0xcf/0x100 ret_from_fork+0x31/0x50 ret_from_fork_asm+0x1a/0x30 ============================================= [2] drgn crash analysis: &gt;&gt;&gt; trace = prog.crashed_thread().stack_trace() &gt;&gt;&gt; trace (0) crash_setup_regs (./arch/x86/include/asm/kexec.h:111:15) (1) __crash_kexec (kernel/crash_core.c:122:4) (2) panic (kernel/panic.c:399:3) (3) oops_end (arch/x86/kernel/dumpstack.c:382:3) ... (8) process_one_work (kernel/workqueue.c:3168:2) (9) process_scheduled_works (kernel/workqueue.c:3310:3) (10) worker_thread (kernel/workqueue.c:3391:4) (11) kthread (kernel/kthread.c:389:9) Line workqueue.c:3168 for this kernel version is in process_one_work(): 3168 strscpy(worker-&gt;desc, pwq-&gt;wq-&gt;name, WORKER_DESC_LEN); &gt;&gt;&gt; trace[8][\"work\"] *(struct work_struct *)0xffff92577d0a21d8 = { .data = (atomic_long_t){ .counter = (s64)536870912, &lt;=== Note }, .entry = (struct list_head){ .next = (struct list_head *)0xffff924d075924c0, .prev = (struct list_head *)0xffff924d075924c0, }, .func = (work_func_t)cma_netevent_work_handler+0x0 = 0xffffffffc2cec280, } Suspicion is that pwq is NULL: &gt;&gt;&gt; trace[8][\"pwq\"] (struct pool_workqueue *) In process_one_work(), pwq is assigned from: struct pool_workqueue *pwq = get_work_pwq(work); and get_work_pwq() is: static struct pool_workqueue *get_work_pwq(struct work_struct *work) { unsigned long data = atomic_long_read(&amp;work-&gt;data); if (data &amp; WORK_STRUCT_PWQ) return work_struct_pwq(data); else return NULL; } WORK_STRUCT_PWQ is 0x4: &gt;&gt;&gt; print(repr(prog['WORK_STRUCT_PWQ'])) Object(prog, 'enum work_flags', value=4) But work-&gt;data is 536870912 which is 0x20000000. So, get_work_pwq() returns NULL and we crash in process_one_work(): 3168 strscpy(worker-&gt;desc, pwq-&gt;wq-&gt;name, WORKER_DESC_LEN); ============================================= "}], "references": [{"url": "https://git.kernel.org/stable/c/45f5dcdd049719fb999393b30679605f16ebce14", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/51003b2c872c63d28bcf5fbcc52cf7b05615f7b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b172a4a0de254f1fcce7591833a9a63547c2f447", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c2b169fc7a12665d8a675c1ff14bca1b9c63fb9a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d23fd7a539ac078df119707110686a5b226ee3bb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
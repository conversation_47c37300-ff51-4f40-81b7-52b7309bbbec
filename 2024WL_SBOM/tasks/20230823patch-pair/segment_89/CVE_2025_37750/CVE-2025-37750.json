{"cve_id": "CVE-2025-37750", "published_date": "2025-05-01T13:15:53.740", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsmb: client: fix UAF in decryption with multichannel\n\nAfter commit f7025d861694 (\"smb: client: allocate crypto only for\nprimary server\") and commit b0abcd65ec54 (\"smb: client: fix UAF in\nasync decryption\"), the channels started reusing AEAD TFM from primary\nchannel to perform synchronous decryption, but that can't done as\nthere could be multiple cifsd threads (one per channel) simultaneously\naccessing it to perform decryption.\n\nThis fixes the following KASAN splat when running fstest generic/249\nwith 'vers=3.1.1,multichannel,max_channels=4,seal' against Windows\nServer 2022:\n\nBUG: KASAN: slab-use-after-free in gf128mul_4k_lle+0xba/0x110\nRead of size 8 at addr ffff8881046c18a0 by task cifsd/986\nCPU: 3 UID: 0 PID: 986 Comm: cifsd Not tainted 6.15.0-rc1 #1\nPREEMPT(voluntary)\nHardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-3.fc41\n04/01/2014\nCall Trace:\n <TASK>\n dump_stack_lvl+0x5d/0x80\n print_report+0x156/0x528\n ? gf128mul_4k_lle+0xba/0x110\n ? __virt_addr_valid+0x145/0x300\n ? __phys_addr+0x46/0x90\n ? gf128mul_4k_lle+0xba/0x110\n kasan_report+0xdf/0x1a0\n ? gf128mul_4k_lle+0xba/0x110\n gf128mul_4k_lle+0xba/0x110\n ghash_update+0x189/0x210\n shash_ahash_update+0x295/0x370\n ? __pfx_shash_ahash_update+0x10/0x10\n ? __pfx_shash_ahash_update+0x10/0x10\n ? __pfx_extract_iter_to_sg+0x10/0x10\n ? ___kmalloc_large_node+0x10e/0x180\n ? __asan_memset+0x23/0x50\n crypto_ahash_update+0x3c/0xc0\n gcm_hash_assoc_remain_continue+0x93/0xc0\n crypt_message+0xe09/0xec0 [cifs]\n ? __pfx_crypt_message+0x10/0x10 [cifs]\n ? _raw_spin_unlock+0x23/0x40\n ? __pfx_cifs_readv_from_socket+0x10/0x10 [cifs]\n decrypt_raw_data+0x229/0x380 [cifs]\n ? __pfx_decrypt_raw_data+0x10/0x10 [cifs]\n ? __pfx_cifs_read_iter_from_socket+0x10/0x10 [cifs]\n smb3_receive_transform+0x837/0xc80 [cifs]\n ? __pfx_smb3_receive_transform+0x10/0x10 [cifs]\n ? __pfx___might_resched+0x10/0x10\n ? __pfx_smb3_is_transform_hdr+0x10/0x10 [cifs]\n cifs_demultiplex_thread+0x692/0x1570 [cifs]\n ? __pfx_cifs_demultiplex_thread+0x10/0x10 [cifs]\n ? rcu_is_watching+0x20/0x50\n ? rcu_lockdep_current_cpu_online+0x62/0xb0\n ? find_held_lock+0x32/0x90\n ? kvm_sched_clock_read+0x11/0x20\n ? local_clock_noinstr+0xd/0xd0\n ? trace_irq_enable.constprop.0+0xa8/0xe0\n ? __pfx_cifs_demultiplex_thread+0x10/0x10 [cifs]\n kthread+0x1fe/0x380\n ? kthread+0x10f/0x380\n ? __pfx_kthread+0x10/0x10\n ? local_clock_noinstr+0xd/0xd0\n ? ret_from_fork+0x1b/0x60\n ? local_clock+0x15/0x30\n ? lock_release+0x29b/0x390\n ? rcu_is_watching+0x20/0x50\n ? __pfx_kthread+0x10/0x10\n ret_from_fork+0x31/0x60\n ? __pfx_kthread+0x10/0x10\n ret_from_fork_asm+0x1a/0x30\n </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: smb: cliente: corregir UAF en descifrado con multicanal Después de la confirmación f7025d861694 (\"smb: cliente: asignar criptografía solo para el servidor principal\") y la confirmación b0abcd65ec54 (\"smb: cliente: corregir UAF en descifrado asíncrono\"), los canales comenzaron a reutilizar AEAD TFM del canal principal para realizar un descifrado sincrónico, pero eso no se puede hacer ya que podría haber varios subprocesos cifsd (uno por canal) accediendo simultáneamente a él para realizar el descifrado. Esto corrige el siguiente splat de KASAN al ejecutar fstest generic/249 con 'vers=3.1.1,multichannel,max_channels=4,seal' en Windows Server 2022: ERROR: KASAN: slab-use-after-free en gf128mul_4k_lle+0xba/0x110 Lectura de tamaño 8 en la dirección ffff8881046c18a0 por la tarea cifsd/986 CPU: 3 UID: 0 PID: 986 Comm: cifsd No contaminado 6.15.0-rc1 #1 PREEMPT(voluntario) Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-3.fc41 01/04/2014 Seguimiento de llamadas:   dump_stack_lvl+0x5d/0x80 print_report+0x156/0x528 ? gf128mul_4k_lle+0xba/0x110 ? __virt_addr_valid+0x145/0x300 ? __phys_addr+0x46/0x90 ? gf128mul_4k_lle+0xba/0x110 kasan_report+0xdf/0x1a0 ? gf128mul_4k_lle+0xba/0x110 gf128mul_4k_lle+0xba/0x110 ghash_update+0x189/0x210 shash_ahash_update+0x295/0x370 ? __pfx_shash_ahash_update+0x10/0x10 ? __pfx_shash_ahash_update+0x10/0x10 ? __pfx_extract_iter_to_sg+0x10/0x10 ? ___kmalloc_large_node+0x10e/0x180 ? __asan_memset+0x23/0x50 crypto_ahash_update+0x3c/0xc0 gcm_hash_assoc_remain_continue+0x93/0xc0 crypt_message+0xe09/0xec0 [cifs] ? __pfx_crypt_message+0x10/0x10 [cifs] ? _raw_spin_unlock+0x23/0x40 ? __pfx_cifs_readv_from_socket+0x10/0x10 [cifs] decrypt_raw_data+0x229/0x380 [cifs] ? __pfx_decrypt_raw_data+0x10/0x10 [cifs] ? __pfx_cifs_read_iter_from_socket+0x10/0x10 [cifs] smb3_receive_transform+0x837/0xc80 [cifs] ? __pfx_smb3_receive_transform+0x10/0x10 [cifs] ? __pfx___might_resched+0x10/0x10 ? __pfx_smb3_is_transform_hdr+0x10/0x10 [cifs] cifs_demultiplex_thread+0x692/0x1570 [cifs] ? __pfx_cifs_demultiplex_thread+0x10/0x10 [cifs] ? rcu_is_watching+0x20/0x50 ? rcu_lockdep_current_cpu_online+0x62/0xb0 ? find_held_lock+0x32/0x90 ? kvm_sched_clock_read+0x11/0x20 ? local_clock_noinstr+0xd/0xd0 ? trace_irq_enable.constprop.0+0xa8/0xe0 ? __pfx_cifs_demultiplex_thread+0x10/0x10 [cifs] kthread+0x1fe/0x380 ? kthread+0x10f/0x380 ? __pfx_kthread+0x10/0x10 ? local_clock_noinstr+0xd/0xd0 ? ret_from_fork+0x1b/0x60 ? local_clock+0x15/0x30 ? lock_release+0x29b/0x390 ? rcu_is_watching+0x20/0x50 ? __pfx_kthread+0x10/0x10 ret_from_fork+0x31/0x60 ? __pfx_kthread+0x10/0x10 ret_from_fork_asm+0x1a/0x30  "}], "references": [{"url": "https://git.kernel.org/stable/c/9502dd5c7029902f4a425bf959917a5a9e7c0e50", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/950557922c1298464749c216d8763e97faf5d0a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aa5a1e4b882964eb79d5b5d1d1e8a1a5efbb1d15", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e859b216d94668bc66330e61be201234f4413d1a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53144", "published_date": "2025-05-02T16:15:33.357", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nerofs: fix wrong kunmap when using LZMA on HIGHMEM platforms\n\nAs the call trace shown, the root cause is kunmap incorrect pages:\n\n BUG: kernel NULL pointer dereference, address: 00000000\n CPU: 1 PID: 40 Comm: kworker/u5:0 Not tainted 6.2.0-rc5 #4\n Workqueue: erofs_worker z_erofs_decompressqueue_work\n EIP: z_erofs_lzma_decompress+0x34b/0x8ac\n  z_erofs_decompress+0x12/0x14\n  z_erofs_decompress_queue+0x7e7/0xb1c\n  z_erofs_decompressqueue_work+0x32/0x60\n  process_one_work+0x24b/0x4d8\n  ? process_one_work+0x1a4/0x4d8\n  worker_thread+0x14c/0x3fc\n  kthread+0xe6/0x10c\n  ? rescuer_thread+0x358/0x358\n  ? kthread_complete_and_exit+0x18/0x18\n  ret_from_fork+0x1c/0x28\n ---[ end trace 0000000000000000 ]---\n\nThe bug is trivial and should be fixed now.  It has no impact on\n!HIGHMEM platforms."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: erofs: corrige kunmap incorrecto al usar LZMA en plataformas HIGHMEM Como lo muestra el seguimiento de llamadas, la causa raíz son páginas incorrectas de kunmap: ERROR: desreferencia de puntero NULL del kernel, dirección: 00000000 CPU: 1 PID: 40 Comm: kworker/u5:0 No contaminado 6.2.0-rc5 #4 Cola de trabajo: erofs_worker z_erofs_decompressqueue_work EIP: z_erofs_lzma_decompress+0x34b/0x8ac z_erofs_decompress+0x12/0x14 z_erofs_decompress_queue+0x7e7/0xb1c z_erofs_decompressqueue_work+0x32/0x60 process_one_work+0x24b/0x4d8 ? process_one_work+0x1a4/0x4d8, work_thread+0x14c/0x3fc, kthread+0xe6/0x10c, rescuer_thread+0x358/0x358, kthread_complete_and_exit+0x18/0x18, ret_from_fork+0x1c/0x28 ---[ fin del seguimiento 000000000000000 ]--- El error es trivial y debería estar corregido. No afecta a las plataformas !HIGHMEM."}], "references": [{"url": "https://git.kernel.org/stable/c/28aea8ae6cf212a5bf3ed962b27921e2029ad754", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8f121dfb15f7b4ab345992ce96003eb63fd608f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fa4056781ac067b5946c6811459e1a36842047fd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
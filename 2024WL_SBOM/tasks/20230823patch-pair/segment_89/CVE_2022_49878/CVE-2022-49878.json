{"cve_id": "CVE-2022-49878", "published_date": "2025-05-01T15:16:12.753", "last_modified_date": "2025-05-07T13:21:23.850", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf, verifier: Fix memory leak in array reallocation for stack state\n\nIf an error (NULL) is returned by krealloc(), callers of realloc_array()\nwere setting their allocation pointers to NULL, but on error krealloc()\ndoes not touch the original allocation. This would result in a memory\nresource leak. Instead, free the old allocation on the error handling\npath.\n\nThe memory leak information is as follows as also reported by Zhengchao:\n\n  unreferenced object 0xffff888019801800 (size 256):\n  comm \"bpf_repo\", pid 6490, jiffies 4294959200 (age 17.170s)\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n  backtrace:\n    [<00000000b211474b>] __kmalloc_node_track_caller+0x45/0xc0\n    [<0000000086712a0b>] krealloc+0x83/0xd0\n    [<00000000139aab02>] realloc_array+0x82/0xe2\n    [<00000000b1ca41d1>] grow_stack_state+0xfb/0x186\n    [<00000000cd6f36d2>] check_mem_access.cold+0x141/0x1341\n    [<0000000081780455>] do_check_common+0x5358/0xb350\n    [<0000000015f6b091>] bpf_check.cold+0xc3/0x29d\n    [<000000002973c690>] bpf_prog_load+0x13db/0x2240\n    [<00000000028d1644>] __sys_bpf+0x1605/0x4ce0\n    [<00000000053f29bd>] __x64_sys_bpf+0x75/0xb0\n    [<0000000056fedaf5>] do_syscall_64+0x35/0x80\n    [<000000002bd58261>] entry_SYSCALL_64_after_hwframe+0x63/0xcd"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf, verificador: Se corrige una fuga de memoria en la reasignación de matriz para el estado de la pila. Si krealloc() devuelve un error (NULL), los usuarios de realloc_array() establecían sus punteros de asignación en NULL, pero en caso de error, krealloc() no modifica la asignación original. Esto provocaría una fuga de recursos de memoria. En su lugar, se libera la asignación anterior en la ruta de gestión de errores. La información de fuga de memoria es la siguiente, tal como lo informó Zhengchao: objeto sin referencia 0xffff888019801800 (tama<PERSON> 256): comm \"bpf_repo\", pid 6490, jiffies 4294959200 (edad 17.170s) volcado hexadecimal (primeros 32 bytes): 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ backtrace: [&lt;00000000b211474b&gt;] __kmalloc_node_track_caller+0x45/0xc0 [&lt;0000000086712a0b&gt;] krealloc+0x83/0xd0 [&lt;00000000139aab02&gt;] realloc_array+0x82/0xe2 [&lt;00000000b1ca41d1&gt;] grow_stack_state+0xfb/0x186 [&lt;00000000cd6f36d2&gt;] check_mem_access.cold+0x141/0x1341 [&lt;0000000081780455&gt;] do_check_common+0x5358/0xb350 [&lt;0000000015f6b091&gt;] bpf_check.cold+0xc3/0x29d [&lt;000000002973c690&gt;] bpf_prog_load+0x13db/0x2240 [&lt;00000000028d1644&gt;] __sys_bpf+0x1605/0x4ce0 [&lt;00000000053f29bd&gt;] __x64_sys_bpf+0x75/0xb0 [&lt;0000000056fedaf5&gt;] do_syscall_64+0x35/0x80 [&lt;000000002bd58261&gt;] entry_SYSCALL_64_after_hwframe+0x63/0xcd "}], "references": [{"url": "https://git.kernel.org/stable/c/06615967d4889b08b19ff3dda96e8b131282f73d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3e210891c4a4c2d858cd6f9f61d5809af251d4df", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/42378a9ca55347102bbf86708776061d8fe3ece2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
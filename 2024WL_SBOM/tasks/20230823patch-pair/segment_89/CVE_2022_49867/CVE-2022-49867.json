{"cve_id": "CVE-2022-49867", "published_date": "2025-05-01T15:16:11.633", "last_modified_date": "2025-05-07T13:22:14.240", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: wwan: iosm: fix memory leak in ipc_wwan_dellink\n\nIOSM driver registers network device without setting the\nneeds_free_netdev flag, and does NOT call free_netdev() when\nunregisters network device, which causes a memory leak.\n\nThis patch sets needs_free_netdev to true when registers\nnetwork device, which makes netdev subsystem call free_netdev()\nautomatically after unregister_netdevice()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: wwan: iosm: se corrige una fuga de memoria en ipc_wwan_dellink. El controlador IOSM registra el dispositivo de red sin configurar el indicador needs_free_netdev y NO llama a free_netdev() al cancelar el registro del dispositivo de red, lo que provoca una fuga de memoria. Este parche establece needs_free_netdev como verdadero al registrar el dispositivo de red, lo que hace que el subsistema netdev llame automáticamente a free_netdev() después de cancelar el registro de netdevice()."}], "references": [{"url": "https://git.kernel.org/stable/c/128514b51a5ba2c82f9e4a106f1c10423907618a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2ce2348c2858d723f7fe389dead9b43b08e0944e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f25caaca424703d5a0607310f0452f978f1f78d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-37744", "published_date": "2025-05-01T13:15:53.100", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: ath12k: fix memory leak in ath12k_pci_remove()\n\nKmemleak reported this error:\n\n  unreferenced object 0xffff1c165cec3060 (size 32):\n    comm \"insmod\", pid 560, jiffies 4296964570 (age 235.596s)\n    backtrace:\n      [<000000005434db68>] __kmem_cache_alloc_node+0x1f4/0x2c0\n      [<000000001203b155>] kmalloc_trace+0x40/0x88\n      [<0000000028adc9c8>] _request_firmware+0xb8/0x608\n      [<00000000cad1aef7>] firmware_request_nowarn+0x50/0x80\n      [<000000005011a682>] local_pci_probe+0x48/0xd0\n      [<00000000077cd295>] pci_device_probe+0xb4/0x200\n      [<0000000087184c94>] really_probe+0x150/0x2c0\n\nThe firmware memory was allocated in ath12k_pci_probe(), but not\nfreed in ath12k_pci_remove() in case ATH12K_FLAG_QMI_FAIL bit is\nset. So call ath12k_fw_unmap() to free the memory.\n\nTested-on: WCN7850 hw2.0 PCI WLAN.HMT.2.0-02280-QCAHMTSWPL_V1.0_V2.0_SILICONZ-1"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: ath12k: se corrige la pérdida de memoria en ath12k_pci_remove() Kmemleak informó este error: objeto sin referencia 0xffff1c165cec3060 (tamaño 32): comm \"insmod\", pid 560, jiffies 4296964570 (edad 235.596s) backtrace: [&lt;000000005434db68&gt;] __kmem_cache_alloc_node+0x1f4/0x2c0 [&lt;000000001203b155&gt;] kmalloc_trace+0x40/0x88 [&lt;0000000028adc9c8&gt;] _request_firmware+0xb8/0x608 [&lt;00000000cad1aef7&gt;] firmware_request_nowarn+0x50/0x80 [&lt;000000005011a682&gt;] local_pci_probe+0x48/0xd0 [&lt;00000000077cd295&gt;] pci_device_probe+0xb4/0x200 [&lt;0000000087184c94&gt;] really_probe+0x150/0x2c0 La memoria del firmware se asignó en ath12k_pci_probe(), pero no se liberó en ath12k_pci_remove() si el bit ATH12K_FLAG_QMI_FAIL está activado. Por lo tanto, se llama ath12k_fw_unmap() para liberar la memoria. Probado en: WCN7850 hw2.0 PCI WLAN.HMT.2.0-02280-QCAHMTSWPL_V1.0_V2.0_SILICONZ-1"}], "references": [{"url": "https://git.kernel.org/stable/c/1b24394ed5c8a8d8f7b9e3aa9044c31495d46f2e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3cb47b50926a5b9eef8c06506a14cdc0f3d95c53", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/52e3132e62c31b5ade43dc4495fa81175e6e8398", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cb8f4c5f9c487d82a566672b5ed0c9f05e40659b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
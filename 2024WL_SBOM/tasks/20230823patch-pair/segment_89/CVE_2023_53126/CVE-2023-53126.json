{"cve_id": "CVE-2023-53126", "published_date": "2025-05-02T16:15:31.643", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: mpi3mr: Fix sas_hba.phy memory leak in mpi3mr_remove()\n\nFree mrioc->sas_hba.phy at .remove."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: mpi3mr: corrige la pérdida de memoria sas_hba.phy en mpi3mr_remove() Libera mrioc-&gt;sas_hba.phy en .remove."}], "references": [{"url": "https://git.kernel.org/stable/c/480aae2f30637b5140e9c7a9b10298e538df2b5e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c60a7c7508645a9f36e4a18a5f548fb79378acd1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d4caa1a4255cc44be56bcab3db2c97c632e6cc10", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-6029", "published_date": "2025-04-30T20:15:20.900", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Tesla Model S Iris Modem Race Condition Firewall Bypass Vulnerability. This vulnerability allows network-adjacent attackers to bypass the firewall on the Iris modem in affected Tesla Model S vehicles. Authentication is not required to exploit this vulnerability.\n \nThe specific flaw exists within the firewall service. The issue results from a failure to obtain the xtables lock. An attacker can leverage this vulnerability to bypass firewall rules. Was ZDI-CAN-23197."}, {"lang": "es", "value": "Vulnerabilidad de elusión del firewall en condiciones de ejecución del módem Iris del Tesla Model S. Esta vulnerabilidad permite a atacantes adyacentes a la red eludir el firewall del módem Iris en los vehículos Tesla Model S afectados. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica existe en el servicio de firewall. El problema se debe a un fallo al obtener el bloqueo de xtables. Un atacante puede aprovechar esta vulnerabilidad para eludir las reglas del firewall. Anteriormente, se denominaba ZDI-CAN-23197."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-260/", "source": "<EMAIL>", "tags": []}]}
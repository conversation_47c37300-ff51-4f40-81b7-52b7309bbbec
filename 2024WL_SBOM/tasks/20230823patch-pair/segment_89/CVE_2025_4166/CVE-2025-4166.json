{"cve_id": "CVE-2025-4166", "published_date": "2025-05-02T15:15:50.313", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "Vault Community and Vault Enterprise Key/Value (kv) Version 2 plugin may unintentionally expose sensitive information in server and audit logs when users submit malformed payloads during secret creation or update operations via the Vault REST API. This vulnerability, identified as CVE-2025-4166, is fixed in Vault Community 1.19.3 and Vault Enterprise 1.19.3, 1.18.9, 1.17.16, 1.16.20."}, {"lang": "es", "value": "El complemento Clave/Valor (kv) versión 2 de Vault Community y Vault Enterprise podría exponer involuntariamente información confidencial en los registros del servidor y de auditoría cuando los usuarios envían payloads malformadas durante la creación o actualización de secretos mediante la API REST de Vault. Esta vulnerabilidad, identificada como CVE-2025-4166, está corregida en Vault Community 1.19.3 y Vault Enterprise 1.19.3, 1.18.9, 1.17.16 y 1.16.20."}], "references": [{"url": "https://discuss.hashicorp.com/t/hcsec-2025-09-vault-may-expose-sensitive-information-in-error-logs-when-processing-malformed-data-with-the-kv-v2-plugin", "source": "<EMAIL>", "tags": []}]}
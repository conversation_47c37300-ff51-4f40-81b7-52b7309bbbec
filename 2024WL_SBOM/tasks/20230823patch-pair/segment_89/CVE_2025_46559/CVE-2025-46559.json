{"cve_id": "CVE-2025-46559", "published_date": "2025-05-05T19:15:56.910", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Misskey is an open source, federated social media platform. Starting in version 12.31.0 and prior to version 2025.4.1, missing validation in `Mk:api` allows malicious AiScript code to access additional endpoints that it isn't designed to have access to. The missing validation allows malicious AiScript code to prefix a URL with `../` to step out of the `/api` directory, thereby being able to make requests to other endpoints, such as `/files`, `/url`, and `/proxy`. Version 2025.4.1 fixes the issue."}, {"lang": "es", "value": "Misskey es una plataforma de redes sociales federada de código abierto. A partir de la versión 12.31.0 y anteriores a la 2025.4.1, la falta de validación en `Mk:api` permite que código AiScript malicioso acceda a endpoints adicionales a los que no está diseñado. Esta falta de validación permite que el código AiScript malicioso anteponga `../` a una URL para salir del directorio `/api`, lo que permite realizar solicitudes a otros endpoints, como `/files`, `/url` y `/proxy`. La versión 2025.4.1 soluciona el problema."}], "references": [{"url": "https://github.com/misskey-dev/misskey/commit/583df3ec63e25a1fd34def0dac13405396b8b663", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/misskey-dev/misskey/security/advisories/GHSA-gmq6-738q-vjp2", "source": "<EMAIL>", "tags": []}]}
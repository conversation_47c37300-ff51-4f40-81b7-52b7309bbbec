{"cve_id": "CVE-2023-53095", "published_date": "2025-05-02T16:15:28.453", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/ttm: Fix a NULL pointer dereference\n\nThe LRU mechanism may look up a resource in the process of being removed\nfrom an object. The locking rules here are a bit unclear but it looks\ncurrently like res->bo assignment is protected by the LRU lock, whereas\nbo->resource is protected by the object lock, while *clearing* of\nbo->resource is also protected by the LRU lock. This means that if\nwe check that bo->resource points to the LRU resource under the LRU\nlock we should be safe.\nSo perform that check before deciding to swap out a bo. That avoids\ndereferencing a NULL bo->resource in ttm_bo_swapout()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/ttm: Corregir una desreferencia de puntero NULL. El mecanismo LRU puede buscar un recurso en proceso de ser eliminado de un objeto. Las reglas de bloqueo aquí son un poco confusas, pero actualmente parece que la asignación res-&gt;bo está protegida por el bloqueo LRU, mientras que bo-&gt;resource está protegida por el bloqueo de objeto, mientras que la *limpieza* de bo-&gt;resource también está protegida por el bloqueo LRU. Esto significa que si comprobamos que bo-&gt;resource apunta al recurso LRU bajo el bloqueo LRU, deberíamos estar seguros. Así que realice esa comprobación antes de decidir intercambiar un bo. Esto evita la desreferencia de un bo-&gt;resource NULL en ttm_bo_swapout()."}], "references": [{"url": "https://git.kernel.org/stable/c/9a9a8fe26751334b7739193a94eba741073b8a55", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ba1720f6c4a0f13c3f3cb5c28132ee75555d04f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9d9b1f9f7a72d83ebf173534e76b246349f32374", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
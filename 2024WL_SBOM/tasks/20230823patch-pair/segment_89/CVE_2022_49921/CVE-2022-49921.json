{"cve_id": "CVE-2022-49921", "published_date": "2025-05-01T15:16:17.627", "last_modified_date": "2025-05-07T13:27:50.000", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: sched: Fix use after free in red_enqueue()\n\nWe can't use \"skb\" again after passing it to qdisc_enqueue().  This is\nbasically identical to commit 2f09707d0c97 (\"sch_sfb: Also store skb\nlen before calling child enqueue\")."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: sched: Se corrige el use after free en red_enqueue(). No podemos volver a usar \"skb\" después de pasarlo a qdisc_enqueue(). Esto es prácticamente idéntico a el commit 2f09707d0c97 (\"sch_sfb: También se almacena la longitud de skb antes de llamar a la cola secundaria\")."}], "references": [{"url": "https://git.kernel.org/stable/c/170e5317042c302777ed6d59fdb84af9b0219d4e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/52e0429471976785c155bfbf51d80990c6cd46e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5960b9081baca85cc7dcb14aec1de85999ea9d36", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/795afe0b9bb6c915f0299a8e309936519be01619", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8bdc2acd420c6f3dd1f1c78750ec989f02a1e2b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a238cdcf2bdc72207c74375fc8be13ee549ca9db", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e877f8fa49fbccc63cb2df2e9179bddc695b825a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fc4b50adb400ee5ec527a04073174e8e73a139fa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-43851", "published_date": "2025-05-05T19:15:56.220", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Retrieval-based-Voice-Conversion-WebUI is a voice changing framework based on VITS. Versions 2.2.231006 and prior are vulnerable to unsafe deserialization. The model_choose variable takes user input (e.g. a path to a model) and passes it to the uvr function in vr.py. In uvr , a new instance of AudioPre class is created with the model_path attribute containing the aformentioned user input. In the AudioPre class, the user input, is used to load the model on that path with torch.load, which can lead to unsafe deserialization and remote code execution. As of time of publication, no known patches exist."}, {"lang": "es", "value": "Retrieval-based-Voice-Conversion-WebUI es un framework de modificación de voz basado en VITS. Las versiones 2.2.231006 y anteriores son vulnerables a deserialización insegura. La variable model_choose toma la entrada del usuario (por ejemplo, la ruta a un modelo) y la pasa a la función uvr en vr.py. En uvr, se crea una nueva instancia de la clase AudioPre con el atributo model_path que contiene la entrada del usuario mencionada. En la clase AudioPre, la entrada del usuario se utiliza para cargar el modelo en esa ruta con torch.load, lo que puede provocar deserialización insegura y ejecución remota de código. Al momento de la publicación, no se conocían parches."}], "references": [{"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/7ef19867780cf703841ebafb565a4e47d1ea86ff/infer/modules/onnx/export.py#L7", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L1073", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L1098", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2025-012_GHSL-2025-022_Retrieval-based-Voice-Conversion-WebUI/", "source": "<EMAIL>", "tags": []}]}
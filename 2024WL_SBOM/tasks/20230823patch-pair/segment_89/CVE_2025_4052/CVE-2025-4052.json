{"cve_id": "CVE-2025-4052", "published_date": "2025-05-05T18:15:44.153", "last_modified_date": "2025-05-28T20:07:45.097", "descriptions": [{"lang": "en", "value": "Inappropriate implementation in DevTools in Google Chrome prior to 136.0.7103.59 allowed a remote attacker who convinced a user to engage in specific UI gestures to bypass discretionary access control via a crafted HTML page. (Chromium security severity: Low)"}, {"lang": "es", "value": "Una implementación incorrecta en DevTools de Google Chrome anterior a la versión 136.0.7103.59 permitía que un atacante remoto, al convencer a un usuario para que realizara gestos específicos de la interfaz de usuario, eludiera el control de acceso discrecional mediante una página HTML manipulada. (Gravedad de seguridad de Chromium: Baja)"}], "references": [{"url": "https://chromereleases.googleblog.com/2025/04/stable-channel-update-for-desktop_29.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://issues.chromium.org/issues/401927528", "source": "<EMAIL>", "tags": ["Permissions Required"]}]}
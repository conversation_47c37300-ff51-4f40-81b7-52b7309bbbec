{"cve_id": "CVE-2023-53046", "published_date": "2025-05-02T16:15:23.697", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: Fix race condition in hci_cmd_sync_clear\n\nThere is a potential race condition in hci_cmd_sync_work and\nhci_cmd_sync_clear, and could lead to use-after-free. For instance,\nhci_cmd_sync_work is added to the 'req_workqueue' after cancel_work_sync\nThe entry of 'cmd_sync_work_list' may be freed in hci_cmd_sync_clear, and\ncausing kernel panic when it is used in 'hci_cmd_sync_work'.\n\nHere's the call trace:\n\ndump_stack_lvl+0x49/0x63\nprint_report.cold+0x5e/0x5d3\n? hci_cmd_sync_work+0x282/0x320\nkasan_report+0xaa/0x120\n? hci_cmd_sync_work+0x282/0x320\n__asan_report_load8_noabort+0x14/0x20\nhci_cmd_sync_work+0x282/0x320\nprocess_one_work+0x77b/0x11c0\n? _raw_spin_lock_irq+0x8e/0xf0\nworker_thread+0x544/0x1180\n? poll_idle+0x1e0/0x1e0\nkthread+0x285/0x320\n? process_one_work+0x11c0/0x11c0\n? kthread_complete_and_exit+0x30/0x30\nret_from_fork+0x22/0x30\n</TASK>\n\nAllocated by task 266:\nkasan_save_stack+0x26/0x50\n__kasan_kmalloc+0xae/0xe0\nkmem_cache_alloc_trace+0x191/0x350\nhci_cmd_sync_queue+0x97/0x2b0\nhci_update_passive_scan+0x176/0x1d0\nle_conn_complete_evt+0x1b5/0x1a00\nhci_le_conn_complete_evt+0x234/0x340\nhci_le_meta_evt+0x231/0x4e0\nhci_event_packet+0x4c5/0xf00\nhci_rx_work+0x37d/0x880\nprocess_one_work+0x77b/0x11c0\nworker_thread+0x544/0x1180\nkthread+0x285/0x320\nret_from_fork+0x22/0x30\n\nFreed by task 269:\nkasan_save_stack+0x26/0x50\nkasan_set_track+0x25/0x40\nkasan_set_free_info+0x24/0x40\n____kasan_slab_free+0x176/0x1c0\n__kasan_slab_free+0x12/0x20\nslab_free_freelist_hook+0x95/0x1a0\nkfree+0xba/0x2f0\nhci_cmd_sync_clear+0x14c/0x210\nhci_unregister_dev+0xff/0x440\nvhci_release+0x7b/0xf0\n__fput+0x1f3/0x970\n____fput+0xe/0x20\ntask_work_run+0xd4/0x160\ndo_exit+0x8b0/0x22a0\ndo_group_exit+0xba/0x2a0\nget_signal+0x1e4a/0x25b0\narch_do_signal_or_restart+0x93/0x1f80\nexit_to_user_mode_prepare+0xf5/0x1a0\nsyscall_exit_to_user_mode+0x26/0x50\nret_from_fork+0x15/0x30"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: Corregir condición de ejecución en hci_cmd_sync_clear Existe una posible condición de ejecución en hci_cmd_sync_work y hci_cmd_sync_clear, y podría provocar un use-after-free. Por ejemplo, hci_cmd_sync_work se añade a 'req_workqueue' después de cancel_work_sync La entrada de 'cmd_sync_work_list' puede liberarse en hci_cmd_sync_clear y provocar un pánico del kernel cuando se utiliza en 'hci_cmd_sync_work'. Aquí está el seguimiento de la llamada: dump_stack_lvl+0x49/0x63 print_report.cold+0x5e/0x5d3 ? hci_cmd_sync_work+0x282/0x320 kasan_report+0xaa/0x120 ? hci_cmd_sync_work+0x282/0x320 __asan_report_load8_noabort+0x14/0x20 hci_cmd_sync_work+0x282/0x320 process_one_work+0x77b/0x11c0 ? _raw_spin_lock_irq+0x8e/0xf0 worker_thread+0x544/0x1180 ? poll_idle+0x1e0/0x1e0 kthread+0x285/0x320 ? process_one_work+0x11c0/0x11c0 ? kthread_complete_and_exit+0x30/0x30 ret_from_fork+0x22/0x30  Allocated by task 266: kasan_save_stack+0x26/0x50 __kasan_kmalloc+0xae/0xe0 kmem_cache_alloc_trace+0x191/0x350 hci_cmd_sync_queue+0x97/0x2b0 hci_update_passive_scan+0x176/0x1d0 le_conn_complete_evt+0x1b5/0x1a00 hci_le_conn_complete_evt+0x234/0x340 hci_le_meta_evt+0x231/0x4e0 hci_event_packet+0x4c5/0xf00 hci_rx_work+0x37d/0x880 process_one_work+0x77b/0x11c0 worker_thread+0x544/0x1180 kthread+0x285/0x320 ret_from_fork+0x22/0x30 Freed by task 269: kasan_save_stack+0x26/0x50 kasan_set_track+0x25/0x40 kasan_set_free_info+0x24/0x40 ____kasan_slab_free+0x176/0x1c0 __kasan_slab_free+0x12/0x20 slab_free_freelist_hook+0x95/0x1a0 kfree+0xba/0x2f0 hci_cmd_sync_clear+0x14c/0x210 hci_unregister_dev+0xff/0x440 vhci_release+0x7b/0xf0 __fput+0x1f3/0x970 ____fput+0xe/0x20 task_work_run+0xd4/0x160 do_exit+0x8b0/0x22a0 do_group_exit+0xba/0x2a0 get_signal+0x1e4a/0x25b0 arch_do_signal_or_restart+0x93/0x1f80 exit_to_user_mode_prepare+0xf5/0x1a0 syscall_exit_to_user_mode+0x26/0x50 ret_from_fork+0x15/0x30 "}], "references": [{"url": "https://git.kernel.org/stable/c/1c66bee492a5fe00ae3fe890bb693bfc99f994c6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/608901a77c945ac15dea23f6098c9882ef19d9f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be586211a3ab40a4f4ca60450e0d31606afc55ec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
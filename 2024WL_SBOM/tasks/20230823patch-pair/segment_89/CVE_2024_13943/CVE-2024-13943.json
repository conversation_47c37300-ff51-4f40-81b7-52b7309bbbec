{"cve_id": "CVE-2024-13943", "published_date": "2025-04-30T20:15:20.520", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Tesla Model S Iris Modem QCMAP_ConnectionManager Improper Input Validation Sandbox Escape Vulnerability. This vulnerability allows local attackers to escape the sandbox on affected affected Tesla Model S vehicles. An attacker must first obtain the ability to execute low-privileged code on the target system in order to exploit this vulnerability.\n\nThe specific flaw exists within the QCMAP_ConnectionManager component. An attacker can abuse the service to assign LAN addresses to the WWAN. An attacker can leverage this vulnerability to access network services that were only intended to be exposed to the internal LAN. Was ZDI-CAN-23199."}, {"lang": "es", "value": "Vulnerabilidad de escape de la zona protegida con validación de entrada incorrecta del módem Iris QCMAP_ConnectionManager de Tesla Model S. Esta vulnerabilidad permite a atacantes locales escapar de la zona de pruebas en los vehículos Tesla Model S afectados. Para explotar esta vulnerabilidad, un atacante debe primero ejecutar código con privilegios bajos en el sistema objetivo. La falla específica se encuentra en el componente QCMAP_ConnectionManager. Un atacante puede abusar del servicio para asignar direcciones LAN a la WWAN. Un atacante puede aprovechar esta vulnerabilidad para acceder a servicios de red que solo estaban destinados a estar expuestos a la LAN interna. Era ZDI-CAN-23199."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-262/", "source": "<EMAIL>", "tags": []}]}
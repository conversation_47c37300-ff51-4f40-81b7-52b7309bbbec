{"cve_id": "CVE-2022-49910", "published_date": "2025-05-01T15:16:16.147", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: L2CAP: Fix use-after-free caused by l2cap_reassemble_sdu\n\nFix the race condition between the following two flows that run in\nparallel:\n\n1. l2cap_reassemble_sdu -> chan->ops->recv (l2cap_sock_recv_cb) ->\n   __sock_queue_rcv_skb.\n\n2. bt_sock_recvmsg -> skb_recv_datagram, skb_free_datagram.\n\nAn SKB can be queued by the first flow and immediately dequeued and\nfreed by the second flow, therefore the callers of l2cap_reassemble_sdu\ncan't use the SKB after that function returns. However, some places\ncontinue accessing struct l2cap_ctrl that resides in the SKB's CB for a\nshort time after l2cap_reassemble_sdu returns, leading to a\nuse-after-free condition (the stack trace is below, line numbers for\nkernel 5.19.8).\n\nFix it by keeping a local copy of struct l2cap_ctrl.\n\nBUG: KASAN: use-after-free in l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth\nRead of size 1 at addr ffff88812025f2f0 by task kworker/u17:3/43169\n\nWorkqueue: hci0 hci_rx_work [bluetooth]\nCall Trace:\n <TASK>\n dump_stack_lvl (lib/dump_stack.c:107 (discriminator 4))\n print_report.cold (mm/kasan/report.c:314 mm/kasan/report.c:429)\n ? l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth\n kasan_report (mm/kasan/report.c:162 mm/kasan/report.c:493)\n ? l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth\n l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth\n l2cap_rx (net/bluetooth/l2cap_core.c:7236 net/bluetooth/l2cap_core.c:7271) bluetooth\n ret_from_fork (arch/x86/entry/entry_64.S:306)\n </TASK>\n\nAllocated by task 43169:\n kasan_save_stack (mm/kasan/common.c:39)\n __kasan_slab_alloc (mm/kasan/common.c:45 mm/kasan/common.c:436 mm/kasan/common.c:469)\n kmem_cache_alloc_node (mm/slab.h:750 mm/slub.c:3243 mm/slub.c:3293)\n __alloc_skb (net/core/skbuff.c:414)\n l2cap_recv_frag (./include/net/bluetooth/bluetooth.h:425 net/bluetooth/l2cap_core.c:8329) bluetooth\n l2cap_recv_acldata (net/bluetooth/l2cap_core.c:8442) bluetooth\n hci_rx_work (net/bluetooth/hci_core.c:3642 net/bluetooth/hci_core.c:3832) bluetooth\n process_one_work (kernel/workqueue.c:2289)\n worker_thread (./include/linux/list.h:292 kernel/workqueue.c:2437)\n kthread (kernel/kthread.c:376)\n ret_from_fork (arch/x86/entry/entry_64.S:306)\n\nFreed by task 27920:\n kasan_save_stack (mm/kasan/common.c:39)\n kasan_set_track (mm/kasan/common.c:45)\n kasan_set_free_info (mm/kasan/generic.c:372)\n ____kasan_slab_free (mm/kasan/common.c:368 mm/kasan/common.c:328)\n slab_free_freelist_hook (mm/slub.c:1780)\n kmem_cache_free (mm/slub.c:3536 mm/slub.c:3553)\n skb_free_datagram (./include/net/sock.h:1578 ./include/net/sock.h:1639 net/core/datagram.c:323)\n bt_sock_recvmsg (net/bluetooth/af_bluetooth.c:295) bluetooth\n l2cap_sock_recvmsg (net/bluetooth/l2cap_sock.c:1212) bluetooth\n sock_read_iter (net/socket.c:1087)\n new_sync_read (./include/linux/fs.h:2052 fs/read_write.c:401)\n vfs_read (fs/read_write.c:482)\n ksys_read (fs/read_write.c:620)\n do_syscall_64 (arch/x86/entry/common.c:50 arch/x86/entry/common.c:80)\n entry_SYSCALL_64_after_hwframe (arch/x86/entry/entry_64.S:120)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: L2CAP: Se corrige eluse-after-free causado por l2cap_reassemble_sdu. Se corrige la condición de ejecución entre los dos flujos que se ejecutan en paralelo: 1. l2cap_reassemble_sdu -&gt; chan-&gt;ops-&gt;recv (l2cap_sock_recv_cb) -&gt; __sock_queue_rcv_skb. 2. bt_sock_recvmsg -&gt; skb_recv_datagram, skb_free_datagram. Un SKB puede ser puesto en cola por el primer flujo e inmediatamente desencolado y liberado por el segundo flujo; por lo tanto, quienes llaman a l2cap_reassemble_sdu no pueden usar el SKB después del retorno de la función. Sin embargo, en algunos lugares, se continúa accediendo a la estructura l2cap_ctrl, que reside en el CB de la SKB, durante un breve periodo después del retorno de l2cap_reassemble_sdu, lo que genera una condición de use-after-free (el seguimiento de la pila se encuentra a continuación; los números de línea corresponden al kernel 5.19.8). Para solucionarlo, mantenga una copia local de la estructura l2cap_ctrl. ERROR: KASAN: use-after-free en l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth Lectura de tamaño 1 en la dirección ffff88812025f2f0 por la tarea kworker/u17:3/43169 Cola de trabajo: hci0 hci_rx_work [bluetooth] Rastreo de llamadas:   dump_stack_lvl (lib/dump_stack.c:107 (discriminator 4)) print_report.cold (mm/kasan/report.c:314 mm/kasan/report.c:429) ? l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth kasan_report (mm/kasan/report.c:162 mm/kasan/report.c:493) ? l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth l2cap_rx_state_recv (net/bluetooth/l2cap_core.c:6906) bluetooth l2cap_rx (net/bluetooth/l2cap_core.c:7236 net/bluetooth/l2cap_core.c:7271) bluetooth ret_from_fork (arch/x86/entry/entry_64.S:306)  Allocated by task 43169: kasan_save_stack (mm/kasan/common.c:39) __kasan_slab_alloc (mm/kasan/common.c:45 mm/kasan/common.c:436 mm/kasan/common.c:469) kmem_cache_alloc_node (mm/slab.h:750 mm/slub.c:3243 mm/slub.c:3293) __alloc_skb (net/core/skbuff.c:414) l2cap_recv_frag (./include/net/bluetooth/bluetooth.h:425 net/bluetooth/l2cap_core.c:8329) bluetooth l2cap_recv_acldata (net/bluetooth/l2cap_core.c:8442) bluetooth hci_rx_work (net/bluetooth/hci_core.c:3642 net/bluetooth/hci_core.c:3832) bluetooth process_one_work (kernel/workqueue.c:2289) worker_thread (./include/linux/list.h:292 kernel/workqueue.c:2437) kthread (kernel/kthread.c:376) ret_from_fork (arch/x86/entry/entry_64.S:306) Freed by task 27920: kasan_save_stack (mm/kasan/common.c:39) kasan_set_track (mm/kasan/common.c:45) kasan_set_free_info (mm/kasan/generic.c:372) ____kasan_slab_free (mm/kasan/common.c:368 mm/kasan/common.c:328) slab_free_freelist_hook (mm/slub.c:1780) kmem_cache_free (mm/slub.c:3536 mm/slub.c:3553) skb_free_datagram (./include/net/sock.h:1578 ./include/net/sock.h:1639 net/core/datagram.c:323) bt_sock_recvmsg (net/bluetooth/af_bluetooth.c:295) bluetooth l2cap_sock_recvmsg (net/bluetooth/l2cap_sock.c:1212) bluetooth sock_read_iter (net/socket.c:1087) new_sync_read (./include/linux/fs.h:2052 fs/read_write.c:401) vfs_read (fs/read_write.c:482) ksys_read (fs/read_write.c:620) do_syscall_64 (arch/x86/entry/common.c:50 arch/x86/entry/common.c:80) entry_SYSCALL_64_after_hwframe (arch/x86/entry/entry_64.S:120) "}], "references": [{"url": "https://git.kernel.org/stable/c/03af22e23b96fb7ef75fb7885407ef457e8b403d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3aff8aaca4e36dc8b17eaa011684881a80238966", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4cd094fd5d872862ca278e15b9b51b07e915ef3f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c7407bfbeafc80a04e6eaedcf34d378532a04f2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8278a87bb1eeea94350d675ef961ee5a03341fde", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9a04161244603f502c6e453913e51edd59cb70c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cb1c012099ef5904cd468bdb8d6fcdfdd9bcb569", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dc30e05bb18852303084430c03ca76e69257d9ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
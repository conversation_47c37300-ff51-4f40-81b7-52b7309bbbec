{"cve_id": "CVE-2025-46728", "published_date": "2025-05-06T01:15:50.790", "last_modified_date": "2025-05-07T14:13:35.980", "descriptions": [{"lang": "en", "value": "cpp-httplib is a C++ header-only HTTP/HTTPS server and client library. Prior to version 0.20.1, the library fails to enforce configured size limits on incoming request bodies when `Transfer-Encoding: chunked` is used or when no `Content-Length` header is provided. A remote attacker can send a chunked request without the terminating zero-length chunk, causing uncontrolled memory allocation on the server. This leads to potential exhaustion of system memory and results in a server crash or unresponsiveness. Version 0.20.1 fixes the issue by enforcing limits during parsing. If the limit is exceeded at any point during reading, the connection is terminated immediately. A short-term workaround through a Reverse Proxy is available. If updating the library immediately is not feasible, deploy a reverse proxy (e.g., Nginx, HAProxy) in front of the `cpp-httplib` application. Configure the proxy to enforce maximum request body size limits, thereby stopping excessively large requests before they reach the vulnerable library code."}, {"lang": "es", "value": "cpp-httplib es una librería de cliente y servidor HTTP/HTTPS de solo encabezado de C++. Antes de la versión 0.20.1, la librería no aplicaba los límites de tamaño configurados en los cuerpos de las solicitudes entrantes cuando se usaba `Transfer-Encoding: chunked` o cuando no se proporcionaba el encabezado `Content-Length`. Un atacante remoto podría enviar una solicitud fragmentada sin el fragmento de longitud cero de terminación, lo que causaba una asignación de memoria incontrolada en el servidor. Esto podría agotar la memoria del sistema y provocar un bloqueo o falta de respuesta del servidor. La versión 0.20.1 soluciona el problema aplicando límites durante el análisis. Si se supera el límite en cualquier momento durante la lectura, la conexión se termina inmediatamente. Existe una solución temporal a través de un proxy inverso. Si no es posible actualizar la librería inmediatamente, implemente un proxy inverso (p. ej., <PERSON><PERSON><PERSON>, HAProxy) delante de la aplicación `cpp-httplib`. Configure el proxy para aplicar límites de tamaño máximo en el cuerpo de la solicitud, deteniendo así las solicitudes excesivamente grandes antes de que lleguen al código de la librería vulnerable."}], "references": [{"url": "https://github.com/yhirose/cpp-httplib/commit/7b752106ac42bd5b907793950d9125a0972c8e8e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/yhirose/cpp-httplib/security/advisories/GHSA-px83-72rx-v57c", "source": "<EMAIL>", "tags": []}]}
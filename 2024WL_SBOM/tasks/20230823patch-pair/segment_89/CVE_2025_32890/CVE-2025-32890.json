{"cve_id": "CVE-2025-32890", "published_date": "2025-05-01T18:15:56.390", "last_modified_date": "2025-06-20T16:28:33.357", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna Mesh devices with app 5.5.3 and firmware 1.1.12. It uses a custom implementation of encryption without any additional integrity checking mechanisms. This leaves messages malleable to an attacker that can access the message."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna Mesh con la aplicación 5.5.3 y el firmware 1.1.12. Este problema utiliza una implementación personalizada de cifrado sin mecanismos adicionales de verificación de integridad. Esto deja los mensajes vulnerables a un atacante que pueda acceder a ellos."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2023-53089", "published_date": "2025-05-02T16:15:27.853", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\next4: fix task hung in ext4_xattr_delete_inode\n\nSyzbot reported a hung task problem:\n==================================================================\nINFO: task syz-executor232:5073 blocked for more than 143 seconds.\n      Not tainted 6.2.0-rc2-s<PERSON>z<PERSON><PERSON>-00024-g512dee0c00ad #0\n\"echo 0 > /proc/sys/kernel/hung_task_timeout_secs\" disables this message.\ntask:syz-exec232 state:D stack:21024 pid:5073 ppid:5072 flags:0x00004004\nCall Trace:\n <TASK>\n context_switch kernel/sched/core.c:5244 [inline]\n __schedule+0x995/0xe20 kernel/sched/core.c:6555\n schedule+0xcb/0x190 kernel/sched/core.c:6631\n __wait_on_freeing_inode fs/inode.c:2196 [inline]\n find_inode_fast+0x35a/0x4c0 fs/inode.c:950\n iget_locked+0xb1/0x830 fs/inode.c:1273\n __ext4_iget+0x22e/0x3ed0 fs/ext4/inode.c:4861\n ext4_xattr_inode_iget+0x68/0x4e0 fs/ext4/xattr.c:389\n ext4_xattr_inode_dec_ref_all+0x1a7/0xe50 fs/ext4/xattr.c:1148\n ext4_xattr_delete_inode+0xb04/0xcd0 fs/ext4/xattr.c:2880\n ext4_evict_inode+0xd7c/0x10b0 fs/ext4/inode.c:296\n evict+0x2a4/0x620 fs/inode.c:664\n ext4_orphan_cleanup+0xb60/0x1340 fs/ext4/orphan.c:474\n __ext4_fill_super fs/ext4/super.c:5516 [inline]\n ext4_fill_super+0x81cd/0x8700 fs/ext4/super.c:5644\n get_tree_bdev+0x400/0x620 fs/super.c:1282\n vfs_get_tree+0x88/0x270 fs/super.c:1489\n do_new_mount+0x289/0xad0 fs/namespace.c:3145\n do_mount fs/namespace.c:3488 [inline]\n __do_sys_mount fs/namespace.c:3697 [inline]\n __se_sys_mount+0x2d3/0x3c0 fs/namespace.c:3674\n do_syscall_x64 arch/x86/entry/common.c:50 [inline]\n do_syscall_64+0x3d/0xb0 arch/x86/entry/common.c:80\n entry_SYSCALL_64_after_hwframe+0x63/0xcd\nRIP: 0033:0x7fa5406fd5ea\nRSP: 002b:00007ffc7232f968 EFLAGS: 00000202 ORIG_RAX: 00000000000000a5\nRAX: ffffffffffffffda RBX: 0000000000000003 RCX: 00007fa5406fd5ea\nRDX: 0000000020000440 RSI: 0000000020000000 RDI: 00007ffc7232f970\nRBP: 00007ffc7232f970 R08: 00007ffc7232f9b0 R09: 0000000000000432\nR10: 0000000000804a03 R11: 0000000000000202 R12: 0000000000000004\nR13: 0000555556a7a2c0 R14: 00007ffc7232f9b0 R15: 0000000000000000\n </TASK>\n==================================================================\n\nThe problem is that the inode contains an xattr entry with ea_inum of 15\nwhen cleaning up an orphan inode <15>. When evict inode <15>, the reference\ncounting of the corresponding EA inode is decreased. When EA inode <15> is\nfound by find_inode_fast() in __ext4_iget(), it is found that the EA inode\nholds the I_FREEING flag and waits for the EA inode to complete deletion.\nAs a result, when inode <15> is being deleted, we wait for inode <15> to\ncomplete the deletion, resulting in an infinite loop and triggering Hung\nTask. To solve this problem, we only need to check whether the ino of EA\ninode and parent is the same before getting EA inode."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ext4: corrección de tarea bloqueada en ext4_xattr_delete_inode. Syzbot informó de un problema de tarea bloqueada: =================================================================== INFORMACIÓN: La tarea syz-executor232:5073 se bloqueó durante más de 143 segundos. No contaminada. 6.2.0-rc2-syzkaller-00024-g512dee0c00ad #0 \"echo 0 &gt; /proc/sys/kernel/hung_task_timeout_secs\" desactiva este mensaje. tarea:syz-exec232 estado:D pila:21024 pid:5073 ppid:5072 indicadores:0x00004004 Rastreo de llamadas:  context_switch kernel/sched/core.c:5244 [en línea] __schedule+0x995/0xe20 kernel/sched/core.c:6555 schedule+0xcb/0x190 kernel/sched/core.c:6631 __wait_on_freeing_inode fs/inode.c:2196 [en línea] find_inode_fast+0x35a/0x4c0 fs/inode.c:950 iget_locked+0xb1/0x830 fs/inode.c:1273 __ext4_iget+0x22e/0x3ed0 fs/ext4/inode.c:4861 ext4_xattr_inode_iget+0x68/0x4e0 fs/ext4/xattr.c:389 ext4_xattr_inode_dec_ref_all+0x1a7/0xe50 fs/ext4/xattr.c:1148 ext4_xattr_delete_inode+0xb04/0xcd0 fs/ext4/xattr.c:2880 ext4_evict_inode+0xd7c/0x10b0 fs/ext4/inode.c:296 evict+0x2a4/0x620 fs/inode.c:664 ext4_orphan_cleanup+0xb60/0x1340 fs/ext4/orphan.c:474 __ext4_fill_super fs/ext4/super.c:5516 [en línea] ext4_fill_super+0x81cd/0x8700 fs/ext4/super.c:5644 get_tree_bdev+0x400/0x620 fs/super.c:1282 vfs_get_tree+0x88/0x270 fs/super.c:1489 do_new_mount+0x289/0xad0 fs/namespace.c:3145 do_mount fs/namespace.c:3488 [en línea] __do_sys_mount fs/namespace.c:3697 [en línea] __se_sys_mount+0x2d3/0x3c0 fs/namespace.c:3674 do_syscall_x64 arch/x86/entry/common.c:50 [en línea] do_syscall_64+0x3d/0xb0 arch/x86/entry/common.c:80 entry_SYSCALL_64_after_hwframe+0x63/0xcd RIP: 0033:0x7fa5406fd5ea RSP: 002b:00007ffc7232f968 EFLAGS: 00000202 ORIG_RAX: 00000000000000a5 RAX: ffffffffffffffda RBX: 0000000000000003 RCX: 00007fa5406fd5ea RDX: 0000000020000440 RSI: 0000000020000000 RDI: 00007ffc7232f970 RBP: 00007ffc7232f970 R08: 00007ffc7232f9b0 R09: 0000000000000432 R10: 0000000000804a03 R11: 0000000000000202 R12: 000000000000004 R13: 0000555556a7a2c0 R14: 00007ffc7232f9b0 R15: 000000000000000  == ... Para resolver este problema, solo necesitamos verificar si el ino del inodo EA y el padre es el mismo antes de obtener el inodo EA."}], "references": [{"url": "https://git.kernel.org/stable/c/0f7bfd6f8164be32dbbdf36aa1e5d00485c53cd7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1aec41c98cce61d19ce89650895e51b9f3cdef13", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2c96c52aeaa6fd9163cfacdd98778b4a0398ef18", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/64b72f5e7574020dea62ab733d88a54d903c42a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/73f7987fe1b82596f1a380e85cd0097ebaae7e01", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/94fd091576b12540924f6316ebc0678e84cb2800", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a98160d8f3e6242ca9b7f443f26e7ef3a61ba684", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/efddc7e106fdf8d1f62d45e79de78f63b7c04fba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
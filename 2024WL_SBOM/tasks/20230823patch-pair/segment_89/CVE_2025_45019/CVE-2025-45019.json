{"cve_id": "CVE-2025-45019", "published_date": "2025-04-30T14:15:29.660", "last_modified_date": "2025-05-09T13:43:45.307", "descriptions": [{"lang": "en", "value": "A SQL injection vulnerability was discovered in /add-foreigners-ticket.php file of PHPGurukul Park Ticketing Management System v2.0. This vulnerability allows remote attackers to execute arbitrary code via the cprice POST request parameter."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección SQL en el archivo /add-foreigners-ticket.php de PHPGurukul Park Ticketing Management System v2.0. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario mediante el parámetro de solicitud POST cprice."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Park-Ticketing-Management-System-Project/SQL/SQL_injection_add_foreigners_ticket.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
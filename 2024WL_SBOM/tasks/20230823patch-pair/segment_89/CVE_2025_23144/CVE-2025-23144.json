{"cve_id": "CVE-2025-23144", "published_date": "2025-05-01T13:15:50.237", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbacklight: led_bl: Hold led_access lock when calling led_sysfs_disable()\n\nLockdep detects the following issue on led-backlight removal:\n  [  142.315935] ------------[ cut here ]------------\n  [  142.315954] WARNING: CPU: 2 PID: 292 at drivers/leds/led-core.c:455 led_sysfs_enable+0x54/0x80\n  ...\n  [  142.500725] Call trace:\n  [  142.503176]  led_sysfs_enable+0x54/0x80 (P)\n  [  142.507370]  led_bl_remove+0x80/0xa8 [led_bl]\n  [  142.511742]  platform_remove+0x30/0x58\n  [  142.515501]  device_remove+0x54/0x90\n  ...\n\nIndeed, led_sysfs_enable() has to be called with the led_access\nlock held.\n\nHold the lock when calling led_sysfs_disable()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: backlight: led_bl: Mantener el bloqueo de led_access al llamar a led_sysfs_disable() Lockdep detecta el siguiente problema al eliminar la retroiluminación LED: [ 142.315935] ------------[ cortar aquí ]------------ [ 142.315954] ADVERTENCIA: CPU: 2 PID: 292 en drivers/leds/led-core.c:455 led_sysfs_enable+0x54/0x80 ... [ 142.500725] Rastreo de llamada: [ 142.503176] led_sysfs_enable+0x54/0x80 (P) [ 142.507370] led_bl_remove+0x80/0xa8 [led_bl] [ 142.511742] platform_remove+0x30/0x58 [ 142.515501] device_remove+0x54/0x90 ... De hecho, led_sysfs_enable() debe llamarse con el bloqueo de led_access activado. Mantenga el bloqueo al llamar a led_sysfs_disable()."}], "references": [{"url": "https://git.kernel.org/stable/c/11d128f7eacec276c75cf4712880a6307ca9c885", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1c82f5a393d8b9a5c1ea032413719862098afd4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/276822a00db3c1061382b41e72cafc09d6a0ec30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/61a5c565fd2442d3128f3bab5f022658adc3a4e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/74c7d67a3c305fc1fa03c32a838e8446fb7aee14", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/87d947a0607be384bfe7bb0935884a711e35ca07", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b447885ec9130cf86f355e011dc6b94d6ccfb5b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b8ddf5107f53789448900f04fa220f34cd2f777e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
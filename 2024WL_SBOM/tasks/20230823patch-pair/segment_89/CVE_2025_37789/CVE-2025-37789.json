{"cve_id": "CVE-2025-37789", "published_date": "2025-05-01T14:15:43.290", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: openvswitch: fix nested key length validation in the set() action\n\nIt's not safe to access nla_len(ovs_key) if the data is smaller than\nthe netlink header.  Check that the attribute is OK first."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: openvswitch: corrección de la validación de la longitud de la clave anidada en la acción set(). No es seguro acceder a nla_len(ovs_key) si los datos son menores que el encabezado netlink. Compruebe primero que el atributo esté correcto."}], "references": [{"url": "https://git.kernel.org/stable/c/03d7262dd53e8c404da35cc81aaa887fd901f76b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1489c195c8eecd262aa6712761ba5288203e28ec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/54c6957d1123a2032099b9eab51c314800f677ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/65d91192aa66f05710cfddf6a14b5a25ee554dba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7fcaec0b2ab8fa5fbf0b45e5512364a168f445bd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/824a7c2df5127b2402b68a21a265d413e78dcad7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a27526e6b48eee9e2d82efff502c4f272f1a91d4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be80768d4f3b6fd13f421451cc3fee8778aba8bc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-2011", "published_date": "2025-05-06T10:15:15.060", "last_modified_date": "2025-05-07T14:15:42.853", "descriptions": [{"lang": "en", "value": "The Slider & Popup Builder by Depicter plugin for WordPress is vulnerable to generic SQL Injection via the ‘s' parameter in all versions up to, and including, 3.6.1 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Slider &amp; Popup Builder de Depicter para WordPress es vulnerable a la inyección SQL genérica mediante el parámetro 's' en todas las versiones hasta la 3.6.1 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas ya existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/depicter/trunk/app/src/Controllers/Ajax/LeadsAjaxController.php?rev=3156664#L179", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/depicter/trunk/app/src/Controllers/Ajax/LeadsAjaxController.php?rev=3156664#L23", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/depicter/trunk/app/src/Controllers/Ajax/LeadsAjaxController.php?rev=3156664#L49", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/depicter/trunk/app/src/Database/Repository/LeadRepository.php?rev=3156664#L224", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/depicter/trunk/app/src/Services/LeadService.php?rev=3156664#L82", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3287525/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/depicter/#description", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/49b36cde-39d8-4a69-8d7c-7b850b76a7cd?source=cve", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/datagoboom/CVE-2025-2011", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
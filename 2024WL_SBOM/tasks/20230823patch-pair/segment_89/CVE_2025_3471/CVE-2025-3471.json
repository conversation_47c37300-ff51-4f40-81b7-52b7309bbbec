{"cve_id": "CVE-2025-3471", "published_date": "2025-04-30T06:15:53.153", "last_modified_date": "2025-05-09T13:48:03.503", "descriptions": [{"lang": "en", "value": "The SureForms  WordPress plugin before 1.4.4 does not have proper authorisation check when updating its settings via the REST API, which could allow Contributor and above roles to perform such action"}, {"lang": "es", "value": "El complemento SureForms de WordPress anterior a la versión 1.4.4 no tiene una verificación de autorización adecuada al actualizar su configuración a través de la API REST, lo que podría permitir que los roles de Colaborador y superiores realicen dicha acción."}], "references": [{"url": "https://wpscan.com/vulnerability/aa21dd2b-1277-4cf9-b7f6-d4f8a6d518c1/", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}]}
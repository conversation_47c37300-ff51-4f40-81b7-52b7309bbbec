{"cve_id": "CVE-2025-37766", "published_date": "2025-05-01T14:15:39.550", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/pm: Prevent division by zero\n\nThe user can set any speed value.\nIf speed is greater than UINT_MAX/8, division by zero is possible.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/pm: Impide la división por cero. El usuario puede establecer cualquier valor de velocidad. Si la velocidad es superior a UINT_MAX/8, es posible la división por cero. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/068091b796480819bf70b159f17e222ad8bea900", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42f7b5d12c28b2a601a98d10a80c6db1fe1a2900", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4e3d9508c056d7e0a56b58d5c81253e2a0d22b6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6b9f9b998b107c7539f148a013d789ddb860c3b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/80814924260cea431a8fc6137d11cc8cb331a10c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/affd2241927a1e74c0aecd50c2d920dc4213c56d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ce773dd844ee19a605af27f11470887e0f2044a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ffd688804425579a472fbd2525bedb58b1d28bd9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
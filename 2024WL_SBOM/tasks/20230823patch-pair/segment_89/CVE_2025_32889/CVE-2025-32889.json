{"cve_id": "CVE-2025-32889", "published_date": "2025-05-01T18:15:56.247", "last_modified_date": "2025-06-20T16:35:09.850", "descriptions": [{"lang": "en", "value": "An issue was discovered on goTenna v1 devices with app 5.5.3 and firmware 0.25.5. The verification token used for sending SMS through a goTenna server is hardcoded in the app."}, {"lang": "es", "value": "Se detectó un problema en dispositivos goTenna v1 con la aplicación 5.5.3 y el firmware 0.25.5. El token de verificación utilizado para enviar SMS a través de un servidor goTenna está codificado en la aplicación."}], "references": [{"url": "https://github.com/Dollarhyde/goTenna_v1_and_Mesh_vulnerabilities", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://gotenna.com", "source": "<EMAIL>", "tags": ["Product"]}]}
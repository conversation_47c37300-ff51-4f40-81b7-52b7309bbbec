{"cve_id": "CVE-2025-27921", "published_date": "2025-05-05T16:15:51.143", "last_modified_date": "2025-06-13T18:40:52.873", "descriptions": [{"lang": "en", "value": "A reflected cross-site scripting (XSS) vulnerability was discovered in Output Messenger before 2.0.63, where unsanitized input could be injected into the web application’s response. This vulnerability occurs when user-controlled input is reflected back into the browser without proper sanitization or encoding."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de cross-site scripting (XSS) reflejado en Output Messenger antes de la versión 2.0.63, donde se podía inyectar información no saneada en la respuesta de la aplicación web. Esta vulnerabilidad ocurre cuando la información controlada por el usuario se refleja en el navegador sin la saneamiento ni la codificación adecuadas."}], "references": [{"url": "https://www.outputmessenger.com/cve-2025-27921/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.srimax.com/products-2/output-messenger/", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2025-37790", "published_date": "2025-05-01T14:15:43.407", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: mctp: Set SOCK_RCU_FREE\n\nBind lookup runs under RCU, so ensure that a socket doesn't go away in\nthe middle of a lookup."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: mctp: La búsqueda de enlace Set SOCK_RCU_FREE se ejecuta bajo RCU, por lo que debe asegurarse de que un socket no desaparezca en medio de una búsqueda."}], "references": [{"url": "https://git.kernel.org/stable/c/3f899bd6dd56ddc46509b526e23a8f0a97712a6d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/52024cd6ec71a6ca934d0cc12452bd8d49850679", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5c1313b93c8c2e3904a48aa88e2fa1db28c607ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a8a3b61ce140e2b0a72a779e8d70f60c0cf1e47a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b9764ebebb007249fb733a131b6110ff333b6616", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3b5edbdb45924a7d4206d13868a2aac71f1e53d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
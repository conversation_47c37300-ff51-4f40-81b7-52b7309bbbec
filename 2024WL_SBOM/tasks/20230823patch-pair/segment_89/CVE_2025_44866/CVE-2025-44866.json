{"cve_id": "CVE-2025-44866", "published_date": "2025-05-01T18:15:57.200", "last_modified_date": "2025-05-27T16:44:21.860", "descriptions": [{"lang": "en", "value": "Tenda W20E V15.11.0.6 was found to contain a command injection vulnerability in the formSetDebugCfg function via the level parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se detectó que Tenda W20E V15.11.0.6 contenía una vulnerabilidad de inyección de comandos en la función formSetDebugCfg mediante el parámetro level. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Tenda_W20E/formSetDebugCfg_level/readme.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-4051", "published_date": "2025-05-05T18:15:44.060", "last_modified_date": "2025-05-28T20:08:14.553", "descriptions": [{"lang": "en", "value": "Insufficient data validation in DevTools in Google Chrome prior to 136.0.7103.59 allowed a remote attacker who convinced a user to engage in specific UI gestures to bypass discretionary access control via a crafted HTML page. (Chromium security severity: Medium)"}, {"lang": "es", "value": "Una validación de datos insuficiente en DevTools de Google Chrome anterior a la versión 136.0.7103.59 permitió que un atacante remoto, tras convencer a un usuario para que realizara gestos específicos de la interfaz de usuario, eludiera el control de acceso discrecional mediante una página HTML manipulada. (Gravedad de seguridad de Chromium: Media)"}], "references": [{"url": "https://chromereleases.googleblog.com/2025/04/stable-channel-update-for-desktop_29.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://issues.chromium.org/issues/404000989", "source": "<EMAIL>", "tags": ["Permissions Required"]}]}
{"cve_id": "CVE-2022-49891", "published_date": "2025-05-01T15:16:14.107", "last_modified_date": "2025-05-07T13:19:49.620", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntracing: kprobe: Fix memory leak in test_gen_kprobe/kretprobe_cmd()\n\ntest_gen_kprobe_cmd() only free buf in fail path, hence buf will leak\nwhen there is no failure. Move kfree(buf) from fail path to common path\nto prevent the memleak. The same reason and solution in\ntest_gen_kretprobe_cmd().\n\nunreferenced object 0xffff888143b14000 (size 2048):\n  comm \"insmod\", pid 52490, jiffies 4301890980 (age 40.553s)\n  hex dump (first 32 bytes):\n    70 3a 6b 70 72 6f 62 65 73 2f 67 65 6e 5f 6b 70  p:kprobes/gen_kp\n    72 6f 62 65 5f 74 65 73 74 20 64 6f 5f 73 79 73  robe_test do_sys\n  backtrace:\n    [<000000006d7b836b>] kmalloc_trace+0x27/0xa0\n    [<0000000009528b5b>] 0xffffffffa059006f\n    [<000000008408b580>] do_one_initcall+0x87/0x2a0\n    [<00000000c4980a7e>] do_init_module+0xdf/0x320\n    [<00000000d775aad0>] load_module+0x3006/0x3390\n    [<00000000e9a74b80>] __do_sys_finit_module+0x113/0x1b0\n    [<000000003726480d>] do_syscall_64+0x35/0x80\n    [<000000003441e93b>] entry_SYSCALL_64_after_hwframe+0x46/0xb0"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rastreo: kprobe: Se corrige la fuga de memoria en test_gen_kprobe/kretprobe_cmd(). test_gen_kprobe_cmd() solo libera búfer en la ruta de fallo, por lo que el búfer se filtrará cuando no haya fallo. Se traslada kfree(buf) de la ruta de fallo a la ruta común para evitar la fuga de memoria. El mismo motivo y solución se aplican en test_gen_kretprobe_cmd(). Objeto sin referencia 0xffff888143b14000 (size 2048): comm \"insmod\", pid 52490, jiffies 4301890980 (age 40.553s) hex dump (first 32 bytes): 70 3a 6b 70 72 6f 62 65 73 2f 67 65 6e 5f 6b 70 p:kprobes/gen_kp 72 6f 62 65 5f 74 65 73 74 20 64 6f 5f 73 79 73 robe_test do_sys backtrace: [&lt;000000006d7b836b&gt;] kmalloc_trace+0x27/0xa0 [&lt;0000000009528b5b&gt;] 0xffffffffa059006f [&lt;000000008408b580&gt;] do_one_initcall+0x87/0x2a0 [&lt;00000000c4980a7e&gt;] do_init_module+0xdf/0x320 [&lt;00000000d775aad0&gt;] load_module+0x3006/0x3390 [&lt;00000000e9a74b80&gt;] __do_sys_finit_module+0x113/0x1b0 [&lt;000000003726480d&gt;] do_syscall_64+0x35/0x80 [&lt;000000003441e93b&gt;] entry_SYSCALL_64_after_hwframe+0x46/0xb0 "}], "references": [{"url": "https://git.kernel.org/stable/c/66f0919c953ef7b55e5ab94389a013da2ce80a2c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/71aeb8d01a8c7ab5cf7da3f81b35206f56ce6bca", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bef08acbe560a926b4cee9cc46404cc98ae5703b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d1b6a8e3414aeaa0985139180c145d2d0fbd2a49", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
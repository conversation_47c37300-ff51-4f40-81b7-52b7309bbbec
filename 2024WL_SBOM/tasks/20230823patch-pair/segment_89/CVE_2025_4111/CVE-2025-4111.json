{"cve_id": "CVE-2025-4111", "published_date": "2025-04-30T11:15:50.283", "last_modified_date": "2025-05-13T20:25:46.647", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Pre-School Enrollment System 1.0. It has been classified as critical. This affects an unknown part of the file /admin/visitor-details.php. The manipulation of the argument Status leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Pre-School Enrollment System 1.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/visitor-details.php. La manipulación del argumento \"Status\" provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306591", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306591", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560706", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-46565", "published_date": "2025-05-01T18:15:57.797", "last_modified_date": "2025-05-02T18:15:27.060", "descriptions": [{"lang": "en", "value": "Vite is a frontend tooling framework for javascript. Prior to versions 6.3.4, 6.2.7, 6.1.6, 5.4.19, and 4.5.14, the contents of files in the project root that are denied by a file matching pattern can be returned to the browser. Only apps explicitly exposing the Vite dev server to the network (using --host or server.host config option) are affected. Only files that are under project root and are denied by a file matching pattern can be bypassed. `server.fs.deny` can contain patterns matching against files (by default it includes .env, .env.*, *.{crt,pem} as such patterns). These patterns were able to bypass for files under `root` by using a combination of slash and dot (/.). This issue has been patched in versions 6.3.4, 6.2.7, 6.1.6, 5.4.19, and 4.5.14."}, {"lang": "es", "value": "Vite es un framework de herramientas frontend para JavaScript. En versiones anteriores a las 6.3.4, 6.2.7, 6.1.6, 5.4.19 y 4.5.14, el contenido de los archivos en la root del proyecto que se deniegan por un patrón de coincidencia de archivos se puede devolver al navegador. Solo las aplicaciones que exponen explícitamente el servidor de desarrollo de Vite a la red (mediante la opción de configuración --host o server.host) se ven afectadas. Solo se pueden omitir los archivos que se encuentran en la root del proyecto y se deniegan por un patrón de coincidencia de archivos. `server.fs.deny` puede contener patrones que coinciden con archivos (por defecto, incluye .env, .env.*, *.{crt,pem} como tales patrones). Estos patrones se podían omitir para los archivos en `root` mediante una combinación de barra diagonal y punto (/.). Este problema se ha solucionado en las versiones 6.3.4, 6.2.7, 6.1.6, 5.4.19 y 4.5.14."}], "references": [{"url": "https://github.com/vitejs/vite/commit/c22c43de612eebb6c182dd67850c24e4fab8cacb", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vitejs/vite/security/advisories/GHSA-859w-5945-r5v3", "source": "<EMAIL>", "tags": []}]}
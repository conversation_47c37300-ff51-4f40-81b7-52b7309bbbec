{"cve_id": "CVE-2025-37788", "published_date": "2025-05-01T14:15:43.163", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncxgb4: fix memory leak in cxgb4_init_ethtool_filters() error path\n\nIn the for loop used to allocate the loc_array and bmap for each port, a\nmemory leak is possible when the allocation for loc_array succeeds,\nbut the allocation for bmap fails. This is because when the control flow\ngoes to the label free_eth_finfo, only the allocations starting from\n(i-1)th iteration are freed.\n\nFix that by freeing the loc_array in the bmap allocation error path."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cxgb4: Se corrige la fuga de memoria en la ruta de error de cxgb4_init_ethtool_filters(). En el bucle for utilizado para asignar loc_array y bmap para cada puerto, es posible que se produzca una fuga de memoria cuando la asignación de loc_array se realiza correctamente, pero la de bmap falla. Esto se debe a que, cuando el flujo de control accede a la etiqueta free_eth_finfo, solo se liberan las asignaciones a partir de la iteración (i-1). Para solucionar esto, libere loc_array en la ruta de error de asignación de bmap."}], "references": [{"url": "https://git.kernel.org/stable/c/00ffb3724ce743578163f5ade2884374554ca021", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/08aa59c0be768596467552c129e9f82166779a67", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/118d05b530343cd9322607b9719405ba254a4183", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76deedea08899885f076aba0bb80bd1276446822", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dafb6e433ab2333b67be05433dc9c6ccbc7b1284", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e9de08e15aee35b96064960f95997bb6c1209c4b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fa2d7708955e4f8212fd69bab1da604e60cb0b15", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
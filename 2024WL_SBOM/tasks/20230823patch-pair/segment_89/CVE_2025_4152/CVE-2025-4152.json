{"cve_id": "CVE-2025-4152", "published_date": "2025-05-01T06:15:35.657", "last_modified_date": "2025-05-07T20:06:35.910", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Online Birth Certificate System 1.0. Affected is an unknown function of the file /admin/bwdates-reports-details.php. The manipulation of the argument fromdate leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Online Birth Certificate System 1.0. Se ve afectada una función desconocida del archivo /admin/bwdates-reports-details.php. La manipulación del argumento fromdate provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://github.com/faithhard/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306684", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306684", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560808", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
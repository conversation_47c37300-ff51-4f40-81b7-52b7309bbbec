{"cve_id": "CVE-2025-4213", "published_date": "2025-05-02T18:15:28.160", "last_modified_date": "2025-05-28T20:56:16.840", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Online Birth Certificate System 1.0 and classified as critical. This vulnerability affects unknown code of the file /admin/search.php. The manipulation of the argument searchdata leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Online Birth Certificate System 1.0, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/search.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/LoovvvE18/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307192", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307192", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562291", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
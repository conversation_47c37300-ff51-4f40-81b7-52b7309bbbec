{"cve_id": "CVE-2025-4307", "published_date": "2025-05-06T03:15:18.660", "last_modified_date": "2025-05-13T19:21:10.793", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.1. It has been classified as critical. Affected is an unknown function of the file /admin/add-art-medium.php. The manipulation of the argument artmed leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Art Gallery Management System 1.1. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /admin/add-art-medium.php. La manipulación del argumento artmed provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/3507998897/myCVE/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307410", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307410", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564179", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2023-53086", "published_date": "2025-05-02T16:15:27.580", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: mt76: connac: do not check WED status for non-mmio devices\n\nWED is supported just for mmio devices, so do not check it for usb or\nsdio devices. This patch fixes the crash reported below:\n\n[   21.946627] wlp0s3u1i3: authenticate with c4:41:1e:f5:2b:1d\n[   22.525298] wlp0s3u1i3: send auth to c4:41:1e:f5:2b:1d (try 1/3)\n[   22.548274] wlp0s3u1i3: authenticate with c4:41:1e:f5:2b:1d\n[   22.557694] wlp0s3u1i3: send auth to c4:41:1e:f5:2b:1d (try 1/3)\n[   22.565885] wlp0s3u1i3: authenticated\n[   22.569502] wlp0s3u1i3: associate with c4:41:1e:f5:2b:1d (try 1/3)\n[   22.578966] wlp0s3u1i3: RX AssocResp from c4:41:1e:f5:2b:1d (capab=0x11 status=30 aid=3)\n[   22.579113] wlp0s3u1i3: c4:41:1e:f5:2b:1d rejected association temporarily; comeback duration 1000 TU (1024 ms)\n[   23.649518] wlp0s3u1i3: associate with c4:41:1e:f5:2b:1d (try 2/3)\n[   23.752528] wlp0s3u1i3: RX AssocResp from c4:41:1e:f5:2b:1d (capab=0x11 status=0 aid=3)\n[   23.797450] wlp0s3u1i3: associated\n[   24.959527] kernel tried to execute NX-protected page - exploit attempt? (uid: 0)\n[   24.959640] BUG: unable to handle page fault for address: ffff88800c223200\n[   24.959706] #PF: supervisor instruction fetch in kernel mode\n[   24.959788] #PF: error_code(0x0011) - permissions violation\n[   24.959846] PGD 2c01067 P4D 2c01067 PUD 2c02067 PMD c2a8063 PTE 800000000c223163\n[   24.959957] Oops: 0011 [#1] PREEMPT SMP\n[   24.960009] CPU: 0 PID: 391 Comm: wpa_supplicant Not tainted 6.2.0-kvm #18\n[   24.960089] Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.1-2.fc37 04/01/2014\n[   24.960191] RIP: 0010:0xffff88800c223200\n[   24.960446] RSP: 0018:ffffc90000ff7698 EFLAGS: 00010282\n[   24.960513] RAX: ffff888028397010 RBX: ffff88800c26e630 RCX: 0000000000000058\n[   24.960598] RDX: ffff88800c26f844 RSI: 0000000000000006 RDI: ffff888028397010\n[   24.960682] RBP: ffff88800ea72f00 R08: 18b873fbab2b964c R09: be06b38235f3c63c\n[   24.960766] R10: 18b873fbab2b964c R11: be06b38235f3c63c R12: 0000000000000001\n[   24.960853] R13: ffff88800c26f84c R14: ffff8880063f0ff8 R15: ffff88800c26e644\n[   24.960950] FS:  00007effcea327c0(0000) GS:ffff88807dc00000(0000) knlGS:0000000000000000\n[   24.961036] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033\n[   24.961106] CR2: ffff88800c223200 CR3: 000000000eaa2000 CR4: 00000000000006b0\n[   24.961190] Call Trace:\n[   24.961219]  <TASK>\n[   24.961245]  ? mt76_connac_mcu_add_key+0x2cf/0x310\n[   24.961313]  ? mt7921_set_key+0x150/0x200\n[   24.961365]  ? drv_set_key+0xa9/0x1b0\n[   24.961418]  ? ieee80211_key_enable_hw_accel+0xd9/0x240\n[   24.961485]  ? ieee80211_key_replace+0x3f3/0x730\n[   24.961541]  ? crypto_shash_setkey+0x89/0xd0\n[   24.961597]  ? ieee80211_key_link+0x2d7/0x3a0\n[   24.961664]  ? crypto_aead_setauthsize+0x31/0x50\n[   24.961730]  ? sta_info_hash_lookup+0xa6/0xf0\n[   24.961785]  ? ieee80211_add_key+0x1fc/0x250\n[   24.961842]  ? rdev_add_key+0x41/0x140\n[   24.961882]  ? nl80211_parse_key+0x6c/0x2f0\n[   24.961940]  ? nl80211_new_key+0x24a/0x290\n[   24.961984]  ? genl_rcv_msg+0x36c/0x3a0\n[   24.962036]  ? rdev_mod_link_station+0xe0/0xe0\n[   24.962102]  ? nl80211_set_key+0x410/0x410\n[   24.962143]  ? nl80211_pre_doit+0x200/0x200\n[   24.962187]  ? genl_bind+0xc0/0xc0\n[   24.962217]  ? netlink_rcv_skb+0xaa/0xd0\n[   24.962259]  ? genl_rcv+0x24/0x40\n[   24.962300]  ? netlink_unicast+0x224/0x2f0\n[   24.962345]  ? netlink_sendmsg+0x30b/0x3d0\n[   24.962388]  ? ____sys_sendmsg+0x109/0x1b0\n[   24.962388]  ? ____sys_sendmsg+0x109/0x1b0\n[   24.962440]  ? __import_iovec+0x2e/0x110\n[   24.962482]  ? ___sys_sendmsg+0xbe/0xe0\n[   24.962525]  ? mod_objcg_state+0x25c/0x330\n[   24.962576]  ? __dentry_kill+0x19e/0x1d0\n[   24.962618]  ? call_rcu+0x18f/0x270\n[   24.962660]  ? __dentry_kill+0x19e/0x1d0\n[   24.962702]  ? __x64_sys_sendmsg+0x70/0x90\n[   24.962744]  ? do_syscall_64+0x3d/0x80\n[   24.962796]  ? exit_to_user_mode_prepare+0x1b/0x70\n[   24.962852]  ? entry_SYSCA\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: mt76: connac: no verifique el estado de WED para dispositivos que no sean mmio WED solo es compatible con dispositivos mmio, por lo que no lo verifique para dispositivos usb o sdio. Este parche corrige el fallo informado a continuación: [ 21.946627] wlp0s3u1i3: autenticar con c4:41:1e:f5:2b:1d [ 22.525298] wlp0s3u1i3: enviar autenticación a c4:41:1e:f5:2b:1d (intentar 1/3) [ 22.548274] wlp0s3u1i3: autenticar con c4:41:1e:f5:2b:1d [ 22.557694] wlp0s3u1i3: enviar autenticación a c4:41:1e:f5:2b:1d (intentar 1/3) [ 22.565885] wlp0s3u1i3: autenticado [ 22.569502] wlp0s3u1i3: asociar con c4:41:1e:f5:2b:1d (try 1/3) [ 22.578966] wlp0s3u1i3: RX AssocResp de c4:41:1e:f5:2b:1d (capab=0x11 status=30 aid=3) [ 22.579113] wlp0s3u1i3: c4:41:1e:f5:2b:1d rechazó la asociación temporalmente; duración del regreso 1000 TU (1024 ms) [ 23.649518] wlp0s3u1i3: asociado con c4:41:1e:f5:2b:1d (intento 2/3) [ 23.752528] wlp0s3u1i3: RX AssocResp de c4:41:1e:f5:2b:1d (capab=0x11 status=0 aid=3) [ 23.797450] wlp0s3u1i3: asociado [ 24.959527] el kernel intentó ejecutar página protegida por NX - ¿intento de explotación? (uid: 0) [24.959640] ERROR: no se puede manejar el error de página para la dirección: ffff88800c223200 [24.959706] #PF: obtención de instrucción de supervisor en modo kernel [24.959788] #PF: error_code(0x0011) - violación de permisos [24.959846] PGD 2c01067 P4D 2c01067 PUD 2c02067 PMD c2a8063 PTE 800000000c223163 [24.959957] Oops: 0011 [#1] PREEMPT SMP [24.960009] CPU: 0 PID: 391 Comm: wpa_supplicant No contaminado 6.2.0-kvm #18 [ 24.960089] Nombre del hardware: PC estándar QEMU (Q35 + ICH9, 2009), BIOS 1.16.1-2.fc37 01/04/2014 [ 24.960191] RIP: 0010:0xffff88800c223200 [ 24.960446] RSP: 0018:ffffc90000ff7698 EFLAGS: 00010282 [ 24.960513] RAX: ffff888028397010 RBX: ffff88800c26e630 RCX: 0000000000000058 [ 24.960598] RDX: ffff88800c26f844 RSI: 0000000000000006 RDI: ffff888028397010 [ 24.960682] RBP: ffff88800ea72f00 R08: 18b873fbab2b964c R09: be06b38235f3c63c [ 24.960766] R10: 18b873fbab2b964c R11: be06b38235f3c63c R12: 000000000000001 [ 24.960853] R13: ffff88800c26f84c R14: ffff8880063f0ff8 R15: ffff88800c26e644 [24.960950] FS: 00007effcea327c0(0000) GS:ffff88807dc00000(0000) knlGS:0000000000000000 [24.961036] CS: 0010 DS: 0000 ES: 0000 CR0: 0000000080050033 [24.961106] CR2: ffff88800c223200 CR3: 000000000eaa2000 CR4: 00000000000006b0 [24.961190] Rastreo de llamadas: [24.961219]  [ 24.961245] ? mt76_connac_mcu_add_key+0x2cf/0x310 [ 24.961313] ? mt7921_set_key+0x150/0x200 [ 24.961365] ? drv_set_key+0xa9/0x1b0 [ 24.961418] ? ieee80211_key_enable_hw_accel+0xd9/0x240 [ 24.961485] ? ieee80211_key_replace+0x3f3/0x730 [ 24.961541] ? crypto_shash_setkey+0x89/0xd0 [ 24.961597] ? ieee80211_key_link+0x2d7/0x3a0 [ 24.961664] ? crypto_aead_setauthsize+0x31/0x50 [ 24.961730] ? sta_info_hash_lookup+0xa6/0xf0 [ 24.961785] ? ieee80211_add_key+0x1fc/0x250 [ 24.961842] ? rdev_add_key+0x41/0x140 [ 24.961882] ? nl80211_parse_key+0x6c/0x2f0 [ 24.961940] ? nl80211_new_key+0x24a/0x290 [ 24.961984] ? genl_rcv_msg+0x36c/0x3a0 [ 24.962036] ? rdev_mod_link_station+0xe0/0xe0 [ 24.962102] ? nl80211_set_key+0x410/0x410 [ 24.962143] ? nl80211_pre_doit+0x200/0x200 [ 24.962187] ? genl_bind+0xc0/0xc0 [ 24.962217] ? netlink_rcv_skb+0xaa/0xd0 [ 24.962259] ? genl_rcv+0x24/0x40 [ 24.962300] ? netlink_unicast+0x224/0x2f0 [ 24.962345] ? netlink_sendmsg+0x30b/0x3d0 [ 24.962388] ? ____sys_sendmsg+0x109/0x1b0 [ 24.962388] ? ____sys_sendmsg+0x109/0x1b0 [ 24.962440] ? __import_iovec+0x2e/0x110 [ 24.962482] ? ___sys_sendmsg+0xbe/0xe0 [ 24.962525] ? mod_objcg_state+0x25c/0x330 [ 24.962576] ? __dentry_kill+0x19e/0x1d0 [ 24.962618] ? call_rcu+0x18f/0x270 [ 24.962660] ? __dentry_kill+0x19e/0x1d0 [ 24.962702] ? __x64_sys_sendmsg+0x70/0x90 [ 24.962744] ? do_syscall_64+0x3d/0x80 [ 24.962796] ? exit_to_user_mode_prepare+0x1b/0x70 [ 24.962852] ? entry_SYSCA ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/53edfda851dd1ce41ac049ce2f195dc41dd27cc1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5683e1488aa9b0805a9403d215e48fed29d6d923", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
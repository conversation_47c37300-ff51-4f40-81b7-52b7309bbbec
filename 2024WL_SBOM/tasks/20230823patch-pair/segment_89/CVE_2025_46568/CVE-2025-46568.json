{"cve_id": "CVE-2025-46568", "published_date": "2025-05-01T18:15:58.263", "last_modified_date": "2025-06-17T14:19:19.067", "descriptions": [{"lang": "en", "value": "Stirling-PDF is a locally hosted web application that allows you to perform various operations on PDF files. Prior to version 0.45.0, Stirling-PDF is vulnerable to SSRF-induced arbitrary file read. WeasyPrint redefines a set of HTML tags, including img, embed, object, and others. The references to several files inside, allow the attachment of content from any webpage or local file to a PDF. This allows the attacker to read any file on the server, including sensitive files and configuration files. All users utilizing this feature will be affected. This issue has been patched in version 0.45.0."}, {"lang": "es", "value": "Stirling-PDF es una aplicación web alojada localmente que permite realizar diversas operaciones con archivos PDF. Antes de la versión 0.45.0, Stirling-PDF era vulnerable a la lectura arbitraria de archivos inducida por SSRF. WeasyPrint redefine un conjunto de etiquetas HTML, como img, embed, object y otras. Las referencias a varios archivos que contiene permiten adjuntar contenido de cualquier página web o archivo local a un PDF. Esto permite al atacante leer cualquier archivo del servidor, incluyendo archivos confidenciales y de configuración. Todos los usuarios que utilicen esta función se verán afectados. Este problema se ha corregido en la versión 0.45.0."}], "references": [{"url": "https://github.com/Stirling-Tools/Stirling-PDF/security/advisories/GHSA-998c-x8hx-737r", "source": "<EMAIL>", "tags": ["Exploit"]}]}
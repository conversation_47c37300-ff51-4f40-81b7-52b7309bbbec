{"cve_id": "CVE-2025-37775", "published_date": "2025-05-01T14:15:41.197", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix the warning from __kernel_write_iter\n\n[ 2110.972290] ------------[ cut here ]------------\n[ 2110.972301] WARNING: CPU: 3 PID: 735 at fs/read_write.c:599 __kernel_write_iter+0x21b/0x280\n\nThis patch doesn't allow writing to directory."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: corrige la advertencia de __kernel_write_iter [ 2110.972290] ------------[ cortar aquí ]------------ [ 2110.972301] ADVERTENCIA: CPU: 3 PID: 735 en fs/read_write.c:599 __kernel_write_iter+0x21b/0x280 Este parche no permite escribir en el directorio."}], "references": [{"url": "https://git.kernel.org/stable/c/1ed343481ba6911178bc5ca7a51be319eafcc747", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2a879da5c34a1e5d971e815d5b30f27eb6d69efc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/44079e544c9f6e3e9fb43a16ddf8b08cf686d657", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b37f2f332b40ad1c27f18682a495850f2f04db0a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b7ce8db490286c2e009758fa1416d66aeb333614", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
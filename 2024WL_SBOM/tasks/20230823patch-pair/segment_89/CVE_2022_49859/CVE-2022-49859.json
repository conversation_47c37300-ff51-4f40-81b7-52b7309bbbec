{"cve_id": "CVE-2022-49859", "published_date": "2025-05-01T15:16:09.510", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: lapbether: fix issue of invalid opcode in lapbeth_open()\n\nIf lapb_register() failed when lapb device goes to up for the first time,\nthe NAPI is not disabled. As a result, the invalid opcode issue is\nreported when the lapb device goes to up for the second time.\n\nThe stack info is as follows:\n[ 1958.311422][T11356] kernel BUG at net/core/dev.c:6442!\n[ 1958.312206][T11356] invalid opcode: 0000 [#1] PREEMPT SMP KASAN\n[ 1958.315979][T11356] RIP: 0010:napi_enable+0x16a/0x1f0\n[ 1958.332310][T11356] Call Trace:\n[ 1958.332817][T11356]  <TASK>\n[ 1958.336135][T11356]  lapbeth_open+0x18/0x90\n[ 1958.337446][T11356]  __dev_open+0x258/0x490\n[ 1958.341672][T11356]  __dev_change_flags+0x4d4/0x6a0\n[ 1958.345325][T11356]  dev_change_flags+0x93/0x160\n[ 1958.346027][T11356]  devinet_ioctl+0x1276/0x1bf0\n[ 1958.346738][T11356]  inet_ioctl+0x1c8/0x2d0\n[ 1958.349638][T11356]  sock_ioctl+0x5d1/0x750\n[ 1958.356059][T11356]  __x64_sys_ioctl+0x3ec/0x1790\n[ 1958.365594][T11356]  do_syscall_64+0x35/0x80\n[ 1958.366239][T11356]  entry_SYSCALL_64_after_hwframe+0x46/0xb0\n[ 1958.377381][T11356]  </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: lapbether: se corrige el problema de un código de operación no válido en lapbeth_open(). Si lapb_register() falla al iniciar el dispositivo lapb por primera vez, NAPI no se desactiva. Por lo tanto, el problema de código de operación no válido se reporta al iniciar el dispositivo lapb por segunda vez. La información de la pila es la siguiente: [ 1958.311422][T11356] kernel BUG at net/core/dev.c:6442! [ 1958.312206][T11356] invalid opcode: 0000 [#1] PREEMPT SMP KASAN [ 1958.315979][T11356] RIP: 0010:napi_enable+0x16a/0x1f0 [ 1958.332310][T11356] Call Trace: [ 1958.332817][T11356]  [ 1958.336135][T11356] lapbeth_open+0x18/0x90 [ 1958.337446][T11356] __dev_open+0x258/0x490 [ 1958.341672][T11356] __dev_change_flags+0x4d4/0x6a0 [ 1958.345325][T11356] dev_change_flags+0x93/0x160 [ 1958.346027][T11356] devinet_ioctl+0x1276/0x1bf0 [ 1958.346738][T11356] inet_ioctl+0x1c8/0x2d0 [ 1958.349638][T11356] sock_ioctl+0x5d1/0x750 [ 1958.356059][T11356] __x64_sys_ioctl+0x3ec/0x1790 [ 1958.365594][T11356] do_syscall_64+0x35/0x80 [ 1958.366239][T11356] entry_SYSCALL_64_after_hwframe+0x46/0xb0 [ 1958.377381][T11356]  "}], "references": [{"url": "https://git.kernel.org/stable/c/3faf7e14ec0c3462c2d747fa6793b8645d1391df", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4689bd3a1b23a1bd917899e63b81bca2ccdfab45", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ed4940050a7ce7fc2ccd51db580ef1ade64290b1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
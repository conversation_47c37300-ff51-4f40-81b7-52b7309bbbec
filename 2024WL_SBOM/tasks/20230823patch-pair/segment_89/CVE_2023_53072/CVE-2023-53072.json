{"cve_id": "CVE-2023-53072", "published_date": "2025-05-02T16:15:26.237", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmptcp: use the workqueue to destroy unaccepted sockets\n\nChristoph reported a UaF at token lookup time after having\nrefactored the passive socket initialization part:\n\n  BUG: KASAN: use-after-free in __token_bucket_busy+0x253/0x260\n  Read of size 4 at addr ffff88810698d5b0 by task syz-executor653/3198\n\n  CPU: 1 PID: 3198 Comm: syz-executor653 Not tainted 6.2.0-rc59af4eaa31c1f6c00c8f1e448ed99a45c66340dd5 #6\n  Hardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.13.0-0-gf21b5a4aeb02-prebuilt.qemu.org 04/01/2014\n  Call Trace:\n   <TASK>\n   dump_stack_lvl+0x6e/0x91\n   print_report+0x16a/0x46f\n   kasan_report+0xad/0x130\n   __token_bucket_busy+0x253/0x260\n   mptcp_token_new_connect+0x13d/0x490\n   mptcp_connect+0x4ed/0x860\n   __inet_stream_connect+0x80e/0xd90\n   tcp_sendmsg_fastopen+0x3ce/0x710\n   mptcp_sendmsg+0xff1/0x1a20\n   inet_sendmsg+0x11d/0x140\n   __sys_sendto+0x405/0x490\n   __x64_sys_sendto+0xdc/0x1b0\n   do_syscall_64+0x3b/0x90\n   entry_SYSCALL_64_after_hwframe+0x72/0xdc\n\nWe need to properly clean-up all the paired MPTCP-level\nresources and be sure to release the msk last, even when\nthe unaccepted subflow is destroyed by the TCP internals\nvia inet_child_forget().\n\nWe can re-use the existing MPTCP_WORK_CLOSE_SUBFLOW infra,\nexplicitly checking that for the critical scenario: the\nclosed subflow is the MPC one, the msk is not accepted and\neventually going through full cleanup.\n\nWith such change, __mptcp_destroy_sock() is always called\non msk sockets, even on accepted ones. We don't need anymore\nto transiently drop one sk reference at msk clone time.\n\nPlease note this commit depends on the parent one:\n\n  mptcp: refactor passive socket initialization"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mptcp: usa workqueue para destruir sockets no aceptados Christoph informó un UaF en el momento de la búsqueda del token después de haber refactorizado la parte de inicialización del socket pasivo: ERROR: KASAN: use-after-free en __token_bucket_busy+0x253/0x260 Lectura de tamaño 4 en la dirección ffff88810698d5b0 por la tarea syz-executor653/3198 CPU: 1 PID: 3198 Comm: syz-executor653 No contaminado 6.2.0-rc59af4eaa31c1f6c00c8f1e448ed99a45c66340dd5 #6 Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996), BIOS rel-1.13.0-0-gf21b5a4aeb02-prebuilt.qemu.org 01/04/2014 Rastreo de llamadas:  dump_stack_lvl+0x6e/0x91 print_report+0x16a/0x46f kasan_report+0xad/0x130 __token_bucket_busy+0x253/0x260 mptcp_token_new_connect+0x13d/0x490 mptcp_connect+0x4ed/0x860 __inet_stream_connect+0x80e/0xd90 tcp_sendmsg_fastopen+0x3ce/0x710 mptcp_sendmsg+0xff1/0x1a20 inet_sendmsg+0x11d/0x140 __sys_sendto+0x405/0x490 __x64_sys_sendto+0xdc/0x1b0 do_syscall_64+0x3b/0x90 entry_SYSCALL_64_after_hwframe+0x72/0xdc Necesitamos limpiar correctamente todos los recursos emparejados de nivel MPTCP y asegurarnos de liberar el msk al final, incluso cuando el subflujo no aceptado es destruido por los procesos internos de TCP mediante inet_child_forget(). Podemos reutilizar la infra MPTCP_WORK_CLOSE_SUBFLOW existente, comprobando explícitamente que para el escenario crítico: el subflujo cerrado es el de MPC, el msk no es aceptado y finalmente se realiza una limpieza completa. Con este cambio, __mptcp_destroy_sock() siempre se llama en los sockets msk, incluso en los aceptados. Ya no es necesario eliminar temporalmente una referencia sk al clonar msk. Tenga en cuenta que esta confirmación depende de la principal: mptcp: refactorizar la inicialización pasiva del socket."}], "references": [{"url": "https://git.kernel.org/stable/c/2827f099b3fb9a59263c997400e9182f5d423e84", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/804cf487fb0031f3c74755b78d8663333f0ba636", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b6985b9b82954caa53f862d6059d06c0526254f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53131", "published_date": "2025-05-02T16:15:32.087", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nSUNRPC: Fix a server shutdown leak\n\nFix a race where kthread_stop() may prevent the threadfn from ever getting\ncalled.  If that happens the svc_rqst will not be cleaned up."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: SUNRPC: Se corrige una fuga de información al apagar el servidor. Se corrige una ejecución donde kthread_stop() podría impedir que se llame a threadfn. Si esto ocurre, svc_rqst no se limpiará."}], "references": [{"url": "https://git.kernel.org/stable/c/7a3720361068ab520aed4608bad31ea9a6cc7fe7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ca6705d9d609441d34f8b853e1e4a6369b3b171", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ad7e40ee157ba33950a4ccdc284334580da3638d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ce7dd61e004002bc1c48d1ca47c887f3f3cc7370", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f74b3286859463cd63cc9d4aeaabd8b0c640182a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
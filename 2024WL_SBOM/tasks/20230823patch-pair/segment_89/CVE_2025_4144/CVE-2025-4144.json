{"cve_id": "CVE-2025-4144", "published_date": "2025-05-01T01:15:54.267", "last_modified_date": "2025-05-12T19:39:31.053", "descriptions": [{"lang": "en", "value": "PKCE was implemented in the OAuth implementation in workers-oauth-provider that is part of  MCP framework https://github.com/cloudflare/workers-mcp . However, it was found that an attacker could cause the check to be skipped.\n\n\nFixed in:\n\n \n\n https://github.com/cloudflare/workers-oauth-provider/pull/27 https://github.com/cloudflare/workers-oauth-provider/pull/27 \n\n\nImpact: \n\nPKCE is a defense-in-depth mechanism against certain kinds of attacks and was an optional extension in OAuth 2.0 which became required in the OAuth 2.1 draft. (Note that the MCP specification requires OAuth 2.1.). This bug completely bypasses PKCE protection."}, {"lang": "es", "value": "PKCE se implementó en la implementación de OAuth en workers-oauth-provider, que forma parte del framework MCP (https://github.com/cloudflare/workers-mcp). Sin embargo, se detectó que un atacante podría omitir la comprobación. Corregido en: https://github.com/cloudflare/workers-oauth-provider/pull/27 https://github.com/cloudflare/workers-oauth-provider/pull/27 Impacto: PKCE es un mecanismo de defensa en profundidad contra ciertos tipos de ataques y era una extensión opcional en OAuth 2.0, que se convirtió en obligatoria en el borrador de OAuth 2.1. (Tenga en cuenta que la especificación MCP requiere OAuth 2.1). Este error ignora por completo la protección de PKCE."}], "references": [{"url": "https://github.com/cloudflare/workers-oauth-provider/pull/27", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}]}
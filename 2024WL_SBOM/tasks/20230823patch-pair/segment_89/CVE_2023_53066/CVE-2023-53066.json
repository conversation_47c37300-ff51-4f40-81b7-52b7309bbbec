{"cve_id": "CVE-2023-53066", "published_date": "2025-05-02T16:15:25.673", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nqed/qed_sriov: guard against NULL derefs from qed_iov_get_vf_info\n\nWe have to make sure that the info returned by the helper is valid\nbefore using it.\n\nFound by Linux Verification Center (linuxtesting.org) with the SVACE\nstatic analysis tool."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: qed/qed_sriov: protección contra desreferencias nulas de qed_iov_get_vf_info. Debemos asegurarnos de que la información devuelta por el ayudante sea válida antes de usarla. Encontrada por el Centro de Verificación de Linux (linuxtesting.org) con la herramienta de análisis estático SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/25143b6a01d0cc5319edd3de22ffa2578b045550", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39c3b9dd481c3afce9439b29bafe00444cb4406b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42d72c6d1edc9dc09a5d6f6695d257fa9e9cc270", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7742c08e012eb65405e8304d100641638c5ff882", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7bd0037822fd04da13721f77a42ee5a077d4c5fb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/97ea704f39b5ded96f071e98701aa543f6f89683", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b224b0cab3a66e93d414825065a2e667a1d28c32", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e42d3bde4ec03c863259878dddaef5c351cca7ad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
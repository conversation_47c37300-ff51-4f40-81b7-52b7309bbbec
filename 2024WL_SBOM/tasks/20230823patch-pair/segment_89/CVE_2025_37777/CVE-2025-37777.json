{"cve_id": "CVE-2025-37777", "published_date": "2025-05-01T14:15:41.493", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix use-after-free in __smb2_lease_break_noti()\n\nMove tcp_transport free to ksmbd_conn_free. If ksmbd connection is\nreferenced when ksmbd server thread terminates, It will not be freed,\nbut conn->tcp_transport is freed. __smb2_lease_break_noti can be performed\nasynchronously when the connection is disconnected. __smb2_lease_break_noti\ncalls ksmbd_conn_write, which can cause use-after-free\nwhen conn->ksmbd_transport is already freed."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: se corrige el error \"use-after-free\" en __smb2_lease_break_noti(). Se mueve tcp_transport libre a ksmbd_conn_free. Si se hace referencia a la conexión ksmbd al finalizar el subproceso del servidor ksmbd, no se liberará, pero sí se liberará conn-&gt;tcp_transport. __smb2_lease_break_noti puede ejecutarse asincrónicamente cuando se desconecta la conexión. __smb2_lease_break_noti llama a ksmbd_conn_write, lo que puede causar un error \"use-after-free\" cuando conn-&gt;ksmbd_transport ya está liberado."}], "references": [{"url": "https://git.kernel.org/stable/c/1aec4d14cf81b7b3e7b69eb1cfa94144eed7138e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/21a4e47578d44c6b37c4fc4aba8ed7cc8dbb13de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e59796fc80603bcd8569d4d2e10b213c1918edb4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
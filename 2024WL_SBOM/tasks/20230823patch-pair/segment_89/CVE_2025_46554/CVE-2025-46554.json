{"cve_id": "CVE-2025-46554", "published_date": "2025-04-30T19:15:55.630", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "XWiki is a generic wiki platform. In versions starting from 1.8.1 to before 14.10.22, from 15.0-rc-1 to before 15.10.12, from 16.0.0-rc-1 to before 16.4.3, and from 16.5.0-rc-1 to before 16.7.0, anyone can access the metadata of any attachment in the wiki using the wiki attachment REST endpoint. There is no filtering for the results depending on current user rights, meaning an unauthenticated user could exploit this even in a private wiki. This issue has been patched in versions 14.10.22, 15.10.12, 16.4.3, and 16.7.0."}, {"lang": "es", "value": "XWiki es una plataforma wiki genérica. En las versiones 1.8.1 y anteriores a la 14.10.22, 15.0-rc-1 y anteriores a la 15.10.12, 16.0.0-rc-1 y anteriores a la 16.4.3, y 16.5.0-rc-1 y anteriores a la 16.7.0, cualquier persona puede acceder a los metadatos de cualquier adjunto en la wiki mediante el endpoint de acceso REST de adjuntos de la wiki. No se filtran los resultados según los permisos de usuario, lo que significa que un usuario no autenticado podría explotar esta vulnerabilidad incluso en una wiki privada. Este problema se ha corregido en las versiones 14.10.22, 15.10.12, 16.4.3 y 16.7.0."}], "references": [{"url": "https://github.com/xwiki/xwiki-platform/commit/37ecea84fdd053c33733c2ae9a0778bf98eae608", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/xwiki/xwiki-platform/commit/a43e933ddeda17dad1772396e1757998260e9342", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/xwiki/xwiki-platform/commit/c02ce7843a39851865b9d7b6132e32fdd21e3856", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/xwiki/xwiki-platform/security/advisories/GHSA-r5cr-xm48-97xp", "source": "<EMAIL>", "tags": []}, {"url": "https://jira.xwiki.org/browse/XWIKI-22424", "source": "<EMAIL>", "tags": []}]}
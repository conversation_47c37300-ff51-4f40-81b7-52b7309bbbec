{"cve_id": "CVE-2022-49893", "published_date": "2025-05-01T15:16:14.317", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncxl/region: Fix cxl_region leak, cleanup targets at region delete\n\nWhen a region is deleted any targets that have been previously assigned\nto that region hold references to it. Trigger those references to\ndrop by detaching all targets at unregister_region() time.\n\nOtherwise that region object will leak as userspace has lost the ability\nto detach targets once region sysfs is torn down."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cxl/region: Se corrige la fuga de cxl_region y se limpian los objetivos al eliminar una región. Al eliminar una región, todos los objetivos previamente asignados a ella contienen referencias a ella. Para eliminar esas referencias, desvincula todos los objetivos durante la ejecución de unregister_region(). De lo contrario, el objeto de región se filtrará, ya que el espacio de usuario ha perdido la capacidad de desvincular objetivos una vez que se desmantela el sistema operativo de la región."}], "references": [{"url": "https://git.kernel.org/stable/c/0d9e734018d70cecf79e2e4c6082167160a0f13f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/45d9fb4b758b9d602ee7776eb6754b0349946aad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
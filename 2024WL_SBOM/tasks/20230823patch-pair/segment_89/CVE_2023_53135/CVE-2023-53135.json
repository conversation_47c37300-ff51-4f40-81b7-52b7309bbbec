{"cve_id": "CVE-2023-53135", "published_date": "2025-05-02T16:15:32.447", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv: Use READ_ONCE_NOCHECK in imprecise unwinding stack mode\n\nWhen CONFIG_FRAME_POINTER is unset, the stack unwinding function\nwalk_stackframe randomly reads the stack and then, when KASAN is enabled,\nit can lead to the following backtrace:\n\n[    0.000000] ==================================================================\n[    0.000000] BUG: KASAN: stack-out-of-bounds in walk_stackframe+0xa6/0x11a\n[    0.000000] Read of size 8 at addr ffffffff81807c40 by task swapper/0\n[    0.000000]\n[    0.000000] CPU: 0 PID: 0 Comm: swapper Not tainted 6.2.0-12919-g24203e6db61f #43\n[    0.000000] Hardware name: riscv-virtio,qemu (DT)\n[    0.000000] Call Trace:\n[    0.000000] [<ffffffff80007ba8>] walk_stackframe+0x0/0x11a\n[    0.000000] [<ffffffff80099ecc>] init_param_lock+0x26/0x2a\n[    0.000000] [<ffffffff80007c4a>] walk_stackframe+0xa2/0x11a\n[    0.000000] [<ffffffff80c49c80>] dump_stack_lvl+0x22/0x36\n[    0.000000] [<ffffffff80c3783e>] print_report+0x198/0x4a8\n[    0.000000] [<ffffffff80099ecc>] init_param_lock+0x26/0x2a\n[    0.000000] [<ffffffff80007c4a>] walk_stackframe+0xa2/0x11a\n[    0.000000] [<ffffffff8015f68a>] kasan_report+0x9a/0xc8\n[    0.000000] [<ffffffff80007c4a>] walk_stackframe+0xa2/0x11a\n[    0.000000] [<ffffffff80007c4a>] walk_stackframe+0xa2/0x11a\n[    0.000000] [<ffffffff8006e99c>] desc_make_final+0x80/0x84\n[    0.000000] [<ffffffff8009a04e>] stack_trace_save+0x88/0xa6\n[    0.000000] [<ffffffff80099fc2>] filter_irq_stacks+0x72/0x76\n[    0.000000] [<ffffffff8006b95e>] devkmsg_read+0x32a/0x32e\n[    0.000000] [<ffffffff8015ec16>] kasan_save_stack+0x28/0x52\n[    0.000000] [<ffffffff8006e998>] desc_make_final+0x7c/0x84\n[    0.000000] [<ffffffff8009a04a>] stack_trace_save+0x84/0xa6\n[    0.000000] [<ffffffff8015ec52>] kasan_set_track+0x12/0x20\n[    0.000000] [<ffffffff8015f22e>] __kasan_slab_alloc+0x58/0x5e\n[    0.000000] [<ffffffff8015e7ea>] __kmem_cache_create+0x21e/0x39a\n[    0.000000] [<ffffffff80e133ac>] create_boot_cache+0x70/0x9c\n[    0.000000] [<ffffffff80e17ab2>] kmem_cache_init+0x6c/0x11e\n[    0.000000] [<ffffffff80e00fd6>] mm_init+0xd8/0xfe\n[    0.000000] [<ffffffff80e011d8>] start_kernel+0x190/0x3ca\n[    0.000000]\n[    0.000000] The buggy address belongs to stack of task swapper/0\n[    0.000000]  and is located at offset 0 in frame:\n[    0.000000]  stack_trace_save+0x0/0xa6\n[    0.000000]\n[    0.000000] This frame has 1 object:\n[    0.000000]  [32, 56) 'c'\n[    0.000000]\n[    0.000000] The buggy address belongs to the physical page:\n[    0.000000] page:(____ptrval____) refcount:1 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x81a07\n[    0.000000] flags: 0x1000(reserved|zone=0)\n[    0.000000] raw: 0000000000001000 ff600003f1e3d150 ff600003f1e3d150 0000000000000000\n[    0.000000] raw: 0000000000000000 0000000000000000 00000001ffffffff\n[    0.000000] page dumped because: kasan: bad access detected\n[    0.000000]\n[    0.000000] Memory state around the buggy address:\n[    0.000000]  ffffffff81807b00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n[    0.000000]  ffffffff81807b80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n[    0.000000] >ffffffff81807c00: 00 00 00 00 00 00 00 00 f1 f1 f1 f1 00 00 00 f3\n[    0.000000]                                            ^\n[    0.000000]  ffffffff81807c80: f3 f3 f3 f3 00 00 00 00 00 00 00 00 00 00 00 00\n[    0.000000]  ffffffff81807d00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\n[    0.000000] ==================================================================\n\nFix that by using READ_ONCE_NOCHECK when reading the stack in imprecise\nmode."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv: Usar READ_ONCE_NOCHECK en modo de desenrollado de pila impreciso Cuando no se establece CONFIG_FRAME_POINTER, la función de desenrollado de pila walk_stackframe lee la pila aleatoriamente y luego, cuando KASAN está habilitado, puede llevar al siguiente backtrace: [ 0.000000] ===================================================================== [ 0.000000] ERROR: KASAN: pila fuera de los límites en walk_stackframe+0xa6/0x11a [ 0.000000] Lectura de tamaño 8 en la dirección ffffffff81807c40 por tarea swapper/0 [ 0.000000] [ 0.000000] CPU: 0 PID: 0 Comm: swapper No contaminado 6.2.0-12919-g24203e6db61f #43 [ 0.000000] Nombre del hardware: riscv-virtio,qemu (DT) [ 0.000000] Rastreo de llamadas: [ 0.000000] [] walk_stackframe+0x0/0x11a [ 0.000000] [] init_param_lock+0x26/0x2a [ 0.000000] [] walk_stackframe+0xa2/0x11a [ 0.000000] [] dump_stack_lvl+0x22/0x36 [ 0.000000] [] print_report+0x198/0x4a8 [ 0.000000] [] init_param_lock+0x26/0x2a [ 0.000000] [] walk_stackframe+0xa2/0x11a [ 0.000000] [] kasan_report+0x9a/0xc8 [ 0.000000] [] walk_stackframe+0xa2/0x11a [ 0.000000] [] walk_stackframe+0xa2/0x11a [ 0.000000] [] desc_make_final+0x80/0x84 [ 0.000000] [] stack_trace_save+0x88/0xa6 [ 0.000000] [] filter_irq_stacks+0x72/0x76 [ 0.000000] [] devkmsg_read+0x32a/0x32e [ 0.000000] [] kasan_save_stack+0x28/0x52 [ 0.000000] [] desc_make_final+0x7c/0x84 [ 0.000000] [] stack_trace_save+0x84/0xa6 [ 0.000000] [] kasan_set_track+0x12/0x20 [ 0.000000] [] __kasan_slab_alloc+0x58/0x5e [ 0.000000] [] __kmem_cache_create+0x21e/0x39a [ 0.000000] [] create_boot_cache+0x70/0x9c [ 0.000000] [] kmem_cache_init+0x6c/0x11e [ 0.000000] [] mm_init+0xd8/0xfe [ 0.000000] [] start_kernel+0x190/0x3ca [ 0.000000] [ 0.000000] The buggy address belongs to stack of task swapper/0 [ 0.000000] and is located at offset 0 in frame: [ 0.000000] stack_trace_save+0x0/0xa6 [ 0.000000] [ 0.000000] This frame has 1 object: [ 0.000000] [32, 56) 'c' [ 0.000000] [ 0.000000] The buggy address belongs to the physical page: [ 0.000000] page:(____ptrval____) refcount:1 mapcount:0 mapping:0000000000000000 index:0x0 pfn:0x81a07 [ 0.000000] flags: 0x1000(reserved|zone=0) [ 0.000000] raw: 0000000000001000 ff600003f1e3d150 ff600003f1e3d150 0000000000000000 [ 0.000000] raw: 0000000000000000 0000000000000000 00000001ffffffff [ 0.000000] page dumped because: kasan: bad access detected [ 0.000000] [ 0.000000] Memory state around the buggy address: [ 0.000000] ffffffff81807b00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 [ 0.000000] ffffffff81807b80: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 [ 0.000000] &gt;ffffffff81807c00: 00 00 00 00 00 00 00 00 f1 f1 f1 f1 00 00 00 f3 [ 0.000000] ^ [ 0.000000] ffffffff81807c80: f3 f3 f3 f3 00 00 00 00 00 00 00 00 00 00 00 00 [ 0.000000] ffffffff81807d00: 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 [ 0.000000] ================================================================== Solucione esto usando READ_ONCE_NOCHECK al leer la pila en modo impreciso."}], "references": [{"url": "https://git.kernel.org/stable/c/17fa90ffba20743c946920fbb0afe160d0ead8c9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/324912d6c0c4006711054d389faa2239c1655e1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3a9418d2c93c1c86ce4d0595112d91c7a8e70c2c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3de277af481ab931fab9e295ad8762692920732a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76950340cf03b149412fe0d5f0810e52ac1df8cb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a99a61d9e1bfca2fc37d223a6a185c0eb66aba02", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
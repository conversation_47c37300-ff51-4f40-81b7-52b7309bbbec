{"cve_id": "CVE-2022-49927", "published_date": "2025-05-01T15:16:18.657", "last_modified_date": "2025-05-07T13:28:39.750", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnfs4: Fix kmemleak when allocate slot failed\n\nIf one of the slot allocate failed, should cleanup all the other\nallocated slots, otherwise, the allocated slots will leak:\n\n  unreferenced object 0xffff8881115aa100 (size 64):\n    comm \"\"mount.nfs\"\", pid 679, jiffies 4294744957 (age 115.037s)\n    hex dump (first 32 bytes):\n      00 cc 19 73 81 88 ff ff 00 a0 5a 11 81 88 ff ff  ...s......Z.....\n      00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n    backtrace:\n      [<000000007a4c434a>] nfs4_find_or_create_slot+0x8e/0x130\n      [<000000005472a39c>] nfs4_realloc_slot_table+0x23f/0x270\n      [<00000000cd8ca0eb>] nfs40_init_client+0x4a/0x90\n      [<00000000128486db>] nfs4_init_client+0xce/0x270\n      [<000000008d2cacad>] nfs4_set_client+0x1a2/0x2b0\n      [<000000000e593b52>] nfs4_create_server+0x300/0x5f0\n      [<00000000e4425dd2>] nfs4_try_get_tree+0x65/0x110\n      [<00000000d3a6176f>] vfs_get_tree+0x41/0xf0\n      [<0000000016b5ad4c>] path_mount+0x9b3/0xdd0\n      [<00000000494cae71>] __x64_sys_mount+0x190/0x1d0\n      [<000000005d56bdec>] do_syscall_64+0x35/0x80\n      [<00000000687c9ae4>] entry_SYSCALL_64_after_hwframe+0x46/0xb0"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: nfs4: Se corrige kmemleak cuando falla la asignación de ranura Si falla la asignación de una ranura, se deben limpiar todas las demás ranuras asignadas, de lo contrario, las ranuras asignadas tendrán fugas: objeto sin referencia 0xffff8881115aa100 (tamaño 64): comm \"\"mount.nfs\"\", pid 679, jiffies 4294744957 (edad 115.037s) volcado hexadecimal (primeros 32 bytes): 00 cc 19 73 81 88 ff ff 00 a0 5a 11 81 88 ff ff ...s......Z..... 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ seguimiento inverso:[&lt;000000007a4c434a&gt;] nfs4_find_or_create_slot+0x8e/0x130 [&lt;000000005472a39c&gt;] nfs4_realloc_slot_table+0x23f/0x270 [&lt;00000000cd8ca0eb&gt;] nfs40_init_client+0x4a/0x90 [&lt;00000000128486db&gt;] nfs4_init_client+0xce/0x270 [&lt;000000008d2cacad&gt;] nfs4_set_client+0x1a2/0x2b0 [&lt;000000000e593b52&gt;] nfs4_create_server+0x300/0x5f0 [&lt;00000000e4425dd2&gt;] nfs4_try_get_tree+0x65/0x110 [&lt;00000000d3a6176f&gt;] vfs_get_tree+0x41/0xf0 [&lt;0000000016b5ad4c&gt;] path_mount+0x9b3/0xdd0 [&lt;00000000494cae71&gt;] __x64_sys_mount+0x190/0x1d0 [&lt;000000005d56bdec&gt;] do_syscall_64+0x35/0x80 [&lt;00000000687c9ae4&gt;] entry_SYSCALL_64_after_hwframe+0x46/0xb0 "}], "references": [{"url": "https://git.kernel.org/stable/c/24641993a7dce6b1628645f4e1d97ca06c9f765d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/45aea4fbf61e205649c29200726b9f45c1718a67", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7e8436728e22181c3f12a5dbabd35ed3a8b8c593", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/84b5cb476903003ae9ca88f32b57ff0eaefa6d4c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/86ce0e93cf6fb4d0c447323ac66577c642628b9d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/925cb538bd5851154602818dc80bf4b4d924c127", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/aae35a0c8a775fa4afa6a4e7dab3f936f1f89bbb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/db333ae981fb8843c383aa7dbf62cc682597d401", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-37759", "published_date": "2025-05-01T13:15:54.690", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nublk: fix handling recovery & reissue in ublk_abort_queue()\n\nCommit 8284066946e6 (\"ublk: grab request reference when the request is handled\nby userspace\") doesn't grab request reference in case of recovery reissue.\nThen the request can be requeued & re-dispatch & failed when canceling\nuring command.\n\nIf it is one zc request, the request can be freed before io_uring\nreturns the zc buffer back, then cause kernel panic:\n\n[  126.773061] BUG: kernel NULL pointer dereference, address: 00000000000000c8\n[  126.773657] #PF: supervisor read access in kernel mode\n[  126.774052] #PF: error_code(0x0000) - not-present page\n[  126.774455] PGD 0 P4D 0\n[  126.774698] Oops: Oops: 0000 [#1] SMP NOPTI\n[  126.775034] CPU: 13 UID: 0 PID: 1612 Comm: kworker/u64:55 Not tainted 6.14.0_blk+ #182 PREEMPT(full)\n[  126.775676] Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-1.fc39 04/01/2014\n[  126.776275] Workqueue: iou_exit io_ring_exit_work\n[  126.776651] RIP: 0010:ublk_io_release+0x14/0x130 [ublk_drv]\n\nFixes it by always grabbing request reference for aborting the request."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ublk: corrección del manejo de la recuperación y la reemisión en ublk_abort_queue(). La confirmación 8284066946e6 (\"ublk: toma la referencia de la solicitud cuando la solicitud es manejada por el espacio de usuario\") no toma la referencia de la solicitud en caso de reemisión de recuperación. En ese caso, la solicitud se puede volver a poner en cola y reenviar, y falla al cancelar el comando \"uring\". Si es una solicitud zc, la solicitud se puede liberar antes de que io_uring devuelva el buffer zc, y luego cause pánico en el kernel: [ 126.773061] ERROR: desreferencia de puntero NULL del kernel, dirección: 00000000000000c8 [ 126.773657] #PF: acceso de lectura del supervisor en modo kernel [ 126.774052] #PF: error_code(0x0000) - página no presente [ 126.774455] PGD 0 P4D 0 [ 126.774698] Oops: Oops: 0000 [#1] SMP NOPTI [ 126.775034] CPU: 13 UID: 0 PID: 1612 Comm: kworker/u64:55 No contaminado 6.14.0_blk+ #182 PREEMPT(full) [ 126.775676] Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-1.fc39 01/04/2014 [ 126.776275] Cola de trabajo: iou_exit io_ring_exit_work [ 126.776651] RIP: 0010:ublk_io_release+0x14/0x130 [ublk_drv] Lo corrige tomando siempre la referencia de la solicitud para abortar la solicitud."}], "references": [{"url": "https://git.kernel.org/stable/c/0a21d259ca4d6310fdfcc0284ebbc000e66cbf70", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5d34a30efac9c9c93e150130caa940c0df6053c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ee6bd5d4fce502a5b5a2ea805e9ff16e6aa890f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/caa5c8a2358604f38bf0a4afaa5eacda13763067", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
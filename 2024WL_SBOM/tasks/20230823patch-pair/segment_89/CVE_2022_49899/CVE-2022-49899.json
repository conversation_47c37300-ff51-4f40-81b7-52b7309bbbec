{"cve_id": "CVE-2022-49899", "published_date": "2025-05-01T15:16:14.953", "last_modified_date": "2025-05-07T13:19:06.700", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfscrypt: stop using keyrings subsystem for fscrypt_master_key\n\nThe approach of fs/crypto/ internally managing the fscrypt_master_key\nstructs as the payloads of \"struct key\" objects contained in a\n\"struct key\" keyring has outlived its usefulness.  The original idea was\nto simplify the code by reusing code from the keyrings subsystem.\nHowever, several issues have arisen that can't easily be resolved:\n\n- When a master key struct is destroyed, blk_crypto_evict_key() must be\n  called on any per-mode keys embedded in it.  (This started being the\n  case when inline encryption support was added.)  Yet, the keyrings\n  subsystem can arbitrarily delay the destruction of keys, even past the\n  time the filesystem was unmounted.  Therefore, currently there is no\n  easy way to call blk_crypto_evict_key() when a master key is\n  destroyed.  Currently, this is worked around by holding an extra\n  reference to the filesystem's request_queue(s).  But it was overlooked\n  that the request_queue reference is *not* guaranteed to pin the\n  corresponding blk_crypto_profile too; for device-mapper devices that\n  support inline crypto, it doesn't.  This can cause a use-after-free.\n\n- When the last inode that was using an incompletely-removed master key\n  is evicted, the master key removal is completed by removing the key\n  struct from the keyring.  Currently this is done via key_invalidate().\n  Yet, key_invalidate() takes the key semaphore.  This can deadlock when\n  called from the shrinker, since in fscrypt_ioctl_add_key(), memory is\n  allocated with GFP_KERNEL under the same semaphore.\n\n- More generally, the fact that the keyrings subsystem can arbitrarily\n  delay the destruction of keys (via garbage collection delay, or via\n  random processes getting temporary key references) is undesirable, as\n  it means we can't strictly guarantee that all secrets are ever wiped.\n\n- Doing the master key lookups via the keyrings subsystem results in the\n  key_permission LSM hook being called.  fscrypt doesn't want this, as\n  all access control for encrypted files is designed to happen via the\n  files themselves, like any other files.  The workaround which SELinux\n  users are using is to change their SELinux policy to grant key search\n  access to all domains.  This works, but it is an odd extra step that\n  shouldn't really have to be done.\n\nThe fix for all these issues is to change the implementation to what I\nshould have done originally: don't use the keyrings subsystem to keep\ntrack of the filesystem's fscrypt_master_key structs.  Instead, just\nstore them in a regular kernel data structure, and rework the reference\ncounting, locking, and lifetime accordingly.  Retain support for\nRCU-mode key lookups by using a hash table.  Replace fscrypt_sb_free()\nwith fscrypt_sb_delete(), which releases the keys synchronously and runs\na bit earlier during unmount, so that block devices are still available.\n\nA side effect of this patch is that neither the master keys themselves\nnor the filesystem keyrings will be listed in /proc/keys anymore.\n(\"Master key users\" and the master key users keyrings will still be\nlisted.)  However, this was mostly an implementation detail, and it was\nintended just for debugging purposes.  I don't know of anyone using it.\n\nThis patch does *not* change how \"master key users\" (->mk_users) works;\nthat still uses the keyrings subsystem.  That is still needed for key\nquotas, and changing that isn't necessary to solve the issues listed\nabove.  If we decide to change that too, it would be a separate patch.\n\nI've marked this as fixing the original commit that added the fscrypt\nkeyring, but as noted above the most important issue that this patch\nfixes wasn't introduced until the addition of inline encryption support."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: fscrypt: dejar de usar el subsistema de llaveros para fscrypt_master_key. El enfoque de fs/crypto/, que gestionaba internamente las estructuras fscrypt_master_key como payloads de objetos \"struct key\" contenidos en un llavero \"struct key\", ha dejado de ser útil. La idea original era simplificar el código reutilizando código del subsistema de llaveros. Sin embargo, han surgido varios problemas que no se pueden resolver fácilmente: - Cuando se destruye una estructura de clave maestra, se debe llamar a blk_crypto_evict_key() en cualquier clave por modo incrustada en ella. (Esto empezó a ocurrir cuando se añadió la compatibilidad con el cifrado en línea). Sin embargo, el subsistema de llaveros puede retrasar arbitrariamente la destrucción de claves, incluso después de que se desmontara el sistema de archivos. <PERSON><PERSON> lo tanto, actualmente no hay una forma sencilla de llamar a blk_crypto_evict_key() cuando se destruye una clave maestra. Actualmente, esto se soluciona manteniendo una referencia adicional a las colas de solicitudes (solicitudes) del sistema de archivos. Sin embargo, se pasó por alto que la referencia a la cola de solicitudes *no* garantiza que también fije el perfil blk_crypto_profile correspondiente; para los dispositivos con mapeador de dispositivos que admiten criptografía en línea, no lo hace. Esto puede causar un uso después de la liberación. - Cuando se expulsa el último inodo que usaba una clave maestra eliminada de forma incompleta, la eliminación de la clave maestra se completa eliminando la estructura de la clave del anillo de claves. Actualmente, esto se realiza mediante key_invalidate(). Sin embargo, key_invalidate() toma el semáforo de la clave. Esto puede generar un bloqueo al ser llamado desde el reductor, ya que en fscrypt_ioctl_add_key(), la memoria se asigna con GFP_KERNEL bajo el mismo semáforo. En términos más generales, el hecho de que el subsistema de llaveros pueda retrasar arbitrariamente la destrucción de claves (mediante un retraso en la recolección de basura o mediante procesos aleatorios que obtienen referencias temporales a las claves) es indeseable, ya que significa que no podemos garantizar estrictamente que todos los secretos se borren. Realizar las búsquedas de la clave maestra a través del subsistema de llaveros resulta en la llamada al gancho LSM key_permission. fscrypt no desea esto, ya que todo el control de acceso a los archivos cifrados está diseñado para realizarse a través de los propios archivos, como cualquier otro archivo. La solución alternativa que utilizan los usuarios de SELinux es cambiar su política de SELinux para otorgar acceso de búsqueda de claves a todos los dominios. Esto funciona, pero es un paso adicional extraño que realmente no debería ser necesario. La solución para todos estos problemas es cambiar la implementación a lo que debería haber hecho originalmente: no usar el subsistema de llaveros para realizar un seguimiento de las estructuras fscrypt_master_key del sistema de archivos. En su lugar, simplemente almacénelos en una estructura de datos de kernel normal y modifique el recuento de referencias, el bloqueo y la duración según corresponda. Mantenga la compatibilidad con las búsquedas de claves en modo RCU mediante una tabla hash. Reemplace fscrypt_sb_free() por fscrypt_sb_delete(), que libera las claves sincrónicamente y se ejecuta un poco antes durante el desmontaje, para que los dispositivos de bloque sigan disponibles. Un efecto secundario de este parche es que ni las claves maestras ni los conjuntos de claves del sistema de archivos aparecerán en /proc/keys. (Los \"usuarios de clave maestra\" y los conjuntos de claves de los usuarios de clave maestra seguirán apareciendo). Sin embargo, esto era principalmente un detalle de implementación y estaba destinado únicamente a fines de depuración. No conozco a nadie que lo use. Este parche *no* cambia el funcionamiento de los \"usuarios de clave maestra\" (-&gt;mk_users)--- truncado ----"}], "references": [{"url": "https://git.kernel.org/stable/c/391cceee6d435e616f68631e68f5b32d480b1e67", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/68d15d6558a386f46d815a6ac39edecad713a1bf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d7e7b9af104c7b389a0c21eb26532511bce4b510", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e6f4fd85ef1ee6ab356bfbd64df28c1cb73aee7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
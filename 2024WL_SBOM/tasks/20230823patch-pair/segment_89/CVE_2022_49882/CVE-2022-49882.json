{"cve_id": "CVE-2022-49882", "published_date": "2025-05-01T15:16:13.183", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: Reject attempts to consume or refresh inactive gfn_to_pfn_cache\n\nReject kvm_gpc_check() and kvm_gpc_refresh() if the cache is inactive.\nNot checking the active flag during refresh is particularly egregious, as\nKVM can end up with a valid, inactive cache, which can lead to a variety\nof use-after-free bugs, e.g. consuming a NULL kernel pointer or missing\nan mmu_notifier invalidation due to the cache not being on the list of\ngfns to invalidate.\n\nNote, \"active\" needs to be set if and only if the cache is on the list\nof caches, i.e. is reachable via mmu_notifier events.  If a relevant\nmmu_notifier event occurs while the cache is \"active\" but not on the\nlist, KVM will not acquire the cache's lock and so will not serailize\nthe mmu_notifier event with active users and/or kvm_gpc_refresh().\n\nA race between KVM_XEN_ATTR_TYPE_SHARED_INFO and KVM_XEN_HVM_EVTCHN_SEND\ncan be exploited to trigger the bug.\n\n1. Deactivate shinfo cache:\n\nkvm_xen_hvm_set_attr\ncase KVM_XEN_ATTR_TYPE_SHARED_INFO\n kvm_gpc_deactivate\n  kvm_gpc_unmap\n   gpc->valid = false\n   gpc->khva = NULL\n  gpc->active = false\n\nResult: active = false, valid = false\n\n2. Cause cache refresh:\n\nkvm_arch_vm_ioctl\ncase KVM_XEN_HVM_EVTCHN_SEND\n kvm_xen_hvm_evtchn_send\n  kvm_xen_set_evtchn\n   kvm_xen_set_evtchn_fast\n    kvm_gpc_check\n    return -EWOULDBLOCK because !gpc->valid\n   kvm_xen_set_evtchn_fast\n    return -EWOULDBLOCK\n   kvm_gpc_refresh\n    hva_to_pfn_retry\n     gpc->valid = true\n     gpc->khva = not NULL\n\nResult: active = false, valid = true\n\n3. Race ioctl KVM_XEN_HVM_EVTCHN_SEND against ioctl\nKVM_XEN_ATTR_TYPE_SHARED_INFO:\n\nkvm_arch_vm_ioctl\ncase KVM_XEN_HVM_EVTCHN_SEND\n kvm_xen_hvm_evtchn_send\n  kvm_xen_set_evtchn\n   kvm_xen_set_evtchn_fast\n    read_lock gpc->lock\n                                          kvm_xen_hvm_set_attr case\n                                          KVM_XEN_ATTR_TYPE_SHARED_INFO\n                                           mutex_lock kvm->lock\n                                           kvm_xen_shared_info_init\n                                            kvm_gpc_activate\n                                             gpc->khva = NULL\n    kvm_gpc_check\n     [ Check passes because gpc->valid is\n       still true, even though gpc->khva\n       is already NULL. ]\n    shinfo = gpc->khva\n    pending_bits = shinfo->evtchn_pending\n    CRASH: test_and_set_bit(..., pending_bits)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: <PERSON><PERSON><PERSON> de consumir o actualizar gfn_to_pfn_cache inactivo. Rechazar kvm_gpc_check() y kvm_gpc_refresh() si la caché está inactiva. No verificar el indicador de activo durante la actualización es particularmente grave, ya que KVM puede terminar con una caché válida inactiva, lo que puede provocar diversos errores de use-after-free, como consumir un puntero de kernel nulo o perder una invalidación de mmu_notifier debido a que la caché no está en la lista de gfns para invalidar. Tenga en cuenta que \"active\" debe establecerse solo si la caché está en la lista de cachés, es decir, es accesible mediante eventos mmu_notifier. Si se produce un evento mmu_notifier relevante mientras la caché está activa, pero no está en la lista, KVM no adquirirá el bloqueo de la caché y, por lo tanto, no serializará el evento mmu_notifier con usuarios activos ni con kvm_gpc_refresh(). Una competencia entre KVM_XEN_ATTR_TYPE_SHARED_INFO y KVM_XEN_HVM_EVTCHN_SEND puede explotarse para activar el error. 1. Desactivar caché shinfo: kvm_xen_hvm_set_attr caso KVM_XEN_ATTR_TYPE_SHARED_INFO kvm_gpc_deactivate kvm_gpc_unmap gpc-&gt;valid = falso gpc-&gt;khva = NULL gpc-&gt;active = falso Resultado: activo = falso, válido = falso 2. Causar actualización de caché: kvm_arch_vm_ioctl caso KVM_XEN_HVM_EVTCHN_SEND kvm_xen_hvm_evtchn_send kvm_xen_set_evtchn kvm_xen_set_evtchn_fast kvm_gpc_check devolver -EWOULDBLOCK porque !gpc-&gt;valid kvm_xen_set_evtchn_fast devolver -EWOULDBLOCK kvm_gpc_refresh hva_to_pfn_retry gpc-&gt;valid = verdadero gpc-&gt;khva = no NULL Resultado: activo = falso, válido = verdadero 3. Competencia ioctl KVM_XEN_HVM_EVTCHN_SEND contra ioctl KVM_XEN_ATTR_TYPE_SHARED_INFO: kvm_arch_vm_ioctl caso KVM_XEN_HVM_EVTCHN_SEND kvm_xen_hvm_evtchn_send kvm_xen_set_evtchn kvm_xen_set_evtchn_fast read_lock gpc-&gt;lock kvm_xen_hvm_set_attr caso KVM_XEN_ATTR_TYPE_SHARED_INFO mutex_lock kvm-&gt;lock kvm_xen_shared_info_init kvm_gpc_activate gpc-&gt;khva = NULL kvm_gpc_check [ La comprobación pasa porque gpc-&gt;valid sigue siendo cierto, aunque gpc-&gt;khva ya sea NULL. ] shinfo = gpc-&gt;khva pending_bits = shinfo-&gt;evtchn_pending CRASH: test_and_set_bit(..., pending_bits)"}], "references": [{"url": "https://git.kernel.org/stable/c/bfa9672f8fc9eb118124bab61899d2dd497f95ba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ecbcf030b45666ad11bc98565e71dfbcb7be4393", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
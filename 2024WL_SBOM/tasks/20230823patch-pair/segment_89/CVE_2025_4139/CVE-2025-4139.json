{"cve_id": "CVE-2025-4139", "published_date": "2025-04-30T21:15:55.003", "last_modified_date": "2025-06-23T15:13:27.283", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in Netgear EX6120 ********. Affected by this vulnerability is the function fwAcosCgiInbound. The manipulation of the argument host leads to buffer overflow. The attack can be launched remotely. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se detectó una vulnerabilidad crítica en Netgear EX6120 ********. Esta vulnerabilidad afecta a la función fwAcosCgiInbound. La manipulación del argumento \"host\" provoca un desbordamiento del búfer. El ataque puede ejecutarse remotamente. Se contactó al proveedor con antelación para informarle sobre esta vulnerabilidad, pero no respondió."}], "references": [{"url": "https://github.com/jylsec/vuldb/blob/main/Netgear/netgear_ex6120/Buffer_overflow-fwAcosCgiInbound-port_end/README.md", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://vuldb.com/?ctiid.306631", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306631", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560785", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.netgear.com/", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2025-25504", "published_date": "2025-05-05T16:15:50.640", "last_modified_date": "2025-06-17T14:13:16.263", "descriptions": [{"lang": "en", "value": "An issue in the /usr/local/bin/jncs.sh script of Gefen WebFWC (In AV over IP products) v1.85h, v1.86v, and v1.70 allows attackers with network access to connect to the device over TCP port 4444 without authentication and execute arbitrary commands with root privileges."}, {"lang": "es", "value": "Un problema en el script /usr/local/bin/jncs.sh de Gefen WebFWC (en productos AV sobre IP) v1.85h, v1.86v y v1.70 permite a atacantes con acceso a la red conectarse al dispositivo a través del puerto TCP 4444 sin autenticación y ejecutar comandos arbitrarios con privilegios de root."}], "references": [{"url": "http://gefen.com", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.troy-wilson.com/cve-2025-25504.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-4329", "published_date": "2025-05-06T07:15:49.013", "last_modified_date": "2025-06-12T17:24:19.030", "descriptions": [{"lang": "en", "value": "A vulnerability was found in 74CMS up to 3.33.0. It has been rated as problematic. Affected by this issue is the function index of the file /index.php/index/download/index. The manipulation of the argument url leads to path traversal. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en 74CMS hasta la versión 3.33.0. Se ha clasificado como problemática. Este problema afecta la función index del archivo /index.php/index/download/index. La manipulación del argumento url provoca un path traversal. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Q16G/cve_detail/blob/main/74cms/fileRead.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.307430", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307430", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564318", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
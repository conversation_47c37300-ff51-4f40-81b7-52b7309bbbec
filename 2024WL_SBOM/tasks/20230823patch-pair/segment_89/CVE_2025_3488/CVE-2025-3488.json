{"cve_id": "CVE-2025-3488", "published_date": "2025-05-02T06:15:48.707", "last_modified_date": "2025-05-06T13:42:26.630", "descriptions": [{"lang": "en", "value": "The WPML plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's wpml_language_switcher shortcode in versions 3.6.0 - 4.7.3 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WPML para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del shortcode wpml_language_switcher en las versiones 3.6.0 a 4.7.3, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wpml.org/category/changelog/", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://wpml.org/documentation/support/wpml-coding-api/shortcodes/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/92c1bd85-5f81-4bb6-b6af-6cda85b91b9e?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
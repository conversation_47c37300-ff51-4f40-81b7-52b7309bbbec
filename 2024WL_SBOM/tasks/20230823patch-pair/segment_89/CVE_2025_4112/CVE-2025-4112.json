{"cve_id": "CVE-2025-4112", "published_date": "2025-04-30T11:15:50.500", "last_modified_date": "2025-05-13T20:25:29.057", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Student Record System 3.20. It has been declared as critical. This vulnerability affects unknown code of the file /add-course.php. The manipulation of the argument course-short leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Student Record System 3.20. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /add-course.php. La manipulación del argumento course-short provoca una inyección SQL. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bleakTS/myCVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306592", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306592", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560701", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-27409", "published_date": "2025-04-30T15:16:01.067", "last_modified_date": "2025-05-16T16:29:40.663", "descriptions": [{"lang": "en", "value": "Joplin is a free, open source note taking and to-do application, which can handle a large number of notes organised into notebooks. Prior to version 3.3.3, path traversal is possible in Joplin Server if static file path starts with `css/pluginAssets` or `js/pluginAssets`. The `findLocalFile` function in the `default route` calls `localFileFromUrl` to check for special `pluginAssets` paths. If the function returns a path, the result is returned directly, without checking for path traversal. The vulnerability allows attackers to read files outside the intended directories. This issue has been patched in version 3.3.3."}, {"lang": "es", "value": "Jo<PERSON><PERSON> es una aplicación gratuita y de código abierto para tomar notas y gestionar tareas pendientes, capaz de gestionar un gran número de notas organizadas en cuadernos. Antes de la versión 3.3.3, era posible path traversal en Joplin Server si la ruta estática del archivo comenzaba por `css/pluginAssets` o `js/pluginAssets`. La función `findLocalFile` de la `ruta predeterminada` llama a `localFileFromUrl` para buscar rutas especiales de `pluginAssets`. Si la función devuelve una ruta, el resultado se devuelve directamente, sin comprobar si hay path traversal. Esta vulnerabilidad permite a los atacantes leer archivos fuera de los directorios previstos. Este problema se ha corregido en la versión 3.3.3."}], "references": [{"url": "https://github.com/laurent22/joplin/pull/11916", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/laurent22/joplin/security/advisories/GHSA-5xv6-7jm3-fmg5", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
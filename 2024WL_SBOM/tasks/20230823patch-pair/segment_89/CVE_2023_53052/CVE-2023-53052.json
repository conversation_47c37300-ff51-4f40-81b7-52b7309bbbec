{"cve_id": "CVE-2023-53052", "published_date": "2025-05-02T16:15:24.283", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncifs: fix use-after-free bug in refresh_cache_worker()\n\nThe UAF bug occurred because we were putting DFS root sessions in\ncifs_umount() while DFS cache refresher was being executed.\n\nMake DFS root sessions have same lifetime as DFS tcons so we can avoid\nthe use-after-free bug is DFS cache refresher and other places that\nrequire IPCs to get new DFS referrals on.  Also, get rid of mount\ngroup handling in DFS cache as we no longer need it.\n\nThis fixes below use-after-free bug catched by KASAN\n\n[ 379.946955] BUG: KASAN: use-after-free in __refresh_tcon.isra.0+0x10b/0xc10 [cifs]\n[ 379.947642] Read of size 8 at addr ffff888018f57030 by task kworker/u4:3/56\n[ 379.948096]\n[ 379.948208] CPU: 0 PID: 56 Comm: kworker/u4:3 Not tainted 6.2.0-rc7-lku #23\n[ 379.948661] Hardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS\nrel-1.16.0-0-gd239552-rebuilt.opensuse.org 04/01/2014\n[ 379.949368] Workqueue: cifs-dfscache refresh_cache_worker [cifs]\n[ 379.949942] Call Trace:\n[ 379.950113] <TASK>\n[ 379.950260] dump_stack_lvl+0x50/0x67\n[ 379.950510] print_report+0x16a/0x48e\n[ 379.950759] ? __virt_addr_valid+0xd8/0x160\n[ 379.951040] ? __phys_addr+0x41/0x80\n[ 379.951285] kasan_report+0xdb/0x110\n[ 379.951533] ? __refresh_tcon.isra.0+0x10b/0xc10 [cifs]\n[ 379.952056] ? __refresh_tcon.isra.0+0x10b/0xc10 [cifs]\n[ 379.952585] __refresh_tcon.isra.0+0x10b/0xc10 [cifs]\n[ 379.953096] ? __pfx___refresh_tcon.isra.0+0x10/0x10 [cifs]\n[ 379.953637] ? __pfx___mutex_lock+0x10/0x10\n[ 379.953915] ? lock_release+0xb6/0x720\n[ 379.954167] ? __pfx_lock_acquire+0x10/0x10\n[ 379.954443] ? refresh_cache_worker+0x34e/0x6d0 [cifs]\n[ 379.954960] ? __pfx_wb_workfn+0x10/0x10\n[ 379.955239] refresh_cache_worker+0x4ad/0x6d0 [cifs]\n[ 379.955755] ? __pfx_refresh_cache_worker+0x10/0x10 [cifs]\n[ 379.956323] ? __pfx_lock_acquired+0x10/0x10\n[ 379.956615] ? read_word_at_a_time+0xe/0x20\n[ 379.956898] ? lockdep_hardirqs_on_prepare+0x12/0x220\n[ 379.957235] process_one_work+0x535/0x990\n[ 379.957509] ? __pfx_process_one_work+0x10/0x10\n[ 379.957812] ? lock_acquired+0xb7/0x5f0\n[ 379.958069] ? __list_add_valid+0x37/0xd0\n[ 379.958341] ? __list_add_valid+0x37/0xd0\n[ 379.958611] worker_thread+0x8e/0x630\n[ 379.958861] ? __pfx_worker_thread+0x10/0x10\n[ 379.959148] kthread+0x17d/0x1b0\n[ 379.959369] ? __pfx_kthread+0x10/0x10\n[ 379.959630] ret_from_fork+0x2c/0x50\n[ 379.959879] </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cifs: se corrige el error de use-after-free en refresh_cache_worker(). El error de UAF se produjo porque se estaban asignando sesiones root de DFS en cifs_umount() mientras se ejecutaba el actualizador de caché DFS. Se ha establecido que las sesiones root de DFS tengan la misma duración que las tcons de DFS para evitar el error de use-after-free en el actualizador de caché DFS y en otros lugares que requieren que los IPC obtengan nuevas referencias DFS. Además, se ha eliminado la gestión de grupos de montaje en la caché DFS, ya que ya no es necesaria. Esto corrige el siguiente error de use-after-free detectado por KASAN [379.946955] ERROR: KASAN: use-after-free en __refresh_tcon.isra.0+0x10b/0xc10 [cifs] [379.947642] Lectura de tamaño 8 en la dirección ffff888018f57030 por la tarea kworker/u4:3/56 [379.948096] [379.948208] CPU: 0 PID: 56 Comm: kworker/u4:3 No contaminado 6.2.0-rc7-lku #23 [379.948661] Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS rel-1.16.0-0-gd239552-rebuilt.opensuse.org 01/04/2014 [ 379.949368] Cola de trabajo: cifs-dfscache refresh_cache_worker [cifs] [ 379.949942] Rastreo de llamadas: [ 379.950113]  [ 379.950260] dump_stack_lvl+0x50/0x67 [ 379.950510] print_report+0x16a/0x48e [ 379.950759] ? __virt_addr_valid+0xd8/0x160 [ 379.951040] ? __phys_addr+0x41/0x80 [379.951285] kasan_report+0xdb/0x110 [379.951533] ? __refresh_tcon.isra.0+0x10b/0xc10 [cifs] [379.952056] ? __refresh_tcon.isra.0+0x10b/0xc10 [cifs] [379.952585] __refresh_tcon.isra.0+0x10b/0xc10 [cifs] [379.953096] ? __pfx___refresh_tcon.isra.0+0x10/0x10 [cifs] [379.953637] ? __pfx___mutex_lock+0x10/0x10 [ 379.953915] ? lock_release+0xb6/0x720 [ 379.954167] ? __pfx_lock_acquire+0x10/0x10 [ 379.954443] ? refresh_cache_worker+0x34e/0x6d0 [cifs] [ 379.954960] ? __pfx_wb_workfn+0x10/0x10 [ 379.955239] refresh_cache_worker+0x4ad/0x6d0 [cifs] [ 379.955755] ? __pfx_refresh_cache_worker+0x10/0x10 [cifs] [ 379.956323] ? __pfx_lock_acquired+0x10/0x10 [ 379.956615] ? read_word_at_a_time+0xe/0x20 [ 379.956898] ? lockdep_hardirqs_on_prepare+0x12/0x220 [ 379.957235] process_one_work+0x535/0x990 [ 379.957509] ? __pfx_process_one_work+0x10/0x10 [ 379.957812] ? bloqueo_adquirido+0xb7/0x5f0 [ 379.958069] ? __lista_add_valid+0x37/0xd0 [ 379.958341] ? __lista_add_valid+0x37/0xd0 [ 379.958611] subproceso_de_trabajo+0x8e/0x630 [ 379.958861] ? __pfx_subproceso_de_trabajo+0x10/0x10 [ 379.959148] kthread+0x17d/0x1b0 [ 379.959369] ? __pfx_kthread+0x10/0x10 [379.959630] ret_from_fork+0x2c/0x50 [379.959879] "}], "references": [{"url": "https://git.kernel.org/stable/c/396935de145589c8bfe552fa03a5e38604071829", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5a89d81c1a3c152837ea204fd29572228e54ce0b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
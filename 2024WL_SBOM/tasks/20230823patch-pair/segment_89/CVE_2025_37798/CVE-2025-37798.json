{"cve_id": "CVE-2025-37798", "published_date": "2025-05-02T15:15:48.657", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncodel: remove sch->q.qlen check before qdisc_tree_reduce_backlog()\n\nAfter making all ->qlen_notify() callbacks idempotent, now it is safe to\nremove the check of qlen!=0 from both fq_codel_dequeue() and\ncodel_qdisc_dequeue()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: codel: eliminar la comprobación de sch-&gt;q.qlen antes de qdisc_tree_reduce_backlog() Después de hacer que todas las devoluciones de llamadas -&gt;qlen_notify() sean idempotentes, ahora es seguro eliminar la comprobación de qlen!=0 de fq_codel_dequeue() y codel_qdisc_dequeue()."}], "references": [{"url": "https://git.kernel.org/stable/c/2f9761a94bae33d26e6a81b31b36e7d776d93dc1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/342debc12183b51773b3345ba267e9263bdfaaef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4d55144b12e742404bb3f8fee6038bafbf45619d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/829c49b6b2ff45b043739168fd1245e4e1a91a30", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a57fe60ef4cf96bfbb6b58397ec28bdb5a5c6b31", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e73c838c80dccb9e4f19becc11d9f3cb4a27d483", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
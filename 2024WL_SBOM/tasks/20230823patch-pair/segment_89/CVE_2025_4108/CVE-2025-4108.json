{"cve_id": "CVE-2025-4108", "published_date": "2025-04-30T10:15:18.407", "last_modified_date": "2025-05-13T20:26:29.923", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Student Record System 3.20. Affected is an unknown function of the file /add-subject.php. The manipulation of the argument sub1 leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Student Record System 3.20. Se ve afectada una función desconocida del archivo /add-subject.php. La manipulación del argumento sub1 provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/bleakTS/myCVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306588", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306588", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560697", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
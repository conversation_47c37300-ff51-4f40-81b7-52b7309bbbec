{"cve_id": "CVE-2025-4154", "published_date": "2025-05-01T07:15:59.153", "last_modified_date": "2025-05-07T19:53:23.417", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Pre-School Enrollment System 1.0. Affected by this issue is some unknown functionality of the file /admin/enrollment-details.php. The manipulation of the argument Status leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Pre-School Enrollment System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /admin/enrollment-details.php. La manipulación del argumento \"Status\" provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Iandweb/CVE/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.306686", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.306686", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.560833", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2024-12225", "published_date": "2025-05-06T20:15:25.747", "last_modified_date": "2025-05-07T14:13:20.483", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Quarkus in the quarkus-security-webauthn module. The Quarkus WebAuthn module publishes default REST endpoints for registering and logging users in while allowing developers to provide custom REST endpoints. When developers provide custom REST endpoints, the default endpoints remain accessible, potentially allowing attackers to obtain a login cookie that has no corresponding user in the Quarkus application or, depending on how the application is written, could correspond to an existing user that has no relation with the current attacker, allowing anyone to log in as an existing user by just knowing that user's user name."}, {"lang": "es", "value": "Se detectó una vulnerabilidad en Quarkus en el módulo quarkus-security-webauthn. El módulo Quarkus WebAuthn publica endpoints REST predeterminados para el registro e inicio de sesión de los usuarios, a la vez que permite a los desarrolladores proporcionar endpoints REST personalizados. Cuando los desarrolladores proporcionan endpoints REST personalizados, los endpoints predeterminados permanecen accesibles, lo que podría permitir a los atacantes obtener una cookie de inicio de sesión que no tiene un usuario correspondiente en la aplicación Quarkus o, dependiendo de cómo esté escrita la aplicación, podría corresponder a un usuario existente sin relación con el atacante actual, lo que permite a cualquiera iniciar sesión como un usuario existente con solo conocer su nombre de usuario."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2024-12225", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2330484", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2022-49913", "published_date": "2025-05-01T15:16:16.477", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: fix inode list leak during backref walking at find_parent_nodes()\n\nDuring backref walking, at find_parent_nodes(), if we are dealing with a\ndata extent and we get an error while resolving the indirect backrefs, at\nresolve_indirect_refs(), or in the while loop that iterates over the refs\nin the direct refs rbtree, we end up leaking the inode lists attached to\nthe direct refs we have in the direct refs rbtree that were not yet added\nto the refs ulist passed as argument to find_parent_nodes(). Since they\nwere not yet added to the refs ulist and prelim_release() does not free\nthe lists, on error the caller can only free the lists attached to the\nrefs that were added to the refs ulist, all the remaining refs get their\ninode lists never freed, therefore leaking their memory.\n\nFix this by having prelim_release() always free any attached inode list\nto each ref found in the rbtree, and have find_parent_nodes() set the\nref's inode list to NULL once it transfers ownership of the inode list\nto a ref added to the refs ulist passed to find_parent_nodes()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: corrección de fuga de lista de inodos durante el recorrido de backref en find_parent_nodes() Durante el recorrido de backref, en find_parent_nodes(), si estamos tratando con una extensión de datos y obtenemos un error al resolver los backrefs indirectos, en resolve_indirect_refs(), o en el bucle while que itera sobre los refs en el rbtree de refs directos, terminamos filtrando las listas de inodos adjuntas a los refs directos que tenemos en el rbtree de refs directos que aún no se agregaron a la ulist de refs pasada como argumento a find_parent_nodes(). Dado que aún no se agregaron a la ulist de refs y prelim_release() no libera las listas, en caso de error, el llamador solo puede liberar las listas adjuntas a los refs que se agregaron a la ulist de refs, todas las referencias restantes obtienen sus listas de inodos nunca liberadas, por lo tanto, filtran su memoria. Para solucionar este problema, haga que prelim_release() siempre libere cualquier lista de inodos adjunta a cada referencia encontrada en el rbtree y que find_parent_nodes() establezca la lista de inodos de la referencia en NULL una vez que transfiera la propiedad de la lista de inodos a una referencia agregada a la lista de referencias pasada a find_parent_nodes()."}], "references": [{"url": "https://git.kernel.org/stable/c/222a3d533027b9492d5b7f5ecdc01a90f57bb5a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/61e06128113711df0534c404fb6bb528eb7d2332", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6a6731a0df8c47ecc703bd7bb73459df767051e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/83ea8c5b54d452a5769e605e3c5c687e8ca06d89", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/92876eec382a0f19f33d09d2c939e9ca49038ae5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
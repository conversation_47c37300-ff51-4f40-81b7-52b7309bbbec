{"cve_id": "CVE-2025-47244", "published_date": "2025-05-03T23:15:48.150", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Inedo ProGet through 2024.22 allows remote attackers to reach restricted functionality through the C# reflection layer, as demonstrated by causing a denial of service (when an attacker executes a loop calling RestartWeb) or obtaining potentially sensitive information. Exploitation can occur if Anonymous access is enabled, or if there is a successful CSRF attack."}, {"lang": "es", "value": "Inedo ProGet, hasta la versión 2024.22, permite a atacantes remotos acceder a funcionalidades restringidas a través de la capa de reflexión de C#, como se demuestra al provocar una denegación de servicio (cuando un atacante ejecuta un bucle que llama a RestartWeb) o al obtener información potencialmente confidencial. La explotación puede ocurrir si se habilita el acceso anónimo o si se produce un ataque CSRF exitoso."}], "references": [{"url": "https://docs.inedo.com/docs/proget/installation/installation-guide", "source": "<EMAIL>", "tags": []}, {"url": "https://forums.inedo.com", "source": "<EMAIL>", "tags": []}, {"url": "https://my.inedo.com/downloads/installers?product=ProGet", "source": "<EMAIL>", "tags": []}, {"url": "https://seclists.org/fulldisclosure/2025/Apr/30", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-4308", "published_date": "2025-05-06T03:15:18.837", "last_modified_date": "2025-05-13T19:20:51.480", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.1. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /admin/add-art-type.php. The manipulation of the argument arttype leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Art Gallery Management System 1.1. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/add-art-type.php. La manipulación del argumento arttype provoca una inyección SQL. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/3507998897/myCVE/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.307411", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.307411", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.564188", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
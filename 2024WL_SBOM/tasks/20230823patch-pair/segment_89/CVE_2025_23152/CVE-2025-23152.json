{"cve_id": "CVE-2025-23152", "published_date": "2025-05-01T13:15:51.110", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64/crc-t10dif: fix use of out-of-scope array in crc_t10dif_arch()\n\nFix a silly bug where an array was used outside of its scope."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64/crc-t10dif: se corrige el uso de una matriz fuera de alcance en crc_t10dif_arch() Se corrige un error tonto en el que se usaba una matriz fuera de su alcance."}], "references": [{"url": "https://git.kernel.org/stable/c/bd9e1a03e579a01dfa66dbaa53d0219c33cbc463", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d48b663f410f8b35b8ba9bd597bafaa00f53293b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
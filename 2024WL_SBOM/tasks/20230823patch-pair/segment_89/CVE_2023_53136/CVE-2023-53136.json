{"cve_id": "CVE-2023-53136", "published_date": "2025-05-02T16:15:32.540", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\naf_unix: fix struct pid leaks in OOB support\n\nsyzbot reported struct pid leak [1].\n\nIssue is that queue_oob() calls maybe_add_creds() which potentially\nholds a reference on a pid.\n\nBut skb->destructor is not set (either directly or by calling\nunix_scm_to_skb())\n\nThis means that subsequent kfree_skb() or consume_skb() would leak\nthis reference.\n\nIn this fix, I chose to fully support scm even for the OOB message.\n\n[1]\nBUG: memory leak\nunreferenced object 0xffff8881053e7f80 (size 128):\ncomm \"syz-executor242\", pid 5066, jiffies 4294946079 (age 13.220s)\nhex dump (first 32 bytes):\n01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................\nbacktrace:\n[<ffffffff812ae26a>] alloc_pid+0x6a/0x560 kernel/pid.c:180\n[<ffffffff812718df>] copy_process+0x169f/0x26c0 kernel/fork.c:2285\n[<ffffffff81272b37>] kernel_clone+0xf7/0x610 kernel/fork.c:2684\n[<ffffffff812730cc>] __do_sys_clone+0x7c/0xb0 kernel/fork.c:2825\n[<ffffffff849ad699>] do_syscall_x64 arch/x86/entry/common.c:50 [inline]\n[<ffffffff849ad699>] do_syscall_64+0x39/0xb0 arch/x86/entry/common.c:80\n[<ffffffff84a0008b>] entry_SYSCALL_64_after_hwframe+0x63/0xcd"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: af_unix: se corrigen fugas de struct pid en la compatibilidad OOB. syzbot reportó una fuga de struct pid [1]. El problema radica en que queue_oob() llama a perhaps_add_creds(), que potencialmente contiene una referencia a un PID. Sin embargo, skb-&gt;destructor no está definido (ni directamente ni mediante la llamada a unix_scm_to_skb()). Esto significa que las posteriores operaciones kfree_skb() o consume_skb() filtrarían esta referencia. En esta corrección, opté por ofrecer compatibilidad total con scm, incluso para el mensaje OOB. [1] ERROR: pérdida de memoria, objeto no referenciado 0xffff8881053e7f80 (tamaño 128): comunicación \"syz-executor242\", pid 5066, jiffies 4294946079 (edad 13.220s), volcado hexadecimal (primeros 32 bytes): 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ backtrace: [] alloc_pid+0x6a/0x560 kernel/pid.c:180 [] copy_process+0x169f/0x26c0 kernel/fork.c:2285 [] kernel_clone+0xf7/0x610 kernel/fork.c:2684 [] __do_sys_clone+0x7c/0xb0 kernel/fork.c:2825 [] do_syscall_x64 arch/x86/entry/common.c:50 [inline] [] do_syscall_64+0x39/0xb0 arch/x86/entry/common.c:80 [] entry_SYSCALL_64_after_hwframe+0x63/0xcd "}], "references": [{"url": "https://git.kernel.org/stable/c/2aab4b96900272885bc157f8b236abf1cdc02e08", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a59d6306263c38e5c0592ea4451ca26a0778c947", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ac1968ac399205fda9ee3b18f7de7416cb3a5d0d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f3969427fb06a2c3cd6efd7faab63505cfa76e76", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
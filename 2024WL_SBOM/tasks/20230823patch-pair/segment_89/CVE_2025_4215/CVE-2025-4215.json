{"cve_id": "CVE-2025-4215", "published_date": "2025-05-02T21:15:23.893", "last_modified_date": "2025-06-17T14:17:53.467", "descriptions": [{"lang": "en", "value": "A vulnerability was found in gorhill uBlock Origin up to 1.63.3b16. It has been classified as problematic. Affected is the function currentStateChanged of the file src/js/1p-filters.js of the component UI. The manipulation leads to inefficient regular expression complexity. It is possible to launch the attack remotely. The complexity of an attack is rather high. The exploitability is told to be difficult. The exploit has been disclosed to the public and may be used. Upgrading to version 1.63.3b17 is able to address this issue. The patch is identified as eaedaf5b10d2f7857c6b77fbf7d4a80681d4d46c. It is recommended to upgrade the affected component."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en gorhill uBlock Origin hasta la versión 1.63.3b16. Se ha clasificado como problemática. La función currentStateChanged del archivo src/js/1p-filters.js de la interfaz de usuario del componente se ve afectada. La manipulación genera una complejidad ineficiente en las expresiones regulares. Es posible ejecutar el ataque de forma remota. Es un ataque de complejidad bastante alta. Parece difícil de explotar. Se ha hecho público el exploit y puede que sea utilizado. Actualizar a la versión 1.63.3b17 soluciona este problema. El parche se identifica como eaedaf5b10d2f7857c6b77fbf7d4a80681d4d46c. Se recomienda actualizar el componente afectado."}], "references": [{"url": "https://github.com/gorhill/uBlock/commit/eaedaf5b10d2f7857c6b77fbf7d4a80681d4d46c", "source": "<EMAIL>", "tags": ["Exploit", "Patch"]}, {"url": "https://github.com/gorhill/uBlock/releases/tag/1.63.3b17", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://vuldb.com/?ctiid.307194", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.307194", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.562301", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://lists.debian.org/debian-lts-announce/2025/06/msg00013.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}]}
{"cve_id": "CVE-2023-53071", "published_date": "2025-05-02T16:15:26.140", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: mt76: do not run mt76_unregister_device() on unregistered hw\n\nTrying to probe a mt7921e pci card without firmware results in a\nsuccessful probe where ieee80211_register_hw hasn't been called. When\nremoving the driver, ieee802111_unregister_hw is called unconditionally\nleading to a kernel NULL pointer dereference.\nFix the issue running mt76_unregister_device routine just for registered\nhw."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: mt76: no ejecutar mt76_unregister_device() en hardware no registrado. Al intentar sondear una tarjeta PCI mt7921e sin firmware, se obtiene un sondeo exitoso donde no se ha llamado a ieee80211_register_hw. Al desinstalar el controlador, se llama a ieee802111_unregister_hw incondicionalmente, lo que provoca una desreferencia de puntero nulo en el kernel. Se solucionó el problema al ejecutar la rutina mt76_unregister_device solo para hardware registrado."}], "references": [{"url": "https://git.kernel.org/stable/c/2d34f27714c97a9786a30b3bb54944d6d8ed612f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/41130c32f3a18fcc930316da17f3a5f3bc326aa1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dffe86df26aee01a5fc56a175b7a7f157961e370", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
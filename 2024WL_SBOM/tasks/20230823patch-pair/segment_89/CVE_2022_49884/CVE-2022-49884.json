{"cve_id": "CVE-2022-49884", "published_date": "2025-05-01T15:16:13.387", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: Initialize gfn_to_pfn_cache locks in dedicated helper\n\nMove the gfn_to_pfn_cache lock initialization to another helper and\ncall the new helper during VM/vCPU creation.  There are race\nconditions possible due to kvm_gfn_to_pfn_cache_init()'s\nability to re-initialize the cache's locks.\n\nFor example: a race between ioctl(KVM_XEN_HVM_EVTCHN_SEND) and\nkvm_gfn_to_pfn_cache_init() leads to a corrupted shinfo gpc lock.\n\n                (thread 1)                |           (thread 2)\n                                          |\n kvm_xen_set_evtchn_fast                  |\n  read_lock_irqsave(&gpc->lock, ...)      |\n                                          | kvm_gfn_to_pfn_cache_init\n                                          |  rwlock_init(&gpc->lock)\n  read_unlock_irqrestore(&gpc->lock, ...) |\n\nRename \"cache_init\" and \"cache_destroy\" to activate+deactivate to\navoid implying that the cache really is destroyed/freed.\n\nNote, there more races in the newly named kvm_gpc_activate() that will\nbe addressed separately.\n\n[sean: call out that this is a bug fix]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: Inicializar bloqueos de gfn_to_pfn_cache en un asistente dedicado. Trasladar la inicialización del bloqueo de gfn_to_pfn_cache a otro asistente y llamar al nuevo asistente durante la creación de la máquina virtual o la vCPU. Es posible que se produzcan condiciones de competencia debido a la capacidad de kvm_gfn_to_pfn_cache_init() de reinicializar los bloqueos de la caché. Por ejemplo: una competencia entre ioctl(KVM_XEN_HVM_EVTCHN_SEND) y kvm_gfn_to_pfn_cache_init() provoca un bloqueo de gpc de shinfo dañado. (hilo 1) | (hilo 2) | kvm_xen_set_evtchn_fast | read_lock_irqsave(&amp;gpc-&gt;lock, ...) | | kvm_gfn_to_pfn_cache_init | rwlock_init(&amp;gpc-&gt;lock) read_unlock_irqrestore(&amp;gpc-&gt;lock, ...) | Renombra \"cache_init\" y \"cache_destroy\" como activate+deactivate para evitar que la caché se destruya o libere. Ten en cuenta que hay más ejecuciones en el nuevo nombre kvm_gpc_activate() que se abordarán por separado. [sean: se indica que se trata de una corrección de error]"}], "references": [{"url": "https://git.kernel.org/stable/c/52491a38b2c2411f3f0229dc6ad610349c704a41", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/61242001d6c9c253df7645dab090842d8da08764", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
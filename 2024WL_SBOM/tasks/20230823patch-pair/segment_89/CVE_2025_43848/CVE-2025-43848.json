{"cve_id": "CVE-2025-43848", "published_date": "2025-05-05T18:15:42.683", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "Retrieval-based-Voice-Conversion-WebUI is a voice changing framework based on VITS. Versions 2.2.231006 and prior are vulnerable to unsafe deserialization. The ckpt_path0 variable takes user input (e.g. a path to a model) and passes it to the change_info function in process_ckpt.py, which uses it to load the model on that path with torch.load, which can lead to unsafe deserialization and remote code execution. As of time of publication, no known patches exist."}, {"lang": "es", "value": "Retrieval-based-Voice-Conversion-WebUI es un framework de modificación de voz basado en VITS. Las versiones 2.2.231006 y anteriores son vulnerables a la deserialización insegura. La variable ckpt_path0 toma la entrada del usuario (por ejemplo, la ruta a un modelo) y la pasa a la función change_info en process_ckpt.py, que la utiliza para cargar el modelo en esa ruta con torch.load, lo que puede provocar una deserialización insegura y la ejecución remota de código. Al momento de la publicación, no se conocían parches."}], "references": [{"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/7ef19867780cf703841ebafb565a4e47d1ea86ff/infer/lib/train/process_ckpt.py#L196", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L1415", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI/blob/9f2f0559e6932c10c48642d404e7d2e771d9db43/infer-web.py#L1431", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2025-012_GHSL-2025-022_Retrieval-based-Voice-Conversion-WebUI/", "source": "<EMAIL>", "tags": []}]}
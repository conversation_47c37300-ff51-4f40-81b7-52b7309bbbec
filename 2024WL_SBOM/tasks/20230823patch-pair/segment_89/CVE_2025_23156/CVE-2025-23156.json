{"cve_id": "CVE-2025-23156", "published_date": "2025-05-01T13:15:51.517", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: venus: hfi_parser: refactor hfi packet parsing logic\n\nwords_count denotes the number of words in total payload, while data\npoints to payload of various property within it. When words_count\nreaches last word, data can access memory beyond the total payload. This\ncan lead to OOB access. With this patch, the utility api for handling\nindividual properties now returns the size of data consumed. Accordingly\nremaining bytes are calculated before parsing the payload, thereby\neliminates the OOB access possibilities."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: venus: hfi_parser: refactorización de la lógica de análisis de paquetes HFI. words_count indica el número de palabras en el payload total, mientras que data apunta al payload de varias propiedades dentro de ella. Cuando words_count alcanza la última palabra, data puede acceder a memoria más allá de payload total. Esto puede provocar accesos fuera de banda (OOB). Con este parche, la API de utilidad para gestionar propiedades individuales ahora devuelve el tamaño de los datos consumidos. Por consiguiente, los bytes restantes se calculan antes de analizar el payload, eliminando así las posibilidades de accesos fuera de banda (OOB)."}], "references": [{"url": "https://git.kernel.org/stable/c/05b07e52a0d08239147ba3460045855f4fb398de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0beabe9b49190a02321b02792b29fc0f0e28b51f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0f9a4bab7d83738963365372e4745854938eab2d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6d278c5548d840c4d85d445347b2a5c31b2ab3a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9edaaa8e3e15aab1ca413ab50556de1975bcb329", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a736c72d476d1c7ca7be5018f2614ee61168ad01", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bb3fd8b7906a12dc2b61389abb742bf6542d97fb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f195e94c7af921d99abd79f57026a218d191d2c7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
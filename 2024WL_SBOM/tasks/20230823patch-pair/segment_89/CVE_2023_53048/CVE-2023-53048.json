{"cve_id": "CVE-2023-53048", "published_date": "2025-05-02T16:15:23.897", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: typec: tcpm: fix warning when handle discover_identity message\n\nSince both source and sink device can send discover_identity message in\nPD3, kernel may dump below warning:\n\n------------[ cut here ]------------\nWARNING: CPU: 0 PID: 169 at drivers/usb/typec/tcpm/tcpm.c:1446 tcpm_queue_vdm+0xe0/0xf0\nModules linked in:\nCPU: 0 PID: 169 Comm: 1-0050 Not tainted 6.1.1-00038-g6a3c36cf1da2-dirty #567\nHardware name: NXP i.MX8MPlus EVK board (DT)\npstate: 20000005 (nzCv daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--)\npc : tcpm_queue_vdm+0xe0/0xf0\nlr : tcpm_queue_vdm+0x2c/0xf0\nsp : ffff80000c19bcd0\nx29: ffff80000c19bcd0 x28: 0000000000000001 x27: ffff0000d11c8ab8\nx26: ffff0000d11cc000 x25: 0000000000000000 x24: 00000000ff008081\nx23: 0000000000000001 x22: 00000000ff00a081 x21: ffff80000c19bdbc\nx20: 0000000000000000 x19: ffff0000d11c8080 x18: ffffffffffffffff\nx17: 0000000000000000 x16: 0000000000000000 x15: ffff0000d716f580\nx14: 0000000000000001 x13: ffff0000d716f507 x12: 0000000000000001\nx11: 0000000000000000 x10: 0000000000000020 x9 : 00000000000ee098\nx8 : 00000000ffffffff x7 : 000000000000001c x6 : ffff0000d716f580\nx5 : 0000000000000000 x4 : 0000000000000000 x3 : 0000000000000000\nx2 : ffff80000c19bdbc x1 : 00000000ff00a081 x0 : 0000000000000004\nCall trace:\ntcpm_queue_vdm+0xe0/0xf0\ntcpm_pd_rx_handler+0x340/0x1ab0\nkthread_worker_fn+0xcc/0x18c\nkthread+0x10c/0x110\nret_from_fork+0x10/0x20\n---[ end trace 0000000000000000 ]---\n\nBelow sequences may trigger this warning:\n\ntcpm_send_discover_work(work)\n  tcpm_send_vdm(port, USB_SID_PD, CMD_DISCOVER_IDENT, NULL, 0);\n   tcpm_queue_vdm(port, header, data, count);\n    port->vdm_state = VDM_STATE_READY;\n\nvdm_state_machine_work(work);\n\t\t\t<-- received discover_identity from partner\n vdm_run_state_machine(port);\n  port->vdm_state = VDM_STATE_SEND_MESSAGE;\n   mod_vdm_delayed_work(port, x);\n\ntcpm_pd_rx_handler(work);\n tcpm_pd_data_request(port, msg);\n  tcpm_handle_vdm_request(port, msg->payload, cnt);\n   tcpm_queue_vdm(port, response[0], &response[1], rlen - 1);\n--> WARN_ON(port->vdm_state > VDM_STATE_DONE);\n\nFor this case, the state machine could still send out discover\nidentity message later if we skip current discover_identity message.\nSo we should handle the received message firstly and override the pending\ndiscover_identity message without warning in this case. Then, a delayed\nsend_discover work will send discover_identity message again."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: typec: tcpm: se corrige la advertencia al manejar el mensaje discover_identity Dado que tanto el dispositivo de origen como el receptor pueden enviar el mensaje discover_identity en PD3, el kernel puede mostrar la siguiente advertencia: ------------[ cortar aquí ]------------ ADVERTENCIA: CPU: 0 PID: 169 en drivers/usb/typec/tcpm/tcpm.c:1446 tcpm_queue_vdm+0xe0/0xf0 Módulos vinculados: CPU: 0 PID: 169 Comm: 1-0050 No contaminado 6.1.1-00038-g6a3c36cf1da2-dirty #567 Nombre del hardware: Placa NXP i.MX8MPlus EVK (DT) pstate: 20000005 (nzCv daif -PAN -UAO -TCO -DIT -SSBS BTYPE=--) pc : tcpm_queue_vdm+0xe0/0xf0 lr : tcpm_queue_vdm+0x2c/0xf0 sp : ffff80000c19bcd0 x29: ffff80000c19bcd0 x28: 0000000000000001 x27: ffff0000d11c8ab8 x26: ffff0000d11cc000 x25: 0000000000000000 x24: 00000000ff008081 x23: 000000000000001 x22: 00000000ff00a081 x21: ffff80000c19bdbc x20: 0000000000000000 x19: ffff0000d11c8080 x18: ffffffffffffffff x17: 0000000000000000 x16: 0000000000000000 x15: ffff0000d716f580 x14: 0000000000000001 x13: ffff0000d716f507 x12: 000000000000001 x11: 000000000000000 x10: 000000000000020 x9 : 00000000000ee098 x8 : 00000000ffffffff x7 : 000000000000001c x6 : ffff0000d716f580 x5 : 0000000000000000 x4 : 0000000000000000 x3 : 0000000000000000 x2 : ffff80000c19bdbc x1 : 00000000ff00a081 x0 : 0000000000000004 Rastreo de llamadas: tcpm_queue_vdm+0xe0/0xf0 tcpm_pd_rx_handler+0x340/0x1ab0 kthread_worker_fn+0xcc/0x18c kthread+0x10c/0x110 ret_from_fork+0x10/0x20 ---[ fin del seguimiento 000000000000000 ]--- Las siguientes secuencias pueden activar esta advertencia: tcpm_send_discover_work(trabajo) tcpm_send_vdm(puerto, USB_SID_PD, CMD_DISCOVER_IDENT, NULL, 0); tcpm_queue_vdm(puerto, encabezado, datos, recuento); puerto-&gt;vdm_state = VDM_STATE_READY; vdm_state_machine_work(trabajo); &lt;-- se recibió discover_identity del socio vdm_run_state_machine(puerto); puerto-&gt;vdm_state = VDM_STATE_SEND_MESSAGE; mod_vdm_delayed_work(puerto, x); tcpm_pd_rx_handler(trabajo); tcpm_pd_data_request(port, msg); tcpm_handle_vdm_request(port, msg-&gt;payload, cnt); tcpm_queue_vdm(port, response[0], &amp;response[1], rlen - 1); --&gt; WARN_ON(port-&gt;vdm_state &gt; VDM_STATE_DONE); En este caso, la máquina de estados podría enviar el mensaje de descubrimiento de identidad más tarde si omitimos el mensaje de descubrimiento de identidad actual. Por lo tanto, debemos procesar primero el mensaje recibido y anular el mensaje de descubrimiento de identidad pendiente sin previo aviso. Posteriormente, una operación de envío de descubrimiento retrasado enviará el mensaje de descubrimiento de identidad nuevamente."}], "references": [{"url": "https://git.kernel.org/stable/c/abfc4fa28f0160df61c7149567da4f6494dfb488", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bb579b3f75c60bf488a7c36e092e8be583407d53", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d55ca2d2ea1a7ec553213986993fba8c0257381c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e37d2c489d71e94ed4a39529bc9520a7fd983d42", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
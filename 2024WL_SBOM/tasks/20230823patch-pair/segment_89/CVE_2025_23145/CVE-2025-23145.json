{"cve_id": "CVE-2025-23145", "published_date": "2025-05-01T13:15:50.343", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmptcp: fix NULL pointer in can_accept_new_subflow\n\nWhen testing valkey benchmark tool with MPTCP, the kernel panics in\n'mptcp_can_accept_new_subflow' because subflow_req->msk is NULL.\n\nCall trace:\n\n  mptcp_can_accept_new_subflow (./net/mptcp/subflow.c:63 (discriminator 4)) (P)\n  subflow_syn_recv_sock (./net/mptcp/subflow.c:854)\n  tcp_check_req (./net/ipv4/tcp_minisocks.c:863)\n  tcp_v4_rcv (./net/ipv4/tcp_ipv4.c:2268)\n  ip_protocol_deliver_rcu (./net/ipv4/ip_input.c:207)\n  ip_local_deliver_finish (./net/ipv4/ip_input.c:234)\n  ip_local_deliver (./net/ipv4/ip_input.c:254)\n  ip_rcv_finish (./net/ipv4/ip_input.c:449)\n  ...\n\nAccording to the debug log, the same req received two SYN-ACK in a very\nshort time, very likely because the client retransmits the syn ack due\nto multiple reasons.\n\nEven if the packets are transmitted with a relevant time interval, they\ncan be processed by the server on different CPUs concurrently). The\n'subflow_req->msk' ownership is transferred to the subflow the first,\nand there will be a risk of a null pointer dereference here.\n\nThis patch fixes this issue by moving the 'subflow_req->msk' under the\n`own_req == true` conditional.\n\nNote that the !msk check in subflow_hmac_valid() can be dropped, because\nthe same check already exists under the own_req mpj branch where the\ncode has been moved to."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mptcp: se corrige el puntero NULL en can_accept_new_subflow Al probar la herramienta de evaluación comparativa valkey con MPTCP, el kernel entra en pánico en 'mptcp_can_accept_new_subflow' porque subflow_req-&gt;msk es NULL. Rastreo de llamadas: mptcp_can_accept_new_subflow (./net/mptcp/subflow.c:63 (discriminador 4)) (P) subflow_syn_recv_sock (./net/mptcp/subflow.c:854) tcp_check_req (./net/ipv4/tcp_minisocks.c:863) tcp_v4_rcv (./net/ipv4/tcp_ipv4.c:2268) ip_protocol_deliver_rcu (./net/ipv4/ip_input.c:207) ip_local_deliver_finish (./net/ipv4/ip_input.c:234) ip_local_deliver (./net/ipv4/ip_input.c:254) ip_rcv_finish (./net/ipv4/ip_input.c:449) ... Según el registro de depuración, la misma solicitud recibió dos SYN-ACK en muy poco tiempo, probablemente porque el cliente retransmite el SYN-ACK por varias razones. Incluso si los paquetes se transmiten con un intervalo de tiempo relevante, el servidor puede procesarlos en diferentes CPU simultáneamente. La propiedad de 'subflow_req-&gt;msk' se transfiere primero al subflujo, lo que conlleva el riesgo de una desreferencia de puntero nulo. Este parche corrige este problema moviendo 'subflow_req-&gt;msk' bajo la condición `own_req == true`. Tenga en cuenta que la comprobación !msk en subflow_hmac_valid() puede omitirse, ya que ya existe en la rama own_req de mpj, donde se ha movido el código."}], "references": [{"url": "https://git.kernel.org/stable/c/443041deb5ef6a1289a99ed95015ec7442f141dc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4b2649b9717678aeb097893cc49f59311a1ecab0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f9ae060ed64aef8f174c5f1ea513825b1be9af1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/855bf0aacd51fced11ea9aa0d5101ee0febaeadb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8cf7fef1bb2ffea7792bcbf71ca00216cecc725d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b3088bd2a6790c8efff139d86d7a9d0b1305977b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dc81e41a307df523072186b241fa8244fecd7803", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/efd58a8dd9e7a709a90ee486a4247c923d27296f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
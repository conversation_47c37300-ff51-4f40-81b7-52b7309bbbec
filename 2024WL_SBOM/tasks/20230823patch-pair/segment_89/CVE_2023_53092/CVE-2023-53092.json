{"cve_id": "CVE-2023-53092", "published_date": "2025-05-02T16:15:28.180", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ninterconnect: exynos: fix node leak in probe PM QoS error path\n\nMake sure to add the newly allocated interconnect node to the provider\nbefore adding the PM QoS request so that the node is freed on errors."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: interconexión: exynos: se corrige la pérdida de nodo en la ruta de error de QoS de PM de la sonda Asegúrese de agregar el nodo de interconexión recién asignado al proveedor antes de agregar la solicitud de QoS de PM para que el nodo se libere en caso de errores."}], "references": [{"url": "https://git.kernel.org/stable/c/3aab264875bf3c915ea2517fae1eec213e0b4987", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b71dd43bd49bd68186c1d19dbeedee219e003149", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c479e4ac4a3d1485a48599e66ce46547c1367828", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fd4738ae1a0c216d25360a98e835967b06d6a253", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
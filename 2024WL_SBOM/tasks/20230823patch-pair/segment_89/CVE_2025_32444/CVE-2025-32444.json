{"cve_id": "CVE-2025-32444", "published_date": "2025-04-30T01:15:51.953", "last_modified_date": "2025-05-28T19:12:58.377", "descriptions": [{"lang": "en", "value": "vLLM is a high-throughput and memory-efficient inference and serving engine for LLMs. Versions starting from 0.6.5 and prior to 0.8.5, having vLLM integration with mooncake, are vulnerable to remote code execution due to using pickle based serialization over unsecured ZeroMQ sockets. The vulnerable sockets were set to listen on all network interfaces, increasing the likelihood that an attacker is able to reach the vulnerable ZeroMQ sockets to carry out an attack. vLLM instances that do not make use of the mooncake integration are not vulnerable. This issue has been patched in version 0.8.5."}, {"lang": "es", "value": "vLLM es un motor de inferencia y servicio de alto rendimiento y eficiente en memoria para LLM. Las versiones a partir de la 0.6.5 y anteriores a la 0.8.5, que integran vLLM con mooncake, son vulnerables a la ejecución remota de código debido al uso de serialización basada en pickle sobre sockets ZeroMQ no seguros. Los sockets vulnerables estaban configurados para escuchar en todas las interfaces de red, lo que aumenta la probabilidad de que un atacante pueda acceder a los sockets ZeroMQ vulnerables para ejecutar un ataque. Las instancias de vLLM que no utilizan la integración con mooncake no son vulnerables. Este problema se ha corregido en la versión 0.8.5."}], "references": [{"url": "https://github.com/vllm-project/vllm/blob/32b14baf8a1f7195ca09484de3008063569b43c5/vllm/distributed/kv_transfer/kv_pipe/mooncake_pipe.py#L179", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/vllm-project/vllm/commit/a5450f11c95847cf51a17207af9a3ca5ab569b2c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-hj4w-hm2g-p6w5", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://github.com/vllm-project/vllm/security/advisories/GHSA-x3m8-f7g5-qhm7", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}
{"cve_id": "CVE-2025-3438", "published_date": "2025-05-02T06:15:48.020", "last_modified_date": "2025-05-06T15:35:14.563", "descriptions": [{"lang": "en", "value": "The MStore API – Create Native Android & iOS Apps On The Cloud plugin for WordPress is vulnerable to limited privilege escalation in all versions up to, and including, 4.17.4. This is due to a lack of restriction of role when registering. This makes it possible for unauthenticated attackers to to register with the 'wcfm_vendor' role, which is a Store Vendor role in the WCFM Marketplace – Multivendor Marketplace for WooCommerce plugin for WordPress. The vulnerability can only be exploited if the WCFM Marketplace – Multivendor Marketplace for WooCommerce plugin is installed and activated. The vulnerability was partially patched in version 4.17.3."}, {"lang": "es", "value": "El complemento MStore API – Create Native Android &amp; iOS Apps On The Cloud para WordPress es vulnerable a una escalada de privilegios limitada en todas las versiones hasta la 4.17.4 incluida. Esto se debe a la falta de restricción de roles al registrarse. Esto permite que atacantes no autenticados se registren con el rol 'wcfm_vendor', que es un rol de vendedor de tienda en el complemento WCFM Marketplace – Multivendor Marketplace para WooCommerce para WordPress. Esta vulnerabilidad solo se puede explotar si el plugin WCFM Marketplace – Multivendor Marketplace para WooCommerce está instalado y activado. La vulnerabilidad se corrigió parcialmente en la versión 4.17.3."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/mstore-api/trunk/controllers/flutter-user.php#L392", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/mstore-api/trunk/controllers/flutter-user.php#L413", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3277790", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3279132/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/be5d86ad-f94b-4fcb-9b74-ecddde2bf29d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
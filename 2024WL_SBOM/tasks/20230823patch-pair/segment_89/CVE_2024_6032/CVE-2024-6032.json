{"cve_id": "CVE-2024-6032", "published_date": "2025-04-30T20:15:21.283", "last_modified_date": "2025-05-02T13:53:40.163", "descriptions": [{"lang": "en", "value": "Tesla Model S Iris Modem ql_atfwd Command Injection Code Execution Vulnerability. This vulnerability allows local attackers to execute arbitrary code on affected Tesla Model S vehicles. An attacker must first obtain the ability to execute code on the target system in order to exploit this vulnerability.\n \nThe specific flaw exists within the ql_atfwd process. The issue results from the lack of proper validation of a user-supplied string before using it to execute a system call. An attacker can leverage this vulnerability to execute code on the target modem in the context of root. Was ZDI-CAN-23201."}, {"lang": "es", "value": "Vulnerabilidad de ejecución de código por inyección de comandos ql_atfwd en el módem Iris del Tesla Model S. Esta vulnerabilidad permite a atacantes locales ejecutar código arbitrario en los vehículos Tesla Model S afectados. Para explotar esta vulnerabilidad, un atacante debe obtener primero la capacidad de ejecutar código en el sistema objetivo. La falla específica se encuentra en el proceso ql_atfwd. El problema se debe a la falta de validación adecuada de una cadena proporcionada por el usuario antes de usarla para ejecutar una llamada al sistema. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el módem objetivo con acceso root. Era ZDI-CAN-23201."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-264/", "source": "<EMAIL>", "tags": []}]}
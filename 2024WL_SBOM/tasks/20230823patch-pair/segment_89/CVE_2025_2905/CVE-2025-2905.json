{"cve_id": "CVE-2025-2905", "published_date": "2025-05-05T09:15:15.923", "last_modified_date": "2025-05-05T20:54:19.760", "descriptions": [{"lang": "en", "value": "An XML External Entity (XXE) vulnerability exists in the gateway component of WSO2 API Manager due to insufficient validation of XML input in crafted URL paths. User-supplied XML is parsed without appropriate restrictions, enabling external entity resolution.\n\n\nThis vulnerability can be exploited by an unauthenticated remote attacker to read files from the server’s filesystem or perform denial-of-service (DoS) attacks.\n\n\n\n  *  \nOn systems running JDK 7 or early JDK 8, full file contents may be exposed.\n\n\n\n\n  *  \nOn later versions of JDK 8 and newer, only the first line of a file may be read, due to improvements in XML parser behavior.\n\n\n\n\n  *  \nDoS attacks such as \"Billion Laughs\" payloads can cause service disruption."}, {"lang": "es", "value": "Existe una vulnerabilidad de Entidad Externa XML (XXE) en el componente de puerta de enlace de WSO2 API Manager debido a una validación insuficiente de la entrada XML en rutas URL manipulada. El XML proporcionado por el usuario se analiza sin las restricciones adecuadas, lo que permite la resolución de entidades externas. Esta vulnerabilidad puede ser explotada por un atacante remoto no autenticado para leer archivos del sistema de archivos del servidor o realizar ataques de denegación de servicio (DoS). * En sistemas con JDK 7 o versiones anteriores de JDK 8, el contenido completo de los archivos puede quedar expuesto. * En versiones posteriores de JDK 8 y posteriores, solo se puede leer la primera línea de un archivo, gracias a mejoras en el comportamiento del analizador XML. * Los ataques DoS, como los payloads \"Billion Laughs\", pueden causar interrupciones del servicio."}], "references": [{"url": "https://security.docs.wso2.com/en/latest/security-announcements/security-advisories/2025/WSO2-2025-3993/", "source": "ed10eef1-636d-4fbe-9993-6890dfa878f8", "tags": []}]}
{"cve_id": "CVE-2023-53041", "published_date": "2025-05-02T16:15:23.220", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: qla2xxx: Perform lockless command completion in abort path\n\nWhile adding and removing the controller, the following call trace was\nobserved:\n\nWARNING: CPU: 3 PID: 623596 at kernel/dma/mapping.c:532 dma_free_attrs+0x33/0x50\nCPU: 3 PID: 623596 Comm: sh Kdump: loaded Not tainted 5.14.0-96.el9.x86_64 #1\nRIP: 0010:dma_free_attrs+0x33/0x50\n\nCall Trace:\n   qla2x00_async_sns_sp_done+0x107/0x1b0 [qla2xxx]\n   qla2x00_abort_srb+0x8e/0x250 [qla2xxx]\n   ? ql_dbg+0x70/0x100 [qla2xxx]\n   __qla2x00_abort_all_cmds+0x108/0x190 [qla2xxx]\n   qla2x00_abort_all_cmds+0x24/0x70 [qla2xxx]\n   qla2x00_abort_isp_cleanup+0x305/0x3e0 [qla2xxx]\n   qla2x00_remove_one+0x364/0x400 [qla2xxx]\n   pci_device_remove+0x36/0xa0\n   __device_release_driver+0x17a/0x230\n   device_release_driver+0x24/0x30\n   pci_stop_bus_device+0x68/0x90\n   pci_stop_and_remove_bus_device_locked+0x16/0x30\n   remove_store+0x75/0x90\n   kernfs_fop_write_iter+0x11c/0x1b0\n   new_sync_write+0x11f/0x1b0\n   vfs_write+0x1eb/0x280\n   ksys_write+0x5f/0xe0\n   do_syscall_64+0x5c/0x80\n   ? do_user_addr_fault+0x1d8/0x680\n   ? do_syscall_64+0x69/0x80\n   ? exc_page_fault+0x62/0x140\n   ? asm_exc_page_fault+0x8/0x30\n   entry_SYSCALL_64_after_hwframe+0x44/0xae\n\nThe command was completed in the abort path during driver unload with a\nlock held, causing the warning in abort path. Hence complete the command\nwithout any lock held."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: qla2xxx: Realizar la finalización de comandos sin bloqueo en la ruta de aborto Al agregar y quitar el controlador, se observó el siguiente seguimiento de llamada: ADVERTENCIA: CPU: 3 PID: 623596 en kernel/dma/mapping.c:532 dma_free_attrs+0x33/0x50 CPU: 3 PID: 623596 Comm: sh Kdump: cargado No contaminado 5.14.0-96.el9.x86_64 #1 RIP: 0010:dma_free_attrs+0x33/0x50 Seguimiento de llamada: qla2x00_async_sns_sp_done+0x107/0x1b0 [qla2xxx] qla2x00_abort_srb+0x8e/0x250 [qla2xxx] ? ql_dbg+0x70/0x100 [qla2xxx] __qla2x00_abort_all_cmds+0x108/0x190 [qla2xxx] qla2x00_abort_all_cmds+0x24/0x70 [qla2xxx] qla2x00_abort_isp_cleanup+0x305/0x3e0 [qla2xxx] qla2x00_remove_one+0x364/0x400 [qla2xxx] pci_device_remove+0x36/0xa0 __device_release_driver+0x17a/0x230 device_release_driver+0x24/0x30 pci_stop_bus_device+0x68/0x90 pci_stop_and_remove_bus_device_locked+0x16/0x30 remove_store+0x75/0x90 kernfs_fop_write_iter+0x11c/0x1b0 new_sync_write+0x11f/0x1b0 vfs_write+0x1eb/0x280 ksys_write+0x5f/0xe0 do_syscall_64+0x5c/0x80 ? do_user_addr_fault+0x1d8/0x680 ? do_syscall_64+0x69/0x80 ? exc_page_fault+0x62/0x140 ? asm_exc_page_fault+0x8/0x30 entry_SYSCALL_64_after_hwframe+0x44/0xae. El comando se completó en la ruta de interrupción durante la descarga del controlador con un bloqueo, lo que provocó la advertencia en la ruta de interrupción. Por lo tanto, complete el comando sin ningún bloqueo."}], "references": [{"url": "https://git.kernel.org/stable/c/0367076b0817d5c75dfb83001ce7ce5c64d803a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/231cfa78ec5badd84a1a2b09465bfad1a926aba1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/415d614344a4f1bbddf55d724fc7eb9ef4b39aad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9189f20b4c5307c0998682bb522e481b4567a8b8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cd0a1804ac5bab2545ac700c8d0fe9ae9284c567", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d6f7377528d2abf338e504126e44439541be8f7d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
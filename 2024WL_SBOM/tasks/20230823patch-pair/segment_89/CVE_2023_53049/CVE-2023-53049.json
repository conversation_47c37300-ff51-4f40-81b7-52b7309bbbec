{"cve_id": "CVE-2023-53049", "published_date": "2025-05-02T16:15:23.990", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: ucsi: Fix NULL pointer deref in ucsi_connector_change()\n\nWhen ucsi_init() fails, ucsi->connector is NULL, yet in case of\nucsi_acpi we may still get events which cause the ucs_acpi code to call\nucsi_connector_change(), which then derefs the NULL ucsi->connector\npointer.\n\nFix this by not setting ucsi->ntfy inside ucsi_init() until ucsi_init()\nhas succeeded, so that ucsi_connector_change() ignores the events\nbecause UCSI_ENABLE_NTFY_CONNECTOR_CHANGE is not set in the ntfy mask."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: ucsi: Se corrige la desreferencia del puntero nulo en ucsi_connector_change(). Cuando ucsi_init() falla, ucsi-&gt;connector es nulo; sin embargo, en el caso de ucsi_acpi, aún pueden aparecer eventos que provocan que el código ucs_acpi llame a ucsi_connector_change(), que a su vez desreferencia el puntero nulo ucsi-&gt;connector. Para solucionar esto, no configure ucsi-&gt;ntfy dentro de ucsi_init() hasta que ucsi_init() se haya ejecutado correctamente, de modo que ucsi_connector_change() ignore los eventos, ya que UCSI_ENABLE_NTFY_CONNECTOR_CHANGE no está configurado en la máscara ntfy."}], "references": [{"url": "https://git.kernel.org/stable/c/1c5abcb13491da8c049f20462189c12c753ba978", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7dd27aed9c456670b3882877ef17a48195f21693", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7ef0423e43f877a328454059d46763043ce3da44", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a6adfe9bbd6ac11e398b54ccd99a0f8eea09f3c0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f87fb985452ab2083967103ac00bfd68fb182764", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53055", "published_date": "2025-05-02T16:15:24.590", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfscrypt: destroy keyring after security_sb_delete()\n\nfscrypt_destroy_keyring() must be called after all potentially-encrypted\ninodes were evicted; otherwise it cannot safely destroy the keyring.\nSince inodes that are in-use by the Landlock LSM don't get evicted until\nsecurity_sb_delete(), this means that fscrypt_destroy_keyring() must be\ncalled *after* security_sb_delete().\n\nThis fixes a WARN_ON followed by a NULL dereference, only possible if\nLandlock was being used on encrypted files."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: fscrypt: destruir el anillo de claves después de security_sb_delete(). fscrypt_destroy_keyring() debe llamarse después de expulsar todos los inodos potencialmente cifrados; de lo contrario, no puede destruir el anillo de claves de forma segura. Dado que los inodos en uso por el LSM de Landlock no se expulsan hasta security_sb_delete(), esto significa que fscrypt_destroy_keyring() debe llamarse *después* de security_sb_delete(). Esto corrige un WARN_ON seguido de una desreferencia a NULL, solo posible si Landlock se utilizaba en archivos cifrados."}], "references": [{"url": "https://git.kernel.org/stable/c/497ab5d9c7852dfedab2c9de75e41b60e54b7c5d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/992a3f3e8a0c92151dfdf65fc85567c865fd558a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ccb820dc7d2236b1af0d54ae038a27b5b6d5ae5a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d77531fac6a1fd9f1db0195438ba5419d72b96c4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2022-49870", "published_date": "2025-05-01T15:16:11.937", "last_modified_date": "2025-05-02T13:52:51.693", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncapabilities: fix undefined behavior in bit shift for CAP_TO_MASK\n\nShifting signed 32-bit value by 31 bits is undefined, so changing\nsignificant bit to unsigned. The UBSAN warning calltrace like below:\n\nUBSAN: shift-out-of-bounds in security/commoncap.c:1252:2\nleft shift of 1 by 31 places cannot be represented in type 'int'\nCall Trace:\n <TASK>\n dump_stack_lvl+0x7d/0xa5\n dump_stack+0x15/0x1b\n ubsan_epilogue+0xe/0x4e\n __ubsan_handle_shift_out_of_bounds+0x1e7/0x20c\n cap_task_prctl+0x561/0x6f0\n security_task_prctl+0x5a/0xb0\n __x64_sys_prctl+0x61/0x8f0\n do_syscall_64+0x58/0x80\n entry_SYSCALL_64_after_hwframe+0x63/0xcd\n </TASK>"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: capacidades: corregir comportamiento indefinido en el cambio de bits para CAP_TO_MASK El cambio de un valor con signo de 32 bits por 31 bits no está definido, por lo que se cambia el bit significativo a sin signo. El seguimiento de llamadas de advertencia de UBSAN como se muestra a continuación: UBSAN: desplazamiento fuera de los límites en security/commoncap.c:1252:2 El desplazamiento a la izquierda de 1 por 31 lugares no se puede representar en el tipo 'int' Seguimiento de llamadas: dump_stack_lvl+0x7d/0xa5 dump_stack+0x15/0x1b ubsan_epilogue+0xe/0x4e __ubsan_handle_shift_out_of_bounds+0x1e7/0x20c cap_task_prctl+0x561/0x6f0 security_task_prctl+0x5a/0xb0 __x64_sys_prctl+0x61/0x8f0 do_syscall_64+0x58/0x80 entry_SYSCALL_64_after_hwframe+0x63/0xcd  "}], "references": [{"url": "https://git.kernel.org/stable/c/151dc8087b5609e53b069c068e3f3ee100efa586", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/27bdb134c043ff32c459d98f16550d0ffa0b3c34", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/46653972e3ea64f79e7f8ae3aa41a4d3fdb70a13", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5661f111a1616ac105ec8cec81bff99b60f847ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5b79fa628e2ab789e629a83cd211ef9b4c1a593e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/65b0bc7a0690861812ade523d19f82688ab819dc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dbaab08c8677d598244d21afb7818e44e1c5d826", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fcbd2b336834bd24e1d9454ad5737856470c10d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2023-53078", "published_date": "2025-05-02T16:15:26.820", "last_modified_date": "2025-05-05T20:54:45.973", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: scsi_dh_alua: Fix memleak for 'qdata' in alua_activate()\n\nIf alua_rtpg_queue() failed from alua_activate(), then 'qdata' is not\nfreed, which will cause following memleak:\n\nunreferenced object 0xffff88810b2c6980 (size 32):\n  comm \"kworker/u16:2\", pid 635322, jiffies 4355801099 (age 1216426.076s)\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n    40 39 24 c1 ff ff ff ff 00 f8 ea 0a 81 88 ff ff  @9$.............\n  backtrace:\n    [<0000000098f3a26d>] alua_activate+0xb0/0x320\n    [<000000003b529641>] scsi_dh_activate+0xb2/0x140\n    [<000000007b296db3>] activate_path_work+0xc6/0xe0 [dm_multipath]\n    [<000000007adc9ace>] process_one_work+0x3c5/0x730\n    [<00000000c457a985>] worker_thread+0x93/0x650\n    [<00000000cb80e628>] kthread+0x1ba/0x210\n    [<00000000a1e61077>] ret_from_fork+0x22/0x30\n\nFix the problem by freeing 'qdata' in error path."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: scsi_dh_alua: Se corrige la fuga de memoria para 'qdata' en alua_activate(). Si alua_rtpg_queue() falla desde alua_activate(), entonces 'qdata' no se libera, lo que causará la siguiente fuga de memoria: objeto sin referencia 0xffff88810b2c6980 (tamaño 32): comm \"kworker/u16:2\", pid 635322, jiffies 4355801099 (edad 1216426.076s) volcado hexadecimal (primeros 32 bytes): 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ 40 39 24 c1 ff ff ff ff 00 f8 ea 0a 81 88 ff ff @9$............. backtrace: [&lt;0000000098f3a26d&gt;] alua_activate+0xb0/0x320 [&lt;000000003b529641&gt;] scsi_dh_activate+0xb2/0x140 [&lt;000000007b296db3&gt;] activate_path_work+0xc6/0xe0 [dm_multipath] [&lt;000000007adc9ace&gt;] process_one_work+0x3c5/0x730 [&lt;00000000c457a985&gt;] worker_thread+0x93/0x650 [&lt;00000000cb80e628&gt;] kthread+0x1ba/0x210 [&lt;00000000a1e61077&gt;] ret_from_fork+0x22/0x30 Solucione el problema liberando 'qdata' en la ruta de error."}], "references": [{"url": "https://git.kernel.org/stable/c/0d89254a4320eb7de0970c478172f764125c6355", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/123483df146492ca22b503ae6dacc2ce7c3a3974", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1c55982beb80c7d3c30278fc6cfda8496a31dbe6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5c4d71424df34fc23dc5336d09394ce68c849542", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9311e7a554dffd3823499e309a8b86a5cd1540e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a13faca032acbf2699293587085293bdfaafc8ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c09cdf6eb815ee35e55d6c50ac7f63db58bd20b8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c110051d335ef7f62ad33474b0c23997fee5bfb5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
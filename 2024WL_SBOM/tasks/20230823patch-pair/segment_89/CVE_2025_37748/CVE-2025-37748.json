{"cve_id": "CVE-2025-37748", "published_date": "2025-05-01T13:15:53.523", "last_modified_date": "2025-05-02T13:53:20.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niommu/mediatek: Fix NULL pointer deference in mtk_iommu_device_group\n\nCurrently, mtk_iommu calls during probe iommu_device_register before\nthe hw_list from driver data is initialized. Since iommu probing issue\nfix, it leads to NULL pointer dereference in mtk_iommu_device_group when\nhw_list is accessed with list_first_entry (not null safe).\n\nSo, change the call order to ensure iommu_device_register is called\nafter the driver data are initialized."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iommu/mediatek: Se ha corregido la deferencia de puntero nulo en mtk_iommu_device_group. Actualmente, mtk_iommu invoca durante el sondeo de iommu_device_register antes de que se inicialice hw_list de los datos del controlador. Desde la corrección del problema de sondeo de iommu, este provoca la desreferencia de puntero nulo en mtk_iommu_device_group cuando se accede a hw_list con list_first_entry (no es seguro para nulos). Por lo tanto, se debe modificar el orden de las llamadas para garantizar que iommu_device_register se invoque después de inicializar los datos del controlador."}], "references": [{"url": "https://git.kernel.org/stable/c/2f75cb27bef43c8692b0f5e471e5632f6a9beb99", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/38e8844005e6068f336a3ad45451a562a0040ca1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/69f9d2d37d1207c5a73dac52a4ce1361ead707f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6abd09bed43b8d83d461e0fb5b9a200a06aa8a27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a0842539e8ef9386c070156103aff888e558a60c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ce7d3b2f6f393fa35f0ea12861b83a1ca28b295c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37924", "published_date": "2025-05-20T16:15:29.037", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix use-after-free in kerberos authentication\n\nSetting sess->user = NULL was introduced to fix the dangling pointer\ncreated by ksmbd_free_user. However, it is possible another thread could\nbe operating on the session and make use of sess->user after it has been\npassed to ksmbd_free_user but before sess->user is set to NULL."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: corrección del use-after-free en la autenticación Kerberos. Se introdujo la configuración sess-&gt;user = NULL para corregir el puntero colgante creado por ksmbd_free_user. Sin embargo, es posible que otro hilo esté operando en la sesión y utilice sess-&gt;user después de que se haya pasado a ksmbd_free_user, pero antes de que sess-&gt;user se establezca en NULL."}], "references": [{"url": "https://git.kernel.org/stable/c/28c756738af44a404a91b77830d017bb0c525890", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b447463562238428503cfba1c913261047772f90", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e18c616718018dfc440e4a2d2b94e28fe91b1861", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e34a33d5d7e87399af0a138bb32f6a3e95dd83d2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e86e9134e1d1c90a960dd57f59ce574d27b9a124", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
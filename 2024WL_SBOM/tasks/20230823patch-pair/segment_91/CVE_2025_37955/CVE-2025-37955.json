{"cve_id": "CVE-2025-37955", "published_date": "2025-05-20T16:15:33.710", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nvirtio-net: free xsk_buffs on error in virtnet_xsk_pool_enable()\n\nThe selftests added to our CI by <PERSON><PERSON> recently reveals\nthat there is a mem leak on the error path of virtnet_xsk_pool_enable():\n\nunreferenced object 0xffff88800a68a000 (size 2048):\n  comm \"xdp_helper\", pid 318, jiffies 4294692778\n  hex dump (first 32 bytes):\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................\n  backtrace (crc 0):\n    __kvmalloc_node_noprof+0x402/0x570\n    virtnet_xsk_pool_enable+0x293/0x6a0 (drivers/net/virtio_net.c:5882)\n    xp_assign_dev+0x369/0x670 (net/xdp/xsk_buff_pool.c:226)\n    xsk_bind+0x6a5/0x1ae0\n    __sys_bind+0x15e/0x230\n    __x64_sys_bind+0x72/0xb0\n    do_syscall_64+0xc1/0x1d0\n    entry_SYSCALL_64_after_hwframe+0x77/0x7f"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: virtio-net: xsk_buffs libres en caso de error en virtnet_xsk_pool_enable() Las autopruebas añadidas a nuestra CI por <PERSON><PERSON><PERSON> revelan recientemente que hay una fuga de memoria en la ruta de error de virtnet_xsk_pool_enable(): objeto sin referencia 0xffff88800a68a000 (tamaño 2048): comm \"xdp_helper\", pid 318, jiffies 4294692778 volcado hexadecimal (primeros 32 bytes): 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................ seguimiento inverso (crc 0): __kvmalloc_node_noprof+0x402/0x570 virtnet_xsk_pool_enable+0x293/0x6a0 (drivers/net/virtio_net.c:5882) xp_assign_dev+0x369/0x670 (net/xdp/xsk_buff_pool.c:226) xsk_bind+0x6a5/0x1ae0 __sys_bind+0x15e/0x230 __x64_sys_bind+0x72/0xb0 do_syscall_64+0xc1/0x1d0 entry_SYSCALL_64_after_hwframe+0x77/0x7f"}], "references": [{"url": "https://git.kernel.org/stable/c/4397684a292a71fbc1e815c3e283f7490ddce5ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/94a6f6c204abb2b2dcd2ce287536cc924469cfb5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ba6917810bb4a5a32661fa941717399052b3f0d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-39348", "published_date": "2025-05-19T20:15:22.740", "last_modified_date": "2025-05-29T21:25:34.930", "descriptions": [{"lang": "en", "value": "Deserialization of Untrusted Data vulnerability in ThemeGoods Grand Restaurant WordPress allows Object Injection.This issue affects Grand Restaurant WordPress: from n/a through 7.0."}, {"lang": "es", "value": "La vulnerabilidad de deserialización de datos no confiables en ThemeGoods Grand Restaurant WordPress permite la inyección de objetos. Este problema afecta a Grand Restaurant WordPress: desde n/a hasta 7.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/grandrestaurant/vulnerability/wordpress-grand-restaurant-wordpress-theme-7-0-php-object-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
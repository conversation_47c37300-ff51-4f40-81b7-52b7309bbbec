{"cve_id": "CVE-2024-9182", "published_date": "2025-05-15T20:16:00.140", "last_modified_date": "2025-06-12T16:36:53.693", "descriptions": [{"lang": "en", "value": "The Maspik  WordPress plugin before 2.1.3 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Cross-Site Scripting attacks even when unfiltered_html is disallowed."}, {"lang": "es", "value": "El complemento Maspik para WordPress anterior a la versión 2.1.3 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting incluso cuando unfiltered_html no está permitido."}], "references": [{"url": "https://wpscan.com/vulnerability/40007323-d684-430d-a882-8b4dfb76172b/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-8493", "published_date": "2025-05-15T20:15:58.823", "last_modified_date": "2025-06-04T20:08:55.440", "descriptions": [{"lang": "en", "value": "The Events Calendar WordPress plugin before 6.6.4 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Events Calendar de WordPress anterior a la versión 6.6.4 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/561b3185-501a-4a75-b880-226b159c0431/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
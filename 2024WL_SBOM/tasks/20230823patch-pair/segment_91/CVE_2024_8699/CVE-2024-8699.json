{"cve_id": "CVE-2024-8699", "published_date": "2025-05-15T20:15:59.467", "last_modified_date": "2025-05-28T15:42:01.943", "descriptions": [{"lang": "en", "value": "The Z-Downloads WordPress plugin before 1.11.5 does not properly validate files uploaded, allowing high privilege users such as admin to upload arbitrary files on the server even when they should not be allowed to (for example in multisite setup)"}, {"lang": "es", "value": "El complemento Z-Downloads de WordPress anterior a la versión 1.11.5 no valida correctamente los archivos cargados, lo que permite que usuarios con privilegios elevados, como el administrador, carguen archivos arbitrarios en el servidor incluso cuando no deberían tener permiso para hacerlo (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/9013351e-224f-4696-970f-eb843dc8dace/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
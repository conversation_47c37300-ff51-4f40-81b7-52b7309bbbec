{"cve_id": "CVE-2025-37988", "published_date": "2025-05-20T18:15:45.673", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfix a couple of races in MNT_TREE_BENEATH handling by do_move_mount()\n\nNormally do_lock_mount(path, _) is locking a mountpoint pinned by\n*path and at the time when matching unlock_mount() unlocks that\nlocation it is still pinned by the same thing.\n\nUnfortunately, for 'beneath' case it's no longer that simple -\nthe object being locked is not the one *path points to.  It's the\nmountpoint of path->mnt.  The thing is, without sufficient locking\n->mnt_parent may change under us and none of the locks are held\nat that point.  The rules are\n\t* mount_lock stabilizes m->mnt_parent for any mount m.\n\t* namespace_sem stabilizes m->mnt_parent, provided that\nm is mounted.\n\t* if either of the above holds and refcount of m is positive,\nwe are guaranteed the same for refcount of m->mnt_parent.\n\nnamespace_sem nests inside inode_lock(), so do_lock_mount() has\nto take inode_lock() before grabbing namespace_sem.  It does\nrecheck that path->mnt is still mounted in the same place after\ngetting namespace_sem, and it does take care to pin the dentry.\nIt is needed, since otherwise we might end up with racing mount --move\n(or umount) happening while we were getting locks; in that case\ndentry would no longer be a mountpoint and could've been evicted\non memory pressure along with its inode - not something you want\nwhen grabbing lock on that inode.\n\nHowever, pinning a dentry is not enough - the matching mount is\nalso pinned only by the fact that path->mnt is mounted on top it\nand at that point we are not holding any locks whatsoever, so\nthe same kind of races could end up with all references to\nthat mount gone just as we are about to enter inode_lock().\nIf that happens, we are left with filesystem being shut down while\nwe are holding a dentry reference on it; results are not pretty.\n\nWhat we need to do is grab both dentry and mount at the same time;\nthat makes inode_lock() safe *and* avoids the problem with fs getting\nshut down under us.  After taking namespace_sem we verify that\npath->mnt is still mounted (which stabilizes its ->mnt_parent) and\ncheck that it's still mounted at the same place.  From that point\non to the matching namespace_unlock() we are guaranteed that\nmount/dentry pair we'd grabbed are also pinned by being the mountpoint\nof path->mnt, so we can quietly drop both the dentry reference (as\nthe current code does) and mnt one - it's OK to do under namespace_sem,\nsince we are not dropping the final refs.\n\nThat solves the problem on do_lock_mount() side; unlock_mount()\nalso has one, since dentry is guaranteed to stay pinned only until\nthe namespace_unlock().  That's easy to fix - just have inode_unlock()\ndone earlier, while it's still pinned by mp->m_dentry."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: corrige un par de ejecucións en el manejo de MNT_TREE_BENEATH por do_move_mount() Normalmente do_lock_mount(path, _) está bloqueando un punto de montaje fijado por *path y en el momento en que la coincidencia de unlock_mount() desbloquea esa ubicación, todavía está fijado por la misma cosa. Desafortunadamente, para el caso 'debajo' ya no es tan simple: el objeto que se bloquea no es el que *path apunta. Es el punto de montaje de path-&gt;mnt. El problema es que, sin un bloqueo suficiente, -&gt;mnt_parent puede cambiar debajo de nosotros y ninguno de los bloqueos se mantiene en ese punto. Las reglas son * mount_lock estabiliza m-&gt;mnt_parent para cualquier montaje m. * namespace_sem estabiliza m-&gt;mnt_parent, siempre que m esté montado. * si se cumple alguna de las anteriores y refcount de m es positivo, se nos garantiza lo mismo para refcount de m-&gt;mnt_parent. namespace_sem se anida dentro de inode_lock(), por lo que do_lock_mount() debe tomar inode_lock() antes de obtener namespace_sem. Vuelve a comprobar que path-&gt;mnt siga montado en el mismo lugar después de obtener namespace_sem y se encarga de fijar el dentry. Esto es necesario, ya que, de lo contrario, podríamos terminar con una ejecución de mount --move (o umount) mientras obteníamos bloqueos; en ese caso, el dentry dejaría de ser un punto de montaje y podría haber sido expulsado por presión de memoria junto con su inodo, algo que no se desea al obtener el bloqueo de ese inodo. Sin embargo, fijar un dentry no es suficiente; el montaje correspondiente también está fijado solo por el hecho de que path-&gt;mnt está montado sobre él y, en ese momento, no tenemos ningún bloqueo. Por lo tanto, el mismo tipo de ejecución podría terminar con todas las referencias a ese montaje eliminadas justo cuando estamos a punto de entrar en inode_lock(). Si esto ocurre, el sistema de archivos se apaga mientras mantenemos una referencia dentry; los resultados no son muy alentadores. Necesitamos obtener dentry y mount simultáneamente; esto hace que inode_lock() sea seguro *y* evita el problema de que el sistema de archivos se apague bajo nuestra supervisión. Después de obtener namespace_sem, verificamos que path-&gt;mnt siga montado (lo que estabiliza su -&gt;mnt_parent) y comprobamos que siga montado en el mismo lugar. Desde ese punto hasta la ejecución correspondiente de namespace_unlock(), tenemos la garantía de que el par mount/dentry que obtuvimos también está fijado al ser el punto de montaje de path-&gt;mnt, por lo que podemos eliminar discretamente tanto la referencia dentry (como hace el código actual) como la de mnt; esto es correcto en namespace_sem, ya que no eliminamos las referencias finales. Esto resuelve el problema en do_lock_mount(); unlock_mount() también tiene uno, ya que se garantiza que dentry permanecerá fijado solo hasta la ejecución de namespace_unlock(). Esto es fácil de solucionar: solo hay que hacer inode_unlock() antes, mientras todavía está fijado por mp-&gt;m_dentry."}], "references": [{"url": "https://git.kernel.org/stable/c/0d039eac6e5950f9d1ecc9e410c2fd1feaeab3b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4f435c1f4c48ff84968e2d9159f6fa41f46cf998", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a61afd54826ac24c2c93845c4f441dbc344875b1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d4b21e8cd3d7efa2deb9cff534f0133e84f35086", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
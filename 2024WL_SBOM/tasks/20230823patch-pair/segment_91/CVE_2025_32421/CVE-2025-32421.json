{"cve_id": "CVE-2025-32421", "published_date": "2025-05-14T23:15:47.870", "last_modified_date": "2025-05-16T14:43:26.160", "descriptions": [{"lang": "en", "value": "Next.js is a React framework for building full-stack web applications. Versions prior to 14.2.24 and 15.1.6 have a race-condition vulnerability. This issue only affects the Pages Router under certain misconfigurations, causing normal endpoints to serve `pageProps` data instead of standard HTML. This issue was patched in versions 15.1.6 and 14.2.24 by stripping the `x-now-route-matches` header from incoming requests. Applications hosted on Vercel's platform are not affected by this issue, as the platform does not cache responses based solely on `200 OK` status without explicit `cache-control` headers. Those who self-host Next.js deployments and are unable to upgrade immediately can mitigate this vulnerability by stripping the `x-now-route-matches` header from all incoming requests at the content development network and setting `cache-control: no-store` for all responses under risk. The maintainers of Next.js strongly recommend only caching responses with explicit cache-control headers."}, {"lang": "es", "value": "Next.js es un framework de React para crear aplicaciones web full-stack. Las versiones anteriores a la 14.2.24 y la 15.1.6 presentan una vulnerabilidad de condición de ejecución. Este problema solo afecta a Pages Router con ciertas configuraciones incorrectas, lo que provoca que los endpoints normales muestren datos `pageProps` en lugar de HTML estándar. Este problema se solucionó en las versiones 15.1.6 y 14.2.24 eliminando el encabezado `x-now-route-matches` de las solicitudes entrantes. Las aplicaciones alojadas en la plataforma Vercel no se ven afectadas, ya que la plataforma no almacena en caché las respuestas basándose únicamente en el estado `200 OK` sin encabezados `cache-control` explícitos. Quienes alojan implementaciones de Next.js en sus propias instalaciones y no pueden actualizar inmediatamente pueden mitigar esta vulnerabilidad eliminando el encabezado `x-now-route-matches` de todas las solicitudes entrantes en la red de desarrollo de contenido y configurando `cache-control: no-store` para todas las respuestas en riesgo. Los mantenedores de Next.js recomiendan encarecidamente almacenar en caché únicamente las respuestas con encabezados de control de caché explícitos."}], "references": [{"url": "https://github.com/vercel/next.js/security/advisories/GHSA-qpjv-v59x-3qc4", "source": "<EMAIL>", "tags": []}, {"url": "https://vercel.com/changelog/cve-2025-32421", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37903", "published_date": "2025-05-20T16:15:26.683", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/display: Fix slab-use-after-free in hdcp\n\nThe HDCP code in amdgpu_dm_hdcp.c copies pointers to amdgpu_dm_connector\nobjects without incrementing the kref reference counts. When using a\nUSB-C dock, and the dock is unplugged, the corresponding\namdgpu_dm_connector objects are freed, creating dangling pointers in the\nHDCP code. When the dock is plugged back, the dangling pointers are\ndereferenced, resulting in a slab-use-after-free:\n\n[   66.775837] BUG: KASAN: slab-use-after-free in event_property_validate+0x42f/0x6c0 [amdgpu]\n[   66.776171] Read of size 4 at addr ffff888127804120 by task kworker/0:1/10\n\n[   66.776179] CPU: 0 UID: 0 PID: 10 Comm: kworker/0:1 Not tainted 6.14.0-rc7-00180-g54505f727a38-dirty #233\n[   66.776183] Hardware name: HP HP Pavilion Aero Laptop 13-be0xxx/8916, BIOS F.17 12/18/2024\n[   66.776186] Workqueue: events event_property_validate [amdgpu]\n[   66.776494] Call Trace:\n[   66.776496]  <TASK>\n[   66.776497]  dump_stack_lvl+0x70/0xa0\n[   66.776504]  print_report+0x175/0x555\n[   66.776507]  ? __virt_addr_valid+0x243/0x450\n[   66.776510]  ? kasan_complete_mode_report_info+0x66/0x1c0\n[   66.776515]  kasan_report+0xeb/0x1c0\n[   66.776518]  ? event_property_validate+0x42f/0x6c0 [amdgpu]\n[   66.776819]  ? event_property_validate+0x42f/0x6c0 [amdgpu]\n[   66.777121]  __asan_report_load4_noabort+0x14/0x20\n[   66.777124]  event_property_validate+0x42f/0x6c0 [amdgpu]\n[   66.777342]  ? __lock_acquire+0x6b40/0x6b40\n[   66.777347]  ? enable_assr+0x250/0x250 [amdgpu]\n[   66.777571]  process_one_work+0x86b/0x1510\n[   66.777575]  ? pwq_dec_nr_in_flight+0xcf0/0xcf0\n[   66.777578]  ? assign_work+0x16b/0x280\n[   66.777580]  ? lock_is_held_type+0xa3/0x130\n[   66.777583]  worker_thread+0x5c0/0xfa0\n[   66.777587]  ? process_one_work+0x1510/0x1510\n[   66.777588]  kthread+0x3a2/0x840\n[   66.777591]  ? kthread_is_per_cpu+0xd0/0xd0\n[   66.777594]  ? trace_hardirqs_on+0x4f/0x60\n[   66.777597]  ? _raw_spin_unlock_irq+0x27/0x60\n[   66.777599]  ? calculate_sigpending+0x77/0xa0\n[   66.777602]  ? kthread_is_per_cpu+0xd0/0xd0\n[   66.777605]  ret_from_fork+0x40/0x90\n[   66.777607]  ? kthread_is_per_cpu+0xd0/0xd0\n[   66.777609]  ret_from_fork_asm+0x11/0x20\n[   66.777614]  </TASK>\n\n[   66.777643] Allocated by task 10:\n[   66.777646]  kasan_save_stack+0x39/0x60\n[   66.777649]  kasan_save_track+0x14/0x40\n[   66.777652]  kasan_save_alloc_info+0x37/0x50\n[   66.777655]  __kasan_kmalloc+0xbb/0xc0\n[   66.777658]  __kmalloc_cache_noprof+0x1c8/0x4b0\n[   66.777661]  dm_dp_add_mst_connector+0xdd/0x5c0 [amdgpu]\n[   66.777880]  drm_dp_mst_port_add_connector+0x47e/0x770 [drm_display_helper]\n[   66.777892]  drm_dp_send_link_address+0x1554/0x2bf0 [drm_display_helper]\n[   66.777901]  drm_dp_check_and_send_link_address+0x187/0x1f0 [drm_display_helper]\n[   66.777909]  drm_dp_mst_link_probe_work+0x2b8/0x410 [drm_display_helper]\n[   66.777917]  process_one_work+0x86b/0x1510\n[   66.777919]  worker_thread+0x5c0/0xfa0\n[   66.777922]  kthread+0x3a2/0x840\n[   66.777925]  ret_from_fork+0x40/0x90\n[   66.777927]  ret_from_fork_asm+0x11/0x20\n\n[   66.777932] Freed by task 1713:\n[   66.777935]  kasan_save_stack+0x39/0x60\n[   66.777938]  kasan_save_track+0x14/0x40\n[   66.777940]  kasan_save_free_info+0x3b/0x60\n[   66.777944]  __kasan_slab_free+0x52/0x70\n[   66.777946]  kfree+0x13f/0x4b0\n[   66.777949]  dm_dp_mst_connector_destroy+0xfa/0x150 [amdgpu]\n[   66.778179]  drm_connector_free+0x7d/0xb0\n[   66.778184]  drm_mode_object_put.part.0+0xee/0x160\n[   66.778188]  drm_mode_object_put+0x37/0x50\n[   66.778191]  drm_atomic_state_default_clear+0x220/0xd60\n[   66.778194]  __drm_atomic_state_free+0x16e/0x2a0\n[   66.778197]  drm_mode_atomic_ioctl+0x15ed/0x2ba0\n[   66.778200]  drm_ioctl_kernel+0x17a/0x310\n[   66.778203]  drm_ioctl+0x584/0xd10\n[   66.778206]  amdgpu_drm_ioctl+0xd2/0x1c0 [amdgpu]\n[   66.778375]  __x64_sys_ioctl+0x139/0x1a0\n[   66.778378]  x64_sys_call+0xee7/0xfb0\n[   66.778381] \n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/display: Se corrige el problema de slab-use-after-free en HDCP. El código HDCP en amdgpu_dm_hdcp.c copia punteros a objetos amdgpu_dm_connector sin incrementar el recuento de referencias de kref. Al usar una base USB-C y desconectarla, se liberan los objetos amdgpu_dm_connector correspondientes, lo que crea punteros colgantes en el código HDCP. Cuando se vuelve a conectar el dock, los punteros colgantes se desreferencian, lo que da como resultado un slab-use-after-free: [ 66.775837] ERROR: KASAN: slab-use-after-free en event_property_validate+0x42f/0x6c0 [amdgpu] [ 66.776171] Lectura de tamaño 4 en la dirección ffff888127804120 por la tarea kworker/0:1/10 [ 66.776179] CPU: 0 UID: 0 PID: 10 Comm: kworker/0:1 No contaminado 6.14.0-rc7-00180-g54505f727a38-dirty #233 [ 66.776183] Nombre del hardware: HP HP Pavilion Aero Laptop 13-be0xxx/8916, BIOS F.17 18/12/2024 [ 66.776186] Cola de trabajo: eventos event_property_validate [amdgpu] [ 66.776494] Seguimiento de llamadas: [ 66.776496]   [ 66.776497] dump_stack_lvl+0x70/0xa0 [ 66.776504] print_report+0x175/0x555 [ 66.776507] ? __virt_addr_valid+0x243/0x450 [ 66.776510] ? kasan_complete_mode_report_info+0x66/0x1c0 [ 66.776515] kasan_report+0xeb/0x1c0 [ 66.776518] ? event_property_validate+0x42f/0x6c0 [amdgpu] [ 66.776819] ? event_property_validate+0x42f/0x6c0 [amdgpu] [ 66.777121] __asan_report_load4_noabort+0x14/0x20 [ 66.777124] event_property_validate+0x42f/0x6c0 [amdgpu] [ 66.777342] ? __lock_acquire+0x6b40/0x6b40 [ 66.777347] ? enable_assr+0x250/0x250 [amdgpu] [ 66.777571] process_one_work+0x86b/0x1510 [ 66.777575] ? pwq_dec_nr_in_flight+0xcf0/0xcf0 [ 66.777578] ? assign_work+0x16b/0x280 [ 66.777580] ? lock_is_held_type+0xa3/0x130 [ 66.777583] worker_thread+0x5c0/0xfa0 [ 66.777587] ? process_one_work+0x1510/0x1510 [ 66.777588] kthread+0x3a2/0x840 [ 66.777591] ? kthread_is_per_cpu+0xd0/0xd0 [ 66.777594] ? trace_hardirqs_on+0x4f/0x60 [ 66.777597] ? _raw_spin_unlock_irq+0x27/0x60 [ 66.777599] ? calculate_sigpending+0x77/0xa0 [ 66.777602] ? kthread_is_per_cpu+0xd0/0xd0 [ 66.777605] ret_from_fork+0x40/0x90 [ 66.777607] ? kthread_is_per_cpu+0xd0/0xd0 [ 66.777609] ret_from_fork_asm+0x11/0x20 [ 66.777614]  [ 66.777643] Asignado por la tarea 10: [ 66.777646] kasan_save_stack+0x39/0x60 [ 66.777649] kasan_save_track+0x14/0x40 [ 66.777652] kasan_save_alloc_info+0x37/0x50 [ 66.777655] __kasan_kmalloc+0xbb/0xc0 [ 66.777658] __kmalloc_cache_noprof+0x1c8/0x4b0 [ 66.777661] dm_dp_add_mst_connector+0xdd/0x5c0 [amdgpu] [ 66.777880] drm_dp_mst_port_add_connector+0x47e/0x770 [drm_display_helper] [ 66.777892] drm_dp_send_link_address+0x1554/0x2bf0 [drm_display_helper] [ 66.777901] drm_dp_check_and_send_link_address+0x187/0x1f0 [drm_display_helper] [ 66.777909] drm_dp_mst_link_probe_work+0x2b8/0x410 [drm_display_helper] [ 66.777917] process_one_work+0x86b/0x1510 [ 66.777919] work_thread+0x5c0/0xfa0 [ 66.777922] kthread+0x3a2/0x840 [ 66.777925] ret_from_fork+0x40/0x90 [ 66.777927] ret_from_fork_asm+0x11/0x20 [ 66.777932] Liberado por la tarea 1713: [ 66.777935] kasan_save_stack+0x39/0x60 [ 66.777938] kasan_save_track+0x14/0x40 [ 66.777940] kasan_save_free_info+0x3b/0x60 [ 66.777944] __kasan_slab_free+0x52/0x70 [ 66.777946] kfree+0x13f/0x4b0 [ 66.777949] dm_dp_mst_connector_destroy+0xfa/0x150 [amdgpu] [ 66.778179] drm_connector_free+0x7d/0xb0 [ 66.778184] drm_mode_object_put.part.0+0xee/0x160 [ 66.778188] drm_mode_object_put+0x37/0x50 [ 66.778191] drm_atomic_state_default_clear+0x220/0xd60 [ 66.778194] __drm_atomic_state_free+0x16e/0x2a0 [ 66.778197] drm_mode_atomic_ioctl+0x15ed/0x2ba0 [ 66.778200] drm_ioctl_kernel+0x17a/0x310 [ 66.778203] drm_ioctl+0x584/0xd10 [ 66.778206] amdgpu_drm_ioctl+0xd2/0x1c0 [amdgpu] [ 66.778375] __x64_sys_ioctl+0x139/0x1a0 [ 66.778378] x64_sys_call+0xee7/0xfb0 [ 66.778381] ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/3a782a83d130ceac6c98a87639ddd89640bff486", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bbc66abcd297be67e3d835276e21e6fdc65205a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be593d9d91c5a3a363d456b9aceb71029aeb3f1d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dd329f04dda35a66e0c9ed462ba91bd5f2c8be70", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e25139c4aa5621f2db8e86688c33546cdd885e42", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
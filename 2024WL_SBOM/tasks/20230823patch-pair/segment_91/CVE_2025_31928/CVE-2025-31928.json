{"cve_id": "CVE-2025-31928", "published_date": "2025-05-16T16:15:38.473", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Multimedia Responsive Carousel with Image Video Audio Support allows SQL Injection. This issue affects Multimedia Responsive Carousel with Image Video Audio Support: from n/a through 2.6.0."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Multimedia Responsive Carousel with Image Video Audio Support permite la inyección SQL. Este problema afecta al Carrusel Multimedia Responsive con compatibilidad con imágenes, vídeo y audio: desde n/d hasta la versión 2.6.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/multimedia-carousel/vulnerability/wordpress-multimedia-responsive-carousel-with-image-video-audio-support-2-6-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
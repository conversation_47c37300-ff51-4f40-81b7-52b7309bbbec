{"cve_id": "CVE-2025-3527", "published_date": "2025-05-17T12:15:24.810", "last_modified_date": "2025-06-04T20:10:33.153", "descriptions": [{"lang": "en", "value": "The EventON Pro plugin for WordPress is vulnerable to unauthorized modification of data due to a missing capability check in the 'assets/lib/settings/settings.js' file in all versions up to, and including, 4.9.6. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. The vulnerability was partially patched in version 4.9.6."}, {"lang": "es", "value": "El complemento EventON Pro para WordPress es vulnerable a la modificación no autorizada de datos debido a la falta de una comprobación de capacidad en el archivo 'assets/lib/settings/settings.js' en todas las versiones hasta la 4.9.6 incluida. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada. La vulnerabilidad se corrigió parcialmente en la versión 4.9.6."}], "references": [{"url": "https://codecanyon.net/item/eventon-wordpress-event-calendar-plugin/1211017#item-description__change-log", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/549ca9cf-0183-4c19-9bd5-b6d55a69df31?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
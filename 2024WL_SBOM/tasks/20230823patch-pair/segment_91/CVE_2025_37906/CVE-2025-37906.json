{"cve_id": "CVE-2025-37906", "published_date": "2025-05-20T16:15:27.070", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nublk: fix race between io_uring_cmd_complete_in_task and ublk_cancel_cmd\n\nublk_cancel_cmd() calls io_uring_cmd_done() to complete uring_cmd, but\nwe may have scheduled task work via io_uring_cmd_complete_in_task() for\ndispatching request, then kernel crash can be triggered.\n\nFix it by not trying to canceling the command if ublk block request is\nstarted."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ublk: se corrige la ejecución entre io_uring_cmd_complete_in_task y ublk_cancel_cmd. ublk_cancel_cmd() llama a io_uring_cmd_done() para completar uring_cmd, pero es posible que hayamos programado el trabajo de la tarea mediante io_uring_cmd_complete_in_task() para el envío de la solicitud, lo que puede provocar un fallo del kernel. Para solucionarlo, no intente cancelar el comando si se inicia la solicitud de bloqueo de ublk."}], "references": [{"url": "https://git.kernel.org/stable/c/f40139fde5278d81af3227444fd6e76a76b9506d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fb2eb9ddf556f93fef45201e1f9d2b8674bcc975", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
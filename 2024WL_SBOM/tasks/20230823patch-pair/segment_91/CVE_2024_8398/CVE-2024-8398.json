{"cve_id": "CVE-2024-8398", "published_date": "2025-05-15T20:15:58.583", "last_modified_date": "2025-06-12T15:43:26.017", "descriptions": [{"lang": "en", "value": "The Simple Nav Archives WordPress plugin through 2.1.3 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin change them via a CSRF attack"}, {"lang": "es", "value": "El complemento Simple Nav Archives de WordPress hasta la versión 2.1.3 no tiene la verificación CSRF activada al actualizar sus configuraciones, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión las cambie mediante un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/f432901f-31dd-433c-91bf-ec19fa61b6d8/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37935", "published_date": "2025-05-20T16:15:30.170", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: ethernet: mtk_eth_soc: fix SER panic with 4GB+ RAM\n\nIf the mtk_poll_rx() function detects the MTK_RESETTING flag, it will\njump to release_desc and refill the high word of the SDP on the 4GB RFB.\nSubsequently, mtk_rx_clean will process an incorrect SDP, leading to a\npanic.\n\nAdd patch from MediaTek's SDK to resolve this."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: ethernet: mtk_eth_soc: corrección del pánico SER con más de 4 GB de RAM. Si la función mtk_poll_rx() detecta el indicador MTK_RESETTING, saltará a release_desc y rellenará la palabra alta del SDP en la RFB de 4 GB. Posteriormente, mtk_rx_clean procesará un SDP incorrecto, lo que provocará un pánico. Se ha añadido un parche del SDK de MediaTek para solucionar esto."}], "references": [{"url": "https://git.kernel.org/stable/c/317013d1ad13524be02d60b9e98f08fbd13f8c14", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/67619cf69dec5d1d7792808dfa548616742dd51d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6e0490fc36cdac696f96e57b61d93b9ae32e0f4c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cb625f783f70dc6614f03612b8e64ad99cb0a13c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
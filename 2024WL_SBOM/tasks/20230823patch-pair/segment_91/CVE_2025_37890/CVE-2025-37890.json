{"cve_id": "CVE-2025-37890", "published_date": "2025-05-16T13:15:52.437", "last_modified_date": "2025-06-04T13:15:26.170", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet_sched: hfsc: Fix a UAF vulnerability in class with netem as child qdisc\n\nAs described in <PERSON><PERSON><PERSON><PERSON>'s report [1], we have a UAF case when an hfsc class\nhas a netem child qdisc. The crux of the issue is that hfsc is assuming\nthat checking for cl->qdisc->q.qlen == 0 guarantees that it hasn't inserted\nthe class in the vttree or eltree (which is not true for the netem\nduplicate case).\n\nThis patch checks the n_active class variable to make sure that the code\nwon't insert the class in the vttree or eltree twice, catering for the\nreentrant case.\n\n[1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net_sched: hfsc: corrige una vulnerabilidad UAF en la clase con netem como qdisc hijo Como se describe en el informe de Gerrard [1], tenemos un caso UAF cuando una clase hfsc tiene una qdisc hijo netem. El quid de la cuestión es que hfsc asume que la comprobación de cl-&gt;qdisc-&gt;q.qlen == 0 garantiza que no ha insertado la clase en vttree o eltree (lo que no es cierto para el caso de duplicado de netem). Este parche comprueba la variable de clase n_active para asegurarse de que el código no inserte la clase en vttree o eltree dos veces, atendiendo al caso reentrante. [1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/141d34391abbb315d68556b7c67ad97885407547", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/273bbcfa53541cde38b2003ad88a59b770306421", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2e7093c7a8aba5d4f8809f271488e5babe75e202", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6082a87af4c52f58150d40dec1716011d871ac21", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8df7d37d626430035b413b97cee18396b3450bef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ac39fd4a757584d78ed062d4f6fd913f83bd98b5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e0cf8ee23e1915431f262a7b2dee0c7a7d699af0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3e949a39a91d1f829a4890e7dfe9417ac72e4d0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
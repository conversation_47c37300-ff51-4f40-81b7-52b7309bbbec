{"cve_id": "CVE-2025-3623", "published_date": "2025-05-14T03:15:33.073", "last_modified_date": "2025-06-13T14:15:21.147", "descriptions": [{"lang": "en", "value": "The Uncanny Automator plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, ******* via deserialization of untrusted input in the automator_api_decode_message() function. This makes it possible for unauthenticated to inject a PHP Object. The additional presence of a POP chain allows attackers to delete arbitrary files."}, {"lang": "es", "value": "El complemento Uncanny Automator para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la ******* incluida, mediante la deserialización de entradas no confiables en la función automator_api_decode_message(). Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar un objeto PHP. La presencia adicional de una cadena POP permite a los atacantes eliminar archivos arbitrarios."}], "references": [{"url": "https://automatorplugin.com/knowledge-base/uncanny-automator-changelog/#6-4-0-2-2025-04-18", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/uncanny-automator/trunk/src/core/lib/helpers/class-automator-recipe-helpers.php#L540", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3276577/uncanny-automator/trunk/src/core/lib/helpers/class-automator-recipe-helpers.php", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/uncanny-automator/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/00bcfd8f-9785-449a-a0ea-16e2583d684a?source=cve", "source": "<EMAIL>", "tags": []}]}
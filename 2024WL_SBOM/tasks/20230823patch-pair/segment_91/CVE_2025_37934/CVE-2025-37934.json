{"cve_id": "CVE-2025-37934", "published_date": "2025-05-20T16:15:30.053", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: simple-card-utils: Fix pointer check in graph_util_parse_link_direction\n\nActually check if the passed pointers are valid, before writing to them.\nThis also fixes a USBAN warning:\nUBSAN: invalid-load in ../sound/soc/fsl/imx-card.c:687:25\nload of value 255 is not a valid value for type '_Bool'\n\nThis is because playback_only is uninitialized and is not written to, as\nthe playback-only property is absent."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: simple-card-utils: Se corrige la comprobación de punteros en graph_util_parse_link_direction. Se comprueba si los punteros pasados son válidos antes de escribir en ellos. Esto también corrige una advertencia de USBAN: UBSAN: carga no válida en ../sound/soc/fsl/imx-card.c:687:25. La carga del valor 255 no es válida para el tipo '_Bool'. Esto se debe a que playback_only no está inicializado y no se escribe en él, ya que la propiedad playback-only no está presente."}], "references": [{"url": "https://git.kernel.org/stable/c/3cc393d2232ec770b5f79bf0673d67702a3536c3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9b5b3088c4d1752253491705919bd7d067964288", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b41a49d5435e0f76da320f231b7252800e8f736f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-3888", "published_date": "2025-05-17T12:15:24.950", "last_modified_date": "2025-06-04T20:10:15.797", "descriptions": [{"lang": "en", "value": "The Jupiter X Core plugin for WordPress is vulnerable to Stored Cross-Site Scripting via SVG File inclusion in all versions up to, and including, 4.8.12 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses the page with the included SVG file."}, {"lang": "es", "value": "El complemento Jupiter X Core para WordPress es vulnerable a Cross-Site Scripting almacenado mediante la inclusión de archivos SVG en todas las versiones hasta la 4.8.12 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a la página con el archivo SVG incluido."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/jupiterx-core/trunk/includes/extensions/raven/includes/modules/inline-svg/widgets/inline-svg.php#L304", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3292376/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f36f1ea5-62f7-48f0-a8d3-a56e0c9915d7?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
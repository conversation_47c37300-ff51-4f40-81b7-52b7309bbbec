{"cve_id": "CVE-2025-37951", "published_date": "2025-05-20T16:15:33.220", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/v3d: Add job to pending list if the reset was skipped\n\nWhen a CL/CSD job times out, we check if the GPU has made any progress\nsince the last timeout. If so, instead of resetting the hardware, we skip\nthe reset and let the timer get rearmed. This gives long-running jobs a\nchance to complete.\n\nHowever, when `timedout_job()` is called, the job in question is removed\nfrom the pending list, which means it won't be automatically freed through\n`free_job()`. Consequently, when we skip the reset and keep the job\nrunning, the job won't be freed when it finally completes.\n\nThis situation leads to a memory leak, as exposed in [1] and [2].\n\nSimilarly to commit 704d3d60fec4 (\"drm/etnaviv: don't block scheduler when\nGPU is still active\"), this patch ensures the job is put back on the\npending list when extending the timeout."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/v3d: Agregar trabajo a la lista de pendientes si se omitió el reinicio Cuando se agota el tiempo de espera de un trabajo CL/CSD, verificamos si la GPU ha progresado desde el último tiempo de espera. Si es así, en lugar de reiniciar el hardware, omitimos el reinicio y dejamos que el temporizador se reactive. Esto les da a los trabajos de larga ejecución la oportunidad de completarse. Sin embargo, cuando se llama a `timedout_job()`, el trabajo en cuestión se elimina de la lista de pendientes, lo que significa que no se liberará automáticamente a través de `free_job()`. En consecuencia, cuando omitimos el reinicio y mantenemos el trabajo en ejecución, el trabajo no se liberará cuando finalmente se complete. Esta situación conduce a una fuga de memoria, como se expone en [1] y [2]. De manera similar a el commit 704d3d60fec4 (\"drm/etnaviv: no bloquee el programador cuando la GPU aún esté activa\"), este parche garantiza que el trabajo se vuelva a colocar en la lista de pendientes al extender el tiempo de espera."}], "references": [{"url": "https://git.kernel.org/stable/c/12125f7d9c15e6d8ac91d10373b2db2f17dcf767", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/35e4079bf1a2570abffce6ababa631afcf8ea0e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/422a8b10ba42097a704d6909ada2956f880246f2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5235b56b7e5449d990d21d78723b1a5e7bb5738e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a5f162727b91e480656da1876247a91f651f76de", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37926", "published_date": "2025-05-20T16:15:29.140", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix use-after-free in ksmbd_session_rpc_open\n\nA UAF issue can occur due to a race condition between\nksmbd_session_rpc_open() and __session_rpc_close().\nAdd rpc_lock to the session to protect it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: se corrige el problema de use-after-free en ksmbd_session_rpc_open. Un problema de UAF puede ocurrir debido a una condición de ejecución entre ksmbd_session_rpc_open() y __session_rpc_close(). Agregue rpc_lock a la sesión para protegerla."}], "references": [{"url": "https://git.kernel.org/stable/c/6323fec65fe54b365961fed260dd579191e46121", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8fb3b6c85b7e3127161623586b62abcc366caa20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a1f46c99d9ea411f9bf30025b912d881d36fc709", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
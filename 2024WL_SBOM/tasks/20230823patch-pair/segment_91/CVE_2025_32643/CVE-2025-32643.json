{"cve_id": "CVE-2025-32643", "published_date": "2025-05-16T16:15:40.057", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in mojoomla WPGYM allows Blind SQL Injection. This issue affects WPGYM: from n/a through 65.0."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en mojoomla WPGYM que permite la inyección SQL ciega. Este problema afecta a WPGYM desde n/d hasta la versión 65.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/gym-management/vulnerability/wordpress-wpgym-plugin-65-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-31926", "published_date": "2025-05-16T16:15:38.340", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Sticky Radio Player allows SQL Injection. This issue affects Sticky Radio Player: from n/a through 3.4."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Sticky Radio Player permite la inyección SQL. Este problema afecta a Sticky Radio Player desde n/d hasta la versión 3.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lbg-audio5-html5-shoutcast_sticky/vulnerability/wordpress-sticky-radio-player-3-4-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
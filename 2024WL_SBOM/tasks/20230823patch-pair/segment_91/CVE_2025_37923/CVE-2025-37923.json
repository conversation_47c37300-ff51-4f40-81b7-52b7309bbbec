{"cve_id": "CVE-2025-37923", "published_date": "2025-05-20T16:15:28.930", "last_modified_date": "2025-06-04T13:15:26.850", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntracing: Fix oob write in trace_seq_to_buffer()\n\nsyzbot reported this bug:\n==================================================================\nBUG: KASAN: slab-out-of-bounds in trace_seq_to_buffer kernel/trace/trace.c:1830 [inline]\nBUG: KASAN: slab-out-of-bounds in tracing_splice_read_pipe+0x6be/0xdd0 kernel/trace/trace.c:6822\nWrite of size 4507 at addr ffff888032b6b000 by task syz.2.320/7260\n\nCPU: 1 UID: 0 PID: 7260 Comm: syz.2.320 Not tainted 6.15.0-rc1-syzkaller-00301-g3bde70a2c827 #0 PREEMPT(full)\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 02/12/2025\nCall Trace:\n <TASK>\n __dump_stack lib/dump_stack.c:94 [inline]\n dump_stack_lvl+0x116/0x1f0 lib/dump_stack.c:120\n print_address_description mm/kasan/report.c:408 [inline]\n print_report+0xc3/0x670 mm/kasan/report.c:521\n kasan_report+0xe0/0x110 mm/kasan/report.c:634\n check_region_inline mm/kasan/generic.c:183 [inline]\n kasan_check_range+0xef/0x1a0 mm/kasan/generic.c:189\n __asan_memcpy+0x3c/0x60 mm/kasan/shadow.c:106\n trace_seq_to_buffer kernel/trace/trace.c:1830 [inline]\n tracing_splice_read_pipe+0x6be/0xdd0 kernel/trace/trace.c:6822\n ....\n==================================================================\n\nIt has been reported that trace_seq_to_buffer() tries to copy more data\nthan PAGE_SIZE to buf. Therefore, to prevent this, we should use the\nsmaller of trace_seq_used(&iter->seq) and PAGE_SIZE as an argument."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rastreo: Se corrige la escritura fuera de los límite en trace_seq_to_buffer() syzbot informó este error: ====================================================================== ERROR: KASAN: slab-out-of-bounds en trace_seq_to_buffer kernel/trace/trace.c:1830 [en línea] ERROR: KASAN: slab-out-of-bounds en tracing_splice_read_pipe+0x6be/0xdd0 kernel/trace/trace.c:6822 Escritura de tamaño 4507 en la dirección ffff888032b6b000 por tarea syz.2.320/7260 CPU: 1 UID: 0 PID: 7260 Comm: syz.2.320 No contaminado 6.15.0-rc1-syzkaller-00301-g3bde70a2c827 #0 PREEMPT(full) Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 02/12/2025 Seguimiento de llamadas:  __dump_stack lib/dump_stack.c:94 [en línea] dump_stack_lvl+0x116/0x1f0 lib/dump_stack.c:120 print_address_description mm/kasan/report.c:408 [en línea] print_report+0xc3/0x670 mm/kasan/report.c:521 kasan_report+0xe0/0x110 mm/kasan/report.c:634 check_region_inline mm/kasan/generic.c:183 [en línea] kasan_check_range+0xef/0x1a0 mm/kasan/generic.c:189 __asan_memcpy+0x3c/0x60 mm/kasan/shadow.c:106 trace_seq_to_buffer kernel/trace/trace.c:1830 [en línea] tracing_splice_read_pipe+0x6be/0xdd0 kernel/trace/trace.c:6822 .... ===================================================================== Se ha informado que trace_seq_to_buffer() intenta copiar más datos que PAGE_SIZE a buf. Por lo tanto, para evitarlo, debemos usar el valor menor entre trace_seq_used(&amp;iter-&gt;seq) y PAGE_SIZE como argumento."}], "references": [{"url": "https://git.kernel.org/stable/c/056ebbddb8faf4ddf83d005454dd78fc25c2d897", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1a3f9482b50b74fa9421bff8ceecfefd0dc06f8f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1f27a3e93b8d674b24b27fcdbc6f72743cd96c0d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/441021e5b3c7d9bd1b963590652c415929f3b157", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/665ce421041890571852422487f4c613d1824ba9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c5d2b66c5ef5037b4b4360e5447605ff00ba1bd4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f4b0174e9f18aaba59ee6ffdaf8827a7f94eb606", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f5178c41bb43444a6008150fe6094497135d07cb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
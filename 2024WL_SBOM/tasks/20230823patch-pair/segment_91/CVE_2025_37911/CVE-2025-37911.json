{"cve_id": "CVE-2025-37911", "published_date": "2025-05-20T16:15:27.610", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbnxt_en: Fix out-of-bound memcpy() during ethtool -w\n\nWhen retrieving the FW coredump using ethtool, it can sometimes cause\nmemory corruption:\n\nBUG: KFENCE: memory corruption in __bnxt_get_coredump+0x3ef/0x670 [bnxt_en]\nCorrupted memory at 0x000000008f0f30e8 [ ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ] (in kfence-#45):\n__bnxt_get_coredump+0x3ef/0x670 [bnxt_en]\nethtool_get_dump_data+0xdc/0x1a0\n__dev_ethtool+0xa1e/0x1af0\ndev_ethtool+0xa8/0x170\ndev_ioctl+0x1b5/0x580\nsock_do_ioctl+0xab/0xf0\nsock_ioctl+0x1ce/0x2e0\n__x64_sys_ioctl+0x87/0xc0\ndo_syscall_64+0x5c/0xf0\nentry_SYSCALL_64_after_hwframe+0x78/0x80\n\n...\n\nThis happens when copying the coredump segment list in\nbnxt_hwrm_dbg_dma_data() with the HWRM_DBG_COREDUMP_LIST FW command.\nThe info->dest_buf buffer is allocated based on the number of coredump\nsegments returned by the FW.  The segment list is then DMA'ed by\nthe FW and the length of the DMA is returned by FW.  The driver then\ncopies this DMA'ed segment list to info->dest_buf.\n\nIn some cases, this DMA length may exceed the info->dest_buf length\nand cause the above BUG condition.  Fix it by capping the copy\nlength to not exceed the length of info->dest_buf.  The extra\nDMA data contains no useful information.\n\nThis code path is shared for the HWRM_DBG_COREDUMP_LIST and the\nHWRM_DBG_COREDUMP_RETRIEVE FW commands.  The buffering is different\nfor these 2 FW commands.  To simplify the logic, we need to move\nthe line to adjust the buffer length for HWRM_DBG_COREDUMP_RETRIEVE\nup, so that the new check to cap the copy length will work for both\ncommands."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bnxt_en: Se corrige memcpy() fuera de los límite durante ethtool -w Al recuperar el volcado de núcleo de FW con ethtool, a veces puede causar corrupción de memoria: ERROR: KFENCE: corrupción de memoria en __bnxt_get_coredump+0x3ef/0x670 [bnxt_en] Memoria corrupta en 0x000000008f0f30e8 [ ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ! ] (en kfence-#45): __bnxt_get_coredump+0x3ef/0x670 [bnxt_es] ethtool_get_dump_data+0xdc/0x1a0 __dev_ethtool+0xa1e/0x1af0 dev_ethtool+0xa8/0x170 dev_ioctl+0x1b5/0x580 sock_do_ioctl+0xab/0xf0 sock_ioctl+0x1ce/0x2e0 __x64_sys_ioctl+0x87/0xc0 do_syscall_64+0x5c/0xf0 entry_SYSCALL_64_after_hwframe+0x78/0x80 ... Esto sucede al copiar la lista de segmentos del volcado de núcleo en bnxt_hwrm_dbg_dma_data() con el comando de firmware HWRM_DBG_COREDUMP_LIST. El búfer info-&gt;dest_buf se asigna según la cantidad de segmentos de volcado de núcleo devueltos por el firmware. El firmware procesa la lista de segmentos mediante DMA y devuelve su longitud. El controlador copia esta lista de segmentos procesada mediante DMA en info-&gt;dest_buf. En algunos casos, esta longitud de DMA puede superar la longitud de info-&gt;dest_buf y causar el error mencionado. Para solucionarlo, limite la longitud de la copia para que no supere la longitud de info-&gt;dest_buf. Los datos DMA adicionales no contienen información útil. Esta ruta de código es compartida por los comandos de firmware HWRM_DBG_COREDUMP_LIST y HWRM_DBG_COREDUMP_RETRIEVE. El almacenamiento en búfer es diferente para estos dos comandos de firmware. Para simplificar la lógica, necesitamos mover la línea para ajustar la longitud del búfer para HWRM_DBG_COREDUMP_RETRIEVE hacia arriba, de modo que la nueva verificación para limitar la longitud de la copia funcione para ambos comandos."}], "references": [{"url": "https://git.kernel.org/stable/c/43292b83424158fa6ec458799f3cb9c54d18c484", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/44807af79efd0d78fa36383dd865ddfe7992c0a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/44d81a9ebf0cad92512e0ffdf7412bfe20db66ec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4d69864915a3a052538e4ba76cd6fd77cfc64ebe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/69b10dd23ab826d0c7f2d9ab311842251978d0c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6b87bd94f34370bbf1dfa59352bed8efab5bf419", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
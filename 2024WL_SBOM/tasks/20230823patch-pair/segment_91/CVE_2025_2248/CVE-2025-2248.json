{"cve_id": "CVE-2025-2248", "published_date": "2025-05-15T20:16:06.067", "last_modified_date": "2025-06-04T20:03:58.247", "descriptions": [{"lang": "en", "value": "The WP-PManager WordPress plugin through 1.2 does not sanitize and escape a parameter before using it in a SQL statement, allowing admins to perform SQL injection attacks"}, {"lang": "es", "value": "El complemento WP-PManager de WordPress hasta la versión 1.2 no depura ni escapa un parámetro antes de usarlo en una declaración SQL, lo que permite a los administradores realizar ataques de inyección SQL."}], "references": [{"url": "https://wpscan.com/vulnerability/b470a277-f5ad-49ff-97dd-4d3ee0269e5a/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
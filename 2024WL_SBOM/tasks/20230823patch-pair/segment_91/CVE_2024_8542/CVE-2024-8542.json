{"cve_id": "CVE-2024-8542", "published_date": "2025-05-15T20:15:58.903", "last_modified_date": "2025-06-04T20:08:44.297", "descriptions": [{"lang": "en", "value": "The Everest Forms  WordPress plugin before ******* does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Everest Forms para WordPress anterior a la versión ******* no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/e5f94dcf-a6dc-4c4c-acb6-1a7ead701053/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
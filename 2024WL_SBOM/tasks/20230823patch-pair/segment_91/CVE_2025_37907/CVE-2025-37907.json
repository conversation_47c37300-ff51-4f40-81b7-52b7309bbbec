{"cve_id": "CVE-2025-37907", "published_date": "2025-05-20T16:15:27.177", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\naccel/ivpu: Fix locking order in ivpu_job_submit\n\nFix deadlock in job submission and abort handling.\nWhen a thread aborts currently executing jobs due to a fault,\nit first locks the global lock protecting submitted_jobs (#1).\n\nAfter the last job is destroyed, it proceeds to release the related context\nand locks file_priv (#2). Meanwhile, in the job submission thread,\nthe file_priv lock (#2) is taken first, and then the submitted_jobs\nlock (#1) is obtained when a job is added to the submitted jobs list.\n\n       CPU0                            CPU1\n       ----                    \t       ----\n  (for example due to a fault)         (jobs submissions keep coming)\n\n  lock(&vdev->submitted_jobs_lock) #1\n  ivpu_jobs_abort_all()\n  job_destroy()\n                                      lock(&file_priv->lock)           #2\n                                      lock(&vdev->submitted_jobs_lock) #1\n  file_priv_release()\n  lock(&vdev->context_list_lock)\n  lock(&file_priv->lock)           #2\n\nThis order of locking causes a deadlock. To resolve this issue,\nchange the order of locking in ivpu_job_submit()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: accel/ivpu: Se corrige el orden de bloqueo en ivpu_job_submit. Se corrige el bloqueo en el envío de trabajos y la gestión de abortos. Cuando un hilo cancela trabajos en ejecución debido a un fallo, primero bloquea el bloqueo global que protege submit_jobs (#1). Tras la destrucción del último trabajo, libera el contexto relacionado y bloquea file_priv (#2). <PERSON><PERSON>ras tanto, en el hilo de envío de trabajos, primero se obtiene el bloqueo file_priv (#2) y, a continuación, el bloqueo submit_jobs (#1) al añadir un trabajo a la lista de trabajos enviados. CPU0 CPU1 ---- ---- (por ejemplo, debido a un fallo) (los envíos de trabajos siguen llegando) lock(&amp;vdev-&gt;submitted_jobs_lock) #1 ivpu_jobs_abort_all() job_destroy() lock(&amp;file_priv-&gt;lock) #2 lock(&amp;vdev-&gt;submitted_jobs_lock) #1 file_priv_release() lock(&amp;vdev-&gt;context_list_lock) lock(&amp;file_priv-&gt;lock) #2 Este orden de bloqueo provoca un interbloqueo. Para solucionar este problema, cambie el orden de bloqueo en ivpu_job_submit()."}], "references": [{"url": "https://git.kernel.org/stable/c/079d2622f8c9e0c380149645fff21d35c59ce6ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab680dc6c78aa035e944ecc8c48a1caab9f39924", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b9b70924a272c2d72023306bc56f521c056212ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
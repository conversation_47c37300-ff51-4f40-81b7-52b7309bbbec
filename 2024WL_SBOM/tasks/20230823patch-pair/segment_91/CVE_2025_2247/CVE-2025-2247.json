{"cve_id": "CVE-2025-2247", "published_date": "2025-05-15T20:16:05.980", "last_modified_date": "2025-06-04T20:04:13.797", "descriptions": [{"lang": "en", "value": "The WP-PManager WordPress plugin through 1.2 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin change them via a CSRF attack"}, {"lang": "es", "value": "El complemento WP-PManager de WordPress hasta la versión 1.2 no tiene una comprobación CSRF activada al actualizar sus configuraciones, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión las cambie mediante un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/3974c5c3-887e-46bd-aad7-4f3169bff6de/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
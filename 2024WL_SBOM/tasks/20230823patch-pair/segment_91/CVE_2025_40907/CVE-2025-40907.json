{"cve_id": "CVE-2025-40907", "published_date": "2025-05-16T13:15:52.683", "last_modified_date": "2025-07-02T01:01:01.290", "descriptions": [{"lang": "en", "value": "FCGI versions 0.44 through 0.82, for Perl, include a vulnerable version of the FastCGI fcgi2 (aka fcgi) library.\n\nThe included FastCGI library is affected by  CVE-2025-23016, causing an integer overflow (and resultant heap-based buffer overflow) via crafted nameLen or valueLen values in data to the IPC socket. This occurs in ReadParams in fcgiapp.c."}, {"lang": "es", "value": "Las versiones 0.44 a 0.82 de FCGI para Perl incluyen una versión vulnerable de la librería FastCGI fcgi2 (también conocida como fcgi). Esta librería está afectada por CVE-2025-23016, lo que provoca un desbordamiento de enteros (y el consiguiente desbordamiento del búfer en el montón) mediante valores nameLen o valueLen manipulados en los datos del socket IPC. Esto ocurre en ReadParams de fcgiapp.c."}], "references": [{"url": "http://www.openwall.com/lists/oss-security/2025/04/23/4", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://github.com/FastCGI-Archives/fcgi2/issues/67", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/FastCGI-Archives/fcgi2/releases/tag/2.4.5", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Release Notes"]}, {"url": "https://github.com/perl-catalyst/FCGI/issues/14", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://patch-diff.githubusercontent.com/raw/FastCGI-Archives/fcgi2/pull/74.patch", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Patch"]}, {"url": "https://www.synacktiv.com/en/publications/cve-2025-23016-exploiting-the-fastcgi-library", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Exploit", "Third Party Advisory"]}]}
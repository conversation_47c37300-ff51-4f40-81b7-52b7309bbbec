{"cve_id": "CVE-2025-37980", "published_date": "2025-05-20T17:15:48.650", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblock: fix resource leak in blk_register_queue() error path\n\nWhen registering a queue fails after blk_mq_sysfs_register() is\nsuccessful but the function later encounters an error, we need\nto clean up the blk_mq_sysfs resources.\n\nAdd the missing blk_mq_sysfs_unregister() call in the error path\nto properly clean up these resources and prevent a memory leak."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bloque: corrección de fuga de recursos en la ruta de error blk_register_queue(). Si el registro de una cola falla después de que blk_mq_sysfs_register() se ejecute correctamente, pero la función detecta un error posteriormente, es necesario limpiar los recursos blk_mq_sysfs. Agregue la llamada blk_mq_sysfs_unregister() faltante en la ruta de error para limpiar correctamente estos recursos y evitar una fuga de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/40f2eb9b531475dd01b683fdaf61ca3cfd03a51e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/41e43134ddda35949974be40520460a12dda3502", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/549cbbd14bbec12469ceb279b79c763c8a24224e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/55a7bb2708f7c7c5b366d4e40916113168a3824c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-1289", "published_date": "2025-05-15T20:16:02.307", "last_modified_date": "2025-06-04T20:06:11.680", "descriptions": [{"lang": "en", "value": "The Plugin Oficial  WordPress plugin through 1.7.3 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Plugin Oficial WordPress hasta la versión 1.7.3 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración multisitio)."}], "references": [{"url": "https://wpscan.com/vulnerability/5a296b59-f305-49a2-88b8-fca998f2c43e/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37971", "published_date": "2025-05-20T17:15:47.630", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nstaging: bcm2835-camera: Initialise dev in v4l2_dev\n\nCommit 42a2f6664e18 (\"staging: vc04_services: Move global g_state to\nvchiq_state\") changed mmal_init to pass dev->v4l2_dev.dev to\nvchiq_mmal_init, however nothing iniitialised dev->v4l2_dev, so we got\na NULL pointer dereference.\n\nSet dev->v4l2_dev.dev during bcm2835_mmal_probe. The device pointer\ncould be passed into v4l2_device_register to set it, however that also\nhas other effects that would need additional changes."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: staging: bcm2835-camera: Inicializar dev en v4l2_dev. el commit 42a2f6664e18 (\"staging: vc04_services: Mover global g_state a vchiq_state\") modificó mmal_init para pasar dev-&gt;v4l2_dev.dev a vchiq_mmal_init. Sin embargo, no se inicializó dev-&gt;v4l2_dev, por lo que se obtuvo una desreferencia de puntero nulo. Se estableció dev-&gt;v4l2_dev.dev durante bcm2835_mmal_probe. El puntero del dispositivo se podría pasar a v4l2_device_register para establecerlo; sin embargo, esto también tiene otros efectos que requerirían cambios adicionales."}], "references": [{"url": "https://git.kernel.org/stable/c/06753f49336ab161ea0e249a0720125b81b7b31b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/98698ca0e58734bc5c1c24e5bbc7429f981cd186", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b70bdd4923e8b8edbacde2af83ca337bb7005261", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
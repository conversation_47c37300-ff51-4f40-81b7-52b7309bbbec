{"cve_id": "CVE-2025-32306", "published_date": "2025-05-16T16:15:39.637", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Radio Player Shoutcast & Icecast WordPress Plugin allows Blind SQL Injection. This issue affects Radio Player Shoutcast & Icecast WordPress Plugin: from n/a through 4.4.6."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Radio Player Shoutcast &amp; Icecast WordPress Plugin permite la inyección SQL ciega. Este problema afecta al plugin de WordPress Radio Player Shoutcast e Icecast desde n/d hasta la versión 4.4.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/audio4-html5/vulnerability/wordpress-radio-player-shoutcast-icecast-wordpress-plugin-4-4-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
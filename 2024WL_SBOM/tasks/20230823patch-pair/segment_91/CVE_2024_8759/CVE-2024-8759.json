{"cve_id": "CVE-2024-8759", "published_date": "2025-05-15T20:15:59.877", "last_modified_date": "2025-06-12T16:33:28.690", "descriptions": [{"lang": "en", "value": "The Nested Pages WordPress plugin before 3.2.9 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Nested Pages de WordPress anterior a la versión 3.2.9 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/3dd41ecb-d0dc-4c23-9e5b-b1f7fbaaddfd/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
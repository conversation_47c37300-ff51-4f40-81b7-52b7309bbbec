{"cve_id": "CVE-2025-30419", "published_date": "2025-05-15T17:15:53.840", "last_modified_date": "2025-05-20T15:49:39.510", "descriptions": [{"lang": "en", "value": "There is a memory corruption vulnerability due to an out of bounds read in GetSymbolBorderRectSize() when using the SymbolEditor in NI Circuit Design Suite.  This vulnerability may result in information disclosure or arbitrary code execution. Successful exploitation requires an attacker to get a user to open a specially crafted .sym file. This vulnerability affects NI Circuit Design Suite 14.3.0 and prior versions."}, {"lang": "es", "value": "Existe una vulnerabilidad de corrupción de memoria debido a una lectura fuera de los límites en GetSymbolBorderRectSize() al usar SymbolEditor en NI Circuit Design Suite. Esta vulnerabilidad puede provocar la divulgación de información o la ejecución de código arbitrario. Para explotarla con éxito, un atacante debe obligar al usuario a abrir un archivo .sym especialmente manipulado. Esta vulnerabilidad afecta a NI Circuit Design Suite 14.3.0 y versiones anteriores."}], "references": [{"url": "https://www.ni.com/en/support/security/available-critical-and-security-updates-for-ni-software/memory-corruption-vulnerabilities-in-ni-circuit-design-suite.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
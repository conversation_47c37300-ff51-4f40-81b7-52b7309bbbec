{"cve_id": "CVE-2025-37918", "published_date": "2025-05-20T16:15:28.393", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: btusb: avoid NULL pointer dereference in skb_dequeue()\n\nA NULL pointer dereference can occur in skb_dequeue() when processing a\nQCA firmware crash dump on WCN7851 (0489:e0f3).\n\n[ 93.672166] Bluetooth: hci0: ACL memdump size(589824)\n\n[ 93.672475] BUG: kernel NULL pointer dereference, address: 0000000000000008\n[ 93.672517] Workqueue: hci0 hci_devcd_rx [bluetooth]\n[ 93.672598] RIP: 0010:skb_dequeue+0x50/0x80\n\nThe issue stems from handle_dump_pkt_qca() returning 0 even when a dump\npacket is successfully processed. This is because it incorrectly\nforwards the return value of hci_devcd_init() (which returns 0 on\nsuccess). As a result, the caller (btusb_recv_acl_qca() or\nbtusb_recv_evt_qca()) assumes the packet was not handled and passes it\nto hci_recv_frame(), leading to premature kfree() of the skb.\n\nLater, hci_devcd_rx() attempts to dequeue the same skb from the dump\nqueue, resulting in a NULL pointer dereference.\n\nFix this by:\n1. Making handle_dump_pkt_qca() return 0 on success and negative errno\n   on failure, consistent with kernel conventions.\n2. Splitting dump packet detection into separate functions for ACL\n   and event packets for better structure and readability.\n\nThis ensures dump packets are properly identified and consumed, avoiding\ndouble handling and preventing NULL pointer access."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: btusb: evitar la desreferencia de puntero NULL en skb_dequeue() Una desreferencia de puntero NULL puede ocurrir en skb_dequeue() cuando se procesa un volcado de memoria de firmware QCA en WCN7851 (0489:e0f3). [ 93.672166] Bluetooth: hci0: tamaño de volcado de memoria ACL (589824) [ 93.672475] ERROR: Desreferencia de puntero nulo del kernel, dirección: 0000000000000008 [ 93.672517] Cola de trabajo: hci0 hci_devcd_rx [bluetooth] [ 93.672598] RIP: 0010:skb_dequeue+0x50/0x80. El problema se debe a que handle_dump_pkt_qca() devuelve 0 incluso cuando un paquete de volcado se procesa correctamente. Esto se debe a que reenvía incorrectamente el valor de retorno de hci_devcd_init() (que devuelve 0 en caso de éxito). Como resultado, el llamador (btusb_recv_acl_qca() o btusb_recv_evt_qca()) asume que el paquete no fue procesado y lo pasa a hci_recv_frame(), lo que provoca un kfree() prematuro del skb. Posteriormente, hci_devcd_rx() intenta retirar el mismo skb de la cola de volcado, lo que resulta en una desreferencia de puntero nulo. Para solucionar esto: 1. Hacer que handle_dump_pkt_qca() devuelva 0 en caso de éxito y errno negativo en caso de error, de acuerdo con las convenciones del kernel. 2. Dividir la detección de paquetes de volcado en funciones independientes para ACL y paquetes de eventos para una mejor estructura y legibilidad. Esto garantiza que los paquetes de volcado se identifiquen y consuman correctamente, evitando el doble manejo y el acceso a punteros nulos."}], "references": [{"url": "https://git.kernel.org/stable/c/0317b033abcd1d8dd2798f0e2de5e84543d0bd22", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2e8d44ebaa7babdd5c5ab50ca275826e241920d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8563d9fabd8a4b726ba7acab4737c438bf11a059", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b70b41591ec48c78ec6a885e1f57bfc4029e5e13", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
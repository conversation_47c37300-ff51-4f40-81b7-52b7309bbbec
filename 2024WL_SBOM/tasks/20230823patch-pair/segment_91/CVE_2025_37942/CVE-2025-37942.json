{"cve_id": "CVE-2025-37942", "published_date": "2025-05-20T16:15:32.000", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nHID: pidff: Make sure to fetch pool before checking SIMULTANEOUS_MAX\n\nAs noted by <PERSON><PERSON> some 20 years ago, pool report is sometimes messed up.\nThis worked fine on many devices but casued oops on VRS DirectForce PRO.\n\nHere, we're making sure pool report is refetched before trying to access\nany of it's fields. While loop was replaced with a for loop + exit\nconditions were moved aroud to decrease the possibility of creating an\ninfinite loop scenario."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: HID: pidff: Asegúrese de obtener el grupo antes de comprobar SIMULTANEOUS_MAX. Como señaló Anssi hace unos 20 años, el informe del grupo a veces presenta errores. Esto funcionaba correctamente en muchos dispositivos, pero causaba errores en VRS DirectForce PRO. En este caso, nos aseguramos de que el informe del grupo se vuelva a obtener antes de intentar acceder a cualquiera de sus campos. El bucle While se sustituyó por un bucle For y las condiciones de salida se reubicaron para reducir la posibilidad de crear un bucle infinito."}], "references": [{"url": "https://git.kernel.org/stable/c/1f650dcec32d22deb1d6db12300a2b98483099a9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/211861869766a7bb7c72158aee0140ec67e182a7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/344d903be8b5c0733ed0f4bc5be34b4a26d905c8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a6f5d30a5c7713238c5c65c98ad95dacb73688d5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
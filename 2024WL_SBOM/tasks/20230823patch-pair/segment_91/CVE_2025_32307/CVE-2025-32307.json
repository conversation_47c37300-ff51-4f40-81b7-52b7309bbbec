{"cve_id": "CVE-2025-32307", "published_date": "2025-05-16T16:15:39.793", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Chameleon HTML5 Audio Player With/Without Playlist allows SQL Injection. This issue affects Chameleon HTML5 Audio Player With/Without Playlist: from n/a through 3.5.6."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Chameleon HTML5 Audio Player With/Without Playlist permite la inyección SQL. Este problema afecta al reproductor de audio HTML5 Chameleon con/sin lista de reproducción desde n/d hasta la versión 3.5.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lbg-audio1-html5/vulnerability/wordpress-chameleon-html5-audio-player-with-without-playlist-3-5-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
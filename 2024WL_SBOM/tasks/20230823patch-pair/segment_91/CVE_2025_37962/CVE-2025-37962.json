{"cve_id": "CVE-2025-37962", "published_date": "2025-05-20T16:15:34.473", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix memory leak in parse_lease_state()\n\nThe previous patch that added bounds check for create lease context\nintroduced a memory leak. When the bounds check fails, the function\nreturns NULL without freeing the previously allocated lease_ctx_info\nstructure.\n\nThis patch fixes the issue by adding kfree(lreq) before returning NULL\nin both boundary check cases."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: corrección de fuga de memoria en parse_lease_state(). El parche anterior, que añadía la comprobación de los límites para el contexto de creación de arrendamiento, introducía una fuga de memoria. Cuando la comprobación de los límites falla, la función devuelve NULL sin liberar la estructura lease_ctx_info previamente asignada. Este parche corrige el problema añadiendo kfree(lreq) antes de devolver NULL en ambos casos de comprobación de los límites."}], "references": [{"url": "https://git.kernel.org/stable/c/2148d34371b06dac696c0497a98a6bf905a51650", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/829e19ef741d9e9932abdc3bee5466195e0852cf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/af9e2d4732a548db8f6f5a90c2c20a789a3d7240", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb4447bcce915b43b691123118893fca4f372a8f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/facf22c1a394c1e023dab5daf9a494f722771e1c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37985", "published_date": "2025-05-20T18:15:45.360", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nUSB: wdm: close race between wdm_open and wdm_wwan_port_stop\n\nClearing WDM_WWAN_IN_USE must be the last action or\nwe can open a chardev whose URBs are still poisoned"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: USB: wdm: ejecución cerrada entre wdm_open y wdm_wwan_port_stop Borrar WDM_WWAN_IN_USE debe ser la última acción o podemos abrir un chardev cuyos URB todavía estén envenenados"}], "references": [{"url": "https://git.kernel.org/stable/c/217fe1fc7d112595a793e02b306710e702eac492", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/52ae15c665b5fe5876655aaccc3ef70560b0e314", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/54f7f8978af19f899dec80bcc71c8d4855dfbd72", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b02a3fef3e8c8fe5a0a266f7a14f38cc608fb167", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c1846ed4eb527bdfe6b3b7dd2c78e2af4bf98f4f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3c9adc69357fcbe6253a2bc2588ee4bbaaedbe9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
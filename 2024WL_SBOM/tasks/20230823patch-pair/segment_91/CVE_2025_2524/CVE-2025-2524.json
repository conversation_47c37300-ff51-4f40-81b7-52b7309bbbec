{"cve_id": "CVE-2025-2524", "published_date": "2025-05-19T06:15:19.107", "last_modified_date": "2025-06-17T20:49:26.367", "descriptions": [{"lang": "en", "value": "The Ninja Forms  WordPress plugin before 3.10.1 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Ninja Forms para WordPress anterior a la versión 3.10.1 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/6e89ad2b-f12e-4b49-b34e-8da7d30629cd/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
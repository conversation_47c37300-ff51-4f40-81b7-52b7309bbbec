{"cve_id": "CVE-2025-23167", "published_date": "2025-05-19T02:15:17.583", "last_modified_date": "2025-05-19T16:15:27.317", "descriptions": [{"lang": "en", "value": "A flaw in Node.js 20's HTTP parser allows improper termination of HTTP/1 headers using `\\r\\n\\rX` instead of the required `\\r\\n\\r\\n`.\nThis inconsistency enables request smuggling, allowing attackers to bypass proxy-based access controls and submit unauthorized requests.\n\nThe issue was resolved by upgrading `llhttp` to version 9, which enforces correct header termination.\n\nImpact:\n* This vulnerability affects only Node.js 20.x users prior to the `llhttp` v9 upgrade."}, {"lang": "es", "value": "Una falla en el analizador HTTP de Node.js 20 permite la terminación incorrecta de los encabezados HTTP/1 mediante `\\r\\n\\rX` en lugar del `\\r\\n\\r\\n` requerido. Esta inconsistencia facilita el contrabando de solicitudes, lo que permite a los atacantes eludir los controles de acceso basados en proxy y enviar solicitudes no autorizadas. El problema se solucionó actualizando `llhttp` a la versión 9, que aplica la terminación correcta de los encabezados. Impacto: * Esta vulnerabilidad afecta solo a los usuarios de Node.js 20.x anteriores a la actualización a `llhttp` v9."}], "references": [{"url": "https://nodejs.org/en/blog/vulnerability/may-2025-security-releases", "source": "<EMAIL>", "tags": []}]}
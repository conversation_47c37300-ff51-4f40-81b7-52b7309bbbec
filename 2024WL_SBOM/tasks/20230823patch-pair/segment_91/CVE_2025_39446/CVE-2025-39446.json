{"cve_id": "CVE-2025-39446", "published_date": "2025-05-19T19:15:49.747", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Pluggabl LLC Booster Plus for WooCommerce allows Reflected XSS.This issue affects Booster Plus for WooCommerce: from n/a through 7.2.4."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Pluggabl LLC Booster Plus para WooCommerce permite XSS reflejado. Este problema afecta a Booster Plus para WooCommerce: desde n/d hasta 7.2.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/booster-plus-for-woocommerce/vulnerability/wordpress-booster-plus-for-woocommerce-plugin-7-2-4-reflected-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
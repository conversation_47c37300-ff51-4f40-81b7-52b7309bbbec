{"cve_id": "CVE-2025-37946", "published_date": "2025-05-20T16:15:32.577", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ns390/pci: Fix duplicate pci_dev_put() in disable_slot() when P<PERSON> has child VFs\n\nWith commit bcb5d6c76903 (\"s390/pci: introduce lock to synchronize state\nof zpci_dev's\") the code to ignore power off of a PF that has child VFs\nwas changed from a direct return to a goto to the unlock and\npci_dev_put() section. The change however left the existing pci_dev_put()\nuntouched resulting in a doubple put. This can subsequently cause a use\nafter free if the struct pci_dev is released in an unexpected state.\nFix this by removing the extra pci_dev_put()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: s390/pci: Se ha corregido un error pci_dev_put() duplicado en disabled_slot() cuando un PF tiene VFs secundarios. Con el commit bcb5d6c76903 (\"s390/pci: introducir un bloqueo para sincronizar el estado de zpci_dev\"), el código para ignorar el apagado de un PF con VFs secundarios se cambió de un retorno directo a un goto a la sección de desbloqueo y pci_dev_put(). Sin embargo, el cambio dejó intacto el pci_dev_put() existente, lo que resultó en un doble put. Esto puede provocar un use after free si la estructura pci_dev se libera en un estado inesperado. Se soluciona eliminando el pci_dev_put() adicional."}], "references": [{"url": "https://git.kernel.org/stable/c/05a2538f2b48500cf4e8a0a0ce76623cc5bafcf1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/957529baef142d95e0d1b1bea786675bd47dbe53", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c488f8b53e156d6dcc0514ef0afa3a33376b8f9e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37970", "published_date": "2025-05-20T17:15:47.490", "last_modified_date": "2025-06-04T13:15:27.677", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niio: imu: st_lsm6dsx: fix possible lockup in st_lsm6dsx_read_fifo\n\nPrevent st_lsm6dsx_read_fifo from falling in an infinite loop in case\npattern_len is equal to zero and the device FIFO is not empty."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iio: imu: st_lsm6dsx: corrige un posible bloqueo en st_lsm6dsx_read_fifo Evita que st_lsm6dsx_read_fifo caiga en un bucle infinito en caso de que pattern_len sea igual a cero y el FIFO del dispositivo no esté vacío."}], "references": [{"url": "https://git.kernel.org/stable/c/159ca7f18129834b6f4c7eae67de48e96c752fc9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3bb6c02d6fe8347ce1785016d135ff539c20043c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c4a5000618a8c44200d455c92e2f2a4db264717", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/84e39f628a3a3333add99076e4d6c8b42b12d3a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a1cad8a3bca41dead9980615d35efc7bff1fd534", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da33c4167b9cc1266a97215114cb74679f881d0c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f06a1a1954527cc4ed086d926c81ff236b2adde9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f3cf233c946531a92fe651ff2bd15ebbe60630a7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
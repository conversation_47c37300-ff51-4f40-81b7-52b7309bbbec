{"cve_id": "CVE-2025-37912", "published_date": "2025-05-20T16:15:27.723", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nice: Check VF VSI Pointer Value in ice_vc_add_fdir_fltr()\n\nAs mentioned in the commit baeb705fd6a7 (\"ice: always check VF VSI\npointer values\"), we need to perform a null pointer check on the return\nvalue of ice_get_vf_vsi() before using it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ice: comprobar el valor del puntero VF VSI en ice_vc_add_fdir_fltr() Como se menciona en el commit baeb705fd6a7 (\"ice: siempre comprobar los valores del puntero VF VSI\"), debemos realizar una verificación de puntero nulo en el valor de retorno de ice_get_vf_vsi() antes de usarlo."}], "references": [{"url": "https://git.kernel.org/stable/c/0561f2e374c3732b90e50f0a244791a4308ec67e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/073791e9cfe6e4a11a6d85816ba87b1aa207493e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/425c5f266b2edeee0ce16fedd8466410cdcfcfe3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a32dcc3b8293600ddc4024731b4d027d4de061a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eae60cfe25d022d7f0321dba4cc23ad8e87ade48", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f68237982dc012230550f4ecf7ce286a9c37ddc9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
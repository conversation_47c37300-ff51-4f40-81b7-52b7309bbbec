{"cve_id": "CVE-2025-3812", "published_date": "2025-05-17T06:15:18.660", "last_modified_date": "2025-05-19T13:35:20.460", "descriptions": [{"lang": "en", "value": "The WPBot Pro Wordpress Chatbot plugin for WordPress is vulnerable to arbitrary file deletion due to insufficient file path validation in the qcld_openai_delete_training_file() function in all versions up to, and including, 13.6.2. This makes it possible for authenticated attackers, with Subscriber-level access and above, to delete arbitrary files on the server, which can easily lead to remote code execution when the right file is deleted (such as wp-config.php)."}, {"lang": "es", "value": "El complemento WPBot Pro Wordpress Chatbot para WordPress es vulnerable a la eliminación arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función qcld_openai_delete_training_file() en todas las versiones hasta la 13.6.2 incluida. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, eliminen archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código al eliminar el archivo correcto (como wp-config.php)."}], "references": [{"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/8fe1609d-17d6-4afe-90b2-5473dc9b6c3b?source=cve", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wpbot.pro/", "source": "<EMAIL>", "tags": []}]}
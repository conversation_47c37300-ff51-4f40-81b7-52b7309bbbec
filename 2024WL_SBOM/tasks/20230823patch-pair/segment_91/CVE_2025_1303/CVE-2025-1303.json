{"cve_id": "CVE-2025-1303", "published_date": "2025-05-15T20:16:02.387", "last_modified_date": "2025-06-04T20:05:45.313", "descriptions": [{"lang": "en", "value": "The Plugin Oficial  WordPress plugin through 1.7.3 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against only unauthenticated users."}, {"lang": "es", "value": "El complemento Plugin Oficial WordPress hasta la versión 1.7.3 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un error de Cross-Site Scripting reflejado que podría usarse solo contra usuarios no autenticados."}], "references": [{"url": "https://wpscan.com/vulnerability/35181798-4f21-4c8d-bb6e-61eb13683a74/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37904", "published_date": "2025-05-20T16:15:26.820", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: fix the inode leak in btrfs_iget()\n\n[BUG]\nThere is a bug report that a syzbot reproducer can lead to the following\nbusy inode at unmount time:\n\n  BTRFS info (device loop1): last unmount of filesystem 1680000e-3c1e-4c46-84b6-56bd3909af50\n  VFS: Busy inodes after unmount of loop1 (btrfs)\n  ------------[ cut here ]------------\n  kernel BUG at fs/super.c:650!\n  Oops: invalid opcode: 0000 [#1] SMP KASAN NOPTI\n  CPU: 0 UID: 0 PID: 48168 Comm: syz-executor Not tainted 6.15.0-rc2-00471-g119009db2674 #2 PREEMPT(full)\n  Hardware name: QEMU Ubuntu 24.04 PC (i440FX + PIIX, 1996), BIOS 1.16.3-debian-1.16.3-2 04/01/2014\n  RIP: 0010:generic_shutdown_super+0x2e9/0x390 fs/super.c:650\n  Call Trace:\n   <TASK>\n   kill_anon_super+0x3a/0x60 fs/super.c:1237\n   btrfs_kill_super+0x3b/0x50 fs/btrfs/super.c:2099\n   deactivate_locked_super+0xbe/0x1a0 fs/super.c:473\n   deactivate_super fs/super.c:506 [inline]\n   deactivate_super+0xe2/0x100 fs/super.c:502\n   cleanup_mnt+0x21f/0x440 fs/namespace.c:1435\n   task_work_run+0x14d/0x240 kernel/task_work.c:227\n   resume_user_mode_work include/linux/resume_user_mode.h:50 [inline]\n   exit_to_user_mode_loop kernel/entry/common.c:114 [inline]\n   exit_to_user_mode_prepare include/linux/entry-common.h:329 [inline]\n   __syscall_exit_to_user_mode_work kernel/entry/common.c:207 [inline]\n   syscall_exit_to_user_mode+0x269/0x290 kernel/entry/common.c:218\n   do_syscall_64+0xd4/0x250 arch/x86/entry/syscall_64.c:100\n   entry_SYSCALL_64_after_hwframe+0x77/0x7f\n   </TASK>\n\n[CAUSE]\nWhen btrfs_alloc_path() failed, btrfs_iget() directly returned without\nreleasing the inode already allocated by btrfs_iget_locked().\n\nThis results the above busy inode and trigger the kernel BUG.\n\n[FIX]\nFix it by calling iget_failed() if btrfs_alloc_path() failed.\n\nIf we hit error inside btrfs_read_locked_inode(), it will properly call\niget_failed(), so nothing to worry about.\n\nAlthough the iget_failed() cleanup inside btrfs_read_locked_inode() is a\nbreak of the normal error handling scheme, let's fix the obvious bug\nand backport first, then rework the error handling later."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: arregla la pérdida de inodo en btrfs_iget() [ERROR] Hay un informe de error que indica que un reproductor syzbot puede provocar el siguiente inodo ocupado en el momento del desmontaje: Información BTRFS (dispositivo loop1): último desmontaje del sistema de archivos 1680000e-3c1e-4c46-84b6-56bd3909af50 VFS: Inodos ocupados después del desmontaje de loop1 (btrfs) ------------[ cortar aquí ]------------ ¡ERROR del kernel en fs/super.c:650! Oops: código de operación no válido: 0000 [#1] SMP KASAN NOPTI CPU: 0 UID: 0 PID: 48168 Comm: syz-executor No contaminado 6.15.0-rc2-00471-g119009db2674 #2 PREEMPT(full) Nombre del hardware: QEMU Ubuntu 24.04 PC (i440FX + PIIX, 1996), BIOS 1.16.3-debian-1.16.3-2 01/04/2014 RIP: 0010:generic_shutdown_super+0x2e9/0x390 fs/super.c:650 Rastreo de llamadas:   kill_anon_super+0x3a/0x60 fs/super.c:1237 btrfs_kill_super+0x3b/0x50 fs/btrfs/super.c:2099 deactivate_locked_super+0xbe/0x1a0 fs/super.c:473 deactivate_super fs/super.c:506 [inline] deactivate_super+0xe2/0x100 fs/super.c:502 cleanup_mnt+0x21f/0x440 fs/namespace.c:1435 task_work_run+0x14d/0x240 kernel/task_work.c:227 resume_user_mode_work include/linux/resume_user_mode.h:50 [inline] exit_to_user_mode_loop kernel/entry/common.c:114 [inline] exit_to_user_mode_prepare include/linux/entry-common.h:329 [inline] __syscall_exit_to_user_mode_work kernel/entry/common.c:207 [inline] syscall_exit_to_user_mode+0x269/0x290 kernel/entry/common.c:218 do_syscall_64+0xd4/0x250 arch/x86/entry/syscall_64.c:100 entry_SYSCALL_64_after_hwframe+0x77/0x7f  [CAUSA] Cuando btrfs_alloc_path() fallaba, btrfs_iget() regresaba directamente sin liberar el inodo ya asignado por btrfs_iget_locked(). Esto genera el inodo ocupado mencionado anteriormente y activa el error del kernel. [SOLUCIÓN] Corríjalo llamando a iget_failed() si btrfs_alloc_path() fallaba. Si encontramos un error en btrfs_read_locked_inode(), se llamará correctamente a iget_failed(), así que no hay de qué preocuparse. Aunque la limpieza de iget_failed() dentro de btrfs_read_locked_inode() supone una ruptura del esquema normal de gestión de errores, primero corregiremos el error obvio y lo adaptaremos a la versión anterior, y luego reescribiremos la gestión de errores."}], "references": [{"url": "https://git.kernel.org/stable/c/30a339bece3a44ab0a821477139e84fb86af9761", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/48c1d1bb525b1c44b8bdc8e7ec5629cb6c2b9fc4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
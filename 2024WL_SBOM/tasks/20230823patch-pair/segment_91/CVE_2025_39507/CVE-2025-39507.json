{"cve_id": "CVE-2025-39507", "published_date": "2025-05-16T16:15:40.930", "last_modified_date": "2025-06-06T22:09:50.510", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in NasaTheme Nasa Core allows PHP Local File Inclusion. This issue affects Nasa Core: from n/a through 6.3.2."}, {"lang": "es", "value": "Vulnerabilidad de control incorrecto del nombre de archivo para la instrucción Include/Require en un programa PHP ('Inclusión remota de archivos en PHP') en NasaTheme Nasa Core permite la inclusión local de archivos en PHP. Este problema afecta a Nasa Core desde n/d hasta la versión 6.3.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/nasa-core/vulnerability/wordpress-nasa-core-plugin-6-3-2-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
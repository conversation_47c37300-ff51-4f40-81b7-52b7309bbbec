{"cve_id": "CVE-2025-37938", "published_date": "2025-05-20T16:15:31.127", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntracing: Verify event formats that have \"%*p..\"\n\nThe trace event verifier checks the formats of trace events to make sure\nthat they do not point at memory that is not in the trace event itself or\nin data that will never be freed. If an event references data that was\nallocated when the event triggered and that same data is freed before the\nevent is read, then the kernel can crash by reading freed memory.\n\nThe verifier runs at boot up (or module load) and scans the print formats\nof the events and checks their arguments to make sure that dereferenced\npointers are safe. If the format uses \"%*p..\" the verifier will ignore it,\nand that could be dangerous. Cover this case as well.\n\nAlso add to the sample code a use case of \"%*pbl\"."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rastreo: Verificar formatos de eventos que contengan \"%*p..\" El verificador de eventos de rastreo verifica los formatos de los eventos de rastreo para asegurarse de que no apunten a memoria que no esté en el propio evento de rastreo ni a datos que nunca se liberarán. Si un evento hace referencia a datos asignados al momento de su activación y esos mismos datos se liberan antes de la lectura del evento, el kernel puede bloquearse al leer la memoria liberada. El verificador se ejecuta al arrancar (o al cargar un módulo) y analiza los formatos de impresión de los eventos, además de comprobar sus argumentos para garantizar la seguridad de los punteros desreferenciados. Si el formato usa \"%*p..\", el verificador lo ignorará, lo cual podría ser peligroso. Considere también este caso. Añada también al código de ejemplo un caso de uso de \"%*pbl\"."}], "references": [{"url": "https://git.kernel.org/stable/c/03127354027508d076073b020d3070990fd6a958", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/04b80d45ecfaf780981d6582899e3ab205e4aa08", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4d11fac941d83509be4e6a21038281d6d96da50c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6854c87ac823181c810f8c07489ba543260c0023", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c7204fd1758c0caf1938e8a59809a1fdf28a8114", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ea8d7647f9ddf1f81e2027ed305299797299aa03", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
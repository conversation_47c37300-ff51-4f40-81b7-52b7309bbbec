{"cve_id": "CVE-2024-8851", "published_date": "2025-05-15T20:15:59.967", "last_modified_date": "2025-06-04T16:31:03.757", "descriptions": [{"lang": "en", "value": "The Polls CP WordPress plugin before 1.0.77 does not sanitise and escape some of its poll settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multi site setup)."}, {"lang": "es", "value": "El complemento Polls CP WordPress anterior a la versión 1.0.77 no depura ni escapa de algunas de sus configuraciones de encuesta, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/493f3360-3155-4105-9b5c-60a8605275ab/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
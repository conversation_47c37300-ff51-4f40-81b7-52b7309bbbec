{"cve_id": "CVE-2025-26086", "published_date": "2025-05-20T15:16:07.023", "last_modified_date": "2025-06-12T16:20:56.180", "descriptions": [{"lang": "en", "value": "An unauthenticated blind SQL injection vulnerability exists in RSI Queue Management System v3.0 within the TaskID parameter of the get request handler. Attackers can remotely inject time-delayed SQL payloads to induce server response delays, enabling time-based inference and iterative extraction of sensitive database contents without authentication."}, {"lang": "es", "value": "Existe una vulnerabilidad de inyección SQL ciega no autenticada en RSI Queue Management System v3.0 dentro del parámetro TaskID del controlador de solicitudes GET. Los atacantes pueden inyectar remotamente payloads SQL con retardo temporal para inducir retrasos en la respuesta del servidor, lo que permite la inferencia basada en el tiempo y la extracción iterativa de contenido confidencial de la base de datos sin autenticación."}], "references": [{"url": "https://seclists.org/fulldisclosure/2025/May/21", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "http://seclists.org/fulldisclosure/2025/May/21", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}]}
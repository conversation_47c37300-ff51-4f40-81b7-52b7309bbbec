{"cve_id": "CVE-2025-37965", "published_date": "2025-05-20T17:15:46.900", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amd/display: Fix invalid context error in dml helper\n\n[Why]\n\"BUG: sleeping function called from invalid context\" error.\nafter:\n\"drm/amd/display: Protect FPU in dml2_validate()/dml21_validate()\"\n\nThe populate_dml_plane_cfg_from_plane_state() uses the GFP_KERNEL flag\nfor memory allocation, which shouldn't be used in atomic contexts.\n\nThe allocation is needed only for using another helper function\nget_scaler_data_for_plane().\n\n[How]\nModify helpers to pass a pointer to scaler_data within existing context,\neliminating the need for dynamic memory allocation/deallocation\nand copying.\n\n(cherry picked from commit bd3e84bc98f81b44f2c43936bdadc3241d654259)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amd/display: Corrección de error de contexto no válido en el asistente dml [Por qué] Error \"BUG: función inactiva llamada desde un contexto no válido\". Después: \"drm/amd/display: Proteger FPU en dml2_validate()/dml21_validate()\". La función populate_dml_plane_cfg_from_plane_state() utiliza el indicador GFP_KERNEL para la asignación de memoria, que no debería usarse en contextos atómicos. Esta asignación solo es necesaria para usar otra función auxiliar, get_scaler_data_for_plane(). [Cómo] Modificar los asistentes para que pasen un puntero a scaler_data dentro del contexto existente, eliminando la necesidad de asignar/desasignar memoria dinámicamente y copiar. (Seleccionado de el commit bd3e84bc98f81b44f2c43936bdadc3241d654259)"}], "references": [{"url": "https://git.kernel.org/stable/c/9984db63742099ee3f3cff35cf71306d10e64356", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b371f8f6d89ec8dfea796e00a44a57c44fc8fcc0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d8c4afe78385cd355e4d80299d785379d6e874df", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
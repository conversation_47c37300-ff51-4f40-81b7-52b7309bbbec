{"cve_id": "CVE-2025-32245", "published_date": "2025-05-16T16:15:38.733", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Apollo allows SQL Injection. This issue affects Apollo: from n/a through 3.6.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Apollo permite la inyección SQL. Este problema afecta a Apollo desde n/d hasta la versión 3.6.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lbg-audio7_html5_full_width_sticky_pro/vulnerability/wordpress-apollo-3-6-3-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
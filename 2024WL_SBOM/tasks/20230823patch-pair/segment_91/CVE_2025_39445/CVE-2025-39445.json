{"cve_id": "CVE-2025-39445", "published_date": "2025-05-19T19:15:49.613", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in highwarden Super Store Finder allows SQL Injection.This issue affects Super Store Finder: from n/a through 7.2."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en Highwarden Super Store Finder permite la inyección SQL. Este problema afecta a Super Store Finder: desde n/a hasta 7.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/superstorefinder-wp/vulnerability/wordpress-super-store-finder-7-2-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
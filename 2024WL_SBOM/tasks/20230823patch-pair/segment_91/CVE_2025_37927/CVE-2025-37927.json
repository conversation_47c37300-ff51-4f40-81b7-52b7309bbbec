{"cve_id": "CVE-2025-37927", "published_date": "2025-05-20T16:15:29.240", "last_modified_date": "2025-06-04T13:15:26.970", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niommu/amd: Fix potential buffer overflow in parse_ivrs_acpihid\n\nThere is a string parsing logic error which can lead to an overflow of hid\nor uid buffers. Comparing ACPIID_LEN against a total string length doesn't\ntake into account the lengths of individual hid and uid buffers so the\ncheck is insufficient in some cases. For example if the length of hid\nstring is 4 and the length of the uid string is 260, the length of str\nwill be equal to ACPIID_LEN + 1 but uid string will overflow uid buffer\nwhich size is 256.\n\nThe same applies to the hid string with length 13 and uid string with\nlength 250.\n\nCheck the length of hid and uid strings separately to prevent\nbuffer overflow.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iommu/amd: Arreglar desbordamiento de búfer potencial en parse_ivrs_acpihid Hay un error de lógica de análisis de cadenas que puede provocar un desbordamiento de los búferes hid o uid. La comparación de ACPIID_LEN con la longitud total de una cadena no tiene en cuenta las longitudes de los búferes hid y uid individuales, por lo que la comprobación es insuficiente en algunos casos. Por ejemplo, si la longitud de la cadena hid es 4 y la longitud de la cadena uid es 260, la longitud de str será igual a ACPIID_LEN + 1, pero la cadena uid desbordará el búfer uid, cuyo tamaño es 256. Lo mismo se aplica a la cadena hid con una longitud de 13 y a la cadena uid con una longitud de 250. Compruebe la longitud de las cadenas hid y uid por separado para evitar el desbordamiento del búfer. Encontrado por el Centro de verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/10d901a95f8e766e5aa0bb9a983fb41271f64718", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/13d67528e1ae4486e9ab24b70122fab104c73c29", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2b65060c84ee4d8dc64fae6d2728b528e9e832e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/466d9da267079a8d3b69fa72dfa3a732e1f6dbb5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8dee308e4c01dea48fc104d37f92d5b58c50b96c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a65ebfed65fa62797ec1f5f1dcf7adb157a2de1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c3f37faa71f5d26dd2144b3f2b14525ec8f5e41f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8bdfc0297965bb13fa439d36ca9c4f7c8447f0f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
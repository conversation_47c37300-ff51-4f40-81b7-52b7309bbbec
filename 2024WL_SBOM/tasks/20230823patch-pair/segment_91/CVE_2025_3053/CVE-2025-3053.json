{"cve_id": "CVE-2025-3053", "published_date": "2025-05-15T05:15:50.830", "last_modified_date": "2025-05-16T14:43:26.160", "descriptions": [{"lang": "en", "value": "The UiPress lite | Effortless custom dashboards, admin themes and pages plugin for WordPress is vulnerable to Remote Code Execution in all versions up to, and including, 3.5.07 via the uip_process_form_input() function. This is due to the function taking user supplied inputs to execute arbitrary functions with arbitrary data, and does not have any sort of capability check. This makes it possible for authenticated attackers, with Subscriber-level access and above, to execute arbitrary code on the server."}, {"lang": "es", "value": "El complemento UiPress lite | Effortless custom dashboards, admin themes and pages para WordPress es vulnerable a la ejecución remota de código en todas las versiones hasta la 3.5.07 incluida, a través de la función uip_process_form_input(). Esto se debe a que la función utiliza las entradas proporcionadas por el usuario para ejecutar funciones arbitrarias con datos arbitrarios y no cuenta con ningún tipo de verificación de capacidad. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, ejecuten código arbitrario en el servidor."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3292552/uipress-lite/trunk/admin/core/ajax-functions.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6717adb0-27bc-4cd4-8c34-bea59bc0e016?source=cve", "source": "<EMAIL>", "tags": []}]}
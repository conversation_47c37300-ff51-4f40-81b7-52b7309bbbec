{"cve_id": "CVE-2025-32296", "published_date": "2025-05-16T16:15:39.243", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Missing Authorization vulnerability in quantumcloud Simple Link Directory Pro allows Exploiting Incorrectly Configured Access Control Security Levels. This issue affects Simple Link Directory Pro: from n/a through 14.7.3."}, {"lang": "es", "value": "La vulnerabilidad de falta de autorización en quantumcloud Simple Link Directory Pro permite explotar niveles de seguridad de control de acceso configurados incorrectamente. Este problema afecta a Simple Link Directory Pro desde la versión n/d hasta la 14.7.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/qc-simple-link-directory/vulnerability/wordpress-simple-link-directory-pro-plugin-14-7-3-broken-access-control-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37979", "published_date": "2025-05-20T17:15:48.540", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: qcom: Fix sc7280 lpass potential buffer overflow\n\nCase values introduced in commit\n5f78e1fb7a3e (\"ASoC: qcom: Add driver support for audioreach solution\")\ncause out of bounds access in arrays of sc7280 driver data (e.g. in case\nof RX_CODEC_DMA_RX_0 in sc7280_snd_hw_params()).\n\nRedefine LPASS_MAX_PORTS to consider the maximum possible port id for\nq6dsp as sc7280 driver utilizes some of those values.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: qcom: Corrección de un posible desbordamiento de búfer en sc7280 lpass. Los valores de caso introducidos en el commit 5f78e1fb7a3e (\"ASoC: qcom: Añadir compatibilidad de controlador para la solución audioreach\") provocan acceso fuera de los límites en matrices de datos del controlador sc7280 (p. ej., en el caso de RX_CODEC_DMA_RX_0 en sc7280_snd_hw_params()). Redefinir LPASS_MAX_PORTS para considerar el ID de puerto máximo posible para q6dsp, ya que el controlador sc7280 utiliza algunos de esos valores. Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con SVACE."}], "references": [{"url": "https://git.kernel.org/stable/c/a12c14577882b1f2b4cff0f86265682f16e97b0c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a31a4934b31faea76e735bab17e63d02fcd8e029", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b807b7c81a6d066757a94af7b8fa5b6a37e4d0b3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c0ce01e0ff8a0d61a7b089ab309cdc12bc527c39", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d78888853eb53f47ae16cf3aa5d0444d0331b9f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
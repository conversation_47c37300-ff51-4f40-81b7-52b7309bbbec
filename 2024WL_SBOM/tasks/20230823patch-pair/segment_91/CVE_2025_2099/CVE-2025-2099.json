{"cve_id": "CVE-2025-2099", "published_date": "2025-05-19T12:15:19.640", "last_modified_date": "2025-05-21T17:43:15.080", "descriptions": [{"lang": "en", "value": "A vulnerability in the `preprocess_string()` function of the `transformers.testing_utils` module in huggingface/transformers version v4.48.3 allows for a Regular Expression Denial of Service (ReDoS) attack. The regular expression used to process code blocks in docstrings contains nested quantifiers, leading to exponential backtracking when processing input with a large number of newline characters. An attacker can exploit this by providing a specially crafted payload, causing high CPU usage and potential application downtime, effectively resulting in a Denial of Service (DoS) scenario."}, {"lang": "es", "value": "Una vulnerabilidad en la función `preprocess_string()` del módulo `transformers.testing_utils` en huggingface/transformers versión v4.48.3 permite un ataque de denegación de servicio (ReDoS) mediante expresiones regulares. La expresión regular utilizada para procesar bloques de código en cadenas de documentación contiene cuantificadores anidados, lo que provoca un retroceso exponencial al procesar entradas con un gran número de caracteres de nueva línea. Un atacante puede explotar esto proporcionando un payload especialmente manipulado, lo que provoca un alto consumo de CPU y un posible tiempo de inactividad de la aplicación, lo que resulta en un escenario de denegación de servicio (DoS)."}], "references": [{"url": "https://github.com/huggingface/transformers/commit/8cb522b4190bd556ce51be04942720650b1a3e57", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://huntr.com/bounties/97b780f3-ffca-424f-ad5d-0e1c57a5bde4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
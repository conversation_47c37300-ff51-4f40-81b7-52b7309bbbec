{"cve_id": "CVE-2025-31637", "published_date": "2025-05-16T16:15:37.280", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup SHOUT allows SQL Injection. This issue affects SHOUT: from n/a through 3.5.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup SHOUT permite la inyección SQL. Este problema afecta a SHOUT desde n/d hasta la versión 3.5.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lbg-audio8-html5-radio_ads/vulnerability/wordpress-shout-3-5-3-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
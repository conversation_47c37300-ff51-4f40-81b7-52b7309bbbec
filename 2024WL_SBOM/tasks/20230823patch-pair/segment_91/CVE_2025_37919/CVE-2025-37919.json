{"cve_id": "CVE-2025-37919", "published_date": "2025-05-20T16:15:28.500", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: amd: acp: Fix NULL pointer deref in acp_i2s_set_tdm_slot\n\nUpdate chip data using dev_get_drvdata(dev->parent) to fix\nNULL pointer deref in acp_i2s_set_tdm_slot."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: amd: acp: Corregir la desreferencia del puntero NULL en acp_i2s_set_tdm_slot Actualizar los datos del chip usando dev_get_drvdata(dev-&gt;parent) para corregir la desreferencia del puntero NULL en acp_i2s_set_tdm_slot."}], "references": [{"url": "https://git.kernel.org/stable/c/3104b7d559ffb28f34e55028ff55a475e26e2e1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6d9b64156d849e358cb49b6b899fb0b7d262bda8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fd4d8d139030dd2de97ef46d332673675ca8ad72", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
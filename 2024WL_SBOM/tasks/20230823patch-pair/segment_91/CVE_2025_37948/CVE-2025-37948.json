{"cve_id": "CVE-2025-37948", "published_date": "2025-05-20T16:15:32.800", "last_modified_date": "2025-06-27T11:15:24.947", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64: bpf: Add BHB mitigation to the epilogue for cBPF programs\n\nA malicious BPF program may manipulate the branch history to influence\nwhat the hardware speculates will happen next.\n\nOn exit from a BPF program, emit the BHB mititgation sequence.\n\nThis is only applied for 'classic' cBPF programs that are loaded by\nseccomp."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64: bpf: Añadir mitigación de BHB al epílogo de programas cBPF. Un programa BPF malicioso puede manipular el historial de ramificaciones para influir en lo que el hardware especula que ocurrirá a continuación. Al salir de un programa BPF, se emite la secuencia de mitigación de BHB. Esto solo se aplica a programas cBPF clásicos cargados por seccomp."}], "references": [{"url": "https://git.kernel.org/stable/c/0dfefc2ea2f29ced2416017d7e5b1253a54c2735", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/38c345fd54afd9d6ed8d3fcddf3f6ea23887bf78", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42a20cf51011788f04cf2adbcd7681f02bdb6c27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/852b8ae934b5cbdc62496fa56ce9969aa2edda7f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8fe5c37b0e08a97cf0210bb75970e945aaaeebab", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/993f63239c219696aef8887a4e7d3a16bf5a8ece", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c6a8735d841bcb7649734bb3a787bb174c67c0d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
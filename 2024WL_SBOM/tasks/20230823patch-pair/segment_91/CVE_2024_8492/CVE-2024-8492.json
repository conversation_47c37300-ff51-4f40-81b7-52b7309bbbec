{"cve_id": "CVE-2024-8492", "published_date": "2025-05-15T20:15:58.743", "last_modified_date": "2025-06-12T15:46:49.280", "descriptions": [{"lang": "en", "value": "The Hustle  WordPress plugin through 7.8.5 does not sanitise and escape some of its settings, which could allow high privilege users such as editors to perform Cross-Site Scripting attacks even when unfiltered_html is disallowed"}, {"lang": "es", "value": "El complemento Hustle de WordPress hasta la versión 7.8.5 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como editores, realizar ataques de Cross-Site Scripting incluso cuando unfiltered_html no está permitido."}], "references": [{"url": "https://wpscan.com/vulnerability/c7437eba-8e91-4fcc-82a3-ff8908b36877/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37937", "published_date": "2025-05-20T16:15:30.760", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nobjtool, media: dib8000: Prevent divide-by-zero in dib8000_set_dds()\n\nIf dib8000_set_dds()'s call to dib8000_read32() returns zero, the result\nis a divide-by-zero.  Prevent that from happening.\n\nFixes the following warning with an UBSAN kernel:\n\n  drivers/media/dvb-frontends/dib8000.o: warning: objtool: dib8000_tune() falls through to next function dib8096p_cfg_DibRx()"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: objtool, media: dib8000: Evitar división por cero en dib8000_set_dds(). Si la llamada de dib8000_set_dds() a dib8000_read32() devuelve cero, el resultado es una división por cero. Se evita que esto ocurra. Corrige la siguiente advertencia con un kernel UBSAN: drivers/media/dvb-frontends/dib8000.o: advertencia: objtool: dib8000_tune() falla a la siguiente función dib8096p_cfg_DibRx()."}], "references": [{"url": "https://git.kernel.org/stable/c/536f7f3595ef8187cfa9ea50d7d24fcf4e84e166", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6cfe46036b163e5a0f07c6b705b518148e1a8b2f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/75b42dfe87657ede3da3f279bd6b1b16d69af954", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/976a85782246a29ba0f6d411a7a4f524cb9ea987", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9b76b198cf209797abcb1314c18ddeb90fe0827b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b9249da6b0ed56269d4f21850df8e5b35dab50bd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8430e72b99936c206b37a8e2daebb3f8df7f2d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cd80277f652138d2619f149f86ae6d17bce721d1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e63d465f59011dede0a0f1d21718b59a64c3ff5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
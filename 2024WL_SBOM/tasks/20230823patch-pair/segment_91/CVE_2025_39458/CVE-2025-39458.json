{"cve_id": "CVE-2025-39458", "published_date": "2025-05-19T19:15:50.293", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in Mikado-Themes Foton allows PHP Local File Inclusion.This issue affects Foton: from n/a through 2.5.2."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión de archivos remotos PHP') en Mikado-Themes Foton permite la inclusión de archivos locales PHP. Este problema afecta a Foton: desde n/a hasta 2.5.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/foton/vulnerability/wordpress-foton-theme-2-5-2-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
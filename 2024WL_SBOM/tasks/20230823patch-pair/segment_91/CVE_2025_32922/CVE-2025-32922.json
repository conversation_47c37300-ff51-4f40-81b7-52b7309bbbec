{"cve_id": "CVE-2025-32922", "published_date": "2025-05-15T19:15:56.983", "last_modified_date": "2025-05-16T14:43:26.160", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Tobias WP2LEADS allows Stored XSS.This issue affects WP2LEADS: from n/a through 3.5.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Tobias WP2LEADS permite XSS almacenado. Este problema afecta a WP2LEADS: desde n/a hasta 3.5.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp2leads/vulnerability/wordpress-wp2leads-plugin-3-5-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
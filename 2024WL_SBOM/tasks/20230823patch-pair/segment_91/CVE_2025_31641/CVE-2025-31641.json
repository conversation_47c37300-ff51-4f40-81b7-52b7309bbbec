{"cve_id": "CVE-2025-31641", "published_date": "2025-05-16T16:15:37.683", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup UberSlider allows SQL Injection. This issue affects UberSlider: from n/a through 2.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup UberSlider permite la inyección SQL. Este problema afecta a UberSlider desde n/d hasta la versión 2.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/uber-classic/vulnerability/wordpress-uberslider-2-3-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
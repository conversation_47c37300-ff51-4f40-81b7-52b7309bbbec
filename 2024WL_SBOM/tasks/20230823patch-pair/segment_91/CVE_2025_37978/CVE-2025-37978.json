{"cve_id": "CVE-2025-37978", "published_date": "2025-05-20T17:15:48.423", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblock: integrity: Do not call set_page_dirty_lock()\n\nPlacing multiple protection information buffers inside the same page\ncan lead to oopses because set_page_dirty_lock() can't be called from\ninterrupt context.\n\nSince a protection information buffer is not backed by a file there is\nno point in setting its page dirty, there is nothing to synchronize.\nDrop the call to set_page_dirty_lock() and remove the last argument to\nbio_integrity_unpin_bvec()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bloque: integridad: No llamar a set_page_dirty_lock(). Colocar varios búferes de información de protección dentro de la misma página puede provocar errores, ya que set_page_dirty_lock() no se puede llamar desde el contexto de interrupción. Dado que un búfer de información de protección no está respaldado por un archivo, no tiene sentido configurar su página como sucia; no hay nada que sincronizar. Elimine la llamada a set_page_dirty_lock() y elimine el último argumento de bio_integrity_unpin_bvec()."}], "references": [{"url": "https://git.kernel.org/stable/c/39e160505198ff8c158f11bce2ba19809a756e8b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9487fc1a10b3aa89feb24e7cedeccaaf63074617", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c38a005e6efb9ddfa06bd8353b82379d6fd5d6c4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
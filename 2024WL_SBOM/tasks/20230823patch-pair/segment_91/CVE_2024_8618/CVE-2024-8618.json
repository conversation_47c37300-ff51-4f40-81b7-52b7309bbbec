{"cve_id": "CVE-2024-8618", "published_date": "2025-05-15T20:15:59.070", "last_modified_date": "2025-05-27T19:51:55.623", "descriptions": [{"lang": "en", "value": "The Page Builder: Pagelayer  WordPress plugin before 1.9.0 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Page Builder: Pagelayer de WordPress anterior a la versión 1.9.0 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/acddcf33-0a18-499e-b42d-c8b49f2c4de5/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-2892", "published_date": "2025-05-19T05:15:17.927", "last_modified_date": "2025-06-04T23:03:24.370", "descriptions": [{"lang": "en", "value": "The All in One SEO – Powerful SEO Plugin to Boost SEO Rankings & Increase Traffic plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the post Meta Description and Canonical URL parameters in all versions up to, and including, ******* due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento All in One SEO – Powerful SEO Plugin to Boost SEO Rankings &amp; Increase Traffic para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de la metadescripción de la publicación y los parámetros de URL canónica en todas las versiones hasta la ******* incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en las páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3289874/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/7fd5d31d-a4f3-458a-b457-f20aeaa71749?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
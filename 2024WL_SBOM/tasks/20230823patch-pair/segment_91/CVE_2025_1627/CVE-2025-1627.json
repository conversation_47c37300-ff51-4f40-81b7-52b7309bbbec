{"cve_id": "CVE-2025-1627", "published_date": "2025-05-19T06:15:18.980", "last_modified_date": "2025-06-17T20:49:11.690", "descriptions": [{"lang": "en", "value": "The Qi Blocks WordPress plugin before 1.4 does not validate and escape some of its block options before outputting them back in a page/post where the block is embed, which could allow users with the contributor role and above to perform Stored Cross-Site Scripting attacks."}, {"lang": "es", "value": "El complemento Qi Blocks de WordPress anterior a la versión 1.4 no valida ni escapa algunas de sus opciones de bloque antes de mostrarlas nuevamente en una página o publicación donde el bloque está incrustado, lo que podría permitir a los usuarios con rol de colaborador y superior realizar ataques de Cross-Site Scripting almacenado."}], "references": [{"url": "https://wpscan.com/vulnerability/31b2292b-1ea7-4d63-ad65-0366e2c05dd3/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
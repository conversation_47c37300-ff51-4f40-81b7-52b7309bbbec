{"cve_id": "CVE-2024-9831", "published_date": "2025-05-15T20:16:01.370", "last_modified_date": "2025-06-12T16:33:23.650", "descriptions": [{"lang": "en", "value": "The Taskbuilder  WordPress plugin before 3.0.9 does not sanitize and escape a parameter before using it in a SQL statement, allowing admins to perform SQL injection attacks"}, {"lang": "es", "value": "El complemento Taskbuilder de WordPress anterior a la versión 3.0.9 no depura ni escapa un parámetro antes de usarlo en una declaración SQL, lo que permite a los administradores realizar ataques de inyección SQL."}], "references": [{"url": "https://wpscan.com/vulnerability/390baaf8-a162-43e5-9367-0d2e979d89f7/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
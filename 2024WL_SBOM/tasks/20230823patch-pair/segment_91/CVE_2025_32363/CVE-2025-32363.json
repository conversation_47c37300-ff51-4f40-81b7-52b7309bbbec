{"cve_id": "CVE-2025-32363", "published_date": "2025-05-14T20:15:22.247", "last_modified_date": "2025-05-16T14:43:26.160", "descriptions": [{"lang": "en", "value": "mediDOK before ********* allows remote attackers to achieve remote code execution on a target system via deserialization of untrusted data."}, {"lang": "es", "value": "mediDOK anterior a ********* permite a atacantes remotos lograr la ejecución remota de código en un sistema objetivo a través de la deserialización de datos no confiables."}], "references": [{"url": "https://code-white.com/public-vulnerability-list/#unauthenticated-remote-code-execution-via-deserialization-of-untrusted-data-in-m", "source": "<EMAIL>", "tags": []}, {"url": "https://medidok.de/aktuelles-neuigkeiten/", "source": "<EMAIL>", "tags": []}, {"url": "https://medidok.de/neueversionen/update-medidok-2-5-18-43-verfugbar/", "source": "<EMAIL>", "tags": []}]}
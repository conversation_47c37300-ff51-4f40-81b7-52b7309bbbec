{"cve_id": "CVE-2025-37898", "published_date": "2025-05-20T16:15:26.177", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\npowerpc64/ftrace: fix module loading without patchable function entries\n\nget_stubs_size assumes that there must always be at least one patchable\nfunction entry, which is not always the case (modules that export data\nbut no code), otherwise it returns -ENOEXEC and thus the section header\nsh_size is set to that value. During module_memory_alloc() the size is\npassed to execmem_alloc() after being page-aligned and thus set to zero\nwhich will cause it to fail the allocation (and thus module loading) as\n__vmalloc_node_range() checks for zero-sized allocs and returns null:\n\n[  115.466896] module_64: cast_common: doesn't contain __patchable_function_entries.\n[  115.469189] ------------[ cut here ]------------\n[  115.469496] WARNING: CPU: 0 PID: 274 at mm/vmalloc.c:3778 __vmalloc_node_range_noprof+0x8b4/0x8f0\n...\n[  115.478574] ---[ end trace 0000000000000000 ]---\n[  115.479545] execmem: unable to allocate memory\n\nFix this by removing the check completely, since it is anyway not\nhelpful to propagate this as an error upwards."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: powerpc64/ftrace: se corrige la carga de módulos sin entradas de función parcheables. get_stubs_size asume que siempre debe haber al menos una entrada de función parcheable, lo cual no siempre ocurre (módulos que exportan datos, pero no código); de lo contrario, devuelve -ENOEXEC y, por lo tanto, el encabezado de sección sh_size se establece en ese valor. Durante module_memory_alloc(), el tamaño se pasa a execmem_alloc() tras la alineación de página y, por lo tanto, se establece en cero, lo que provocará un error en la asignación (y, por lo tanto, en la carga del módulo), ya que __vmalloc_node_range() busca asignaciones de tamaño cero y devuelve null: [115.466896] module_64: cast_common: no contiene __patchable_function_entries. [ 115.469189] ------------[ cortar aquí ]------------ [ 115.469496] ADVERTENCIA: CPU: 0 PID: 274 en mm/vmalloc.c:3778 __vmalloc_node_range_noprof+0x8b4/0x8f0 ... [ 115.478574] ---[ fin del seguimiento 0000000000000000 ]--- [ 115.479545] execmem: no se puede asignar memoria Solucione esto eliminando la verificación por completo, ya que de todos modos no es útil propagar esto como un error hacia arriba."}], "references": [{"url": "https://git.kernel.org/stable/c/358b559afec7806b9d01c2405b490e782c347022", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/534f5a8ba27863141e29766467a3e1f61bcb47ac", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-8095", "published_date": "2025-05-15T20:15:57.937", "last_modified_date": "2025-05-27T19:57:24.200", "descriptions": [{"lang": "en", "value": "The BabelZ  WordPress plugin through 1.1.5 does not have CSRF check in some places, and is missing sanitisation as well as escaping, which could allow attackers to make logged in admin add Stored XSS payloads via a CSRF attack."}, {"lang": "es", "value": "El complemento BabelZ para WordPress hasta la versión 1.1.5 no tiene verificación CSRF en algunos lugares y le falta depuración y escape, lo que podría permitir a los atacantes hacer que el administrador que haya iniciado sesión agregue payloads XSS almacenado a través de un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/56d22ad0-c5f5-488b-bc1f-73188dfc71d2/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-39411", "published_date": "2025-05-19T19:15:49.473", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in Indie_Plugins WhatsApp Click to Chat Plugin for WordPress.This issue affects WhatsApp Click to Chat Plugin for WordPress: from n/a through 2.2.12."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión de archivo remoto PHP') en Indie_Plugins WhatsApp Click to Chat Plugin para WordPress. Este problema afecta al complemento Indie_Plugins WhatsApp Click to Chat para WordPress: desde n/a hasta 2.2.12."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wpt-whatsapp/vulnerability/wordpress-whatsapp-click-to-chat-plugin-for-wordpress-plugin-2-2-12-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
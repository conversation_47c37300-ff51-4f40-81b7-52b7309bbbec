{"cve_id": "CVE-2025-37931", "published_date": "2025-05-20T16:15:29.713", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbtrfs: adjust subpage bit start based on sectorsize\n\nWhen running machines with 64k page size and a 16k nodesize we started\nseeing tree log corruption in production.  This turned out to be because\nwe were not writing out dirty blocks sometimes, so this in fact affects\nall metadata writes.\n\nWhen writing out a subpage EB we scan the subpage bitmap for a dirty\nrange.  If the range isn't dirty we do\n\n\tbit_start++;\n\nto move onto the next bit.  The problem is the bitmap is based on the\nnumber of sectors that an EB has.  So in this case, we have a 64k\npagesize, 16k nodesize, but a 4k sectorsize.  This means our bitmap is 4\nbits for every node.  With a 64k page size we end up with 4 nodes per\npage.\n\nTo make this easier this is how everything looks\n\n[0         16k       32k       48k     ] logical address\n[0         4         8         12      ] radix tree offset\n[               64k page               ] folio\n[ 16k eb ][ 16k eb ][ 16k eb ][ 16k eb ] extent buffers\n[ | | | |  | | | |   | | | |   | | | | ] bitmap\n\nNow we use all of our addressing based on fs_info->sectorsize_bits, so\nas you can see the above our 16k eb->start turns into radix entry 4.\n\nWhen we find a dirty range for our eb, we correctly do bit_start +=\nsectors_per_node, because if we start at bit 0, the next bit for the\nnext eb is 4, to correspond to eb->start 16k.\n\nHowever if our range is clean, we will do bit_start++, which will now\nput us offset from our radix tree entries.\n\nIn our case, assume that the first time we check the bitmap the block is\nnot dirty, we increment bit_start so now it == 1, and then we loop\naround and check again.  This time it is dirty, and we go to find that\nstart using the following equation\n\n\tstart = folio_start + bit_start * fs_info->sectorsize;\n\nso in the case above, eb->start 0 is now dirty, and we calculate start\nas\n\n\t0 + 1 * fs_info->sectorsize = 4096\n\t4096 >> 12 = 1\n\nNow we're looking up the radix tree for 1, and we won't find an eb.\nWhat's worse is now we're using bit_start == 1, so we do bit_start +=\nsectors_per_node, which is now 5.  If that eb is dirty we will run into\nthe same thing, we will look at an offset that is not populated in the\nradix tree, and now we're skipping the writeout of dirty extent buffers.\n\nThe best fix for this is to not use sectorsize_bits to address nodes,\nbut that's a larger change.  Since this is a fs corruption problem fix\nit simply by always using sectors_per_node to increment the start bit."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: btrfs: ajustar el inicio del bit de la subpágina en función del tamaño del sector Al ejecutar máquinas con un tamaño de página de 64k y un tamaño de nodo de 16k, comenzamos a ver corrupción en el registro del árbol en producción. Esto resultó ser porque a veces no escribíamos bloques sucios, por lo que, de hecho, afecta a todas las escrituras de metadatos. Al escribir un EB de subpágina, escaneamos el mapa de bits de la subpágina en busca de un rango sucio. Si el rango no está sucio, hacemos bit_start++; para pasar al siguiente bit. El problema es que el mapa de bits se basa en la cantidad de sectores que tiene un EB. Entonces, en este caso, tenemos un tamaño de página de 64k, un tamaño de nodo de 16k, pero un tamaño de sector de 4k. Esto significa que nuestro mapa de bits es de 4 bits para cada nodo. Con un tamaño de página de 64k, terminamos con 4 nodos por página. Para hacer esto más fácil así es como se ve todo [0 16k 32k 48k ] dirección lógica [0 4 8 12 ] desplazamiento del árbol de radix [ página 64k ] folio [ 16k eb ][ 16k eb ][ 16k eb ][ 16k eb ] búferes de extensión [ | | | | | | | | | | | | | | | | ] mapa de bits Ahora usamos todo nuestro direccionamiento basado en fs_info-&gt;sectorsize_bits, así que como puedes ver arriba nuestro eb-&gt;start de 16k se convierte en la entrada de radix 4. Cuando encontramos un rango sucio para nuestro eb, hacemos correctamente bit_start += sectores_per_node, porque si empezamos en el bit 0, el siguiente bit para el siguiente eb es 4, para corresponder a eb-&gt;start 16k. Sin embargo, si nuestro rango está limpio, haremos bit_start++, que ahora nos pondrá en un desplazamiento desde nuestras entradas del árbol de radix. En nuestro caso, supongamos que la primera vez que comprobamos el mapa de bits, el bloque no está sucio, incrementamos bit_start para que ahora sea == 1, y luego hacemos un bucle y comprobamos de nuevo. Esta vez está sucio, y vamos a encontrar ese inicio usando la siguiente ecuación start = folio_start + bit_start * fs_info-&gt;sectorsize; así que en el caso anterior, eb-&gt;start 0 ahora está sucio, y calculamos start como 0 + 1 * fs_info-&gt;sectorsize = 4096 4096 &gt;&gt; 12 = 1 Ahora estamos buscando el árbol de bases para 1, y no encontraremos un eb. Lo que es peor es que ahora estamos usando bit_start == 1, así que hacemos bit_start += sectores_por_nodo, que ahora es 5. Si ese eb está sucio, nos encontraremos con lo mismo, veremos un desplazamiento que no está rellenado en el árbol de bases, y ahora estamos omitiendo la escritura de los búferes de extensión sucios. La mejor solución es no usar sectorsize_bits para direccionar nodos, pero ese es un cambio mayor. Dado que se trata de un problema de corrupción del sistema de archivos, corríjalo simplemente usando siempre sectores_por_nodo para incrementar el bit de inicio."}], "references": [{"url": "https://git.kernel.org/stable/c/396f4002710030ea1cfd4c789ebaf0a6969ab34f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b80db09b614cb7edec5bada1bc7c7b0eb3b453ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e08e49d986f82c30f42ad0ed43ebbede1e1e3739", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
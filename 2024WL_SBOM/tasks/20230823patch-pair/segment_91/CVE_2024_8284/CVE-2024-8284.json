{"cve_id": "CVE-2024-8284", "published_date": "2025-05-15T20:15:58.340", "last_modified_date": "2025-06-12T15:29:52.710", "descriptions": [{"lang": "en", "value": "The Download Manager WordPress plugin before 3.2.99 does not sanitise and escape some of its settings, which could allow high privilege users such as editors to perform Cross-Site Scripting attacks even when unfiltered_html is disallowed"}, {"lang": "es", "value": "El complemento Download Manager de WordPress anterior a la versión 3.2.99 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como editores, realizar ataques de Cross-Site Scripting incluso cuando unfiltered_html no está permitido."}], "references": [{"url": "https://wpscan.com/vulnerability/93e38b8c-8a2e-4264-b520-ebdbe995d61e/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
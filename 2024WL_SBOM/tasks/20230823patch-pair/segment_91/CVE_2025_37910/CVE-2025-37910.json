{"cve_id": "CVE-2025-37910", "published_date": "2025-05-20T16:15:27.507", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nptp: ocp: Fix NULL dereference in Adva board SMA sysfs operations\n\nOn Adva boards, SMA sysfs store/get operations can call\n__handle_signal_outputs() or __handle_signal_inputs() while the `irig`\nand `dcf` pointers are uninitialized, leading to a NULL pointer\ndereference in __handle_signal() and causing a kernel crash. Adva boards\ndon't use `irig` or `dcf` functionality, so add Adva-specific callbacks\n`ptp_ocp_sma_adva_set_outputs()` and `ptp_ocp_sma_adva_set_inputs()` that\navoid invoking `irig` or `dcf` input/output routines."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ptp: ocp: Corregir la desreferencia NULL en las operaciones sysfs de SMA de la placa Adva En las placas Adva, las operaciones de almacenamiento/obtención de SMA sysfs pueden llamar a __handle_signal_outputs() o __handle_signal_inputs() mientras los punteros `irig` y `dcf` no están inicializados, lo que provoca una desreferencia de puntero NULL en __handle_signal() y causa un fallo del kernel. Las placas Adva no usan la funcionalidad `irig` o `dcf`, así que añada las devoluciones de llamada específicas de Adva `ptp_ocp_sma_adva_set_outputs()` y `ptp_ocp_sma_adva_set_inputs()` que evitan invocar las rutinas de entrada/salida `irig` o `dcf`."}], "references": [{"url": "https://git.kernel.org/stable/c/5b349f9cdb4a9daa133bea267dfc0c383628387a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8a543d825e78b8d680d8f891381b83fbffdb0bb6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e98386d79a23c57cf179fe4138322e277aa3aa74", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37909", "published_date": "2025-05-20T16:15:27.390", "last_modified_date": "2025-06-04T13:15:26.290", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: lan743x: Fix memleak issue when GSO enabled\n\nAlways map the `skb` to the LS descriptor. Previously skb was\nmapped to EXT descriptor when the number of fragments is zero with\nGSO enabled. Mapping the skb to EXT descriptor prevents it from\nbeing freed, leading to a memory leak"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: lan743x: Se solucionó el problema de fuga de memoria con GSO habilitado. Siempre se asigna `skb` al descriptor LS. Anteriormente, skb se asignaba al descriptor EXT cuando el número de fragmentos era cero con GSO habilitado. Asignar skb al descriptor EXT impide su liberación, lo que provoca una fuga de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/093855ce90177488eac772de4eefbb909033ce5f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/189b05f189cac9fd233ef04d31cb5078c4d09c39", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2d52e2e38b85c8b7bc00dca55c2499f46f8c8198", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c65ee5ad632eb8dcd3a91cf5dc99b22535f44d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a0e0efbabbbe6a1859bc31bf65237ce91e124b9b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dae1ce27ceaea7e1522025b15252e3cc52802622", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/df993daa4c968b4b23078eacc248f6502ede8664", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f42c18e2f14c1b1fdd2a5250069a84bc854c398c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
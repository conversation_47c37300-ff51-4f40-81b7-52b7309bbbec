{"cve_id": "CVE-2024-8619", "published_date": "2025-05-15T20:15:59.147", "last_modified_date": "2025-06-04T20:08:23.630", "descriptions": [{"lang": "en", "value": "The Ajax Search Lite  WordPress plugin before 4.12.3 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Ajax Search Lite para WordPress anterior a la versión 4.12.3 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/84f6733e-028a-4288-b01a-7578a4a89dbe/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
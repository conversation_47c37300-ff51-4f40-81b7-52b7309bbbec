{"cve_id": "CVE-2025-37928", "published_date": "2025-05-20T16:15:29.363", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndm-bufio: don't schedule in atomic context\n\nA BUG was reported as below when CONFIG_DEBUG_ATOMIC_SLEEP and\ntry_verify_in_tasklet are enabled.\n[  129.444685][  T934] BUG: sleeping function called from invalid context at drivers/md/dm-bufio.c:2421\n[  129.444723][  T934] in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 934, name: kworker/1:4\n[  129.444740][  T934] preempt_count: 201, expected: 0\n[  129.444756][  T934] RCU nest depth: 0, expected: 0\n[  129.444781][  T934] Preemption disabled at:\n[  129.444789][  T934] [<ffffffd816231900>] shrink_work+0x21c/0x248\n[  129.445167][  T934] kernel BUG at kernel/sched/walt/walt_debug.c:16!\n[  129.445183][  T934] Internal error: Oops - BUG: 00000000f2000800 [#1] PREEMPT SMP\n[  129.445204][  T934] Skip md ftrace buffer dump for: 0x1609e0\n[  129.447348][  T934] CPU: 1 PID: 934 Comm: kworker/1:4 Tainted: G        W  OE      6.6.56-android15-8-o-g6f82312b30b9-debug #1 1400000003000000474e5500b3187743670464e8\n[  129.447362][  T934] Hardware name: Qualcomm Technologies, Inc. Parrot QRD, Alpha-M (DT)\n[  129.447373][  T934] Workqueue: dm_bufio_cache shrink_work\n[  129.447394][  T934] pstate: 60400005 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)\n[  129.447406][  T934] pc : android_rvh_schedule_bug+0x0/0x8 [sched_walt_debug]\n[  129.447435][  T934] lr : __traceiter_android_rvh_schedule_bug+0x44/0x6c\n[  129.447451][  T934] sp : ffffffc0843dbc90\n[  129.447459][  T934] x29: ffffffc0843dbc90 x28: ffffffffffffffff x27: 0000000000000c8b\n[  129.447479][  T934] x26: 0000000000000040 x25: ffffff804b3d6260 x24: ffffffd816232b68\n[  129.447497][  T934] x23: ffffff805171c5b4 x22: 0000000000000000 x21: ffffffd816231900\n[  129.447517][  T934] x20: ffffff80306ba898 x19: 0000000000000000 x18: ffffffc084159030\n[  129.447535][  T934] x17: 00000000d2b5dd1f x16: 00000000d2b5dd1f x15: ffffffd816720358\n[  129.447554][  T934] x14: 0000000000000004 x13: ffffff89ef978000 x12: 0000000000000003\n[  129.447572][  T934] x11: ffffffd817a823c4 x10: 0000000000000202 x9 : 7e779c5735de9400\n[  129.447591][  T934] x8 : ffffffd81560d004 x7 : 205b5d3938373434 x6 : ffffffd8167397c8\n[  129.447610][  T934] x5 : 0000000000000000 x4 : 0000000000000001 x3 : ffffffc0843db9e0\n[  129.447629][  T934] x2 : 0000000000002f15 x1 : 0000000000000000 x0 : 0000000000000000\n[  129.447647][  T934] Call trace:\n[  129.447655][  T934]  android_rvh_schedule_bug+0x0/0x8 [sched_walt_debug 1400000003000000474e550080cce8a8a78606b6]\n[  129.447681][  T934]  __might_resched+0x190/0x1a8\n[  129.447694][  T934]  shrink_work+0x180/0x248\n[  129.447706][  T934]  process_one_work+0x260/0x624\n[  129.447718][  T934]  worker_thread+0x28c/0x454\n[  129.447729][  T934]  kthread+0x118/0x158\n[  129.447742][  T934]  ret_from_fork+0x10/0x20\n[  129.447761][  T934] Code: ???????? ???????? ???????? d2b5dd1f (d4210000)\n[  129.447772][  T934] ---[ end trace 0000000000000000 ]---\n\ndm_bufio_lock will call spin_lock_bh when try_verify_in_tasklet\nis enabled, and __scan will be called in atomic context."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: dm-bufio: no programar en contexto atómico Se informó un ERROR como el siguiente cuando CONFIG_DEBUG_ATOMIC_SLEEP y try_verify_in_tasklet están habilitados. [ 129.444685][ T934] ERROR: función de suspensión llamada desde un contexto no válido en drivers/md/dm-bufio.c:2421 [ 129.444723][ T934] in_atomic(): 1, irqs_disabled(): 0, non_block: 0, pid: 934, name: kworker/1:4 [ 129.444740][ T934] preempt_count: 201, esperado: 0 [ 129.444756][ T934] Profundidad de anidamiento de RCU: 0, esperado: 0 [ 129.444781][ T934] Preempción deshabilitada en: [ 129.444789][ T934] [] shrink_work+0x21c/0x248 [ 129.445167][ T934] ¡ERROR del kernel en kernel/sched/walt/walt_debug.c:16! [ 129.445183][ T934] Error interno: Ups - BUG: 00000000f2000800 [#1] PREEMPT SMP [ 129.445204][ T934] Omitir volcado de búfer de md ftrace para: 0x1609e0 [ 129.447348][ T934] CPU: 1 PID: 934 Comm: kworker/1:4 Contaminado: GW OE 6.6.56-android15-8-o-g6f82312b30b9-debug #1 1400000003000000474e5500b3187743670464e8 [ 129.447362][ T934] Nombre del hardware: Qualcomm Technologies, Inc. Parrot QRD, Alpha-M (DT) [ 129.447373][ T934] Cola de trabajo: dm_bufio_cache shrink_work [ 129.447394][ T934] pstate: 60400005 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--) [ 129.447406][ T934] pc : android_rvh_schedule_bug+0x0/0x8 [sched_walt_debug] [ 129.447435][ T934] lr : __traceiter_android_rvh_schedule_bug+0x44/0x6c [ 129.447451][ T934] sp : ffffffc0843dbc90 [ 129.447459][T934] x29: ffffffc0843dbc90 x28: ffffffffffffffff x27: 0000000000000c8b [ 129.447479][T934] x26: 0000000000000040 x25: ffffff804b3d6260 x24: ffffffd816232b68 [ 129.447497][ T934] x23: ffffff805171c5b4 x22: 00000000000000000 x21: ffffffd816231900 [ 129.447517][T934] x20: ffffff80306ba898 x19: 0000000000000000 x18: ffffffc084159030 [ 129.447535][ T934] x17: 00000000d2b5dd1f x16: 00000000d2b5dd1f x15: ffffffd816720358 [ 129.447554][ T934] x14: 0000000000000004 x13: ffffff89ef978000 x12: 0000000000000003 [ 129.447572][ T934] x11: ffffffd817a823c4 x10: 0000000000000202 x9: 7e779c5735de9400 [129.447591][T934] x8: ffffffd81560d004 x7: 205b5d3938373434 x6: ffffffd8167397c8 [ 129.447610][T934] x5: 0000000000000000 x4: 0000000000000001 x3: ffffffc0843db9e0 [129.447629][T934] x2: 0000000000002f15 x1: 0000000000000000 x0 : 0000000000000000 [ 129.447647][ T934] Rastreo de llamadas: [ 129.447655][ T934] android_rvh_schedule_bug+0x0/0x8 [sched_walt_debug 1400000003000000474e550080cce8a8a78606b6] [ 129.447681][ T934] __might_resched+0x190/0x1a8 [ 129.447694][ T934] shrink_work+0x180/0x248 [ 129.447706][ T934] proceso_uno_trabajo+0x260/0x624 [ 129.447718][ T934] subproceso_de_trabajo+0x28c/0x454 [ 129.447729][ T934] subproceso_de_trabajo+0x118/0x158 [ 129.447742][ T934] ret_de_bifurcación+0x10/0x20 [ 129.447761][ T934] Código: ???????? ???????? ???????? d2b5dd1f (d4210000) [ 129.447772][ T934] ---[ fin del seguimiento 0000000000000000 ]--- dm_bufio_lock llamará a spin_lock_bh cuando try_verify_in_tasklet esté habilitado, y se llamará a __scan en el contexto atómico."}], "references": [{"url": "https://git.kernel.org/stable/c/69a37b3ba85088fc6b903b8e1db7f0a1d4d0b52d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a3d8f0a7f5e8b193db509c7191fefeed3533fc44", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a99f5bf4f7197009859dbce14c12f8e2ce5a5a69", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8c83052283bcf2fdd467a33d1d2bd5ba36e935a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f45108257280e0a1cc951ce254853721b40c0812", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-32287", "published_date": "2025-05-16T16:15:38.860", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Responsive HTML5 Audio Player PRO With Playlist allows SQL Injection. This issue affects Responsive HTML5 Audio Player PRO With Playlist: from n/a through 3.5.7."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Responsive HTML5 Audio Player PRO With Playlist permite la inyección SQL. Este problema afecta a Responsive HTML5 Audio Player PRO With Playlist: desde n/d hasta la versión 3.5.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lbg-audio2-html5/vulnerability/wordpress-responsive-html5-audio-player-pro-with-playlist-3-5-7-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37916", "published_date": "2025-05-20T16:15:28.170", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\npds_core: remove write-after-free of client_id\n\nA use-after-free error popped up in stress testing:\n\n[Mon Apr 21 21:21:33 2025] BUG: KFENCE: use-after-free write in pdsc_auxbus_dev_del+0xef/0x160 [pds_core]\n[Mon Apr 21 21:21:33 2025] Use-after-free write at 0x000000007013ecd1 (in kfence-#47):\n[Mon Apr 21 21:21:33 2025]  pdsc_auxbus_dev_del+0xef/0x160 [pds_core]\n[Mon Apr 21 21:21:33 2025]  pdsc_remove+0xc0/0x1b0 [pds_core]\n[Mon Apr 21 21:21:33 2025]  pci_device_remove+0x24/0x70\n[Mon Apr 21 21:21:33 2025]  device_release_driver_internal+0x11f/0x180\n[Mon Apr 21 21:21:33 2025]  driver_detach+0x45/0x80\n[Mon Apr 21 21:21:33 2025]  bus_remove_driver+0x83/0xe0\n[Mon Apr 21 21:21:33 2025]  pci_unregister_driver+0x1a/0x80\n\nThe actual device uninit usually happens on a separate thread\nscheduled after this code runs, but there is no guarantee of order\nof thread execution, so this could be a problem.  There's no\nactual need to clear the client_id at this point, so simply\nremove the offending code."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: pds_core: eliminar escritura use-after-free de client_id Apareció un error de use-after-free en las pruebas de estrés: [Lun 21 Abr 21:21:33 2025] ERROR: KFENCE: escritura use-after-free en pdsc_auxbus_dev_del+0xef/0x160 [pds_core] [Lun 21 Abr 21:21:33 2025] Escritura use-after-free en 0x000000007013ecd1 (en kfence-#47): [Lun 21 Abr 21:21:33 2025] pdsc_auxbus_dev_del+0xef/0x160 [pds_core] [Lun 21 Abr 21:21:33 2025] pdsc_remove+0xc0/0x1b0 [pds_core] [Lun 21 Abr 21:21:33 2025] pci_device_remove+0x24/0x70 [Lun 21 Abr 21:21:33 2025] device_release_driver_internal+0x11f/0x180 [Lun 21 Abr 21:21:33 2025] driver_detach+0x45/0x80 [Lun 21 Abr 21:21:33 2025] bus_remove_driver+0x83/0xe0 [Lun 21 Abr 21:21:33 2025] pci_unregister_driver+0x1a/0x80 La desinicialización real del dispositivo suele ocurrir en un subproceso separado programado después de que se ejecuta este código, pero no hay garantía de que Orden de ejecución del hilo, por lo que esto podría ser un problema. No es necesario borrar el client_id en este punto; simplemente elimine el código problemático."}], "references": [{"url": "https://git.kernel.org/stable/c/26dc701021302f11c8350108321d11763bd81dfe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9b467c5bcdb45a41d2a49fbb9ffca73d1380e99b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c649b9653ed09196e91d3f4b16b679041b3c42e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dfd76010f8e821b66116dec3c7d90dd2403d1396", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
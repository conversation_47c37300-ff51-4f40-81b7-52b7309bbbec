{"cve_id": "CVE-2025-37930", "published_date": "2025-05-20T16:15:29.600", "last_modified_date": "2025-06-04T13:15:27.087", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/nouveau: Fix WARN_ON in nouveau_fence_context_kill()\n\nNouveau is mostly designed in a way that it's expected that fences only\never get signaled through nouveau_fence_signal(). However, in at least\none other place, nouveau_fence_done(), can signal fences, too. If that\nhappens (race) a signaled fence remains in the pending list for a while,\nuntil it gets removed by nouveau_fence_update().\n\nShould nouveau_fence_context_kill() run in the meantime, this would be\na bug because the function would attempt to set an error code on an\nalready signaled fence.\n\nHave nouveau_fence_context_kill() check for a fence being signaled."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/nouveau: Corrección de WARN_ON en nouveau_fence_context_kill(). Nouveau está diseñado principalmente de tal manera que se espera que las vallas solo se señalicen mediante nouveau_fence_signal(). Sin embargo, en al menos otro lugar, nouveau_fence_done(), también puede señalizar vallas. Si esto sucede (ejecución), una valla señalizada permanece en la lista de pendientes durante un tiempo, hasta que nouveau_fence_update() la elimina. Si nouveau_fence_context_kill() se ejecuta mientras tanto, esto sería un error porque la función intentaría establecer un código de error en una valla ya señalizada. Haga que nouveau_fence_context_kill() compruebe si hay una valla señalizada."}], "references": [{"url": "https://git.kernel.org/stable/c/0453825167ecc816ec15c736e52316f69db0deb9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/126f5c6e0cb84e5c6f7a3a856d799d85668fb38e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2ec0f5f6d4768f292c8406ed92fa699f184577e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39d6e889c0b19a2c79e1c74c843ea7c2d0f99c28", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/47ca11836c35c5698088fd87f7fb4b0ffa217e17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b771b2017260ffc3a8d4e81266619649bffcb242", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bbe5679f30d7690a9b6838a583b9690ea73fe0e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
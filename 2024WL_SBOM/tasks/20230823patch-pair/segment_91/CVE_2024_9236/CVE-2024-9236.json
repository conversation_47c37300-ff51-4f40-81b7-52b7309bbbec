{"cve_id": "CVE-2024-9236", "published_date": "2025-05-15T20:16:00.390", "last_modified_date": "2025-06-12T16:43:19.150", "descriptions": [{"lang": "en", "value": "The Team  WordPress plugin before 4.4.2 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Team WordPress anterior a la versión 4.4.2 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/fd06ba56-37dd-4c23-ae7c-ab8de40d1645/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
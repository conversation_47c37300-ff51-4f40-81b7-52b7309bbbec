{"cve_id": "CVE-2025-40635", "published_date": "2025-05-20T13:15:47.300", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in Comerzzia Backoffice: Sales Orchestrator 3.0.15. This vulnerability allows an attacker to retrieve, create, update and delete databases via the ‘uidActivity’, ‘codCompany’ and ‘uidInstance’ parameters of the ‘/comerzzia/login’ endpoint."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en Comerzzia Backoffice: Sales Orchestrator 3.0.15. Esta vulnerabilidad permite a un atacante recuperar, crear, actualizar y eliminar bases de datos mediante los parámetros 'uidActivity', 'codCompany' y 'uidInstance' del endpoint '/comerzzia/login'."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/sql-injection-comerzzia", "source": "<EMAIL>", "tags": []}]}
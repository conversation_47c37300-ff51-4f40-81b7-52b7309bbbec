{"cve_id": "CVE-2024-8702", "published_date": "2025-05-15T20:15:59.710", "last_modified_date": "2025-06-12T16:30:25.093", "descriptions": [{"lang": "en", "value": "The Backup Database WordPress plugin through 4.9 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Backup Database de WordPress hasta la versión 4.9 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir que usuarios con privilegios elevados, como el administrador, realicen ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/2199ef66-25bd-4eb4-a675-d8b30f047847/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
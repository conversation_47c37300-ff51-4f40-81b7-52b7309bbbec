{"cve_id": "CVE-2025-37936", "published_date": "2025-05-20T16:15:30.443", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nperf/x86/intel: KVM: Mask PEBS_ENABLE loaded for guest with vCPU's value.\n\nWhen generating the MSR_IA32_PEBS_ENABLE value that will be loaded on\nVM-Entry to a KVM guest, mask the value with the vCPU's desired PEBS_ENABLE\nvalue.  Consulting only the host kernel's host vs. guest masks results in\nrunning the guest with PEBS enabled even when the guest doesn't want to use\nPEBS.  Because KVM uses perf events to proxy the guest virtual PMU, simply\nlooking at exclude_host can't differentiate between events created by host\nuserspace, and events created by <PERSON><PERSON><PERSON> on behalf of the guest.\n\nRunning the guest with PEBS unexpectedly enabled typically manifests as\ncrashes due to a near-infinite stream of #PFs.  E.g. if the guest hasn't\nwritten MSR_IA32_DS_AREA, the CPU will hit page faults on address '0' when\ntrying to record PEBS events.\n\nThe issue is most easily reproduced by running `perf kvm top` from before\ncommit 7b100989b4f6 (\"perf evlist: Remove __evlist__add_default\") (after\nwhich, `perf kvm top` effectively stopped using PEBS).\tThe userspace side\nof perf creates a guest-only PEBS event, which intel_guest_get_msrs()\nmisconstrues a guest-*owned* PEBS event.\n\nArguably, this is a userspace bug, as enabling PEBS on guest-only events\nsimply cannot work, and userspace can kill VMs in many other ways (there\nis no danger to the host).  However, even if this is considered to be bad\nuserspace behavior, there's zero downside to perf/KVM restricting PEBS to\nguest-owned events.\n\nNote, commit 854250329c02 (\"KVM: x86/pmu: Disable guest PEBS temporarily\nin two rare situations\") fixed the case where host userspace is profiling\nKVM *and* userspace, but missed the case where userspace is profiling only\nKVM."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: perf/x86/intel: KVM: Enmascarar PEBS_ENABLE cargado para el invitado con el valor de la vCPU. Al generar el valor MSR_IA32_PEBS_ENABLE que se cargará en VM-Entry a un invitado KVM, enmascarar el valor con el valor PEBS_ENABLE deseado de la vCPU. Consultar únicamente las máscaras de host vs. invitado del kernel del host da como resultado que el invitado se ejecute con PEBS habilitado incluso cuando el invitado no desea usarlo. Debido a que KVM usa eventos perf para proxyizar la PMU virtual del invitado, simplemente mirar exclude_host no puede diferenciar entre eventos creados por el espacio de usuario del host y eventos creados por KVM en nombre del invitado. Ejecutar el invitado con PEBS habilitado inesperadamente generalmente se manifiesta como bloqueos debido a un flujo casi infinito de #PF. Por ejemplo, si el invitado no ha escrito MSR_IA32_DS_AREA, la CPU encontrará fallos de página en la dirección '0' al intentar registrar eventos PEBS. El problema se reproduce más fácilmente ejecutando `perf kvm top` antes de el commit 7b100989b4f6 (\"perf evlist: Remove __evlist__add_default\") (tras lo cual, `perf kvm top` dejó de usar PEBS). El lado del espacio de usuario de perf crea un evento PEBS exclusivo para invitados, que intel_guest_get_msrs() malinterpreta como un evento PEBS *propiedad* del invitado. Podría decirse que se trata de un error del espacio de usuario, ya que habilitar PEBS en eventos exclusivos para invitados simplemente no funciona, y el espacio de usuario puede bloquear máquinas virtuales de muchas otras maneras (sin peligro para el host). Sin embargo, incluso si esto se considera un comportamiento inadecuado del espacio de usuario, no hay ninguna desventaja en que perf/KVM restrinja PEBS a eventos propios del invitado. Nota: el commit 854250329c02 (\"KVM: x86/pmu: Deshabilitar PEBS invitado temporalmente en dos situaciones excepcionales\") corrigió el caso en el que el espacio de usuario del host perfila KVM *y* el espacio de usuario, pero no el caso en el que el espacio de usuario perfila solo KVM."}], "references": [{"url": "https://git.kernel.org/stable/c/160153cf9e4aa875ad086cc094ce34aac8e13d63", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/34b6fa11431aef71045ae5a00d90a7d630597eda", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/44ee0afc9d1e7a7c1932698de01362ed80cfc4b5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/58f6217e5d0132a9f14e401e62796916aa055c1b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/86aa62895fc2fb7ab09d7ca40fae8ad09841f66b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
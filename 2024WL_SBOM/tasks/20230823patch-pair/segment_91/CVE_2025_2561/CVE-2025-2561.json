{"cve_id": "CVE-2025-2561", "published_date": "2025-05-19T06:15:19.350", "last_modified_date": "2025-06-17T21:07:57.860", "descriptions": [{"lang": "en", "value": "The Ninja Forms  WordPress plugin before 3.10.1 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Ninja Forms para WordPress anterior a la versión 3.10.1 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/4a2074a3-a479-4473-92fb-04397f20dd86/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
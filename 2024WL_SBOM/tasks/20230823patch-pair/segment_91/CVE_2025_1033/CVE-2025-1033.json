{"cve_id": "CVE-2025-1033", "published_date": "2025-05-15T20:16:02.060", "last_modified_date": "2025-06-12T16:36:24.740", "descriptions": [{"lang": "en", "value": "The Badgearoo WordPress plugin through 1.0.14 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Badgearoo para WordPress hasta la versión 1.0.14 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/cbb63e80-92aa-4e85-9d47-dc68211af97d/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
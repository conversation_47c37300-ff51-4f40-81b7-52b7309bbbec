{"cve_id": "CVE-2025-37914", "published_date": "2025-05-20T16:15:27.943", "last_modified_date": "2025-06-04T13:15:26.610", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet_sched: ets: Fix double list add in class with netem as child qdisc\n\nAs described in <PERSON><PERSON><PERSON><PERSON>'s report [1], there are use cases where a netem\nchild qdisc will make the parent qdisc's enqueue callback reentrant.\nIn the case of ets, there won't be a UAF, but the code will add the same\nclassifier to the list twice, which will cause memory corruption.\n\nIn addition to checking for qlen being zero, this patch checks whether\nthe class was already added to the active_list (cl_is_active) before\ndoing the addition to cater for the reentrant case.\n\n[1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net_sched: ets: Se corrige la adición doble de lista en la clase con netem como qdisc secundaria. Como se describe en el informe de Gerrard [1], existen casos de uso en los que una qdisc secundaria netem hará que la devolución de llamada de encolado de la qdisc primaria sea reentrante. En el caso de ets, no habrá un UAF, pero el código agregará el mismo clasificador a la lista dos veces, lo que causará corrupción de memoria. Además de verificar que qlen sea cero, este parche verifica si la clase ya se agregó a active_list (cl_is_active) antes de realizar la adición para atender el caso reentrante. [1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/1a6d0c00fa07972384b0c308c72db091d49988b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1f01e9f961605eb397c6ecd1d7b0233dfbf9077c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/24388ba0a1b1b6d4af1b205927ac7f7b119ee4ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/554acc5a2ea9703e08023eb9a003f9e5a830a502", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/72c3da7e6ceb74e74ddbb5a305a35c9fdfcac6e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9efb6a0fa88e0910d079fdfeb4f7ce4d4ac6c990", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bc321f714de693aae06e3786f88df2975376d996", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
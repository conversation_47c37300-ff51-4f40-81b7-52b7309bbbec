{"cve_id": "CVE-2025-39374", "published_date": "2025-05-19T17:15:26.397", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in aseem1234 Best Posts Summary allows Stored XSS.This issue affects Best Posts Summary: from n/a through 1.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en aseem1234 Best Posts Summary permite XSS almacenado. Este problema afecta a Best Posts Summary: desde n/a hasta 1.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/best-posts-summary/vulnerability/wordpress-best-posts-summary-plugin-1-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
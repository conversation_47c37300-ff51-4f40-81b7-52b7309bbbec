{"cve_id": "CVE-2025-23166", "published_date": "2025-05-19T02:15:17.470", "last_modified_date": "2025-05-19T15:15:23.310", "descriptions": [{"lang": "en", "value": "The C++ method SignTraits::DeriveBits() may incorrectly call ThrowException() based on user-supplied inputs when executing in a background thread, crashing the Node.js process. Such cryptographic operations are commonly applied to untrusted inputs. Thus, this mechanism potentially allows an adversary to remotely crash a Node.js runtime."}, {"lang": "es", "value": "El método SignTraits::DeriveBits() de C++ puede llamar incorrectamente a ThrowException() basándose en las entradas proporcionadas por el usuario al ejecutarse en segundo plano, lo que provoca el bloqueo del proceso de Node.js. Estas operaciones criptográficas se aplican comúnmente a entradas no confiables. Por lo tanto, este mecanismo podría permitir que un adversario bloquee remotamente un entorno de ejecución de Node.js."}], "references": [{"url": "https://nodejs.org/en/blog/vulnerability/may-2025-security-releases", "source": "<EMAIL>", "tags": []}]}
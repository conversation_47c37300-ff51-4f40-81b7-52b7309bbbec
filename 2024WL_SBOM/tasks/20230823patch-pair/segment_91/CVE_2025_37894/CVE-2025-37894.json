{"cve_id": "CVE-2025-37894", "published_date": "2025-05-20T16:15:25.760", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: use sock_gen_put() when sk_state is TCP_TIME_WAIT\n\nIt is possible for a pointer of type struct inet_timewait_sock to be\nreturned from the functions __inet_lookup_established() and\n__inet6_lookup_established(). This can cause a crash when the\nreturned pointer is of type struct inet_timewait_sock and\nsock_put() is called on it. The following is a crash call stack that\nshows sk->sk_wmem_alloc being accessed in sk_free() during the call to\nsock_put() on a struct inet_timewait_sock pointer. To avoid this issue,\nuse sock_gen_put() instead of sock_put() when sk->sk_state\nis TCP_TIME_WAIT.\n\nmrdump.ko        ipanic() + 120\nvmlinux          notifier_call_chain(nr_to_call=-1, nr_calls=0) + 132\nvmlinux          atomic_notifier_call_chain(val=0) + 56\nvmlinux          panic() + 344\nvmlinux          add_taint() + 164\nvmlinux          end_report() + 136\nvmlinux          kasan_report(size=0) + 236\nvmlinux          report_tag_fault() + 16\nvmlinux          do_tag_recovery() + 16\nvmlinux          __do_kernel_fault() + 88\nvmlinux          do_bad_area() + 28\nvmlinux          do_tag_check_fault() + 60\nvmlinux          do_mem_abort() + 80\nvmlinux          el1_abort() + 56\nvmlinux          el1h_64_sync_handler() + 124\nvmlinux        > 0xFFFFFFC080011294()\nvmlinux          __lse_atomic_fetch_add_release(v=0xF2FFFF82A896087C)\nvmlinux          __lse_atomic_fetch_sub_release(v=0xF2FFFF82A896087C)\nvmlinux          arch_atomic_fetch_sub_release(i=1, v=0xF2FFFF82A896087C)\n+ 8\nvmlinux          raw_atomic_fetch_sub_release(i=1, v=0xF2FFFF82A896087C)\n+ 8\nvmlinux          atomic_fetch_sub_release(i=1, v=0xF2FFFF82A896087C) + 8\nvmlinux          __refcount_sub_and_test(i=1, r=0xF2FFFF82A896087C,\noldp=0) + 8\nvmlinux          __refcount_dec_and_test(r=0xF2FFFF82A896087C, oldp=0) + 8\nvmlinux          refcount_dec_and_test(r=0xF2FFFF82A896087C) + 8\nvmlinux          sk_free(sk=0xF2FFFF82A8960700) + 28\nvmlinux          sock_put() + 48\nvmlinux          tcp6_check_fraglist_gro() + 236\nvmlinux          tcp6_gro_receive() + 624\nvmlinux          ipv6_gro_receive() + 912\nvmlinux          dev_gro_receive() + 1116\nvmlinux          napi_gro_receive() + 196\nccmni.ko         ccmni_rx_callback() + 208\nccmni.ko         ccmni_queue_recv_skb() + 388\nccci_dpmaif.ko   dpmaif_rxq_push_thread() + 1088\nvmlinux          kthread() + 268\nvmlinux          0xFFFFFFC08001F30C()"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: usar sock_gen_put() cuando sk_state es TCP_TIME_WAIT Es posible que un puntero de tipo struct inet_timewait_sock sea devuelto desde las funciones __inet_lookup_established() y __inet6_lookup_established(). Esto puede causar un bloqueo cuando el puntero devuelto es de tipo struct inet_timewait_sock y se llama a sock_put() en él. La siguiente es una pila de llamadas de bloqueo que muestra que se accede a sk-&gt;sk_wmem_alloc en sk_free() durante la llamada a sock_put() en un puntero struct inet_timewait_sock. Para evitar este problema, use sock_gen_put() en lugar de sock_put() cuando sk-&gt;sk_state es TCP_TIME_WAIT. mrdump.ko ipanic() + 120 vmlinux notifier_call_chain(nr_to_call=-1, nr_calls=0) + 132 vmlinux atomic_notifier_call_chain(val=0) + 56 vmlinux panic() + 344 vmlinux add_taint() + 164 vmlinux end_report() + 136 vmlinux kasan_report(size=0) + 236 vmlinux report_tag_fault() + 16 vmlinux do_tag_recovery() + 16 vmlinux __do_kernel_fault() + 88 vmlinux do_bad_area() + 28 vmlinux do_tag_check_fault() + 60 vmlinux do_mem_abort() + 80 vmlinux el1_abort() + 56 vmlinux el1h_64_sync_handler() + 124 vmlinux &gt; 0xFFFFFFC080011294() vmlinux __lse_atomic_fetch_add_release(v=0xF2FFFF82A896087C) vmlinux __lse_atomic_fetch_sub_release(v=0xF2FFFF82A896087C) vmlinux arch_atomic_fetch_sub_release(i=1, v=0xF2FFFF82A896087C) + 8 vmlinux raw_atomic_fetch_sub_release(i=1, v=0xF2FFFF82A896087C) + 8 vmlinux atomic_fetch_sub_release(i=1, v=0xF2FFFF82A896087C) + 8 vmlinux __refcount_sub_and_test(i=1, r=0xF2FFFF82A896087C, p anterior=0) + 8 vmlinux __refcount_dec_and_test(r=0xF2FFFF82A896087C, p anterior=0) + 8 vmlinux refcount_dec_and_test(r=0xF2FFFF82A896087C) + 8 vmlinux sk_free(sk=0xF2FFFF82A8960700) + 28 vmlinux sock_put() + 48 vmlinux tcp6_check_fraglist_gro() + 236 vmlinux tcp6_gro_receive() + 624 vmlinux ipv6_gro_receive() + 912 vmlinux dev_gro_receive() + 1116 vmlinux napi_gro_receive() + 196 ccmni.ko ccmni_rx_callback() + 208 ccmni.ko ccmni_queue_recv_skb() + 388 ccci_dpmaif.ko dpmaif_rxq_push_thread() + 1088 vmlinux kthread() + 268 vmlinux 0xFFFFFFC08001F30C()"}], "references": [{"url": "https://git.kernel.org/stable/c/786650e644c5b1c063921799ca203c0b8670d79a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c0dba059b118b5206e755042b15b49368a388898", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f920436a44295ca791ebb6dae3f4190142eec703", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37973", "published_date": "2025-05-20T17:15:47.870", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: cfg80211: fix out-of-bounds access during multi-link element defragmentation\n\nCurrently during the multi-link element defragmentation process, the\nmulti-link element length added to the total IEs length when calculating\nthe length of remaining IEs after the multi-link element in\ncfg80211_defrag_mle(). This could lead to out-of-bounds access if the\nmulti-link element or its corresponding fragment elements are the last\nelements in the IEs buffer.\n\nTo address this issue, correctly calculate the remaining IEs length by\ndeducting the multi-link element end offset from total IEs end offset."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: cfg80211: corrección de acceso fuera de los límites durante la desfragmentación de elementos multienlace. Actualmente, durante el proceso de desfragmentación de elementos multienlace, la longitud de este elemento se sumaba a la longitud total de los elementos de entrada (IE) al calcular la longitud de los elementos de entrada restantes después del elemento multienlace en cfg80211_defrag_mle(). Esto podría provocar un acceso fuera de los límites si el elemento multienlace o sus elementos de fragmento correspondientes son los últimos elementos en el búfer de los elementos de entrada. Para solucionar este problema, calcule correctamente la longitud de los elementos de entrada restantes restando el desplazamiento final del elemento multienlace del desplazamiento final total de los elementos de entrada."}], "references": [{"url": "https://git.kernel.org/stable/c/023c1f2f0609218103cbcb48e0104b144d4a16dc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/73dde269a1a43e6b1aa92eba13ad2df58bfdd38e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9423f6da825172b8dc60d4688ed3d147291c3be9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e1c6d0c6199bd5f4cfc7a66ae7032b6e805f904d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
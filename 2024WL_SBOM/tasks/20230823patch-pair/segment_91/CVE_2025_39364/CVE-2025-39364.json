{"cve_id": "CVE-2025-39364", "published_date": "2025-05-19T17:15:25.213", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in PluginEver Product Category Slider for WooCommerce allows PHP Local File Inclusion.This issue affects Product Category Slider for WooCommerce: from n/a through 4.3.4."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión de archivo remoto PHP') en PluginEver Product Category Slider para WooCommerce permite la inclusión de archivos locales PHP. Este problema afecta a Product Category Slider para WooCommerce: desde n/a hasta 4.3.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/woo-category-slider-by-pluginever/vulnerability/wordpress-product-category-slider-for-woocommerce-plugin-4-3-4-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
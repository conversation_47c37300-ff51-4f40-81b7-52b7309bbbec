{"cve_id": "CVE-2025-37960", "published_date": "2025-05-20T16:15:34.267", "last_modified_date": "2025-05-22T13:15:56.003", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmemblock: Accept allocated memory before use in memblock_double_array()\n\nWhen increasing the array size in memblock_double_array() and the slab\nis not yet available, a call to memblock_find_in_range() is used to\nreserve/allocate memory. However, the range returned may not have been\naccepted, which can result in a crash when booting an SNP guest:\n\n  RIP: 0010:memcpy_orig+0x68/0x130\n  Code: ...\n  RSP: 0000:ffffffff9cc03ce8 EFLAGS: 00010006\n  RAX: ff11001ff83e5000 RBX: 0000000000000000 RCX: fffffffffffff000\n  RDX: 0000000000000bc0 RSI: ffffffff9dba8860 RDI: ff11001ff83e5c00\n  RBP: 0000000000002000 R08: 0000000000000000 R09: 0000000000002000\n  R10: 000000207fffe000 R11: 0000040000000000 R12: ffffffff9d06ef78\n  R13: ff11001ff83e5000 R14: ffffffff9dba7c60 R15: 0000000000000c00\n  memblock_double_array+0xff/0x310\n  memblock_add_range+0x1fb/0x2f0\n  memblock_reserve+0x4f/0xa0\n  memblock_alloc_range_nid+0xac/0x130\n  memblock_alloc_internal+0x53/0xc0\n  memblock_alloc_try_nid+0x3d/0xa0\n  swiotlb_init_remap+0x149/0x2f0\n  mem_init+0xb/0xb0\n  mm_core_init+0x8f/0x350\n  start_kernel+0x17e/0x5d0\n  x86_64_start_reservations+0x14/0x30\n  x86_64_start_kernel+0x92/0xa0\n  secondary_startup_64_no_verify+0x194/0x19b\n\nMitigate this by calling accept_memory() on the memory range returned\nbefore the slab is available.\n\nPrior to v6.12, the accept_memory() interface used a 'start' and 'end'\nparameter instead of 'start' and 'size', therefore the accept_memory()\ncall must be adjusted to specify 'start + size' for 'end' when applying\nto kernels prior to v6.12."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: memblock: aceptar memoria asignada antes de su uso en memblock_double_array(). Al aumentar el tamaño de la matriz en memblock_double_array() y la losa aún no está disponible, se utiliza una llamada a memblock_find_in_range() para reservar/asignar memoria. Sin embargo, es posible que no se haya aceptado el rango devuelto, lo que puede provocar un bloqueo al iniciar un invitado SNP: RIP: 0010:memcpy_orig+0x68/0x130 Código: ... RSP: 0000:ffffffff9cc03ce8 EFLAGS: 00010006 RAX: ff11001ff83e5000 RBX: 0000000000000000 RCX: fffffffffffff000 RDX: 0000000000000bc0 RSI: ffffffff9dba8860 RDI: ff11001ff83e5c00 RBP: 0000000000002000 R08: 000000000000000 R09: 0000000000002000 R10: 000000207fffe000 R11: 0000040000000000 R12: ffffffff9d06ef78 R13: ff11001ff83e5000 R14: ffffffff9dba7c60 R15: 0000000000000c00 memblock_double_array+0xff/0x310 memblock_add_range+0x1fb/0x2f0 memblock_reserve+0x4f/0xa0 memblock_alloc_range_nid+0xac/0x130 memblock_alloc_internal+0x53/0xc0 memblock_alloc_try_nid+0x3d/0xa0 swiotlb_init_remap+0x149/0x2f0 mem_init+0xb/0xb0 mm_core_init+0x8f/0x350 start_kernel+0x17e/0x5d0 x86_64_start_reservations+0x14/0x30 x86_64_start_kernel+0x92/0xa0 secondary_startup_64_no_verify+0x194/0x19b Para mitigar este problema, llame a accept_memory() en el rango de memoria devuelto antes de que el slab esté disponible. Antes de la versión 6.12, la interfaz accept_memory() utilizaba los parámetros \"start\" y \"end\" en lugar de \"start\" y \"size\". Por lo tanto, la llamada a accept_memory() debe ajustarse para especificar \"start + size\" en lugar de \"end\" al aplicarlo a kernels anteriores a la versión 6.12."}], "references": [{"url": "https://git.kernel.org/stable/c/7bcd29181bab8d508d2adfdbb132de8b1e088698", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aa513e69e011a2b19fa22ce62ce35effbd5e0c81", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d66a22f6a432a9dd376c9b365d7dc89bd416909c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da8bf5daa5e55a6af2b285ecda460d6454712ff4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
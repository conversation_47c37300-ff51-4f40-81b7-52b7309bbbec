{"cve_id": "CVE-2024-8854", "published_date": "2025-05-15T20:16:00.053", "last_modified_date": "2025-06-04T16:31:17.827", "descriptions": [{"lang": "en", "value": "The Polls CP WordPress plugin before 1.0.77 does not sanitise and escape some of its poll settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multi site setup)."}, {"lang": "es", "value": "El complemento Polls CP WordPress anterior a la versión 1.0.77 no depura ni escapa de algunas de sus configuraciones de encuesta, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/bffe0f75-33a2-4270-af13-835b8eb65688/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
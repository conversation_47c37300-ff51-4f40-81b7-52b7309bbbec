{"cve_id": "CVE-2025-37966", "published_date": "2025-05-20T17:15:47.020", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv: Fix kernel crash due to PR_SET_TAGGED_ADDR_CTRL\n\nWhen userspace does PR_SET_TAGGED_ADDR_CTRL, but Supm extension is not\navailable, the kernel crashes:\n\nOops - illegal instruction [#1]\n    [snip]\nepc : set_tagged_addr_ctrl+0x112/0x15a\n ra : set_tagged_addr_ctrl+0x74/0x15a\nepc : ffffffff80011ace ra : ffffffff80011a30 sp : ffffffc60039be10\n    [snip]\nstatus: 0000000200000120 badaddr: 0000000010a79073 cause: 0000000000000002\n    set_tagged_addr_ctrl+0x112/0x15a\n    __riscv_sys_prctl+0x352/0x73c\n    do_trap_ecall_u+0x17c/0x20c\n    andle_exception+0x150/0x15c\n\nFix it by checking if Supm is available."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv: Se corrige el fallo del kernel debido a PR_SET_TAGGED_ADDR_CTRL Cuando el espacio de usuario realiza PR_SET_TAGGED_ADDR_CTRL, pero la extensión Supm no está disponible, el kernel se bloquea: Oops - instrucción ilegal [#1] [snip] epc : set_tagged_addr_ctrl+0x112/0x15a ra : set_tagged_addr_ctrl+0x74/0x15a epc : ffffffff80011ace ra : ffffffff80011a30 sp : ffffffc60039be10 [snip] status: 0000000200000120 badaddr: 0000000010a79073 cause: 00000000000000002 set_tagged_addr_ctrl+0x112/0x15a __riscv_sys_prctl+0x352/0x73c do_trap_ecall_u+0x17c/0x20c andle_exception+0x150/0x15c Corríjalo verificando si Supm está disponible."}], "references": [{"url": "https://git.kernel.org/stable/c/4b595a2f5656cd45d534ed2160c94f7662adefe5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ae08d55807c099357c047dba17624b09414635dd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
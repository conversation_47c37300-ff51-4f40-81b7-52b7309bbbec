{"cve_id": "CVE-2025-37958", "published_date": "2025-05-20T16:15:34.027", "last_modified_date": "2025-06-27T11:15:25.057", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm/huge_memory: fix dereferencing invalid pmd migration entry\n\nWhen migrating a THP, concurrent access to the PMD migration entry during\na deferred split scan can lead to an invalid address access, as\nillustrated below.  To prevent this invalid access, it is necessary to\ncheck the PMD migration entry and return early.  In this context, there is\nno need to use pmd_to_swp_entry and pfn_swap_entry_to_page to verify the\nequality of the target folio.  Since the PMD migration entry is locked, it\ncannot be served as the target.\n\nMailing list discussion and explanation from <PERSON>: \"An anon_vma\nlookup points to a location which may contain the folio of interest, but\nmight instead contain another folio: and weeding out those other folios is\nprecisely what the \"folio != pmd_folio((*pmd)\" check (and the \"risk of\nreplacing the wrong folio\" comment a few lines above it) is for.\"\n\nBUG: unable to handle page fault for address: ffffea60001db008\nCPU: 0 UID: 0 PID: 2199114 Comm: tee Not tainted 6.14.0+ #4 NONE\nHardware name: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-debian-1.16.3-2 04/01/2014\nRIP: 0010:split_huge_pmd_locked+0x3b5/0x2b60\nCall Trace:\n<TASK>\ntry_to_migrate_one+0x28c/0x3730\nrmap_walk_anon+0x4f6/0x770\nunmap_folio+0x196/0x1f0\nsplit_huge_page_to_list_to_order+0x9f6/0x1560\ndeferred_split_scan+0xac5/0x12a0\nshrinker_debugfs_scan_write+0x376/0x470\nfull_proxy_write+0x15c/0x220\nvfs_write+0x2fc/0xcb0\nksys_write+0x146/0x250\ndo_syscall_64+0x6a/0x120\nentry_SYSCALL_64_after_hwframe+0x76/0x7e\n\nThe bug is found by syzkaller on an internal kernel, then confirmed on\nupstream."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm/huge_memory: se corrige la desreferenciación de una entrada de migración de PMD no válida. Al migrar un THP, el acceso simultáneo a la entrada de migración de PMD durante un escaneo dividido diferido puede provocar un acceso no válido a una dirección, como se ilustra a continuación. Para evitar este acceso no válido, es necesario comprobar la entrada de migración de PMD y regresar antes. En este contexto, no es necesario usar pmd_to_swp_entry ni pfn_swap_entry_to_page para verificar la igualdad del folio de destino. Dado que la entrada de migración de PMD está bloqueada, no puede servir como destino. Discusión y explicación de Hugh Dickins en la lista de correo: \"Una búsqueda anon_vma apunta a una ubicación que podría contener el folio de interés, pero que en su lugar podría contener otro folio: y eliminar esos otros folios es precisamente para lo que sirve la comprobación \"folio != pmd_folio((*pmd)\" (y el comentario \"riesgo de reemplazar el folio equivocado\" unas líneas más arriba).\" ERROR: no se puede gestionar el fallo de página para la dirección: ffffea60001db008 CPU: 0 UID: 0 PID: 2199114 Comm: tee No contaminado 6.14.0+ #4 NINGUNO Nombre del hardware: QEMU Standard PC (Q35 + ICH9, 2009), BIOS 1.16.3-debian-1.16.3-2 04/01/2014 RIP: 0010: pmd_enorme_división_bloqueada+0x3b5/0x2b60 Rastreo de llamadas:  try_to_migrate_one+0x28c/0x3730 rmap_walk_anon+0x4f6/0x770 unmap_folio+0x196/0x1f0 split_huge_page_to_list_to_order+0x9f6/0x1560 deferred_split_scan+0xac5/0x12a0 shrinker_debugfs_scan_write+0x376/0x470 full_proxy_write+0x15c/0x220 vfs_write+0x2fc/0xcb0 ksys_write+0x146/0x250 do_syscall_64+0x6a/0x120 entry_SYSCALL_64_after_hwframe+0x76/0x7e El error fue detectado por syzkaller en un kernel interno, y luego confirmado en upstream."}], "references": [{"url": "https://git.kernel.org/stable/c/22f6368768340260e862f35151d2e1c55cb1dc75", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3977946f61cdba87b6b5aaf7d7094e96089583a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6166c3cf405441f7147b322980144feb3cefc617", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/753f142f7ff7d2223a47105b61e1efd91587d711", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9468afbda3fbfcec21ac8132364dff3dab945faf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be6e843fc51a584672dfd9c4a6a24c8cb81d5fb7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ef5706bed97e240b4abf4233ceb03da7336bc775", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fbab262b0c8226c697af1851a424896ed47dedcc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
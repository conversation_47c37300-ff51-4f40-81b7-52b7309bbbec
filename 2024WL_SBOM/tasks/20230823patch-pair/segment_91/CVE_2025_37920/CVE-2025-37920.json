{"cve_id": "CVE-2025-37920", "published_date": "2025-05-20T16:15:28.603", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nxsk: Fix race condition in AF_XDP generic RX path\n\nMove rx_lock from xsk_socket to xsk_buff_pool.\nFix synchronization for shared umem mode in\ngeneric RX path where multiple sockets share\nsingle xsk_buff_pool.\n\nRX queue is exclusive to xsk_socket, while FILL\nqueue can be shared between multiple sockets.\nThis could result in race condition where two\nCPU cores access RX path of two different sockets\nsharing the same umem.\n\nProtect both queues by acquiring spinlock in shared\nxsk_buff_pool.\n\nLock contention may be minimized in the future by some\nper-thread FQ buffering.\n\nIt's safe and necessary to move spin_lock_bh(rx_lock)\nafter xsk_rcv_check():\n* xs->pool and spinlock_init is synchronized by\n  xsk_bind() -> xsk_is_bound() memory barriers.\n* xsk_rcv_check() may return true at the moment\n  of xsk_release() or xsk_unbind_dev(),\n  however this will not cause any data races or\n  race conditions. xsk_unbind_dev() removes xdp\n  socket from all maps and waits for completion\n  of all outstanding rx operations. Packets in\n  RX path will either complete safely or drop."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: xsk: Corrige la condición de ejecución en la ruta RX genérica AF_XDP Mueve rx_lock de xsk_socket a xsk_buff_pool. Corrige la sincronización para el modo umem compartido en la ruta RX genérica donde varios sockets comparten un solo xsk_buff_pool. La cola RX es exclusiva de xsk_socket, mientras que la cola FILL se puede compartir entre varios sockets. Esto podría resultar en una condición de ejecución donde dos núcleos de CPU acceden a la ruta RX de dos sockets diferentes que comparten el mismo umem. Protege ambas colas adquiriendo spinlock en xsk_buff_pool compartido. La contención de bloqueos se puede minimizar en el futuro mediante algún búfer FQ por subproceso. Es seguro y necesario mover spin_lock_bh(rx_lock) después de xsk_rcv_check(): * xs-&gt;pool y spinlock_init se sincronizan mediante barreras de memoria xsk_bind() -&gt; xsk_is_bound(). * xsk_rcv_check() puede devolver verdadero al ejecutar xsk_release() o xsk_unbind_dev(); sin embargo, esto no causará ejecucións de datos ni condiciones de ejecución. xsk_unbind_dev() elimina el socket xdp de todos los mapas y espera a que se completen todas las operaciones de recepción pendientes. Los paquetes en la ruta de recepción se completarán correctamente o se descartarán."}], "references": [{"url": "https://git.kernel.org/stable/c/65d3c570614b892257dc58a1b202908242ecf8fd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/75a240a3e8abf17b9e00b0ef0492b1bbaa932251", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a1356ac7749cafc4e27aa62c0c4604b5dca4983e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
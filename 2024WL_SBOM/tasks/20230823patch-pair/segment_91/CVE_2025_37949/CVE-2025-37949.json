{"cve_id": "CVE-2025-37949", "published_date": "2025-05-20T16:15:32.920", "last_modified_date": "2025-06-04T13:15:27.320", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nxenbus: Use kref to track req lifetime\n\n<PERSON><PERSON> reported seeing a NULL pointer fault in the xenbus_thread\ncallstack:\nBUG: kernel NULL pointer dereference, address: 0000000000000000\nRIP: e030:__wake_up_common+0x4c/0x180\nCall Trace:\n <TASK>\n __wake_up_common_lock+0x82/0xd0\n process_msg+0x18e/0x2f0\n xenbus_thread+0x165/0x1c0\n\nprocess_msg+0x18e is req->cb(req).  req->cb is set to xs_wake_up(), a\nthin wrapper around wake_up(), or xenbus_dev_queue_reply().  It seems\nlike it was xs_wake_up() in this case.\n\nIt seems like req may have woken up the xs_wait_for_reply(), which\nkfree()ed the req.  When xenbus_thread resumes, it faults on the zero-ed\ndata.\n\nLinux Device Drivers 2nd edition states:\n\"Normally, a wake_up call can cause an immediate reschedule to happen,\nmeaning that other processes might run before wake_up returns.\"\n... which would match the behaviour observed.\n\nChange to keeping two krefs on each request.  One for the caller, and\none for xenbus_thread.  Each will kref_put() when finished, and the last\nwill free it.\n\nThis use of kref matches the description in\nDocumentation/core-api/kref.rst"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: xenbus: Usar kref para rastrear el tiempo de vida de req. Marek informó haber visto un fallo de puntero nulo en la pila de llamadas xenbus_thread: ERROR: desreferencia de puntero nulo del kernel, dirección: 0000000000000000 RIP: e030:__wake_up_common+0x4c/0x180 Rastreo de llamadas:  __wake_up_common_lock+0x82/0xd0 process_msg+0x18e/0x2f0 xenbus_thread+0x165/0x1c0 process_msg+0x18e es req-&gt;cb(req). req-&gt;cb está configurado como xs_wake_up(), una envoltura ligera alrededor de wake_up(), o xenbus_dev_queue_reply(). Parece que en este caso era xs_wake_up(). Parece que req pudo haber despertado xs_wait_for_reply(), que liberó la solicitud. Cuando xenbus_thread se reanuda, falla en los datos con ceros. La segunda edición de los controladores de dispositivos de Linux indica: «Normalmente, una llamada wake_up puede provocar una reprogramación inmediata, lo que significa que otros procesos podrían ejecutarse antes de que wake_up regrese». ... lo cual coincidiría con el comportamiento observado. Se recomienda mantener dos krefs en cada solicitud: uno para el que realiza la llamada y otro para xenbus_thread. Cada uno ejecutará kref_put() al finalizar, y el último lo liberará. Este uso de kref coincide con la descripción en Documentation/core-api/kref.rst."}], "references": [{"url": "https://git.kernel.org/stable/c/0e94a246bb6d9538010b6c02d2b1d4717a97b2e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1f0304dfd9d217c2f8b04a9ef4b3258a66eedd27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2466b0f66795c3c426cacc8998499f38031dbb59", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4d260a5558df4650eb87bc41b2c9ac2d6b2ba447", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8b02f85e84dc6f7c150cef40ddb69af5a25659e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e9c8a0393b5f85f1820c565ab8105660f4e8f92", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cbfaf46b88a4c01b64c4186cdccd766c19ae644c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f1bcac367bc95631afbb918348f30dec887d0e1b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
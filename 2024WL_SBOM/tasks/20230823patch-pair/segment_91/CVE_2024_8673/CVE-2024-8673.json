{"cve_id": "CVE-2024-8673", "published_date": "2025-05-15T20:15:59.387", "last_modified_date": "2025-05-28T15:42:17.150", "descriptions": [{"lang": "en", "value": "The Z-Downloads WordPress plugin before 1.11.7 does not properly validate uploaded files allowing for the uploading of SVGs containing malicious JavaScript."}, {"lang": "es", "value": "El complemento Z-Downloads para WordPress anterior a la versión 1.11.7 no valida correctamente los archivos cargados, lo que permite cargar SVG que contienen JavaScript malicioso."}], "references": [{"url": "https://wpscan.com/vulnerability/fed2cd26-7ccb-419d-b589-978410953bf4/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
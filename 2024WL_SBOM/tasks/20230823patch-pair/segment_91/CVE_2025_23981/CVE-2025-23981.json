{"cve_id": "CVE-2025-23981", "published_date": "2025-05-19T16:15:27.560", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Takimi Themes CarZine allows Reflected XSS.This issue affects CarZine: from n/a through 1.4.6."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de entrada durante la generación de páginas web ('Cross-site Scripting') en Takimi Themes CarZine permite XSS reflejado. Este problema afecta a CarZine: desde n/a hasta 1.4.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/carzine/vulnerability/wordpress-carzine-theme-1-4-6-reflected-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
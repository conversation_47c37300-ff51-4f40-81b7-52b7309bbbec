{"cve_id": "CVE-2025-37957", "published_date": "2025-05-20T16:15:33.917", "last_modified_date": "2025-05-22T13:15:55.897", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: SVM: Forcibly leave SMM mode on SHUTDOWN interception\n\nPreviously, commit ed129ec9057f (\"KVM: x86: forcibly leave nested mode\non vCPU reset\") addressed an issue where a triple fault occurring in\nnested mode could lead to use-after-free scenarios. However, the commit\ndid not handle the analogous situation for System Management Mode (SMM).\n\nThis omission results in triggering a WARN when KVM forces a vCPU INIT\nafter SHUTDOWN interception while the vCPU is in SMM. This situation was\nreprodused using <PERSON><PERSON><PERSON><PERSON><PERSON> by:\n\n  1) Creating a KVM VM and vCPU\n  2) Sending a KVM_SMI ioctl to explicitly enter SMM\n  3) Executing invalid instructions causing consecutive exceptions and\n     eventually a triple fault\n\nThe issue manifests as follows:\n\n  WARNING: CPU: 0 PID: 25506 at arch/x86/kvm/x86.c:12112\n  kvm_vcpu_reset+0x1d2/0x1530 arch/x86/kvm/x86.c:12112\n  Modules linked in:\n  CPU: 0 PID: 25506 Comm: syz-executor.0 Not tainted\n  6.1.130-syzkaller-00157-g164fe5dde9b6 #0\n  Hardware name: QEMU Standard PC (i440FX + PIIX, 1996),\n  BIOS 1.12.0-1 04/01/2014\n  RIP: 0010:kvm_vcpu_reset+0x1d2/0x1530 arch/x86/kvm/x86.c:12112\n  Call Trace:\n   <TASK>\n   shutdown_interception+0x66/0xb0 arch/x86/kvm/svm/svm.c:2136\n   svm_invoke_exit_handler+0x110/0x530 arch/x86/kvm/svm/svm.c:3395\n   svm_handle_exit+0x424/0x920 arch/x86/kvm/svm/svm.c:3457\n   vcpu_enter_guest arch/x86/kvm/x86.c:10959 [inline]\n   vcpu_run+0x2c43/0x5a90 arch/x86/kvm/x86.c:11062\n   kvm_arch_vcpu_ioctl_run+0x50f/0x1cf0 arch/x86/kvm/x86.c:11283\n   kvm_vcpu_ioctl+0x570/0xf00 arch/x86/kvm/../../../virt/kvm/kvm_main.c:4122\n   vfs_ioctl fs/ioctl.c:51 [inline]\n   __do_sys_ioctl fs/ioctl.c:870 [inline]\n   __se_sys_ioctl fs/ioctl.c:856 [inline]\n   __x64_sys_ioctl+0x19a/0x210 fs/ioctl.c:856\n   do_syscall_x64 arch/x86/entry/common.c:51 [inline]\n   do_syscall_64+0x35/0x80 arch/x86/entry/common.c:81\n   entry_SYSCALL_64_after_hwframe+0x6e/0xd8\n\nArchitecturally, INIT is blocked when the CPU is in SMM, hence KVM's WARN()\nin kvm_vcpu_reset() to guard against KVM bugs, e.g. to detect improper\nemulation of INIT.  SHUTDOWN on SVM is a weird edge case where KVM needs to\ndo _something_ sane with the VMCB, since it's technically undefined, and\nINIT is the least awful choice given KVM's ABI.\n\nSo, double down on stuffing INIT on SHUTDOWN, and force the vCPU out of\nSMM to avoid any weirdness (and the WARN).\n\nFound by Linux Verification Center (linuxtesting.org) with Syzkaller.\n\n[sean: massage changelog, make it clear this isn't architectural behavior]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: SVM: Forzar la salida del modo SMM al interceptar el apagado. Anteriormente, el commit ed129ec9057f (\"KVM: x86: forzar la salida del modo anidado al reiniciar la vCPU\") solucionó un problema en el que una triple falla en el modo anidado podía provocar escenarios de use-after-free. Sin embargo, esta confirmación no solucionó la situación análoga para el modo de administración del sistema (SMM). Esta omisión provoca la activación de una advertencia cuando KVM fuerza una inicialización de la vCPU tras la interceptación del apagado mientras esta se encuentra en SMM. Esta situación se reprodujo utilizando Syzkaller mediante: 1) la creación de una máquina virtual KVM y vCPU 2) el envío de un ioctl KVM_SMI para ingresar explícitamente a SMM 3) la ejecución de instrucciones no válidas que causan excepciones consecutivas y, finalmente, un fallo triple El problema se manifiesta de la siguiente manera: ADVERTENCIA: CPU: 0 PID: 25506 en arch/x86/kvm/x86.c:12112 kvm_vcpu_reset+0x1d2/0x1530 arch/x86/kvm/x86.c:12112 Módulos vinculados en: CPU: 0 PID: 25506 Comm: syz-executor.0 No contaminado 6.1.130-syzkaller-00157-g164fe5dde9b6 #0 Nombre del hardware: QEMU Standard PC (i440FX + PIIX, 1996), BIOS 1.12.0-1 01/04/2014 RIP: 0010:kvm_vcpu_reset+0x1d2/0x1530 arch/x86/kvm/x86.c:12112 Rastreo de llamadas:  shutdown_interception+0x66/0xb0 arch/x86/kvm/svm/svm.c:2136 svm_invoke_exit_handler+0x110/0x530 arch/x86/kvm/svm/svm.c:3395 svm_handle_exit+0x424/0x920 arch/x86/kvm/svm/svm.c:3457 vcpu_enter_guest arch/x86/kvm/x86.c:10959 [en línea] vcpu_run+0x2c43/0x5a90 arch/x86/kvm/x86.c:11062 kvm_arch_vcpu_ioctl_run+0x50f/0x1cf0 arch/x86/kvm/x86.c:11283 kvm_vcpu_ioctl+0x570/0xf00 arch/x86/kvm/../../../virt/kvm/kvm_main.c:4122 vfs_ioctl fs/ioctl.c:51 [en línea] __do_sys_ioctl fs/ioctl.c:870 [en línea] __se_sys_ioctl fs/ioctl.c:856 [en línea] __x64_sys_ioctl+0x19a/0x210 fs/ioctl.c:856 do_syscall_x64 arch/x86/entry/common.c:51 [en línea] do_syscall_64+0x35/0x80 arch/x86/entry/common.c:81 entry_SYSCALL_64_after_hwframe+0x6e/0xd8 Arquitectónicamente, INIT se bloquea cuando la CPU está en SMM, de ahí el WARN() de KVM en kvm_vcpu_reset() para protegerse contra errores de KVM, por ejemplo, para detectar una emulación incorrecta de INIT. SHUTDOWN en SVM es un caso extremo extraño en el que KVM necesita hacer _algo_ sensato con el VMCB, ya que técnicamente no está definido, e INIT es la opción menos terrible dada la ABI de KVM. Así que, redobla la apuesta por el uso excesivo de INIT al apagar y fuerza la salida de la vCPU de SMM para evitar cualquier anomalía (y la advertencia). Encontrado por el Centro de Verificación de Linux (linuxtesting.org) con Syzkaller. [sean: revisa el registro de cambios, aclara que esto no es un comportamiento arquitectónico]"}], "references": [{"url": "https://git.kernel.org/stable/c/a2620f8932fa9fdabc3d78ed6efb004ca409019f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d362b21fefcef7eda8f1cd78a5925735d2b3287c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e9b28bc65fd3a56755ba503258024608292b4ab1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ec24e62a1dd3540ee696314422040180040c1e4a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
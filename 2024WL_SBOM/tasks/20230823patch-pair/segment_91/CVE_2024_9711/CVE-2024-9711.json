{"cve_id": "CVE-2024-9711", "published_date": "2025-05-15T20:16:01.200", "last_modified_date": "2025-05-28T15:41:03.570", "descriptions": [{"lang": "en", "value": "The EKC Tournament Manager WordPress plugin before 2.2.2 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin change them via a CSRF attack"}, {"lang": "es", "value": "El complemento EKC Tournament Manager de WordPress anterior a la versión 2.2.2 no tiene la verificación CSRF activada al actualizar sus configuraciones, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión las cambie mediante un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/daee95c5-006e-4a83-b92a-7faa3e89d985/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
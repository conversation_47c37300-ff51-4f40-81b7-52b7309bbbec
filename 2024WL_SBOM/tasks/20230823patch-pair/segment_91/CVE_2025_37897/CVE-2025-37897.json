{"cve_id": "CVE-2025-37897", "published_date": "2025-05-20T16:15:26.067", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: plfxlc: <PERSON><PERSON>ve erroneous assert in plfxlc_mac_release\n\nplfxlc_mac_release() asserts that mac->lock is held. This assertion is\nincorrect, because even if it was possible, it would not be the valid\nbehaviour. The function is used when probe fails or after the device is\ndisconnected. In both cases mac->lock can not be held as the driver is\nnot working with the device at the moment. All functions that use mac->lock\nunlock it just after it was held. There is also no need to hold mac->lock\nfor plfxlc_mac_release() itself, as mac data is not affected, except for\nmac->flags, which is modified atomically.\n\nThis bug leads to the following warning:\n================================================================\nWARNING: CPU: 0 PID: 127 at drivers/net/wireless/purelifi/plfxlc/mac.c:106 plfxlc_mac_release+0x7d/0xa0\nModules linked in:\nCPU: 0 PID: 127 Comm: kworker/0:2 Not tainted 6.1.124-syzkaller #0\nHardware name: Google Google Compute Engine/Google Compute Engine, BIOS Google 09/13/2024\nWorkqueue: usb_hub_wq hub_event\nRIP: 0010:plfxlc_mac_release+0x7d/0xa0 drivers/net/wireless/purelifi/plfxlc/mac.c:106\nCall Trace:\n <TASK>\n probe+0x941/0xbd0 drivers/net/wireless/purelifi/plfxlc/usb.c:694\n usb_probe_interface+0x5c0/0xaf0 drivers/usb/core/driver.c:396\n really_probe+0x2ab/0xcb0 drivers/base/dd.c:639\n __driver_probe_device+0x1a2/0x3d0 drivers/base/dd.c:785\n driver_probe_device+0x50/0x420 drivers/base/dd.c:815\n __device_attach_driver+0x2cf/0x510 drivers/base/dd.c:943\n bus_for_each_drv+0x183/0x200 drivers/base/bus.c:429\n __device_attach+0x359/0x570 drivers/base/dd.c:1015\n bus_probe_device+0xba/0x1e0 drivers/base/bus.c:489\n device_add+0xb48/0xfd0 drivers/base/core.c:3696\n usb_set_configuration+0x19dd/0x2020 drivers/usb/core/message.c:2165\n usb_generic_driver_probe+0x84/0x140 drivers/usb/core/generic.c:238\n usb_probe_device+0x130/0x260 drivers/usb/core/driver.c:293\n really_probe+0x2ab/0xcb0 drivers/base/dd.c:639\n __driver_probe_device+0x1a2/0x3d0 drivers/base/dd.c:785\n driver_probe_device+0x50/0x420 drivers/base/dd.c:815\n __device_attach_driver+0x2cf/0x510 drivers/base/dd.c:943\n bus_for_each_drv+0x183/0x200 drivers/base/bus.c:429\n __device_attach+0x359/0x570 drivers/base/dd.c:1015\n bus_probe_device+0xba/0x1e0 drivers/base/bus.c:489\n device_add+0xb48/0xfd0 drivers/base/core.c:3696\n usb_new_device+0xbdd/0x18f0 drivers/usb/core/hub.c:2620\n hub_port_connect drivers/usb/core/hub.c:5477 [inline]\n hub_port_connect_change drivers/usb/core/hub.c:5617 [inline]\n port_event drivers/usb/core/hub.c:5773 [inline]\n hub_event+0x2efe/0x5730 drivers/usb/core/hub.c:5855\n process_one_work+0x8a9/0x11d0 kernel/workqueue.c:2292\n worker_thread+0xa47/0x1200 kernel/workqueue.c:2439\n kthread+0x28d/0x320 kernel/kthread.c:376\n ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:295\n </TASK>\n================================================================\n\nFound by Linux Verification Center (linuxtesting.org) with Syzkaller."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: plfxlc: Eliminar aserción errónea en plfxlc_mac_release. plfxlc_mac_release() afirma que mac-&gt;lock se mantiene. Esta aserción es incorrecta, ya que, incluso si fuera posible, no sería el comportamiento válido. La función se utiliza cuando falla la sonda o después de desconectar el dispositivo. En ambos casos, mac-&gt;lock no se puede mantener, ya que el controlador no está trabajando con el dispositivo en ese momento. Todas las funciones que usan mac-&gt;lock lo desbloquean justo después de mantenerlo. Tampoco es necesario mantener mac-&gt;lock para plfxlc_mac_release(), ya que los datos de mac no se ven afectados, excepto mac-&gt;flags, que se modifica automáticamente. Este error genera la siguiente advertencia: ================================================================== ADVERTENCIA: CPU: 0 PID: 127 en drivers/net/wireless/purelifi/plfxlc/mac.c:106 plfxlc_mac_release+0x7d/0xa0 Módulos vinculados: CPU: 0 PID: 127 Comm: kworker/0:2 No contaminado 6.1.124-syzkaller #0 Nombre del hardware: Google Google Compute Engine/Google Compute Engine, BIOS Google 13/09/2024 Cola de trabajo: usb_hub_wq hub_event RIP: 0010:plfxlc_mac_release+0x7d/0xa0 controladores/red/inalámbrico/purelifi/plfxlc/mac.c:106 Rastreo de llamadas:  probe+0x941/0xbd0 drivers/net/wireless/purelifi/plfxlc/usb.c:694 usb_probe_interface+0x5c0/0xaf0 drivers/usb/core/driver.c:396 really_probe+0x2ab/0xcb0 drivers/base/dd.c:639 __driver_probe_device+0x1a2/0x3d0 drivers/base/dd.c:785 driver_probe_device+0x50/0x420 drivers/base/dd.c:815 __device_attach_driver+0x2cf/0x510 drivers/base/dd.c:943 bus_for_each_drv+0x183/0x200 drivers/base/bus.c:429 __device_attach+0x359/0x570 drivers/base/dd.c:1015 bus_probe_device+0xba/0x1e0 drivers/base/bus.c:489 device_add+0xb48/0xfd0 drivers/base/core.c:3696 usb_set_configuration+0x19dd/0x2020 drivers/usb/core/message.c:2165 usb_generic_driver_probe+0x84/0x140 drivers/usb/core/generic.c:238 usb_probe_device+0x130/0x260 drivers/usb/core/driver.c:293 really_probe+0x2ab/0xcb0 drivers/base/dd.c:639 __driver_probe_device+0x1a2/0x3d0 drivers/base/dd.c:785 driver_probe_device+0x50/0x420 drivers/base/dd.c:815 __device_attach_driver+0x2cf/0x510 drivers/base/dd.c:943 bus_for_each_drv+0x183/0x200 drivers/base/bus.c:429 __device_attach+0x359/0x570 drivers/base/dd.c:1015 bus_probe_device+0xba/0x1e0 drivers/base/bus.c:489 device_add+0xb48/0xfd0 drivers/base/core.c:3696 usb_new_device+0xbdd/0x18f0 drivers/usb/core/hub.c:2620 hub_port_connect drivers/usb/core/hub.c:5477 [inline] hub_port_connect_change drivers/usb/core/hub.c:5617 [inline] port_event drivers/usb/core/hub.c:5773 [inline] hub_event+0x2efe/0x5730 drivers/usb/core/hub.c:5855 process_one_work+0x8a9/0x11d0 kernel/workqueue.c:2292 worker_thread+0xa47/0x1200 kernel/workqueue.c:2439 kthread+0x28d/0x320 kernel/kthread.c:376 ret_from_fork+0x1f/0x30 arch/x86/entry/entry_64.S:295  ================================================================ Encontrado por el Centro de verificación de Linux (linuxtesting.org) con Syzkaller."}], "references": [{"url": "https://git.kernel.org/stable/c/0fb15ae3b0a9221be01715dac0335647c79f3362", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/36a9a2647810e57e704dde59abdf831380ca9102", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/791a2d9e87c411aec0b9b2fb735fd15e48af9de9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/93d646911be1e5be20d4f5d6c48359464cef0097", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ecb4af39f80cdda3e57825923243ec11e48be6b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
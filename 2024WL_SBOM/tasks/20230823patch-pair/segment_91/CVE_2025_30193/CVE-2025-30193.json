{"cve_id": "CVE-2025-30193", "published_date": "2025-05-20T12:15:19.697", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In some circumstances, when DNSdist is configured to allow an unlimited number of queries on a single, incoming TCP connection from a client, an attacker can cause a denial of service by crafting a TCP exchange that triggers an exhaustion of the stack and a crash of DNSdist, causing a denial of service.\n\nThe remedy is: upgrade to the patched 1.9.10 version.\n\nA workaround is to restrict the maximum number of queries on incoming TCP connections to a safe value, like 50, via the setMaxTCPQueriesPerConnection setting.\n\nWe would like to thank <PERSON><PERSON> for bringing this issue to our attention."}, {"lang": "es", "value": "En algunas circunstancias, cuando DNSdist está configurado para permitir un número ilimitado de consultas en una única conexión TCP entrante desde un cliente, un atacante puede provocar una denegación de servicio manipulando un intercambio TCP que provoca el agotamiento de la pila y un bloqueo de DNSdist, lo que provoca una denegación de servicio. La solución es actualizar a la versión 1.9.10 con el parche. Una alternativa consiste en restringir el número máximo de consultas en las conexiones TCP entrantes a un valor seguro, como 50, mediante la configuración setMaxTCPQueriesPerConnection. Agradecemos a Renaud Allard por informarnos sobre este problema."}], "references": [{"url": "https://dnsdist.org/security-advisories/powerdns-advisory-for-dnsdist-2025-03.html", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27010", "published_date": "2025-05-19T18:15:28.497", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Path Traversal: '.../...//' vulnerability in bslthemes Tastyc allows PHP Local File Inclusion.This issue affects Tastyc: from n/a before 2.5.2."}, {"lang": "es", "value": "Path Traversal: la vulnerabilidad '.../...//' en bslthemes Tastyc permite la inclusión de archivos locales en PHP. Este problema afecta a Tastyc: desde n/a antes de 2.5.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/tastyc/vulnerability/wordpress-tastyc-2-5-2-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
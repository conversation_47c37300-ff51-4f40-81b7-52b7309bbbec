{"cve_id": "CVE-2025-37899", "published_date": "2025-05-20T16:15:26.273", "last_modified_date": "2025-05-24T19:15:23.640", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: fix use-after-free in session logoff\n\nThe sess->user object can currently be in use by another thread, for\nexample if another connection has sent a session setup request to\nbind to the session being free'd. The handler for that connection could\nbe in the smb2_sess_setup function which makes use of sess->user."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: corrección del problema \"use-after-free\" al cerrar sesión. El objeto sess-&gt;user puede estar siendo utilizado por otro hilo, por ejemplo, si otra conexión ha enviado una solicitud de configuración de sesión para enlazarse a la sesión que se está liberando. El controlador de esa conexión podría estar en la función smb2_sess_setup, que utiliza sess-&gt;user."}], "references": [{"url": "https://git.kernel.org/stable/c/02d16046cd11a5c037b28c12ffb818c56dd3ef43", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2fc9feff45d92a92cd5f96487655d5be23fb7e2b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d5ec1d79509b3ee01de02c236f096bc050221b7f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://news.ycombinator.com/item?id=44081338", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://sean.heelan.io/2025/05/22/how-i-used-o3-to-find-cve-2025-37899-a-remote-zeroday-vulnerability-in-the-linux-kernels-smb-implementation/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
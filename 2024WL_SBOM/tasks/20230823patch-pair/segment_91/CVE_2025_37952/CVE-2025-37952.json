{"cve_id": "CVE-2025-37952", "published_date": "2025-05-20T16:15:33.353", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: Fix UAF in __close_file_table_ids\n\nA use-after-free is possible if one thread destroys the file\nvia __ksmbd_close_fd while another thread holds a reference to\nit. The existing checks on fp->refcount are not sufficient to\nprevent this.\n\nThe fix takes ft->lock around the section which removes the\nfile from the file table. This prevents two threads acquiring the\nsame file pointer via __close_file_table_ids, as well as the other\nfunctions which retrieve a file from the IDR and which already use\nthis same lock."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: Corrección de UAF en __close_file_table_ids. Es posible un use-after-free si un hilo destruye el archivo mediante __ksmbd_close_fd mientras otro hilo mantiene una referencia a él. Las comprobaciones existentes en fp-&gt;refcount no son suficientes para evitarlo. La corrección aplica ft-&gt;lock a la sección que elimina el archivo de la tabla de archivos. Esto impide que dos hilos adquieran el mismo puntero de archivo mediante __close_file_table_ids, así como otras funciones que recuperan un archivo del IDR y que ya utilizan este mismo bloqueo."}], "references": [{"url": "https://git.kernel.org/stable/c/16727e442568a46d9cca69fe2595896de86e120d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/36991c1ccde2d5a521577c448ffe07fcccfe104d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e9841e232b51171ddf3bc4ee517d5d28dc8cad6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fec1f9e9a650e8e7011330a085c77e7bf2a08ea9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
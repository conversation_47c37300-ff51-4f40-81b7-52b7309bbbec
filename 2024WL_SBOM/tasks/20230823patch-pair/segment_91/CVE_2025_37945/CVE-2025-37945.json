{"cve_id": "CVE-2025-37945", "published_date": "2025-05-20T16:15:32.453", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: phy: allow MDIO bus PM ops to start/stop state machine for phylink-controlled PHY\n\nDSA has 2 kinds of drivers:\n\n1. Those who call dsa_switch_suspend() and dsa_switch_resume() from\n   their device PM ops: qca8k-8xxx, bcm_sf2, microchip ksz\n2. Those who don't: all others. The above methods should be optional.\n\nFor type 1, dsa_switch_suspend() calls dsa_user_suspend() -> phylink_stop(),\nand dsa_switch_resume() calls dsa_user_resume() -> phylink_start().\nThese seem good candidates for setting mac_managed_pm = true because\nthat is essentially its definition [1], but that does not seem to be the\nbiggest problem for now, and is not what this change focuses on.\n\nTalking strictly about the 2nd category of DSA drivers here (which\ndo not have MAC managed PM, meaning that for their attached PHYs,\nmdio_bus_phy_suspend() and mdio_bus_phy_resume() should run in full),\nI have noticed that the following warning from mdio_bus_phy_resume() is\ntriggered:\n\n\tWARN_ON(phydev->state != PHY_HALTED && phydev->state != PHY_READY &&\n\t\tphydev->state != PHY_UP);\n\nbecause the PHY state machine is running.\n\nIt's running as a result of a previous dsa_user_open() -> ... ->\nphylink_start() -> phy_start() having been initiated by the user.\n\nThe previous mdio_bus_phy_suspend() was supposed to have called\nphy_stop_machine(), but it didn't. So this is why the PHY is in state\nPHY_NOLINK by the time mdio_bus_phy_resume() runs.\n\nmdio_bus_phy_suspend() did not call phy_stop_machine() because for\nphylink, the phydev->adjust_link function pointer is NULL. This seems a\ntechnicality introduced by commit fddd91016d16 (\"phylib: fix PAL state\nmachine restart on resume\"). That commit was written before phylink\nexisted, and was intended to avoid crashing with consumer drivers which\ndon't use the PHY state machine - phylink always does, when using a PHY.\nBut phylink itself has historically not been developed with\nsuspend/resume in mind, and apparently not tested too much in that\nscenario, allowing this bug to exist unnoticed for so long. Plus, prior\nto the WARN_ON(), it would have likely been invisible.\n\nThis issue is not in fact restricted to type 2 DSA drivers (according to\nthe above ad-hoc classification), but can be extrapolated to any MAC\ndriver with phylink and MDIO-bus-managed PHY PM ops. DSA is just where\nthe issue was reported. Assuming mac_managed_pm is set correctly, a\nquick search indicates the following other drivers might be affected:\n\n$ grep -Zlr PHYLINK_NETDEV drivers/ | xargs -0 grep -L mac_managed_pm\ndrivers/net/ethernet/atheros/ag71xx.c\ndrivers/net/ethernet/microchip/sparx5/sparx5_main.c\ndrivers/net/ethernet/microchip/lan966x/lan966x_main.c\ndrivers/net/ethernet/freescale/dpaa2/dpaa2-mac.c\ndrivers/net/ethernet/freescale/fs_enet/fs_enet-main.c\ndrivers/net/ethernet/freescale/dpaa/dpaa_eth.c\ndrivers/net/ethernet/freescale/ucc_geth.c\ndrivers/net/ethernet/freescale/enetc/enetc_pf_common.c\ndrivers/net/ethernet/marvell/mvpp2/mvpp2_main.c\ndrivers/net/ethernet/marvell/mvneta.c\ndrivers/net/ethernet/marvell/prestera/prestera_main.c\ndrivers/net/ethernet/mediatek/mtk_eth_soc.c\ndrivers/net/ethernet/altera/altera_tse_main.c\ndrivers/net/ethernet/wangxun/txgbe/txgbe_phy.c\ndrivers/net/ethernet/meta/fbnic/fbnic_phylink.c\ndrivers/net/ethernet/tehuti/tn40_phy.c\ndrivers/net/ethernet/mscc/ocelot_net.c\n\nMake the existing conditions dependent on the PHY device having a\nphydev->phy_link_change() implementation equal to the default\nphy_link_change() provided by phylib. Otherwise, we implicitly know that\nthe phydev has the phylink-provided phylink_phy_change() callback, and\nwhen phylink is used, the PHY state machine always needs to be stopped/\nstarted on the suspend/resume path. The code is structured as such that\nif phydev->phy_link_change() is absent, it is a matter of time until the\nkernel will crash - no need to further complicate the test.\n\nThus, for the situation where the PM is not managed b\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: phy: permite que las operaciones PM del bus MDIO inicien o detengan la máquina de estados para PHY controlada por phylink DSA tiene 2 tipos de controladores: 1. Los que llaman a dsa_switch_suspend() y dsa_switch_resume() desde las operaciones PM de su dispositivo: qca8k-8xxx, bcm_sf2, microchip ksz 2. Los que no lo hacen: todos los demás. Los métodos anteriores deberían ser opcionales. Para el tipo 1, dsa_switch_suspend() llama a dsa_user_suspend() -&gt; phylink_stop() y dsa_switch_resume() llama a dsa_user_resume() -&gt; phylink_start(). Estos parecen buenos candidatos para establecer mac_managed_pm = true porque esa es esencialmente su definición [1], pero ese no parece ser el mayor problema por ahora y no es en lo que se centra este cambio. Hablando estrictamente sobre la segunda categoría de controladores DSA (que no tienen PM administrado por MAC, lo que significa que para sus PHYs conectados, mdio_bus_phy_suspend() y mdio_bus_phy_resume() deben ejecutarse por completo), he notado que se activa la siguiente advertencia de mdio_bus_phy_resume(): WARN_ON(phydev-&gt;state != PHY_HALTED &amp;&amp; phydev-&gt;state != PHY_READY &amp;&amp; phydev-&gt;state != PHY_UP); porque la máquina de estados PHY está en ejecución. Se está ejecutando como resultado de un dsa_user_open() -&gt; ... -&gt; phylink_start() -&gt; phy_start() previo iniciado por el usuario. El mdio_bus_phy_suspend() anterior debería haber llamado a phy_stop_machine(), pero no lo hizo. Por eso, el PHY está en estado PHY_NOLINK cuando se ejecuta mdio_bus_phy_resume(). mdio_bus_phy_suspend() no llamó a phy_stop_machine() porque, para phylink, el puntero a la función phydev-&gt;adjust_link es NULL. Esto parece ser un tecnicismo introducido por el commit fddd91016d16 (\"phylib: corrección del reinicio de la máquina de estados PAL al reanudar\"). Esta confirmación se escribió antes de que existiera phylink y su objetivo era evitar fallos con controladores de consumidor que no utilizan la máquina de estados PHY; phylink siempre lo hace al usar una PHY. Sin embargo, phylink no se ha desarrollado históricamente con la suspensión/reanudación en mente, y aparentemente no se ha probado demasiado en ese escenario, lo que ha permitido que este error pase desapercibido durante tanto tiempo. Además, antes de WARN_ON(), probablemente habría sido invisible. De hecho, este problema no se limita a los controladores DSA de tipo 2 (según la clasificación ad hoc anterior), sino que se puede extrapolar a cualquier controlador MAC con phylink y operaciones de PM PHY gestionadas por bus MDIO. DSA es justo donde se reportó el problema. Suponiendo que mac_managed_pm esté configurado correctamente, una búsqueda rápida indica que los siguientes controladores podrían estar afectados: $ grep -Zlr PHYLINK_NETDEV drivers/ | xargs -0 grep -L mac_managed_pm drivers/net/ethernet/atheros/ag71xx.c drivers/net/ethernet/microchip/sparx5/sparx5_main.c drivers/net/ethernet/microchip/lan966x/lan966x_main.c drivers/net/ethernet/freescale/dpaa2/dpaa2-mac.c drivers/net/ethernet/freescale/fs_enet/fs_enet-main.c drivers/net/ethernet/freescale/dpaa/dpaa_eth.c drivers/net/ethernet/freescale/ucc_geth.c drivers/net/ethernet/freescale/enetc/enetc_pf_common.c drivers/net/ethernet/marvell/mvpp2/mvpp2_main.c drivers/net/ethernet/marvell/mvneta.c Haga que las condiciones existentes dependan de que el dispositivo PHY tenga una implementación de phydev-&gt;phy_link_change() igual al phy_link_change() predeterminado proporcionado por phylib. De lo contrario, sabemos implícitamente que phydev cuenta con la función de devolución de llamada phylink_phy_change() proporcionada por phylink, y cuando se usa phylink, la máquina de estados PHY siempre debe detenerse/iniciar en la ruta de suspensión/reinicio. El código está estructurado de tal manera que, si phydev-&gt;phy_link_change() no está presente, es cuestión de tiempo hasta que el kernel se bloquee; no es necesario complicar aún más la prueba. Por lo tanto, si el PM no se gestiona, se trunca."}], "references": [{"url": "https://git.kernel.org/stable/c/54e5d00a8de6c13f6c01a94ed48025e882cd15f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a6ed6f8ec81b8ca7100dcd9e62bdbc0dff1b2259", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bd4037d51d3f6667636a1383e78e48a5b7b60755", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fc75ea20ffb452652f0d4033f38fe88d7cfdae35", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
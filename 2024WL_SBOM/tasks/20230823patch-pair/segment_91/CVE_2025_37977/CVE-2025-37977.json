{"cve_id": "CVE-2025-37977", "published_date": "2025-05-20T17:15:48.320", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: ufs: exynos: Disable iocc if dma-coherent property isn't set\n\nIf dma-coherent property isn't set then descriptors are non-cacheable\nand the iocc shareability bits should be disabled. Without this UFS can\nend up in an incompatible configuration and suffer from random cache\nrelated stability issues."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: ufs: exynos: Deshabilitar iocc si la propiedad dma-coherent no está establecida. Si la propiedad dma-coherent no está establecida, los descriptores no se pueden almacenar en caché y los bits de compartición de iocc deben estar deshabilitados. Sin esto, UFS puede terminar en una configuración incompatible y sufrir problemas de estabilidad aleatorios relacionados con la caché."}], "references": [{"url": "https://git.kernel.org/stable/c/869749e48115ef944eeabec8e84138908471fa51", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f0c6728a6f2e269ebb234a9b5bb6c2c24aafeb51", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f92bb7436802f8eb7ee72dc911a33c8897fde366", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
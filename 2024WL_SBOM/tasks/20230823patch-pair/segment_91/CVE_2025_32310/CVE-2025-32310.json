{"cve_id": "CVE-2025-32310", "published_date": "2025-05-16T16:15:39.927", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in ThemeMove QuickCal allows Privilege Escalation. This issue affects QuickCal: from n/a through 1.0.13."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en ThemeMove QuickCal permite la escalada de privilegios. Este problema afecta a QuickCal desde la versión n/d hasta la 1.0.13."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/quickcal/vulnerability/wordpress-quickcal-plugin-1-0-13-csrf-to-privilege-escalation-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
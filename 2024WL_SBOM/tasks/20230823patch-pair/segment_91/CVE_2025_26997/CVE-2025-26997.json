{"cve_id": "CVE-2025-26997", "published_date": "2025-05-19T18:15:28.320", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in validas Wireless Butler allows Reflected XSS.This issue affects Wireless Butler: from n/a through 1.0.11."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en validas Wireless Butler permite XSS reflejado. Este problema afecta a Wireless Butler: desde n/a hasta 1.0.11."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/wireless-butler/vulnerability/wordpress-wireless-butler-plugin-1-0-11-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
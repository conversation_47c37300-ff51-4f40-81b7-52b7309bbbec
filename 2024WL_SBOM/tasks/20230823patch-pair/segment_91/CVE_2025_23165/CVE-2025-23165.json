{"cve_id": "CVE-2025-23165", "published_date": "2025-05-19T02:15:17.370", "last_modified_date": "2025-05-19T14:15:22.683", "descriptions": [{"lang": "en", "value": "In Node.js, the `ReadFileUtf8` internal binding leaks memory due to a corrupted pointer in `uv_fs_s.file`: a UTF-16 path buffer is allocated but subsequently overwritten when the file descriptor is set. This results in an unrecoverable memory leak on every call. Repeated use can cause unbounded memory growth, leading to a denial of service.\r\n\r\nImpact:\r\n* This vulnerability affects APIs relying on `ReadFileUtf8` on Node.js release lines: v20 and v22."}, {"lang": "es", "value": "En Node.js, el enlace interno de `ReadFileUtf8` causa una pérdida de memoria debido a un puntero dañado en `uv_fs_s.file`: se asigna un búfer de ruta UTF-16, pero posteriormente se sobrescribe al configurar el descriptor de archivo. Esto provoca una pérdida de memoria irrecuperable en cada llamada. El uso repetido puede causar un crecimiento descontrolado de la memoria, lo que resulta en una denegación de servicio. Impacto: * Esta vulnerabilidad afecta a las API que dependen de `ReadFileUtf8` en las versiones v20 y v22 de Node.js."}], "references": [{"url": "https://nodejs.org/en/blog/vulnerability/may-2025-security-releases", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37967", "published_date": "2025-05-20T17:15:47.130", "last_modified_date": "2025-05-22T13:15:56.110", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: typec: ucsi: displayport: Fix deadlock\n\nThis patch introduces the ucsi_con_mutex_lock / ucsi_con_mutex_unlock\nfunctions to the UCSI driver. ucsi_con_mutex_lock ensures the connector\nmutex is only locked if a connection is established and the partner pointer\nis valid. This resolves a deadlock scenario where\nucsi_displayport_remove_partner holds con->mutex waiting for\ndp_altmode_work to complete while dp_altmode_work attempts to acquire it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: typec: ucsi: displayport: Corrección de interbloqueo. Este parche incorpora las funciones ucsi_con_mutex_lock y ucsi_con_mutex_unlock al controlador UCSI. ucsi_con_mutex_lock garantiza que el mutex del conector solo se bloquee si se establece una conexión y el puntero del asociado es válido. Esto resuelve un escenario de interbloqueo donde ucsi_displayport_remove_partner mantiene con-&gt;mutex esperando a que dp_altmode_work complete su ejecución mientras dp_altmode_work intenta adquirirlo."}], "references": [{"url": "https://git.kernel.org/stable/c/364618c89d4c57c85e5fc51a2446cd939bf57802", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5924b324468845fc795bd76f588f51d7ab4f202d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/61fc1a8e1e10cc784cab5829930838aaf1d37af5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/962ce9028ca6eb450d5c205238a3ee27de9d214d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f32451ca4cb7dc53f2a0e2e66b84d34162747eb7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f4bd982563c2fd41ec9ca6c517c392d759db801c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
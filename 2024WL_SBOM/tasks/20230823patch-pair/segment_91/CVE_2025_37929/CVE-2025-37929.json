{"cve_id": "CVE-2025-37929", "published_date": "2025-05-20T16:15:29.483", "last_modified_date": "2025-06-27T11:15:24.827", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64: errata: Add missing sentinels to Spectre-BHB MIDR arrays\n\nCommit a5951389e58d (\"arm64: errata: Add newer ARM cores to the\nspectre_bhb_loop_affected() lists\") added some additional CPUs to the\nSpectre-BHB workaround, including some new arrays for designs that\nrequire new 'k' values for the workaround to be effective.\n\nUnfortunately, the new arrays omitted the sentinel entry and so\nis_midr_in_range_list() will walk off the end when it doesn't find a\nmatch. With UBSAN enabled, this leads to a crash during boot when\nis_midr_in_range_list() is inlined (which was more common prior to\nc8c2647e69be (\"arm64: Make  _midr_in_range_list() an exported\nfunction\")):\n\n |  Internal error: aarch64 BRK: 00000000f2000001 [#1] PREEMPT SMP\n |  pstate: 804000c5 (Nzcv daIF +PAN -UAO -TCO -DIT -SSBS BTYPE=--)\n |  pc : spectre_bhb_loop_affected+0x28/0x30\n |  lr : is_spectre_bhb_affected+0x170/0x190\n | [...]\n |  Call trace:\n |   spectre_bhb_loop_affected+0x28/0x30\n |   update_cpu_capabilities+0xc0/0x184\n |   init_cpu_features+0x188/0x1a4\n |   cpuinfo_store_boot_cpu+0x4c/0x60\n |   smp_prepare_boot_cpu+0x38/0x54\n |   start_kernel+0x8c/0x478\n |   __primary_switched+0xc8/0xd4\n |  Code: 6b09011f 54000061 52801080 d65f03c0 (d4200020)\n |  ---[ end trace 0000000000000000 ]---\n |  Kernel panic - not syncing: aarch64 BRK: Fatal exception\n\nAdd the missing sentinel entries."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64: errata: <PERSON><PERSON><PERSON> centinelas faltantes a las matrices MIDR de Spectre-BHB. El commit a5951389e58d (\"arm64: errata: Añadir nuevos núcleos ARM a las listas spectre_bhb_loop_affected()\") añadió CPU adicionales a la solución alternativa de Spectre-BHB, incluyendo nuevas matrices para diseños que requieren nuevos valores 'k' para que la solución sea efectiva. Desafortunadamente, las nuevas matrices omitieron la entrada de centinela, por lo que is_midr_in_range_list() se desviará al final si no encuentra una coincidencia. Con UBSAN habilitado, esto provoca un fallo durante el arranque cuando is_midr_in_range_list() está en línea (lo cual era más común antes de c8c2647e69be (\"arm64: Convertir _midr_in_range_list() en una función exportada\")): | Error interno: aarch64 BRK: 00000000f2000001 [#1] PREEMPT SMP | pstate: 804000c5 (Nzcv daIF +PAN -UAO -TCO -DIT -SSBS BTYPE=--) | pc : spectre_bhb_loop_affected+0x28/0x30 | lr : is_spectre_bhb_affected+0x170/0x190 | [...] | Rastreo de llamadas: | spectre_bhb_loop_affected+0x28/0x30 | update_cpu_capabilities+0xc0/0x184 | init_cpu_features+0x188/0x1a4 | cpuinfo_store_boot_cpu+0x4c/0x60 | smp_prepare_boot_cpu+0x38/0x54 | start_kernel+0x8c/0x478 | __primary_switched+0xc8/0xd4 | Código: 6b09011f 54000061 52801080 d65f03c0 (d4200020) | ---[ fin del seguimiento 000000000000000 ]--- | Pánico del kernel - no sincroniza: aarch64 BRK: Excepción fatal Agregue las entradas centinela faltantes."}], "references": [{"url": "https://git.kernel.org/stable/c/090c8714efe1c3c470301cc2f794c1ee2a57746c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/333579202f09e260e8116321df4c55f80a19b160", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3821cae9bd5a99a42d3d0be1b58e41f072cd4c4c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/446289b8b36b2ee98dabf6388acbddcc33ed41be", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6266b3509b2c6ebf2f9daf2239ff8eb60c5f5bd3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e68da90ac00d8b681561aeb8f5d6c47af3a04861", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fee4d171451c1ad9e8aaf65fc0ab7d143a33bd72", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
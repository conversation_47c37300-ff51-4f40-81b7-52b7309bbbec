{"cve_id": "CVE-2025-37915", "published_date": "2025-05-20T16:15:28.057", "last_modified_date": "2025-06-04T13:15:26.730", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet_sched: drr: Fix double list add in class with netem as child qdisc\n\nAs described in <PERSON><PERSON><PERSON><PERSON>'s report [1], there are use cases where a netem\nchild qdisc will make the parent qdisc's enqueue callback reentrant.\nIn the case of drr, there won't be a UAF, but the code will add the same\nclassifier to the list twice, which will cause memory corruption.\n\nIn addition to checking for qlen being zero, this patch checks whether the\nclass was already added to the active_list (cl_is_active) before adding\nto the list to cover for the reentrant case.\n\n[1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net_sched: drr: Se corrige la doble adición de lista en la clase con netem como qdisc hija. Como se describe en el informe de Gerrard [1], existen casos de uso en los que una qdisc hija de netem hará que la devolución de llamada de encolado de la qdisc madre sea reentrante. En el caso de drr, no habrá un UAF, pero el código agregará el mismo clasificador a la lista dos veces, lo que causará corrupción de memoria. Además de verificar que qlen sea cero, este parche verifica si la clase ya se agregó a active_list (cl_is_active) antes de agregarla a la lista para cubrir el caso reentrante. [1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/26e75716b94d6ff9be5ea07d63675c4d189f30b4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2968632880f1792007eedd12eeedf7f6e2b7e9f3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4b07ac06b0a712923255aaf2691637693fc7100d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4f0ecf50cdf76da95828578a92f130b653ac2fcf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5da3aad1a13e7edb8ff0778a444ccf49930313e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab2248110738d4429668140ad22f530a9ee730e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/db205b92dfe0501e5b92fb7cf00971d0e44ba3eb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f99a3fbf023e20b626be4b0f042463d598050c9a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
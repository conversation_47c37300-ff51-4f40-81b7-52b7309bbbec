{"cve_id": "CVE-2025-37944", "published_date": "2025-05-20T16:15:32.310", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: ath12k: Fix invalid entry fetch in ath12k_dp_mon_srng_process\n\nCurrently, ath12k_dp_mon_srng_process uses ath12k_hal_srng_src_get_next_entry\nto fetch the next entry from the destination ring. This is incorrect because\nath12k_hal_srng_src_get_next_entry is intended for source rings, not destination\nrings. This leads to invalid entry fetches, causing potential data corruption or\ncrashes due to accessing incorrect memory locations. This happens because the\nsource ring and destination ring have different handling mechanisms and using\nthe wrong function results in incorrect pointer arithmetic and ring management.\n\nTo fix this issue, replace the call to ath12k_hal_srng_src_get_next_entry with\nath12k_hal_srng_dst_get_next_entry in ath12k_dp_mon_srng_process. This ensures\nthat the correct function is used for fetching entries from the destination\nring, preventing invalid memory accesses.\n\nTested-on: QCN9274 hw2.0 PCI WLAN.WBE.1.3.1-00173-QCAHKSWPL_SILICONZ-1\nTested-on: WCN7850 hw2.0 WLAN.HMT.1.0.c5-00481-QCAHMTSWPL_V1.0_V2.0_SILICONZ-3"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: ath12k: Se corrige la obtención de entrada no válida en ath12k_dp_mon_srng_process Actualmente, ath12k_dp_mon_srng_process usa ath12k_hal_srng_src_get_next_entry para obtener la siguiente entrada del anillo de destino. Esto es incorrecto porque ath12k_hal_srng_src_get_next_entry está diseñado para anillos de origen, no de destino. Esto conduce a la obtención de entradas no válidas, lo que puede causar corrupción de datos o bloqueos debido al acceso a ubicaciones de memoria incorrectas. Esto sucede porque el anillo de origen y el anillo de destino tienen diferentes mecanismos de manejo y el uso de la función incorrecta da como resultado una aritmética de punteros y una gestión del anillo incorrectas. Para solucionar este problema, reemplace la llamada a ath12k_hal_srng_src_get_next_entry por ath12k_hal_srng_dst_get_next_entry en ath12k_dp_mon_srng_process. Esto garantiza que se utilice la función correcta para obtener entradas del anillo de destino, evitando accesos no válidos a la memoria. Probado en: QCN9274 hw2.0 PCI WLAN.WBE.1.3.1-00173-QCAHKSWPL_SILICONZ-1. Probado en: WCN7850 hw2.0 WLAN.HMT.1.0.c5-00481-QCAHMTSWPL_V1.0_V2.0_SILICONZ-3."}], "references": [{"url": "https://git.kernel.org/stable/c/298f0aea5cb32b5038f991f5db201a0fcbb9a31b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2c512f2eadabb1e80816116894ffaf7d802a944e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/63fdc4509bcf483e79548de6bc08bf3c8e504bb3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab7edf42ce800eb34d2f73dd7271b826661a06a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b6a3b2b2cead103089d3bb7a57d8209bdfa5399d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
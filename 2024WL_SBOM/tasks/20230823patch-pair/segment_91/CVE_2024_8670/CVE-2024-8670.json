{"cve_id": "CVE-2024-8670", "published_date": "2025-05-15T20:15:59.303", "last_modified_date": "2025-06-04T20:08:00.993", "descriptions": [{"lang": "en", "value": "The Photo Gallery by 10Web  WordPress plugin before 1.8.29 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Photo Gallery de 10Web para WordPress anterior a la versión 1.8.29 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/50665594-778b-42f5-bfba-2a249a5e0260/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37989", "published_date": "2025-05-20T18:15:45.773", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: phy: leds: fix memory leak\n\nA network restart test on a router led to an out-of-memory condition,\nwhich was traced to a memory leak in the PHY LED trigger code.\n\nThe root cause is misuse of the devm API. The registration function\n(phy_led_triggers_register) is called from phy_attach_direct, not\nphy_probe, and the unregister function (phy_led_triggers_unregister)\nis called from phy_detach, not phy_remove. This means the register and\nunregister functions can be called multiple times for the same PHY\ndevice, but devm-allocated memory is not freed until the driver is\nunbound.\n\nThis also prevents kmemleak from detecting the leak, as the devm API\ninternally stores the allocated pointer.\n\nFix this by replacing devm_kzalloc/devm_kcalloc with standard\nkzalloc/kcalloc, and add the corresponding kfree calls in the unregister\npath."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: phy: leds: fix memory leakage Una prueba de reinicio de red en un enrutador provocó una condición de falta de memoria, que se rastreó hasta una fuga de memoria en el código de activación del LED PHY. La causa raíz es el uso indebido de la API devm. La función de registro (phy_led_triggers_register) se llama desde phy_attach_direct, no desde phy_probe, y la función de anulación del registro (phy_led_triggers_unregister) se llama desde phy_detach, no desde phy_remove. Esto significa que las funciones de registro y anulación del registro se pueden llamar varias veces para el mismo dispositivo PHY, pero la memoria asignada por devm no se libera hasta que se desvincula el controlador. Esto también evita que kmemleak detecte la fuga, ya que la API devm almacena internamente el puntero asignado. Solucione esto reemplazando devm_kzalloc/devm_kcalloc con kzalloc/kcalloc estándar y agregue las llamadas kfree correspondientes en la ruta de anulación de registro."}], "references": [{"url": "https://git.kernel.org/stable/c/41143e71052a00d654c15dc924fda50c1e7357d0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/618541a6cc1511064dfa58c89b3445e21844092f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/663c3da86e807c6c07ed48f911c7526fad6fe1ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f3d5880800f962c347777c4f8358f29f5fc403c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/95bed65cc0eb2a610550abf849a8b94374da80a7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/966d6494e2ed9be9052fcd9815afba830896aaf8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b7f0ee992adf601aa00c252418266177eb7ac2bc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f41f097f68a33d392579885426d0734a81219501", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
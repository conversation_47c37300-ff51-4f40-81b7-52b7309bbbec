{"cve_id": "CVE-2025-31640", "published_date": "2025-05-16T16:15:37.550", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Magic Responsive Slider and Carousel WordPress allows SQL Injection. This issue affects Magic Responsive Slider and Carousel WordPress: from n/a through 1.4."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Magic Responsive Slider and Carousel WordPress permite la inyección SQL. Este problema afecta a Magic Responsive Slider y Carousel WordPress desde n/d hasta la versión 1.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/magic-carousel/vulnerability/wordpress-magic-responsive-slider-and-carousel-wordpress-1-4-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
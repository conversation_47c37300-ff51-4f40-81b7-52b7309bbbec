{"cve_id": "CVE-2025-32301", "published_date": "2025-05-16T16:15:39.500", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup CountDown Pro WP Plugin allows SQL Injection. This issue affects CountDown Pro WP Plugin: from n/a through 2.7."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup CountDown Pro WP Plugin permite la inyección SQL. Este problema afecta al plugin CountDown Pro WP desde n/d hasta la versión 2.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/circular_countdown/vulnerability/wordpress-countdown-pro-wp-plugin-2-7-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
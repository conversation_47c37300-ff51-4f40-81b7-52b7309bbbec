{"cve_id": "CVE-2025-37963", "published_date": "2025-05-20T16:15:34.580", "last_modified_date": "2025-06-27T11:15:25.160", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\narm64: bpf: Only mitigate cBPF programs loaded by unprivileged users\n\nSupport for eBPF programs loaded by unprivileged users is typically\ndisabled. This means only cBPF programs need to be mitigated for BHB.\n\nIn addition, only mitigate cBPF programs that were loaded by an\nunprivileged user. Privileged users can also load the same program\nvia eBPF, making the mitigation pointless."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: arm64: bpf: Mitigar solo programas cBPF cargados por usuarios sin privilegios. La compatibilidad con programas eBPF cargados por usuarios sin privilegios suele estar deshabilitada. Esto significa que solo es necesario mitigar los programas cBPF para BHB. Además, solo se deben mitigar los programas cBPF cargados por un usuario sin privilegios. Los usuarios con privilegios también pueden cargar el mismo programa mediante eBPF, lo que hace que la mitigación sea inútil."}], "references": [{"url": "https://git.kernel.org/stable/c/038866e01ea5e5a3d948898ac216e531e7848669", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/477481c4348268136227348984b6699d6370b685", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6e52d043f7dbf1839a24a3fab2b12b0d3839de7a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/80251f62028f1ab2e09be5ca3123f84e8b00389a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/df53d418709205450a02bb4d71cbfb4ff86f2c1e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e5f5100f1c64ac6c72671b2cf6b46542fce93706", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f300769ead032513a68e4a02e806393402e626f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
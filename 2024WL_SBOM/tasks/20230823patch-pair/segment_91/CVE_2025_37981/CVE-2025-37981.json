{"cve_id": "CVE-2025-37981", "published_date": "2025-05-20T17:15:48.763", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nscsi: smartpqi: Use is_kdump_kernel() to check for kdump\n\nThe smartpqi driver checks the reset_devices variable to determine\nwhether special adjustments need to be made for kdump. This has the\neffect that after a regular kexec reboot, some driver parameters such as\nmax_transfer_size are much lower than usual. More importantly, kexec\nreboot tests have revealed memory corruption caused by the driver log\nbeing written to system memory after a kexec.\n\nFix this by testing is_kdump_kernel() rather than reset_devices where\nappropriate."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: scsi: smartpqi: Usar is_kdump_kernel() para comprobar kdump. El controlador smartpqi comprueba la variable reset_devices para determinar si es necesario realizar ajustes especiales para kdump. Esto provoca que, tras un reinicio normal de kexec, algunos parámetros del controlador, como max_transfer_size, sean mucho más bajos de lo habitual. Es más, las pruebas de reinicio de kexec han revelado corrupción de memoria causada por la escritura del registro del controlador en la memoria del sistema después de un kexec. Para solucionar esto, pruebe is_kdump_kernel() en lugar de reset_devices cuando corresponda."}], "references": [{"url": "https://git.kernel.org/stable/c/7cc670e8ebaa5241dd99c0ad75eceb8f8f64f607", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a2d5a0072235a69749ceb04c1a26dc75df66a31a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ebf673c76ce91e612a882dfaa9a3824962994aae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
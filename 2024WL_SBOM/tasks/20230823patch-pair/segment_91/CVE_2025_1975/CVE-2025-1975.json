{"cve_id": "CVE-2025-1975", "published_date": "2025-05-16T09:15:17.980", "last_modified_date": "2025-06-24T16:40:44.220", "descriptions": [{"lang": "en", "value": "A vulnerability in the Ollama server version 0.5.11 allows a malicious user to cause a Denial of Service (DoS) attack by customizing the manifest content and spoofing a service. This is due to improper validation of array index access when downloading a model via the /api/pull endpoint, which can lead to a server crash."}, {"lang": "es", "value": "Una vulnerabilidad en la versión 0.5.11 del servidor Ollama permite a un usuario malintencionado provocar un ataque de denegación de servicio (DoS) personalizando el contenido del manifiesto y suplantando un servicio. Esto se debe a una validación incorrecta del acceso al índice de la matriz al descargar un modelo mediante el endpoint /api/pull, lo que puede provocar un fallo del servidor."}], "references": [{"url": "https://huntr.com/bounties/921ba5d4-f1d0-4c66-9764-4f72dffe7acd", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
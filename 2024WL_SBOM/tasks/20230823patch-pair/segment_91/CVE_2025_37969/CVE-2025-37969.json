{"cve_id": "CVE-2025-37969", "published_date": "2025-05-20T17:15:47.363", "last_modified_date": "2025-06-04T13:15:27.550", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niio: imu: st_lsm6dsx: fix possible lockup in st_lsm6dsx_read_tagged_fifo\n\nPrevent st_lsm6dsx_read_tagged_fifo from falling in an infinite loop in\ncase pattern_len is equal to zero and the device FIFO is not empty."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iio: imu: st_lsm6dsx: corrige un posible bloqueo en st_lsm6dsx_read_tagged_fifo Evita que st_lsm6dsx_read_tagged_fifo caiga en un bucle infinito en caso de que pattern_len sea igual a cero y el FIFO del dispositivo no esté vacío."}], "references": [{"url": "https://git.kernel.org/stable/c/16857370b3a30663515956b3bd27f3def6a2cf06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/35b8c0a284983b71d92d082c54b7eb655ed4194f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4db7d923a8c298788181b796f71adf6ca499f966", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/76727a1d81afde77d21ea8feaeb12d34605be6f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8114ef86e2058e2554111b793596f17bee23fa15", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ce662851380fe2018e36e15c0bdcb1ad177ed95", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ddb4cf2192c213e4dba1733bbcdc94cf6d85bf7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dadf9116108315f2eb14c7415c7805f392c476b4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
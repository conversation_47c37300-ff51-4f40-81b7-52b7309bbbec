{"cve_id": "CVE-2025-24189", "published_date": "2025-05-19T16:15:28.323", "last_modified_date": "2025-05-28T14:19:16.157", "descriptions": [{"lang": "en", "value": "The issue was addressed with improved checks. This issue is fixed in Safari 18.3, visionOS 2.3, iOS 18.3 and iPadOS 18.3, macOS Sequoia 15.3, watchOS 11.3, tvOS 18.3. Processing maliciously crafted web content may lead to memory corruption."}, {"lang": "es", "value": "El problema se solucionó mejorando las comprobaciones. Este problema está corregido en Safari 18.3, visionOS 2.3, iOS 18.3 y iPadOS 18.3, macOS Sequoia 15.3, watchOS 11.3 y tvOS 18.3. El procesamiento de contenido web malintencionado puede provocar daños en la memoria."}], "references": [{"url": "https://support.apple.com/en-us/122066", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://support.apple.com/en-us/122068", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://support.apple.com/en-us/122071", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://support.apple.com/en-us/122072", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://support.apple.com/en-us/122073", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}, {"url": "https://support.apple.com/en-us/122074", "source": "<EMAIL>", "tags": ["Release Notes", "Vendor Advisory"]}]}
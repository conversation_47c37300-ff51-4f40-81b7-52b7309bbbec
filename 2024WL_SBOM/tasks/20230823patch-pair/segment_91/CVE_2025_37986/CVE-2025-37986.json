{"cve_id": "CVE-2025-37986", "published_date": "2025-05-20T18:15:45.470", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: typec: class: Invalidate USB device pointers on partner unregistration\n\nTo avoid using invalid USB device pointers after a Type-C partner\ndisconnects, this patch clears the pointers upon partner unregistration.\nThis ensures a clean state for future connections."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: typec: class: Invalidación de punteros de dispositivos USB al cancelar el registro del socio. Para evitar el uso de punteros de dispositivos USB no válidos tras la desconexión de un socio de tipo C, este parche borra los punteros al cancelar el registro del socio. Esto garantiza un estado limpio para futuras conexiones."}], "references": [{"url": "https://git.kernel.org/stable/c/40966fc9939e85677fdb489dfddfa205baaad03b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/66e1a887273c6b89f09bc11a40d0a71d5a081a8e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/74911338f47c13d1b9470fc50718182bffad42e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
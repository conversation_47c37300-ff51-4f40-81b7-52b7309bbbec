{"cve_id": "CVE-2025-37908", "published_date": "2025-05-20T16:15:27.280", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmm, slab: clean up slab->obj_exts always\n\nWhen memory allocation profiling is disabled at runtime or due to an\nerror, shutdown_mem_profiling() is called: slab->obj_exts which\npreviously allocated remains.\nIt won't be cleared by unaccount_slab() because of\nmem_alloc_profiling_enabled() not true. It's incorrect, slab->obj_exts\nshould always be cleaned up in unaccount_slab() to avoid following error:\n\n[...]BUG: Bad page state in process...\n..\n[...]page dumped because: page still charged to cgroup\n\n[and<PERSON><PERSON>.she<PERSON><EMAIL>: fold need_slab_obj_ext() into its only user]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mm, slab: limpiar slab-&gt;obj_exts siempre. Cuando el perfilado de asignación de memoria se deshabilita en tiempo de ejecución o debido a un error, se llama a shutdown_mem_profiling(): slab-&gt;obj_exts, previamente asignado, permanece. No se borrará con unaccount_slab() debido a que mem_alloc_profiling_enabled() no es verdadero. Es incorrecto; slab-&gt;obj_exts siempre debe limpiarse en unaccount_slab() para evitar el siguiente error: [...] ERROR: Estado de página incorrecto en proceso... [...] página volcada porque: la página aún se carga al cgroup [<EMAIL>: integrar need_slab_obj_ext() en su único usuario]"}], "references": [{"url": "https://git.kernel.org/stable/c/01db0e1a48345aa1937f3bdfc7c7108d03ebcf7e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be8250786ca94952a19ce87f98ad9906448bc9ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dab2a13059a475b6392550f882276e170fe2fcff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-8187", "published_date": "2025-05-15T20:15:58.130", "last_modified_date": "2025-05-27T19:53:19.030", "descriptions": [{"lang": "en", "value": "The Smart Post Show  WordPress plugin before 3.0.1 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Smart Post Show de WordPress anterior a la versión 3.0.1 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/0e51b3b5-f003-4af9-8538-95f266065e36/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
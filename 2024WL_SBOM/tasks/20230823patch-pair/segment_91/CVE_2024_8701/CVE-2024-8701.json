{"cve_id": "CVE-2024-8701", "published_date": "2025-05-15T20:15:59.630", "last_modified_date": "2025-06-12T15:50:22.660", "descriptions": [{"lang": "en", "value": "The events-calendar WordPress plugin through 1.0.4 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento events-calendar de WordPress hasta la versión 1.0.4 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/707d4b5b-8efe-4010-ba7d-80538545a2d5/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
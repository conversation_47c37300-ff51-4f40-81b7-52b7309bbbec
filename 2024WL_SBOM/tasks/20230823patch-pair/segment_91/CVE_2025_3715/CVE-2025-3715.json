{"cve_id": "CVE-2025-3715", "published_date": "2025-05-18T06:15:17.717", "last_modified_date": "2025-05-19T13:35:20.460", "descriptions": [{"lang": "en", "value": "The Bold Page Builder plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the data-text parameter in all versions up to, and including, 5.3.5 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Bold Page Builder para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del parámetro data-text en todas las versiones hasta la 5.3.5 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/bold-page-builder/tags/5.3.5/content_elements_misc/js/content_elements.js", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3292512/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a2452dd7-2bb9-4a0c-81db-6699a9b049ae?source=cve", "source": "<EMAIL>", "tags": []}]}
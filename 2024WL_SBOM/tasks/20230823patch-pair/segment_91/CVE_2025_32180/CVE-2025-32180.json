{"cve_id": "CVE-2025-32180", "published_date": "2025-05-16T16:15:38.603", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Missing Authorization vulnerability in QuanticaLabs CSS3 Tooltips for WordPress allows Exploiting Incorrectly Configured Access Control Security Levels. This issue affects CSS3 Tooltips for WordPress: from n/a through 1.8."}, {"lang": "es", "value": "La vulnerabilidad de falta de autorización en QuanticaLabs CSS3 Tooltips for WordPress permite explotar niveles de seguridad de control de acceso configurados incorrectamente. Este problema afecta a las descripciones emergentes CSS3 para WordPress desde la versión n/d hasta la 1.8."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/css3_tooltips/vulnerability/wordpress-css3-tooltips-for-wordpress-1-8-broken-access-control-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
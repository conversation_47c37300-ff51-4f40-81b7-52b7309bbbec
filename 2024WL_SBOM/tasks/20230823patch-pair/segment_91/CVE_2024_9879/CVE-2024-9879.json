{"cve_id": "CVE-2024-9879", "published_date": "2025-05-15T20:16:01.533", "last_modified_date": "2025-06-12T16:34:46.577", "descriptions": [{"lang": "en", "value": "The Melapress File Monitor WordPress plugin before 2.1.1 does not sanitize and escape a parameter before using it in a SQL statement, allowing admins to perform SQL injection attacks"}, {"lang": "es", "value": "El complemento Melapress File Monitor de WordPress anterior a la versión 2.1.1 no depura ni escapa un parámetro antes de usarlo en una declaración SQL, lo que permite a los administradores realizar ataques de inyección SQL."}], "references": [{"url": "https://wpscan.com/vulnerability/cda54097-4aec-472e-a73f-31ecb76ebb23/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
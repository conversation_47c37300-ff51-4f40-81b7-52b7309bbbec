{"cve_id": "CVE-2024-9233", "published_date": "2025-05-15T20:16:00.307", "last_modified_date": "2025-06-04T20:07:13.377", "descriptions": [{"lang": "en", "value": "The Logo Slider  WordPress plugin before 3.7.1 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin change them via a CSRF attack"}, {"lang": "es", "value": "El complemento Logo Slider de WordPress anterior a la versión 3.7.1 no tiene la comprobación CSRF activada al actualizar su configuración, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión la cambie mediante un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/a466cea4-0ae5-44a1-9e12-bd5dbecde2f2/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
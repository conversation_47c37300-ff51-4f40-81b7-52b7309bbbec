{"cve_id": "CVE-2025-37953", "published_date": "2025-05-20T16:15:33.483", "last_modified_date": "2025-06-04T13:15:27.437", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsch_htb: make htb_deactivate() idempotent\n\n<PERSON> reported a NULL pointer dereference in htb_next_rb_node()\nafter we made htb_qlen_notify() idempotent.\n\nIt turns out in the following case it introduced some regression:\n\nhtb_dequeue_tree():\n  |-> fq_codel_dequeue()\n    |-> qdisc_tree_reduce_backlog()\n      |-> htb_qlen_notify()\n        |-> htb_deactivate()\n  |-> htb_next_rb_node()\n  |-> htb_deactivate()\n\nFor htb_next_rb_node(), after calling the 1st htb_deactivate(), the\nclprio[prio]->ptr could be already set to  NULL, which means\nhtb_next_rb_node() is vulnerable here.\n\nFor htb_deactivate(), although we checked qlen before calling it, in\ncase of qlen==0 after qdisc_tree_reduce_backlog(), we may call it again\nwhich triggers the warning inside.\n\nTo fix the issues here, we need to:\n\n1) Make htb_deactivate() idempotent, that is, simply return if we\n   already call it before.\n2) Make htb_next_rb_node() safe against ptr==NULL.\n\nMany thanks to <PERSON> for testing and for the reproducer."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: sch_htb: hacer que htb_deactivate() sea idempotente. Alan informó una desreferencia de puntero NULL en htb_next_rb_node() después de que htb_qlen_notify() fuera idempotente. Resulta que en el siguiente caso introdujo alguna regresión: htb_dequeue_tree(): |-&gt; fq_codel_dequeue() |-&gt; qdisc_tree_reduce_backlog() |-&gt; htb_qlen_notify() |-&gt; htb_deactivate() |-&gt; htb_next_rb_node() |-&gt; htb_deactivate() Para htb_next_rb_node(), después de llamar al primer htb_deactivate(), el clprio[prio]-&gt;ptr podría estar ya establecido en NULL, lo que significa que htb_next_rb_node() es vulnerable aquí. Para htb_deactivate(), aunque verificamos qlen antes de llamarlo, en caso de qlen==0 después de qdisc_tree_reduce_backlog(), podemos llamarlo nuevamente, lo que activa la advertencia interna. Para solucionar estos problemas, necesitamos: 1) Hacer que htb_deactivate() sea idempotente, es decir, que simplemente regrese si ya lo llamamos. 2) Hacer que htb_next_rb_node() sea seguro contra ptr==NULL. Muchas gracias a Alan por las pruebas y por el reproductor."}], "references": [{"url": "https://git.kernel.org/stable/c/31ff70ad39485698cf779f2078132d80b57f6c07", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3769478610135e82b262640252d90f6efb05be71", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/98cd7ed92753090a714f0802d4434314526fe61d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/99ff8a20fd61315bf9ae627440a5ff07d22ee153", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c4792b9e38d2f61b07eac72f10909fa76130314b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c928dd4f6bf0c25c72b11824a1e9ac9bd37296a0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
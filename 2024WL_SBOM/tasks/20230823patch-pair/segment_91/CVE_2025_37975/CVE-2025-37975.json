{"cve_id": "CVE-2025-37975", "published_date": "2025-05-20T17:15:48.093", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nriscv: module: Fix out-of-bounds relocation access\n\nThe current code allows rel[j] to access one element past the end of the\nrelocation section. Simplify to num_relocations which is equivalent to\nthe existing size expression."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: riscv: módulo: Corrección de acceso de reubicación fuera de los límites. El código actual permite que rel[j] acceda a un elemento después del final de la sección de reubicación. Simplificar a num_relocations, que equivale a la expresión de tamaño existente."}], "references": [{"url": "https://git.kernel.org/stable/c/0b4cce68efb93e31a8e51795d696df6e379cb41c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/95e4e1c1cf614d125f159db9726b7abb32e18385", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a068ea00009d373d825c528f9c168501519211b4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-2203", "published_date": "2025-05-15T20:16:05.900", "last_modified_date": "2025-06-12T16:39:08.403", "descriptions": [{"lang": "en", "value": "The FunnelKit  WordPress plugin before 3.10.2 does not sanitize and escape a parameter before using it in a SQL statement, allowing admins to perform SQL injection attacks"}, {"lang": "es", "value": "El complemento FunnelKit para WordPress anterior a la versión 3.10.2 no depura ni escapa un parámetro antes de usarlo en una declaración SQL, lo que permite a los administradores realizar ataques de inyección SQL."}], "references": [{"url": "https://wpscan.com/vulnerability/d553cff4-074a-44e7-aebe-e61c86ab8042/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37982", "published_date": "2025-05-20T17:15:48.873", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: wl1251: fix memory leak in wl1251_tx_work\n\nThe skb dequeued from tx_queue is lost when wl1251_ps_elp_wakeup fails\nwith a -ETIMEDOUT error. Fix that by queueing the skb back to tx_queue."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: wl1251: se corrige una fuga de memoria en wl1251_tx_work. El skb desencolado de tx_queue se pierde cuando wl1251_ps_elp_wakeup falla con un error -ETIMEDOUT. Para solucionarlo, vuelva a encolar el skb en tx_queue."}], "references": [{"url": "https://git.kernel.org/stable/c/13c9744c1bcdb5de4e7dc1a78784788ecec52add", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2996144be660d930d5e394652abe08fe89dbe00e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4a43fd36710669d67dbb5c16287a58412582ca26", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/52f224009ce1e44805e6ff3ffc2a06af9c1c3c5b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5a90c29d0204c5ffc45b43b4eced6eef0e19a33a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8fd4b9551af214d037fbc9d8e179840b8b917841", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a0f0dc96de03ffeefc2a177b7f8acde565cb77f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f08448a885403722c5c77dae51964badfcb69495", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37956", "published_date": "2025-05-20T16:15:33.813", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: prevent rename with empty string\n\nClient can send empty newname string to ksmbd server.\nIt will cause a kernel oops from d_alloc.\nThis patch return the error when attempting to rename\na file or directory with an empty new name string."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: impide renombrar con una cadena vacía. El cliente puede enviar una cadena vacía de nombre nuevo al servidor ksmbd. Esto provocará un error de kernel desde d_alloc. Este parche devuelve el error al intentar renombrar un archivo o directorio con una cadena vacía de nombre nuevo."}], "references": [{"url": "https://git.kernel.org/stable/c/53e3e5babc0963a92d856a5ec0ce92c59f54bc12", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ee551672c8cf36108b0cfba92ec0c7c28ac3439", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c57301e332cc413fe0a7294a90725f4e21e9549d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d7f2c00acb1ef64304fd40ac507e9213ff1d9b5c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
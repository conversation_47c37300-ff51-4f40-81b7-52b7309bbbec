{"cve_id": "CVE-2025-26864", "published_date": "2025-05-14T11:16:28.437", "last_modified_date": "2025-07-01T19:23:28.797", "descriptions": [{"lang": "en", "value": "Exposure of Sensitive Information to an Unauthorized Actor, Insertion of Sensitive Information into Log File vulnerability in the OpenIdAuthorizer of Apache IoTDB.\n\nThis issue affects Apache IoTDB: from 0.10.0 through 1.3.3, from 2.0.1-beta before 2.0.2.\n\nUsers are recommended to upgrade to version 1.3.4 and 2.0.2, which fix the issue."}, {"lang": "es", "value": "Vulnerabilidad de exposición de información confidencial a un agente no autorizado e inserción de información confidencial en archivos de registro en OpenIdAuthorizer de Apache IoTDB. Este problema afecta a Apache IoTDB desde la versión 0.10.0 hasta la 1.3.3, y desde la versión 2.0.1-beta hasta la 2.0.2. Se recomienda actualizar a las versiones 1.3.4 y 2.0.2, que solucionan el problema."}], "references": [{"url": "https://lists.apache.org/thread/2kcjnlypppk8qjh17dpz0jvkcpn6l162", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/05/14/4", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}]}
{"cve_id": "CVE-2025-37892", "published_date": "2025-05-20T11:15:48.233", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmtd: inftlcore: Add error check for inftl_read_oob()\n\nIn INFTL_findwriteunit(), the return value of inftl_read_oob()\nneed to be checked. A proper implementation can be\nfound in INFTL_deleteblock(). The status will be set as\nSECTOR_IGNORE to break from the while-loop correctly\nif the inftl_read_oob() fails."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: mtd: inftlcore: Se ha añadido una comprobación de errores para inftl_read_oob(). En INFTL_findwriteunit(), es necesario comprobar el valor de retorno de inftl_read_oob(). Se puede encontrar una implementación adecuada en INFTL_deleteblock(). El estado se establecerá como SECTOR_IGNORE para interrumpir correctamente el bucle while si inftl_read_oob() falla."}], "references": [{"url": "https://git.kernel.org/stable/c/0300e751170cf80c05ca1a762a7b449e8ca6b693", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/114d94f095aa405fa9a51484c4be34846d7bb386", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1c22356dfb041e5292835c9ff44d5f91bef8dd18", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5479a6af3c96f73bec2d2819532b6d6814f52dd6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6af3b92b1c0b58ca281d0e1501bad2567f73c1a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7772621041ee78823ccc5f1fe38f6faa22af7023", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b828d394308e8e00df0a6f57e7dabae609bb8b7b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d027951dc85cb2e15924c980dc22a6754d100c7c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e7d6ceff95c55297f0ee8f9dbc4da5c558f30e9e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
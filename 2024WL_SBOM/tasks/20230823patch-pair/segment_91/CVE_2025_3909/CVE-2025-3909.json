{"cve_id": "CVE-2025-3909", "published_date": "2025-05-14T17:15:48.660", "last_modified_date": "2025-06-05T14:26:42.720", "descriptions": [{"lang": "en", "value": "Thunderbird's handling of the X-Mozilla-External-Attachment-URL header can be exploited to execute JavaScript in the file:/// context. By crafting a nested email attachment (message/rfc822) and setting its content type to application/pdf, Thunderbird may incorrectly render it as HTML when opened, allowing the embedded JavaScript to run without requiring a file download. This behavior relies on Thunderbird auto-saving the attachment to /tmp and linking to it via the file:/// protocol, potentially enabling JavaScript execution as part of the HTML. This vulnerability affects Thunderbird < 128.10.1 and Thunderbird < 138.0.1."}, {"lang": "es", "value": "La gestión que Thunderbird hace del encabezado X-Mozilla-External-Attachment-URL puede explotarse para ejecutar JavaScript en el contexto file:///. Al manipular un archivo adjunto de correo electrónico anidado (message/rfc822) y configurar su tipo de contenido como application/pdf, Thunderbird puede representarlo incorrectamente como HTML al abrirlo, lo que permite que el JavaScript incrustado se ejecute sin necesidad de descargar el archivo. Este comportamiento se basa en que Thunderbird guarda automáticamente el archivo adjunto en /tmp y lo enlaza mediante el protocolo file:///, lo que podría permitir la ejecución de JavaScript como parte del HTML. Esta vulnerabilidad afecta a Thunderbird versiones anteriores a la 128.10.1 y a la 138.0.1."}], "references": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1958376", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-34/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-35/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
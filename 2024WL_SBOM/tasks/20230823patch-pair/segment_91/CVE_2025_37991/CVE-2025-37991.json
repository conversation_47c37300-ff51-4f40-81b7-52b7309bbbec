{"cve_id": "CVE-2025-37991", "published_date": "2025-05-20T18:15:45.997", "last_modified_date": "2025-06-04T13:15:28.053", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nparisc: Fix double SIGFPE crash\n\n<PERSON><PERSON> noticed that on parisc a SIGFPE exception will crash an application with\na second SIGFPE in the signal handler.  <PERSON> analyzed it, and it happens\nbecause glibc uses a double-word floating-point store to atomically update\nfunction descriptors. As a result of lazy binding, we hit a floating-point\nstore in fpe_func almost immediately.\n\nWhen the T bit is set, an assist exception trap occurs when when the\nco-processor encounters *any* floating-point instruction except for a double\nstore of register %fr0.  The latter cancels all pending traps.  Let's fix this\nby clearing the Trap (T) bit in the FP status register before returning to the\nsignal handler in userspace.\n\nThe issue can be reproduced with this test program:\n\nroot@parisc:~# cat fpe.c\n\nstatic void fpe_func(int sig, siginfo_t *i, void *v) {\n        sigset_t set;\n        sigemptyset(&set);\n        sigaddset(&set, SIGFPE);\n        sigprocmask(SIG_UNBLOCK, &set, NULL);\n        printf(\"GOT signal %d with si_code %ld\\n\", sig, i->si_code);\n}\n\nint main() {\n        struct sigaction action = {\n                .sa_sigaction = fpe_func,\n                .sa_flags = SA_RESTART|SA_SIGINFO };\n        sigaction(SIGFPE, &action, 0);\n        feenableexcept(FE_OVERFLOW);\n        return printf(\"%lf\\n\",1.7976931348623158E308*1.7976931348623158E308);\n}\n\nroot@parisc:~# gcc fpe.c -lm\nroot@parisc:~# ./a.out\n Floating point exception\n\nroot@parisc:~# strace -f ./a.out\n execve(\"./a.out\", [\"./a.out\"], 0xf9ac7034 /* 20 vars */) = 0\n getrlimit(RLIMIT_STACK, {rlim_cur=8192*1024, rlim_max=RLIM_INFINITY}) = 0\n ...\n rt_sigaction(SIGFPE, {sa_handler=0x1110a, sa_mask=[], sa_flags=SA_RESTART|SA_SIGINFO}, NULL, 8) = 0\n --- SIGFPE {si_signo=SIGFPE, si_code=FPE_FLTOVF, si_addr=0x1078f} ---\n --- SIGFPE {si_signo=SIGFPE, si_code=FPE_FLTOVF, si_addr=0xf8f21237} ---\n +++ killed by SIGFPE +++\n Floating point exception"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: parisc: Se corrige el doble fallo de SIGFPE Camm notó que en parisc una excepción SIGFPE bloqueará una aplicación con un segundo SIGFPE en el manejador de señales. Dave lo analizó y sucede porque glibc usa un almacén de punto flotante de doble palabra para actualizar atómicamente los descriptores de función. Como resultado del enlace diferido, llegamos a un almacén de punto flotante en fpe_func casi inmediatamente. Cuando se establece el bit T, se produce una trampa de excepción de asistencia cuando el coprocesador encuentra *cualquier* instrucción de punto flotante excepto un almacén doble del registro %fr0. Este último cancela todas las trampas pendientes. Arreglemos esto borrando el bit Trap (T) en el registro de estado FP antes de regresar al manejador de señales en el espacio de usuario. El problema se puede reproducir con este programa de prueba: root@parisc:~# cat fpe.c static void fpe_func(int sig, siginfo_t *i, void *v) { sigset_t set; sigemptyset(&amp;set); sigaddset(&amp;set, SIGFPE); sigprocmask(SIG_UNBLOCK, &amp;set, NULL); printf(\"Señal GOT %d con código si %ld\\n\", sig, i-&gt;código si); } int main() { struct sigaction action = { .sa_sigaction = fpe_func, .sa_flags = SA_RESTART|SA_SIGINFO }; sigaction(SIGFPE, &amp;action, 0); feenableexcept(FE_OVERFLOW); return printf(\"%lf\\n\",1.7976931348623158E308*1.7976931348623158E308); } root@parisc:~# gcc fpe.c -lm root@parisc:~# ./a.out Excepción de punto flotante root@parisc:~# strace -f ./a.out execve(\"./a.out\", [\"./a.out\"], 0xf9ac7034 /* 20 variables */) = 0 getrlimit(RLIMIT_STACK, {rlim_cur=8192*1024, rlim_max=RLIM_INFINITY}) = 0 ... rt_sigaction(SIGFPE, {sa_handler=0x1110a, sa_mask=[], sa_flags=SA_RESTART|SA_SIGINFO}, NULL, 8) = 0 --- SIGFPE {si_signo=SIGFPE, si_code=FPE_FLTOVF, si_addr=0x1078f} --- --- SIGFPE {si_signo=SIGFPE, si_code=FPE_FLTOVF, si_addr=0xf8f21237} --- +++ eliminado por SIGFPE +++ Excepción de punto flotante"}], "references": [{"url": "https://git.kernel.org/stable/c/2a1aff3616b3b57aa4a5f8a7762cce1e82493fe6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6a098c51d18ec99485668da44294565c43dbc106", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c639af49e9e5615a8395981eaf5943fb40acd6f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/757ba4d17b868482837c566cfefca59e2296c608", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cf21e890f56b7d0038ddaf25224e4f4c69ecd143", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de3629baf5a33af1919dec7136d643b0662e85ef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/df3592e493d7f29bae4ffde9a9325de50ddf962e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ec4584495868bd465fe60a3f771915c0e7ce7951", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
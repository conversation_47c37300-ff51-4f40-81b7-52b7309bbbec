{"cve_id": "CVE-2025-2929", "published_date": "2025-05-20T06:15:38.737", "last_modified_date": "2025-06-12T16:23:16.733", "descriptions": [{"lang": "en", "value": "The Order Delivery Date WordPress plugin before 12.4.0 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin"}, {"lang": "es", "value": "El complemento Order Delivery Date de WordPress anterior a la versión 12.4.0 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios altos, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/e9299d8f-900b-4487-b135-b82946825e61/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-8397", "published_date": "2025-05-15T20:15:58.500", "last_modified_date": "2025-06-12T15:36:39.860", "descriptions": [{"lang": "en", "value": "The webtoffee-gdpr-cookie-consent WordPress plugin before 2.6.1 does not properly sanitize and escape the IP headers when logging them, allowing visitors to conduct Stored Cross-Site Scripting attacks. The payload gets triggered when an admin visits the 'Consent report' page and the malicious script is executed in the admin context."}, {"lang": "es", "value": "El complemento webtoffee-gdpr-cookie-consent de WordPress, anterior a la versión 2.6.1, no depura ni escapa correctamente los encabezados IP al registrarlos, lo que permite a los visitantes realizar ataques de Cross-Site Scripting. El payload se activa cuando un administrador visita la página \"Consent report\" y el script malicioso se ejecuta en el contexto del administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/847fbf5d-f7cf-49fd-88bc-d7fa2a8110bd/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37933", "published_date": "2025-05-20T16:15:29.933", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocteon_ep: Fix host hang issue during device reboot\n\nWhen the host loses heartbeat messages from the device,\nthe driver calls the device-specific ndo_stop function,\nwhich frees the resources. If the driver is unloaded in\nthis scenario, it calls ndo_stop again, attempting to free\nresources that have already been freed, leading to a host\nhang issue. To resolve this, dev_close should be called\ninstead of the device-specific stop function.dev_close\ninternally calls ndo_stop to stop the network interface\nand performs additional cleanup tasks. During the driver\nunload process, if the device is already down, ndo_stop\nis not called."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: octeon_ep: Se solucionó el problema de bloqueo del host durante el reinicio del dispositivo. Cuando el host pierde los mensajes de latido del dispositivo, el controlador llama a la función ndo_stop específica del dispositivo, que libera los recursos. Si el controlador se descarga en este caso, vuelve a llamar a ndo_stop para intentar liberar los recursos ya liberados, lo que provoca un problema de bloqueo del host. Para solucionar esto, se debe llamar a dev_close en lugar de a la función de detención específica del dispositivo. Dev_close llama internamente a ndo_stop para detener la interfaz de red y realiza tareas de limpieza adicionales. Durante el proceso de descarga del controlador, si el dispositivo ya está inactivo, no se llama a ndo_stop."}], "references": [{"url": "https://git.kernel.org/stable/c/34f42736b325287a7b2ce37e415838f539767bda", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6d1052423518e7d0aece9af5e77bbc324face8f1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7e1ca1bed3f66e00377f7d2147be390144924276", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c8d788f800f83b94d9db8b3dacc1d26be38a6ef4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
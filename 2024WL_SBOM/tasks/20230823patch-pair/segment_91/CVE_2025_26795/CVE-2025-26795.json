{"cve_id": "CVE-2025-26795", "published_date": "2025-05-14T11:16:26.487", "last_modified_date": "2025-07-11T16:16:19.057", "descriptions": [{"lang": "en", "value": "Exposure of Sensitive Information to an Unauthorized Actor, Insertion of Sensitive Information into Log File vulnerability in Apache IoTDB JDBC driver.\n\nThis issue affects iotdb-jdbc: from 0.10.0 through 1.3.3, from 2.0.1-beta before 2.0.2.\n\nUsers are recommended to upgrade to version 2.0.2 and 1.3.4, which fix the issue."}, {"lang": "es", "value": "Vulnerabilidad de exposición de información confidencial a un agente no autorizado e inserción de información confidencial en archivos de registro en el controlador JDBC de Apache IoTDB. Este problema afecta a iotdb-jdbc: desde la versión 0.10.0 a la 1.3.3, y de la versión 2.0.1-beta a la 2.0.2. Se recomienda actualizar a las versiones 2.0.2 y 1.3.4, que solucionan el problema."}], "references": [{"url": "https://lists.apache.org/thread/bj0ytxr5wg0c4jw8xm7rhfd8ogho0r91", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/05/14/3", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}]}
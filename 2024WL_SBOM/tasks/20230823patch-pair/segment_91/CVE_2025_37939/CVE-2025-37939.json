{"cve_id": "CVE-2025-37939", "published_date": "2025-05-20T16:15:31.467", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nlibbpf: Fix accessing BTF.ext core_relo header\n\nUpdate btf_ext_parse_info() to ensure the core_relo header is present\nbefore reading its fields. This avoids a potential buffer read overflow\nreported by the OSS Fuzz project."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: libbpf: Se corrige el acceso al encabezado core_relo de BTF.ext. Se actualiza btf_ext_parse_info() para garantizar que el encabezado core_relo esté presente antes de leer sus campos. Esto evita un posible desbordamiento de lectura del búfer reportado por el proyecto OSS Fuzz."}], "references": [{"url": "https://git.kernel.org/stable/c/0a7c2a84359612e54328aa52030eb202093da6e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3a67f60f0a8be10cea7a884a1a00e9feb6645657", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d529411ec44535308c5d59cbeff74be6fe14b479", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
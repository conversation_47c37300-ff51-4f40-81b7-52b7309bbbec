{"cve_id": "CVE-2025-37940", "published_date": "2025-05-20T16:15:31.737", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nftrace: Add cond_resched() to ftrace_graph_set_hash()\n\nWhen the kernel contains a large number of functions that can be traced,\nthe loop in ftrace_graph_set_hash() may take a lot of time to execute.\nThis may trigger the softlockup watchdog.\n\nAdd cond_resched() within the loop to allow the kernel to remain\nresponsive even when processing a large number of functions.\n\nThis matches the cond_resched() that is used in other locations of the\ncode that iterates over all functions that can be traced."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ftrace: Añadir cond_resched() a ftrace_graph_set_hash(). Cuando el kernel contiene una gran cantidad de funciones rastreables, el bucle en ftrace_graph_set_hash() puede tardar mucho en ejecutarse. Esto puede activar el mecanismo de vigilancia de bloqueo suave. Añadir cond_resched() dentro del bucle para que el kernel siga respondiendo incluso al procesar una gran cantidad de funciones. Esto coincide con el cond_resched() utilizado en otras partes del código que itera sobre todas las funciones rastreables."}], "references": [{"url": "https://git.kernel.org/stable/c/1fce9574b9d515bcb8a75379a8053e18602424e3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42ea22e754ba4f2b86f8760ca27f6f71da2d982c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4429535acab750d963fdc3dfcc9e0eee42f4d599", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5d336ac215e5c76e43ef4bca9ba699835e53e2fd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/618655d54c5f8af5d57b77491d08c0f0ff77d114", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/72be43ff061a889c6ee648a330a42486cafa15a6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8dd7d7280357596ba63dfdb4c1725d9dd24bd42a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dd38803c9088b848c6b56f4f6d7efc4497bfde61", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e5b4ae6f01d4a510d5725eca7254519a1093920d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
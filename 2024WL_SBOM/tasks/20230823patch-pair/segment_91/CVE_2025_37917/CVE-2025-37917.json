{"cve_id": "CVE-2025-37917", "published_date": "2025-05-20T16:15:28.273", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet: ethernet: mtk-star-emac: fix spinlock recursion issues on rx/tx poll\n\nUse spin_lock_irqsave and spin_unlock_irqrestore instead of spin_lock\nand spin_unlock in mtk_star_emac driver to avoid spinlock recursion\noccurrence that can happen when enabling the DMA interrupts again in\nrx/tx poll.\n\n```\nBUG: spinlock recursion on CPU#0, swapper/0/0\n lock: 0xffff00000db9cf20, .magic: dead4ead, .owner: swapper/0/0,\n    .owner_cpu: 0\nCPU: 0 UID: 0 PID: 0 Comm: swapper/0 Not tainted\n    6.15.0-rc2-next-20250417-00001-gf6a27738686c-dirty #28 PREEMPT\nHardware name: MediaTek MT8365 Open Platform EVK (DT)\nCall trace:\n show_stack+0x18/0x24 (C)\n dump_stack_lvl+0x60/0x80\n dump_stack+0x18/0x24\n spin_dump+0x78/0x88\n do_raw_spin_lock+0x11c/0x120\n _raw_spin_lock+0x20/0x2c\n mtk_star_handle_irq+0xc0/0x22c [mtk_star_emac]\n __handle_irq_event_percpu+0x48/0x140\n handle_irq_event+0x4c/0xb0\n handle_fasteoi_irq+0xa0/0x1bc\n handle_irq_desc+0x34/0x58\n generic_handle_domain_irq+0x1c/0x28\n gic_handle_irq+0x4c/0x120\n do_interrupt_handler+0x50/0x84\n el1_interrupt+0x34/0x68\n el1h_64_irq_handler+0x18/0x24\n el1h_64_irq+0x6c/0x70\n regmap_mmio_read32le+0xc/0x20 (P)\n _regmap_bus_reg_read+0x6c/0xac\n _regmap_read+0x60/0xdc\n regmap_read+0x4c/0x80\n mtk_star_rx_poll+0x2f4/0x39c [mtk_star_emac]\n __napi_poll+0x38/0x188\n net_rx_action+0x164/0x2c0\n handle_softirqs+0x100/0x244\n __do_softirq+0x14/0x20\n ____do_softirq+0x10/0x20\n call_on_irq_stack+0x24/0x64\n do_softirq_own_stack+0x1c/0x40\n __irq_exit_rcu+0xd4/0x10c\n irq_exit_rcu+0x10/0x1c\n el1_interrupt+0x38/0x68\n el1h_64_irq_handler+0x18/0x24\n el1h_64_irq+0x6c/0x70\n cpuidle_enter_state+0xac/0x320 (P)\n cpuidle_enter+0x38/0x50\n do_idle+0x1e4/0x260\n cpu_startup_entry+0x34/0x3c\n rest_init+0xdc/0xe0\n console_on_rootfs+0x0/0x6c\n __primary_switched+0x88/0x90\n```"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net: ethernet: mtk-star-emac: corrige problemas de recursión de spinlock en el sondeo rx/tx Utilice spin_lock_irqsave y spin_unlock_irqrestore en lugar de spin_lock y spin_unlock en el controlador mtk_star_emac para evitar que se produzca una recursión de spinlock que puede suceder cuando se habilitan nuevamente las interrupciones DMA en el sondeo rx/tx. ``` ERROR: recursión de bloqueo de giro en CPU#0, bloqueo swapper/0/0: 0xffff00000db9cf20, .magic: dead4ead, .owner: swapper/0/0, .owner_cpu: 0 CPU: 0 UID: 0 PID: 0 Comm: swapper/0 No contaminado 6.15.0-rc2-next-20250417-00001-gf6a27738686c-dirty #28 PREEMPT Nombre del hardware: MediaTek MT8365 Open Platform EVK (DT) Rastreo de llamadas: show_stack+0x18/0x24 (C) dump_stack_lvl+0x60/0x80 dump_stack+0x18/0x24 spin_dump+0x78/0x88 do_raw_spin_lock+0x11c/0x120 _raw_spin_lock+0x20/0x2c mtk_star_handle_irq+0xc0/0x22c [mtk_star_emac] __handle_irq_event_percpu+0x48/0x140 handle_irq_event+0x4c/0xb0 handle_fasteoi_irq+0xa0/0x1bc handle_irq_desc+0x34/0x58 generic_handle_domain_irq+0x1c/0x28 gic_handle_irq+0x4c/0x120 do_interrupt_handler+0x50/0x84 el1_interrupt+0x34/0x68 el1h_64_irq_handler+0x18/0x24 el1h_64_irq+0x6c/0x70 regmap_mmio_read32le+0xc/0x20 (P) _regmap_bus_reg_read+0x6c/0xac _regmap_read+0x60/0xdc regmap_read+0x4c/0x80 mtk_star_rx_poll+0x2f4/0x39c [mtk_star_emac] __napi_poll+0x38/0x188 net_rx_action+0x164/0x2c0 handle_softirqs+0x100/0x244 __do_softirq+0x14/0x20 ____do_softirq+0x10/0x20 call_on_irq_stack+0x24/0x64 do_softirq_own_stack+0x1c/0x40 __irq_exit_rcu+0xd4/0x10c irq_exit_rcu+0x10/0x1c el1_interrupt+0x38/0x68 el1h_64_irq_handler+0x18/0x24 el1h_64_irq+0x6c/0x70 cpuidle_enter_state+0xac/0x320 (P) cpuidle_enter+0x38/0x50 do_idle+0x1e4/0x260 cpu_startup_entry+0x34/0x3c rest_init+0xdc/0xe0 console_on_rootfs+0x0/0x6c __primary_switched+0x88/0x90 ```"}], "references": [{"url": "https://git.kernel.org/stable/c/6fe0866014486736cc3ba1c6fd4606d3dbe55c9c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7cb10f17bddc415f30fbc00a4e2b490e0d94c462", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8d40bf73fa7f31eac2b0a7c9d85de67df82ee7f3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/94107259f972d2fd896dbbcaa176b3b2451ff9e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bedd287fdd3142dffad7ae2ac6ef15f4a2ad0629", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d886f8d85494d12b2752fd7c6c32162d982d5dd5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-8617", "published_date": "2025-05-15T20:15:58.990", "last_modified_date": "2025-06-04T20:08:32.893", "descriptions": [{"lang": "en", "value": "The Quiz Maker WordPress plugin before ******* does not sanitize and escape some of its settings, which could allow high-privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)"}, {"lang": "es", "value": "El complemento Quiz Maker para WordPress anterior a la versión ******* no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración multisitio)."}], "references": [{"url": "https://wpscan.com/vulnerability/ba6b6b82-6f21-45ff-bd64-685ea8ae1b82/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
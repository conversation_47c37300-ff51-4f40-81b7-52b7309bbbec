{"cve_id": "CVE-2025-37941", "published_date": "2025-05-20T16:15:31.883", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: codecs: wcd937x: fix a potential memory leak in wcd937x_soc_codec_probe()\n\nWhen snd_soc_dapm_new_controls() or snd_soc_dapm_add_routes() fails,\nwcd937x_soc_codec_probe() returns without releasing 'wcd937x->clsh_info',\nwhich is allocated by wcd_clsh_ctrl_alloc. Add wcd_clsh_ctrl_free()\nto prevent potential memory leak."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: códecs: wcd937x: se corrige una posible pérdida de memoria en wcd937x_soc_codec_probe(). Cuando fallan snd_soc_dapm_new_controls() o snd_soc_dapm_add_routes(), wcd937x_soc_codec_probe() regresa sin liberar 'wcd937x-&gt;clsh_info', que está asignado por wcd_clsh_ctrl_alloc. Se ha añadido wcd_clsh_ctrl_free() para evitar posibles pérdidas de memoria."}], "references": [{"url": "https://git.kernel.org/stable/c/3e330acf4efd63876d673c046cd073a1d4ed57a8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aafb5325aca3e806b3ea3707402189263473d257", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/acadb2e2b3c5b9977a843a3a94fece9bdcf6aea1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b573e04116fd33b9143fa276bbab2f0afad0a1ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
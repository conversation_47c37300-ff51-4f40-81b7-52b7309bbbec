{"cve_id": "CVE-2025-39537", "published_date": "2025-05-16T16:15:41.317", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Authorization Bypass Through User-Controlled Key vulnerability in Chimpstudio WP JobHunt allows Exploiting Incorrectly Configured Access Control Security Levels. This issue affects WP JobHunt: from n/a through 7.1."}, {"lang": "es", "value": "La vulnerabilidad de omisión de autorización mediante clave controlada por el usuario en Chimpstudio WP JobHunt permite explotar niveles de seguridad de control de acceso configurados incorrectamente. Este problema afecta a WP JobHunt desde la versión n/d hasta la 7.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-jobhunt/vulnerability/wordpress-wp-jobhunt-7-1-insecure-direct-object-references-idor-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
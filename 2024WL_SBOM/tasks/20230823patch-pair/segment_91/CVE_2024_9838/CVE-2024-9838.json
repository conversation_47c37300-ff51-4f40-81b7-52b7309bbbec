{"cve_id": "CVE-2024-9838", "published_date": "2025-05-15T20:16:01.450", "last_modified_date": "2025-06-12T16:33:57.980", "descriptions": [{"lang": "en", "value": "The Auto Affiliate Links WordPress plugin before 6.4.7 does not sanitize and escape a parameter before using it in a SQL statement, allowing admins to perform SQL injection attacks"}, {"lang": "es", "value": "El complemento Auto Affiliate Links de WordPress anterior a la versión 6.4.7 no depura ni escapa un parámetro antes de usarlo en una declaración SQL, lo que permite a los administradores realizar ataques de inyección SQL."}], "references": [{"url": "https://wpscan.com/vulnerability/3cc0ff78-b310-40a4-899c-15fecbb345c5/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37974", "published_date": "2025-05-20T17:15:47.987", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ns390/pci: Fix missing check for zpci_create_device() error return\n\nThe zpci_create_device() function returns an error pointer that needs to\nbe checked before dereferencing it as a struct zpci_dev pointer. Add the\nmissing check in __clp_add() where it was missed when adding the\nscan_list in the fixed commit. Simply not adding the device to the scan\nlist results in the previous behavior."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: s390/pci: Se corrige la falta de comprobación para el retorno del error zpci_create_device(). La función zpci_create_device() devuelve un puntero de error que debe comprobarse antes de desreferenciarlo como puntero struct zpci_dev. Se añade la comprobación faltante en __clp_add(), donde se omitió al añadir la lista de escaneo en el commit corregida. Simplemente no añadir el dispositivo a la lista de escaneo provoca el comportamiento anterior."}], "references": [{"url": "https://git.kernel.org/stable/c/2769b718e164df983c20c314b263a71a699be6cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42420c50c68f3e95e90de2479464f420602229fc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/be54b750c333a9db7c3b3686846bb06b07b011fe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
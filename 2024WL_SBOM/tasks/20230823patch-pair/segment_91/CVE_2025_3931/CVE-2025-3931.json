{"cve_id": "CVE-2025-3931", "published_date": "2025-05-14T12:15:19.493", "last_modified_date": "2025-07-25T22:15:25.153", "descriptions": [{"lang": "en", "value": "A flaw was found in Yggdrasil, which acts as a system broker, allowing the processes to communicate to other children's \"worker\" processes through the DBus component. Yggdrasil creates a DBus method to dispatch messages to workers. However, it misses authentication and authorization checks, allowing every system user to call it. One available Yggdrasil worker acts as a package manager with capabilities to create and enable new repositories and install or remove packages. \n\nThis flaw allows an attacker with access to the system to leverage the lack of authentication on the dispatch message to force the Yggdrasil worker to install arbitrary RPM packages. This issue results in local privilege escalation, enabling the attacker to access and modify sensitive system data."}, {"lang": "es", "value": "Se encontró una falla en Yggdrasil, que actúa como intermediario del sistema, permitiendo que los procesos se comuniquen con los procesos \"worker\" de otros subordinados a través del componente DBus. Yggdrasil crea un método DBus para enviar mensajes a los trabajadores. Sin embargo, omite las comprobaciones de autenticación y autorización, lo que permite que cualquier usuario del sistema lo invoque. Un trabajador de Yggdrasil disponible actúa como gestor de paquetes, con la capacidad de crear y habilitar nuevos repositorios e instalar o eliminar paquetes. Esta falla permite a un atacante con acceso al sistema aprovechar la falta de autenticación en el mensaje de envío para forzar al trabajador de Yggdrasil a instalar paquetes RPM arbitrarios. Este problema provoca una escalada de privilegios local, lo que permite al atacante acceder y modificar datos confidenciales del sistema."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2025:7592", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2025-3931", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2362345", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RedHatInsights/yggdrasil/pull/336", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-39481", "published_date": "2025-05-16T16:15:40.290", "last_modified_date": "2025-05-21T14:23:08.503", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in imithemes Eventer allows Blind SQL Injection. This issue affects Eventer: from n/a through 3.9.6."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en imithemes Eventer permite la inyección SQL ciega. Este problema afecta a Eventer desde n/d hasta la versión 3.9.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/eventer/vulnerability/wordpress-eventer-wordpress-event-booking-manager-plugin-plugin-3-9-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37959", "published_date": "2025-05-20T16:15:34.143", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: Scrub packet on bpf_redirect_peer\n\nWhen bpf_redirect_peer is used to redirect packets to a device in\nanother network namespace, the skb isn't scrubbed. That can lead skb\ninformation from one namespace to be \"misused\" in another namespace.\n\nAs one example, this is causing Cilium to drop traffic when using\nbpf_redirect_peer to redirect packets that just went through IPsec\ndecryption to a container namespace. The following pwru trace shows (1)\nthe packet path from the host's XFRM layer to the container's XFRM\nlayer where it's dropped and (2) the number of active skb extensions at\neach function.\n\n    NETNS       MARK  IFACE  TUPLE                                FUNC\n    4026533547  d00   eth0   ************:35473->************:53  xfrm_rcv_cb\n                             .active_extensions = (__u8)2,\n    4026533547  d00   eth0   ************:35473->************:53  xfrm4_rcv_cb\n                             .active_extensions = (__u8)2,\n    4026533547  d00   eth0   ************:35473->************:53  gro_cells_receive\n                             .active_extensions = (__u8)2,\n    [...]\n    4026533547  0     eth0   ************:35473->************:53  skb_do_redirect\n                             .active_extensions = (__u8)2,\n    4026534999  0     eth0   ************:35473->************:53  ip_rcv\n                             .active_extensions = (__u8)2,\n    4026534999  0     eth0   ************:35473->************:53  ip_rcv_core\n                             .active_extensions = (__u8)2,\n    [...]\n    4026534999  0     eth0   ************:35473->************:53  udp_queue_rcv_one_skb\n                             .active_extensions = (__u8)2,\n    4026534999  0     eth0   ************:35473->************:53  __xfrm_policy_check\n                             .active_extensions = (__u8)2,\n    4026534999  0     eth0   ************:35473->************:53  __xfrm_decode_session\n                             .active_extensions = (__u8)2,\n    4026534999  0     eth0   ************:35473->************:53  security_xfrm_decode_session\n                             .active_extensions = (__u8)2,\n    4026534999  0     eth0   ************:35473->************:53  kfree_skb_reason(SKB_DROP_REASON_XFRM_POLICY)\n                             .active_extensions = (__u8)2,\n\nIn this case, there are no XFRM policies in the container's network\nnamespace so the drop is unexpected. When we decrypt the IPsec packet,\nthe XFRM state used for decryption is set in the skb extensions. This\ninformation is preserved across the netns switch. When we reach the\nXFRM policy check in the container's netns, __xfrm_policy_check drops\nthe packet with LINUX_MIB_XFRMINNOPOLS because a (container-side) XFRM\npolicy can't be found that matches the (host-side) XFRM state used for\ndecryption.\n\nThis patch fixes this by scrubbing the packet when using\nbpf_redirect_peer, as is done on typical netns switches via veth\ndevices except skb->mark and skb->tstamp are not zeroed."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: Depuración de paquetes en bpf_redirect_peer. Cuando se usa bpf_redirect_peer para redirigir paquetes a un dispositivo en otro espacio de nombres de red, el skb no se depura. Esto puede provocar que la información de skb de un espacio de nombres se utilice indebidamente en otro. Por ejemplo, esto provoca que Cilium descarte tráfico al usar bpf_redirect_peer para redirigir paquetes que acaban de pasar por el descifrado de IPsec a un espacio de nombres de contenedor. El siguiente seguimiento de pwru muestra (1) la ruta del paquete desde la capa XFRM del host hasta la capa XFRM del contenedor, donde se descarta, y (2) el número de extensiones skb activas en cada función. NETNS MARK IFACE TUPLE FUNC 4026533547 d00 eth0 ************:35473-&gt;************:53 xfrm_rcv_cb .active_extensions = (__u8)2, 4026533547 d00 eth0 ************:35473-&gt;************:53 xfrm4_rcv_cb .active_extensions = (__u8)2, 4026533547 d00 eth0 ************:35473-&gt;************:53 gro_cells_receive .active_extensions = (__u8)2, [...] 4026533547 0 eth0 ************:35473-&gt;************:53 skb_do_redirect .active_extensions = (__u8)2, 4026534999 0 eth0 ************:35473-&gt;************:53 ip_rcv .active_extensions = (__u8)2, 4026534999 0 eth0 ************:35473-&gt;************:53 ip_rcv_core .active_extensions = (__u8)2, [...] 4026534999 0 eth0 ************:35473-&gt;************:53 udp_queue_rcv_one_skb .active_extensions = (__u8)2, 4026534999 0 eth0 ************:35473-&gt;************:53 __xfrm_policy_check .active_extensions = (__u8)2, 4026534999 0 eth0 ************:35473-&gt;************:53 __xfrm_decode_session .active_extensions = (__u8)2, 4026534999 0 eth0 ************:35473-&gt;************:53 security_xfrm_decode_session .active_extensions = (__u8)2, 4026534999 0 eth0 ************:35473-&gt;************:53 kfree_skb_reason(SKB_DROP_REASON_XFRM_POLICY) .active_extensions = (__u8)2. En este caso, no hay políticas XFRM en el espacio de nombres de red del contenedor, por lo que la pérdida es inesperada. Al descifrar el paquete IPsec, el estado XFRM utilizado para el descifrado se configura en las extensiones skb. Esta información se conserva en el conmutador netns. Al llegar a la comprobación de la política XFRM en las redes netn del contenedor, __xfrm_policy_check descarta el paquete con LINUX_MIB_XFRMINNOPOLS porque no se encuentra una política XFRM (del lado del contenedor) que coincida con el estado XFRM (del lado del host) utilizado para el descifrado. Este parche corrige este problema depurando el paquete al usar bpf_redirect_peer, como se hace en conmutadores netn típicos a través de dispositivos veth, excepto que skb-&gt;mark y skb-&gt;tstamp no se ponen a cero."}], "references": [{"url": "https://git.kernel.org/stable/c/355b0526336c0bf2bf7feaca033568ede524f763", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9e15ef33ba39fb6d9d1f51445957f16983a9437a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b37e54259cab4f78b53953d6f6268b85f07bef3e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c4327229948879814229b46aa26a750718888503", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/de1067cc8cf0e8c11ae20cbe5c467aef19d04ded", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-9227", "published_date": "2025-05-15T20:16:00.227", "last_modified_date": "2025-06-05T14:21:12.423", "descriptions": [{"lang": "en", "value": "The PowerPress Podcasting plugin by <PERSON><PERSON>rry WordPress plugin before 11.9.18 does not sanitise and escape some of its settings when adding a podcast, which could allow admin users to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed."}, {"lang": "es", "value": "El complemento PowerPress Podcasting de Blubrry para WordPress anterior a la versión 11.9.18 no depura ni escapa de algunas de sus configuraciones al agregar un podcast, lo que podría permitir a los usuarios administradores realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida."}], "references": [{"url": "https://wpscan.com/vulnerability/bb6515b9-a316-4146-8b7d-9b70a47aa366/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-9663", "published_date": "2025-05-15T20:16:01.007", "last_modified_date": "2025-06-12T16:33:05.327", "descriptions": [{"lang": "en", "value": "The CYAN Backup WordPress plugin before 2.5.3 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento CYAN Backup WordPress anterior a la versión 2.5.3 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/0dbd0927-f245-4202-b96b-e55f36a8bb30/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
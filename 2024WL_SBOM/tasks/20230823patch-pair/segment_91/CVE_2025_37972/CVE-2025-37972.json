{"cve_id": "CVE-2025-37972", "published_date": "2025-05-20T17:15:47.743", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nInput: mtk-pmic-keys - fix possible null pointer dereference\n\nIn mtk_pmic_keys_probe, the regs parameter is only set if the button is\nparsed in the device tree. However, on hardware where the button is left\nfloating, that node will most likely be removed not to enable that\ninput. In that case the code will try to dereference a null pointer.\n\nLet's use the regs struct instead as it is defined for all supported\nplatforms. Note that it is ok setting the key reg even if that latter is\ndisabled as the interrupt won't be enabled anyway."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Entrada: mtk-pmic-keys - corrección de posible desreferencia de puntero nulo. En mtk_pmic_keys_probe, el parámetro regs solo se establece si el botón se analiza en el árbol de dispositivos. Sin embargo, en hardware donde el botón se deja flotante, es muy probable que se elimine ese nodo para no habilitar esa entrada. En ese caso, el código intentará desreferenciar un puntero nulo. En su lugar, usemos la estructura regs, tal como está definida para todas las plataformas compatibles. Tenga en cuenta que es posible establecer el registro de clave incluso si este último está deshabilitado, ya que la interrupción no se habilitará de todos modos."}], "references": [{"url": "https://git.kernel.org/stable/c/09429ddb5a91e9e8f72cd18c012ec4171c2f85ec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/11cdb506d0fbf5ac05bf55f5afcb3a215c316490", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/334d74a798463ceec02a41eb0e2354aaac0d6249", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/619c05fb176c272ac6cecf723446b39723ee6d97", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90fa6015ff83ef1c373cc61b7c924ab2bcbe1801", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
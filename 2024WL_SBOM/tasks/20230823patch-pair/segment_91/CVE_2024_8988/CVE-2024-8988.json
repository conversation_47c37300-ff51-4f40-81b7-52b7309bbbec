{"cve_id": "CVE-2024-8988", "published_date": "2025-05-14T09:15:19.803", "last_modified_date": "2025-05-16T14:43:56.797", "descriptions": [{"lang": "en", "value": "The PeepSo Core: File Uploads plugin for WordPress is vulnerable to Insecure Direct Object Reference in all versions up to, and including, ******* via the file_download REST API endpoint due to missing validation on a user controlled key. This makes it possible for unauthenticated attackers to download files uploaded by others users and expose potentially sensitive information."}, {"lang": "es", "value": "El complemento PeepSo Core: File Uploads para WordPress es vulnerable a una Referencia Directa a Objetos Insegura en todas las versiones hasta la ******* incluida, a través del endpoint de la API REST file_download, debido a la falta de validación en una clave controlada por el usuario. Esto permite que atacantes no autenticados descarguen archivos subidos por otros usuarios y expongan información potencialmente confidencial."}], "references": [{"url": "https://www.peepso.com/changelog/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d3184996-655c-41d5-a3c5-6b36fbff58dc?source=cve", "source": "<EMAIL>", "tags": []}]}
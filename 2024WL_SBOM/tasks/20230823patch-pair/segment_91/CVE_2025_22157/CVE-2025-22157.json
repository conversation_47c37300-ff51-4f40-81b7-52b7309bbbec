{"cve_id": "CVE-2025-22157", "published_date": "2025-05-20T18:15:44.990", "last_modified_date": "2025-06-12T16:20:47.860", "descriptions": [{"lang": "en", "value": "This High severity PrivEsc (Privilege Escalation) vulnerability was introduced in versions:\n\n9.12.0, 10.3.0, 10.4.0, and 10.5.0 of Jira Core Data Center and Server\n\n5.12.0, 10.3.0, 10.4.0, and 10.5.0 of Jira Service Management Data Center and Server\n\nThis PrivEsc (Privilege Escalation) vulnerability, with a CVSS Score of 7.2, allows an attacker to perform actions as a higher-privileged user. \n\nAtlassian recommends that Jira Core Data Center and Server and Jira Service Management Data Center and Server customers upgrade to latest version, if you are unable to do so, upgrade your instance to one of the specified supported fixed versions:\n\nJira Core Data Center and Server 9.12: Upgrade to a release greater than or equal to 9.12.20\n\nJira Service Management Data Center and Server 5.12: Upgrade to a release greater than or equal to 5.12.20\n\nJira Core Data Center 10.3: Upgrade to a release greater than or equal to 10.3.5\n\nJira Service Management Data Center 10.3: Upgrade to a release greater than or equal to 10.3.5\n\nJira Core Data Center 10.4: Upgrade to a release greater than or equal to 10.6.0\n\nJira Service Management Data Center 10.4: Upgrade to a release greater than or equal to 10.6.0\n\nJira Core Data Center 10.5: Upgrade to a release greater than or equal to 10.5.1\n\nJira Service Management Data Center 10.5: Upgrade to a release greater than or equal to 10.5.1\n\nSee the release notes. You can download the latest version of Jira Core Data Center and Jira Service Management Data Center from the download center. \n\nThis vulnerability was reported via our Atlassian (Internal) program."}, {"lang": "es", "value": "Esta vulnerabilidad PrivEsc (escalada de privilegios) de alta gravedad se introdujo en las versiones: 9.12.0, 10.3.0, 10.4.0 y 10.5.0 de Jira Core Data Center y Server 5.12.0, 10.3.0, 10.4.0 y 10.5.0 de Jira Service Management Data Center y Server Esta vulnerabilidad PrivEsc (escalada de privilegios), con un puntaje CVSS de 7.2, permite a un atacante realizar acciones como un usuario con mayores privilegios. Atlassian recomienda que los clientes de Jira Core Data Center and Server y Jira Service Management Data Center and Server actualicen a la última versión. Si no pueden hacerlo, actualicen su instancia a una de las versiones fijas compatibles especificadas: Jira Core Data Center and Server 9.12: Actualizar a una versión posterior o igual a la 9.12.20 Jira Service Management Data Center and Server 5.12: Actualizar a una versión posterior o igual a la 5.12.20 Jira Core Data Center 10.3: Actualizar a una versión posterior o igual a la 10.3.5 Jira Service Management Data Center 10.3: Actualizar a una versión posterior o igual a la 10.3.5 Jira Core Data Center 10.4: Actualizar a una versión posterior o igual a la 10.6.0 Jira Service Management Data Center 10.4: Actualizar a una versión posterior o igual a la 10.6.0 Jira Core Data Center 10.5: Actualizar a una versión posterior o igual a la 10.5.1 Jira Service Management Data Center 10.5: Actualice a una versión superior o igual a la 10.5.1. Consulte las notas de la versión. Puede descargar la última versión de Jira Core Data Center y Jira Service Management Data Center desde el centro de descargas. Esta vulnerabilidad se reportó a través de nuestro programa interno de Atlassian."}], "references": [{"url": "https://confluence.atlassian.com/pages/viewpage.action?pageId=1561365992", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://jira.atlassian.com/browse/JRASERVER-78766", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://jira.atlassian.com/browse/JSDSERVER-16206", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}
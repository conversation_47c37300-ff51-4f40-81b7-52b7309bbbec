{"cve_id": "CVE-2025-37990", "published_date": "2025-05-20T18:15:45.883", "last_modified_date": "2025-06-04T13:15:27.940", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: brcm80211: fmac: Add error handling for brcmf_usb_dl_writeimage()\n\nThe function brcmf_usb_dl_writeimage() calls the function\nbrcmf_usb_dl_cmd() but dose not check its return value. The\n'state.state' and the 'state.bytes' are uninitialized if the\nfunction brcmf_usb_dl_cmd() fails. It is dangerous to use\nuninitialized variables in the conditions.\n\nAdd error handling for brcmf_usb_dl_cmd() to jump to error\nhandling path if the brcmf_usb_dl_cmd() fails and the\n'state.state' and the 'state.bytes' are uninitialized.\n\nImprove the error message to report more detailed error\ninformation."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: brcm80211: fmac: Añadir gestión de errores para brcmf_usb_dl_writeimage() La función brcmf_usb_dl_writeimage() llama a la función brcmf_usb_dl_cmd() pero no comprueba su valor de retorno. 'state.state' y 'state.bytes' no se inicializan si la función brcmf_usb_dl_cmd() falla. Es peligroso utilizar variables no inicializadas en las condiciones. Añadir gestión de errores para brcmf_usb_dl_cmd() para saltar a la ruta de gestión de errores si brcmf_usb_dl_cmd() falla y 'state.state' y 'state.bytes' no se inicializan. Mejorar el mensaje de error para informar de errores más detallados."}], "references": [{"url": "https://git.kernel.org/stable/c/08424a0922fb9e32a19b09d852ee87fb6c497538", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/508be7c001437bacad7b9a43f08a723887bcd1ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/524b70441baba453b193c418e3142bd31059cc1f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/62a4f2955d9a1745bdb410bf83fb16666d8865d6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e089e7b585d95122c8122d732d1d5ef8f879396", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/972bf75e53f778c78039c5d139dd47443a6d66a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bdb435ef9815b1ae28eefffa01c6959d0fcf1fa7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fa9b9f02212574ee1867fbefb0a675362a71b31d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
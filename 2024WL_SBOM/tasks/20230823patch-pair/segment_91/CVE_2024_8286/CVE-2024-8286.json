{"cve_id": "CVE-2024-8286", "published_date": "2025-05-15T20:15:58.417", "last_modified_date": "2025-06-12T15:34:28.220", "descriptions": [{"lang": "en", "value": "The webtoffee-gdpr-cookie-consent WordPress plugin before 2.6.1 does not have CSRF checks in some bulk actions, which could allow attackers to make logged in admins perform unwanted actions, such as deleting visit logs via CSRF attacks"}, {"lang": "es", "value": "El complemento webtoffee-gdpr-cookie-consent de WordPress anterior a la versión 2.6.1 no tiene comprobaciones CSRF en algunas acciones masivas, lo que podría permitir a los atacantes hacer que los administradores conectados realicen acciones no deseadas, como eliminar registros de visitas mediante ataques CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/628bbac0-76b1-4666-9c00-bae84b48f85c/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
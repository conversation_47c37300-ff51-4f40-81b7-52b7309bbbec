{"cve_id": "CVE-2025-22233", "published_date": "2025-05-16T20:15:22.143", "last_modified_date": "2025-05-19T13:35:20.460", "descriptions": [{"lang": "en", "value": "CVE-2024-38820 ensured Locale-independent, lowercase conversion for both the configured disallowedFields patterns and for request parameter names. However, there are still cases where it is possible to bypass the disallowedFields checks.\n\nAffected Spring Products and Versions\n\nSpring Framework:\n  *  6.2.0 - 6.2.6\n\n  *  6.1.0 - 6.1.19\n\n  *  6.0.0 - 6.0.27\n\n  *  5.3.0 - 5.3.42\n  *  Older, unsupported versions are also affected\n\n\n\nMitigation\n\nUsers of affected versions should upgrade to the corresponding fixed version.\n\nAffected version(s)Fix Version Availability 6.2.x\n 6.2.7\nOSS6.1.x\n 6.1.20\nOSS6.0.x\n 6.0.28\n Commercial https://enterprise.spring.io/ 5.3.x\n 5.3.43\n Commercial https://enterprise.spring.io/ \nNo further mitigation steps are necessary.\n\n\nGenerally, we recommend using a dedicated model object with properties only for data binding, or using constructor binding since constructor arguments explicitly declare what to bind together with turning off setter binding through the declarativeBinding flag. See the Model Design section in the reference documentation.\n\nFor setting binding, prefer the use of allowedFields (an explicit list) over disallowedFields.\n\nCredit\n\nThis issue was responsibly reported by the TERASOLUNA Framework Development Team from NTT DATA Group Corporation."}, {"lang": "es", "value": "La CVE-2024-38820 garantizó la conversión a minúsculas, independiente de la configuración regional, tanto para los patrones de disallowedFields configurados como para los nombres de los parámetros de solicitud. Sin embargo, aún existen casos en los que es posible omitir las comprobaciones de disallowedFields. Productos y versiones de Spring afectados: Spring Framework: * 6.2.0 - 6.2.6 * 6.1.0 - 6.1.19 * 6.0.0 - 6.0.27 * 5.3.0 - 5.3.42 * Las versiones anteriores sin soporte también se ven afectadas. Mitigación: Los usuarios de las versiones afectadas deben actualizar a la versión corregida correspondiente. Versión(s) afectada(s) Versión de corrección Disponibilidad 6.2.x 6.2.7 OSS6.1.x 6.1.20 OSS6.0.x 6.0.28 Comercial https://enterprise.spring.io/ 5.3.x 5.3.43 Comercial https://enterprise.spring.io/ No se necesitan más medidas de mitigación. En general, recomendamos usar un objeto de modelo dedicado con propiedades solo para el enlace de datos o usar el enlace del constructor, ya que los argumentos del constructor declaran explícitamente qué enlazar junto con la desactivación del enlace del establecedor a través del indicador declarativeBinding. Consulte la sección Diseño del modelo en la documentación de referencia. Para el enlace de configuración, prefiera el uso de allowedFields (una lista explícita) en lugar de disallowedFields. Crédito Este problema fue reportado responsablemente por el equipo de desarrollo del marco TERASOLUNA de NTT DATA Group Corporation."}], "references": [{"url": "https://nvd.nist.gov/vuln-metrics/cvss/v3-calculator?vector=AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:N&version=3.1", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-37895", "published_date": "2025-05-20T16:15:25.860", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbnxt_en: Fix error handling path in bnxt_init_chip()\n\nWARN_ON() is triggered in __flush_work() if bnxt_init_chip() fails\nbecause we call cancel_work_sync() on dim work that has not been\ninitialized.\n\nWARNING: CPU: 37 PID: 5223 at kernel/workqueue.c:4201 __flush_work.isra.0+0x212/0x230\n\nThe driver relies on the BNXT_STATE_NAPI_DISABLED bit to check if dim\nwork has already been cancelled.  But in the bnxt_open() path,\nBNXT_STATE_NAPI_DISABLED is not set and this causes the error\npath to think that it needs to cancel the uninitalized dim work.\nFix it by setting BNXT_STATE_NAPI_DISABLED during initialization.\nThe bit will be cleared when we enable NAPI and initialize dim work."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bnxt_en: Corregir la ruta de manejo de errores en bnxt_init_chip() WARN_ON() se activa en __flush_work() si bnxt_init_chip() falla porque llamamos a cancel_work_sync() en el trabajo de atenuación que no se ha inicializado. ADVERTENCIA: CPU: 37 PID: 5223 en kernel/workqueue.c:4201 __flush_work.isra.0+0x212/0x230 El controlador se basa en el bit BNXT_STATE_NAPI_DISABLED para comprobar si el trabajo de atenuación ya se ha cancelado. Pero en la ruta bnxt_open(), BNXT_STATE_NAPI_DISABLED no está configurado y esto hace que la ruta de error piense que necesita cancelar el trabajo de atenuación no inicializado. Corríjalo configurando BNXT_STATE_NAPI_DISABLED durante la inicialización. El bit se borrará cuando habilitemos NAPI e inicialicemos el trabajo de atenuación."}], "references": [{"url": "https://git.kernel.org/stable/c/21116727f452474502ee74f956d5e7466103e19b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ab7a709c926c16b4433cf02d04fcbcf35aaab2b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e039b00ddbfeaa0dc59b8659be114f1a1b37c5bf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
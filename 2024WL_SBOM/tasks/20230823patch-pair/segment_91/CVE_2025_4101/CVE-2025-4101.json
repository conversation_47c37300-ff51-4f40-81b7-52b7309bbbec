{"cve_id": "CVE-2025-4101", "published_date": "2025-05-17T13:15:47.910", "last_modified_date": "2025-05-28T13:28:20.060", "descriptions": [{"lang": "en", "value": "The MultiVendorX – WooCommerce Multivendor Marketplace Solutions plugin for WordPress is vulnerable to unauthorized loss of data due to a misconfigured capability check on the 'delete_fpm_product' function in all versions up to, and including, 4.2.22. This makes it possible for authenticated attackers, with Contributor-level access and above, to delete arbitrary posts, pages, attachments, and products. The vulnerability was partially patched in version 4.2.22."}, {"lang": "es", "value": "El complemento MultiVendorX – WooCommerce Multivendor Marketplace Solutions para WordPress es vulnerable a la pérdida no autorizada de datos debido a una comprobación de capacidad mal configurada en la función \"delete_fpm_product\" en todas las versiones hasta la 4.2.22 incluida. Esto permite a atacantes autenticados, con acceso de Colaborador o superior, eliminar entradas, páginas, archivos adjuntos y productos arbitrarios. La vulnerabilidad se corrigió parcialmente en la versión 4.2.22."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/dc-woocommerce-multi-vendor/trunk/classes/class-mvx-ajax.php#L982", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3293832/dc-woocommerce-multi-vendor/trunk/classes/class-mvx-ajax.php?old=3272848&old_path=dc-woocommerce-multi-vendor%2Ftrunk%2Fclasses%2Fclass-mvx-ajax.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5c1fd517-32ee-429d-9026-512afe117dc5?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
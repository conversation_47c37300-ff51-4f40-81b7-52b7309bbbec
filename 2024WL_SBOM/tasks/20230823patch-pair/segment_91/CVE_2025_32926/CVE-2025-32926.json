{"cve_id": "CVE-2025-32926", "published_date": "2025-05-19T20:15:22.213", "last_modified_date": "2025-06-09T17:09:57.147", "descriptions": [{"lang": "en", "value": "Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal') vulnerability in ThemeGoods Grand Restaurant WordPress allows Path Traversal.This issue affects Grand Restaurant WordPress: from n/a through 7.0."}, {"lang": "es", "value": "La vulnerabilidad de limitación incorrecta de una ruta a un directorio restringido ('Path Traversal') en ThemeGoods Grand Restaurant WordPress permite Path Traversal. Este problema afecta a Grand Restaurant WordPress: desde n/a hasta 7.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/grandrestaurant/vulnerability/wordpress-grand-restaurant-wordpress-theme-7-0-path-traversal-to-php-object-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
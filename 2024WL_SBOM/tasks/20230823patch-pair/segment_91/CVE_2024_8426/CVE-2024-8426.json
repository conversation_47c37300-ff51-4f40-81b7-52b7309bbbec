{"cve_id": "CVE-2024-8426", "published_date": "2025-05-15T20:15:58.663", "last_modified_date": "2025-05-27T19:52:11.790", "descriptions": [{"lang": "en", "value": "The Page Builder: Pagelayer  WordPress plugin before 1.8.8 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Cross-Site Scripting attacks even when unfiltered_html is disallowed"}, {"lang": "es", "value": "El complemento Page Builder: Pagelayer de WordPress anterior a la versión 1.8.8 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting incluso cuando unfiltered_html no está permitido."}], "references": [{"url": "https://wpscan.com/vulnerability/f81b7478-c775-45ff-bbb8-d13c3f58acc6/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-9238", "published_date": "2025-05-15T20:16:00.463", "last_modified_date": "2025-06-12T16:31:47.370", "descriptions": [{"lang": "en", "value": "The AVIF Uploader WordPress plugin before 1.1.1 does not sanitise uploaded SVG files, which could allow users with a role as low as Author to upload a malicious SVG containing XSS payloads."}, {"lang": "es", "value": "El complemento AVIF Uploader de WordPress anterior a la versión 1.1.1 no depura los archivos SVG cargados, lo que podría permitir que los usuarios con un rol tan bajo como Autor carguen un SVG malicioso que contenga payloads XSS."}], "references": [{"url": "https://wpscan.com/vulnerability/a7de0cf6-3064-4595-9037-f8407fe40724/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
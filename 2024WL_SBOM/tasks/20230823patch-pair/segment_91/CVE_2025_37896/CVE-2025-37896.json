{"cve_id": "CVE-2025-37896", "published_date": "2025-05-20T16:15:25.960", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nspi: spi-mem: Add fix to avoid divide error\n\nFor some SPI flash memory operations, dummy bytes are not mandatory. For\nexample, in Winbond SPINAND flash memory devices, the `write_cache` and\n`update_cache` operation variants have zero dummy bytes. Calculating the\nduration for SPI memory operations with zero dummy bytes causes\na divide error when `ncycles` is calculated in the\nspi_mem_calc_op_duration().\n\nAdd changes to skip the 'ncylcles' calculation for zero dummy bytes.\n\nFollowing divide error is fixed by this change:\n\n Oops: divide error: 0000 [#1] PREEMPT SMP NOPTI\n...\n\n  ? do_trap+0xdb/0x100\n  ? do_error_trap+0x75/0xb0\n  ? spi_mem_calc_op_duration+0x56/0xb0\n  ? exc_divide_error+0x3b/0x70\n  ? spi_mem_calc_op_duration+0x56/0xb0\n  ? asm_exc_divide_error+0x1b/0x20\n  ? spi_mem_calc_op_duration+0x56/0xb0\n  ? spinand_select_op_variant+0xee/0x190 [spinand]\n  spinand_match_and_init+0x13e/0x1a0 [spinand]\n  spinand_manufacturer_match+0x6e/0xa0 [spinand]\n  spinand_probe+0x357/0x7f0 [spinand]\n  ? kernfs_activate+0x87/0xd0\n  spi_mem_probe+0x7a/0xb0\n  spi_probe+0x7d/0x130"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: spi: spi-mem: Agregar corrección para evitar el error de división Para algunas operaciones de memoria flash SPI, los bytes ficticios no son obligatorios. Por ejemplo, en dispositivos de memoria flash Winbond SPINAND, las variantes de operación `write_cache` y `update_cache` tienen cero bytes ficticios. Calcular la duración de las operaciones de memoria SPI con cero bytes ficticios causa un error de división cuando se calcula `ncycles` en spi_mem_calc_op_duration(). Agregar cambios para omitir el cálculo de 'ncylcles' para cero bytes ficticios. El siguiente error de división se corrige con este cambio: Oops: error de división: 0000 [#1] PREEMPT SMP NOPTI ... ? do_trap+0xdb/0x100 ? do_error_trap+0x75/0xb0 ? spi_mem_calc_op_duration+0x56/0xb0 ? Error de división de error de exc. +0x3b/0x70 ? Duración de operación de cálculo de memoria spi +0x56/0xb0 ? Error de división de error de asm de exc. +0x1b/0x20 ? Duración de operación de cálculo de memoria spi +0x56/0xb0 ? Variante de operación de selección de spinand +0xee/0x190 [spinand] Coincidencia de spinand e inicialización +0x13e/0x1a0 [spinand] Coincidencia de fabricante de spinand +0x6e/0xa0 [spinand] Sonda de spinand +0x357/0x7f0 [spinand] ? Activación de Kernfs +0x87/0xd0 Sonda de memoria spi +0x7a/0xb0 Sonda spi +0x7d/0x130"}], "references": [{"url": "https://git.kernel.org/stable/c/1915dbd67dadc0bb35670c8e28229baa29368d17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e4d3d8a5e51e07bd0d6cdd81b5e4af79f796927", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
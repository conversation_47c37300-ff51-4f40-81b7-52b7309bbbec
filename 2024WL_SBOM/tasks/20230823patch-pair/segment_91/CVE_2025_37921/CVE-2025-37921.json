{"cve_id": "CVE-2025-37921", "published_date": "2025-05-20T16:15:28.713", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nvxlan: vnifilter: Fix unlocked deletion of default FDB entry\n\nWhen a VNI is deleted from a VXLAN device in 'vnifilter' mode, the FDB\nentry associated with the default remote (assuming one was configured)\nis deleted without holding the hash lock. This is wrong and will result\nin a warning [1] being generated by the lockdep annotation that was\nadded by commit ebe642067455 (\"vxlan: Create wrappers for FDB lookup\").\n\nReproducer:\n\n # ip link add vx0 up type vxlan dstport 4789 external vnifilter local *********\n # bridge vni add vni 10010 remote ************ dev vx0\n # bridge vni del vni 10010 dev vx0\n\nFix by acquiring the hash lock before the deletion and releasing it\nafterwards. Blame the original commit that introduced the issue rather\nthan the one that exposed it.\n\n[1]\nWARNING: CPU: 3 PID: 392 at drivers/net/vxlan/vxlan_core.c:417 vxlan_find_mac+0x17f/0x1a0\n[...]\nRIP: 0010:vxlan_find_mac+0x17f/0x1a0\n[...]\nCall Trace:\n <TASK>\n __vxlan_fdb_delete+0xbe/0x560\n vxlan_vni_delete_group+0x2ba/0x940\n vxlan_vni_del.isra.0+0x15f/0x580\n vxlan_process_vni_filter+0x38b/0x7b0\n vxlan_vnifilter_process+0x3bb/0x510\n rtnetlink_rcv_msg+0x2f7/0xb70\n netlink_rcv_skb+0x131/0x360\n netlink_unicast+0x426/0x710\n netlink_sendmsg+0x75a/0xc20\n __sock_sendmsg+0xc1/0x150\n ____sys_sendmsg+0x5aa/0x7b0\n ___sys_sendmsg+0xfc/0x180\n __sys_sendmsg+0x121/0x1b0\n do_syscall_64+0xbb/0x1d0\n entry_SYSCALL_64_after_hwframe+0x4b/0x53"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: vxlan: vnifilter: Se corrige la eliminación desbloqueada de la entrada FDB predeterminada. Cuando se elimina una VNI de un dispositivo VXLAN en modo 'vnifilter', la entrada FDB asociada al control remoto predeterminado (si se configuró uno) se elimina sin mantener el bloqueo hash. Esto es incorrecto y generará una advertencia [1] generada por la anotación lockdep, añadida por el commit ebe642067455 (\"vxlan: Crear envoltorios para la búsqueda FDB\"). Reproductor: # ip link add vx0 up type vxlan dstport 4789 external vnifilter local ********* # bridge vni add vni 10010 remote ************ dev vx0 # bridge vni del vni 10010 dev vx0 Se corrige adquiriendo el bloqueo hash antes de la eliminación y liberándolo después. Culpe a el commit original que introdujo el problema en lugar de a la que lo expuso. [1] ADVERTENCIA: CPU: 3 PID: 392 en drivers/net/vxlan/vxlan_core.c:417 vxlan_find_mac+0x17f/0x1a0 [...] RIP: 0010:vxlan_find_mac+0x17f/0x1a0 [...] Rastreo de llamadas:  __vxlan_fdb_delete+0xbe/0x560 vxlan_vni_delete_group+0x2ba/0x940 vxlan_vni_del.isra.0+0x15f/0x580 vxlan_process_vni_filter+0x38b/0x7b0 vxlan_vnifilter_process+0x3bb/0x510 rtnetlink_rcv_msg+0x2f7/0xb70 netlink_rcv_skb+0x131/0x360 netlink_unicast+0x426/0x710 netlink_sendmsg+0x75a/0xc20 __sock_sendmsg+0xc1/0x150 ____sys_sendmsg+0x5aa/0x7b0 ___sys_sendmsg+0xfc/0x180 __sys_sendmsg+0x121/0x1b0 do_syscall_64+0xbb/0x1d0 entry_SYSCALL_64_after_hwframe+0x4b/0x53 "}], "references": [{"url": "https://git.kernel.org/stable/c/087a9eb9e5978e3ba362e1163691e41097e8ca20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2d4a121296aa3940d2df9906f955c2b6b4e38bc3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3576e9a80b6c4381b01ce0cbaa07f5e92d4492ed", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/470206205588559e60035fceb5f256640cb45f99", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5cb9e07f84e527974b12e82e2549fa6c0cc6eef0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
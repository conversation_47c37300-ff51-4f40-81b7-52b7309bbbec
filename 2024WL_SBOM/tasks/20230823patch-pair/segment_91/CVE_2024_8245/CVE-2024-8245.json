{"cve_id": "CVE-2024-8245", "published_date": "2025-05-15T20:15:58.260", "last_modified_date": "2025-06-12T15:25:29.653", "descriptions": [{"lang": "en", "value": "The GamiPress  WordPress plugin before 1.0.1 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin change them via a CSRF attack"}, {"lang": "es", "value": "El complemento GamiPress para WordPress anterior a la versión 1.0.1 no tiene la comprobación CSRF activada al actualizar su configuración, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión la cambie mediante un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/3fb6292c-502c-481a-8223-ecda03d4c3fe/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-0131", "published_date": "2025-05-14T19:15:51.233", "last_modified_date": "2025-05-16T14:43:56.797", "descriptions": [{"lang": "en", "value": "An incorrect privilege management vulnerability in the OPSWAT MetaDefender Endpoint Security SDK used by the Palo Alto Networks GlobalProtect™ app on Windows devices allows a locally authenticated non-administrative Windows user to escalate their privileges to NT AUTHORITY\\SYSTEM. However, execution requires that the local user also successfully exploits a race condition, which makes this vulnerability difficult to exploit."}, {"lang": "es", "value": "Una vulnerabilidad de gestión incorrecta de privilegios en el SDK de seguridad de endpoints OPSWAT MetaDefender, utilizado por la aplicación GlobalProtect™ de Palo Alto Networks en dispositivos Windows, permite que un usuario de Windows no administrativo autenticado localmente escale sus privilegios a NT AUTHORITY\\SYSTEM. Sin embargo, la ejecución requiere que el usuario local también explote con éxito una condición de carrera, lo que dificulta la explotación de esta vulnerabilidad."}], "references": [{"url": "https://security.paloaltonetworks.com/CVE-2025-0131", "source": "<EMAIL>", "tags": []}, {"url": "https://www.opswat.com/docs/mdsdk/release-notes/cve-2025-0131", "source": "<EMAIL>", "tags": []}]}
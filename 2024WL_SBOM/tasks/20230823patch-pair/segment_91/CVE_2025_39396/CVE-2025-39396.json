{"cve_id": "CVE-2025-39396", "published_date": "2025-05-19T18:15:28.863", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in Crocoblock JetReviews allows PHP Local File Inclusion.This issue affects JetReviews: from n/a through 2.3.6."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión de archivos remotos PHP') en Crocoblock JetReviews permite la inclusión de archivos locales PHP. Este problema afecta a JetReviews: desde n/a hasta 2.3.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/jet-reviews/vulnerability/wordpress-jetreviews-plugin-2-3-6-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-39355", "published_date": "2025-05-19T20:15:23.490", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in roninwp FAT Services Booking allows SQL Injection.This issue affects FAT Services Booking: from n/a through 5.6."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en roninwp FAT Services Booking permite la inyección SQL. Este problema afecta a FAT Services Booking: desde n/a hasta 5.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/fat-services-booking/vulnerability/wordpress-fat-services-booking-plugin-5-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
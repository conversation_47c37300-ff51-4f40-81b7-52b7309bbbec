{"cve_id": "CVE-2025-3875", "published_date": "2025-05-14T17:15:48.470", "last_modified_date": "2025-06-05T14:27:05.837", "descriptions": [{"lang": "en", "value": "Thunderbird parses addresses in a way that can allow sender spoofing in case the server allows an invalid From address to be used. For example, if the From header contains an (invalid) value \"Spoofed Name  \", <NAME_EMAIL> as the actual address. This vulnerability affects Thunderbird < 128.10.1 and Thunderbird < 138.0.1."}, {"lang": "es", "value": "Thunderbird analiza las direcciones de forma que puede permite la suplantación del remitente si el servidor permite el uso de una dirección de remitente no válida. Por ejemplo, si el encabezado \"De\" contiene el valor (inválido) \"Nombre falsificado\", <NAME_EMAIL> como la dirección real. Esta vulnerabilidad afecta a Thunderbird &lt; 128.10.1 y Thunderbird &lt; 138.0.1."}], "references": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1950629", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-34/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-35/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
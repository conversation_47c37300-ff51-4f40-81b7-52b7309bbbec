{"cve_id": "CVE-2024-9662", "published_date": "2025-05-15T20:16:00.917", "last_modified_date": "2025-06-12T16:32:42.000", "descriptions": [{"lang": "en", "value": "The CYAN Backup WordPress plugin before 2.5.3 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento CYAN Backup WordPress anterior a la versión 2.5.3 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/dfa6ff7d-c0dc-4118-afe0-587a24c76f12/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
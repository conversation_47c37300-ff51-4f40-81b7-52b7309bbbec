{"cve_id": "CVE-2025-37987", "published_date": "2025-05-20T18:15:45.567", "last_modified_date": "2025-06-04T13:15:27.810", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\npds_core: Prevent possible adminq overflow/stuck condition\n\nThe pds_core's adminq is protected by the adminq_lock, which prevents\nmore than 1 command to be posted onto it at any one time. This makes it\nso the client drivers cannot simultaneously post adminq commands.\nHowever, the completions happen in a different context, which means\nmultiple adminq commands can be posted sequentially and all waiting\non completion.\n\nOn the FW side, the backing adminq request queue is only 16 entries\nlong and the retry mechanism and/or overflow/stuck prevention is\nlacking. This can cause the adminq to get stuck, so commands are no\nlonger processed and completions are no longer sent by the FW.\n\nAs an initial fix, prevent more than 16 outstanding adminq commands so\nthere's no way to cause the adminq from getting stuck. This works\nbecause the backing adminq request queue will never have more than 16\npending adminq commands, so it will never overflow. This is done by\nreducing the adminq depth to 16."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: pds_core: Prevenir posible condición de desbordamiento/atascamiento de adminq El adminq de pds_core está protegido por adminq_lock, que impide que se le envíe más de 1 comando a la vez. Esto hace que los controladores del cliente no puedan enviar comandos adminq simultáneamente. Sin embargo, las finalizaciones ocurren en un contexto diferente, lo que significa que se pueden enviar múltiples comandos adminq secuencialmente y todos esperando a ser completados. En el lado del FW, la cola de solicitudes adminq de respaldo solo tiene 16 entradas y falta el mecanismo de reintento o la prevención de desbordamiento/atascamiento. Esto puede provocar que adminq se atasque, por lo que el FW ya no procesa los comandos y ya no envía las finalizaciones. Como solución inicial, evite que haya más de 16 comandos adminq pendientes para que no haya forma de provocar que adminq se atasque. Esto funciona porque la cola de solicitudes adminq de respaldo nunca tendrá más de 16 comandos adminq pendientes, por lo que nunca se desbordará. Esto se hace reduciendo la profundidad de adminq a 16."}], "references": [{"url": "https://git.kernel.org/stable/c/2982e07ad72b48eb12c29a87a3f2126ea552688c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/517f928cc0c133472618cbba18382b46f5f71ba3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e3dc65675faad846420d24762e4faadc12d9392", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d9e2f070d8af60f2c8c02b2ddf0a9e90b4e9220c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-1288", "published_date": "2025-05-15T20:16:02.227", "last_modified_date": "2025-06-12T16:38:01.743", "descriptions": [{"lang": "en", "value": "The WOOEXIM  WordPress plugin through 5.0.0 does not have CSRF check in some places, and is missing sanitisation as well as escaping, which could allow attackers to make an unauthenticated user vulnerable to reflected XSS via a CSRF attack."}, {"lang": "es", "value": "El complemento WOOEXIM para WordPress hasta la versión 5.0.0 no tiene verificación CSRF en algunos lugares y carece de depuración y escape, lo que podría permitir a los atacantes hacer que un usuario no autenticado sea vulnerable a XSS reflejado a través de un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/175af35d-6972-42c9-b7ac-913ce1fbac64/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
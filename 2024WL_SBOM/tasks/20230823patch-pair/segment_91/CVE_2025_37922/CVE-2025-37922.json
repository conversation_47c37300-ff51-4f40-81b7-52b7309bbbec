{"cve_id": "CVE-2025-37922", "published_date": "2025-05-20T16:15:28.827", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbook3s64/radix : Align section vmemmap start address to PAGE_SIZE\n\nA vmemmap altmap is a device-provided region used to provide\nbacking storage for struct pages. For each namespace, the altmap\nshould belong to that same namespace. If the namespaces are\ncreated unaligned, there is a chance that the section vmemmap\nstart address could also be unaligned. If the section vmemmap\nstart address is unaligned, the altmap page allocated from the\ncurrent namespace might be used by the previous namespace also.\nDuring the free operation, since the altmap is shared between two\nnamespaces, the previous namespace may detect that the page does\nnot belong to its altmap and incorrectly assume that the page is a\nnormal page. It then attempts to free the normal page, which leads\nto a kernel crash.\n\n<PERSON>el attempted to read user page (18) - exploit attempt? (uid: 0)\nBUG: Kernel NULL pointer dereference on read at 0x00000018\nFaulting instruction address: 0xc000000000530c7c\nOops: Kernel access of bad area, sig: 11 [#1]\nLE PAGE_SIZE=64K MMU=Radix SMP NR_CPUS=2048 NUMA pSeries\nCPU: 32 PID: 2104 Comm: ndctl Kdump: loaded Tainted: G        W\nNIP:  c000000000530c7c LR: c000000000530e00 CTR: 0000000000007ffe\nREGS: c000000015e57040 TRAP: 0300   Tainted: G        W\nMSR:  800000000280b033 <SF,VEC,VSX,EE,FP,ME,IR,DR,RI,LE>  CR: 84482404\nCFAR: c000000000530dfc DAR: 0000000000000018 DSISR: 40000000 IRQMASK: 0\nGPR00: c000000000530e00 c000000015e572e0 c000000002c5cb00 c00c000101008040\nGPR04: 0000000000000000 0000000000000007 0000000000000001 000000000000001f\nGPR08: 0000000000000005 0000000000000000 0000000000000018 0000000000002000\nGPR12: c0000000001d2fb0 c0000060de6b0080 0000000000000000 c0000060dbf90020\nGPR16: c00c000101008000 0000000000000001 0000000000000000 c000000125b20f00\nGPR20: 0000000000000001 0000000000000000 ffffffffffffffff c00c000101007fff\nGPR24: 0000000000000001 0000000000000000 0000000000000000 0000000000000000\nGPR28: 0000000004040201 0000000000000001 0000000000000000 c00c000101008040\nNIP [c000000000530c7c] get_pfnblock_flags_mask+0x7c/0xd0\nLR [c000000000530e00] free_unref_page_prepare+0x130/0x4f0\nCall Trace:\nfree_unref_page+0x50/0x1e0\nfree_reserved_page+0x40/0x68\nfree_vmemmap_pages+0x98/0xe0\nremove_pte_table+0x164/0x1e8\nremove_pmd_table+0x204/0x2c8\nremove_pud_table+0x1c4/0x288\nremove_pagetable+0x1c8/0x310\nvmemmap_free+0x24/0x50\nsection_deactivate+0x28c/0x2a0\n__remove_pages+0x84/0x110\narch_remove_memory+0x38/0x60\nmemunmap_pages+0x18c/0x3d0\ndevm_action_release+0x30/0x50\nrelease_nodes+0x68/0x140\ndevres_release_group+0x100/0x190\ndax_pmem_compat_release+0x44/0x80 [dax_pmem_compat]\ndevice_for_each_child+0x8c/0x100\n[dax_pmem_compat_remove+0x2c/0x50 [dax_pmem_compat]\nnvdimm_bus_remove+0x78/0x140 [libnvdimm]\ndevice_remove+0x70/0xd0\n\nAnother issue is that if there is no altmap, a PMD-sized vmemmap\npage will be allocated from RAM, regardless of the alignment of\nthe section start address. If the section start address is not\naligned to the PMD size, a VM_BUG_ON will be triggered when\nsetting the PMD-sized page to page table.\n\nIn this patch, we are aligning the section vmemmap start address\nto PAGE_SIZE. After alignment, the start address will not be\npart of the current namespace, and a normal page will be allocated\nfor the vmemmap mapping of the current section. For the remaining\nsections, altmaps will be allocated. During the free operation,\nthe normal page will be correctly freed.\n\nIn the same way, a PMD_SIZE vmemmap page will be allocated only if\nthe section start address is PMD_SIZE-aligned; otherwise, it will\nfall back to a PAGE-sized vmemmap allocation.\n\nWithout this patch\n==================\nNS1 start               NS2 start\n _________________________________________________________\n|         NS1               |            NS2              |\n ---------------------------------------------------------\n| Altmap| Altmap | .....|Altmap| Altmap | ...........\n|  NS1  |  NS1   \n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: book3s64/radix : Alinear la dirección de inicio de la sección vmemmap a PAGE_SIZE Un altmap de vmemmap es una región proporcionada por el dispositivo que se utiliza para proporcionar almacenamiento de respaldo para páginas de estructura. Para cada espacio de nombres, el altmap debe pertenecer a ese mismo espacio de nombres. Si los espacios de nombres se crean sin alinear, existe la posibilidad de que la dirección de inicio de la sección vmemmap también esté desalineada. Si la dirección de inicio de la sección vmemmap no está alineada, la página altmap asignada desde el espacio de nombres actual también podría ser utilizada por el espacio de nombres anterior. Durante la operación de liberación, dado que el altmap se comparte entre dos espacios de nombres, el espacio de nombres anterior puede detectar que la página no pertenece a su altmap y asumir incorrectamente que la página es una página normal. Entonces intenta liberar la página normal, lo que provoca un fallo del kernel. El kernel intentó leer la página del usuario (18): ¿intento de explotación? (uid: 0) ERROR: Desreferencia de puntero NULL del kernel en lectura en 0x00000018 Dirección de instrucción con errores: 0xc000000000530c7c Oops: Acceso del kernel al área incorrecta, sig: 11 [#1] LE PAGE_SIZE=64K MMU=Radix SMP NR_CPUS=2048 NUMA pSeries CPU: 32 PID: 2104 Comm: ndctl Kdump: cargado Contaminado: GW NIP: c000000000530c7c LR: c000000000530e00 CTR: 0000000000007ffe REGS: c000000015e57040 TRAP: 0300 Contaminado: GW MSR: 800000000280b033  CR: 84482404 CFAR: c000000000530dfc DAR: 00000000000000018 DSISR: 40000000 IRQMASK: 0 GPR00: c000000000530e00 c000000015e572e0 c000000002c5cb00 c00c000101008040 GPR04: 00000000000000000 0000000000000007 0000000000000001 0000000000000001f GPR08: 0000000000000005 0000000000000000 0000000000000018 0000000000002000 GPR12: c0000000001d2fb0 c0000060de6b0080 0000000000000000 c0000060dbf90020 GPR16: c00c000101008000 0000000000000001 000000000000000 c000000125b20f00 GPR20: 0000000000000001 0000000000000000 ffffffffffffffff c00c000101007fff GPR24: 0000000000000001 0000000000000000 0000000000000000 0000000000000000 GPR28: 0000000004040201 000000000000001 000000000000000 c00c000101008040 NIP [c000000000530c7c] máscara_obtener_indicadores_pfnblock+0x7c/0xd0 LR [c000000000530e00] free_unref_page_prepare+0x130/0x4f0 Rastreo de llamadas: free_unref_page+0x50/0x1e0 free_reserved_page+0x40/0x68 free_vmemmap_pages+0x98/0xe0 remove_pte_table+0x164/0x1e8 remove_pmd_table+0x204/0x2c8 remove_pud_table+0x1c4/0x288 remove_pagetable+0x1c8/0x310 vmemmap_free+0x24/0x50 section_deactivate+0x28c/0x2a0 __remove_pages+0x84/0x110 arch_remove_memory+0x38/0x60 Otro problema es que si no hay un altmap, se asignará una página vmemmap del tamaño de PMD desde la RAM, independientemente de la alineación de la dirección de inicio de la sección. Si la dirección de inicio de la sección no está alineada con el tamaño de PMD, se activará un error VM_BUG_ON al configurar la página de tamaño PMD en la tabla de páginas. En esta revisión, estamos alineando la dirección de inicio de vmemmap de la sección con PAGE_SIZE. Después de la alineación, la dirección de inicio no formará parte del espacio de nombres actual y se asignará una página normal para la asignación de vmemmap de la sección actual. Para las secciones restantes, se asignarán mapas alternativos. Durante la operación de liberación, la página normal se liberará correctamente. De la misma manera, se asignará una página vmemmap PMD_SIZE solo si la dirección de inicio de la sección está alineada con PMD_SIZE; de lo contrario, se recurrirá a una asignación de vmemmap de tamaño PAGE. Sin esta revisión ===================== Inicio de NS1 Inicio de NS2 _________________________________________________________ | NS1 | NS2 | --------------------------------------------------------- | Altmap| Altmap | .....|Altmap| Altmap | ........... | NS1 | NS1 ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/400be767deaf31a073c6d14c5d151ae5ac2a60e2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f5476d80f2cb364701cd1fa138a14b241ca99e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9a8d4d7072d4df108479b1adc4b0840e96f6f61d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9cf7e13fecbab0894f6986fc6986ab2eba8de52e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
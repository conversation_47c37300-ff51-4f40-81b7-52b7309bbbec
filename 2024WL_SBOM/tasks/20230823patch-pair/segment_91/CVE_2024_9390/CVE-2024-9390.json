{"cve_id": "CVE-2024-9390", "published_date": "2025-05-15T20:16:00.557", "last_modified_date": "2025-06-04T20:07:00.193", "descriptions": [{"lang": "en", "value": "The RegistrationMagic  WordPress plugin before ******* does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento RegistrationMagic para WordPress anterior a la versión ******* no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/6a5308fb-83bf-4f6a-a7ef-e3e1b69aa80f/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
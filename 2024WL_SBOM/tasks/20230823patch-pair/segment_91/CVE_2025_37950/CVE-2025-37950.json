{"cve_id": "CVE-2025-37950", "published_date": "2025-05-20T16:15:33.107", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nocfs2: fix panic in failed foilio allocation\n\ncommit 7e119cff9d0a (\"ocfs2: convert w_pages to w_folios\") and commit\n9a5e08652dc4b (\"ocfs2: use an array of folios instead of an array of\npages\") save -ENOMEM in the folio array upon allocation failure and call\nthe folio array free code.\n\nThe folio array free code expects either valid folio pointers or NULL. \nFinding the -ENOMEM will result in a panic.  Fix by NULLing the error\nfolio entry."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ocfs2: Se corrige el pánico en la asignación fallida de foilio, commit 7e119cff9d0a (\"ocfs2: convertir w_pages a w_folios\") y commit 9a5e08652dc4b (\"ocfs2: usar un array de folios en lugar de un array de páginas\"). Se guarda -ENOMEM en el array de folios tras un fallo de asignación y se llama al código de liberación del array de folios. Este código espera punteros de folio válidos o NULL. Encontrar -ENOMEM provocará un pánico. Se corrige anulando la entrada de folio con error."}], "references": [{"url": "https://git.kernel.org/stable/c/31d4cd4eb2f8d9b87ebfa6a5e443a59e3b3d7b8c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/80d18f060d5bdf2c5eb3d1d00dcb744d6a879222", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-9599", "published_date": "2025-05-15T20:16:00.757", "last_modified_date": "2025-06-04T20:06:33.623", "descriptions": [{"lang": "en", "value": "The Popup Box  WordPress plugin before 4.7.8 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Popup Box de WordPress anterior a la versión 4.7.8 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/9e8a2659-7a6c-4528-b0b2-64d462485b43/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37968", "published_date": "2025-05-20T17:15:47.250", "last_modified_date": "2025-05-22T13:15:56.220", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niio: light: opt3001: fix deadlock due to concurrent flag access\n\nThe threaded IRQ function in this driver is reading the flag twice: once to\nlock a mutex and once to unlock it. Even though the code setting the flag\nis designed to prevent it, there are subtle cases where the flag could be\ntrue at the mutex_lock stage and false at the mutex_unlock stage. This\nresults in the mutex not being unlocked, resulting in a deadlock.\n\nFix it by making the opt3001_irq() code generally more robust, reading the\nflag into a variable and using the variable value at both stages."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iio: light: opt3001: corrección de interbloqueo debido al acceso simultáneo a indicadores. La función IRQ enhebrada de este controlador lee el indicador dos veces: una para bloquear un mutex y otra para desbloquearlo. Aunque el código que configura el indicador está diseñado para evitarlo, existen casos sutiles en los que el indicador podría ser verdadero en la etapa mutex_lock y falso en la etapa mutex_unlock. Esto provoca que el mutex no se desbloquee, lo que genera un interbloqueo. Para solucionarlo, haga que el código opt3001_irq() sea generalmente más robusto, leyendo el indicador en una variable y utilizando el valor de la variable en ambas etapas."}], "references": [{"url": "https://git.kernel.org/stable/c/2c95c8f0959d0a72575eabf2ff888f47ed6d8b77", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7ca84f6a22d50bf8b31efe9eb05f9859947266d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f063a28002e3350088b4577c5640882bf4ea17ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-3516", "published_date": "2025-05-16T06:15:46.343", "last_modified_date": "2025-05-22T17:03:25.230", "descriptions": [{"lang": "en", "value": "The Simple Lightbox WordPress plugin before 2.9.4 does not validate and escape some of its attributes before outputting them back in a page/post, which could allow users with the contributor role and above to perform Stored Cross-Site Scripting attacks."}, {"lang": "es", "value": "El complemento Simple Lightbox de WordPress anterior a la versión 2.9.4 no valida ni escapa algunos de sus atributos antes de mostrarlos nuevamente en una página/publicación, lo que podría permitir a los usuarios con rol de colaborador y superior realizar ataques de Cross-Site Scripting almacenado."}], "references": [{"url": "https://wpscan.com/vulnerability/336a78cd-297b-4f47-a007-e33eac7f1dad/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-37913", "published_date": "2025-05-20T16:15:27.837", "last_modified_date": "2025-06-04T13:15:26.490", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnet_sched: qfq: Fix double list add in class with netem as child qdisc\n\nAs described in <PERSON><PERSON><PERSON><PERSON>'s report [1], there are use cases where a netem\nchild qdisc will make the parent qdisc's enqueue callback reentrant.\nIn the case of qfq, there won't be a UAF, but the code will add the same\nclassifier to the list twice, which will cause memory corruption.\n\nThis patch checks whether the class was already added to the agg->active\nlist (cl_is_active) before doing the addition to cater for the reentrant\ncase.\n\n[1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: net_sched: qfq: Se corrige la adición doble de lista en una clase con netem como qdisc secundaria. Como se describe en el informe de Gerrard [1], existen casos de uso en los que una qdisc secundaria netem hará que la devolución de llamada de encolado de la qdisc primaria sea reentrante. En el caso de qfq, no habrá un UAF, pero el código agregará el mismo clasificador a la lista dos veces, lo que causará corrupción de memoria. Este parche verifica si la clase ya se agregó a la lista agg-&gt;active (cl_is_active) antes de realizar la adición para atender el caso reentrante. [1] https://lore.kernel.org/netdev/CAHcdcOm+03OD2j6R0=YHKqmy=<EMAIL>/"}], "references": [{"url": "https://git.kernel.org/stable/c/005a479540478a820c52de098e5e767e63e36f0a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/041f410aec2c1751ee22b8b73ba05d38c3a6a602", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0aa23e0856b7cedb3c88d8e3d281c212c7e4fbeb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0bf32d6fb1fcbf841bb9945570e0e2a70072c00f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/370218e8ce711684acc4cdd3cc3c6dd7956bc165", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/53bc0b55178bd59bdd4bcd16349505cabf54b1a2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a43783119e01849fbf2fe8855634e8989b240cb4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f139f37dcdf34b67f5bf92bc8e0f7f6b3ac63aa4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
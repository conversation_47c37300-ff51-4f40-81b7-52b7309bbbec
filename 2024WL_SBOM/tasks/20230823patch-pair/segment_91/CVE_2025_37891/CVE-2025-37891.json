{"cve_id": "CVE-2025-37891", "published_date": "2025-05-19T08:15:21.710", "last_modified_date": "2025-05-19T13:35:20.460", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nALSA: ump: Fix buffer overflow at UMP SysEx message conversion\n\nThe conversion function from MIDI 1.0 to UMP packet contains an\ninternal buffer to keep the incoming MIDI bytes, and its size is 4, as\nit was supposed to be the max size for a MIDI1 UMP packet data.\nHowever, the implementation overlooked that SysEx is handled in a\ndifferent format, and it can be up to 6 bytes, as found in\ndo_convert_to_ump().  It leads eventually to a buffer overflow, and\nmay corrupt the memory when a longer SysEx message is received.\n\nThe fix is simply to extend the buffer size to 6 to fit with the SysEx\nUMP message."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ALSA: ump: Se corrige el desbordamiento del búfer en la conversión de mensajes SysEx de UMP. La función de conversión de paquetes MIDI 1.0 a UMP contiene un búfer interno para almacenar los bytes MIDI entrantes, y su tamaño es de 4, ya que se suponía que era el tamaño máximo para los datos de un paquete MIDI1 UMP. Sin embargo, la implementación pasó por alto que SysEx se maneja en un formato diferente, y puede tener hasta 6 bytes, como se encuentra en do_convert_to_ump(). Eventualmente conduce a un desbordamiento del búfer y puede dañar la memoria cuando se recibe un mensaje SysEx más largo. La solución es simplemente extender el tamaño del búfer a 6 para que se ajuste al mensaje SysEx UMP."}], "references": [{"url": "https://git.kernel.org/stable/c/226beac5605afbb33f8782148d188b64396145a4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/42ef48dd4ebb082a1a90b5c3feeda2e68a9e32fe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/56f1f30e6795b890463d9b20b11e576adf5a2f77", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ce4f77bef276e7d2eb7ab03a5d08bcbaa40710ec", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
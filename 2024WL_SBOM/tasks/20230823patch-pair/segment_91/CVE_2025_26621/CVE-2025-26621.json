{"cve_id": "CVE-2025-26621", "published_date": "2025-05-19T16:15:28.560", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "OpenCTI is an open source platform for managing cyber threat intelligence knowledge and observables. Prior to version 6.5.2, any user with the capability manage customizations can edit webhook that will execute javascript code. This can be abused to cause a denial of service attack by prototype pollution, making the node js server running the OpenCTI frontend become unavailable. Version 6.5.2 fixes the issue."}, {"lang": "es", "value": "OpenCTI es una plataforma de código abierto para gestionar el conocimiento y los observables de inteligencia sobre ciberamenazas. Antes de la versión 6.5.2, cualquier usuario con la capacidad de gestionar personalizaciones podía editar un webhook que ejecutaría código JavaScript. Esto puede utilizarse para provocar un ataque de denegación de servicio mediante la contaminación del prototipo, lo que hace que el servidor Node.js que ejecuta el frontend de OpenCTI deje de estar disponible. La versión 6.5.2 soluciona este problema."}], "references": [{"url": "https://github.com/OpenCTI-Platform/opencti/security/advisories/GHSA-gq63-jm3h-374p", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/OpenCTI-Platform/opencti/security/advisories/GHSA-mf88-g2wq-p7qm", "source": "<EMAIL>", "tags": []}]}
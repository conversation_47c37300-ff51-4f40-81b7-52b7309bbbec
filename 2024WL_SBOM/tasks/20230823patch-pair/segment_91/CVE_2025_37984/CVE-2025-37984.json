{"cve_id": "CVE-2025-37984", "published_date": "2025-05-20T18:15:45.253", "last_modified_date": "2025-07-25T14:15:31.667", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncrypto: ecdsa - Harden against integer overflows in DIV_ROUND_UP()\n\n<PERSON> notes that DIV_ROUND_UP() may overflow unnecessarily if an ecdsa\nimplementation's ->key_size() callback returns an unusually large value.\n<PERSON> instead suggests (for a division by 8):\n\n  X / 8 + !!(X & 7)\n\nBased on this formula, introduce a generic DIV_ROUND_UP_POW2() macro and\nuse it in lieu of DIV_ROUND_UP() for ->key_size() return values.\n\nAdditionally, use the macro in ecc_digits_from_bytes(), whose \"nbytes\"\nparameter is a ->key_size() return value in some instances, or a\nuser-specified ASN.1 length in the case of ecdsa_get_signature_rs()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: crypto: ecdsa - Protección contra desbordamientos de enteros en DIV_ROUND_UP(). <PERSON> señala que DIV_ROUND_UP() puede desbordarse innecesariamente si la función de retorno -&gt;key_size() de una implementación de ecdsa devuelve un valor inusualmente grande. En su lugar, <PERSON> (para una división entre 8): X / 8 + !!(X y 7). Con base en esta fórmula, introduzca una macro genérica DIV_ROUND_UP_POW2() y úsela en lugar de DIV_ROUND_UP() para los valores de retorno de -&gt;key_size(). Además, utilice la macro en ecc_digits_from_bytes(), cuyo parámetro \"nbytes\" es un valor de retorno de -&gt;key_size() en algunos casos, o una longitud ASN.1 especificada por el usuario en el caso de ecdsa_get_signature_rs()."}], "references": [{"url": "https://git.kernel.org/stable/c/921b8167f10708e38080f84e195cdc68a7a561f1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b16510a530d1e6ab9683f04f8fb34f2e0f538275", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f02f0218be412cff1c844addf58e002071be298b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f2133b849ff273abddb6da622daddd8f6f6fa448", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
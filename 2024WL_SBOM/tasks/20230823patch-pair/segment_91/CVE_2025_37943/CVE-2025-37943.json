{"cve_id": "CVE-2025-37943", "published_date": "2025-05-20T16:15:32.133", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: ath12k: Fix invalid data access in ath12k_dp_rx_h_undecap_nwifi\n\nIn certain cases, hardware might provide packets with a\nlength greater than the maximum native Wi-Fi header length.\nThis can lead to accessing and modifying fields in the header\nwithin the ath12k_dp_rx_h_undecap_nwifi function for\nDP_RX_DECAP_TYPE_NATIVE_WIFI decap type and\npotentially resulting in invalid data access and memory corruption.\n\nAdd a sanity check before processing the SKB to prevent invalid\ndata access in the undecap native Wi-Fi function for the\nDP_RX_DECAP_TYPE_NATIVE_WIFI decap type.\n\nTested-on: QCN9274 hw2.0 PCI WLAN.WBE.1.3.1-00173-QCAHKSWPL_SILICONZ-1"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: ath12k: Se ha corregido el acceso no válido a datos en ath12k_dp_rx_h_undecap_nwifi. En ciertos casos, el hardware podría proporcionar paquetes con una longitud superior a la longitud máxima del encabezado Wi-Fi nativo. Esto puede provocar el acceso y la modificación de campos del encabezado dentro de la función ath12k_dp_rx_h_undecap_nwifi para el tipo de decap DP_RX_DECAP_TYPE_NATIVE_WIFI, lo que podría provocar acceso no válido a datos y corrupción de memoria. Se ha añadido una comprobación de seguridad antes de procesar el SKB para evitar el acceso no válido a datos en la función Wi-Fi nativa undecap para el tipo de decap DP_RX_DECAP_TYPE_NATIVE_WIFI. Probado en: QCN9274 hw2.0 PCI WLAN.WBE.1.3.1-00173-QCAHKSWPL_SILICONZ-1."}], "references": [{"url": "https://git.kernel.org/stable/c/3abe15e756481c45f6acba3d476cb3ca4afc3b61", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/50be1fb76556e80af9f5da80f28168b6c71bce58", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ee653194ddb83674913fd2727b8ecfae0597ade", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f1d986da5c6abb75ffe4d0d325fc9b341c41a1c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9a0dddfb30f120db3851627935851d262e4e7acb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
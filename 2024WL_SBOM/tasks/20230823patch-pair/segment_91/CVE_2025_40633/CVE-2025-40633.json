{"cve_id": "CVE-2025-40633", "published_date": "2025-05-20T11:15:48.630", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "A Stored Cross-Site Scripting (XSS) vulnerability has been found in \nKoibox for versions prior to e8cbce2. This vulnerability allows an \nauthenticated attacker to upload an image containing malicious \nJavaScript code as profile picture in the \n'/es/dashboard/clientes/ficha/' endpoint"}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad de Cross-Site Scripting (XSS) Almacenado en Koibox para versiones anteriores a e8cbce2. Esta vulnerabilidad permite a un atacante autenticado cargar una imagen con código JavaScript malicioso como foto de perfil en el endpoint '/es/dashboard/clientes/ficha/'."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/stored-cross-site-scripting-xss-koibox", "source": "<EMAIL>", "tags": []}]}
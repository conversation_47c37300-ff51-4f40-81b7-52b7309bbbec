{"cve_id": "CVE-2025-37932", "published_date": "2025-05-20T16:15:29.817", "last_modified_date": "2025-06-04T13:15:27.210", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsch_htb: make htb_qlen_notify() idempotent\n\nhtb_qlen_notify() always deactivates the HTB class and in fact could\ntrigger a warning if it is already deactivated. Therefore, it is not\nidempotent and not friendly to its callers, like fq_codel_dequeue().\n\nLet's make it idempotent to ease qdisc_tree_reduce_backlog() callers'\nlife."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: sch_htb: hacer idempotente a htb_qlen_notify(). htb_qlen_notify() siempre desactiva la clase HTB y, de hecho, podría generar una advertencia si ya está desactivada. Por lo tanto, no es idempotente ni incompatible con quienes la llaman, como fq_codel_dequeue(). Hagámosla idempotente para facilitar la tarea a quienes llaman a qdisc_tree_reduce_backlog()."}], "references": [{"url": "https://git.kernel.org/stable/c/0a188c0e197383683fd093ab1ea6ce9a5869a6ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5ba8b837b522d7051ef81bacf3d95383ff8edce5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/73cf6af13153d62f9b76eff422eea79dbc70f15e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a61f1b5921761fbaf166231418bc1db301e5bf59", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bbbf5e0f87078b715e7a665d662a2c0e77f044ae", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e6b45f4de763b00dc1c55e685e2dd1aaf525d3c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-37983", "published_date": "2025-05-20T18:15:45.137", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nqibfs: fix _another_ leak\n\nfailure to allocate inode => leaked dentry...\n\nthis one had been there since the initial merge; to be fair,\nif we are that far OOM, the odds of failing at that particular\nallocation are low..."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: qibfs: corrige _otro_ fallo de fuga en la asignación de inodo =&gt; dentry filtrado... este había estado allí desde la fusión inicial; para ser justos, si estamos tan lejos de OOM, las probabilidades de fallar en esa asignación en particular son bajas..."}], "references": [{"url": "https://git.kernel.org/stable/c/24faa6ea274a2b96d0a78a0996c3137c2b2a65f0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3c2fde33e3e505dfd1a895d1f24bad650c655e14", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/47ab2caba495c1d6a899d284e541a8df656dcfe9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/545defa656568c74590317cd30068f85134a8216", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5d53e88d8370b9ab14dd830abb410d9a2671edb6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5e280cce3a29b7fe7b828c6ccd5aa5ba87ceb6b6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5fe708c5e3c8b2152c6caaa67243e431a5d6cca3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/bdb43af4fdb39f844ede401bdb1258f67a580a27", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-32407", "published_date": "2025-05-16T21:15:35.093", "last_modified_date": "2025-06-12T16:30:02.260", "descriptions": [{"lang": "en", "value": "Samsung Internet for Galaxy Watch version 5.0.9, available up until Samsung Galaxy Watch 3, does not properly validate TLS certificates, allowing for an attacker to impersonate any and all websites visited by the user. This is a critical misconfiguration in the way the browser validates the identity of the server. It negates the use of HTTPS as a secure channel, allowing for Man-in-the-Middle attacks, stealing sensitive information or modifying incoming and outgoing traffic. NOTE: This vulnerability is in an end-of-life product that is no longer maintained by the vendor."}, {"lang": "es", "value": "Samsung Internet para Galaxy Watch versión 5.0.9, disponible hasta el Samsung Galaxy Watch 3, no valida correctamente los certificados TLS, lo que permite a un atacante suplantar la identidad de todos los sitios web visitados por el usuario. Esto representa un error crítico en la forma en que el navegador valida la identidad del servidor. Impide el uso de HTTPS como canal seguro, lo que permite ataques de intermediario (Man-in-the-Middle), el robo de información confidencial o la modificación del tráfico entrante y saliente. NOTA: Esta vulnerabilidad se encuentra en un producto al final de su vida útil que ya no recibe mantenimiento del proveedor."}], "references": [{"url": "https://github.com/diegovargasj/CVE-2025-32407", "source": "<EMAIL>", "tags": ["Exploit"]}]}
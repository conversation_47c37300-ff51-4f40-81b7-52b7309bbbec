{"cve_id": "CVE-2025-37947", "published_date": "2025-05-20T16:15:32.677", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nksmbd: prevent out-of-bounds stream writes by validating *pos\n\nksmbd_vfs_stream_write() did not validate whether the write offset\n(*pos) was within the bounds of the existing stream data length (v_len).\nIf *pos was greater than or equal to v_len, this could lead to an\nout-of-bounds memory write.\n\nThis patch adds a check to ensure *pos is less than v_len before\nproceeding. If the condition fails, -EINVAL is returned."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ksmbd: impide escrituras de flujo fuera de los límites mediante la validación de *pos. ksmbd_vfs_stream_write() no validaba si el desplazamiento de escritura (*pos) estaba dentro de los límites de la longitud de datos del flujo existente (v_len). Si *pos era mayor o igual que v_len, esto podía provocar una escritura en memoria fuera de los límites. Este parche añade una comprobación para garantizar que *pos sea menor que v_len antes de continuar. Si la condición falla, se devuelve -EINVAL."}], "references": [{"url": "https://git.kernel.org/stable/c/04c8a38c60346bb5a7c49b276de7233f703ce9cb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0ca6df4f40cf4c32487944aaf48319cb6c25accc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7f61da79df86fd140c7768e668ad846bfa7ec8e1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d62ba16563a86aae052f96d270b3b6f78fca154c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e6356499fd216ed6343ae0363f4c9303f02c5034", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
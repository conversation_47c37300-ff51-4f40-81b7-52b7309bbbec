{"cve_id": "CVE-2025-32962", "published_date": "2025-05-16T14:15:31.190", "last_modified_date": "2025-05-16T14:42:18.700", "descriptions": [{"lang": "en", "value": "Flask-AppBuilder is an application development framework built on top of Flask. Versions prior to 4.6.2 would allow for a malicious unauthenticated actor to perform an open redirect by manipulating the Host header in HTTP requests. Flask-AppBuilder 4.6.2 introduced the `FAB_SAFE_REDIRECT_HOSTS` configuration variable, which allows administrators to explicitly define which domains are considered safe for redirection. As a workaround, use a reverse proxy to enforce trusted host headers."}, {"lang": "es", "value": "Flask-AppBuilder es un framework de desarrollo de aplicaciones basado en Flask. Las versiones anteriores a la 4.6.2 permitían que un agente malicioso no autenticado realizara una redirección abierta manipulando el encabezado Host en las solicitudes HTTP. Flask-AppBuilder 4.6.2 introdujo la variable de configuración `FAB_SAFE_REDIRECT_HOSTS`, que permite a los administradores definir explícitamente qué dominios se consideran seguros para la redirección. Como workaround, utilice un proxy inverso para aplicar encabezados de host de confianza."}], "references": [{"url": "https://github.com/dpgaspar/Flask-AppBuilder/commit/32eedbbb5cb483a3e782c5f2732de4a6a650d9b6", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/dpgaspar/Flask-AppBuilder/security/advisories/GHSA-99pm-ch96-ccp2", "source": "<EMAIL>", "tags": []}]}
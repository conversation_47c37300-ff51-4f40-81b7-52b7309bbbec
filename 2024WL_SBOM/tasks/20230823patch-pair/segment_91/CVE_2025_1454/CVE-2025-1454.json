{"cve_id": "CVE-2025-1454", "published_date": "2025-05-15T20:16:02.467", "last_modified_date": "2025-06-12T16:38:53.070", "descriptions": [{"lang": "en", "value": "The Ninja Pages WordPress plugin through 1.4.2 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Ninja Pages para WordPress hasta la versión 1.4.2 no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados como el administrador realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/0089f813-82fa-4ffc-acd6-a70e67edc8ea/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
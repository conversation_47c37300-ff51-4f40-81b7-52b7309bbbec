{"cve_id": "CVE-2025-37901", "published_date": "2025-05-20T16:15:26.453", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nirqchip/qcom-mpm: Prevent crash when trying to handle non-wake GPIOs\n\nOn Qualcomm chipsets not all GPIOs are wakeup capable. Those GPIOs do not\nhave a corresponding MPM pin and should not be handled inside the MPM\ndriver. The IRQ domain hierarchy is always applied, so it's required to\nexplicitly disconnect the hierarchy for those. The pinctrl-msm driver marks\nthese with GPIO_NO_WAKE_IRQ. qcom-pdc has a check for this, but\nirq-qcom-mpm is currently missing the check. This is causing crashes when\nsetting up interrupts for non-wake GPIOs:\n\n root@rb1:~# gpiomon -c gpiochip1 10\n   irq: IRQ159: trimming hierarchy from :soc@0:interrupt-controller@f200000-1\n   Unable to handle kernel paging request at virtual address ffff8000a1dc3820\n   Hardware name: Qualcomm Technologies, Inc. Robotics RB1 (DT)\n   pc : mpm_set_type+0x80/0xcc\n   lr : mpm_set_type+0x5c/0xcc\n   Call trace:\n    mpm_set_type+0x80/0xcc (P)\n    qcom_mpm_set_type+0x64/0x158\n    irq_chip_set_type_parent+0x20/0x38\n    msm_gpio_irq_set_type+0x50/0x530\n    __irq_set_trigger+0x60/0x184\n    __setup_irq+0x304/0x6bc\n    request_threaded_irq+0xc8/0x19c\n    edge_detector_setup+0x260/0x364\n    linereq_create+0x420/0x5a8\n    gpio_ioctl+0x2d4/0x6c0\n\nFix this by copying the check for GPIO_NO_WAKE_IRQ from qcom-pdc.c, so that\nMPM is removed entirely from the hierarchy for non-wake GPIOs."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: irqchip/qcom-mpm: Evita un bloqueo al intentar gestionar GPIO sin activación. En los chipsets Qualcomm, no todos los GPIO tienen capacidad de activación. Estos GPIO no tienen un pin MPM correspondiente y no deben gestionarse dentro del controlador MPM. La jerarquía del dominio IRQ siempre se aplica, por lo que es necesario desconectarla explícitamente. El controlador pinctrl-msm los marca con GPIO_NO_WAKE_IRQ. qcom-pdc cuenta con una comprobación para esto, pero irq-qcom-mpm actualmente no la tiene. Esto está causando fallos al configurar interrupciones para GPIO que no son de activación: root@rb1:~# gpiomon -c gpiochip1 10 irq: IRQ159: recortar jerarquía de :soc@0:interrupt-controller@f200000-1 No se puede manejar la solicitud de paginación del núcleo en la dirección virtual ffff8000a1dc3820 Nombre del hardware: Qualcomm Technologies, Inc. Robotics RB1 (DT) pc: mpm_set_type+0x80/0xcc lr: mpm_set_type+0x5c/0xcc Rastreo de llamadas: mpm_set_type+0x80/0xcc (P) qcom_mpm_set_type+0x64/0x158 irq_chip_set_type_parent+0x20/0x38 msm_gpio_irq_set_type+0x50/0x530 Solucione esto copiando la comprobación de GPIO_NO_WAKE_IRQ desde qcom-pdc.c, de modo que MPM se elimine por completo de la jerarquía para los GPIO que no son de activación."}], "references": [{"url": "https://git.kernel.org/stable/c/38a05c0b87833f5b188ae43b428b1f792df2b384", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/45aced97f01d5ab14c8a2a60f6748f18c501c3f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d5c10448f411a925dd59005785cb971f0626e032", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dfbaecf7e38f5e9bfa5e47a1e525ffbb58bab8cf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f102342360950b56959e5fff4a874ea88ae13758", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-0133", "published_date": "2025-05-14T19:15:51.517", "last_modified_date": "2025-05-16T14:43:56.797", "descriptions": [{"lang": "en", "value": "A reflected cross-site scripting (XSS) vulnerability in the GlobalProtect™ gateway and portal features of Palo Alto Networks PAN-OS® software enables execution of malicious JavaScript in the context of an authenticated Captive Portal user's browser when they click on a specially crafted link. The primary risk is phishing attacks that can lead to credential theft—particularly if you enabled Clientless VPN.\n\nThere is no availability impact to GlobalProtect features or GlobalProtect users. Attackers cannot use this vulnerability to tamper with or modify contents or configurations of the GlobalProtect portal or gateways. The integrity impact of this vulnerability is limited to enabling an attacker to create phishing and credential-stealing links that appear to be hosted on the GlobalProtect portal.\n\n\n\nFor GlobalProtect users with Clientless VPN enabled, there is a limited impact on confidentiality due to inherent risks of Clientless VPN that facilitate credential theft. You can read more about this risk in the informational bulletin  PAN-SA-2025-0005 https://security.paloaltonetworks.com/PAN-SA-2025-0005   https://security.paloaltonetworks.com/PAN-SA-2025-0005 . There is no impact to confidentiality for GlobalProtect users if you did not enable (or you disable) Clientless VPN."}, {"lang": "es", "value": "Una vulnerabilidad cross-site scripting (XSS) reflejado en las funciones de portal y puerta de enlace de GlobalProtect™ del software PAN-OS® de Palo Alto Networks permite la ejecución de JavaScript malicioso en el navegador de un usuario autenticado de un portal cautivo al hacer clic en un enlace especialmente manipulado. El principal riesgo son los ataques de phishing que pueden provocar el robo de credenciales, especialmente si se ha habilitado la VPN sin cliente. No se ha visto afectada la disponibilidad de las funciones ni los usuarios de GlobalProtect. Los atacantes no pueden usar esta vulnerabilidad para manipular o modificar el contenido o la configuración del portal o las puertas de enlace de GlobalProtect. El impacto en la integridad de esta vulnerabilidad se limita a permitir que un atacante cree enlaces de phishing y robo de credenciales que parecen estar alojados en el portal de GlobalProtect. Para los usuarios de GlobalProtect con la VPN sin cliente habilitada, el impacto en la confidencialidad es limitado debido a los riesgos inherentes de la VPN sin cliente que facilitan el robo de credenciales. Puede obtener más información sobre este riesgo en el boletín informativo PAN-SA-2025-0005 (https://security.paloaltonetworks.com/PAN-SA-2025-0005). La confidencialidad de los usuarios de GlobalProtect no se ve afectada si no habilita (o deshabilita) la VPN sin cliente."}], "references": [{"url": "https://security.paloaltonetworks.com/CVE-2025-0133", "source": "<EMAIL>", "tags": []}]}
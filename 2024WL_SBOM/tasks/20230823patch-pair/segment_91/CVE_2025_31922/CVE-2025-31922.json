{"cve_id": "CVE-2025-31922", "published_date": "2025-05-16T16:15:38.073", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in QuanticaLabs CSS3 Accordions for WordPress allows Stored XSS. This issue affects CSS3 Accordions for WordPress: from n/a through 3.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en QuanticaLabs CSS3 Accordions for WordPress permite XSS almacenados. Este problema afecta a los acordeones CSS3 para WordPress desde la versión n/d hasta la 3.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/css3_accordions/vulnerability/wordpress-css3-accordions-for-wordpress-plugin-3-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
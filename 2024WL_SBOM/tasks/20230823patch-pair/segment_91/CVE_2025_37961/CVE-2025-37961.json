{"cve_id": "CVE-2025-37961", "published_date": "2025-05-20T16:15:34.367", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipvs: fix uninit-value for saddr in do_output_route4\n\nsyzbot reports for uninit-value for the saddr argument [1].\ncommit 4754957f04f5 (\"ipvs: do not use random local source address for\ntunnels\") already implies that the input value of saddr\nshould be ignored but the code is still reading it which can prevent\nto connect the route. Fix it by changing the argument to ret_saddr.\n\n[1]\nBUG: KMSAN: uninit-value in do_output_route4+0x42c/0x4d0 net/netfilter/ipvs/ip_vs_xmit.c:147\n do_output_route4+0x42c/0x4d0 net/netfilter/ipvs/ip_vs_xmit.c:147\n __ip_vs_get_out_rt+0x403/0x21d0 net/netfilter/ipvs/ip_vs_xmit.c:330\n ip_vs_tunnel_xmit+0x205/0x2380 net/netfilter/ipvs/ip_vs_xmit.c:1136\n ip_vs_in_hook+0x1aa5/0x35b0 net/netfilter/ipvs/ip_vs_core.c:2063\n nf_hook_entry_hookfn include/linux/netfilter.h:154 [inline]\n nf_hook_slow+0xf7/0x400 net/netfilter/core.c:626\n nf_hook include/linux/netfilter.h:269 [inline]\n __ip_local_out+0x758/0x7e0 net/ipv4/ip_output.c:118\n ip_local_out net/ipv4/ip_output.c:127 [inline]\n ip_send_skb+0x6a/0x3c0 net/ipv4/ip_output.c:1501\n udp_send_skb+0xfda/0x1b70 net/ipv4/udp.c:1195\n udp_sendmsg+0x2fe3/0x33c0 net/ipv4/udp.c:1483\n inet_sendmsg+0x1fc/0x280 net/ipv4/af_inet.c:851\n sock_sendmsg_nosec net/socket.c:712 [inline]\n __sock_sendmsg+0x267/0x380 net/socket.c:727\n ____sys_sendmsg+0x91b/0xda0 net/socket.c:2566\n ___sys_sendmsg+0x28d/0x3c0 net/socket.c:2620\n __sys_sendmmsg+0x41d/0x880 net/socket.c:2702\n __compat_sys_sendmmsg net/compat.c:360 [inline]\n __do_compat_sys_sendmmsg net/compat.c:367 [inline]\n __se_compat_sys_sendmmsg net/compat.c:364 [inline]\n __ia32_compat_sys_sendmmsg+0xc8/0x140 net/compat.c:364\n ia32_sys_call+0x3ffa/0x41f0 arch/x86/include/generated/asm/syscalls_32.h:346\n do_syscall_32_irqs_on arch/x86/entry/syscall_32.c:83 [inline]\n __do_fast_syscall_32+0xb0/0x110 arch/x86/entry/syscall_32.c:306\n do_fast_syscall_32+0x38/0x80 arch/x86/entry/syscall_32.c:331\n do_SYSENTER_32+0x1f/0x30 arch/x86/entry/syscall_32.c:369\n entry_SYSENTER_compat_after_hwframe+0x84/0x8e\n\nUninit was created at:\n slab_post_alloc_hook mm/slub.c:4167 [inline]\n slab_alloc_node mm/slub.c:4210 [inline]\n __kmalloc_cache_noprof+0x8fa/0xe00 mm/slub.c:4367\n kmalloc_noprof include/linux/slab.h:905 [inline]\n ip_vs_dest_dst_alloc net/netfilter/ipvs/ip_vs_xmit.c:61 [inline]\n __ip_vs_get_out_rt+0x35d/0x21d0 net/netfilter/ipvs/ip_vs_xmit.c:323\n ip_vs_tunnel_xmit+0x205/0x2380 net/netfilter/ipvs/ip_vs_xmit.c:1136\n ip_vs_in_hook+0x1aa5/0x35b0 net/netfilter/ipvs/ip_vs_core.c:2063\n nf_hook_entry_hookfn include/linux/netfilter.h:154 [inline]\n nf_hook_slow+0xf7/0x400 net/netfilter/core.c:626\n nf_hook include/linux/netfilter.h:269 [inline]\n __ip_local_out+0x758/0x7e0 net/ipv4/ip_output.c:118\n ip_local_out net/ipv4/ip_output.c:127 [inline]\n ip_send_skb+0x6a/0x3c0 net/ipv4/ip_output.c:1501\n udp_send_skb+0xfda/0x1b70 net/ipv4/udp.c:1195\n udp_sendmsg+0x2fe3/0x33c0 net/ipv4/udp.c:1483\n inet_sendmsg+0x1fc/0x280 net/ipv4/af_inet.c:851\n sock_sendmsg_nosec net/socket.c:712 [inline]\n __sock_sendmsg+0x267/0x380 net/socket.c:727\n ____sys_sendmsg+0x91b/0xda0 net/socket.c:2566\n ___sys_sendmsg+0x28d/0x3c0 net/socket.c:2620\n __sys_sendmmsg+0x41d/0x880 net/socket.c:2702\n __compat_sys_sendmmsg net/compat.c:360 [inline]\n __do_compat_sys_sendmmsg net/compat.c:367 [inline]\n __se_compat_sys_sendmmsg net/compat.c:364 [inline]\n __ia32_compat_sys_sendmmsg+0xc8/0x140 net/compat.c:364\n ia32_sys_call+0x3ffa/0x41f0 arch/x86/include/generated/asm/syscalls_32.h:346\n do_syscall_32_irqs_on arch/x86/entry/syscall_32.c:83 [inline]\n __do_fast_syscall_32+0xb0/0x110 arch/x86/entry/syscall_32.c:306\n do_fast_syscall_32+0x38/0x80 arch/x86/entry/syscall_32.c:331\n do_SYSENTER_32+0x1f/0x30 arch/x86/entry/syscall_32.c:369\n entry_SYSENTER_compat_after_hwframe+0x84/0x8e\n\nCPU: 0 UID: 0 PID: 22408 Comm: syz.4.5165 Not tainted 6.15.0-rc3-syzkaller-00019-gbc3372351d0c #0 PREEMPT(undef)\nHardware name: Google Google Compute Engi\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipvs: corrección de uninit-value para saddr en do_output_route4. syzbot informa de uninit-value para el argumento saddr [1]. el commit 4754957f04f5 (\"ipvs: no usar dirección de origen local aleatoria para túneles\") ya implica que el valor de entrada de saddr debe ignorarse, pero el código sigue leyéndolo, lo que puede impedir la conexión a la ruta. Se puede corregir cambiando el argumento a ret_saddr. [1] ERROR: KMSAN: valor no inicializado en do_output_route4+0x42c/0x4d0 net/netfilter/ipvs/ip_vs_xmit.c:147 do_output_route4+0x42c/0x4d0 net/netfilter/ipvs/ip_vs_xmit.c:147 __ip_vs_get_out_rt+0x403/0x21d0 net/netfilter/ipvs/ip_vs_xmit.c:330 ip_vs_tunnel_xmit+0x205/0x2380 net/netfilter/ipvs/ip_vs_xmit.c:1136 ip_vs_in_hook+0x1aa5/0x35b0 net/netfilter/ipvs/ip_vs_core.c:2063 nf_hook_entry_hookfn include/linux/netfilter.h:154 [en línea] nf_hook_slow+0xf7/0x400 net/netfilter/core.c:626 nf_hook include/linux/netfilter.h:269 [en línea] __ip_local_out+0x758/0x7e0 net/ipv4/ip_output.c:118 ip_local_out net/ipv4/ip_output.c:127 [en línea] ip_send_skb+0x6a/0x3c0 net/ipv4/ip_output.c:1501 udp_send_skb+0xfda/0x1b70 net/ipv4/udp.c:1195 udp_sendmsg+0x2fe3/0x33c0 net/ipv4/udp.c:1483 inet_sendmsg+0x1fc/0x280 net/ipv4/af_inet.c:851 sock_sendmsg_nosec net/socket.c:712 [en línea] __sock_sendmsg+0x267/0x380 net/socket.c:727 ____sys_sendmsg+0x91b/0xda0 net/socket.c:2566 ___sys_sendmsg+0x28d/0x3c0 net/socket.c:2620 __sys_sendmmsg+0x41d/0x880 net/socket.c:2702 __compat_sys_sendmmsg net/compat.c:360 [en línea] __do_compat_sys_sendmmsg net/compat.c:367 [en línea] __se_compat_sys_sendmmsg net/compat.c:364 [en línea] __ia32_compat_sys_sendmmsg+0xc8/0x140 net/compat.c:364 ia32_sys_call+0x3ffa/0x41f0 arch/x86/include/generated/asm/syscalls_32.h:346 do_syscall_32_irqs_on arch/x86/entry/syscall_32.c:83 [en línea] __do_fast_syscall_32+0xb0/0x110 arch/x86/entry/syscall_32.c:306 do_fast_syscall_32+0x38/0x80 arch/x86/entry/syscall_32.c:331 do_SYSENTER_32+0x1f/0x30 arch/x86/entry/syscall_32.c:369 entry_SYSENTER_compat_after_hwframe+0x84/0x8e Uninit se creó en: slab_post_alloc_hook mm/slub.c:4167 [en línea] slab_alloc_node mm/slub.c:4210 [en línea] __kmalloc_cache_noprof+0x8fa/0xe00 mm/slub.c:4367 kmalloc_noprof include/linux/slab.h:905 [en línea] ip_vs_dest_dst_alloc net/netfilter/ipvs/ip_vs_xmit.c:61 [en línea] __ip_vs_get_out_rt+0x35d/0x21d0 net/netfilter/ipvs/ip_vs_xmit.c:323 ip_vs_tunnel_xmit+0x205/0x2380 net/netfilter/ipvs/ip_vs_xmit.c:1136 ip_vs_in_hook+0x1aa5/0x35b0 net/netfilter/ipvs/ip_vs_core.c:2063 nf_hook_entry_hookfn include/linux/netfilter.h:154 [en línea] nf_hook_slow+0xf7/0x400 net/netfilter/core.c:626 nf_hook include/linux/netfilter.h:269 [en línea] __ip_local_out+0x758/0x7e0 net/ipv4/ip_output.c:118 ip_local_out net/ipv4/ip_output.c:127 [en línea] ip_send_skb+0x6a/0x3c0 net/ipv4/ip_output.c:1501 udp_send_skb+0xfda/0x1b70 net/ipv4/udp.c:1195 udp_sendmsg+0x2fe3/0x33c0 net/ipv4/udp.c:1483 inet_sendmsg+0x1fc/0x280 net/ipv4/af_inet.c:851 sock_sendmsg_nosec net/socket.c:712 [en línea] __sock_sendmsg+0x267/0x380 net/socket.c:727 ____sys_sendmsg+0x91b/0xda0 net/socket.c:2566 ___sys_sendmsg+0x28d/0x3c0 net/socket.c:2620 __sys_sendmmsg+0x41d/0x880 net/socket.c:2702 __compat_sys_sendmmsg net/compat.c:360 [en línea] __do_compat_sys_sendmmsg net/compat.c:367 [en línea] __se_compat_sys_sendmmsg net/compat.c:364 [en línea] __ia32_compat_sys_sendmmsg+0xc8/0x140 net/compat.c:364 ia32_sys_call+0x3ffa/0x41f0 arch/x86/include/generated/asm/syscalls_32.h:346 do_syscall_32_irqs_on arch/x86/entry/syscall_32.c:83 [en línea] __do_fast_syscall_32+0xb0/0x110 arch/x86/entry/syscall_32.c:306 do_fast_syscall_32+0x38/0x80 arch/x86/entry/syscall_32.c:331 do_SYSENTER_32+0x1f/0x30 arch/x86/entry/syscall_32.c:369 entry_SYSENTER_compat_after_hwframe+0x84/0x8e CPU: 0 UID: 0 PID: 22408 Comm: syz.4.5165 No contaminado 6.15.0-rc3-syzkaller-00019-gbc3372351d0c #0 PREEMPT(undef) Nombre del hardware: Google Google Compute Engi ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/0160ac84fb03a0bd8dce8a42cb25bfaeedd110f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d0032112a0380d0b8d7c9005f621928a9b9fc76", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a3a1b784791a3cbfc6e05c4d8a3c321ac5136e25", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/adbc8cc1162951cb152ed7f147d5fbd35ce3e62f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e34090d7214e0516eb8722aee295cb2507317c07", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
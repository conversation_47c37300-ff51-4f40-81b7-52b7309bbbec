{"cve_id": "CVE-2025-40629", "published_date": "2025-05-16T13:15:52.543", "last_modified_date": "2025-05-16T14:42:18.700", "descriptions": [{"lang": "en", "value": "PNETLab 4.2.10 does not properly sanitize user inputs in its file access mechanisms. This allows attackers to perform directory traversal by manipulating file paths in HTTP requests. Specifically, the application is vulnerable to requests that access sensitive files outside the intended directory."}, {"lang": "es", "value": "PNETLab 4.2.10 no desinfecta correctamente las entradas del usuario en sus mecanismos de acceso a archivos. Esto permite a los atacantes directory traversal manipulando las rutas de los archivos en las solicitudes HTTP. En concreto, la aplicación es vulnerable a las solicitudes que acceden a archivos confidenciales fuera del directorio previsto."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/path-traversal-vulnerability-pnetlab", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1286", "published_date": "2025-05-15T20:16:02.147", "last_modified_date": "2025-06-12T16:37:11.290", "descriptions": [{"lang": "en", "value": "The Download HTML TinyMCE Button WordPress plugin through 1.2 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin."}, {"lang": "es", "value": "El complemento Download HTML TinyMCE Button de WordPress hasta la versión 1.2 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios altos, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/c42556c7-09b6-49ae-9f87-cbaf16e7c280/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
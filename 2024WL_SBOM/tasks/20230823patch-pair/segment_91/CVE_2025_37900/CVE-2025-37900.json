{"cve_id": "CVE-2025-37900", "published_date": "2025-05-20T16:15:26.357", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niommu: Fix two issues in iommu_copy_struct_from_user()\n\nIn the review for iommu_copy_struct_to_user() helper, <PERSON> pointed out that\na NULL pointer should be rejected prior to dereferencing it:\nhttps://lore.kernel.org/all/<EMAIL>\n\nAnd <PERSON><PERSON> pointed out a typo at the same time:\nhttps://lore.kernel.org/all/<EMAIL>\n\nSince both issues were copied from iommu_copy_struct_from_user(), fix them\nfirst in the current header."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iommu: Se corrigen dos problemas en iommu_copy_struct_from_user() En la revisión del asistente iommu_copy_struct_to_user(), <PERSON> que se debe rechazar un puntero NULL antes de desreferenciarlo: https://lore.kernel.org/all/<EMAIL> Y Alok señaló un error tipográfico al mismo tiempo: https://lore.kernel.org/all/<EMAIL> Dado que ambos problemas se copiaron de iommu_copy_struct_from_user(), corríjalos primero en el encabezado actual."}], "references": [{"url": "https://git.kernel.org/stable/c/2e303d010722787dc84d94f68d70fe10dfc1b9ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/30a3f2f3e4bd6335b727c83c08a982d969752bc1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/967d6f0d9a20a1bf15ee7ed881e2d4e532e22709", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
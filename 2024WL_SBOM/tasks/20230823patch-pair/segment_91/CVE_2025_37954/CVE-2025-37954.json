{"cve_id": "CVE-2025-37954", "published_date": "2025-05-20T16:15:33.603", "last_modified_date": "2025-05-21T20:24:58.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nsmb: client: Avoid race in open_cached_dir with lease breaks\n\nA pre-existing valid cfid returned from find_or_create_cached_dir might\nrace with a lease break, meaning open_cached_dir doesn't consider it\nvalid, and thinks it's newly-constructed. This leaks a dentry reference\nif the allocation occurs before the queued lease break work runs.\n\nAvoid the race by extending holding the cfid_list_lock across\nfind_or_create_cached_dir and when the result is checked."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: smb: cliente: Evitar la competencia en open_cached_dir con interrupciones de arrendamiento. Un cfid válido preexistente devuelto desde find_or_create_cached_dir podría competir con una interrupción de arrendamiento, lo que significa que open_cached_dir no lo considera válido y cree que es de nueva construcción. Esto filtra una referencia de dentry si la asignación ocurre antes de que se ejecute el trabajo de interrupción de arrendamiento en cola. Evite la competencia extendiendo la retención de cfid_list_lock a través de find_or_create_cached_dir y cuando se comprueba el resultado."}], "references": [{"url": "https://git.kernel.org/stable/c/2407265dc32bc8cc45b62a612c2a214ba9038e8b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2ed98e89ebc2e1bc73534dc3c18cb7843a889ff9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3ca02e63edccb78ef3659bebc68579c7224a6ca2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/571dcf3d27b24800c171aea7b5e04ff06d10e2e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-31639", "published_date": "2025-05-16T16:15:37.420", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in themeton Spare allows Cross Site Request Forgery. This issue affects Spare: from n/a through 1.7."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en themeton Spare permite Cross-Site Request Forgery. Este problema afecta a Spare desde n/d hasta la versión 1.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/spare/vulnerability/wordpress-spare-1-7-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
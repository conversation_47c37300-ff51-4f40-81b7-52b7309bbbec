{"cve_id": "CVE-2025-37905", "published_date": "2025-05-20T16:15:26.927", "last_modified_date": "2025-05-21T20:25:16.407", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfirmware: arm_scmi: Balance device refcount when destroying devices\n\nUsing device_find_child() to lookup the proper SCMI device to destroy\ncauses an unbalance in device refcount, since device_find_child() calls an\nimplicit get_device(): this, in turns, inhibits the call of the provided\nrelease methods upon devices destruction.\n\nAs a consequence, one of the structures that is not freed properly upon\ndestruction is the internal struct device_private dev->p populated by the\ndrivers subsystem core.\n\nKMemleak detects this situation since loading/unloding some SCMI driver\ncauses related devices to be created/destroyed without calling any\ndevice_release method.\n\nunreferenced object 0xffff00000f583800 (size 512):\n  comm \"insmod\", pid 227, jiffies 4294912190\n  hex dump (first 32 bytes):\n    00 00 00 00 ad 4e ad de ff ff ff ff 00 00 00 00  .....N..........\n    ff ff ff ff ff ff ff ff 60 36 1d 8a 00 80 ff ff  ........`6......\n  backtrace (crc 114e2eed):\n    kmemleak_alloc+0xbc/0xd8\n    __kmalloc_cache_noprof+0x2dc/0x398\n    device_add+0x954/0x12d0\n    device_register+0x28/0x40\n    __scmi_device_create.part.0+0x1bc/0x380\n    scmi_device_create+0x2d0/0x390\n    scmi_create_protocol_devices+0x74/0xf8\n    scmi_device_request_notifier+0x1f8/0x2a8\n    notifier_call_chain+0x110/0x3b0\n    blocking_notifier_call_chain+0x70/0xb0\n    scmi_driver_register+0x350/0x7f0\n    0xffff80000a3b3038\n    do_one_initcall+0x12c/0x730\n    do_init_module+0x1dc/0x640\n    load_module+0x4b20/0x5b70\n    init_module_from_file+0xec/0x158\n\n$ ./scripts/faddr2line ./vmlinux device_add+0x954/0x12d0\ndevice_add+0x954/0x12d0:\nkmalloc_noprof at include/linux/slab.h:901\n(inlined by) kzalloc_noprof at include/linux/slab.h:1037\n(inlined by) device_private_init at drivers/base/core.c:3510\n(inlined by) device_add at drivers/base/core.c:3561\n\nBalance device refcount by issuing a put_device() on devices found via\ndevice_find_child()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: firmware: arm_scmi: Equilibrar el recuento de referencias de dispositivos al destruir dispositivos. El uso de device_find_child() para buscar el dispositivo SCMI adecuado para destruir provoca un desequilibrio en el recuento de referencias de dispositivos, ya que device_find_child() llama a un método get_device() implícito: esto, a su vez, inhibe la llamada a los métodos de liberación proporcionados tras la destrucción de dispositivos. Como consecuencia, una de las estructuras que no se libera correctamente tras la destrucción es la estructura interna device_private dev-&gt;p, rellenada por el núcleo del subsistema de controladores. KMemleak detecta esta situación, ya que la carga/descarga de algún controlador SCMI provoca que los dispositivos relacionados se creen/destruyan sin llamar a ningún método device_release. objeto sin referencia 0xffff00000f583800 (tamaño 512): comm \"insmod\", pid 227, jiffies 4294912190 volcado hexadecimal (primeros 32 bytes): 00 00 00 00 ad 4e ad de ff ff ff ff 00 00 00 00 .....N.......... ff ff ff ff ff ff ff ff ff 60 36 1d 8a 00 80 ff ff ........`6...... seguimiento inverso (crc 114e2eed): kmemleak_alloc+0xbc/0xd8 __kmalloc_cache_noprof+0x2dc/0x398 device_add+0x954/0x12d0 device_register+0x28/0x40 __scmi_device_create.part.0+0x1bc/0x380 scmi_device_create+0x2d0/0x390 scmi_create_protocol_devices+0x74/0xf8 scmi_device_request_notifier+0x1f8/0x2a8 notifier_call_chain+0x110/0x3b0 blocking_notifier_call_chain+0x70/0xb0 scmi_driver_register+0x350/0x7f0 0xffff80000a3b3038 do_one_initcall+0x12c/0x730 do_init_module+0x1dc/0x640 load_module+0x4b20/0x5b70 init_module_from_file+0xec/0x158 $ ./scripts/faddr2line ./vmlinux device_add+0x954/0x12d0 device_add+0x954/0x12d0: kmalloc_noprof en include/linux/slab.h:901 (en línea por) kzalloc_noprof en include/linux/slab.h:1037 (en línea por) device_private_init en drivers/base/core.c:3510 (en línea por) device_add en drivers/base/core.c:3561 Equilibre el recuento de dispositivos emitiendo un put_device() en los dispositivos encontrados a través de device_find_child()."}], "references": [{"url": "https://git.kernel.org/stable/c/2fbf6c9695ad9f05e7e5c166bf43fac7cb3276b3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8a8a3547d5c4960da053df49c75bf623827a25da", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/91ff1e9652fb9beb0174267d6bb38243dff211bb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/969d8beaa2e374387bf9aa5602ef84fc50bb48d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9ca67840c0ddf3f39407339624cef824a4f27599", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ff4273d47da81b95ed9396110bcbd1b7b7470fe8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
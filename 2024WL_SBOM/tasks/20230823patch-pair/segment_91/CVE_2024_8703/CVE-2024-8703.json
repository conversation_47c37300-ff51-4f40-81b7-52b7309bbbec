{"cve_id": "CVE-2024-8703", "published_date": "2025-05-15T20:15:59.793", "last_modified_date": "2025-05-28T15:41:51.927", "descriptions": [{"lang": "en", "value": "The Z-Downloads WordPress plugin before 1.11.6 does not sanitise and escape some parameters when outputting them in the page, which could allow unauthenticated visitors to perform Cross-Site Scripting attacks when accessing share URLs."}, {"lang": "es", "value": "El complemento Z-Downloads para WordPress anterior a la versión 1.11.6 no depura ni escapa algunos parámetros al mostrarlos en la página, lo que podría permitir a visitantes no autenticados realizar ataques de Cross-Site Scripting al acceder a URL compartidas."}], "references": [{"url": "https://wpscan.com/vulnerability/604e990e-9bec-469e-8630-605eea74e12c/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-32290", "published_date": "2025-05-16T16:15:38.990", "last_modified_date": "2025-05-19T13:35:50.497", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in LambertGroup Sticky HTML5 Music Player allows SQL Injection. This issue affects Sticky HTML5 Music Player: from n/a through 3.1.6."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en LambertGroup Sticky HTML5 Music Player permite la inyección SQL. Este problema afecta al reproductor de música HTML5 Sticky desde n/d hasta la versión 3.1.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lbg-audio3-html5/vulnerability/wordpress-sticky-html5-music-player-3-1-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2020-36706", "published_date": "2023-10-20T07:15:14.650", "last_modified_date": "2024-11-21T05:30:07.123", "descriptions": [{"lang": "en", "value": "The Simple:Press – WordPress Forum Plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the ~/admin/resources/jscript/ajaxupload/sf-uploader.php file in versions up to, and including, 6.6.0. This makes it possible for attackers to upload arbitrary files on the affected sites server which may make remote code execution possible."}, {"lang": "es", "value": "The Simple:Press – WordPress Forum Plugin para WordPress es vulnerable a cargas de archivos arbitrarias debido a la falta de validación del tipo de archivo en el archivo ~/admin/resources/jscript/ajaxupload/sf-uploader.php en versiones hasta la 6.6.0 incluida. Esto hace posible que los atacantes carguen archivos arbitrarios en el servidor del sitio afectado, lo que puede hacer posible la ejecución remota de código."}], "references": [{"url": "https://blog.nintechnet.com/wordpress-simplepress-plugin-fixed-critical-vulnerabilities/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://wpscan.com/vulnerability/27d4a8a5-9d81-4b42-92be-3f7d1ef22843", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.acunetix.com/vulnerabilities/web/wordpress-plugin-simple-press-wordpress-forum-arbitrary-file-upload-6-6-0/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/53eba5b4-7cc0-48e1-bb9c-6ed3207151ab?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
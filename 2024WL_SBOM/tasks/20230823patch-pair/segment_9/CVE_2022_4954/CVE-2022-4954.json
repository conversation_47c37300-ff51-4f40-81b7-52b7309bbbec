{"cve_id": "CVE-2022-4954", "published_date": "2023-10-20T07:15:15.103", "last_modified_date": "2024-11-21T07:36:19.187", "descriptions": [{"lang": "en", "value": "The Waiting: One-click countdowns plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Countdown name in versions up to, and including, 0.6.2 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Waiting: One-click countdowns para WordPress es vulnerable a Cross-Site Scripting (XSS) Almacenado a través del nombre Countdown en versiones hasta la 0.6.2 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con permisos de nivel de administrador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/waiting/trunk/waiting.php?rev=2826039", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2ef5b0de-0b8b-4286-86ea-6dca0dbc1a52?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
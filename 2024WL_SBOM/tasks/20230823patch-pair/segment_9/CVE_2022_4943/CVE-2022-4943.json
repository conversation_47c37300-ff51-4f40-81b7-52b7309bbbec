{"cve_id": "CVE-2022-4943", "published_date": "2023-10-20T08:15:11.983", "last_modified_date": "2024-11-21T07:36:18.063", "descriptions": [{"lang": "en", "value": "The miniOrange's Google Authenticator plugin for WordPress is vulnerable to authorization bypass due to a missing capability check when changing plugin settings in versions up to, and including, 5.6.5. This makes it possible for unauthenticated attackers to change the plugin's settings."}, {"lang": "es", "value": "El complemento Google Authenticator de miniOrange para WordPress es vulnerable a la omisión de autorización debido a una falta de verificación de capacidad al cambiar la configuración del complemento en versiones hasta la 5.6.5 incluida. Esto hace posible que atacantes no autenticados cambien la configuración del complemento."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=2842228%40miniorange-2-factor-authentication%2Ftrunk&old=2815645%40miniorange-2-factor-authentication%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/7267ede1-7745-47cc-ac0d-4362140b4c23?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-47583", "published_date": "2023-10-19T16:15:08.833", "last_modified_date": "2024-11-21T07:32:12.673", "descriptions": [{"lang": "en", "value": "Terminal character injection in Mintty before 3.6.3 allows code execution via unescaped output to the terminal."}, {"lang": "es", "value": "La inyección de caracteres de terminal en Mintty anterior a 3.6.3 permite la ejecución de código a través de salida sin escape al terminal."}], "references": [{"url": "https://dgl.cx/2023/09/ansi-terminal-security#mintty", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/mintty/mintty/releases/tag/3.6.3", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
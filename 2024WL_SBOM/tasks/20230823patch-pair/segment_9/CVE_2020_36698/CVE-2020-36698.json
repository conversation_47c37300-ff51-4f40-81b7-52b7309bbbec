{"cve_id": "CVE-2020-36698", "published_date": "2023-10-20T07:15:14.487", "last_modified_date": "2024-11-21T05:30:05.813", "descriptions": [{"lang": "en", "value": "The Security & Malware scan by CleanTalk plugin for WordPress is vulnerable to unauthorized user interaction in versions up to, and including, 2.50. This is due to missing capability checks on several AJAX actions and nonce disclosure in the source page of the administrative dashboard. This makes it possible for authenticated attackers, with subscriber-level permissions and above, to call functions and delete and/or upload files."}, {"lang": "es", "value": "El análisis de Seguridad y Malware del complemento CleanTalk para WordPress es vulnerable a la interacción no autorizada del usuario en versiones hasta la 2.50 incluida. Esto se debe a que faltan comprobaciones de capacidad en varias acciones AJAX y a la divulgación nonce en la página de origen del panel administrativo. Esto hace posible que atacantes autenticados, con permisos de nivel de suscriptor y superiores, llamen funciones y eliminen y/o carguen archivos."}], "references": [{"url": "https://blog.nintechnet.com/multiple-vulnerabilities-fixed-in-security-malware-scan-by-cleantalk-plugin/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://wpscan.com/vulnerability/23960f42-dfc1-4951-9169-02d889283f01", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0fb9b039-eb04-4c27-89eb-1932c9c31962?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
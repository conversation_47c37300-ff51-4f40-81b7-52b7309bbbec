{"cve_id": "CVE-2022-24402", "published_date": "2023-10-19T10:15:09.573", "last_modified_date": "2024-11-21T06:50:20.720", "descriptions": [{"lang": "en", "value": "The TETRA TEA1 keystream generator implements a key register initialization function that compresses the 80-bit key to only 32 bits for usage during the keystream generation phase, which is insufficient to safeguard against exhaustive search attacks."}, {"lang": "es", "value": "El generador de flujo de claves TETRA TEA1 implementa una función de inicialización de registro de claves que comprime la clave de 80 bits a solo 32 bits para su uso durante la fase de generación del flujo de claves, lo cual es insuficiente para protegerse contra ataques de búsqueda exhaustiva."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2022-24404", "published_date": "2023-10-19T10:15:09.627", "last_modified_date": "2024-11-21T06:50:20.940", "descriptions": [{"lang": "en", "value": "Lack of cryptographic integrity check on TETRA air-interface encrypted traffic. Since a stream cipher is employed, this allows an active adversary to manipulate cleartext data in a bit-by-bit fashion."}, {"lang": "es", "value": "Falta de verificación de integridad criptográfica en el tráfico cifrado de interfaz aérea TETRA. Dado que se emplea un cifrado de flujo, esto permite que un adversario activo manipule datos de texto plano bit a bit."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
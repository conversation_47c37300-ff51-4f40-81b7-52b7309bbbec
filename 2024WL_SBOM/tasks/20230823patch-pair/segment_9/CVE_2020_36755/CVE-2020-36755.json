{"cve_id": "CVE-2020-36755", "published_date": "2023-10-20T08:15:11.327", "last_modified_date": "2024-11-21T05:30:14.043", "descriptions": [{"lang": "en", "value": "The Customizr theme for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 4.3.0. This is due to missing or incorrect nonce validation on the czr_fn_post_fields_save() function. This makes it possible for unauthenticated attackers to post fields via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El Customizr theme para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en versiones hasta la 4.3.0 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función czr_fn_post_fields_save(). Esto hace posible que atacantes no autenticados publiquen campos a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://blog.nintechnet.com/25-wordpress-plugins-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/more-wordpress-plugins-and-themes-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-1/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-2/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-3/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-4/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-5/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://themes.trac.wordpress.org/browser/customizr/4.3.1/core/czr-admin-ccat.php?rev=135570#L1764", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d9f6b600-a35a-49c2-8758-a7cc5c00e947?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
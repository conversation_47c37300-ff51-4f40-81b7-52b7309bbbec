{"cve_id": "CVE-2022-42150", "published_date": "2023-10-19T20:15:08.710", "last_modified_date": "2024-11-21T07:24:27.350", "descriptions": [{"lang": "en", "value": "TinyLab linux-lab v1.1-rc1 and cloud-labv0.8-rc2, v1.1-rc1 are vulnerable to insecure permissions. The default configuration could cause Container Escape."}, {"lang": "es", "value": "TinyLab linux-lab v1.1-rc1 y cloud-labv0.8-rc2, v1.1-rc1 son vulnerables a permisos inseguros. La configuración predeterminada podría provocar el escape del contenedor."}], "references": [{"url": "https://github.com/eBPF-Research/eBPF-Attack/blob/main/PoC.md#attack-requirements", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/tinyclub/cloud-lab/blob/d19ff92713685a7fb84b423dea6a184b25c378c9/configs/common/seccomp-profiles-default.json", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/tinyclub/linux-lab/issues/14", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://hackmd.io/%40UR9gnr32QymtmtZHnZceOw/ry428EZGo", "source": "<EMAIL>", "tags": []}, {"url": "https://www.usenix.org/conference/usenixsecurity23/presentation/he", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
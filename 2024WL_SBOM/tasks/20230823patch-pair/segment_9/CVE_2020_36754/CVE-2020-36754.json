{"cve_id": "CVE-2020-36754", "published_date": "2023-10-20T08:15:11.250", "last_modified_date": "2024-11-21T05:30:13.900", "descriptions": [{"lang": "en", "value": "The Paid Memberships Pro  plugin for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 2.4.2. This is due to missing or incorrect nonce validation on the pmpro_page_save() function. This makes it possible for unauthenticated attackers to save pages via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Paid Memberships Pro para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en versiones hasta la 2.4.2 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función pmpro_page_save(). Esto hace posible que atacantes no autenticados guarden páginas a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://blog.nintechnet.com/25-wordpress-plugins-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://blog.nintechnet.com/more-wordpress-plugins-and-themes-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-1/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-2/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-3/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-4/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-5/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=2368689%40paid-memberships-pro&new=2368689%40paid-memberships-pro&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d74553a4-0ef7-4908-a2e8-5e0216f7b256?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
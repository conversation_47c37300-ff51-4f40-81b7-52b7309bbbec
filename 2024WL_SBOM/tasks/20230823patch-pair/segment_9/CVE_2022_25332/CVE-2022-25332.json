{"cve_id": "CVE-2022-25332", "published_date": "2023-10-19T10:15:09.683", "last_modified_date": "2024-11-21T06:52:01.130", "descriptions": [{"lang": "en", "value": "The AES implementation in the Texas Instruments OMAP L138 (secure variants), present in mask ROM, suffers from a timing side channel which can be exploited by an adversary with non-secure supervisor privileges by managing cache contents and collecting timing information for different ciphertext inputs. Using this side channel, the SK_LOAD secure kernel routine can be used to recover the Customer Encryption Key (CEK)."}, {"lang": "es", "value": "La implementación de AES en Texas Instruments OMAP L138 (variantes seguras), presente en la máscara ROM, sufre de un canal lateral de temporización que puede ser explotado por un adversario con privilegios de supervisor no seguros al administrar el contenido de la caché y recopilar información de temporización para diferentes entradas de texto cifrado. Usando este canal lateral, la rutina de kernel segura SK_LOAD se puede usar para recuperar el Customer Encryption Key (CEK)."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Technical Description"]}]}
{"cve_id": "CVE-2020-36714", "published_date": "2023-10-20T08:15:11.020", "last_modified_date": "2025-01-16T15:08:00.773", "descriptions": [{"lang": "en", "value": "The Brizy plugin for WordPress is vulnerable to authorization bypass due to a incorrect capability check on the is_administrator() function in versions up to, and including, 1.0.125. This makes it possible for authenticated attackers to access and interact with available AJAX functions."}, {"lang": "es", "value": "El complemento Brizy para WordPress es vulnerable a la omisión de autorización debido a una verificación de capacidad incorrecta en la función is_administrator() en versiones hasta la 1.0.125 incluida. Esto hace posible que los atacantes autenticados accedan e interactúen con las funciones AJAX disponibles."}], "references": [{"url": "https://blog.nintechnet.com/wordpress-brizy-page-builder-plugin-fixed-critical-vulnerabilities/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9495e25d-a5a6-4f25-9363-783626e58a4a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
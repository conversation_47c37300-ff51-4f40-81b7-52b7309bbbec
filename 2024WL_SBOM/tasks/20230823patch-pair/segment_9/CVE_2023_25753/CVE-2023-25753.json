{"cve_id": "CVE-2023-25753", "published_date": "2023-10-19T09:15:08.480", "last_modified_date": "2024-11-21T07:50:04.903", "descriptions": [{"lang": "en", "value": "\nThere exists an SSRF (Server-Side Request Forgery) vulnerability located at the /sandbox/proxyGateway endpoint. This vulnerability allows us to manipulate arbitrary requests and retrieve corresponding responses by inputting any URL into the requestUrl parameter.\n\nOf particular concern is our ability to exert control over the HTTP method, cookies, IP address, and headers. This effectively grants us the capability to dispatch complete HTTP requests to hosts of our choosing.\n\nThis issue affects Apache ShenYu: 2.5.1.\n\nUpgrade to Apache ShenYu 2.6.0 or apply patch  https://github.com/apache/shenyu/pull/4776  .\n\n"}, {"lang": "es", "value": "Existe una vulnerabilidad SSRF (falsificación de solicitudes del lado del servidor) ubicada en el endpoint /sandbox/proxyGateway. Esta vulnerabilidad nos permite manipular solicitudes arbitrarias y recuperar las respuestas correspondientes ingresando cualquier URL en el parámetro requestUrl. De particular preocupación es nuestra capacidad para ejercer control sobre el método HTTP, las cookies, la dirección IP y los encabezados. Esto efectivamente nos otorga la capacidad de enviar solicitudes HTTP completas a los hosts de nuestra elección. Este problema afecta a Apache ShenYu: 2.5.1. Actualice a Apache ShenYu 2.6.0 o aplique el parche https://github.com/apache/shenyu/pull/4776"}], "references": [{"url": "https://lists.apache.org/thread/chprswxvb22z35vnoxv9tt3zknsm977d", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}]}
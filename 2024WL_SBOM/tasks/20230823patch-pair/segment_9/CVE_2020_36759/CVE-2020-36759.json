{"cve_id": "CVE-2020-36759", "published_date": "2023-10-20T08:15:11.473", "last_modified_date": "2024-11-21T05:30:14.600", "descriptions": [{"lang": "en", "value": "The Woody code snippets plugin for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 2.3.9. This is due to missing or incorrect nonce validation on the runActions() function. This makes it possible for unauthenticated attackers to activate and deactivate snippets via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Woody code snippets para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en versiones hasta la 2.3.9 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función runActions(). Esto hace posible que atacantes no autenticados activen y desactiven fragmentos a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://blog.nintechnet.com/25-wordpress-plugins-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://blog.nintechnet.com/more-wordpress-plugins-and-themes-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-1/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-2/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-3/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-4/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-5/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=2368332%40insert-php&new=2368332%40insert-php&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e573c0a4-d053-400b-828c-0d0eca880776?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
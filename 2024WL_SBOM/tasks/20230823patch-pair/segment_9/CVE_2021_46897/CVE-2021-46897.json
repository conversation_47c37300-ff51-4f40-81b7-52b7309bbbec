{"cve_id": "CVE-2021-46897", "published_date": "2023-10-22T19:15:08.240", "last_modified_date": "2024-11-21T06:34:52.867", "descriptions": [{"lang": "en", "value": "views.py in Wagtail CRX CodeRed Extensions (formerly CodeRed CMS or coderedcms) before 0.22.3 allows upward protected/..%2f..%2f path traversal when serving protected media."}, {"lang": "es", "value": "views.py en Wagtail CRX CodeRed Extensions (anteriormente CodeRed CMS o coderedcms) anterior a 0.22.3 permite el path traversal hacia arriba protected/..%2f..%2f al servir medios protegidos."}], "references": [{"url": "https://github.com/coderedcorp/coderedcms/compare/v0.22.2...v0.22.3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/coderedcorp/coderedcms/issues/448", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/coderedcorp/coderedcms/pull/450", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2023-28796", "published_date": "2023-10-23T14:15:09.507", "last_modified_date": "2024-11-21T07:56:01.850", "descriptions": [{"lang": "en", "value": "Improper Verification of Cryptographic Signature vulnerability in Zscaler Client Connector on Linux allows Code Injection. This issue affects Zscaler Client Connector for Linux: before *******."}, {"lang": "es", "value": "La verificación incorrecta de la vulnerabilidad de Cryptographic Signature en Zscaler Client Connector en Linux permite la inyección de código. Este problema afecta a Zscaler Client Connector para Linux: versiones anteriores a *******."}], "references": [{"url": "https://help.zscaler.com/client-connector/client-connector-app-release-summary-2022?applicable_category=Linux&applicable_version=1.3.1&deployment_date=2022-09-19", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
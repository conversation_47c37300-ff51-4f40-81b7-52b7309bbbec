{"cve_id": "CVE-2020-36753", "published_date": "2023-10-20T08:15:11.177", "last_modified_date": "2024-11-21T05:30:13.767", "descriptions": [{"lang": "en", "value": "The Hueman theme for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 3.6.3. This is due to missing or incorrect nonce validation on the save_meta_box() function. This makes it possible for unauthenticated attackers to save metabox data via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El Hueman theme para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en versiones hasta la 3.6.3 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función save_meta_box(). Esto hace posible que atacantes no autenticados guarden datos de Metabox a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://blog.nintechnet.com/25-wordpress-plugins-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/more-wordpress-plugins-and-themes-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-1/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-2/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-3/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-4/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-5/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://themes.trac.wordpress.org/browser/hueman/3.6.4/option-tree/includes/class-ot-meta-box.php#L207", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d54b4dc9-8590-433c-873a-efb49e2e79cd?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
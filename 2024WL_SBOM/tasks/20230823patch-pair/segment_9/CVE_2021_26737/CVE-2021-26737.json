{"cve_id": "CVE-2021-26737", "published_date": "2023-10-23T14:15:09.127", "last_modified_date": "2024-11-21T05:56:46.830", "descriptions": [{"lang": "en", "value": "The Zscaler Client Connector for macOS prior to 3.6 did not sufficiently validate RPC clients. A local adversary without sufficient privileges may be able to shutdown the Zscaler tunnel by exploiting a race condition.\n\n\n"}, {"lang": "es", "value": "Zscaler Client Connector para macOS anterior a 3.6 no validaba suficientemente los clientes RPC. Un adversario local sin privilegios suficientes podría cerrar el túnel Zscaler aprovechando una condición de ejecución."}], "references": [{"url": "https://help.zscaler.com/client-connector/client-connector-app-release-summary-2022?applicable_category=macOS&applicable_version=3.6&deployment_date=2022-01-07&id=1388686", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
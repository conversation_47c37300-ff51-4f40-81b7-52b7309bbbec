{"cve_id": "CVE-2022-25333", "published_date": "2023-10-19T10:15:09.747", "last_modified_date": "2024-11-21T06:52:01.270", "descriptions": [{"lang": "en", "value": "The Texas Instruments OMAP L138 (secure variants) trusted execution environment (TEE) performs an RSA check implemented in mask ROM when loading a module through the SK_LOAD routine. However, only the module header authenticity is validated. An adversary can re-use any correctly signed header and append a forged payload, to be encrypted using the CEK (obtainable through CVE-2022-25332) in order to obtain arbitrary code execution in secure context. This constitutes a full break of the TEE security architecture."}, {"lang": "es", "value": "Texas Instruments OMAP L138 (variantes seguras) Trusted Execution Environment (TEE) realiza una verificación RSA implementada en la máscara ROM al cargar un módulo a través de la rutina SK_LOAD. Sin embargo, sólo se valida la autenticidad del encabezado del módulo. Un adversario puede reutilizar cualquier encabezado firmado correctamente y agregar un payload falsificado, que se cifrará mediante CEK (que se puede obtener a través de CVE-2022-25332) para obtener la ejecución de código arbitrario en un contexto seguro. Esto constituye una ruptura total de la arquitectura de seguridad de TEE."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}
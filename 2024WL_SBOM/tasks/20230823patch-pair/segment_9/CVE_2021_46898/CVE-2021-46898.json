{"cve_id": "CVE-2021-46898", "published_date": "2023-10-22T19:15:08.297", "last_modified_date": "2024-11-21T06:34:53.027", "descriptions": [{"lang": "en", "value": "views/switch.py in django-grappelli (aka Django Grappelli) before 2.15.2 attempts to prevent external redirection with startswith(\"/\") but this does not consider a protocol-relative URL (e.g., //example.com) attack."}, {"lang": "es", "value": "views/switch.py en django-grappelli (también conocido como Django Grappelli) anterior a 2.15.2 intenta evitar la redirección externa con startwith(\"/\") pero esto no considera un ataque de URL relativo al protocolo (por ejemplo, //example.com) ."}], "references": [{"url": "https://github.com/sehmaschine/django-grappelli/commit/4ca94bcda0fa2720594506853d85e00c8212968f", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/sehmaschine/django-grappelli/compare/2.15.1...2.15.2", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/sehmaschine/django-grappelli/issues/975", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/sehmaschine/django-grappelli/pull/976", "source": "<EMAIL>", "tags": ["Patch"]}]}
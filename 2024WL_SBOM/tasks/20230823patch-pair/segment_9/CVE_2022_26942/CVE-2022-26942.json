{"cve_id": "CVE-2022-26942", "published_date": "2023-10-19T10:15:09.913", "last_modified_date": "2024-11-21T06:54:50.667", "descriptions": [{"lang": "en", "value": "The Motorola MTM5000 series firmwares lack pointer validation on arguments passed to trusted execution environment (TEE) modules. Two modules are used, one responsible for KVL key management and the other for TETRA cryptographic functionality. In both modules, an adversary with non-secure supervisor level code execution can exploit the issue in order to gain secure supervisor code execution within the TEE. This constitutes a full break of the TEE module, exposing the device key as well as any TETRA cryptographic keys and the confidential TETRA cryptographic primitives. "}, {"lang": "es", "value": "Los firmwares de la serie Motorola MTM5000 carecen de validación de puntero en los argumentos pasados a los módulos Trusted Execution Environment (TEE). Se utilizan dos módulos, uno responsable de la gestión de claves KVL y el otro de la funcionalidad criptográfica TETRA. En ambos módulos, un adversario con una ejecución de código de nivel de supervisor no segura puede aprovechar el problema para obtener una ejecución segura de código de supervisor dentro del TEE. Esto constituye una ruptura total del módulo TEE, exponiendo la clave del dispositivo, así como cualquier clave criptográfica TETRA y las primitivas criptográficas TETRA confidenciales."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Technical Description"]}]}
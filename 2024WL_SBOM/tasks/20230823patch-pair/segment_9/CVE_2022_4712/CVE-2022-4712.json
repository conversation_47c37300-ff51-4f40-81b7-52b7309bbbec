{"cve_id": "CVE-2022-4712", "published_date": "2023-10-20T07:15:14.993", "last_modified_date": "2024-11-21T07:35:47.073", "descriptions": [{"lang": "en", "value": "The WP Cerber Security plugin for WordPress is vulnerable to stored cross-site scripting via the log parameter when logging in to the site in versions up to, and including, 9.1. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WP Cerber Security para WordPress es vulnerable a Cross-Site Scripting (XSS) almacenados a través del parámetro de registro al iniciar sesión en el sitio en versiones hasta la 9.1 incluida. Esto hace posible que atacantes no autenticados inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-cerber/trunk/admin/cerber-dashboard.php?rev=2721561#L1338", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6cd9cbba-10b0-4fb0-ad49-4593a307a615?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
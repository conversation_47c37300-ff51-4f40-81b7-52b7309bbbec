{"cve_id": "CVE-2021-26738", "published_date": "2023-10-23T14:15:09.187", "last_modified_date": "2024-11-21T05:56:46.947", "descriptions": [{"lang": "en", "value": "Zscaler Client Connector for macOS prior to 3.7 had an unquoted search path vulnerability via the PATH variable. A local adversary may be able to execute code with root privileges.\n\n\n"}, {"lang": "es", "value": "Zscaler Client Connector para macOS anterior a 3.7 tenía una vulnerabilidad de ruta de búsqueda sin comillas a través de la variable PATH. Un adversario local puede ejecutar código con privilegios de root."}], "references": [{"url": "https://help.zscaler.com/client-connector/client-connector-app-release-summary-2022?applicable_category=macOS&applicable_version=3.7&deployment_date=2022-08-19&id=1414851", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
{"cve_id": "CVE-2023-23373", "published_date": "2023-10-20T17:15:08.427", "last_modified_date": "2024-11-21T07:46:03.497", "descriptions": [{"lang": "en", "value": "An OS command injection vulnerability has been reported to affect QUSBCam2. If exploited, the vulnerability could allow users to execute commands via a network.\n\nWe have already fixed the vulnerability in the following version:\nQUSBCam2 2.0.3 ( 2023/06/15 ) and later\n"}, {"lang": "es", "value": "Se ha informado que una vulnerabilidad de inyección de comandos del sistema operativo afecta a QUSBCam2. Si se explota, la vulnerabilidad podría permitir a los usuarios ejecutar comandos a través de una red. Ya hemos solucionado la vulnerabilidad en la siguiente versión: QUSBCam2 2.0.3 (2023/06/15) y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-43", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
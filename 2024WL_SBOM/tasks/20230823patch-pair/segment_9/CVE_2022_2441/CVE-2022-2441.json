{"cve_id": "CVE-2022-2441", "published_date": "2023-10-20T08:15:11.707", "last_modified_date": "2024-11-21T07:00:59.857", "descriptions": [{"lang": "en", "value": "The ImageMagick Engine plugin for WordPress is vulnerable to remote code execution via the 'cli_path' parameter in versions up to, and including 1.7.5. This makes it possible for unauthenticated users to run arbitrary commands leading to remote command execution, granted they can trick a site administrator into performing an action such as clicking on a link. This makes it possible for an attacker to create and or modify files hosted on the server which can easily grant attackers backdoor access to the affected server."}, {"lang": "es", "value": "El complemento ImageMagick Engine para WordPress es vulnerable a la ejecución remota de código a través del parámetro 'cli_path' en versiones hasta la 1.7.5 incluida. Esto hace posible que usuarios no autenticados ejecuten comandos arbitrarios que conduzcan a la ejecución remota de comandos, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer clic en un enlace. Esto hace posible que un atacante cree o modifique archivos alojados en el servidor, lo que puede otorgar fácilmente a los atacantes acceso por puerta trasera al servidor afectado."}], "references": [{"url": "https://github.com/orangelabweb/imagemagick-engine/blob/1.7.4/imagemagick-engine.php#L529", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/orangelabweb/imagemagick-engine/blob/v.1.7.2/imagemagick-engine.php#L529", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=2801283%40imagemagick-engine%2Ftrunk&old=2732430%40imagemagick-engine%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.exploit-db.com/exploits/51025", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b1f17a83-1df0-44fe-bd86-243cff6ec91b?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.wordfence.com/vulnerability-advisories-continued/#CVE-2022-2441", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
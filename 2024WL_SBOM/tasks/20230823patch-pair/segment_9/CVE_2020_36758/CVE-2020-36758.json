{"cve_id": "CVE-2020-36758", "published_date": "2023-10-20T08:15:11.393", "last_modified_date": "2024-11-21T05:30:14.460", "descriptions": [{"lang": "en", "value": "The RSS Aggregator by Feedzy plugin for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 3.4.2. This is due to missing or incorrect nonce validation on the save_feedzy_post_type_meta() function. This makes it possible for unauthenticated attackers to update post meta via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento RSS Aggregator de Feedzy para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en versiones hasta la 3.4.2 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función save_feedzy_post_type_meta(). Esto hace posible que atacantes no autenticados actualicen el metadato de la publicación a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://blog.nintechnet.com/25-wordpress-plugins-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://blog.nintechnet.com/more-wordpress-plugins-and-themes-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-1/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-2/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-3/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-4/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-5/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://plugins.trac.wordpress.org/changeset/2369394/feedzy-rss-feeds/trunk/includes/admin/feedzy-rss-feeds-admin.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e3b916dc-3b94-4319-a805-0ea99d14429f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-26943", "published_date": "2023-10-19T10:15:09.963", "last_modified_date": "2024-11-21T06:54:50.800", "descriptions": [{"lang": "en", "value": "The Motorola MTM5000 series firmwares generate TETRA authentication challenges using a PRNG using a tick count register as its sole entropy source. Low boottime entropy and limited re-seeding of the pool renders the authentication challenge vulnerable to two attacks. First, due to the limited boottime pool entropy, an adversary can derive the contents of the entropy pool by an exhaustive search of possible values, based on an observed authentication challenge. Second, an adversary can use knowledge of the entropy pool to predict authentication challenges. As such, the unit is vulnerable to CVE-2022-24400."}, {"lang": "es", "value": "Los firmwares de la serie Motorola MTM5000 generan desafíos de autenticación TETRA utilizando un PRNG que utiliza un registro de conteo de ticks como única fuente de entropía. La baja entropía del tiempo de arranque y la resiembra limitada del grupo hacen que el desafío de autenticación sea vulnerable a dos ataques. En primer lugar, debido a la entropía limitada del grupo de tiempo de arranque, un adversario puede derivar el contenido del grupo de entropía mediante una búsqueda exhaustiva de valores posibles, basándose en un desafío de autenticación observado. En segundo lugar, un adversario puede utilizar el conocimiento del conjunto de entropía para predecir los desafíos de autenticación. Como tal, la unidad es vulnerable a CVE-2022-24400."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Technical Description"]}]}
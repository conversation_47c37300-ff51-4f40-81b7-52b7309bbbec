{"cve_id": "CVE-2022-27813", "published_date": "2023-10-19T10:15:10.013", "last_modified_date": "2024-11-21T06:56:14.420", "descriptions": [{"lang": "en", "value": "Motorola MTM5000 series firmwares lack properly configured memory protection of pages shared between the OMAP-L138 ARM and DSP cores. The SoC provides two memory protection units, MPU1 and MPU2, to enforce the trust boundary between the two cores. Since both units are left unconfigured by the firmwares, an adversary with control over either core can trivially gain code execution on the other, by overwriting code located in shared RAM or DDR2 memory regions."}, {"lang": "es", "value": "Los firmwares de la serie Motorola MTM5000 carecen de protección de memoria configurada correctamente para las páginas compartidas entre los núcleos OMAP-L138 ARM y DSP. El SoC proporciona dos unidades de protección de memoria, MPU1 y MPU2, para reforzar el límite de confianza entre los dos núcleos. Dado que los firmwares dejan ambas unidades sin configurar, un adversario con control sobre cualquiera de los núcleos puede obtener trivialmente la ejecución de código en el otro, sobrescribiendo el código ubicado en la RAM compartida o en las regiones de memoria DDR2."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Technical Description"]}]}
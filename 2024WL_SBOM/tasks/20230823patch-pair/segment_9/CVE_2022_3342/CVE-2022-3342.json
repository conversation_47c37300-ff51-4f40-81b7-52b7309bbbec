{"cve_id": "CVE-2022-3342", "published_date": "2023-10-20T08:15:11.787", "last_modified_date": "2024-11-21T07:19:20.087", "descriptions": [{"lang": "en", "value": "The Jetpack CRM plugin for WordPress is vulnerable to PHAR deserialization via the ‘zbscrmcsvimpf’ parameter in the 'zeroBSCRM_CSVImporterLitehtml_app' function in versions up to, and including, 5.3.1. While the function performs a nonce check, steps 2 and 3 of the check do not take any action upon a failed check. These steps then perform a 'file_exists' check on the value of 'zbscrmcsvimpf'. If a phar:// archive is supplied, its contents will be deserialized and an object injected in the execution stream. This allows an unauthenticated attacker to obtain object injection if they are able to upload a phar archive (for instance if the site supports image uploads) and then trick an administrator into performing an action, such as clicking a link."}, {"lang": "es", "value": "El complemento Jetpack CRM para WordPress es vulnerable a la deserialización PHAR a través del parámetro 'zbscrmcsvimpf' en la función 'zeroBSCRM_CSVImporterLitehtml_app' en versiones hasta la 5.3.1 incluida. Si bien la función realiza una verificación nonce, los pasos 2 y 3 de la verificación no realizan ninguna acción ante una verificación fallida. <PERSON><PERSON>, estos pasos realizan una verificación de 'file_exists' en el valor de 'zbscrmcsvimpf'. Si se proporciona un archivo phar://, su contenido se deserializará y se inyectará un objeto en el flujo de ejecución. Esto permite a un atacante no autenticado obtener una inyección de objetos si puede cargar un archivo phar (por ejemplo, si el sitio admite la carga de imágenes) y luego engañar a un administrador para que realice una acción, como hacer click en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/zero-bs-crm/trunk/includes/ZeroBSCRM.CSVImporter.php?rev=2790863", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset/2805282/zero-bs-crm/trunk/includes/ZeroBSCRM.CSVImporter.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/98ab264f-b210-41d0-bb6f-b4f31d933f80?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2022-25334", "published_date": "2023-10-19T10:15:09.803", "last_modified_date": "2024-11-21T06:52:01.390", "descriptions": [{"lang": "en", "value": "The Texas Instruments OMAP L138 (secure variants) trusted execution environment (TEE) lacks a bounds check on the signature size field in the SK_LOAD module loading routine, present in mask ROM. A module with a sufficiently large signature field causes a stack overflow, affecting secure kernel data pages. This can be leveraged to obtain arbitrary code execution in secure supervisor context by overwriting a SHA256 function pointer in the secure kernel data area when loading a forged, unsigned SK_LOAD module encrypted with the CEK (obtainable through CVE-2022-25332). This constitutes a full break of the TEE security architecture."}, {"lang": "es", "value": "Texas Instruments OMAP L138 (variantes seguras) Trusted Execution Environment (TEE) carece de una verificación de límites en el campo de tamaño de firma en la rutina de carga del módulo SK_LOAD, presente en la máscara ROM. Un módulo con un campo de firma suficientemente grande provoca un desbordamiento de la pila, lo que afecta las páginas seguras de datos del kernel. Esto se puede aprovechar para obtener la ejecución de código arbitrario en un contexto de supervisor seguro sobrescribiendo un puntero de función SHA256 en el área segura de datos del kernel al cargar un módulo SK_LOAD falsificado y sin firmar cifrado con CEK (obtenible a través de CVE-2022-25332). Esto constituye una ruptura total de la arquitectura de seguridad de TEE."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}
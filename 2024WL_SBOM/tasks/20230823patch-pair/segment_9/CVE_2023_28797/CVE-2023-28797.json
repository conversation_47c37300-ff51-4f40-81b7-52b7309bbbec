{"cve_id": "CVE-2023-28797", "published_date": "2023-10-23T14:15:09.567", "last_modified_date": "2024-11-21T07:56:01.970", "descriptions": [{"lang": "en", "value": "Zscaler Client Connector for Windows before 4.1 writes/deletes a configuration file inside specific folders on the disk. A malicious user can replace the folder and execute code as a privileged user.\n\n\n\n"}, {"lang": "es", "value": "Zscaler Client Connector para Windows anterior a 4.1 escribe/elimina un archivo de configuración dentro de carpetas específicas en el disco. Un usuario malintencionado puede reemplazar la carpeta y ejecutar código como usuario privilegiado."}], "references": [{"url": "https://help.zscaler.com/client-connector/client-connector-app-release-summary-2022", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
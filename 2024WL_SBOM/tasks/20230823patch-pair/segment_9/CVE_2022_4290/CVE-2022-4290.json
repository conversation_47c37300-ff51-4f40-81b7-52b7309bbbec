{"cve_id": "CVE-2022-4290", "published_date": "2023-10-20T08:15:11.917", "last_modified_date": "2024-11-21T07:34:57.010", "descriptions": [{"lang": "en", "value": "The Cyr to Lat plugin for WordPress is vulnerable to authenticated SQL Injection via the 'ctl_sanitize_title' function in versions up to, and including, 3.5 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query. This potentially allows authenticated users with the ability to add or modify terms or tags to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database. A partial patch became available in version 3.6 and the issue was fully patched in version 3.7."}, {"lang": "es", "value": "El complemento Cyr to Lat para WordPress es vulnerable a la inyección SQL autenticada a través de la función 'ctl_sanitize_title' en versiones hasta la 3.5 incluida debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Potencialmente, esto permite a los usuarios autenticados tener la capacidad de agregar o modificar términos o etiquetas para agregar consultas SQL adicionales a consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos. Un parche parcial estuvo disponible en la versión 3.6 y el problema se solucionó completamente en la versión 3.7."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/cyr3lat/trunk/cyr-to-lat.php?rev=1117224#L69", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c9c29130-1b42-4edd-ad62-6f635e03ae31?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
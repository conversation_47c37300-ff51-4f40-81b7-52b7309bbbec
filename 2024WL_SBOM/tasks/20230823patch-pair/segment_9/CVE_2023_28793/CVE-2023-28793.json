{"cve_id": "CVE-2023-28793", "published_date": "2023-10-23T14:15:09.387", "last_modified_date": "2024-11-21T07:56:01.457", "descriptions": [{"lang": "en", "value": "Buffer overflow vulnerability in the signelf library used by Zscaler Client Connector on Linux allows Code Injection. This issue affects Zscaler Client Connector for Linux: before *******."}, {"lang": "es", "value": "Una vulnerabilidad de desbordamiento de búfer en la librería signelf utilizada por Zscaler Client Connector en Linux permite la inyección de código. Este problema afecta a Zscaler Client Connector para Linux: versiones anteriores a *******."}], "references": [{"url": "https://help.zscaler.com/client-connector/client-connector-app-release-summary-2022?applicable_category=Linux&applicable_version=1.3.1&deployment_date=2022-09-19", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
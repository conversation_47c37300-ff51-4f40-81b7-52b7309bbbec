{"cve_id": "CVE-2023-28804", "published_date": "2023-10-23T14:15:09.687", "last_modified_date": "2024-11-21T07:56:02.890", "descriptions": [{"lang": "en", "value": "An Improper Verification of Cryptographic Signature vulnerability in Zscaler Client Connector on Linux allows replacing binaries.This issue affects Linux Client Connector: before *********"}, {"lang": "es", "value": "Una vulnerabilidad de verificación incorrecta de Cryptographic Signature en Zscaler Client Connector en Linux permite reemplazar archivos binarios. Este problema afecta a Linux Client Connector: antes de *********"}], "references": [{"url": "https://help.zscaler.com/client-connector/client-connector-app-release-summary-2023", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
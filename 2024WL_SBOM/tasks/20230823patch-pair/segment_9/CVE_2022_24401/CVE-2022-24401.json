{"cve_id": "CVE-2022-24401", "published_date": "2023-10-19T10:15:09.510", "last_modified_date": "2024-11-21T06:50:20.597", "descriptions": [{"lang": "en", "value": "Adversary-induced keystream re-use on TETRA air-interface encrypted traffic using any TEA keystream generator. IV generation is based upon several TDMA frame counters, which are frequently broadcast by the infrastructure in an unauthenticated manner. An active adversary can manipulate the view of these counters in a mobile station, provoking keystream re-use. By sending crafted messages to the MS and analyzing MS responses, keystream for arbitrary frames can be recovered."}, {"lang": "es", "value": "Reutilización del flujo de claves inducida por el adversario en el tráfico cifrado de interfaz aérea TETRA utilizando cualquier generador de flujo de claves TEA. La generación IV se basa en varios contadores de frame TDMA, que frecuentemente la infraestructura transmite sin autenticación. Un adversario activo puede manipular la vista de estos contadores en una estación móvil, provocando la reutilización del flujo de claves. Al enviar mensajes manipulados al MS y analizar las respuestas del MS, se puede recuperar el flujo de claves de frames arbitrarios."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2022-26941", "published_date": "2023-10-19T10:15:09.860", "last_modified_date": "2024-11-21T06:54:50.533", "descriptions": [{"lang": "en", "value": "A format string vulnerability exists in Motorola MTM5000 series firmware AT command handler for the AT+CTGL command. An attacker-controllable string is improperly handled, allowing for a write-anything-anywhere scenario. This can be leveraged to obtain arbitrary code execution inside the teds_app binary, which runs with root privileges."}, {"lang": "es", "value": "Existe una vulnerabilidad de cadena de formato en el controlador de comandos AT del firmware de la serie Motorola MTM5000 para el comando AT+CTGL. Una cadena controlable por un atacante se maneja incorrectamente, lo que permite un escenario en el que se puede escribir cualquier cosa en cualquier lugar. Esto se puede aprovechar para obtener la ejecución de código arbitrario dentro del binario teds_app, que se ejecuta con privilegios de root."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Technical Description"]}]}
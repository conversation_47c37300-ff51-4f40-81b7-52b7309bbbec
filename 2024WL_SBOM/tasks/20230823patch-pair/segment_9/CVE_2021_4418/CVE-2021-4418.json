{"cve_id": "CVE-2021-4418", "published_date": "2023-10-20T08:15:11.627", "last_modified_date": "2024-11-21T06:37:40.597", "descriptions": [{"lang": "en", "value": "The Custom CSS, JS & PHP plugin for WordPress is vulnerable to Cross-Site Request Forgery in versions up to, and including, 2.0.7. This is due to missing or incorrect nonce validation on the save() function. This makes it possible for unauthenticated attackers to save code snippets via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Custom CSS, JS &amp; PHP para WordPress es vulnerable a Cross-Site Request Forgery (CSRF) en versiones hasta la 2.0.7 incluida. Esto se debe a una validación nonce faltante o incorrecta en la función save(). Esto hace posible que atacantes no autenticados guarden fragmentos de código a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer click en un enlace."}], "references": [{"url": "https://blog.nintechnet.com/25-wordpress-plugins-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/more-wordpress-plugins-and-themes-vulnerable-to-csrf-attacks/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-1/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-2/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-3/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-4/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://blog.nintechnet.com/multiple-wordpress-plugins-fixed-csrf-vulnerabilities-part-5/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://plugins.trac.wordpress.org/browser/custom-css-js-php/trunk/modules/code/model.code.php#L85", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d21dc02f-789c-497e-9d01-02fa49bf9e30?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-52814", "published_date": "2025-06-27T12:15:43.213", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in ovatheme BRW allows PHP Local File Inclusion. This issue affects BRW: from n/a through 1.7.9."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en ovatheme BRW permite la inclusión local de archivos PHP. Este problema afecta a BRW desde n/d hasta la versión 1.7.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/ova-brw/vulnerability/wordpress-brw-1-7-9-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
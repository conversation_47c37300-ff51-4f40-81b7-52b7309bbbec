{"cve_id": "CVE-2025-53339", "published_date": "2025-06-27T14:15:56.797", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in devnex Devnex Addons For Elementor allows PHP Local File Inclusion. This issue affects Devnex Addons For Elementor: from n/a through 1.0.9."}, {"lang": "es", "value": "Vulnerabilidad de control incorrecto del nombre de archivo para la instrucción Include/Require en programas PHP ('Inclusión remota de archivos en PHP') en devnex Devnex Addons para Elementor permite la inclusión local de archivos en PHP. Este problema afecta a Devnex Addons para Elementor desde n/d hasta la versión 1.0.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/devnex-addons-for-elementor/vulnerability/wordpress-devnex-addons-for-elementor-plugin-1-0-9-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
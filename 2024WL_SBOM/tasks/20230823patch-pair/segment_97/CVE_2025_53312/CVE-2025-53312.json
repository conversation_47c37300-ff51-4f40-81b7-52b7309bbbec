{"cve_id": "CVE-2025-53312", "published_date": "2025-06-27T14:15:53.470", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Looks Awesome OnionBuzz allows Stored XSS. This issue affects OnionBuzz: from n/a through 1.0.7."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en Looks Awesome OnionBuzz permite XSS almacenado. Este problema afecta a OnionBuzz desde n/d hasta la versión 1.0.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/onionbuzz-viral-quiz/vulnerability/wordpress-onionbuzz-plugin-1-0-7-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
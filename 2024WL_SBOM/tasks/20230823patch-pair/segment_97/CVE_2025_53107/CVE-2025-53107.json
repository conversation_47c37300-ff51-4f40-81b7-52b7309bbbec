{"cve_id": "CVE-2025-53107", "published_date": "2025-07-01T18:15:25.990", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "@cyanheads/git-mcp-server is an MCP server designed to interact with Git repositories. Prior to version 2.1.5, there is a command injection vulnerability caused by the unsanitized use of input parameters within a call to child_process.exec, enabling an attacker to inject arbitrary system commands. Successful exploitation can lead to remote code execution under the server process's privileges. The server constructs and executes shell commands using unvalidated user input directly within command-line strings. This introduces the possibility of shell metacharacter injection (|, >, &&, etc.). An MCP Client can be instructed to execute additional actions for example via indirect prompt injection when asked to read git logs. This issue has been patched in version 2.1.5."}, {"lang": "es", "value": "@cyanheads/git-mcp-server es un servidor MCP diseñado para interactuar con repositorios Git. Antes de la versión 2.1.5, existía una vulnerabilidad de inyección de comandos causada por el uso no autorizado de parámetros de entrada en una llamada a child_process.exec, lo que permitía a un atacante inyectar comandos arbitrarios del sistema. Una explotación exitosa puede provocar la ejecución remota de código con los privilegios del proceso del servidor. El servidor construye y ejecuta comandos de shell utilizando entradas de usuario no validadas directamente dentro de las cadenas de la línea de comandos. Esto introduce la posibilidad de inyección de metacaracteres de shell (|, &gt;, &amp;&amp;, etc.). Se puede indicar a un cliente MCP que ejecute acciones adicionales, por ejemplo, mediante la inyección indirecta de prompts cuando se le solicita que lea los registros de Git. Este problema se ha corregido en la versión 2.1.5."}], "references": [{"url": "https://github.com/cyanheads/git-mcp-server/commit/0dbd6995ccdf76ab770b58013034365b2d06c4d9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cyanheads/git-mcp-server/releases/tag/v2.1.5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/cyanheads/git-mcp-server/security/advisories/GHSA-3q26-f695-pp76", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-5937", "published_date": "2025-06-28T08:15:25.143", "last_modified_date": "2025-07-08T14:46:09.287", "descriptions": [{"lang": "en", "value": "The MicroPayments – Fans Paysite: Paid Creator Subscriptions, Digital Assets, Wallet plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 3.2.0. This is due to missing or incorrect nonce validation on the adminOptions() function. This makes it possible for unauthenticated attackers to reset the plugin's settings via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento MicroPayments – Fans Paysite: Paid Creator Subscriptions, Digital Assets, Wallet para WordPress es vulnerable a Cross Site Request Forgery en todas las versiones hasta la 3.2.0 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la función adminOptions(). Esto permite que atacantes no autenticados restablezcan la configuración del complemento mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/paid-membership/trunk/inc/options.php#L1364", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3318389/#file0", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d80417bc-2bb2-4826-be03-796a7cd2825f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
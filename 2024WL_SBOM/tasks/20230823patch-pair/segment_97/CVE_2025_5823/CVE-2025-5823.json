{"cve_id": "CVE-2025-5823", "published_date": "2025-06-25T18:15:23.043", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial Serial Number Exposed Dangerous Method Information Disclosure Vulnerability. This vulnerability allows remote attackers to disclose sensitive information on affected installations of Autel MaxiCharger AC Wallbox Commercial EV chargers. Authentication is required to exploit this vulnerability.\n\nThe specific flaw exists within the implementation of the Autel Technician API. The issue results from an exposed dangerous method. An attacker can leverage this vulnerability to disclose credentials, leading to further compromise. Was ZDI-CAN-26351."}, {"lang": "es", "value": "Vulnerabilidad de divulgación de información por método peligroso expuesta en el número de serie del Autel MaxiCharger AC Wallbox Commercial. Esta vulnerabilidad permite a atacantes remotos divulgar información confidencial sobre las instalaciones afectadas de los cargadores para vehículos eléctricos Autel MaxiCharger AC Wallbox Commercial. Se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en la implementación de la API Autel Technician. El problema se debe a un método peligroso expuesto. Un atacante puede aprovechar esta vulnerabilidad para divulgar credenciales, lo que conlleva una mayor vulnerabilidad. Se denominó ZDI-CAN-26351."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-341/", "source": "<EMAIL>", "tags": []}]}
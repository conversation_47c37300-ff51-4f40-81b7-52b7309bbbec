{"cve_id": "CVE-2025-52896", "published_date": "2025-06-30T17:15:33.093", "last_modified_date": "2025-07-08T14:10:33.893", "descriptions": [{"lang": "en", "value": "Frappe is a full-stack web application framework. Prior to versions 14.94.2 and 15.57.0, authenticated users could upload carefully crafted malicious files via Data Import, leading to cross-site scripting (XSS). This issue has been patched in versions 14.94.2 and 15.57.0. There are no workarounds for this issue other than upgrading."}, {"lang": "es", "value": "Frappe es un framework de aplicaciones web integral. Antes de las versiones 14.94.2 y 15.57.0, los usuarios autenticados podían subir archivos maliciosos cuidadosamente manipulados mediante la importación de datos, lo que provocaba ataques de cross-site scripting (XSS). Este problema se ha corregido en las versiones 14.94.2 y 15.57.0. No existen soluciones alternativas aparte de actualizar."}], "references": [{"url": "https://github.com/frappe/frappe/commit/152fd09de5bca16b8d299d715a1f5df6fca3866f", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/frappe/frappe/commit/f11c53d4df745b58bd1c1c08e1634a2f5a55322a", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/frappe/frappe/pull/31483", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/frappe/frappe/security/advisories/GHSA-hv29-66qg-2v6p", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
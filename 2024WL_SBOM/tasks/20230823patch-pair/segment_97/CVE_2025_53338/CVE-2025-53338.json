{"cve_id": "CVE-2025-53338", "published_date": "2025-06-27T14:15:56.610", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in dor re.place allows Stored XSS. This issue affects re.place: from n/a through 0.2.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en dor re.place permite XSS almacenado. Este problema afecta a re.place desde n/d hasta la versión 0.2.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/replace/vulnerability/wordpress-re-place-plugin-0-2-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
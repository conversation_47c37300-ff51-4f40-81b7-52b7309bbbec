{"cve_id": "CVE-2025-53003", "published_date": "2025-07-01T02:15:22.413", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "The Janssen Project is an open-source identity and access management (IAM) platform. Prior to version 1.8.0, the Config API returns results without scope verification. This has a large internal surface attack area that exposes all sorts of information from the IDP including clients, users, scripts ..etc. This issue has been patched in version 1.8.0. A workaround for this vulnerability involves users forking and building the config api, patching it in their system following commit 92eea4d."}, {"lang": "es", "value": "Janssen Project es una plataforma de gestión de identidades y accesos (IAM) de código abierto. Antes de la versión 1.8.0, la API de configuración devolvía resultados sin verificación de alcance. Esto presentaba una amplia área de ataque interna que exponía todo tipo de información del IDP, incluyendo clientes, usuarios, scripts, etc. Este problema se solucionó en la versión 1.8.0. Una solución alternativa a esta vulnerabilidad consiste en que los usuarios bifurquen y compilen la API de configuración, y la parcheen en sus sistemas después del commit 92eea4d."}], "references": [{"url": "https://github.com/JanssenProject/jans/commit/92eea4d4637f1cae16ad2f07b2c16378ff3fc5f1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JanssenProject/jans/issues/11575", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JanssenProject/jans/releases/tag/v1.8.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JanssenProject/jans/security/advisories/GHSA-373j-mhpf-84wg", "source": "<EMAIL>", "tags": []}]}
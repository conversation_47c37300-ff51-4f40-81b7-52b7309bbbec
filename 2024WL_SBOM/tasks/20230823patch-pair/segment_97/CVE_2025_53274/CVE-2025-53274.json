{"cve_id": "CVE-2025-53274", "published_date": "2025-06-27T14:15:48.193", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Hossin Asaadi WP Permalink Translator allows Stored XSS. This issue affects WP Permalink Translator: from n/a through 1.7.6."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en Hossin Asaadi WP Permalink Translator permite XSS almacenado. Este problema afecta a WP Permalink Translator desde n/d hasta la versión 1.7.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-permalink-translator/vulnerability/wordpress-wp-permalink-translator-plugin-1-7-6-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-5035", "published_date": "2025-06-27T06:15:26.393", "last_modified_date": "2025-07-01T17:43:20.530", "descriptions": [{"lang": "en", "value": "The Firelight Lightbox WordPress plugin before 2.3.16 does not sanitise and escape title attributes before outputting them in the page, which could allow users with a role as low as contributors to perform stored Cross-Site Scripting attacks."}, {"lang": "es", "value": "El complemento Firelight Lightbox para WordPress anterior a la versión 2.3.16 no depura ni escapa los atributos de título antes de mostrarlos en la página, lo que podría permitir a los usuarios con un rol tan bajo como el de colaboradores realizar ataques de cross site scripting almacenado."}], "references": [{"url": "https://wpscan.com/vulnerability/5dca30af-4624-4a71-93be-00fa8dc00c97/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
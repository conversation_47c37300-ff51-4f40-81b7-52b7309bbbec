{"cve_id": "CVE-2025-52815", "published_date": "2025-06-27T12:15:43.377", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in AncoraThemes CityGov allows PHP Local File Inclusion. This issue affects CityGov: from n/a through 1.9."}, {"lang": "es", "value": "La vulnerabilidad \"Control inadecuado del nombre de archivo para la instrucción Include/Require en un programa PHP\" ('Inclusión remota de archivos PHP') en AncoraThemes CityGov permite la inclusión local de archivos PHP. Este problema afecta a CityGov desde n/d hasta la versión 1.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/citygov/vulnerability/wordpress-citygov-1-9-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
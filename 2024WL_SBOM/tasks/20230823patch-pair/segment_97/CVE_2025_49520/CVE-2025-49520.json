{"cve_id": "CVE-2025-49520", "published_date": "2025-06-30T21:15:30.913", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "A flaw was found in Ansible Automation Platform’s EDA component where user-supplied Git URLs are passed unsanitized to the git ls-remote command. This vulnerability allows an authenticated attacker to inject arguments and execute arbitrary commands on the EDA worker. In Kubernetes/OpenShift environments, this can lead to service account token theft and cluster access."}, {"lang": "es", "value": "Se detectó una falla en el componente EDA de Ansible Automation Platform, donde las URL de Git proporcionadas por el usuario se pasan sin depurar al comando git ls-remote. Esta vulnerabilidad permite a un atacante autenticado inyectar argumentos y ejecutar comandos arbitrarios en el trabajador de EDA. En entornos Kubernetes/OpenShift, esto puede provocar el robo de tokens de cuentas de servicio y el acceso al clúster."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2025:9986", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2025-49520", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2370812", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53292", "published_date": "2025-06-27T14:15:50.777", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in samsk WP DataTable allows DOM-Based XSS. This issue affects WP DataTable: from n/a through 0.2.7."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en samsk WP DataTable permite XSS basado en DOM. Este problema afecta a WP DataTable desde n/d hasta la versión 0.2.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-datatable/vulnerability/wordpress-wp-datatable-plugin-0-2-7-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
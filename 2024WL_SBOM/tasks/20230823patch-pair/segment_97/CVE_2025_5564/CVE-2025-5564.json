{"cve_id": "CVE-2025-5564", "published_date": "2025-06-26T02:15:21.957", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "The GC Social Wall plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'gc_social_wall' shortcode in all versions up to, and including, 1.15 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento GC Social Wall para WordPress es vulnerable a cross site scripting almacenado a través del shortcode 'gc_social_wall' en todas las versiones hasta la 1.15 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.svn.wordpress.org/gc-social-wall/trunk/GCSocialWall.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/cfe548ce-5dc9-4073-b755-d28e37720808?source=cve", "source": "<EMAIL>", "tags": []}]}
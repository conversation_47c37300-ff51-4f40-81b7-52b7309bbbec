{"cve_id": "CVE-2025-52997", "published_date": "2025-06-30T20:15:25.847", "last_modified_date": "2025-07-10T13:48:39.957", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit files. Prior to version 2.34.1, a missing password policy and brute-force protection makes the authentication process insecure. Attackers could mount a brute-force attack to retrieve the passwords of all accounts in a given instance. This issue has been patched in version 2.34.1."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. Antes de la versión 2.34.1, la falta de una política de contraseñas y la protección contra ataques de fuerza bruta hacían inseguro el proceso de autenticación. Los atacantes podían realizar un ataque de fuerza bruta para recuperar las contraseñas de todas las cuentas en una instancia dada. Este problema se ha corregido en la versión 2.34.1."}], "references": [{"url": "https://github.com/filebrowser/filebrowser/commit/bf37f88c32222ad9c186482bb97338a9c9b4a93c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-cm2r-rg7r-p7gg", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
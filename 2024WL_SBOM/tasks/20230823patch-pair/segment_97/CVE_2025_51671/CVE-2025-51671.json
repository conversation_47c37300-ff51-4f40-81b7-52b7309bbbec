{"cve_id": "CVE-2025-51671", "published_date": "2025-06-26T16:15:31.147", "last_modified_date": "2025-07-01T15:53:21.097", "descriptions": [{"lang": "en", "value": "A SQL injection vulnerability was discovered in the PHPGurukul Dairy Farm Shop Management System 1.3. The vulnerability allows remote attackers to execute arbitrary SQL code via the category and categorycode parameters in a POST request to the manage-categories.php file."}, {"lang": "es", "value": "Se descubrió una vulnerabilidad de inyección SQL en PHPGurukul Dairy Farm Shop Management System 1.3. Esta vulnerabilidad permite a atacantes remotos ejecutar código SQL arbitrario mediante los parámetros category y categorycode en una solicitud POST al archivo manage-categories.php."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Dairy-Farm-Shop-Management-System/SQL/SQL_injection_edit-category.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
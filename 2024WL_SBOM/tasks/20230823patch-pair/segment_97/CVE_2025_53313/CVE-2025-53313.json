{"cve_id": "CVE-2025-53313", "published_date": "2025-06-27T14:15:53.663", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in plumwd Twitch TV Embed Suite allows Stored XSS. This issue affects Twitch TV Embed Suite: from n/a through 2.1.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en plumwd Twitch TV Embed Suite permite XSS almacenado. Este problema afecta a Twitch TV Embed Suite desde n/d hasta la versión 2.1.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/twitch-tv-embed-suite/vulnerability/wordpress-twitch-tv-embed-suite-plugin-2-1-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
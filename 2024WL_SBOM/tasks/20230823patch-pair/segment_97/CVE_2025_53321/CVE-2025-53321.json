{"cve_id": "CVE-2025-53321", "published_date": "2025-06-27T14:15:54.767", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Raise The Money Raise The Money allows DOM-Based XSS. This issue affects Raise The Money: from n/a through 5.2."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Raise The Money Raise The Money permite XSS basado en DOM. Este problema afecta a Raise The Money desde n/d hasta la versión 5.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/raise-the-money/vulnerability/wordpress-raise-the-money-plugin-5-2-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
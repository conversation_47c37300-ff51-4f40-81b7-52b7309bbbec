{"cve_id": "CVE-2025-52808", "published_date": "2025-06-27T12:15:42.293", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in real-web RealtyElite allows PHP Local File Inclusion. This issue affects RealtyElite: from n/a through 1.0.0."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en real-web RealtyElite permite la inclusión local de archivos en PHP. Este problema afecta a RealtyElite desde n/d hasta la versión 1.0.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/realtyelite/vulnerability/wordpress-realtyelite-1-0-0-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
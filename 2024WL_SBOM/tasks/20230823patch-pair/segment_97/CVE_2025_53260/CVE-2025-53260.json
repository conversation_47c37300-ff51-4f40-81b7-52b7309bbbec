{"cve_id": "CVE-2025-53260", "published_date": "2025-06-27T14:15:45.647", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Unrestricted Upload of File with Dangerous Type vulnerability in getredhawkstudio File Manager Plugin For Wordpress allows Upload a Web Shell to a Web Server. This issue affects File Manager Plugin For Wordpress: from n/a through 7.5."}, {"lang": "es", "value": "La vulnerabilidad de subida sin restricciones de archivos con tipo peligroso en getredhawkstudio File Manager Plugin For Wordpress permite subir un shell web a un servidor web. Este problema afecta al plugin de gestión de archivos para WordPress desde la versión n/d hasta la 7.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/file-manager-plugin-for-wordpress/vulnerability/wordpress-file-manager-plugin-for-wordpress-plugin-7-5-arbitrary-file-upload-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-5824", "published_date": "2025-06-25T18:15:23.173", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial Origin Validation Error Authentication Bypass Vulnerability. This vulnerability allows network-adjacent attackers to bypass authentication on affected installations of Autel MaxiCharger AC Wallbox Commercial. An attacker must first obtain the ability to pair a malicious Bluetooth device with the target system in order to exploit this vulnerability.\n\nThe specific flaw exists within the handling of bluetooth pairing requests. The issue results from insufficient validation of the origin of commands. An attacker can leverage this vulnerability to bypass authentication on the system. Was ZDI-CAN-26353."}, {"lang": "es", "value": "Vulnerabilidad de omisión de autenticación por error de validación de origen en Autel MaxiCharger AC Wallbox Commercial. Esta vulnerabilidad permite a atacantes adyacentes a la red omitir la autenticación en las instalaciones afectadas de Autel MaxiCharger AC Wallbox Commercial. Para explotar esta vulnerabilidad, un atacante debe primero emparejar un dispositivo Bluetooth malicioso con el sistema objetivo. La falla específica se encuentra en la gestión de solicitudes de emparejamiento Bluetooth. El problema se debe a una validación insuficiente del origen de los comandos. Un atacante puede aprovechar esta vulnerabilidad para omitir la autenticación en el sistema. Anteriormente, se conocía como ZDI-CAN-26353."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-343/", "source": "<EMAIL>", "tags": []}]}
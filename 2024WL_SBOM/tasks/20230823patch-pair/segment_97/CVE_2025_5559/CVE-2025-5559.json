{"cve_id": "CVE-2025-5559", "published_date": "2025-06-26T02:15:21.807", "last_modified_date": "2025-07-16T15:25:26.087", "descriptions": [{"lang": "en", "value": "The TimeZoneCalculator plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'timezonecalculator_output' shortcode in all versions up to, and including, 3.37 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento TimeZoneCalculator para WordPress es vulnerable a cross site scripting almacenado a través del shortcode 'timezonecalculator_output' en todas las versiones hasta la 3.37 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wordpress.org/plugins/timezonecalculator/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f1e1a9ab-9ba9-45ff-aecd-b8953abc653a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
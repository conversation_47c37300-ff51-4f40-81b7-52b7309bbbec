{"cve_id": "CVE-2025-53256", "published_date": "2025-06-27T14:15:44.917", "last_modified_date": "2025-07-02T21:15:41.410", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in YayCommerce YaySMTP allows SQL Injection.This issue affects YaySMTP: from n/a through 2.6.5."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en YayCommerce YaySMTP permite la inyección SQL. Este problema afecta a YaySMTP desde n/d hasta la versión 6.8.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/yaysmtp/vulnerability/wordpress-yaysmtp-plugin-6-8-1-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
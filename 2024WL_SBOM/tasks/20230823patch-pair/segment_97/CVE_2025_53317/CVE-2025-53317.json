{"cve_id": "CVE-2025-53317", "published_date": "2025-06-27T14:15:54.243", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in AcmeeDesign WPShapere Lite allows Stored XSS. This issue affects WPShapere Lite: from n/a through 1.4."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en AcmeeDesign WPShapere Lite permite XSS almacenado. Este problema afecta a WPShapere Lite desde n/d hasta la versión 1.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wpshapere-lite/vulnerability/wordpress-wpshapere-lite-plugin-1-4-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
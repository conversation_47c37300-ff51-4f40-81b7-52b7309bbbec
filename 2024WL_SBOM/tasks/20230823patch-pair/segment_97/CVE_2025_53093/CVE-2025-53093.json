{"cve_id": "CVE-2025-53093", "published_date": "2025-06-27T18:15:50.773", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "TabberNeue is a MediaWiki extension that allows the wiki to create tabs. Starting in version 3.0.0 and prior to version 3.1.1, any user can insert arbitrary HTMLinto the DOM by inserting a payload into any allowed attribute of the `<tabber>` tag. Version 3.1.1 contains a patch for the bug."}, {"lang": "es", "value": "TabberNeue es una extensión de MediaWiki que permite crear pestañas en la wiki. A partir de la versión 3.0.0 y anteriores a la 3.1.1, cualquier usuario puede insertar HTML arbitrario en el DOM insertando un payload en cualquier atributo permitido de la etiqueta ``. La versión 3.1.1 incluye un parche para este error."}], "references": [{"url": "https://github.com/StarCitizenTools/mediawiki-extensions-TabberNeue/blob/3a23b703ce36cfc4128e7921841f68230be4059a/includes/Components/TabberComponentTabs.php#L15-L31", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/StarCitizenTools/mediawiki-extensions-TabberNeue/blob/3a23b703ce36cfc4128e7921841f68230be4059a/includes/Tabber.php#L76", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/StarCitizenTools/mediawiki-extensions-TabberNeue/blob/3a23b703ce36cfc4128e7921841f68230be4059a/includes/templates/Tabs.mustache#L1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/StarCitizenTools/mediawiki-extensions-TabberNeue/commit/4cdf217ef96da74a1503d1dd0bb0ed898fc2a612", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/StarCitizenTools/mediawiki-extensions-TabberNeue/commit/62ce0fcdf32bd3cfa77f92ff6b940459a14315fa", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/StarCitizenTools/mediawiki-extensions-TabberNeue/security/advisories/GHSA-jfj7-249r-7j2m", "source": "<EMAIL>", "tags": []}]}
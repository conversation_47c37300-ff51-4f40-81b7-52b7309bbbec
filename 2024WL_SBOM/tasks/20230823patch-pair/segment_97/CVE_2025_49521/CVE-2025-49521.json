{"cve_id": "CVE-2025-49521", "published_date": "2025-06-30T21:15:31.063", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "A flaw was found in the EDA component of the Ansible Automation Platform, where user-supplied Git branch or refspec values are evaluated as Jinja2 templates. This vulnerability allows authenticated users to inject expressions that execute commands or access sensitive files on the EDA worker. In OpenShift, it can lead to service account token theft."}, {"lang": "es", "value": "Se detectó una falla en el componente EDA de Ansible Automation Platform, donde los valores de rama o refspec de Git proporcionados por el usuario se evalúan como plantillas Jinja2. Esta vulnerabilidad permite a los usuarios autenticados inyectar expresiones que ejecutan comandos o acceden a archivos confidenciales en el trabajador EDA. En OpenShift, puede provocar el robo de tokens de cuentas de servicio."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2025:9986", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2025-49521", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2370817", "source": "<EMAIL>", "tags": []}]}
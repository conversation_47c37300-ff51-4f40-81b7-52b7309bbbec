{"cve_id": "CVE-2025-52890", "published_date": "2025-06-25T17:15:39.370", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Incus is a system container and virtual machine manager. When using an ACL on a device connected to a bridge, Incus versions 6.12 and 6.13generates nftables rules that partially bypass security options `security.mac_filtering`, `security.ipv4_filtering` and `security.ipv6_filtering`. This can lead to ARP spoofing on the bridge and to fully spoof another VM/container on the same bridge. Commit 254dfd2483ab8de39b47c2258b7f1cf0759231c8 contains a patch for the issue."}, {"lang": "es", "value": "Incus es un administrador de contenedores de sistema y máquinas virtuales. Al usar una ACL en un dispositivo conectado a un puente, las versiones 6.12 y 6.13 de Incus generan reglas de nftables que omiten parcialmente las opciones de seguridad `security.mac_filtering`, `security.ipv4_filtering` y `security.ipv6_filtering`. Esto puede provocar suplantación de ARP en el puente y suplantación completa de otra máquina virtual/contenedor en el mismo puente. El commit 254dfd2483ab8de39b47c2258b7f1cf0759231c8 contiene un parche para este problema."}], "references": [{"url": "https://github.com/lxc/incus/commit/254dfd2483ab8de39b47c2258b7f1cf0759231c8", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lxc/incus/security/advisories/GHSA-p7fw-vjjm-2rwp", "source": "<EMAIL>", "tags": []}]}
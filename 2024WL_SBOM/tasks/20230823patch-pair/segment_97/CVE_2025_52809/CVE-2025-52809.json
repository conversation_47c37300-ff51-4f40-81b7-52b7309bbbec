{"cve_id": "CVE-2025-52809", "published_date": "2025-06-27T12:15:42.463", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in <PERSON> National Weather Service Alerts allows PHP Local File Inclusion. This issue affects National Weather Service Alerts: from n/a through 1.3.5."}, {"lang": "es", "value": "La vulnerabilidad de control incorrecto del nombre de archivo para la instrucción Include/Require en un programa PHP («Inclusión remota de archivos en PHP») en John Russell National Weather Service Alerts  permite la inclusión local de archivos en PHP. Este problema afecta a las Alertas del Servicio Meteorológico Nacional desde la versión n/d hasta la 1.3.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/national-weather-service-alerts/vulnerability/wordpress-national-weather-service-alerts-1-3-5-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
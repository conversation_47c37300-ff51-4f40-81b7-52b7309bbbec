{"cve_id": "CVE-2025-53287", "published_date": "2025-06-27T14:15:50.250", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in <PERSON> Quick Favicon allows Stored XSS. This issue affects Quick Favicon: from n/a through 0.22.8."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en <PERSON> Quick Favicon permite XSS almacenado. Este problema afecta a Quick Favicon desde n/d hasta la versión 0.22.8."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/quick-favicon/vulnerability/wordpress-quick-favicon-plugin-0-22-8-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
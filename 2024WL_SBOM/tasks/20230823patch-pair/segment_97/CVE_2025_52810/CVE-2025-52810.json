{"cve_id": "CVE-2025-52810", "published_date": "2025-06-27T12:15:42.643", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Path Traversal vulnerability in TMRW-studio Katerio - Magazine allows PHP Local File Inclusion. This issue affects Katerio - Magazine: from n/a through 1.5.1."}, {"lang": "es", "value": "Vulnerabilidad de Path Traversal en TMRW-studio Katerio - Magazine permite la inclusión de archivos locales en PHP. Este problema afecta a Katerio - Magazine desde n/d hasta la versión 1.5.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/katerio/vulnerability/wordpress-katerio-magazine-1-5-1-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53331", "published_date": "2025-06-27T14:15:55.863", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in samcharrington RSS Digest allows Stored XSS. This issue affects RSS Digest: from n/a through 1.5."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en samcharrington RSS Digest permite XSS almacenado. Este problema afecta a RSS Digest: desde n/d hasta la versión 1.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/rss-digest/vulnerability/wordpress-rss-digest-plugin-1-5-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-51672", "published_date": "2025-06-26T15:15:23.140", "last_modified_date": "2025-07-01T18:03:27.253", "descriptions": [{"lang": "en", "value": "A time-based blind SQL injection vulnerability was identified in the PHPGurukul Dairy Farm Shop Management System 1.3. The vulnerability exists in the manage-companies.php file and allows remote attackers to execute arbitrary SQL code via the companyname parameter in a POST request."}, {"lang": "es", "value": "Se identificó una vulnerabilidad de inyección SQL ciega basada en tiempo en PHPGurukul Dairy Farm Shop Management System 1.3. La vulnerabilidad se encuentra en el archivo manage-companies.php y permite a atacantes remotos ejecutar código SQL arbitrario mediante el parámetro \"companyname\" en una solicitud POST."}], "references": [{"url": "https://github.com/rtnthakur/CVE/blob/main/PHPGurukul/Dairy-Farm-Shop-Management-System/SQL/SQL_injection_edit-company.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
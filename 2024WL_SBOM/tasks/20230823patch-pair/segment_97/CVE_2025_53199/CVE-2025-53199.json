{"cve_id": "CVE-2025-53199", "published_date": "2025-06-27T14:15:43.180", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in HT Plugins HT Slider For Elementor allows DOM-Based XSS. This issue affects HT Slider For Elementor: from n/a through 1.6.5."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en HT Plugins HT Slider For Elementor permite XSS basado en DOM. Este problema afecta a HT Slider para Elementor desde n/d hasta la versión 1.6.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/ht-slider-for-elementor/vulnerability/wordpress-ht-slider-for-elementor-plugin-1-6-5-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53314", "published_date": "2025-06-27T14:15:53.857", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in sh1zen WP Optimizer allows SQL Injection. This issue affects WP Optimizer: from n/a through 2.3.6."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en sh1zen WP Optimizer permite la inyección de SQL. Este problema afecta a WP Optimizer desde n/d hasta la versión 2.3.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-optimizer/vulnerability/wordpress-wp-optimizer-plugin-2-3-6-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
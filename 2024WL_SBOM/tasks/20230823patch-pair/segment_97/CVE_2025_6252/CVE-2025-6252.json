{"cve_id": "CVE-2025-6252", "published_date": "2025-06-28T05:15:24.710", "last_modified_date": "2025-07-07T14:51:20.410", "descriptions": [{"lang": "en", "value": "The Qi Addons For Elementor plugin for WordPress is vulnerable to Stored Cross-Site Scripting via several parameters in all versions up to, and including, 1.9.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Qi Addons para Elementor para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de varios parámetros en todas las versiones hasta la 1.9.1 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/qi-addons-for-elementor/trunk/assets/js/main.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=3318746%40qi-addons-for-elementor%2Ftrunk&old=3308494%40qi-addons-for-elementor%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0ef82a52-0a32-4dc4-b027-3d2098549404?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
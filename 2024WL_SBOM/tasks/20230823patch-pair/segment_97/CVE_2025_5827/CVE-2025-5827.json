{"cve_id": "CVE-2025-5827", "published_date": "2025-06-25T18:15:23.563", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial ble_process_esp32_msg Stack-based Buffer Overflow Remote Code Execution Vulnerability. This vulnerability allows network-adjacent attackers to execute arbitrary code on affected installations of Autel MaxiCharger AC Wallbox Commercial EV chargers. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the ble_process_esp32_msg function. The issue results from the lack of proper validation of the length of user-supplied data prior to copying it to a fixed-length stack-based buffer. An attacker can leverage this vulnerability to execute code in the context of the device. Was ZDI-CAN-26369."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por desbordamiento de búfer basado en pila en Autel MaxiCharger AC Wallbox Commercial ble_process_esp32_msg. Esta vulnerabilidad permite a atacantes adyacentes a la red ejecutar código arbitrario en las instalaciones afectadas de cargadores de vehículos eléctricos Autel MaxiCharger AC Wallbox Commercial. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en la función ble_process_esp32_msg. El problema se debe a la falta de una validación adecuada de la longitud de los datos proporcionados por el usuario antes de copiarlos a un búfer basado en pila de longitud fija. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del dispositivo. Anteriormente, se denominó ZDI-CAN-26369."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-346/", "source": "<EMAIL>", "tags": []}]}
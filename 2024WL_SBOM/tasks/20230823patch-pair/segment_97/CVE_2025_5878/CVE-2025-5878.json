{"cve_id": "CVE-2025-5878", "published_date": "2025-06-29T12:15:23.633", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "A vulnerability was found in ESAPI esapi-java-legacy and classified as problematic. This issue affects the interface Encoder.encodeForSQL of the SQL Injection Defense. An attack leads to an improper neutralization of special elements. The attack may be initiated remotely and an exploit has been disclosed to the public. The project was contacted early about this issue and handled it with an exceptional level of professionalism. Upgrading to version ******* is able to address this issue. Commit ID f75ac2c2647a81d2cfbdc9c899f8719c240ed512 is disabling the feature by default and any attempt to use it will trigger a warning. And commit ID e2322914304d9b1c52523ff24be495b7832f6a56 is updating the misleading Java class documentation to warn about the risks."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en ESAPI esapi-java-legacy y se clasificó como problemática. Este problema afecta a la interfaz Encoder.encodeForSQL de la Defensa contra Inyecciones SQL. Un ataque provoca una neutralización incorrecta de elementos especiales. El ataque puede ejecutarse en remoto y se ha hecho público el exploit. Se contactó al proyecto con anticipación sobre este problema y lo gestionó con un nivel excepcional de profesionalismo. La actualización a la versión ******* permite solucionar este problema. El ID del commit f75ac2c2647a81d2cfbdc9c899f8719c240ed512 deshabilita la función por defecto y cualquier intento de usarla generará una advertencia. El ID del commit e2322914304d9b1c52523ff24be495b7832f6a56 actualiza la documentación engañosa de la clase Java para advertir sobre los riesgos."}], "references": [{"url": "https://github.com/ESAPI/esapi-java-legacy/blob/develop/documentation/ESAPI-security-bulletin13.pdf", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ESAPI/esapi-java-legacy/commit/e2322914304d9b1c52523ff24be495b7832f6a56", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ESAPI/esapi-java-legacy/commit/f75ac2c2647a81d2cfbdc9c899f8719c240ed512", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ESAPI/esapi-java-legacy/releases/tag/esapi-*******", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/uglory-gll/javasec/blob/main/ESAPI.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.314321", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.314321", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.590149", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.590150", "source": "<EMAIL>", "tags": []}]}
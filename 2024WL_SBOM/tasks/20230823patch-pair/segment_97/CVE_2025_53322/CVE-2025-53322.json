{"cve_id": "CVE-2025-53322", "published_date": "2025-06-27T14:15:54.967", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Insertion of Sensitive Information Into Sent Data vulnerability in ZealousWeb Accept Authorize.NET Payments Using Contact Form 7 allows Retrieve Embedded Sensitive Data. This issue affects Accept Authorize.NET Payments Using Contact Form 7: from n/a through 2.5."}, {"lang": "es", "value": "La vulnerabilidad de inserción de información confidencial en los datos enviados en ZealousWeb Accept Authorize.NET Payments permite recuperar datos confidenciales incrustados. Este problema afecta a Accept Authorize.NET Payments Using Contact Form 7: desde n/d hasta la versión 2.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/accept-authorize-net-payments-using-contact-form-7/vulnerability/wordpress-accept-authorize-net-payments-using-contact-form-7-plugin-2-5-sensitive-data-exposure-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
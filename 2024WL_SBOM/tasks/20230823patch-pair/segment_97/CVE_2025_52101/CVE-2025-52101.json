{"cve_id": "CVE-2025-52101", "published_date": "2025-07-01T21:15:25.533", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "linjiashop <=0.9 is vulnerable to Incorrect Access Control. When using the default-generated JWT authentication, attackers can bypass the authentication and retrieve the encrypted \"password\" and \"salt\". The password can then be obtained through brute-force cracking."}, {"lang": "es", "value": "linjiashop &lt;=0.9 es vulnerable a un control de acceso incorrecto. Al usar la autenticación JWT predeterminada, los atacantes pueden omitirla y recuperar la \"contraseña\" y el \"salt\" cifrados. La contraseña puede obtenerse mediante un ataque de fuerza bruta."}], "references": [{"url": "https://gist.github.com/NSW111/33824ceb4d1b920671124f77abfe27e8", "source": "<EMAIL>", "tags": []}, {"url": "https://gitee.com/microapp/linjiashop", "source": "<EMAIL>", "tags": []}]}
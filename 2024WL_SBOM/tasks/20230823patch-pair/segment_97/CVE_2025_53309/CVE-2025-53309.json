{"cve_id": "CVE-2025-53309", "published_date": "2025-06-27T14:15:52.953", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Insertion of Sensitive Information Into Sent Data vulnerability in ZealousWeb Accept Stripe Payments Using Contact Form 7 allows Retrieve Embedded Sensitive Data. This issue affects Accept Stripe Payments Using Contact Form 7: from n/a through 3.0."}, {"lang": "es", "value": "La vulnerabilidad de inserción de información confidencial en los datos enviados en ZealousWeb Accept Stripe Payments Using Contact Form 7 permite recuperar datos confidenciales incrustados. Este problema afecta a Accept Stripe Payments Using Contact Form 7: desde n/d hasta la versión 3.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/accept-stripe-payments-using-contact-form-7/vulnerability/wordpress-accept-stripe-payments-using-contact-form-7-plugin-3-0-sensitive-data-exposure-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
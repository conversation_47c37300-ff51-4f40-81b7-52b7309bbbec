{"cve_id": "CVE-2025-5822", "published_date": "2025-06-25T18:15:22.900", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial Technician API Incorrect Authorization Privilege Escalation Vulnerability. This vulnerability allows remote attackers to escalate privileges on affected installations of Autel MaxiCharger AC Wallbox Commercial charging stations. An attacker must first obtain a low-privileged authorization token in order to exploit this vulnerability.\n\nThe specific flaw exists within the implementation of the Autel Technician API. The issue results from incorrect authorization. An attacker can leverage this vulnerability to escalate privileges to resources normally protected from the user. Was ZDI-CAN-26325."}, {"lang": "es", "value": "Vulnerabilidad de escalada de privilegios de autorización incorrecta en la API de Autel MaxiCharger AC Wallbox Commercial Technician. Esta vulnerabilidad permite a atacantes remotos escalar privilegios en las instalaciones afectadas de las estaciones de carga Autel MaxiCharger AC Wallbox Commercial. Para explotar esta vulnerabilidad, un atacante debe obtener primero un token de autorización con privilegios bajos. La falla específica se encuentra en la implementación de la API de Autel Technician. El problema se debe a una autorización incorrecta. Un atacante puede aprovechar esta vulnerabilidad para escalar privilegios a recursos que normalmente estarían protegidos del usuario. La vulnerabilidad era ZDI-CAN-26325."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-340/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52811", "published_date": "2025-06-27T12:15:42.830", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Path Traversal vulnerability in Creanncy Davenport - Versatile Blog and Magazine WordPress Theme allows PHP Local File Inclusion. This issue affects Davenport - Versatile Blog and Magazine WordPress Theme: from n/a through 1.3."}, {"lang": "es", "value": "Vulnerabilidad de Path Traversal en Creanncy Davenport - Versatile Blog and Magazine WordPress Theme permite la inclusión de archivos locales en PHP. Este problema afecta a Davenport - Versatile Blog and Magazine WordPress Theme: desde n/d hasta la versión 1.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/davenport/vulnerability/wordpress-davenport-versatile-blog-and-magazine-wordpress-theme-1-3-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
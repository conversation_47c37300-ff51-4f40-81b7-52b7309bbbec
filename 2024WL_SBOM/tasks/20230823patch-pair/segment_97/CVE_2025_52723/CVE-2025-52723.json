{"cve_id": "CVE-2025-52723", "published_date": "2025-06-27T12:15:39.590", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in codesupplyco Networker allows PHP Local File Inclusion. This issue affects Networker: from n/a through 1.2.0."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en Codesupplyco Networker permite la inclusión local de archivos en PHP. Este problema afecta a Networker desde n/d hasta la versión 1.2.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/networker/vulnerability/wordpress-networker-1-2-0-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
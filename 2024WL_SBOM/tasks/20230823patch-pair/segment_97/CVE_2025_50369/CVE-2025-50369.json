{"cve_id": "CVE-2025-50369", "published_date": "2025-06-27T16:15:25.407", "last_modified_date": "2025-07-01T18:13:49.520", "descriptions": [{"lang": "en", "value": "A Cross-Site Request Forgery (CSRF) vulnerability exists in the Manage Card functionality (/mcgs/admin/manage-card.php) of PHPGurukul Medical Card Generation System 1.0. The vulnerable endpoint allows an authorized admin to delete medical card records by sending a simple GET request without verifying the origin of the request."}, {"lang": "es", "value": "Existe una vulnerabilidad de Cross Site Request Forgery (CSRF) en la función de administración de tarjetas (/mcgs/admin/manage-card.php) de PHPGurukul Medical Card Generation System 1.0. El endpoint vulnerable permite a un administrador autorizado eliminar registros de tarjetas médicas mediante una simple solicitud GET sin verificar el origen de la solicitud."}], "references": [{"url": "https://github.com/1h3ll/CVEs/blob/main/CSRF-MANAGECARD_Medicalcard_Generations_System.md", "source": "<EMAIL>", "tags": ["Broken Link"]}]}
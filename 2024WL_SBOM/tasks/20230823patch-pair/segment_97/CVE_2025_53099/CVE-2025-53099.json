{"cve_id": "CVE-2025-53099", "published_date": "2025-07-01T15:15:26.277", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "Sentry is a developer-first error tracking and performance monitoring tool. Prior to version 25.5.0, an attacker with a malicious OAuth application registered with Sentry can take advantage of a race condition and improper handling of authorization code within Sentry to maintain persistence to a user's account. With a specially timed requests and redirect flows, an attacker could generate multiple authorization codes that could be used to exchange for access and refresh tokens. This was possible even after de-authorizing the particular application. This issue has been patched in version 25.5.0. Self-hosted Sentry users should upgrade to version 25.5.0 or higher. Sentry SaaS users do not need to take any action."}, {"lang": "es", "value": "Sentry es una herramienta de seguimiento de errores y monitorización del rendimiento centrada en el desarrollador. Antes de la versión 25.5.0, un atacante con una aplicación OAuth maliciosa registrada en Sentry podía aprovechar una condición de ejecución y la gestión incorrecta del código de autorización en Sentry para mantener la persistencia en la cuenta de un usuario. Con solicitudes y flujos de redirección con tiempos específicos, un atacante podía generar múltiples códigos de autorización que se podían usar para intercambiar tokens de acceso y actualización. Esto era posible incluso después de desautorizar la aplicación en cuestión. Este problema se ha corregido en la versión 25.5.0. Los usuarios de Sentry autoalojado deben actualizar a la versión 25.5.0 o superior. Los usuarios de Sentry SaaS no necesitan realizar ninguna acción."}], "references": [{"url": "https://github.com/getsentry/sentry/commit/57f0129e1e977b76fe8d16667a586578791a3dcd", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/commit/ab5fd932ca6bd46529ba3308b4669e3cee719b8f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/commit/e6241254aead969e6c8490a81cde9a01335df19d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/pull/85570", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/pull/85571", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/pull/86069", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/pull/86532", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/getsentry/sentry/security/advisories/GHSA-mgh8-h4xc-pfmj", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52774", "published_date": "2025-06-27T12:15:41.720", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Infility Infility Global allows Reflected XSS. This issue affects Infility Global: from n/a through 2.12.7."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Infility Infility Global permite XSS reflejado. Este problema afecta a Infility Global desde n/d hasta la versión 2.12.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/infility-global/vulnerability/wordpress-infility-global-2-12-6-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
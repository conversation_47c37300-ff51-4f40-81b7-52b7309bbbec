{"cve_id": "CVE-2025-53076", "published_date": "2025-06-30T03:15:25.843", "last_modified_date": "2025-07-03T15:04:16.297", "descriptions": [{"lang": "en", "value": "Improper Input Validation vulnerability in Samsung Open Source rLottie allows Overread Buffers.This issue affects rLottie: V0.2."}, {"lang": "es", "value": "La vulnerabilidad de validación de entrada incorrecta en Samsung Open Source rLottie permite sobrelectura de búferes. Este problema afecta a rLottie: V0.2."}], "references": [{"url": "https://github.com/Samsung/rlottie/pull/573", "source": "<EMAIL>", "tags": ["Issue Tracking"]}]}
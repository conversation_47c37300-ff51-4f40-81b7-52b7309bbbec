{"cve_id": "CVE-2025-5590", "published_date": "2025-06-26T02:15:22.260", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "The Owl carousel responsive plugin for WordPress is vulnerable to time-based SQL Injection via the ‘id’ parameter in all versions up to, and including, 1.9 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Contributor-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Owl carousel responsive para WordPress es vulnerable a la inyección SQL basada en tiempo mediante el parámetro 'id' en todas las versiones hasta la 1.9 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes autenticados, con acceso de colaborador o superior, añadir consultas SQL adicionales a consultas ya existentes que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/responsive-owl-carousel/trunk/query/db_gallery.php#L57", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/responsive-owl-carousel/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e1f230f5-d40c-43b2-82f2-c920dca9707f?source=cve", "source": "<EMAIL>", "tags": []}]}
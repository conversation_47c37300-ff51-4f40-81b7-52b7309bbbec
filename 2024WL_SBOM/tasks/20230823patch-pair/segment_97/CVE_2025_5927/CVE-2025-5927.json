{"cve_id": "CVE-2025-5927", "published_date": "2025-06-25T10:15:23.090", "last_modified_date": "2025-07-08T14:54:00.810", "descriptions": [{"lang": "en", "value": "The Everest Forms (Pro) plugin for WordPress is vulnerable to arbitrary file deletion due to insufficient file path validation in the delete_entry_files() function in all versions up to, and including, 1.9.4. This makes it possible for unauthenticated attackers to delete arbitrary files on the server, which can easily lead to remote code execution when the right file is deleted (such as wp-config.php). The vulnerability requires an admin to trigger the deletion via deletion of a form entry and cannot be carried out by the attacker alone."}, {"lang": "es", "value": "El complemento Everest Forms (Pro) para WordPress es vulnerable a la eliminación arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función delete_entry_files() en todas las versiones hasta la 1.9.4 incluida. Esto permite a atacantes no autenticados eliminar archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código al eliminar el archivo correcto (como wp-config.php). La vulnerabilidad requiere que un administrador active la eliminación mediante la eliminación de una entrada del formulario y no puede ser realizada por el atacante solo."}], "references": [{"url": "https://everestforms.net/changelog/", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://wordpress.org/plugins/everest-forms/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9e3a118f-4321-4579-a986-05ce077dc6b9?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
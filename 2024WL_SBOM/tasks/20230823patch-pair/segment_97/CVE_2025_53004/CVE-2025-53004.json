{"cve_id": "CVE-2025-53004", "published_date": "2025-06-30T21:15:31.563", "last_modified_date": "2025-07-10T13:42:12.150", "descriptions": [{"lang": "en", "value": "DataEase is an open source business intelligence and data visualization tool. Prior to version 2.10.11, there is a bypass vulnerability in Dataease's Redshift Data Source JDBC Connection Parameters. The sslfactory and sslfactoryarg parameters could trigger a bypass vulnerability. This issue has been patched in version 2.10.11."}, {"lang": "es", "value": "DataEase es una herramienta de código abierto de inteligencia empresarial y visualización de datos. Antes de la versión 2.10.11, existía una vulnerabilidad de omisión en los parámetros de conexión JDBC de la fuente de datos Redshift de DataEase. Los parámetros sslfactory y sslfactoryarg podían desencadenar una vulnerabilidad de omisión. Este problema se ha corregido en la versión 2.10.11."}], "references": [{"url": "https://github.com/dataease/dataease/security/advisories/GHSA-mfg2-qr5c-99pp", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
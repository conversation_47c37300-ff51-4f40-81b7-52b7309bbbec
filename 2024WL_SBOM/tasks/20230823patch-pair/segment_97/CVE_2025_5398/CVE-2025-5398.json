{"cve_id": "CVE-2025-5398", "published_date": "2025-06-27T10:15:26.470", "last_modified_date": "2025-07-07T15:31:31.283", "descriptions": [{"lang": "en", "value": "The Ninja Forms – The Contact Form Builder That Grows With You plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the use of a templating engine in all versions up to, and including, ******** due to insufficient output escaping on user data passed through the template. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Ninja Forms – The Contact Form Builder That Grows With You para WordPress es vulnerable a Cross-Site Scripting almacenado mediante el uso de un motor de plantillas en todas las versiones hasta la ******** incluida, debido a un escape de salida insuficiente en los datos de usuario que se pasan a través de la plantilla. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/ninja-forms/tags/3.10.1/assets/js/min/front-end.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3317181%40ninja-forms&new=3317181%40ninja-forms&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/92d106c6-a910-4f41-94d1-59f6b7f3aeb0?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
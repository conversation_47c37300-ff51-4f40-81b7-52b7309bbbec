{"cve_id": "CVE-2025-5338", "published_date": "2025-06-26T10:15:25.307", "last_modified_date": "2025-07-08T11:34:52.400", "descriptions": [{"lang": "en", "value": "The Royal Elementor Addons plugin for WordPress is vulnerable to Stored Cross-Site Scripting via multiple widgets in all versions up to, and including, 1.7.1024 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Royal Elementor Addons para WordPress es vulnerable a cross site scripting almacenado a través de múltiples widgets en todas las versiones hasta la 1.7.1024 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/royal-elementor-addons/tags/1.7.1022/assets/js/frontend.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3309082/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/royal-elementor-addons/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/201ff7b6-d72a-43c3-a7b1-c4f917c9d27f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
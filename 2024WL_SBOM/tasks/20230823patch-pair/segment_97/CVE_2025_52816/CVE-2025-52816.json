{"cve_id": "CVE-2025-52816", "published_date": "2025-06-27T12:15:43.610", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in themehunk Zita allows PHP Local File Inclusion. This issue affects <PERSON><PERSON>: from n/a through 1.6.5."}, {"lang": "es", "value": "Vulnerabilidad de control incorrecto del nombre de archivo para la instrucción Include/Require en un programa PHP ('Inclusión remota de archivos en PHP') en themehunk Zita permite la inclusión local de archivos en PHP. Este problema afecta a Zita desde n/d hasta la versión 1.6.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/zita/vulnerability/wordpress-zita-1-6-5-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
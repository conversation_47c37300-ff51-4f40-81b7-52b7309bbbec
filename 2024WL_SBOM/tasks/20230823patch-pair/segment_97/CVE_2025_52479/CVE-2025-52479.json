{"cve_id": "CVE-2025-52479", "published_date": "2025-06-25T16:15:27.017", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "HTTP.jl provides HTTP client and server functionality for Julia, and URIs.jl parses and works with Uniform Resource Identifiers (URIs). URIs.jl prior to version 1.6.0 and HTTP.jl prior to version 1.10.17 allows the construction of URIs containing CR/LF characters. If user input was not otherwise escaped or protected, this can lead to a CRLF injection attack. Users of HTTP.jl should upgrade immediately to HTTP.jl v1.10.17, and users of URIs.jl should upgrade immediately to URIs.jl v1.6.0. The check for valid URIs is now in the URI.jl package, and the latest version of HTTP.jl incorporates that fix. As a workaround, manually validate any URIs before passing them on to functions in this package."}, {"lang": "es", "value": "HTTP.jl proporciona funcionalidad de cliente y servidor HTTP para Julia, y URIs.jl analiza y trabaja con Identificadores Uniformes de Recursos (URI). Las versiones anteriores a URIs.jl 1.6.0 y 1.10.17 de HTTP.jl permiten la construcción de URIs con caracteres CR/LF. Si la entrada del usuario no se escapa ni se protege de otra forma, puede provocar un ataque de inyección CRLF. Los usuarios de HTTP.jl deben actualizar inmediatamente a HTTP.jl v1.10.17 y URIs.jl v1.6.0. La comprobación de URIs válidos ahora se encuentra en el paquete URI.jl, y la última versión de HTTP.jl incorpora esta corrección. Como solución alternativa, valide manualmente cualquier URI antes de pasarlo a las funciones de este paquete."}], "references": [{"url": "https://github.com/JuliaWeb/HTTP.jl/security/advisories/GHSA-4g68-4pxg-mw93", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JuliaWeb/URIs.jl/pull/66", "source": "<EMAIL>", "tags": []}]}
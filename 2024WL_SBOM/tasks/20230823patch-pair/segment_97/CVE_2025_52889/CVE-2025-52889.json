{"cve_id": "CVE-2025-52889", "published_date": "2025-06-25T17:15:39.200", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Incus is a system container and virtual machine manager. When using an ACL on a device connected to a bridge, Incus version 6.12 and 6.13 generates nftables rules for local services (DHCP, DNS...) that partially bypass security options `security.mac_filtering`, `security.ipv4_filtering` and `security.ipv6_filtering`. This can lead to DHCP pool exhaustion and opens the door for other attacks. A patch is available at commit 2516fb19ad8428454cb4edfe70c0a5f0dc1da214."}, {"lang": "es", "value": "Incus es un contenedor de sistema y administrador de máquinas virtuales. Al usar una ACL en un dispositivo conectado a un puente, las versiones 6.12 y 6.13 de Incus generan reglas de nftables para servicios locales (DHCP, DNS, etc.) que omiten parcialmente las opciones de seguridad `security.mac_filtering`, `security.ipv4_filtering` y `security.ipv6_filtering`. Esto puede provocar el agotamiento del pool de DHCP y abrir la puerta a otros ataques. Hay un parche disponible en el commit 2516fb19ad8428454cb4edfe70c0a5f0dc1da214."}], "references": [{"url": "https://github.com/lxc/incus/commit/2516fb19ad8428454cb4edfe70c0a5f0dc1da214", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lxc/incus/commit/a7c33301738aede3c035063e973b1d885d9bac7c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lxc/incus/security/advisories/GHSA-9q7c-qmhm-jv86", "source": "<EMAIL>", "tags": []}]}
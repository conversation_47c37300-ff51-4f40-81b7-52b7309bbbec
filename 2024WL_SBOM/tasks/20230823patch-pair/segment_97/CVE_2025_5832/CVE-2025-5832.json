{"cve_id": "CVE-2025-5832", "published_date": "2025-06-25T18:15:24.077", "last_modified_date": "2025-07-08T14:52:30.267", "descriptions": [{"lang": "en", "value": "Pioneer DMH-WT7600NEX Software Update Signing Insufficient Verification of Data Authenticity Vulnerability. This vulnerability allows physically present attackers to execute arbitrary code on affected installations of Pioneer DMH-WT7600NEX devices. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the software update verification process. The issue results from the lack of validating all the data in the software update. An attacker can leverage this vulnerability to execute code in the context of the device. Was ZDI-CAN-26079."}, {"lang": "es", "value": "Vulnerabilidad de verificación insuficiente de la autenticidad de los datos en la firma de actualizaciones de software del Pioneer DMH-WT7600NEX. Esta vulnerabilidad permite a atacantes con presencia física ejecutar código arbitrario en las instalaciones afectadas de los dispositivos Pioneer DMH-WT7600NEX. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en el proceso de verificación de actualizaciones de software. El problema se debe a la falta de validación de todos los datos en la actualización de software. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del dispositivo. Era ZDI-CAN-26079."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-352/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
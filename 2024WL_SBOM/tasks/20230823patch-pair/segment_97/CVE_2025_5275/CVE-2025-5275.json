{"cve_id": "CVE-2025-5275", "published_date": "2025-06-26T03:15:23.860", "last_modified_date": "2025-07-08T11:32:22.210", "descriptions": [{"lang": "en", "value": "The Charitable – Donation Plugin for WordPress – Fundraising with Recurring Donations & More plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the privacy settings fields in all versions up to, and including, ******* due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level access, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled.\r\nThis issue was partially fixed in version ******* and fully fixed in version *******."}, {"lang": "es", "value": "El complemento Charitable – Donation Plugin para WordPress – Fundraising with Recurring Donations &amp; More para WordPress es vulnerable a cross site scripting almacenado a través de los campos de configuración de privacidad en todas las versiones hasta la ******* incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de administrador, inyectar scripts web arbitrarios en las páginas que se ejecutarán al acceder un usuario a una página inyectada. Esto solo afecta a instalaciones multisitio y a instalaciones donde se ha deshabilitado unfiltered_html. Este problema se solucionó parcialmente en la versión ******* y completamente en la *******."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/charitable/tags/*******/templates/form-fields/checkbox.php#L40", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/453d8918-32dc-43d6-8969-71f719536891?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
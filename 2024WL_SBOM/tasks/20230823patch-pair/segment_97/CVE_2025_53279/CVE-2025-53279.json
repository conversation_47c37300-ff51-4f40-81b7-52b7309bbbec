{"cve_id": "CVE-2025-53279", "published_date": "2025-06-27T14:15:49.133", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Aman Popup addon for Ninja Forms allows DOM-Based XSS. This issue affects Popup addon for Ninja Forms: from n/a through 3.4."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Aman Popup addon for Ninja Forms permite XSS basado en DOM. Este problema afecta al complemento Popup para Ninja Forms desde la versión n/d hasta la 3.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/popup-addon-for-ninja-forms/vulnerability/wordpress-popup-addon-for-ninja-forms-plugin-3-4-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
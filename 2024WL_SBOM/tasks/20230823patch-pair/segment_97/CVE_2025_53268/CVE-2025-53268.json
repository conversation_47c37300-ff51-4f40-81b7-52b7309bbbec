{"cve_id": "CVE-2025-53268", "published_date": "2025-06-27T14:15:47.127", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in ryanpcmcquen Import external attachments allows Cross Site Request Forgery. This issue affects Import external attachments: from n/a through 1.5.12."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en ryanpcmcquen Import external attachments permite Cross Site Request Forgery. Este problema afecta a la importación de archivos adjuntos externos desde n/d hasta la versión 1.5.12."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/import-external-attachments/vulnerability/wordpress-import-external-attachments-plugin-1-5-12-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53091", "published_date": "2025-06-27T15:15:25.957", "last_modified_date": "2025-07-08T14:48:01.653", "descriptions": [{"lang": "en", "value": "WeGIA is an open source web manager with a focus on the Portuguese language and charitable institutions. A Time-Based Blind SQL Injection vulnerability was discovered in version 3.3.3 the almox parameter of the `/controle/getProdutosPorAlmox.php` endpoint. This issue allows any unauthenticated attacker to inject arbitrary SQL queries, potentially leading to unauthorized data access or further exploitation depending on database configuration. Version 3.4.0 fixes the issue."}, {"lang": "es", "value": "WeGIA es un gestor web de código abierto centrado en el idioma portugués y las instituciones benéficas. Se descubrió una vulnerabilidad de inyección SQL ciega basada en el tiempo en la versión 3.3.3, en el parámetro almox del endpoint `/controle/getProdutosPorAlmox.php`. Este problema permite a cualquier atacante no autenticado inyectar consultas SQL arbitrarias, lo que podría provocar acceso no autorizado a los datos o una mayor explotación, dependiendo de la configuración de la base de datos. La versión 3.4.0 corrige el problema."}], "references": [{"url": "https://github.com/LabRedesCefetRJ/WeGIA/security/advisories/GHSA-pmf9-2rc3-vvxx", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
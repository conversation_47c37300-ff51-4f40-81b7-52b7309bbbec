{"cve_id": "CVE-2025-5842", "published_date": "2025-06-26T10:15:25.537", "last_modified_date": "2025-07-08T11:35:12.807", "descriptions": [{"lang": "en", "value": "The Modern Design Library plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘class’ parameter in all versions up to, and including, 1.1.4 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Modern Design Library para WordPress es vulnerable a cross site scripting almacenado a través del parámetro 'class' en todas las versiones hasta la 1.1.4 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/mdl-shortcodes/trunk/mdl-shortcodes.php#L197", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3317171/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/mdl-shortcodes/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1be519d5-b505-4b5d-9f14-c8544e8f8298?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
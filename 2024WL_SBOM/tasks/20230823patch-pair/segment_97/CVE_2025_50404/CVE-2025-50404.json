{"cve_id": "CVE-2025-50404", "published_date": "2025-07-01T15:15:26.043", "last_modified_date": "2025-07-08T16:15:58.260", "descriptions": [{"lang": "en", "value": "Intelbras RX1500 Router v2.2.17 and before is vulnerable to Integer Overflow. The websReadEvent function incorrectly uses the int type when processing the \"command\" field of the http header, causing the array to cross the boundary and overwrite other fields in the array."}, {"lang": "es", "value": "Intelbras RX1500 Router v2.2.17 y versiones anteriores es vulnerable a desbordamiento de enteros. La función websReadEvent utiliza incorrectamente el tipo int al procesar el campo \"command\" del encabezado http, lo que provoca que la matriz cruce el límite y sobrescriba otros campos de la matriz."}], "references": [{"url": "https://github.com/feiwuxingxie/cve/blob/main/Intelbras/vul01/01.md", "source": "<EMAIL>", "tags": []}, {"url": "https://www.intelbras.com/en", "source": "<EMAIL>", "tags": []}]}
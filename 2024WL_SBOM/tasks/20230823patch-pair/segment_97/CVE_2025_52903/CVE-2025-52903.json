{"cve_id": "CVE-2025-52903", "published_date": "2025-06-26T19:15:21.587", "last_modified_date": "2025-06-30T18:39:09.973", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit files. In version 2.32.0, the Command Execution feature of File Browser only allows the execution of shell command which have been predefined on a user-specific allowlist. Many tools allow the execution of arbitrary different commands, rendering this limitation void. The concrete impact depends on the commands being granted to the attacker, but the large number of standard commands allowing the execution of subcommands makes it likely that every user having the `Execute commands` permissions can exploit this vulnerability. Everyone who can exploit it will have full code execution rights with the uid of the server process. Until this issue is fixed, the maintainers recommend to completely disable `Execute commands` for all accounts. Since the command execution is an inherently dangerous feature that is not used by all deployments, it should be possible to completely disable it in the application's configuration. As a defense-in-depth measure, organizations not requiring command execution should operate the Filebrowser from a distroless container image. A patch version has been pushed to disable the feature for all existent installations, and making it opt-in. A warning has been added to the documentation and is printed on the console if the feature is enabled. Due to the project being in maintenance-only mode, the bug has not been fixed. The fix is tracked on pull request 5199."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. En la versión 2.32.0, la función de Ejecución de Comandos del Explorador de Archivos solo permite la ejecución de comandos de shell predefinidos en una lista de permitidos específica del usuario. Muchas herramientas permiten la ejecución de comandos arbitrarios, lo que invalida esta limitación. El impacto concreto depende de los comandos otorgados al atacante, pero la gran cantidad de comandos estándar que permiten la ejecución de subcomandos hace probable que cualquier usuario con permisos de \"Ejecutar comandos\" pueda explotar esta vulnerabilidad. Cualquiera que pueda explotarla tendrá plenos derechos de ejecución de código con el uid del proceso del servidor. Hasta que se solucione este problema, los mantenedores recomiendan deshabilitar completamente la función de \"Ejecutar comandos\" para todas las cuentas. Dado que la ejecución de comandos es una función inherentemente peligrosa que no se utiliza en todas las implementaciones, debería ser posible deshabilitarla por completo en la configuración de la aplicación. Como medida de defensa a fondo, las organizaciones que no requieran la ejecución de comandos deberían operar el Explorador de archivos desde una imagen de contenedor sin distribución. Se ha publicado una versión de parche para deshabilitar la función en todas las instalaciones existentes y habilitarla. Se ha añadido una advertencia a la documentación, que se muestra en la consola si la función está habilitada. Debido a que el proyecto se encuentra en modo de mantenimiento, el error no se ha corregido. La corrección se encuentra en la solicitud de incorporación de cambios 5199."}], "references": [{"url": "https://github.com/GoogleContainerTools/distroless", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/filebrowser/filebrowser/issues/5199", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-3q2w-42mv-cph4", "source": "<EMAIL>", "tags": []}, {"url": "https://manpages.debian.org/bookworm/util-linux/prlimit.1.en.html", "source": "<EMAIL>", "tags": []}]}
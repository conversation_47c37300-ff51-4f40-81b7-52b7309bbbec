{"cve_id": "CVE-2025-5588", "published_date": "2025-06-26T02:15:22.107", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "The Image Editor by Pixo plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘download’ parameter in all versions up to, and including, 2.3.6 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Image Editor by Pixo para WordPress es vulnerable a cross site scripting almacenado a través del parámetro \"download\" en todas las versiones hasta la 2.3.6 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/image-editor-by-pixo/trunk/frontend.php#L42", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3315303/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/image-editor-by-pixo/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1036a34d-ec03-4bec-8455-02c83fdb8b36?source=cve", "source": "<EMAIL>", "tags": []}]}
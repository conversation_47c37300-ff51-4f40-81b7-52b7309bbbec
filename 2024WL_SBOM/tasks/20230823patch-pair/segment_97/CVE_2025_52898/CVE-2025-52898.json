{"cve_id": "CVE-2025-52898", "published_date": "2025-06-30T18:15:25.773", "last_modified_date": "2025-07-08T14:43:50.023", "descriptions": [{"lang": "en", "value": "Frappe is a full-stack web application framework. Prior to versions 14.94.3 and 15.58.0, a carefully crafted request could lead to a malicious actor getting access to a user's password reset token. This can only be exploited on self hosted instances configured in a certain way. Frappe Cloud users are safe. This issue has been patched in versions 14.94.3 and 15.58.0. Workarounds for this issue involve verifying password reset URLs before clicking on them or upgrading for self hosted users."}, {"lang": "es", "value": "Frappe es un framework de aplicaciones web integral. Antes de las versiones 14.94.3 y 15.58.0, una solicitud cuidadosamente manipulada podía permitir que un atacante malicioso accediera al token de restablecimiento de contraseña de un usuario. Esto solo se puede explotar en instancias alojadas en servidores propios con una configuración específica. Los usuarios de Frappe Cloud están seguros. Este problema se ha corregido en las versiones 14.94.3 y 15.58.0. Las soluciones alternativas incluyen verificar las URL de restablecimiento de contraseña antes de acceder a ellas o actualizar la versión para usuarios alojados en servidores propios."}], "references": [{"url": "https://github.com/frappe/frappe/commit/52e31337a6c964189c8b883a2f7bc3a28ab374f2", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/frappe/frappe/commit/5b4849b1ab5fd796b306312745b4e202b0e90d66", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/frappe/frappe/pull/31522", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/frappe/frappe/security/advisories/GHSA-p284-r7rh-wq7j", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
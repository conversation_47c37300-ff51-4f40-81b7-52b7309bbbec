{"cve_id": "CVE-2025-53259", "published_date": "2025-06-27T14:15:45.467", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in nicdark Hotel Booking allows PHP Local File Inclusion. This issue affects Hotel Booking: from n/a through 3.7."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en nicdark Hotel Booking permite la inclusión local de archivos en PHP. Este problema afecta a Hotel Booking desde n/d hasta la versión 3.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/nd-booking/vulnerability/wordpress-hotel-booking-plugin-3-7-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
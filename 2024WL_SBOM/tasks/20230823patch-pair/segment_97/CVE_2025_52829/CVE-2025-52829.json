{"cve_id": "CVE-2025-52829", "published_date": "2025-06-27T12:15:44.803", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in DirectIQ DirectIQ Email Marketing allows SQL Injection. This issue affects DirectIQ Email Marketing: from n/a through 2.0."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en DirectIQ DirectIQ Email Marketing permite la inyección SQL. Este problema afecta a DirectIQ Email Marketing desde n/d hasta la versión 2.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/directiq-wp/vulnerability/wordpress-directiq-email-marketing-2-0-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
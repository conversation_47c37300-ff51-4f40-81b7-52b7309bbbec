{"cve_id": "CVE-2025-49003", "published_date": "2025-06-26T14:15:30.433", "last_modified_date": "2025-07-09T18:47:27.597", "descriptions": [{"lang": "en", "value": "DataEase is an open source business intelligence and data visualization tool. Prior to version 2.10.11, a threat actor may take advantage of a feature in Java in which the character \"ı\" becomes \"I\" when converted to uppercase, and the character \"ſ\" becomes \"S\" when converted to uppercase. A threat actor who uses a carefully crafted message that exploits this character conversion can cause remote code execution. The vulnerability has been fixed in v2.10.11. No known workarounds are available."}, {"lang": "es", "value": "DataEase es una herramienta de código abierto de inteligencia empresarial y visualización de datos. Antes de la versión 2.10.11, un atacante podía aprovechar una función de Java que permitía que el carácter \"?\" se convirtiera en \"I\" al convertirse a mayúsculas, y el carácter \"?\" se convirtiera en \"S\" al convertirse a mayúsculas. Un atacante que utiliza un mensaje cuidadosamente manipulado que aprovecha esta conversión de caracteres puede provocar la ejecución remota de código. Esta vulnerabilidad se ha corregido en la versión 2.10.11. No se conocen soluciones alternativas."}], "references": [{"url": "https://github.com/dataease/dataease/security/advisories/GHSA-x97w-69ff-r55q", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
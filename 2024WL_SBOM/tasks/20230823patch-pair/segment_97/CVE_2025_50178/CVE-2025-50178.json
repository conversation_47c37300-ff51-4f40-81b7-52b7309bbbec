{"cve_id": "CVE-2025-50178", "published_date": "2025-06-25T16:15:26.693", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "GitForge.jl is a unified interface for interacting with Git \"forges.\" Versions prior to 0.4.3 lack input validation for user provided values in certain functions. In the `GitForge.get_repo` function for GitHub, the user can provide any string for the owner and repo fields. These inputs are not validated or safely encoded and are sent directly to the server. This means a user can add path traversal patterns like `../` in the input to access any other endpoints on api.github.com that were not intended. Version 0.4.3 contains a patch for the issue. No known workarounds are available."}, {"lang": "es", "value": "GitForge.jl es una interfaz unificada para interactuar con las forjas de Git. Las versiones anteriores a la 0.4.3 carecen de validación de entrada para los valores proporcionados por el usuario en ciertas funciones. En la función `GitForge.get_repo` de GitHub, el usuario puede proporcionar cualquier cadena para los campos \"propietario\" y \"repositorio\". Estas entradas no se validan ni codifican de forma segura y se envían directamente al servidor. Esto significa que un usuario puede añadir patrones de path traversal como `../` en la entrada para acceder a cualquier otro endpoint en api.github.com que no estuviera previsto. La versión 0.4.3 incluye un parche para este problema. No se conocen soluciones alternativas."}], "references": [{"url": "https://github.com/JuliaWeb/GitForge.jl/pull/50", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JuliaWeb/GitForge.jl/security/advisories/GHSA-g2xx-229f-3qjm", "source": "<EMAIL>", "tags": []}]}
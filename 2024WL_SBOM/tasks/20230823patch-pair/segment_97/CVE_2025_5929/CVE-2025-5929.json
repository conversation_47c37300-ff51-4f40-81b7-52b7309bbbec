{"cve_id": "CVE-2025-5929", "published_date": "2025-06-26T03:15:24.953", "last_modified_date": "2025-07-08T11:32:50.840", "descriptions": [{"lang": "en", "value": "The The Countdown plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘clientId’ parameter in all versions up to, and including, 2.0.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento The Countdown para WordPress es vulnerable a cross site scripting almacenado a través del parámetro 'clientId' en todas las versiones hasta la 2.0.1 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/the-countdown/tags/2.0.1/the-countdown.php#L95", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://wordpress.org/plugins/the-countdown/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/34578df8-661c-4c54-b06c-e1d787ca3c55?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-52902", "published_date": "2025-06-26T15:15:23.687", "last_modified_date": "2025-07-10T01:09:35.847", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit files. The Markdown preview function of File Browser prior to v2.33.7 is vulnerable to Stored Cross-Site-Scripting (XSS). Any JavaScript code that is part of a Markdown file uploaded by a user will be executed by the browser. Version 2.33.7 contains a fix for the issue."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. La función de previsualización de Markdown del Explorador de Archivos anterior a la versión 2.33.7 es vulnerable a ataques de Cross-Site-Scripting (XSS). Cualquier código JavaScript que forme parte de un archivo Markdown subido por un usuario será ejecutado por el navegador. La versión 2.33.7 contiene una solución para este problema."}], "references": [{"url": "https://github.com/filebrowser/filebrowser/commit/f19943a42e8e092e811dffbe9f4623dac36f1f0d", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-4wx8-5gm2-2j97", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
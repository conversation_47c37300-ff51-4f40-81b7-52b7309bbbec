{"cve_id": "CVE-2025-52480", "published_date": "2025-06-25T17:15:38.590", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Registrator is a GitHub app that automates creation of registration pull requests for julia packages to the General registry. Prior to version 1.9.5, if the clone URL returned by GitHub is malicious (or can be injected using upstream vulnerabilities), an argument injection is possible in the `gettreesha()` function. This can then lead to a potential remote code execution. Users should upgrade immediately to v1.9.5 to receive a patch. All prior versions are vulnerable. No known workarounds are available."}, {"lang": "es", "value": "Registrator es una aplicación de GitHub que automatiza la creación de solicitudes de extracción de registro para paquetes de Julia en el registro general. Antes de la versión 1.9.5, si la URL del clon devuelta por GitHub era maliciosa (o podía inyectarse mediante vulnerabilidades de origen), era posible inyectar argumentos en la función `gettreesha()`. Esto podría provocar una posible ejecución remota de código. Los usuarios deben actualizar inmediatamente a la versión 1.9.5 para recibir un parche. Todas las versiones anteriores son vulnerables. No se conocen soluciones alternativas."}], "references": [{"url": "https://github.com/JuliaRegistries/Registrator.jl/pull/449", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JuliaRegistries/Registrator.jl/security/advisories/GHSA-w8jv-rg3h-fc68", "source": "<EMAIL>", "tags": []}]}
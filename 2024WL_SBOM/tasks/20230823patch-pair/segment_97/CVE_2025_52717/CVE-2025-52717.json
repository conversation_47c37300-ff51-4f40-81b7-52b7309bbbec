{"cve_id": "CVE-2025-52717", "published_date": "2025-06-27T12:15:39.220", "last_modified_date": "2025-07-11T14:21:09.873", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in chrisbadgett LifterLMS allows SQL Injection. This issue affects LifterLMS: from n/a through 8.0.6."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en chrisbadgett LifterLMS permite la inyección SQL. Este problema afecta a LifterLMS desde n/d hasta la versión 8.0.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lifterlms/vulnerability/wordpress-lifterlms-8-0-6-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
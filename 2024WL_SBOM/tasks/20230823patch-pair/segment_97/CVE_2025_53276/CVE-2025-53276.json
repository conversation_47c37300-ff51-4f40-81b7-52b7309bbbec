{"cve_id": "CVE-2025-53276", "published_date": "2025-06-27T14:15:48.567", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in omnipressteam Omnipress allows DOM-Based XSS. This issue affects Omnipress: from n/a through 1.6.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en omnipressteam Omnipress permite XSS basado en DOM. Este problema afecta a Omnipress desde n/d hasta la versión 1.6.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/omnipress/vulnerability/wordpress-omnipress-plugin-1-6-3-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-49886", "published_date": "2025-06-27T12:15:38.663", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in WebGeniusLab Zikzag Core allows PHP Local File Inclusion. This issue affects Zikzag Core: from n/a through 1.4.5."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en WebGeniusLab Zikzag Core permite la inclusión local de archivos PHP. Este problema afecta a Zikzag Core desde n/d hasta la versión 1.4.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/zikzag-core/vulnerability/wordpress-zikzag-core-1-4-5-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52901", "published_date": "2025-06-30T20:15:25.390", "last_modified_date": "2025-07-10T14:48:43.007", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit files. Prior to version 2.33.9, access tokens are used as GET parameters. The JSON Web Token (JWT) which is used as a session identifier will get leaked to anyone having access to the URLs accessed by the user. This will give an attacker full access to a user's account and, in consequence, to all sensitive files the user has access to. This issue has been patched in version 2.33.9."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. Antes de la versión 2.33.9, los tokens de acceso se utilizaban como parámetros GET. El token web JSON (JWT), utilizado como identificador de sesión, se filtraba a cualquiera que tuviera acceso a las URL a las que accedía el usuario. Esto otorgaba a un atacante acceso total a la cuenta del usuario y, en consecuencia, a todos los archivos confidenciales a los que tenía acceso. Este problema se ha corregido en la versión 2.33.9."}], "references": [{"url": "https://github.com/filebrowser/filebrowser/commit/d5b39a14fd3fc0d1c364116b41289484df7c27b2", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/filebrowser/filebrowser/releases/tag/v2.33.9", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-rmwh-g367-mj4x", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
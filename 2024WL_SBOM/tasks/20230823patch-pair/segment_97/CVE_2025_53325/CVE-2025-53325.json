{"cve_id": "CVE-2025-53325", "published_date": "2025-06-27T14:15:55.350", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in <PERSON><PERSON>r Beauty Contact Popup Form allows Stored XSS. This issue affects Beauty Contact Popup Form: from n/a through 6.0."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Di<PERSON> kumar Beauty Contact Popup Form permite XSS almacenado. Este problema afecta al formulario emergente de contacto de Beauty: desde n/d hasta la versión 6.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/beauty-contact-popup-form/vulnerability/wordpress-beauty-contact-popup-form-plugin-6-0-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53295", "published_date": "2025-06-27T14:15:51.317", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Missing Authorization vulnerability in iCount iCount Payment Gateway allows Accessing Functionality Not Properly Constrained by ACLs. This issue affects iCount Payment Gateway: from n/a through 2.0.6."}, {"lang": "es", "value": "La vulnerabilidad de falta de autorización en iCount iCount Payment Gateway permite acceder a funciones no restringidas correctamente por las ACL. Este problema afecta a la pasarela de pago iCount desde n/d hasta la versión 2.0.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/icount/vulnerability/wordpress-icount-payment-gateway-plugin-2-0-6-broken-access-control-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
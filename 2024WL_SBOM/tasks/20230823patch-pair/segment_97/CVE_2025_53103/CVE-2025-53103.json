{"cve_id": "CVE-2025-53103", "published_date": "2025-07-01T18:15:25.837", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "JUnit is a testing framework for Java and the JVM. From version 5.12.0 to 5.13.1, JUnit's support for writing Open Test Reporting XML files can leak Git credentials. The impact depends on the level of the access token exposed through the OpenTestReportGeneratingListener. If these test reports are published or stored anywhere public, then there is the possibility that a rouge attacker can steal the token and perform elevated actions by impersonating the user or app. This issue as been patched in version 5.13.2."}, {"lang": "es", "value": "JUnit es un framework de pruebas para Java y la JVM. Desde la versión 5.12.0 hasta la 5.13.1, la compatibilidad de JUnit con la escritura de archivos XML de Open Test Reporting puede filtrar credenciales de Git. El impacto depende del nivel del token de acceso expuesto a través de OpenTestReportGeneratingListener. Si estos informes de prueba se publican o almacenan en un lugar público, existe la posibilidad de que un atacante malintencionado robe el token y realice acciones elevadas suplantando la identidad del usuario o la aplicación. Este problema se ha corregido en la versión 5.13.2."}], "references": [{"url": "https://github.com/junit-team/junit-framework/commit/d4fc834c8c1c0b3168cd030c13551d1d041f51bc", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/junit-team/junit-framework/security/advisories/GHSA-m43g-m425-p68x", "source": "<EMAIL>", "tags": []}]}
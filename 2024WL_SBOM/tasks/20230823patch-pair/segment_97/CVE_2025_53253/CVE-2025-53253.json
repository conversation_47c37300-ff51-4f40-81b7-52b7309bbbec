{"cve_id": "CVE-2025-53253", "published_date": "2025-06-27T14:15:44.370", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Josh WP Edit allows Stored XSS. This issue affects WP Edit: from n/a through 4.0.4."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Josh WP Edit permite XSS almacenado. Este problema afecta a WP Edit desde la versión n/d hasta la 4.0.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-edit/vulnerability/wordpress-wp-edit-plugin-4-0-4-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53332", "published_date": "2025-06-27T14:15:56.027", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in ethoseo Track Everything allows Stored XSS. This issue affects Track Everything: from n/a through 2.0.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en ethoseo Track Everything permite XSS almacenado. Este problema afecta a Track Everything desde la versión n/d hasta la 2.0.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/track-everything/vulnerability/wordpress-track-everything-plugin-2-0-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
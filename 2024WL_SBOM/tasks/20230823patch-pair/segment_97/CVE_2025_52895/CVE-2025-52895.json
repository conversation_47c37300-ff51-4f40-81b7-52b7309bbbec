{"cve_id": "CVE-2025-52895", "published_date": "2025-06-30T17:15:32.930", "last_modified_date": "2025-07-08T14:10:54.147", "descriptions": [{"lang": "en", "value": "Frappe is a full-stack web application framework. Prior to versions 14.94.3 and 15.58.0, SQL injection could be achieved via a specially crafted request, which could allow malicious person to gain access to sensitive information. This issue has been patched in versions 14.94.3 and 15.58.0. There are no workarounds for this issue other than upgrading."}, {"lang": "es", "value": "Frappe es un framework de aplicaciones web integral. Antes de las versiones 14.94.3 y 15.58.0, se podía realizar una inyección SQL mediante una solicitud especialmente manipulada, lo que podía permitir que personas malintencionadas accedieran a información confidencial. Este problema se ha corregido en las versiones 14.94.3 y 15.58.0. No existen soluciones alternativas aparte de actualizar."}], "references": [{"url": "https://github.com/frappe/frappe/commit/c795e351be033070174437324d74f44759a744a6", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/frappe/frappe/commit/f0933590103c80c6393647dd0403d399e64c951c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/frappe/frappe/pull/31526", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/frappe/frappe/security/advisories/GHSA-mhj8-jfhf-mcw9", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
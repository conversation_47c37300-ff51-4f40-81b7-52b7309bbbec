{"cve_id": "CVE-2025-53098", "published_date": "2025-06-27T22:15:25.993", "last_modified_date": "2025-07-01T14:15:40.843", "descriptions": [{"lang": "en", "value": "Roo Code is an AI-powered autonomous coding agent. The project-specific MCP configuration for the Roo Code agent is stored in the `.roo/mcp.json` file within the VS Code workspace. Because the MCP configuration format allows for execution of arbitrary commands, prior to version 3.20.3, it would have been possible for an attacker with access to craft a prompt to ask the agent to write a malicious command to the MCP configuration file. If the user had opted-in to auto-approving file writes within the project, this would have led to arbitrary command execution. This issue is of moderate severity, since it requires the attacker to already be able to submit prompts to the agent (for instance through a prompt injection attack), for the user to have MCP enabled (on by default), and for the user to have enabled auto-approved file writes (off by default). Version 3.20.3 fixes the issue by adding an additional layer of opt-in configuration for auto-approving writing to <PERSON><PERSON>'s configuration files, including all files within the `.roo/` folder."}, {"lang": "es", "value": "Roo Code es un agente de codificación autónomo basado en IA. La configuración de MCP específica del proyecto para el agente de Roo Code se almacena en el archivo `.roo/mcp.json` dentro del espacio de trabajo de VS Code. Dado que el formato de configuración de MCP permite la ejecución de comandos arbitrarios, antes de la versión 3.20.3, un atacante con acceso habría podido manipular un mensaje para solicitar al agente que escribiera un comando malicioso en el archivo de configuración de MCP. Si el usuario hubiera habilitado la aprobación automática de escrituras de archivos dentro del proyecto, esto habría provocado la ejecución de comandos arbitrarios. Este problema es de gravedad moderada, ya que requiere que el atacante ya pueda enviar mensajes al agente (por ejemplo, mediante un ataque de inyección de mensajes), que el usuario tenga MCP habilitado (activado por defecto) y que tenga habilitada la aprobación automática de escrituras de archivos (desactivada por defecto). La versión 3.20.3 corrige el problema agregando una capa adicional de configuración opt-in para aprobar automáticamente la escritura en los archivos de configuración de Roo, incluidos todos los archivos dentro de la carpeta `.roo/`."}], "references": [{"url": "https://github.com/RooCodeInc/Roo-Code/commit/7d0b22f9e659dc6c26aab0bacbea27874986e772", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RooCodeInc/Roo-Code/security/advisories/GHSA-5x8h-m52g-5v54", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53095", "published_date": "2025-07-01T02:15:22.563", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "<PERSON> is a self-hosted game stream host for Moonlight. Prior to version 2025.628.4510, the web UI of Sunshine lacks protection against Cross-Site Request Forgery (CSRF) attacks. This vulnerability allows an attacker to craft a malicious web page that, when visited by an authenticated user, can trigger unintended actions within the Sunshine application on behalf of that user. Specifically, since the application does OS command execution by design, this issue can be exploited to abuse the \"Command Preparations\" feature, enabling an attacker to inject arbitrary commands that will be executed with Administrator privileges when an application is launched. This issue has been patched in version 2025.628.4510."}, {"lang": "es", "value": "Sunshine es un servidor de streaming de juegos autoalojado para Moonlight. Antes de la versión 2025.628.4510, la interfaz web de Sunshine carecía de protección contra ataques de Cross-Site Request Forgery (CSRF). Esta vulnerabilidad permite a un atacante crear una página web maliciosa que, al ser visitada por un usuario autenticado, puede desencadenar acciones no deseadas dentro de la aplicación Sunshine en nombre de dicho usuario. En concreto, dado que la aplicación ejecuta comandos del sistema operativo por diseño, este problema puede explotarse para abusar de la función \"Preparación de comandos\", lo que permite a un atacante inyectar comandos arbitrarios que se ejecutarán con privilegios de administrador al iniciar una aplicación. Este problema se ha corregido en la versión 2025.628.4510."}], "references": [{"url": "https://github.com/LizardByte/Sunshine/commit/738ac93a0ec1cd10412d1f339968775f53bfefe0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/LizardByte/Sunshine/security/advisories/GHSA-39hj-fxvw-758m", "source": "<EMAIL>", "tags": []}]}
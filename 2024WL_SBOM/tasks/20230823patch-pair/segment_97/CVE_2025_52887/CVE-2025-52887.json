{"cve_id": "CVE-2025-52887", "published_date": "2025-06-26T15:15:23.350", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "cpp-httplib is a C++11 single-file header-only cross platform HTTP/HTTPS library. In version 0.21.0, when many http headers fields are passed in, the library does not limit the number of headers, and the memory associated with the headers will not be released when the connection is disconnected. This leads to potential exhaustion of system memory and results in a server crash or unresponsiveness. Version 0.22.0 contains a patch for the issue."}, {"lang": "es", "value": "cpp-httplib es una librería HTTP/HTTPS multiplataforma de un solo archivo de encabezados de C++11. En la versión 0.21.0, al pasar muchos campos de encabezados HTTP, la biblioteca no limita el número de encabezados y la memoria asociada no se libera al desconectarse la conexión. Esto puede agotar la memoria del sistema y provocar un fallo del servidor o la falta de respuesta. La versión 0.22.0 incluye un parche para este problema."}], "references": [{"url": "https://github.com/yhirose/cpp-httplib/commit/28dcf379e82a2cdb544d812696a7fd46067eb7f9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/yhirose/cpp-httplib/security/advisories/GHSA-xjhg-gf59-p92h", "source": "<EMAIL>", "tags": []}]}
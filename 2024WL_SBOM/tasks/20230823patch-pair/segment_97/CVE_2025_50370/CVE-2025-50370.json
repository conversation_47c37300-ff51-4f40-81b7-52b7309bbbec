{"cve_id": "CVE-2025-50370", "published_date": "2025-06-27T16:15:25.543", "last_modified_date": "2025-07-01T18:13:30.903", "descriptions": [{"lang": "en", "value": "A Cross-Site Request Forgery (CSRF) vulnerability exists in the Inquiry Management functionality /mcgs/admin/readenq.php of the Phpgurukul Medical Card Generation System 1.0. The vulnerable endpoint allows an authenticated admin to delete inquiry records via a simple GET request, without requiring a CSRF token or validating the origin of the request."}, {"lang": "es", "value": "Existe una vulnerabilidad de Cross Site Request Forgery (CSRF) en la función de gestión de consultas /mcgs/admin/readenq.php de Phpgurukul Medical Card Generation System 1.0. El endpoint vulnerable permite a un administrador autenticado eliminar registros de consultas mediante una simple solicitud GET, sin necesidad de un token CSRF ni de validar el origen de la solicitud."}], "references": [{"url": "https://github.com/1h3ll/CVEs/blob/main/CSRF-ReadEnquiry_Medicalcard_Generations_System.md", "source": "<EMAIL>", "tags": ["Broken Link"]}]}
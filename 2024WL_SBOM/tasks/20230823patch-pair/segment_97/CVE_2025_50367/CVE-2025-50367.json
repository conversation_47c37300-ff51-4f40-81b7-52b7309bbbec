{"cve_id": "CVE-2025-50367", "published_date": "2025-06-27T16:15:25.263", "last_modified_date": "2025-07-01T18:14:15.680", "descriptions": [{"lang": "en", "value": "A stored blind XSS vulnerability exists in the Contact Page of the Phpgurukul Medical Card Generation System 1.0 mcgs/contact.php. The name field fails to properly sanitize user input, allowing an attacker to inject malicious JavaScript."}, {"lang": "es", "value": "Existe una vulnerabilidad XSS ciega almacenada en la página de contacto de Phpgurukul Medical Card Generation System 1.0 mcgs/contact.php. El campo de nombre no depura correctamente la entrada del usuario, lo que permite a un atacante inyectar JavaScript malicioso."}], "references": [{"url": "https://github.com/1h3ll/CVEs/blob/main/BXSS-Medicalcard_Generations_System.md", "source": "<EMAIL>", "tags": ["Broken Link"]}]}
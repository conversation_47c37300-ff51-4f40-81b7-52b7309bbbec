{"cve_id": "CVE-2025-5830", "published_date": "2025-06-25T18:15:23.950", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial DLB_SlaveRegister Heap-based Buffer Overflow Remote Code Execution Vulnerability. This vulnerability allows network-adjacent attackers to execute arbitrary code on affected installations of Autel MaxiCharger AC Wallbox Commercial EV chargers. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the handling of DLB_SlaveRegister messages. The issue results from the lack of proper validation of the length of user-supplied data prior to copying it to a fixed-length heap-based buffer. An attacker can leverage this vulnerability to execute code in the context of the device. Was ZDI-CAN-26327."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por desbordamiento de búfer basado en el heap DLB_SlaveRegister en Autel MaxiCharger AC Wallbox Commercial. Esta vulnerabilidad permite a atacantes adyacentes a la red ejecutar código arbitrario en las instalaciones afectadas de cargadores de vehículos eléctricos Autel MaxiCharger AC Wallbox Commercial. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en el manejo de los mensajes DLB_SlaveRegister. El problema se debe a la falta de una validación adecuada de la longitud de los datos proporcionados por el usuario antes de copiarlos a un búfer basado en el heap de longitud fija. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del dispositivo. Era ZDI-CAN-26327."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-349/", "source": "<EMAIL>", "tags": []}]}
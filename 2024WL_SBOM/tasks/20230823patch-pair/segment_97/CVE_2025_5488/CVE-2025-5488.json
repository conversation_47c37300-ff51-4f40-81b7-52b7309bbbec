{"cve_id": "CVE-2025-5488", "published_date": "2025-06-26T02:15:21.333", "last_modified_date": "2025-07-11T14:31:08.653", "descriptions": [{"lang": "en", "value": "The WP Masonry & Infinite Scroll plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'wmis' shortcode in all versions up to, and including, 2.2 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WP Masonry &amp; Infinite Scroll para WordPress es vulnerable a cross site scripting almacenado a través del shortcode 'wmis' del plugin en todas las versiones hasta la 2.2 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-masonry-infinite-scroll/trunk/includes/functions.php#L227", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3314905%40wp-masonry-infinite-scroll&new=3314905%40wp-masonry-infinite-scroll&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/656c6236-55e6-4989-8f3d-2d2f81ab0093?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
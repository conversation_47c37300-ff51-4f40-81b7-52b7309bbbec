{"cve_id": "CVE-2025-49592", "published_date": "2025-06-26T20:15:29.323", "last_modified_date": "2025-06-30T18:39:09.973", "descriptions": [{"lang": "en", "value": "n8n is a workflow automation platform. Versions prior to 1.98.0 have an Open Redirect vulnerability in the login flow. Authenticated users can be redirected to untrusted, attacker-controlled domains after logging in, by crafting malicious URLs with a misleading redirect query parameter. This may lead to phishing attacks by impersonating the n8n UI on lookalike domains (e.g., n8n.local.evil.com), credential or 2FA theft if users are tricked into re-entering sensitive information, and/or reputation risk due to the visual similarity between attacker-controlled domains and trusted ones. The vulnerability affects anyone hosting n8n and exposing the `/signin` endpoint to users. The issue has been patched in version 1.98.0. All users should upgrade to this version or later. The fix introduces strict origin validation for redirect URLs, ensuring only same-origin or relative paths are allowed after login."}, {"lang": "es", "value": "n8n es una plataforma de automatización de flujos de trabajo. Las versiones anteriores a la 1.98.0 presentan una vulnerabilidad de redirección abierta en el flujo de inicio de sesión. Tras iniciar sesión, los usuarios autenticados pueden ser redirigidos a dominios no confiables, controlados por atacantes, mediante la manipulación de URL maliciosas con un parámetro de consulta de redirección engañoso. Esto puede provocar ataques de phishing al suplantar la interfaz de usuario de n8n en dominios similares (p. ej., n8n.local.evil.com), robo de credenciales o de 2FA si se engaña a los usuarios para que vuelvan a introducir información confidencial, o riesgo para la reputación debido a la similitud visual entre los dominios controlados por atacantes y los de confianza. La vulnerabilidad afecta a cualquiera que aloje n8n y exponga el endpoint `/signin` a los usuarios. El problema se ha corregido en la versión 1.98.0. Todos los usuarios deben actualizar a esta versión o una posterior. La corrección introduce una validación de origen estricta para las URL de redirección, lo que garantiza que solo se permitan rutas del mismo origen o relativas tras el inicio de sesión."}], "references": [{"url": "https://github.com/n8n-io/n8n/commit/4865d1e360a0fe7b045e295b5e1a29daad12314e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/n8n-io/n8n/pull/16034", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/n8n-io/n8n/releases/tag/n8n%401.98.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/n8n-io/n8n/security/advisories/GHSA-5vj6-wjr7-5v9f", "source": "<EMAIL>", "tags": []}]}
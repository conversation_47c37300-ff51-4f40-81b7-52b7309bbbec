{"cve_id": "CVE-2025-52576", "published_date": "2025-06-25T17:15:39.023", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Kanboard is project management software that focuses on the Kanban methodology. Prior to version 1.2.46, Kanboard is vulnerable to username enumeration and IP spoofing-based brute-force protection bypass. By analyzing login behavior and abusing trusted HTTP headers, an attacker can determine valid usernames and circumvent rate-limiting or blocking mechanisms. Any organization running a publicly accessible Kanboard instance is affected, especially if relying on IP-based protections like Fail2Ban or CAPTCHA for login rate-limiting. Attackers with access to the login page can exploit this flaw to enumerate valid usernames and bypass IP-based blocking mechanisms, putting all user accounts at higher risk of brute-force or credential stuffing attacks. Version 1.2.46 contains a patch for the issue."}, {"lang": "es", "value": "Kanboard es un software de gestión de proyectos centrado en la metodología Kanban. Antes de la versión 1.2.46, Kanboard era vulnerable a la enumeración de nombres de usuario y a la elusión de la protección por fuerza bruta basada en suplantación de IP. Al analizar el comportamiento de inicio de sesión y abusar de los encabezados HTTP de confianza, un atacante puede determinar nombres de usuario válidos y eludir los mecanismos de limitación o bloqueo. Cualquier organización que ejecute una instancia de Kanboard de acceso público se ve afectada, especialmente si utiliza protecciones basadas en IP como Fail2Ban o CAPTCHA para la limitación de la tasa de inicio de sesión. Los atacantes con acceso a la página de inicio de sesión pueden explotar esta vulnerabilidad para enumerar nombres de usuario válidos y eludir los mecanismos de bloqueo basados en IP, lo que aumenta el riesgo de ataques de fuerza bruta o robo de credenciales. La versión 1.2.46 incluye un parche para solucionar este problema."}], "references": [{"url": "https://github.com/kanboard/kanboard/blob/cbb7e60fb595ff4572bb8801b275a0b451c4bda0/app/Model/UserLockingModel.php#L101-L104", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kanboard/kanboard/blob/cbb7e60fb595ff4572bb8801b275a0b451c4bda0/app/Subscriber/AuthSubscriber.php#L96-L108", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kanboard/kanboard/commit/3079623640dc39f9c7b0c840d2a79095331051f1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kanboard/kanboard/security/advisories/GHSA-qw57-7cx6-wvp7", "source": "<EMAIL>", "tags": []}]}
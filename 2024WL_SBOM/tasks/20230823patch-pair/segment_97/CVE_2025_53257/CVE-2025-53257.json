{"cve_id": "CVE-2025-53257", "published_date": "2025-06-27T14:15:45.103", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in Serhii Pasyuk Gmedia Photo Gallery allows PHP Local File Inclusion. This issue affects Gmedia Photo Gallery: from n/a through 1.23.0."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en Serhii Pasyuk Gmedia Photo Gallery permite la inclusión local de archivos en PHP. Este problema afecta a la galería fotográfica Gmedia desde n/d hasta la versión 1.23.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/grand-media/vulnerability/wordpress-gmedia-photo-gallery-plugin-1-23-0-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
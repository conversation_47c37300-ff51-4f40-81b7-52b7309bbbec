{"cve_id": "CVE-2025-5932", "published_date": "2025-06-26T03:15:25.110", "last_modified_date": "2025-07-07T16:03:18.250", "descriptions": [{"lang": "en", "value": "The Homerunner plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.0.29. This is due to missing or incorrect nonce validation on the main_settings() function. This makes it possible for unauthenticated attackers to update plugin settings via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Homerunner para WordPress es vulnerable a cross-site request forgery en todas las versiones hasta la 1.0.29 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la función main_settings(). Esto permite que atacantes no autenticados actualicen la configuración del complemento mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/homerunner-smartcheckout/tags/1.0.29/classes/class-settings.php#L319", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/36eaff34-50cd-4399-8314-19ae4f50d017?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
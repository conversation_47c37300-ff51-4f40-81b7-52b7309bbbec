{"cve_id": "CVE-2025-53311", "published_date": "2025-06-27T14:15:53.280", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Amol Nirmala Waman Navayan Subscribe allows Stored XSS. This issue affects Navayan Subscribe: from n/a through 1.13."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en Amol Nirmala Waman Navayan Subscribe permite XSS almacenado. Este problema afecta a Navayan Subscribe desde n/d hasta la versión 1.13."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/navayan-subscribe/vulnerability/wordpress-navayan-subscribe-plugin-1-13-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
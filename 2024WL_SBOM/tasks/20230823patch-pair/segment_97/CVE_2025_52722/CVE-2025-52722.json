{"cve_id": "CVE-2025-52722", "published_date": "2025-06-27T12:15:39.403", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in JoinWebs Classiera allows SQL Injection. This issue affects Classiera: from n/a through 4.0.34."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en JoinWebs Classiera permite la inyección SQL. Este problema afecta a Classiera desde n/d hasta la versión 4.0.34."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/classiera/vulnerability/wordpress-classiera-4-0-34-sql-injection-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
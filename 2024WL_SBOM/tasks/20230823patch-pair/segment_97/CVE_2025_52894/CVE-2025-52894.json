{"cve_id": "CVE-2025-52894", "published_date": "2025-06-25T17:15:39.677", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "OpenBao exists to provide a software solution to manage, store, and distribute sensitive data including secrets, certificates, and keys. OpenBao before v2.3.0 allowed an attacker to perform unauthenticated, unaudited cancellation of root rekey and recovery rekey operations, effecting a denial of service. In OpenBao v2.2.0 and later, manually setting the configuration option `disable_unauthed_rekey_endpoints=true` allows an operator to deny these rarely-used endpoints on global listeners. A patch is available at commit fe75468822a22a88318c6079425357a02ae5b77b. In a future OpenBao release communicated on OpenBao's website, the maintainers will set this to `true` for all users and provide an authenticated alternative. As a workaround, if an active proxy or load balancer sits in front of OpenBao, an operator can deny requests to these endpoints from unauthorized IP ranges."}, {"lang": "es", "value": "OpenBao existe para proporcionar una solución de software que permite gestionar, almacenar y distribuir datos confidenciales, como secretos, certificados y claves. En versiones anteriores a la v2.3.0, OpenBao permitía a un atacante realizar cancelaciones no autenticadas ni auditadas de operaciones de regeneración de claves de raíz y de recuperación, lo que provocaba una denegación de servicio. En OpenBao v2.2.0 y posteriores, la configuración manual de la opción `disable_unauthed_rekey_endpoints=true` permite a un operador denegar el acceso a estos endpoints poco utilizados en escuchas globales. Hay un parche disponible en el commit fe75468822a22a88318c6079425357a02ae5b77b. En una futura versión de OpenBao, anunciada en su sitio web, los fabricantees la configurarán como `true` para todos los usuarios y ofrecerán una alternativa autenticada. Como solución alternativa, si un proxy activo o un balanceador de carga se encuentra frente a OpenBao, un operador puede rechazar solicitudes a estos endpoints desde rangos de IP no autorizados."}], "references": [{"url": "https://github.com/openbao/openbao/commit/fe75468822a22a88318c6079425357a02ae5b77b", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openbao/openbao/security/advisories/GHSA-prpj-rchp-9j5h", "source": "<EMAIL>", "tags": []}, {"url": "https://openbao.org/docs/deprecation", "source": "<EMAIL>", "tags": []}, {"url": "https://openbao.org/docs/deprecation/unauthed-rekey", "source": "<EMAIL>", "tags": []}]}
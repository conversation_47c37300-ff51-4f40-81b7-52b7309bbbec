{"cve_id": "CVE-2025-53097", "published_date": "2025-06-27T22:15:25.803", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "Roo Code is an AI-powered autonomous coding agent. Prior to version 3.20.3, there was an issue where the Roo Code agent's `search_files` tool did not respect the setting to disable reads outside of the VS Code workspace. This means that an attacker who was able to inject a prompt into the agent could potentially read a sensitive file and then write the information to a JSON schema. Users have the option to disable schema fetching in VS Code, but the feature is enabled by default. For users with this feature enabled, writing to the schema would trigger a network request without the user having a chance to deny. This issue is of moderate severity, since it requires the attacker to already be able to submit prompts to the agent. Version 3.20.3 fixed the issue where `search_files` did not respect the setting to limit it to the workspace. This reduces the scope of the damage if an attacker is able to take control of the agent through prompt injection or another vector."}, {"lang": "es", "value": "Roo Code es un agente de codificación autónomo basado en IA. Antes de la versión 3.20.3, existía un problema por el cual la herramienta `search_files` del agente Roo Code no respetaba la configuración para deshabilitar las lecturas fuera del espacio de trabajo de VS Code. Esto significa que un atacante que pudiera inyectar un mensaje en el agente podría leer un archivo confidencial y luego escribir la información en un esquema JSON. Los usuarios tienen la opción de deshabilitar la obtención del esquema en VS Code, pero la función está habilitada por defecto. Para los usuarios con esta función habilitada, escribir en el esquema activaría una solicitud de red sin que el usuario pudiera denegarla. Este problema es de gravedad moderada, ya que requiere que el atacante ya pueda enviar mensajes al agente. La versión 3.20.3 solucionó el problema por el cual `search_files` no respetaba la configuración para limitarlo al espacio de trabajo. Esto reduce el alcance del daño si un atacante logra tomar el control del agente mediante la inyección de mensajes u otro vector."}], "references": [{"url": "https://github.com/RooCodeInc/Roo-Code/commit/10b2fb32ed047bbd7b8d10ef185c1ed345efcc92", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RooCodeInc/Roo-Code/commit/7d0b22f9e659dc6c26aab0bacbea27874986e772", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RooCodeInc/Roo-Code/security/advisories/GHSA-wr2q-46pg-f228", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53005", "published_date": "2025-07-01T01:15:28.430", "last_modified_date": "2025-07-16T14:43:07.000", "descriptions": [{"lang": "en", "value": "DataEase is an open source business intelligence and data visualization tool. Prior to version 2.10.11, there is a bypass vulnerability in Dataease's PostgreSQL Data Source JDBC Connection Parameters. The sslfactory and sslfactoryarg parameters could trigger a bypass vulnerability. This issue has been patched in version 2.10.11."}, {"lang": "es", "value": "DataEase es una herramienta de código abierto de inteligencia empresarial y visualización de datos. Antes de la versión 2.10.11, existía una vulnerabilidad de omisión en los parámetros de conexión JDBC de la fuente de datos PostgreSQL de DataEase. Los parámetros sslfactory y sslfactoryarg podían desencadenar una vulnerabilidad de omisión. Este problema se ha corregido en la versión 2.10.11."}], "references": [{"url": "https://github.com/dataease/dataease/security/advisories/GHSA-99c4-h4fq-r23v", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
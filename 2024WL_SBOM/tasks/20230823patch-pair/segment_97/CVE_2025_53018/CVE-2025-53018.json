{"cve_id": "CVE-2025-53018", "published_date": "2025-06-27T13:15:24.803", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Lychee is a free, open-source photo-management tool. Prior to version 6.6.13, a critical Server-Side Request Forgery (SSRF) vulnerability exists in the `/api/v2/Photo::fromUrl` endpoint. This flaw lets an attacker instruct the application’s backend to make HTTP requests to any URL they choose. Consequently, internal network resources—such as localhost services or cloud-provider metadata endpoints—become reachable. The endpoint takes a URL from the user and calls it server-side via fopen() without any safeguards. There is no IP address validation, nor are there any allow-list, timeout, or size restrictions. Because of this, attackers can point the application at internal targets. Using this flaw, an attacker can perform internal port scans or retrieve sensitive cloud metadata. Version 6.6.13 contains a patch for the issue."}, {"lang": "es", "value": "Lychee es una herramienta gratuita y de código abierto para la gestión de fotos. Antes de la versión 6.6.13, existía una vulnerabilidad crítica de Server-Side Request Forgery (SSRF) en el endpoint `/api/v2/Photo::fromUrl`. Esta falla permite a un atacante indicar al backend de la aplicación que realice solicitudes HTTP a cualquier URL. En consecuencia, se puede acceder a recursos internos de la red, como servicios de host local o endpoints de metadatos de proveedores de la nube. El endpoint toma una URL del usuario y la llama desde el servidor mediante fopen() sin ninguna protección. No hay validación de dirección IP, ni restricciones de lista de permitidos, tiempo de espera ni tamaño. Gracias a esto, los atacantes pueden apuntar la aplicación a objetivos internos. Con esta falla, un atacante puede realizar escaneos de puertos internos o recuperar metadatos confidenciales de la nube. La versión 6.6.13 incluye un parche para este problema."}], "references": [{"url": "https://github.com/LycheeOrg/Lychee/commit/9dc162eefe56ce185ac1d59da42ee557933d914d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/LycheeOrg/Lychee/security/advisories/GHSA-cpgw-wgf3-xc6v", "source": "<EMAIL>", "tags": []}]}
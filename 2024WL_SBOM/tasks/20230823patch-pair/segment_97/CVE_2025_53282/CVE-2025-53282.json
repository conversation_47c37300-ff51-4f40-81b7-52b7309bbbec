{"cve_id": "CVE-2025-53282", "published_date": "2025-06-27T14:15:49.673", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in aviplugins.com Thumbnail Editor allows Stored XSS. This issue affects Thumbnail Editor: from n/a through 2.3.3."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en el aviplugins.com Thumbnail Editor permite XSS almacenado. Este problema afecta al editor de miniaturas desde la versión n/d hasta la 2.3.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/thumbnail-editor/vulnerability/wordpress-thumbnail-editor-plugin-2-3-3-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
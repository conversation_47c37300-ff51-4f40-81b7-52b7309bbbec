{"cve_id": "CVE-2025-53310", "published_date": "2025-06-27T14:15:53.120", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Funnnny HidePost allows Reflected XSS. This issue affects HidePost: from n/a through 2.3.8."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en Funnnny HidePost permite XSS reflejado. Este problema afecta a HidePost desde la versión n/d hasta la 2.3.8."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/hidepost/vulnerability/wordpress-hidepost-plugin-2-3-8-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
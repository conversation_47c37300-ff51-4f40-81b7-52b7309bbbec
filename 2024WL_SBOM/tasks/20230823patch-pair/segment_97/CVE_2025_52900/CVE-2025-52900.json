{"cve_id": "CVE-2025-52900", "published_date": "2025-06-26T15:15:23.520", "last_modified_date": "2025-07-10T01:17:03.443", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit files. The file access permissions for files uploaded to or created from File Browser are never explicitly set by the application. The same is true for the database used by File Browser. On standard servers using File Browser prior to version 2.33.7 where the umask configuration has not been hardened before, this makes all the stated files readable by any operating system account. Version 2.33.7 fixes the issue."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. La aplicación nunca configura explícitamente los permisos de acceso a los archivos cargados o creados desde el Explorador de Archivos. Lo mismo ocurre con la base de datos que utiliza. En servidores estándar que utilizan el Explorador de Archivos antes de la versión 2.33.7, donde la configuración de umask no se ha reforzado previamente, esto permite que todos los archivos indicados sean legibles por cualquier cuenta del sistema operativo. La versión 2.33.7 soluciona el problema."}], "references": [{"url": "https://github.com/filebrowser/filebrowser/commit/ca86f916216620365c0f81629c0934ce02574d76", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-jj2r-455p-5gvf", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
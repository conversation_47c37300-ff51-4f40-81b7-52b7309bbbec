{"cve_id": "CVE-2025-53094", "published_date": "2025-06-27T20:15:35.173", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "ESPAsyncWebServer is an asynchronous HTTP and WebSocket server library for ESP32, ESP8266, RP2040 and RP2350. In versions up to and including 3.7.8, a CRLF (Carriage Return Line Feed) injection vulnerability exists in the construction and output of HTTP headers within `AsyncWebHeader.cpp`. Unsanitized input allows attackers to inject CR (`\\r`) or LF (`\\n`) characters into header names or values, leading to arbitrary header or response manipulation. Manipulation of HTTP headers and responses can enable a wide range of attacks, making the severity of this vulnerability high. A fix is available at pull request 211 and is expected to be part of version 3.7.9."}, {"lang": "es", "value": "ESPAsyncWebServer es una librería de servidor HTTP y WebSocket asíncrono para ESP32, ESP8266, RP2040 y RP2350. En versiones hasta la 3.7.8 (incluida), existe una vulnerabilidad de inyección CRLF (Carriage Return Line Feed) en la construcción y salida de encabezados HTTP dentro de `AsyncWebHeader.cpp`. La entrada no depurada permite a los atacantes inyectar caracteres CR (`\\r`) o LF (`\\n`) en los nombres o valores de los encabezados, lo que provoca la manipulación arbitraria de encabezados o respuestas. La manipulación de encabezados y respuestas HTTP puede permitir una amplia gama de ataques, lo que aumenta la gravedad de esta vulnerabilidad. Hay una solución disponible en la solicitud de incorporación de cambios 211 y se espera que forme parte de la versión 3.7.9."}], "references": [{"url": "https://github.com/ESP32Async/ESPAsyncWebServer/blob/1095dfd1ecf1a903aede29854232af1b24f089b1/src/AsyncWebHeader.cpp#L6-L32", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ESP32Async/ESPAsyncWebServer/pull/211", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ESP32Async/ESPAsyncWebServer/security/advisories/GHSA-87j8-6f7g-h8wh", "source": "<EMAIL>", "tags": []}]}
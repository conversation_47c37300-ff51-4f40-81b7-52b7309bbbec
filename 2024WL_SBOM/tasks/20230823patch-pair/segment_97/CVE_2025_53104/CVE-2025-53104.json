{"cve_id": "CVE-2025-53104", "published_date": "2025-07-01T19:15:27.800", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "gluestack-ui is a library of copy-pasteable components & patterns crafted with Tailwind CSS (NativeWind). Prior to commit e6b4271, a command injection vulnerability was discovered in the discussion-to-slack.yml GitHub Actions workflow. Untrusted discussion fields (title, body, etc.) were directly interpolated into shell commands in a run: block. An attacker could craft a malicious GitHub Discussion title or body (e.g., $(curl ...)) to execute arbitrary shell commands on the Actions runner. This issue has been fixed in commit e6b4271 where the discussion-to-slack.yml workflow was removed. Users should remove the discussion-to-slack.yml workflow if using a fork or derivative of this repository."}, {"lang": "es", "value": "Gluestack-ui es una librería de componentes y patrones copiables y pegables, manipulada con Tailwind CSS (NativeWind). Antes del commit e6b4271, se descubrió una vulnerabilidad de inyección de comandos en el flujo de trabajo de GitHub Actions, que convierte la discusión en slack.yml. Campos de discusión no confiables (título, cuerpo, etc.) se interpolaban directamente en comandos de shell en un bloque `run:`. Un atacante podría crear un título o cuerpo malicioso de discusión de GitHub (p. ej., $(curl ...)) para ejecutar comandos de shell arbitrarios en el ejecutor de acciones. Este problema se solucionó en el commit e6b4271, donde se eliminó el flujo de trabajo `discussion-to-slack.yml`. Los usuarios deben eliminar el flujo de trabajo `discussion-to-slack.yml` si utilizan una bifurcación o un derivado de este repositorio."}], "references": [{"url": "https://github.com/gluestack/gluestack-ui/commit/e6b427150b35e97a089ea10409de8c5c52f8a7b9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/gluestack/gluestack-ui/security/advisories/GHSA-432r-9455-7f9x", "source": "<EMAIL>", "tags": []}]}
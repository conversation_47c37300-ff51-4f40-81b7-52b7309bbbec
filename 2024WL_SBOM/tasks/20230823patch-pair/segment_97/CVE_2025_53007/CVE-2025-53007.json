{"cve_id": "CVE-2025-53007", "published_date": "2025-06-26T15:15:24.043", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "arduino-esp32 provides an Arduino core for the ESP32. Versions prior to 3.3.0-RC1 and 3.2.1 contain a HTTP Response Splitting vulnerability. The `sendHeader` function takes arbitrary input for the HTTP header name and value, concatenates them into an HTTP header line, and appends this to the outgoing HTTP response headers. There is no validation or sanitization of the `name` or `value` parameters before they are included in the HTTP response. If an attacker can control the input to `sendHeader` (either directly or indirectly), they could inject carriage return (`\\r`) or line feed (`\\n`) characters into either the header name or value. This could allow the attacker to inject additional headers, manipulate the structure of the HTTP response, potentially inject an entire new HTTP response (HTTP Response Splitting), and/or ause header confusion or other HTTP protocol attacks. Versions 3.3.0-RC1 and 3.2.1 contain a fix for the issue."}, {"lang": "es", "value": "Arduino-esp32 proporciona un núcleo Arduino para el ESP32. Las versiones anteriores a la 3.3.0-RC1 y la 3.2.1 contienen una vulnerabilidad de división de respuesta HTTP. La función `sendHeader` toma una entrada arbitraria para el nombre y el valor del encabezado HTTP, los concatena en una línea de encabezado HTTP y la añade a los encabezados de respuesta HTTP salientes. No se realiza ninguna validación ni depuración de los parámetros `name` ni `value` antes de que se incluyan en la respuesta HTTP. Si un atacante puede controlar la entrada de `sendHeader` (ya sea directa o indirectamente), podría inyectar caracteres de retorno de carro (`\\r`) o salto de línea (`\\n`) en el nombre o el valor del encabezado. Esto podría permitirle inyectar encabezados adicionales, manipular la estructura de la respuesta HTTP, potencialmente inyectar una respuesta HTTP completamente nueva (división de respuesta HTTP) o provocar confusión de encabezados u otros ataques al protocolo HTTP. Las versiones 3.3.0-RC1 y 3.2.1 contienen una solución para el problema."}], "references": [{"url": "https://github.com/espressif/arduino-esp32/blob/9e61fa7e4bce59c05cb17c15b11b53b9bafca077/libraries/WebServer/src/WebServer.cpp#L504-L521", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/espressif/arduino-esp32/blob/9e61fa7e4bce59c05cb17c15b11b53b9bafca077/libraries/WebServer/src/WebServer.cpp#L577-L582", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/espressif/arduino-esp32/commit/21640ac82a1bb5efa8cf0b3841be1ac80add6785", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/espressif/arduino-esp32/security/advisories/GHSA-5476-9jjq-563m", "source": "<EMAIL>", "tags": []}]}
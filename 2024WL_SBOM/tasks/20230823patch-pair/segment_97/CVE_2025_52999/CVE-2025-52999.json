{"cve_id": "CVE-2025-52999", "published_date": "2025-06-25T17:15:39.820", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "jackson-core contains core low-level incremental (\"streaming\") parser and generator abstractions used by Jackson Data Processor. In versions prior to 2.15.0, if a user parses an input file and it has deeply nested data, <PERSON> could end up throwing a StackoverflowError if the depth is particularly large. jackson-core 2.15.0 contains a configurable limit for how deep <PERSON> will traverse in an input document, defaulting to an allowable depth of 1000. jackson-core will throw a StreamConstraintsException if the limit is reached. jackson-databind also benefits from this change because it uses jackson-core to parse JSON inputs. As a workaround, users should avoid parsing input files from untrusted sources."}, {"lang": "es", "value": "jackson-core contiene las abstracciones principales del analizador incremental (\"streaming\") de bajo nivel y del generador utilizadas por Jackson Data Processor. En versiones anteriores a la 2.15.0, si un usuario analiza un archivo de entrada con datos profundamente anidados, Jackson podía generar un error de Stackoverflow si la profundidad era excesiva. jackson-core 2.15.0 incluye un límite configurable para la profundidad que Jackson recorrerá en un documento de entrada, con una profundidad predeterminada de 1000. jackson-core generará una excepción StreamConstraintsException si se alcanza el límite. jackson-databind también se beneficia de este cambio, ya que utiliza jackson-core para analizar las entradas JSON. Como solución alternativa, se recomienda a los usuarios evitar analizar archivos de entrada de fuentes no confiables."}], "references": [{"url": "https://github.com/FasterXML/jackson-core/pull/943", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/FasterXML/jackson-core/security/advisories/GHSA-h46c-h94j-95f3", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-5825", "published_date": "2025-06-25T18:15:23.303", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial Firmware Downgrade Remote Code Execution Vulnerability. This vulnerability allows network-adjacent attackers to execute arbitrary code on affected installations of Autel MaxiCharger AC Wallbox Commercial charging stations. An attacker must first obtain the ability to pair a malicious Bluetooth device with the target system in order to exploit this vulnerability.\n\nThe specific flaw exists within the firmware update process. The issue results from the lack of proper validation of a firmware image before using it to perform an upgrade. An attacker can leverage this in conjunction with other vulnerabilities to execute arbitrary code in the context of the device. Was ZDI-CAN-26354."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código en la degradación del firmware de Autel MaxiCharger AC Wallbox Commercial. Esta vulnerabilidad permite a atacantes adyacentes a la red ejecutar código arbitrario en las instalaciones afectadas de las estaciones de carga Autel MaxiCharger AC Wallbox Commercial. Para explotar esta vulnerabilidad, un atacante debe primero vincular un dispositivo Bluetooth malicioso con el sistema objetivo. La falla específica se encuentra en el proceso de actualización del firmware. El problema se debe a la falta de validación adecuada de una imagen de firmware antes de usarla para realizar una actualización. Un atacante puede aprovechar esto, junto con otras vulnerabilidades, para ejecutar código arbitrario en el dispositivo. Anteriormente, se denominaba ZDI-CAN-26354."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-344/", "source": "<EMAIL>", "tags": []}]}
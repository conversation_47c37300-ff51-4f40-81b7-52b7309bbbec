{"cve_id": "CVE-2025-52555", "published_date": "2025-06-26T21:15:28.310", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Ceph is a distributed object, block, and file storage platform. In versions 17.2.7, 18.2.1 through 18.2.4, and 19.0.0 through 19.2.2, an unprivileged user can escalate to root privileges in a ceph-fuse mounted CephFS by chmod 777 a directory owned by root to gain access. The result of this is that a user could read, write and execute to any directory owned by root as long as they chmod 777 it. This impacts confidentiality, integrity, and availability. It is patched in versions 17.2.8, 18.2.5, and 19.2.3."}, {"lang": "es", "value": "Ceph es una plataforma distribuida de almacenamiento de objetos, bloques y archivos. En las versiones 17.2.7, 18.2.1 a 18.2.4 y 19.0.0 a 19.2.2, un usuario sin privilegios puede escalar a privilegios de root en un CephFS montado con ceph-fuse mediante chmod 777 en un directorio propiedad de root para obtener acceso. Como resultado, un usuario puede leer, escribir y ejecutar en cualquier directorio propiedad de root siempre que lo modifique con chmod 777. Esto afecta la confidencialidad, la integridad y la disponibilidad. Este problema se ha corregido en las versiones 17.2.8, 18.2.5 y 19.2.3."}], "references": [{"url": "https://github.com/ceph/ceph/pull/60314", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ceph/ceph/security/advisories/GHSA-89hm-qq33-2fjm", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-5812", "published_date": "2025-06-26T02:15:22.420", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "The VG WORT METIS plugin for WordPress is vulnerable to unauthorized modification of data due to a missing capability check on the gutenberg_save_post() function in all versions up to, and including, 2.0.0. This makes it possible for authenticated attackers, with Subscriber-level access and above, to update limited post settings."}, {"lang": "es", "value": "El complemento VG WORT METIS para WordPress es vulnerable a la modificación no autorizada de datos debido a la falta de una comprobación de capacidad en la función gutenberg_save_post() en todas las versiones hasta la 2.0.0 incluida. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, actualicen la configuración limitada de las publicaciones."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/vgw-metis/trunk/classes/admin.php#L422", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b9edcbdc-5b01-4880-95ec-57d87ccbb472?source=cve", "source": "<EMAIL>", "tags": []}]}
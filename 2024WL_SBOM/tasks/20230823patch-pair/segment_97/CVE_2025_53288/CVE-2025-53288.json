{"cve_id": "CVE-2025-53288", "published_date": "2025-06-27T14:15:50.420", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Missing Authorization vulnerability in Adrian Ladó PlatiOnline Payments allows Exploiting Incorrectly Configured Access Control Security Levels. This issue affects PlatiOnline Payments: from n/a through 6.3.2."}, {"lang": "es", "value": "La vulnerabilidad de falta de autorización en Adrian Ladó PlatiOnline Payments permite explotar niveles de seguridad de control de acceso configurados incorrectamente. Este problema afecta a los Pagos de PlatiOnline desde la versión n/d hasta la 6.3.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/plationline/vulnerability/wordpress-plationline-payments-plugin-6-3-2-broken-access-control-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53002", "published_date": "2025-06-26T15:15:23.873", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "LLaMA-Factory is a tuning library for large language models. A remote code execution vulnerability was discovered in LLaMA-Factory versions up to and including 0.9.3 during the LLaMA-Factory training process. This vulnerability arises because the `vhead_file` is loaded without proper safeguards, allowing malicious attackers to execute arbitrary malicious code on the host system simply by passing a malicious `Checkpoint path` parameter through the `WebUI` interface. The attack is stealthy, as the victim remains unaware of the exploitation. The root cause is that the `vhead_file` argument is loaded without the secure parameter `weights_only=True`. Version 0.9.4 contains a fix for the issue."}, {"lang": "es", "value": "LLaMA-Factory es una librería de optimización para modelos de lenguaje grandes. Se descubrió una vulnerabilidad de ejecución remota de código en versiones de LLaMA-Factory hasta la 0.9.3 (incluida) durante su entrenamiento. Esta vulnerabilidad surge porque el `vhead_file` se carga sin las protecciones adecuadas, lo que permite a atacantes maliciosos ejecutar código malicioso arbitrario en el sistema host simplemente pasando el parámetro `Checkpoint path` malicioso a través de la interfaz `WebUI`. El ataque es sigiloso, ya que la víctima desconoce la explotación. La causa principal es que el argumento `vhead_file` se carga sin el parámetro seguro `weights_only=True`. La versión 0.9.4 contiene una solución para este problema."}], "references": [{"url": "https://drive.google.com/file/d/1AddKm2mllsXfuvL4Tvbn_WJdjEOYXx4y/view?usp=sharing", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/hiyouga/LLaMA-Factory/commit/bb7bf51554d4ba8432333c35a5e3b52705955ede", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/hiyouga/LLaMA-Factory/security/advisories/GHSA-xj56-p8mm-qmxj", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-5826", "published_date": "2025-06-25T18:15:23.433", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial ble_process_esp32_msg Misinterpretation of Input Vulnerability. This vulnerability allows network-adjacent attackers to inject arbitrary AT commands on affected installations of Autel MaxiCharger AC Wallbox Commercial charging stations. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the ble_process_esp32_msg function. The issue results from misinterpretation of input data. An attacker can leverage this vulnerability to execute AT commands in the context of the device. Was ZDI-CAN-26368."}, {"lang": "es", "value": "Vulnerabilidad de interpretación errónea de datos de entrada en Autel MaxiCharger AC Wallbox Commercial ble_process_esp32_msg. Esta vulnerabilidad permite a atacantes adyacentes a la red inyectar comandos AT arbitrarios en las instalaciones afectadas de las estaciones de carga Autel MaxiCharger AC Wallbox Commercial. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en la función ble_process_esp32_msg. El problema se debe a la interpretación errónea de los datos de entrada. Un atacante puede aprovechar esta vulnerabilidad para ejecutar comandos AT en el contexto del dispositivo. Anteriormente, se denominó ZDI-CAN-26368."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-345/", "source": "<EMAIL>", "tags": []}]}
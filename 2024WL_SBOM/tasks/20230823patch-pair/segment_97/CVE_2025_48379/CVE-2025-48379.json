{"cve_id": "CVE-2025-48379", "published_date": "2025-07-01T19:15:27.353", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "Pillow is a Python imaging library. In versions 11.2.0 to before 11.3.0, there is a heap buffer overflow when writing a sufficiently large (>64k encoded with default settings) image in the DDS format due to writing into a buffer without checking for available space. This only affects users who save untrusted data as a compressed DDS image. This issue has been patched in version 11.3.0."}, {"lang": "es", "value": "Pillow es una librería de imágenes de Python. En las versiones 11.2.0 y anteriores a la 11.3.0, se produce un desbordamiento del búfer de montón al escribir una imagen suficientemente grande (más de 64k codificada con la configuración predeterminada) en formato DDS, debido a que se escribe en un búfer sin comprobar el espacio disponible. Esto solo afecta a los usuarios que guardan datos no confiables como una imagen DDS comprimida. Este problema se ha corregido en la versión 11.3.0. "}], "references": [{"url": "https://github.com/python-pillow/Pillow/commit/ef98b3510e3e4f14b547762764813d7e5ca3c5a4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/python-pillow/Pillow/pull/9041", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/python-pillow/Pillow/releases/tag/11.3.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/python-pillow/Pillow/security/advisories/GHSA-xg8h-j46f-w952", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52727", "published_date": "2025-06-27T12:15:40.960", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in QuanticaLabs CSS3 Vertical Web Pricing Tables allows Reflected XSS. This issue affects CSS3 Vertical Web Pricing Tables: from n/a through 1.9."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en QuanticaLabs CSS3 Vertical Web Pricing Tables permite XSS reflejado. Este problema afecta a las tablas de precios verticales CSS3 desde n/d hasta la versión 1.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/css3_vertical_web_pricing_tables/vulnerability/wordpress-css3-vertical-web-pricing-tables-1-9-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-49885", "published_date": "2025-06-27T12:15:38.477", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Unrestricted Upload of File with Dangerous Type vulnerability in HaruTheme Drag and Drop Multiple File Upload (Pro) - WooCommerce allows Upload a Web Shell to a Web Server. This issue affects Drag and Drop Multiple File Upload (Pro) - WooCommerce: from n/a through 5.0.6."}, {"lang": "es", "value": "Vulnerabilidad de tipo peligroso en la carga sin restricciones de archivos en HaruTheme Drag and Drop Multiple File Upload (Pro) - WooCommerce permite subir un shell web a un servidor web. Este problema afecta a la función de arrastrar y soltar múltiples archivos (Pro) de WooCommerce: desde n/d hasta la versión 5.0.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/drag-and-drop-file-upload-wc-pro/vulnerability/wordpress-drag-and-drop-multiple-file-upload-pro-woocommerce-5-0-6-arbitrary-file-upload-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
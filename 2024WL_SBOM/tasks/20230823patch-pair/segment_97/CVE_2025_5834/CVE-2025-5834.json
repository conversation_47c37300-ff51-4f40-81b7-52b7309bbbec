{"cve_id": "CVE-2025-5834", "published_date": "2025-06-25T18:15:24.337", "last_modified_date": "2025-07-08T14:51:29.567", "descriptions": [{"lang": "en", "value": "Pioneer DMH-WT7600NEX Missing Immutable Root of Trust in Hardware Local Privilege Escalation Vulnerability. This vulnerability allows local attackers to bypass authentication on affected installations of Pioneer DMH-WT7600NEX devices. Although authentication is required to exploit this vulnerability, the existing authentication mechanism can be bypassed.\n\nThe specific flaw exists within the configuration of the application system-on-chip (SoC). The issue results from the lack of a properly configured hardware root of trust. An attacker can leverage this vulnerability to escalate privileges and execute arbitrary code in the context of the boot process. Was ZDI-CAN-26078."}, {"lang": "es", "value": "Vulnerabilidad de escalada de privilegios locales de hardware: falta de raíz de confianza inmutable en el dispositivo Pioneer DMH-WT7600NEX. Esta vulnerabilidad permite a atacantes locales eludir la autenticación en las instalaciones afectadas de los dispositivos Pioneer DMH-WT7600NEX. Si bien se requiere autenticación para explotar esta vulnerabilidad, el mecanismo de autenticación existente puede eludirse. La falla específica se encuentra en la configuración del sistema en chip (SoC) de la aplicación. El problema se debe a la falta de una raíz de confianza de hardware correctamente configurada. Un atacante puede aprovechar esta vulnerabilidad para escalar privilegios y ejecutar código arbitrario durante el arranque. Anteriormente, se denominó ZDI-CAN-26078."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-351/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-5585", "published_date": "2025-06-25T03:15:27.853", "last_modified_date": "2025-07-08T14:54:51.113", "descriptions": [{"lang": "en", "value": "The SiteOrigin Widgets Bundle plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the `data-url` DOM Element Attribute in all versions up to, and including, 1.68.4 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento SiteOrigin Widgets Bundle para WordPress es vulnerable a Cross-Site Scripting almacenado a través del atributo del elemento DOM `data-url` en todas las versiones hasta la 1.68.4 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.svn.wordpress.org/so-widgets-bundle/tags/1.68.4/js/slider/jquery.slider.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/bb1b93ee-8641-4ddb-8b6b-2e9d30fe338d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-49883", "published_date": "2025-06-27T12:15:38.307", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in thembay Greenmart allows PHP Local File Inclusion. This issue affects Greenmart: from n/a through 4.2.3."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en thembay Greenmart permite la inclusión local de archivos en PHP. Este problema afecta a Greenmart desde n/d hasta la versión 4.2.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/greenmart/vulnerability/wordpress-greenmart-4-2-3-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-49416", "published_date": "2025-06-27T12:15:37.763", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in Fastw3b LLC FW Gallery allows PHP Local File Inclusion. This issue affects FW Gallery: from n/a through 8.0.0."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión remota de archivos PHP') en Fastw3b LLC FW Gallery permite la inclusión local de archivos PHP. Este problema afecta a FW Gallery desde n/d hasta la versión 8.0.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/fw-gallery/vulnerability/wordpress-fw-gallery-8-0-0-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
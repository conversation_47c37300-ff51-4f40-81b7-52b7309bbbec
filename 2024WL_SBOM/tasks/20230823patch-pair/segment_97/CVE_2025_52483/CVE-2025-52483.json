{"cve_id": "CVE-2025-52483", "published_date": "2025-06-25T17:15:38.740", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Registrator is a GitHub app that automates creation of registration pull requests for julia packages to the General registry. Prior to version 1.9.5, if the clone URL returned by GitHub is malicious (or can be injected using upstream vulnerabilities) a shell script injection can occur within the `withpasswd` function. Alternatively, an argument injection is possible in the `gettreesha `function. either of these can then lead to a potential RCE. Users should upgrade immediately to v1.9.5 to receive a fix. All prior versions are vulnerable. No known workarounds are available."}, {"lang": "es", "value": "Registrator es una aplicación de GitHub que automatiza la creación de solicitudes de extracción de registro para paquetes de Julia en el registro general. Antes de la versión 1.9.5, si la URL de clonación devuelta por GitHub era maliciosa (o podía inyectarse mediante vulnerabilidades de origen), se podía inyectar un script de shell en la función `withpasswd`. Alternativamente, se puede inyectar un argumento en la función `gettreesha`. Cualquiera de estas opciones puede provocar una posible RCE. Los usuarios deben actualizar inmediatamente a la versión 1.9.5 para obtener una solución. Todas las versiones anteriores son vulnerables. No se conocen soluciones alternativas."}], "references": [{"url": "https://github.com/JuliaRegistries/Registrator.jl/pull/448", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JuliaRegistries/Registrator.jl/security/advisories/GHSA-589r-g8hf-xx59", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53122", "published_date": "2025-06-26T20:15:32.063", "last_modified_date": "2025-06-30T18:39:09.973", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection') vulnerability in OpenNMS Horizon and Meridian applications allows SQL Injection. \n\nUsers\nshould upgrade to Meridian 2024.2.6 or newer, or Horizon 33.16 or newer. Meridian and\nHorizon installation instructions state that they are intended for installation\nwithin an organization's private networks and should not be directly accessible\nfrom the Internet."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de elementos especiales utilizados en un comando SQL ('Inyección SQL') en las aplicaciones OpenNMS Horizon y Meridian permite la inyección SQL. Los usuarios deben actualizar a Meridian 2024.2.6 o posterior, o a Horizon 33.16 o posterior. Las instrucciones de instalación de Meridian y Horizon indican que están diseñadas para instalarse en las redes privadas de una organización y no deben ser accesibles directamente desde Internet."}], "references": [{"url": "https://docs.opennms.com/meridian/2024/releasenotes/changelog.html#releasenotes-changelog-Meridian-2024.2.6", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/OpenNMS/opennms/pull/7709", "source": "<EMAIL>", "tags": []}]}
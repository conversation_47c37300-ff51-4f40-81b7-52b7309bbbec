{"cve_id": "CVE-2025-5535", "published_date": "2025-06-26T02:15:21.493", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "The e.nigma buttons plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'button' shortcode in all versions up to, and including, 1.1.3 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento e.nigma buttons para WordPress es vulnerable a cross site scripting almacenado a través del shortcode \"button\" del complemento en todas las versiones hasta la 1.1.3 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.svn.wordpress.org/enigma-buttons/trunk/enigma-buttons.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4c570533-1a67-46ad-9d29-35f70ae3bb6a?source=cve", "source": "<EMAIL>", "tags": []}]}
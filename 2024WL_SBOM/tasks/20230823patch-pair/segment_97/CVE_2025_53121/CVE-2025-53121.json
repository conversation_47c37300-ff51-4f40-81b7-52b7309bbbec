{"cve_id": "CVE-2025-53121", "published_date": "2025-06-26T19:15:21.960", "last_modified_date": "2025-06-30T18:39:09.973", "descriptions": [{"lang": "en", "value": "Multiple stored XSS were found on different nodes with unsanitized parameters in OpenMNS Horizon 33.0.8 and versions earlier than 33.1.6 on multiple platforms that allow an attacker to store on database and then inject HTML and/or Javascript on the page. The solution is to upgrade to Horizon 33.1.6, 33.1.7 or Meridian 2024.2.6, 2024.2.7 or newer. Meridian and Horizon installation instructions state that they are intended for installation within an organization's private networks and should not be directly accessible from the Internet. OpenNMS thanks <PERSON><PERSON><PERSON> for reporting this issue."}, {"lang": "es", "value": "Se encontraron múltiples XSS almacenado en diferentes nodos con parámetros no saneados en OpenMNS Horizon 33.0.8 y versiones anteriores a la 33.1.6 en varias plataformas, lo que permite a un atacante almacenarlos en una base de datos e inyectar HTML o Javascript en la página. La solución es actualizar a Horizon 33.1.6, 33.1.7 o Meridian 2024.2.6, 2024.2.7 o posterior. Las instrucciones de instalación de Meridian y Horizon indican que están diseñados para instalarse en las redes privadas de una organización y no deben ser accesibles directamente desde Internet. OpenNMS agradece a Fábio Tomé por informar sobre este problema."}], "references": [{"url": "https://github.com/OpenNMS/opennms", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/OpenNMS/opennms/pull/7708", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52818", "published_date": "2025-06-27T12:15:43.993", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Missing Authorization vulnerability in Dejan J<PERSON>nic Trusty Whistleblowing allows Exploiting Incorrectly Configured Access Control Security Levels. This issue affects Trusty Whistleblowing: from n/a through 1.5.2."}, {"lang": "es", "value": "La vulnerabilidad de falta de autorización en Dejan Jasnic Trusty Whistleblowing permite explotar niveles de seguridad de control de acceso configurados incorrectamente. Este problema afecta a Trusty Whistleblowing desde la versión n/d hasta la 1.5.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/trusty-whistleblowing-solution/vulnerability/wordpress-trusty-whistleblowing-1-5-2-broken-access-control-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
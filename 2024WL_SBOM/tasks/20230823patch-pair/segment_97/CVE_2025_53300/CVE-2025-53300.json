{"cve_id": "CVE-2025-53300", "published_date": "2025-06-27T14:15:51.893", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in douglaskarr Podcast Feed Player Widget and Shortcode allows Stored XSS. This issue affects Podcast Feed Player Widget and Shortcode: from n/a through 2.2.0."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en douglaskarr Podcast Feed Player Widget and Shortcode permite XSS almacenado. Este problema afecta al widget y código corto del reproductor de podcasts desde n/d hasta la versión 2.2.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/podcast-feed-player-widget/vulnerability/wordpress-podcast-feed-player-widget-and-shortcode-plugin-2-2-0-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
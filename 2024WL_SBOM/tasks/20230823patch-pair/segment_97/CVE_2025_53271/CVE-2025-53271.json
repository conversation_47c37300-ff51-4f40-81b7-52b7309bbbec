{"cve_id": "CVE-2025-53271", "published_date": "2025-06-27T14:15:47.647", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Anton Bond Additional Order Filters for WooCommerce allows Stored XSS. This issue affects Additional Order Filters for WooCommerce: from n/a through 1.22."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en <PERSON> Additional Order Filters for WooCommerce permite XSS almacenado. Este problema afecta a Additional Order Filters for WooCommerce: desde n/d hasta la versión 1.22."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/additional-order-filters-for-woocommerce/vulnerability/wordpress-additional-order-filters-for-woocommerce-plugin-1-22-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
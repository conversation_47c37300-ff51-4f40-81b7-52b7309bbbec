{"cve_id": "CVE-2025-53203", "published_date": "2025-06-27T14:15:43.783", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in EDGARROJAS WooCommerce PDF Invoice Builder allows Cross Site Request Forgery. This issue affects WooCommerce PDF Invoice Builder: from n/a through 1.2.148."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en EDGARROJAS WooCommerce PDF Invoice Builder permite Cross Site Request Forgery. Este problema afecta a WooCommerce PDF Invoice Builder desde n/d hasta la versión 1.2.148."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/woo-pdf-invoice-builder/vulnerability/wordpress-woocommerce-pdf-invoice-builder-plugin-1-2-148-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
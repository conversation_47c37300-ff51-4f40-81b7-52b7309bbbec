{"cve_id": "CVE-2025-52477", "published_date": "2025-06-26T17:15:30.897", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Octo-STS is a GitHub App that acts like a Security Token Service (STS) for the GitHub API. Octo-STS versions before v0.5.3 are vulnerable to unauthenticated SSRF by abusing fields in OpenID Connect tokens. Malicious tokens were shown to trigger internal network requests which could reflect error logs with sensitive information. Upgrade to v0.5.3 to resolve this issue. This version includes patch sets to sanitize input and redact logging."}, {"lang": "es", "value": "Octo-STS es una aplicación de GitHub que funciona como un Servicio de Token de Seguridad (STS) para la API de GitHub. Las versiones de Octo-STS anteriores a la v0.5.3 son vulnerables a SSRF no autenticado al abusar de los campos de los tokens de OpenID Connect. Se ha demostrado que los tokens maliciosos activan solicitudes de red internas que podrían reflejar registros de errores con información confidencial. Actualice a la v0.5.3 para resolver este problema. Esta versión incluye conjuntos de parches para sanear la entrada y redactar los registros."}], "references": [{"url": "https://github.com/octo-sts/app/commit/0f177fde54f9318e33f0bba6abaea9463a7c3afd", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/octo-sts/app/commit/b3976e39bd8c8c217c0670747d34a4499043da92", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/octo-sts/app/security/advisories/GHSA-h3qp-hwvr-9xcq", "source": "<EMAIL>", "tags": []}]}
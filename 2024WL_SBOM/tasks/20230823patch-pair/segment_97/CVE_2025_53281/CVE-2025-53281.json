{"cve_id": "CVE-2025-53281", "published_date": "2025-06-27T14:15:49.487", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in WPBean WPB Category Slider for WooCommerce allows PHP Local File Inclusion. This issue affects WPB Category Slider for WooCommerce: from n/a through 1.71."}, {"lang": "es", "value": "Vulnerabilidad de control incorrecto del nombre de archivo para la instrucción Include/Require en programas PHP ('Inclusión remota de archivos en PHP') en WPBean WPB Category Slider for WooCommerce permite la inclusión local de archivos en PHP. Este problema afecta a WPB Category Slider para WooCommerce desde n/d hasta la versión 1.71."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wpb-woocommerce-category-slider/vulnerability/wordpress-wpb-category-slider-for-woocommerce-plugin-1-71-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
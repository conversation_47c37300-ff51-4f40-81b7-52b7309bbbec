{"cve_id": "CVE-2025-50179", "published_date": "2025-06-25T16:15:26.843", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Tuleap is an Open Source Suite to improve management of software developments and collaboration. An attacker could use a cross-site request forgery vulnerability in Tuleap Community Edition prior to version 16.8.99.1749830289 and Tuleap Enterprise Edition prior to version 16.9-1 to trick victims into changing the canned responses. Tuleap Community Edition 16.8.99.1749830289 and Tuleap Enterprise Edition 16.9-1 contain a patch for the issue."}, {"lang": "es", "value": "Tuleap es una suite de código abierto que mejora la gestión del desarrollo de software y la colaboración. Un atacante podría aprovechar una vulnerabilidad de cross-site request forgery en Tuleap Community Edition (versión anterior a la 16.8.99.1749830289) y Tuleap Enterprise Edition (versión anterior a la 16.9-1) para engañar a las víctimas y lograr que modifiquen las respuestas predefinidas. Tuleap Community Edition (versión anterior a la 16.8.99.1749830289) y Tuleap Enterprise Edition (versión anterior a la 16.9-1) incluyen un parche para este problema."}], "references": [{"url": "https://github.com/Enalean/tuleap/commit/0f9aab6e3640e892c74c9dfc90ad65fd3aff499e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Enalean/tuleap/security/advisories/GHSA-rxpm-g7gw-4mrv", "source": "<EMAIL>", "tags": []}, {"url": "https://tuleap.net/plugins/git/tuleap/tuleap/stable?a=commit&h=0f9aab6e3640e892c74c9dfc90ad65fd3aff499e", "source": "<EMAIL>", "tags": []}, {"url": "https://tuleap.net/plugins/tracker/?aid=43357", "source": "<EMAIL>", "tags": []}]}
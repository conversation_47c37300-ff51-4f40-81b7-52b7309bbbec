{"cve_id": "CVE-2025-52812", "published_date": "2025-06-27T12:15:43.037", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in ApusWP Domnoo allows PHP Local File Inclusion. This issue affects Domnoo: from n/a through 1.49."}, {"lang": "es", "value": "Vulnerabilidad de control incorrecto del nombre de archivo para la instrucción Include/Require en programas PHP («Inclusión remota de archivos en PHP») en ApusWP Domnoo permite la inclusión local de archivos en PHP. Este problema afecta a Domnoo desde n/d hasta la versión 1.49."}], "references": [{"url": "https://patchstack.com/database/wordpress/theme/domnoo/vulnerability/wordpress-domnoo-1-49-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
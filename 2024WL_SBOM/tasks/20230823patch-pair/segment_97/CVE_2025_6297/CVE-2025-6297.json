{"cve_id": "CVE-2025-6297", "published_date": "2025-07-01T17:15:30.177", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "It was discovered that dpkg-deb does not properly sanitize directory permissions when extracting a control member into a temporary directory, which is\ndocumented as being a safe operation even on untrusted data. This may result in leaving temporary files behind on cleanup. Given automated and repeated execution of dpkg-deb commands on\nadversarial .deb packages or with well compressible files, placed\ninside a directory with permissions not allowing removal by a non-root\nuser, this can end up in a DoS scenario due to causing disk quota\nexhaustion or disk full conditions."}, {"lang": "es", "value": "Se descubrió que dpkg-deb no depura correctamente los permisos de directorio al extraer un miembro de control a un directorio temporal, lo cual se documenta como una operación segura incluso con datos no confiables. Esto puede resultar en la pérdida de archivos temporales durante la depuración. Si se ejecutan comandos dpkg-deb de forma automatizada y repetida en paquetes .deb adversarios o con archivos bien comprimibles, ubicados dentro de un directorio con permisos que impiden la eliminación por parte de un usuario no root, esto puede provocar un ataque de denegación de servicio (DoS) al causar el agotamiento de la cuota de disco o la saturación del disco."}], "references": [{"url": "https://git.dpkg.org/cgit/dpkg/dpkg.git/commit/?id=ed6bbd445dd8800308c67236ba35d08004c98e82", "source": "<EMAIL>", "tags": []}]}
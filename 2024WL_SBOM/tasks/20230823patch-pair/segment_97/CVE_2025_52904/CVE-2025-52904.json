{"cve_id": "CVE-2025-52904", "published_date": "2025-06-26T19:15:21.743", "last_modified_date": "2025-06-30T18:39:09.973", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit files. In version 2.32.0 of the web application, all users have a scope assigned, and they only have access to the files within that scope. The Command Execution feature of Filebrowser allows the execution of shell commands which are not restricted to the scope, potentially giving an attacker read and write access to all files managed by the server. Until this issue is fixed, the maintainers recommend to completely disable `Execute commands` for all accounts. Since the command execution is an inherently dangerous feature that is not used by all deployments, it should be possible to completely disable it in the application's configuration. As a defense-in-depth measure, organizations not requiring command execution should operate the Filebrowser from a distroless container image. A patch version has been pushed to disable the feature for all existent installations, and making it opt-in. A warning has been added to the documentation and is printed on the console if the feature is enabled. Due to the project being in maintenance-only mode, the bug has not been fixed. Fix is tracked on pull request 5199."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. En la versión 2.32.0 de la aplicación web, todos los usuarios tienen un ámbito asignado y solo tienen acceso a los archivos dentro de él. La función de Ejecución de Comandos del Explorador de Archivos permite la ejecución de comandos de shell sin restricciones de ámbito, lo que podría otorgar a un atacante acceso de lectura y escritura a todos los archivos administrados por el servidor. Hasta que se solucione este problema, los responsables recomiendan deshabilitar completamente la función \"Ejecutar comandos\" en todas las cuentas. Dado que la ejecución de comandos es una función inherentemente peligrosa que no se utiliza en todas las implementaciones, debería ser posible deshabilitarla por completo en la configuración de la aplicación. Como medida de defensa, las organizaciones que no requieran la ejecución de comandos deberían operar el Explorador de Archivos desde una imagen de contenedor sin distribución. Se ha publicado una versión de parche para deshabilitar la función en todas las instalaciones existentes y habilitarla. Se ha añadido una advertencia a la documentación que se muestra en la consola si la función está habilitada. Debido a que el proyecto se encuentra en modo de mantenimiento, el error no se ha corregido. La corrección se encuentra en la solicitud de incorporación de cambios 5199."}], "references": [{"url": "https://github.com/GoogleContainerTools/distroless", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/filebrowser/filebrowser/issues/5199", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-hc8f-m8g5-8362", "source": "<EMAIL>", "tags": []}, {"url": "https://sloonz.github.io/posts/sandboxing-1", "source": "<EMAIL>", "tags": []}]}
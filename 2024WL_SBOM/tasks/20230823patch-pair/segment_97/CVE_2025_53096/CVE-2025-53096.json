{"cve_id": "CVE-2025-53096", "published_date": "2025-07-01T02:15:22.717", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "<PERSON> is a self-hosted game stream host for Moonlight. Prior to version 2025.628.4510, the web UI of Sunshine lacks protection against Clickjacking attacks. This vulnerability allows an attacker to embed the Sunshine interface within a malicious website using an invisible or disguised iframe. If a user is tricked into interacting (one or multiple clicks) with the malicious page while authenticated, they may unknowingly perform actions within the Sunshine application without their consent. This issue has been patched in version 2025.628.4510."}, {"lang": "es", "value": "Sunshine es un servidor de streaming de juegos autoalojado para Moonlight. Antes de la versión 2025.628.4510, la interfaz web de Sunshine carecía de protección contra ataques de clickjacking. Esta vulnerabilidad permite a un atacante integrar la interfaz de Sunshine en un sitio web malicioso mediante un iframe invisible o camuflado. Si se engaña a un usuario para que interactúe (uno o varios clics) con la página maliciosa mientras está autenticado, podría realizar acciones dentro de la aplicación Sunshine sin su consentimiento. Este problema se ha corregido en la versión 2025.628.4510."}], "references": [{"url": "https://github.com/LizardByte/Sunshine/commit/2f27a57d01911436017f87bf08b9e36dcfaa86cc", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/LizardByte/Sunshine/security/advisories/GHSA-x97g-h2vp-g2c5", "source": "<EMAIL>", "tags": []}]}
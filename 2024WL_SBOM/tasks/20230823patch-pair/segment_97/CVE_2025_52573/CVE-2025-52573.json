{"cve_id": "CVE-2025-52573", "published_date": "2025-06-26T14:15:30.577", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "iOS Simulator MCP Server (ios-simulator-mcp) is a Model Context Protocol (MCP) server for interacting with iOS simulators. Versions prior to 1.3.3 are written in a way that is vulnerable to command injection vulnerability attacks as part of some of its MCP Server tool definition and implementation. The MCP Server exposes the tool `ui_tap` which relies on Node.js child process API `exec` which is an unsafe and vulnerable API if concatenated with untrusted user input. LLM exposed user input for `duration`, `udid`, and `x` and `y` args can be replaced with shell meta-characters like `;` or `&&` or others to change the behavior from running the expected command `idb` to another command. When LLMs are tricked through prompt injection (and other techniques and attack vectors) to call the tool with input that uses special shell characters such as `; rm -rf /tmp;#` and other payload variations, the full command-line text will be interepted by the shell and result in other commands except of `ps` executing on the host running the MCP Server. Version 1.3.3 contains a patch for the issue."}, {"lang": "es", "value": "iOS Simulator MCP Server (ios-simulator-mcp) es un servidor de protocolo de contexto de modelo (MCP) para interactuar con simuladores de iOS. Las versiones anteriores a la 1.3.3 están escritas de forma vulnerable a ataques de vulnerabilidad de inyección de comandos como parte de la definición e implementación de la herramienta del servidor MCP. El servidor MCP expone la herramienta `ui_tap`, que se basa en la API `exec` del proceso secundario de Node.js, que es una API insegura y vulnerable si se concatena con entradas de usuario no confiables. La entrada de usuario expuesta por LLM para los argumentos `duration`, `udid` y `x` e `y` se puede reemplazar con metacaracteres de shell como `;` o `&amp;&amp;` u otros para cambiar el comportamiento de ejecutar el comando esperado `idb` a otro comando. Cuando se engaña a los LLM mediante la inyección de prompt (y otras técnicas y vectores de ataque) para llamar a la herramienta con una entrada que usa caracteres de shell especiales como `;` rm -rf /tmp;#` y otras variaciones del payload, el texto completo de la línea de comandos será interpretado por el shell y provocará la ejecución de otros comandos, excepto `ps`, en el host que ejecuta el servidor MCP. La versión 1.3.3 incluye un parche para este problema."}], "references": [{"url": "https://github.com/joshuayoes/ios-simulator-mcp/blob/main/src/index.ts#L166-L207", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/joshuayoes/ios-simulator-mcp/commit/eb53a4f2cc8bbeb13e8d6d930f00167befcdb809", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/joshuayoes/ios-simulator-mcp/releases/tag/v1.3.3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/joshuayoes/ios-simulator-mcp/security/advisories/GHSA-6f6r-m9pv-67jw", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52569", "published_date": "2025-06-25T17:15:38.883", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "GitForge.jl is a unified interface for interacting with Git \"forges.\" Versions prior to 5.9.1 lack input validation of input validation for user-provided values in certain functions. In the `GitHub.repo()` function, the user can provide any string for the `repo_name` field. These inputs are not validated or safely encoded and are sent directly to the server. This means a user can add path traversal patterns like `../` in the input to access any other endpoints on `api.github.com` that were not intended. Users should upgrade immediately to v5.9.1 or later to receive a patch. All prior versions are vulnerable. No known workarounds are available."}, {"lang": "es", "value": "GitForge.jl es una interfaz unificada para interactuar con las forjas de Git. Las versiones anteriores a la 5.9.1 carecen de validación de entrada para los valores proporcionados por el usuario en ciertas funciones. En la función `GitHub.repo()`, el usuario puede proporcionar cualquier cadena para el campo `repo_name`. Estas entradas no se validan ni codifican de forma segura y se envían directamente al servidor. Esto significa que un usuario puede añadir patrones de path traversal como `../` en la entrada para acceder a cualquier otro endpoint en `api.github.com` que no estaba previsto. Los usuarios deben actualizar inmediatamente a la versión 5.9.1 o posterior para recibir un parche. Todas las versiones anteriores son vulnerables. No se conocen soluciones alternativas."}], "references": [{"url": "https://github.com/JuliaWeb/GitHub.jl/pull/224", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JuliaWeb/GitHub.jl/security/advisories/GHSA-jg9p-c3wh-q83x", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53013", "published_date": "2025-06-26T18:15:23.370", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Himmelblau is an interoperability suite for Microsoft Azure Entra ID and Intune. A vulnerability present in versions 0.9.10 through 0.9.16 allows a user to authenticate to a Linux host via Himmelblau using an *invalid* Linux Hello PIN, provided the host is offline. While the user gains access to the local system, Single Sign-On (SSO) fails due to the network being down and the inability to issue tokens (due to a failure to unlock the Hello key). The core issue lies in an incorrect assumption within the `acquire_token_by_hello_for_business_key` function: it was expected to return a `TPMFail` error for an invalid Hello key when offline, but instead, a preceding nonce request resulted in a `RequestFailed` error, leading the system to erroneously transition to an offline success state without validating the Hello key unlock. This impacts systems using Himmelblau for authentication when operating in an offline state with Hello PIN authentication enabled. Rocky Linux 8 (and variants) are not affected by this vulnerability. The problem is resolved in Himmelblau version 0.9.17. A workaround is available for users who cannot immediately upgrade. Disabling Hello PIN authentication by setting `enable_hello = false` in `/etc/himmelblau/himmelblau.conf` will mitigate the vulnerability."}, {"lang": "es", "value": "Himmelblau es una suite de interoperabilidad para Microsoft Azure Entra ID e Intune. Una vulnerabilidad presente en las versiones 0.9.10 a 0.9.16 permite a un usuario autenticarse en un host Linux a través de Himmelblau usando un PIN de Hello de Linux *inválido*, siempre que el host esté desconectado. Mientras el usuario accede al sistema local, el inicio de sesión único (SSO) falla debido a la caída de la red y a la imposibilidad de emitir tokens (debido a un fallo en el desbloqueo de la clave Hello). El problema principal radica en una suposición incorrecta dentro de la función `acquire_token_by_hello_for_business_key`: se esperaba que devolviera un error `TPMFail` para una clave Hello inválida al estar desconectado, pero en su lugar, una solicitud nonce anterior resultó en un error `RequestFailed`, lo que provocó que el sistema pasara erróneamente a un estado de desconexión exitosa sin validar el desbloqueo de la clave Hello. Esto afecta a los sistemas que usan Himmelblau para la autenticación cuando operan desconectados con la autenticación con PIN de Hello habilitada. Rocky Linux 8 (y sus variantes) no se ven afectados por esta vulnerabilidad. El problema se ha resuelto en la versión 0.9.17 de Himmelblau. Existe una solución alternativa para los usuarios que no puedan actualizar inmediatamente. Deshabilitar la autenticación con PIN de Hello configurando `enable_hello = false` en `/etc/himmelblau/himmelblau.conf` mitigará la vulnerabilidad."}], "references": [{"url": "https://github.com/himmelblau-idm/himmelblau/commit/64b03739f1d5ee472b1cff3ed20ed9af1c65a6f8", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/himmelblau-idm/himmelblau/commit/78477d684df710d57c10091c87b92665cfac98ae", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/himmelblau-idm/himmelblau/security/advisories/GHSA-j93j-pwm6-p97j", "source": "<EMAIL>", "tags": []}]}
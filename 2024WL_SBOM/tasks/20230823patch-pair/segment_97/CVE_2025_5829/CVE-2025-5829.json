{"cve_id": "CVE-2025-5829", "published_date": "2025-06-25T18:15:23.817", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "Autel MaxiCharger AC Wallbox Commercial autocharge Stack-based Buffer Overflow Remote Code Execution Vulnerability. This vulnerability allows physically present attackers to execute arbitrary code on affected  affected installations of Autel MaxiCharger AC Wallbox Commercial EV chargers. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the handling of JSON messages. The issue results from the lack of proper validation of the length of user-supplied data prior to copying it to a fixed-length stack-based buffer. An attacker can leverage this vulnerability to execute code in the context of the device. Was ZDI-CAN-26330."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por desbordamiento de búfer basado en pila en el cargador automático Autel MaxiCharger AC Wallbox Commercial. Esta vulnerabilidad permite a atacantes con presencia física ejecutar código arbitrario en las instalaciones afectadas de cargadores de vehículos eléctricos Autel MaxiCharger AC Wallbox Commercial. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en el manejo de mensajes JSON. El problema se debe a la falta de una validación adecuada de la longitud de los datos proporcionados por el usuario antes de copiarlos a un búfer basado en pila de longitud fija. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del dispositivo. Era ZDI-CAN-26330."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-348/", "source": "<EMAIL>", "tags": []}]}
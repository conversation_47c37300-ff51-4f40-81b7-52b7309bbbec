{"cve_id": "CVE-2025-6212", "published_date": "2025-06-26T10:15:25.747", "last_modified_date": "2025-07-08T11:35:01.817", "descriptions": [{"lang": "en", "value": "The Ultra Addons for Contact Form 7 plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Database module in versions 3.5.11 to 3.5.19 due to insufficient input sanitization and output escaping. The unfiltered field names are stored alongside the sanitized values. Later, the admin-side AJAX endpoint ajax_get_table_data() returns those raw names as JSON column headers, and the client-side DataTables renderer injects them directly into the DOM without any HTML encoding. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Ultra Addons para Contact Form 7 de WordPress es vulnerable a cross site scripting almacenado a través del módulo Base de datos en las versiones 3.5.11 a 3.5.19 debido a una depuración de entrada y un escape de salida insuficientes. Los nombres de campo sin filtrar se almacenan junto con los valores limpios. Posteriormente, el endpoint AJAX ajax_get_table_data() del lado del administrador devuelve estos nombres sin procesar como encabezados de columna JSON, y el renderizador DataTables del lado del cliente los inyecta directamente en el DOM sin codificación HTML. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/ultimate-addons-for-contact-form-7/trunk/addons/database/assets/js/database-pro-main.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/ultimate-addons-for-contact-form-7/trunk/addons/database/database.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3316177/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/ultimate-addons-for-contact-form-7/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f49e48cb-7d0b-4bcf-9090-869472b8442a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
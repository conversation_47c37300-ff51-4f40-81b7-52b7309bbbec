{"cve_id": "CVE-2025-6290", "published_date": "2025-06-26T02:15:22.733", "last_modified_date": "2025-07-08T11:32:32.773", "descriptions": [{"lang": "en", "value": "The Tournament Bracket Generator plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'bracket' shortcode in all versions up to, and including, 1.0.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Tournament Bracket Generator para WordPress es vulnerable a cross site scripting almacenado a través del shortcode \"bracket\" del complemento en todas las versiones hasta la 1.0.0 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wordpress.org/plugins/tournament-bracket-generator/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/bdde01aa-2d38-4085-b11a-ef8633ee928a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
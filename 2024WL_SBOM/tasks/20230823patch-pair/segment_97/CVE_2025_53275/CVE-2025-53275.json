{"cve_id": "CVE-2025-53275", "published_date": "2025-06-27T14:15:48.383", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in VaultDweller Leyka allows DOM-Based XSS. This issue affects Leyka: from n/a through 3.31.9."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en VaultDweller Leyka permite XSS basado en DOM. Este problema afecta a Leyka desde n/d hasta la versión 3.31.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/leyka/vulnerability/wordpress-leyka-plugin-3-31-9-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
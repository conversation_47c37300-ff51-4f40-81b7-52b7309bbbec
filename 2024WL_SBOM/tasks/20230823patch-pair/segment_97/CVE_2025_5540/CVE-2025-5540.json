{"cve_id": "CVE-2025-5540", "published_date": "2025-06-26T02:15:21.650", "last_modified_date": "2025-07-11T14:29:47.347", "descriptions": [{"lang": "en", "value": "The Event RSVP and Simple Event Management Plugin plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'emd_mb_meta' shortcode in all versions up to, and including, 4.1.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Event RSVP y Simple Event Management para WordPress es vulnerable a cross site scripting almacenado a través del shortcode 'emd_mb_meta' en todas las versiones hasta la 4.1.0 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wordpress.org/plugins/wp-easy-events/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f09ffc02-bfed-4aa3-a3d3-58e188b3e147?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-5940", "published_date": "2025-06-27T08:15:22.857", "last_modified_date": "2025-07-07T15:54:17.250", "descriptions": [{"lang": "en", "value": "The Osom Blocks – Custom Post Type listing block plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘class_name’ parameter in all versions up to, and including, 1.2.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Osom Blocks – Custom Post Type listing block para WordPress es vulnerable a cross site scripting almacenado a través del parámetro 'class_name' en todas las versiones hasta la 1.2.1 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/osomblocks/trunk/blocks/cpt-list/index.php#L171", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://wordpress.org/plugins/osomblocks/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/54e022df-0dc7-4f60-811d-48a92b723d55?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
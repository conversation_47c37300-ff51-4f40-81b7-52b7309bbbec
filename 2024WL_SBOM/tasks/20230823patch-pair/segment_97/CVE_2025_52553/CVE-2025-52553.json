{"cve_id": "CVE-2025-52553", "published_date": "2025-06-27T15:15:25.143", "last_modified_date": "2025-06-30T18:38:23.493", "descriptions": [{"lang": "en", "value": "authentik is an open-source identity provider. After authorizing access to a RAC endpoint, authentik creates a token which is used for a single connection and is sent to the client in the URL. This token is intended to only be valid for the session of the user who authorized the connection, however this check is missing in versions prior to 2025.6.3 and 2025.4.3. When, for example, using RAC during a screenshare, a malicious user could access the same session by copying the URL from the shown browser. authentik 2025.4.3 and 2025.6.3 fix this issue. As a workaround, it is recommended to decrease the duration a token is valid for (in the RAC Provider settings, set Connection expiry to `minutes=5` for example). The maintainers of authentik also recommend enabling the option Delete authorization on disconnect."}, {"lang": "es", "value": "Authentik es un proveedor de identidad de código abierto. Tras autorizar el acceso a un endpoint RAC, Authentik crea un token que se utiliza para una única conexión y se envía al cliente en la URL. Este token está diseñado para ser válido únicamente durante la sesión del usuario que autorizó la conexión; sin embargo, esta comprobación no está disponible en versiones anteriores a 2025.6.3 y 2025.4.3. <PERSON><PERSON> eje<PERSON><PERSON>, al usar RAC durante una pantalla compartida, un usuario malintencionado podría acceder a la misma sesión copiando la URL del navegador mostrado. Authentik 2025.4.3 y 2025.6.3 soluciona este problema. Como solución alternativa, se recomienda reducir la validez de un token (por ejemplo, en la configuración del proveedor RAC, establezca la caducidad de la conexión en `minutos=5`). Los desarrolladores de Authentik también recomiendan habilitar la opción \"Eliminar autorización al desconectar\"."}], "references": [{"url": "https://github.com/goauthentik/authentik/commit/0e07414e9739b318cff9401a413a5fe849545325", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goauthentik/authentik/commit/65373ab21711d58147b5cb9276c5b5876baaa5eb", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goauthentik/authentik/commit/7100d3c6741853f1cfe3ea2073ba01823ab55caa", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/goauthentik/authentik/security/advisories/GHSA-wr3v-9p2c-chx7", "source": "<EMAIL>", "tags": []}]}
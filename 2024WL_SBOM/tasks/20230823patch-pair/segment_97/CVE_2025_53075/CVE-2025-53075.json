{"cve_id": "CVE-2025-53075", "published_date": "2025-06-30T02:15:21.237", "last_modified_date": "2025-07-08T14:19:34.503", "descriptions": [{"lang": "en", "value": "Improper Input Validation vulnerability in Samsung Open Source rLottie allows Path Traversal.This issue affects rLottie: V0.2."}, {"lang": "es", "value": "La vulnerabilidad de validación de entrada incorrecta en Samsung Open Source rLottie permite el Path Traversal. Este problema afecta a rLottie: V0.2."}], "references": [{"url": "https://github.com/Samsung/rlottie/pull/571", "source": "<EMAIL>", "tags": ["Issue Tracking"]}]}
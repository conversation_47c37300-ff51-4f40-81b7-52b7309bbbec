{"cve_id": "CVE-2025-53206", "published_date": "2025-06-27T14:15:43.980", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in HT Plugins HT Mega – Absolute Addons for WPBakery Page Builder allows Stored XSS. This issue affects HT Mega – Absolute Addons for WPBakery Page Builder: from n/a through 1.0.8."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en HT Plugins HT Mega – Absolute Addons for WPBakery Page Builder permite XSS almacenado. Este problema afecta a HT Mega (Absolute Addons para WPBakery Page Builder) desde n/d hasta la versión 1.0.8."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/ht-mega-for-wpbakery/vulnerability/wordpress-ht-mega-absolute-addons-for-wpbakery-page-builder-plugin-1-0-8-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-52992", "published_date": "2025-06-27T14:15:41.990", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "The Nix, Lix, and Guix package managers fail to properly set permissions when a derivation build fails. This may allow arbitrary processes to modify the content of a store outside of the build sandbox. This affects <PERSON> before 2.24.15, 2.26.4, 2.28.4, and 2.29.1; Lix before 2.91.2, 2.92.2, and 2.93.1; and <PERSON><PERSON><PERSON> before 1.4.0-38.0e79d5b."}, {"lang": "es", "value": "Los gestores de paquetes Nix, Lix y Guix no configuran correctamente los permisos cuando falla una compilación derivada. Esto puede permitir que procesos arbitrarios modifiquen el contenido de un almacén fuera del entorno de pruebas de compilación. Esto afecta a Nix anteriores a 2.24.15, 2.26.4, 2.28.4 y 2.29.1; Lix anteriores a 2.91.2, 2.92.2 y 2.93.1; y Guix anteriores a 1.4.0-38.0e79d5b. "}], "references": [{"url": "https://discourse.nixos.org/t/security-advisory-privilege-escalations-in-nix-lix-and-guix/66017", "source": "<EMAIL>", "tags": []}, {"url": "https://guix.gnu.org/en/blog/2025/privilege-escalation-vulnerabilities-2025/", "source": "<EMAIL>", "tags": []}, {"url": "https://labs.snyk.io", "source": "<EMAIL>", "tags": []}, {"url": "https://lix.systems/blog/2025-06-24-lix-cves/", "source": "<EMAIL>", "tags": []}, {"url": "https://security-tracker.debian.org/tracker/CVE-2025-52992", "source": "<EMAIL>", "tags": []}, {"url": "https://security.snyk.io/vuln/?search=CVE-2025-52992", "source": "<EMAIL>", "tags": []}]}
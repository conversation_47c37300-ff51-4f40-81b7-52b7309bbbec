{"cve_id": "CVE-2025-5936", "published_date": "2025-06-27T08:15:22.497", "last_modified_date": "2025-07-07T15:55:10.410", "descriptions": [{"lang": "en", "value": "The VR Calendar plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 2.4.7. This is due to missing or incorrect nonce validation on the syncCalendar() function. This makes it possible for unauthenticated attackers to trigger a calendar sync via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento VR Calendar para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 2.4.7 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la función syncCalendar(). Esto permite que atacantes no autenticados activen la sincronización del calendario mediante una solicitud falsificada, ya que pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/vr-calendar-sync/trunk/Admin/Classes/VRCalendarAdmin.class.php#L98", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/57dbafe8-dcb3-4ac9-ad5e-76baf1963850?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
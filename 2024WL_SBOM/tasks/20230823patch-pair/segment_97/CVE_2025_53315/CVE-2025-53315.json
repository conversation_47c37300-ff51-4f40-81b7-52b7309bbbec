{"cve_id": "CVE-2025-53315", "published_date": "2025-06-27T14:15:54.050", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in alanft Relocate Upload allows Stored XSS. This issue affects Relocate Upload: from n/a through 0.24.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Request Forgery (CSRF) en alanft Relocate Upload permite XSS almacenado. Este problema afecta a Relocate Upload desde n/d hasta la versión 0.24.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/relocate-upload/vulnerability/wordpress-relocate-upload-plugin-0-24-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
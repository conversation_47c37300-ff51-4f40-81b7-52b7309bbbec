{"cve_id": "CVE-2025-52991", "published_date": "2025-06-27T14:15:41.253", "last_modified_date": "2025-06-30T18:38:48.477", "descriptions": [{"lang": "en", "value": "The Nix, Lix, and Guix package managers default to using temporary build directories in a world-readable and world-writable location. This allows standard users to deceive the package manager into using directories with pre-existing content, potentially leading to unauthorized actions or data manipulation. This affects <PERSON> before 2.24.15, 2.26.4, 2.28.4, and 2.29.1; Lix before 2.91.2, 2.92.2, and 2.93.1; and Guix before 1.4.0-38.0e79d5b."}, {"lang": "es", "value": "Los gestores de paquetes Nix, Lix y Guix utilizan por defecto directorios de compilación temporales en una ubicación legible y modificable para todos. Esto permite a los usuarios estándar engañar al gestor de paquetes para que utilice directorios con contenido preexistente, lo que podría provocar acciones no autorizadas o manipulación de datos. Esto afecta a Nix anteriores a las versiones 2.24.15, 2.26.4, 2.28.4 y 2.29.1; Lix anteriores a las versiones 2.91.2, 2.92.2 y 2.93.1; y Guix anteriores a la versión 1.4.0-38.0e79d5b."}], "references": [{"url": "https://discourse.nixos.org/t/security-advisory-privilege-escalations-in-nix-lix-and-guix/66017", "source": "<EMAIL>", "tags": []}, {"url": "https://guix.gnu.org/en/blog/2025/privilege-escalation-vulnerabilities-2025/", "source": "<EMAIL>", "tags": []}, {"url": "https://labs.snyk.io", "source": "<EMAIL>", "tags": []}, {"url": "https://lix.systems/blog/2025-06-24-lix-cves/", "source": "<EMAIL>", "tags": []}, {"url": "https://security-tracker.debian.org/tracker/CVE-2025-52991", "source": "<EMAIL>", "tags": []}, {"url": "https://security.snyk.io/vuln/?search=CVE-2025-52991", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-53100", "published_date": "2025-07-01T18:15:25.703", "last_modified_date": "2025-07-03T15:14:12.767", "descriptions": [{"lang": "en", "value": "RestDB's Codehooks.io MCP Server is an MCP server on the Codehooks.io platform. Prior to version 0.2.2, the MCP server is written in a way that is vulnerable to command injection attacks as part of some of its MCP Server tools definition and implementation. This could result in a user initiated remote command injection attack on a running MCP Server. This issue has been patched in version 0.2.2."}, {"lang": "es", "value": "RestDB's Codehooks.io MCP Server es un servidor MCP en la plataforma Codehooks.io. Antes de la versión 0.2.2, el servidor MCP estaba escrito de forma que era vulnerable a ataques de inyección de comandos como parte de la definición e implementación de algunas de sus herramientas. Esto podría resultar en un ataque de inyección de comandos remoto iniciado por el usuario en un servidor MCP en ejecución. Este problema se ha corregido en la versión 0.2.2."}], "references": [{"url": "https://github.com/RestDB/codehooks-mcp-server/commit/62f918a6fde6a8c700521b542b85315c70f05794", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RestDB/codehooks-mcp-server/commit/83db1d1b4c856cbe4a1b961d315706198bb0ffb8", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RestDB/codehooks-mcp-server/security/advisories/GHSA-fhq6-jf5q-qxvq", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-6258", "published_date": "2025-06-26T02:15:22.573", "last_modified_date": "2025-06-26T18:57:43.670", "descriptions": [{"lang": "en", "value": "The WP SoundSystem plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's wpsstm-track shortcode in all versions up to, and including, 3.4.2 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WP SoundSystem para WordPress es vulnerable a cross site scripting almacenado a través del shortcode wpsstm-track en todas las versiones hasta la 3.4.2 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://wordpress.org/plugins/wp-soundsystem/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f027626a-471c-48aa-add6-7597254dcfa9?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-7953", "published_date": "2025-07-22T04:15:34.427", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in Sanluan PublicCMS up to 5.202506.a. This issue affects some unknown processing of the file publiccms-parent/publiccms/src/main/webapp/resource/plugins/pdfjs/viewer.html. The manipulation of the argument File leads to open redirect. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The patch is named f1af17af004ca9345c6fe4d5936d87d008d26e75. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como problemática en Sanluan PublicCMS hasta la versión 5.202506.a. Este problema afecta a un procesamiento desconocido del archivo publiccms-parent/publiccms/src/main/webapp/resource/plugins/pdfjs/viewer.html. La manipulación del argumento \"File\" provoca una redirección abierta. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. El parche se llama f1af17af004ca9345c6fe4d5936d87d008d26e75. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/sanluan/PublicCMS/commit/f1af17af004ca9345c6fe4d5936d87d008d26e75", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/sanluan/PublicCMS/issues/88", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317099", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317099", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619279", "source": "<EMAIL>", "tags": []}]}
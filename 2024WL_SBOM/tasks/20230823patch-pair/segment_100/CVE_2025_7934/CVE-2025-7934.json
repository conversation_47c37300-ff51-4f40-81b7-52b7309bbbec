{"cve_id": "CVE-2025-7934", "published_date": "2025-07-21T19:15:32.670", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in fuyang_lipengjun platform up to ca9aceff6902feb7b0b6bf510842aea88430796a. This issue affects the function queryPage of the file platform-schedule/src/main/java/com/platform/controller/ScheduleJobController.java. The manipulation of the argument bean<PERSON>ame leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. This product does not use versioning. This is why information about affected and unaffected releases are unavailable."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en la plataforma fuyang_lipengjun hasta ca9aceff6902feb7b0b6bf510842aea88430796a. Este problema afecta a la función queryPage del archivo platform-schedule/src/main/java/com/platform/controller/ScheduleJobController.java. La manipulación del argumento beanName provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto no utiliza control de versiones. Por ello, no hay información disponible sobre las versiones afectadas y no afectadas."}], "references": [{"url": "https://gitee.com/fuyang_lipengjun/platform/issues/ICLILS", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317063", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317063", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.618977", "source": "<EMAIL>", "tags": []}]}
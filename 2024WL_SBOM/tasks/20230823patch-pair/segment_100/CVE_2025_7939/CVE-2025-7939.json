{"cve_id": "CVE-2025-7939", "published_date": "2025-07-21T21:15:27.380", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability was found in jerryshensjf JPACookieShop 蛋糕商城JPA版 1.0. It has been classified as critical. Affected is the function addGoods of the file GoodsController.java. The manipulation leads to unrestricted upload. It is possible to launch the attack remotely."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en jerryshensjf JPACookieShop ????JPA? 1.0. Se ha clasificado como crítica. La función addGoods del archivo GoodsController.java está afectada. La manipulación permite la carga sin restricciones. Es posible ejecutar el ataque de forma remota."}], "references": [{"url": "https://github.com/Bemcliu/cve-reports/blob/main/cve-03-%E8%9B%8B%E7%B3%95%E5%95%86%E5%9F%8EJPA%E7%89%88-Arbitrary%20File%20Upload/readme.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317076", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317076", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.618986", "source": "<EMAIL>", "tags": []}]}
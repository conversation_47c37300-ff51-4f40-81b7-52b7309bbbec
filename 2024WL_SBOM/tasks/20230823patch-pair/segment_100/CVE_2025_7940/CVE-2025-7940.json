{"cve_id": "CVE-2025-7940", "published_date": "2025-07-21T21:15:27.560", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Genshin Albedo Cat House App 1.0.2 on Android. It has been declared as problematic. Affected by this vulnerability is an unknown functionality of the file AndroidManifest.xml of the component com.house.auscat. The manipulation leads to improper export of android application components. Local access is required to approach this attack. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Genshin Albedo Cat House App 1.0.2 para Android. Se ha declarado problemática. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo AndroidManifest.xml del componente com.house.auscat. Esta manipulación provoca la exportación incorrecta de componentes de la aplicación para Android. Se requiere acceso local para este ataque. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/KMov-g/androidapps/blob/main/com.house.auscat.md", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/KMov-g/androidapps/blob/main/com.house.auscat.md#video-proof-of-concept", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317077", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317077", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619036", "source": "<EMAIL>", "tags": []}]}
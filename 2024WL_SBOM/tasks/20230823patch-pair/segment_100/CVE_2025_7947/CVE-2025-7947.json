{"cve_id": "CVE-2025-7947", "published_date": "2025-07-22T01:15:22.820", "last_modified_date": "2025-07-22T14:15:48.000", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in jshERP up to 3.5. Affected is an unknown function of the file /user/delete of the component Account <PERSON><PERSON>. The manipulation of the argument ID leads to improper authorization. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en jshERP hasta la versión 3.5. Afecta a una función desconocida del archivo /user/delete del componente Account <PERSON>. La manipulación del ID del argumento provoca una autorización incorrecta. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/jishenghua/jshERP/issues/124", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317088", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317088", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619276", "source": "<EMAIL>", "tags": []}]}
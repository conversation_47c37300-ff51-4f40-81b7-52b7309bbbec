{"cve_id": "CVE-2025-8018", "published_date": "2025-07-22T15:15:42.417", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Food Ordering Review System 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /user/reservation_page.php. The manipulation of the argument reg_Id leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Food Ordering Review System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /user/reservation_page.php. La manipulación del argumento reg_Id provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/i-Corner/cve/issues/10", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317221", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317221", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619379", "source": "<EMAIL>", "tags": []}]}
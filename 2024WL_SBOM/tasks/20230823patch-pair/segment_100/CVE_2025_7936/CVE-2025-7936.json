{"cve_id": "CVE-2025-7936", "published_date": "2025-07-21T20:15:56.613", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in fuyang_lipengjun platform up to ca9aceff6902feb7b0b6bf510842aea88430796a and classified as critical. Affected by this vulnerability is the function queryPage of the file com/platform/controller/ScheduleJobLogController.java. The manipulation of the argument beanName/methodName leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en la plataforma fuyang_lipengjun hasta ca9aceff6902feb7b0b6bf510842aea88430796a, clasificada como crítica. Esta vulnerabilidad afecta a la función queryPage del archivo com/platform/controller/ScheduleJobLogController.java. La manipulación del argumento beanName/methodName provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza el enfoque de lanzamiento continuo para garantizar una entrega continua. Por lo tanto, no se dispone de información sobre las versiones afectadas ni sobre las actualizadas."}], "references": [{"url": "https://gitee.com/fuyang_lipengjun/platform/issues/ICLIK1", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317065", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317065", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.618979", "source": "<EMAIL>", "tags": []}]}
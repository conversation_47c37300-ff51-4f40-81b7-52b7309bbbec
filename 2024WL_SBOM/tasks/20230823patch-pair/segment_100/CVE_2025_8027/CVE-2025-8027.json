{"cve_id": "CVE-2025-8027", "published_date": "2025-07-22T21:15:49.830", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "On 64-bit platforms IonMonkey-JIT only wrote 32 bits of the 64-bit return value space on the stack. Baseline-JIT, however, read the entire 64 bits. This vulnerability affects Firefox < 141, Firefox ESR < 115.26, Firefox ESR < 128.13, Firefox ESR < 140.1, Thunderbird < 141, Thunderbird < 128.13, and Thunderbird < 140.1."}, {"lang": "es", "value": "En plataformas de 64 bits, IonMonkey-JIT solo escribía 32 bits del espacio de valor de retorno de 64 bits en la pila. Sin embargo, Baseline-JIT leía los 64 bits completos. Esta vulnerabilidad afecta a Firefox &lt; 141, Firefox ESR &lt; 115.26, Firefox ESR &lt; 128.13, Firefox ESR &lt; 140.1, Thunderbird &lt; 141, Thunderbird &lt; 128.13 y Thunderbird &lt; 140.1."}], "references": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1968423", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-56/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-57/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-58/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-59/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-61/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-62/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-63/", "source": "<EMAIL>", "tags": []}]}
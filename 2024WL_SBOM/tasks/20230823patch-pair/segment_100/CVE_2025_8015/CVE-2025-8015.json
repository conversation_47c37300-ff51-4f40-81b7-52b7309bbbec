{"cve_id": "CVE-2025-8015", "published_date": "2025-07-22T15:15:42.113", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The WP Shortcodes Plugin — Shortcodes Ultimate plugin for WordPress is vulnerable to Stored Cross-Site Scripting via an uploaded image's 'Title' and 'Slide link' fields in all versions up to, and including, 7.4.2 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Author-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WP Shortcodes Plugin — Shortcodes Ultimate para WordPress es vulnerable a cross-site scripting almacenado a través de los campos \"Title\" y \"Slide link\" de una imagen subida en todas las versiones hasta la 7.4.2 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de autor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3328729/shortcodes-ultimate", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/deba0a29-7fe5-4f94-bee6-9d01e023215e?source=cve", "source": "<EMAIL>", "tags": []}]}
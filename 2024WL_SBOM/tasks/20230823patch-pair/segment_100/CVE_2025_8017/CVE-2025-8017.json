{"cve_id": "CVE-2025-8017", "published_date": "2025-07-22T14:15:48.403", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Tenda AC7 ***********. It has been classified as critical. Affected is the function formSetMacFilterCfg of the file /goform/setMacFilterCfg of the component httpd. The manipulation of the argument deviceList leads to stack-based buffer overflow. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Tenda AC7 ***********. Se ha clasificado como crítica. La función formSetMacFilterCfg del archivo /goform/setMacFilterCfg del componente httpd está afectada. La manipulación del argumento deviceList provoca un desbordamiento del búfer basado en la pila. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Thir0th/Thir0th-CVE/blob/main/Tenda_AC7%20V1.0_V***********%20has%20a%20stack%20overflow%20vulnerability%20in%20parse_macfilter_rule.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317220", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317220", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619364", "source": "<EMAIL>", "tags": []}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": []}]}
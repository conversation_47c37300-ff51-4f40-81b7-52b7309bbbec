{"cve_id": "CVE-2025-7944", "published_date": "2025-07-21T23:15:26.183", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Taxi Stand Management System 1.0. It has been classified as problematic. This affects an unknown part of the file /search.php. The manipulation of the argument searchdata leads to cross site scripting. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Taxi Stand Management System 1.0. Se ha clasificado como problemática. Afecta a una parte desconocida del archivo /search.php. La manipulación del argumento \"searchdata\" provoca ataques de cross site scripting. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/LagonGit/ReportCVE/issues/9", "source": "<EMAIL>", "tags": []}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317085", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317085", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619179", "source": "<EMAIL>", "tags": []}]}
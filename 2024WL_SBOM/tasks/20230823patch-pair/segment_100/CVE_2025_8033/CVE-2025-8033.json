{"cve_id": "CVE-2025-8033", "published_date": "2025-07-22T21:15:50.457", "last_modified_date": "2025-07-25T15:29:44.523", "descriptions": [{"lang": "en", "value": "The JavaScript engine did not handle closed generators correctly and it was possible to resume them leading to a nullptr deref. This vulnerability affects Firefox < 141, Firefox ESR < 115.26, Firefox ESR < 128.13, Firefox ESR < 140.1, Thunderbird < 141, Thunderbird < 128.13, and Thunderbird < 140.1."}, {"lang": "es", "value": "El motor de JavaScript no gestionaba correctamente los generadores cerrados y era posible resumirlos, lo que provocaba una desreferencia nullptr. Esta vulnerabilidad afecta a Firefox &lt; 141, Firefox ESR &lt; 115.26, Firefox ESR &lt; 128.13, Firefox ESR &lt; 140.1, Thunderbird &lt; 141, Thunderbird &lt; 128.13 y Thunderbird &lt; 140.1."}], "references": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1973990", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-56/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-57/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-58/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-59/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-61/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-62/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.mozilla.org/security/advisories/mfsa2025-63/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-7941", "published_date": "2025-07-21T22:15:34.277", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, was found in PHPGurukul Time Table Generator System 1.0. Affected is an unknown function of the file /admin/profile.php. The manipulation of the argument adminname leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en PHPGurukul Taxi Stand Management System 1.0. La vulnerabilidad afecta a una función desconocida del archivo /admin/profile.php. La manipulación del argumento \"adminname\" provoca cross site scripting. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://drive.google.com/file/d/19C2IWKR38Qqw7RLNn1nWzvWv4GszgPnU/view", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/LagonGit/ReportCVE/issues/6", "source": "<EMAIL>", "tags": []}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317082", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317082", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619141", "source": "<EMAIL>", "tags": []}]}
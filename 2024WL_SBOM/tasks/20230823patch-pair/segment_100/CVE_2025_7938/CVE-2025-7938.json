{"cve_id": "CVE-2025-7938", "published_date": "2025-07-21T20:15:56.803", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability was found in jerryshensjf JPACookieShop 蛋糕商城JPA版 1.0 and classified as critical. This issue affects the function updateGoods of the file GoodsController.java. The manipulation leads to authorization bypass. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en jerryshensjf JPACookieShop ????JPA? 1.0 y se clasificó como crítica. Este problema afecta a la función updateGoods del archivo GoodsController.java. La manipulación permite eludir la autorización. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/Bemcliu/cve-reports/blob/main/cve-02-%E8%9B%8B%E7%B3%95%E5%95%86%E5%9F%8EJPA%E7%89%88-Privilege%20Escalation/readme.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317075", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317075", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.618985", "source": "<EMAIL>", "tags": []}]}
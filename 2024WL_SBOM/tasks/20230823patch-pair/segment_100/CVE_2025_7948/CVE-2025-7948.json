{"cve_id": "CVE-2025-7948", "published_date": "2025-07-22T01:15:23.003", "last_modified_date": "2025-07-22T14:15:48.143", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in jshERP up to 3.5. Affected by this vulnerability is an unknown functionality of the file /jshERP-boot/user/updatePwd. The manipulation leads to weak password recovery. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en jshERP hasta la versión 3.5. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /jshERP-boot/user/updatePwd. La manipulación da como resultado una recuperación de contraseñas poco fiable. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/jishenghua/jshERP/issues/123", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317089", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317089", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619277", "source": "<EMAIL>", "tags": []}]}
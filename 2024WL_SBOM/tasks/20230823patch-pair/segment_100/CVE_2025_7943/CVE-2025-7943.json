{"cve_id": "CVE-2025-7943", "published_date": "2025-07-21T23:15:25.983", "last_modified_date": "2025-07-22T13:05:40.573", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Taxi Stand Management System 1.0 and classified as problematic. Affected by this issue is some unknown functionality of the file /admin/search-autoortaxi.php. The manipulation of the argument searchdata leads to cross site scripting. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Taxi Stand Management System 1.0 y se clasificó como problemática. Este problema afecta a una funcionalidad desconocida del archivo /admin/search-autoortaxi.php. La manipulación del argumento \"searchdata\" provoca cross site scripting. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/LagonGit/ReportCVE/issues/8", "source": "<EMAIL>", "tags": []}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317084", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317084", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619178", "source": "<EMAIL>", "tags": []}]}
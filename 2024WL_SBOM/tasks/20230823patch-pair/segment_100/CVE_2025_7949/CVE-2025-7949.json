{"cve_id": "CVE-2025-7949", "published_date": "2025-07-22T02:15:23.537", "last_modified_date": "2025-07-22T14:15:48.267", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Sanluan PublicCMS up to 5.202506.a. It has been declared as problematic. Affected by this vulnerability is an unknown functionality of the file publiccms-parent/publiccms/src/main/resources/templates/admin/cmsDiy/preview.html. The manipulation of the argument url leads to open redirect. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The patch is named c1e79f124e3f4c458315d908ed7dee06f9f12a76/f1af17af004ca9345c6fe4d5936d87d008d26e75. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Sanluan PublicCMS hasta la versión 5.202506.a. Se ha declarado problemática. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo publiccms-parent/publiccms/src/main/resources/templates/admin/cmsDiy/preview.html. La manipulación del argumento URL provoca una redirección abierta. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. El parche se llama c1e79f124e3f4c458315d908ed7dee06f9f12a76/f1af17af004ca9345c6fe4d5936d87d008d26e75. Se recomienda instalar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/sanluan/PublicCMS/commit/c1e79f124e3f4c458315d908ed7dee06f9f12a76", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/sanluan/PublicCMS/issues/87", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.317095", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.317095", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.619278", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27913", "published_date": "2025-03-10T20:15:14.500", "last_modified_date": "2025-06-19T00:14:38.870", "descriptions": [{"lang": "en", "value": "Passbolt API before 5, if the server is misconfigured (with an incorrect installation process and disregarding of Health Check results), can send email messages with a domain name taken from an attacker-controlled HTTP Host header."}, {"lang": "es", "value": "La API de Passbolt anterior a la versión 5, si el servidor está mal configurado (con un proceso de instalación incorrecto y sin tener en cuenta los resultados de la comprobación de estado), puede enviar mensajes de correo electrónico con un nombre de dominio tomado de un encabezado de host HTTP controlado por el atacante."}], "references": [{"url": "https://www.passbolt.com/incidents/host-header-injection", "source": "<EMAIL>", "tags": ["Vendor Advisory", "Mitigation"]}]}
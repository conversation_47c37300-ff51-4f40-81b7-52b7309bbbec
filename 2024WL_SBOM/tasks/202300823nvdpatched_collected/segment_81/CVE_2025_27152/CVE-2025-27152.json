{"cve_id": "CVE-2025-27152", "published_date": "2025-03-07T16:15:38.773", "last_modified_date": "2025-03-07T20:15:38.560", "descriptions": [{"lang": "en", "value": "axios is a promise based HTTP client for the browser and node.js. The issue occurs when passing absolute URLs rather than protocol-relative URLs to axios. Even if ⁠baseURL is set, axios sends the request to the specified absolute URL, potentially causing SSRF and credential leakage. This issue impacts both server-side and client-side usage of axios. This issue is fixed in 1.8.2."}, {"lang": "es", "value": "axios es un cliente HTTP basado en promesas para el navegador y node.js. El problema ocurre cuando se pasan URL absolutas en lugar de URL relativas al protocolo a axios. Incluso si se configura ?baseURL, axios envía la solicitud a la URL absoluta especificada, lo que puede provocar una fuga de credenciales y SSRF. Este problema afecta tanto al uso del lado del servidor como del lado del cliente de axios. Este problema se solucionó en 1.8.2."}], "references": [{"url": "https://github.com/axios/axios/issues/6463", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/axios/axios/security/advisories/GHSA-jr5f-v2jv-69x6", "source": "<EMAIL>", "tags": []}]}
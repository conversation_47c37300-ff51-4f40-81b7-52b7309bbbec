{"cve_id": "CVE-2025-25362", "published_date": "2025-03-05T21:15:19.803", "last_modified_date": "2025-04-02T13:15:44.063", "descriptions": [{"lang": "en", "value": "A Server-Side Template Injection (SSTI) vulnerability in Spacy-LLM v0.7.2 allows attackers to execute arbitrary code via injecting a crafted payload into the template field."}, {"lang": "es", "value": "Una vulnerabilidad de Server-Side Template Injection (SSTI) en Spacy-LLM v0.7.2 permite a los atacantes ejecutar código arbitrario mediante la inyección de un payload manipulado en el campo de plantilla."}], "references": [{"url": "https://github.com/explosion/spacy-llm/issues/492", "source": "<EMAIL>", "tags": []}, {"url": "https://www.hacktivesecurity.com/blog/2025/04/01/cve-2025-25362-old-vulnerabilities-new-victims-breaking-llm-prompts-with-ssti/", "source": "<EMAIL>", "tags": []}]}
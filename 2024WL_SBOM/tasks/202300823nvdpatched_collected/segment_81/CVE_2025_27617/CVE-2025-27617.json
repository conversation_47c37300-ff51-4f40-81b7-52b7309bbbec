{"cve_id": "CVE-2025-27617", "published_date": "2025-03-11T16:15:18.310", "last_modified_date": "2025-03-11T16:15:18.310", "descriptions": [{"lang": "en", "value": "Pimcore is an open source data and experience management platform. Prior to version 11.5.4, authenticated users can craft a filter string used to cause a SQL injection. Version 11.5.4 fixes the issue."}, {"lang": "es", "value": "Pimcore es una plataforma de código abierto para la gestión de datos y experiencias. Antes de la versión 11.5.4, los usuarios autenticados podían crear una cadena de filtro para generar una inyección SQL. La versión 11.5.4 soluciona este problema."}], "references": [{"url": "https://github.com/pimcore/pimcore/blob/c721a42c23efffd4ca916511ddb969598d302396/models/DataObject/ClassDefinition/Data/Extension/RelationFilterConditionParser.php#L29-L47", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pimcore/pimcore/blob/c721a42c23efffd4ca916511ddb969598d302396/models/DataObject/ClassDefinition/Data/Multiselect.php#L332-L347", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pimcore/pimcore/commit/19a8520895484e68fd254773e32476565d91deea", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pimcore/pimcore/security/advisories/GHSA-qjpx-5m2p-5pgh", "source": "<EMAIL>", "tags": []}]}
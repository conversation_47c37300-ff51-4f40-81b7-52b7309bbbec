{"cve_id": "CVE-2024-13350", "published_date": "2025-03-05T09:15:09.163", "last_modified_date": "2025-05-26T01:54:31.620", "descriptions": [{"lang": "en", "value": "The SearchIQ – The Search Solution plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'siq_searchbox' shortcode in all versions up to, and including, 4.7 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento SearchIQ – The Search Solution para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del código abreviado 'siq_searchbox' del complemento en todas las versiones hasta la 4.7 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/searchiq/trunk/library/shortcode.php#L132", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://wordpress.org/plugins/searchiq", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a04f074c-448d-4c5f-ae46-0ad1a3effdb4?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
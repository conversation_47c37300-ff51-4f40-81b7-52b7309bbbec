{"cve_id": "CVE-2025-21842", "published_date": "2025-03-07T09:15:17.237", "last_modified_date": "2025-03-07T09:15:17.237", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\namdkfd: properly free gang_ctx_bo when failed to init user queue\n\nThe destructor of a gtt bo is declared as\nvoid amdgpu_amdkfd_free_gtt_mem(struct amdgpu_device *adev, void **mem_obj);\nWhich takes void** as the second parameter.\n\nGCC allows passing void* to the function because void* can be implicitly\ncasted to any other types, so it can pass compiling.\n\nHowever, passing this void* parameter into the function's\nexecution process(which expects void** and dereferencing void**)\nwill result in errors."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: amdkfd: libera correctamente gang_ctx_bo cuando no se puede inicializar la cola de usuarios El destructor de un bo gtt se declara como void amdgpu_amdkfd_free_gtt_mem(struct amdgpu_device *adev, void **mem_obj); que toma void** como segundo parámetro. GCC permite pasar void* a la función porque void* se puede convertir implícitamente a cualquier otro tipo, por lo que puede pasar la compilación. Sin embargo, pasar este parámetro void* al proceso de ejecución de la función (que espera void** y desreferenciar void**) dará como resultado errores."}], "references": [{"url": "https://git.kernel.org/stable/c/091a68c58c1bbd2ab7d05d1b32c1306394ec691d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a33f7f9660705fb2ecf3467b2c48965564f392ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ae5ab1c1ae504f622cc1ff48830a9ed48428146d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
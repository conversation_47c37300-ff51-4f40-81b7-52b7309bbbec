{"cve_id": "CVE-2025-25927", "published_date": "2025-03-11T20:15:17.050", "last_modified_date": "2025-05-21T19:15:51.170", "descriptions": [{"lang": "en", "value": "A Cross-Site Request Forgery (CSRF) in Openmrs 2.4.3 Build 0ff0ed allows attackers to execute arbitrary operations via a crafted GET request."}, {"lang": "es", "value": "Cross-Site Request Forgery (CSRF) en Openmrs 2.4.3 Build 0ff0ed permite a los atacantes ejecutar operaciones arbitrarias a través de una solicitud GET manipulada."}], "references": [{"url": "https://github.com/johnchd/CVEs/blob/main/OpenMRS/CVE-2025-25927%20-%20CSRF%20via%20GET.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-2151", "published_date": "2025-03-10T13:15:36.497", "last_modified_date": "2025-05-28T18:28:41.050", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in Open Asset Import Library Assimp 5.4.3. This vulnerability affects the function Assimp::GetNextLine in the library ParsingUtils.h of the component File Handler. The manipulation leads to stack-based buffer overflow. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en Open Asset Import Library Assimp 5.4.3. Esta vulnerabilidad afecta a la función Assimp::GetNextLine en la librería ParsingUtils.h del componente File Handler. La manipulación provoca un desbordamiento del búfer basado en la pila. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6016", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}, {"url": "https://github.com/assimp/assimp/issues/6026", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}, {"url": "https://github.com/sae-as-me/Crashes/raw/refs/heads/main/assimp/assimp_crash_1", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://vuldb.com/?ctiid.299062", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299062", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.510582", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
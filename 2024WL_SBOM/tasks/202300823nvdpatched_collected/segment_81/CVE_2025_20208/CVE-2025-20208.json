{"cve_id": "CVE-2025-20208", "published_date": "2025-03-05T17:15:14.610", "last_modified_date": "2025-03-25T12:59:40.957", "descriptions": [{"lang": "en", "value": "A vulnerability in the web-based management interface of Cisco TelePresence Management Suite (TMS) could allow a low-privileged, remote attacker to conduct a cross-site scripting (XSS) attack against a user of the interface.  \r\n\r This vulnerability is due to insufficient input validation by the web-based management interface. An attacker could exploit this vulnerability by inserting malicious data in a specific data field in the interface. A successful exploit could allow the attacker to execute arbitrary script code in the context of the affected interface or access sensitive, browser-based information."}, {"lang": "es", "value": "Una vulnerabilidad en la interfaz de administración basada en web de Cisco TelePresence Management Suite (TMS) podría permitir que un atacante remoto con pocos privilegios realice un ataque de Cross-Site Scripting (XSS) contra un usuario de la interfaz. Esta vulnerabilidad se debe a una validación de entrada insuficiente por parte de la interfaz de administración basada en web. Un atacante podría aprovechar esta vulnerabilidad insertando datos maliciosos en un campo de datos específico de la interfaz. Una explotación exitosa podría permitir al atacante ejecutar código de scripts arbitrarios en el contexto de la interfaz afectada o acceder a información confidencial basada en el navegador."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-tms-xss-vuln-WbTcYwxG", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
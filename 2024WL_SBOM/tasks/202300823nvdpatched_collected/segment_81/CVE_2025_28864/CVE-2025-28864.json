{"cve_id": "CVE-2025-28864", "published_date": "2025-03-11T21:15:43.810", "last_modified_date": "2025-03-19T14:16:41.287", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Planet Studio Builder for Contact Form 7 by Webconstruct allows Cross Site Request Forgery. This issue affects Builder for Contact Form 7 by Webconstruct: from n/a through 1.2.2."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Planet Studio Builder para Contact Form 7 de Webconstruct permite Cross-Site Request Forgery. Este problema afecta a Builder para Contact Form 7 de Webconstruct desde n/d hasta la versión 1.2.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/cf7-builder/vulnerability/wordpress-builder-for-contact-form-7-by-webconstruct-plugin-1-2-2-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2024-58058", "published_date": "2025-03-06T16:15:52.037", "last_modified_date": "2025-03-25T14:35:24.150", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nubifs: skip dumping tnc tree when zroot is null\n\nClearing slab cache will free all znode in memory and make\nc->zroot.znode = NULL, then dumping tnc tree will access\nc->zroot.znode which cause null pointer dereference."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ubifs: omite el volcado del árbol tnc cuando zroot es nulo. Limpiar el caché de slab liberará todos los znode en la memoria y hará que c-&gt;zroot.znode = NULL, luego, al volcar el árbol tnc se accederá a c-&gt;zroot.znode, lo que provocará una desreferencia del puntero nulo."}], "references": [{"url": "https://git.kernel.org/stable/c/1787cd67bb94b106555ffe64f887f6aa24b47010", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2a987950df825d0144370e700dc5fb337684ffba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/40e25a3c0063935763717877bb2a814c081509ff", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/428aff8f7cfb0d9a8854477648022cef96bcab28", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6211c11fc20424bbc6d79c835c7c212b553ae898", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/77e5266e3d3faa6bdcf20d9c68a8972f6aa06522", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bdb0ca39e0acccf6771db49c3f94ed787d05f2d7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e01b55f261ccc96e347eba4931e4429d080d879d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
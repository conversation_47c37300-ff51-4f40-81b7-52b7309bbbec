{"cve_id": "CVE-2024-13908", "published_date": "2025-03-08T07:15:10.690", "last_modified_date": "2025-03-13T13:07:55.160", "descriptions": [{"lang": "en", "value": "The SMTP by BestWebSoft plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the 'save_options' function in all versions up to, and including, 1.1.9. This makes it possible for authenticated attackers, with Administrator-level access and above, to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento SMTP de BestWebSoft para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de validación del tipo de archivo en la función 'save_options' en todas las versiones hasta la 1.1.9 incluida. Esto hace posible que atacantes autenticados, con acceso de nivel de administrador o superior, carguen archivos arbitrarios en el servidor del sitio afectado, lo que puede hacer posible la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/bws-smtp/tags/1.1.8/includes/class-bwssmtp-settings.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3250935/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9f3b0637-b1ee-4e0b-95cd-11ac377805a7?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-2060", "published_date": "2025-03-07T03:15:33.457", "last_modified_date": "2025-05-21T18:44:15.670", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Emergency Ambulance Hiring Portal 1.0. It has been classified as critical. This affects an unknown part of the file /admin/admin-profile.php. The manipulation of the argument contactnumber leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Emergency Ambulance Hiring Portal 1.0. Se ha clasificado como crítica. Afecta a una parte desconocida del archivo /admin/admin-profile.php. La manipulación del argumento contactnumber provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/siznwaa/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298815", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298815", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514523", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
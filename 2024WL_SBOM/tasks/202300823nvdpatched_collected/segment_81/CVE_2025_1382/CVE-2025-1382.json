{"cve_id": "CVE-2025-1382", "published_date": "2025-03-09T06:15:11.837", "last_modified_date": "2025-05-21T16:54:27.347", "descriptions": [{"lang": "en", "value": "The Contact Us By Lord <PERSON>ress plugin through 2.6 does not have CSRF check in some places, and is missing sanitisation as well as escaping, which could allow attackers to make logged in admin add Stored XSS payloads via a CSRF attack."}, {"lang": "es", "value": "El complemento Contact Us By Lord <PERSON> de WordPress hasta la versión 2.6 no tiene verificación CSRF en algunos lugares y le falta depuración y escape, lo que podría permitir a los atacantes hacer que el administrador que haya iniciado sesión agregue payloads XSS almacenado a través de un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/a3002265-ac83-4c00-8afb-cbfbb4afc1e9/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
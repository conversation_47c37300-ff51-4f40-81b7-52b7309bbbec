{"cve_id": "CVE-2025-27773", "published_date": "2025-03-11T19:15:43.677", "last_modified_date": "2025-05-09T20:15:38.887", "descriptions": [{"lang": "en", "value": "The SimpleSAMLphp SAML2 library is a PHP library for SAML2 related functionality. Prior to versions 4.17.0 and 5.0.0-alpha.20, there is a signature confusion attack in the HTTPRedirect binding. An attacker with any signed SAMLResponse via the HTTP-Redirect binding can cause the application to accept an unsigned message. Versions 4.17.0 and 5.0.0-alpha.20 contain a fix for the issue."}, {"lang": "es", "value": "La librería SimpleSAMLphp SAML2 es una librería PHP para funcionalidades relacionadas con SAML2. En versiones anteriores a la 4.17.0 y la 5.0.0-alpha.20, existía un ataque de confusión de firmas en el enlace HTTPRedirect. Un atacante con cualquier SAMLResponse firmada mediante el enlace HTTP-Redirect podía provocar que la aplicación aceptara un mensaje sin firmar. Las versiones 4.17.0 y 5.0.0-alpha.20 incluyen una solución para este problema."}], "references": [{"url": "https://github.com/simplesamlphp/saml2/blob/9545abd0d9d48388f2fa00469c5c1e0294f0303e/src/SAML2/HTTPRedirect.php#L104-L113", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/simplesamlphp/saml2/blob/9545abd0d9d48388f2fa00469c5c1e0294f0303e/src/SAML2/HTTPRedirect.php#L178-L217", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/simplesamlphp/saml2/commit/7867d6099dc7f31bed1ea10e5bea159c5623d2a0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/simplesamlphp/saml2/security/advisories/GHSA-46r4-f8gj-xg56", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2025/05/msg00013.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
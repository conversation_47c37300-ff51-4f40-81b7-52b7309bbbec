{"cve_id": "CVE-2024-12607", "published_date": "2025-03-07T09:15:14.700", "last_modified_date": "2025-07-07T18:19:26.810", "descriptions": [{"lang": "en", "value": "The School Management System for Wordpress plugin for WordPress is vulnerable to SQL Injection via the 'id' parameter of the 'mj_smgt_show_event_task' AJAX action in all versions up to, and including, 92.0.0 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Custom-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento School Management System for Wordpress para WordPress es vulnerable a la inyección SQL a través del parámetro 'id' de la acción AJAX 'mj_smgt_show_event_task' en todas las versiones hasta la 92.0.0 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel personalizado y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/school-management-system-for-wordpress/11470032", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/175fe7f4-ac92-4c52-9889-47635c21cd9b?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
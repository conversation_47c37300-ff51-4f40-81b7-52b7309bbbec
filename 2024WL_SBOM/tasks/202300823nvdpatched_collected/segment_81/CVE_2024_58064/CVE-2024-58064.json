{"cve_id": "CVE-2024-58064", "published_date": "2025-03-06T16:15:52.733", "last_modified_date": "2025-03-25T14:37:32.823", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: cfg80211: tests: Fix potential NULL dereference in test_cfg80211_parse_colocated_ap()\n\nkunit_kzalloc() may return NULL, dereferencing it without NULL check may\nlead to NULL dereference.\nAdd a NULL check for ies."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: cfg80211: pruebas: Se corrige la posible desreferenciación de NULL en test_cfg80211_parse_colocated_ap() kunit_kzalloc() puede devolver NULL, desreferenciarlo sin la comprobación de NULL puede provocar una desreferencia de NULL. Agregue una comprobación de NULL para ies."}], "references": [{"url": "https://git.kernel.org/stable/c/0d17d81143f5aa56ee87e60bb1000a2372a0ada8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/13c4f7714c6a1ecf748a2f22099447c14fe6ed8c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/886271409603956edd09df229dde7442c410a872", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
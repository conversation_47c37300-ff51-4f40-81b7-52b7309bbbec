{"cve_id": "CVE-2024-58069", "published_date": "2025-03-06T16:15:53.373", "last_modified_date": "2025-03-25T14:48:40.760", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nrtc: pcf85063: fix potential OOB write in PCF85063 NVMEM read\n\nThe nvmem interface supports variable buffer sizes, while the regmap\ninterface operates with fixed-size storage. If an nvmem client uses a\nbuffer size less than 4 bytes, regmap_read will write out of bounds\nas it expects the buffer to point at an unsigned int.\n\nFix this by using an intermediary unsigned int to hold the value."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rtc: pcf85063: se corrige una posible escritura OOB en la lectura NVMEM PCF85063 La interfaz nvmem admite tamaños de búfer variables, mientras que la interfaz regmap opera con almacenamiento de tamaño fijo. Si un cliente nvmem usa un tamaño de búfer menor a 4 bytes, regmap_read escribirá fuera de los límites ya que espera que el búfer apunte a una int sin signo. Corrija esto usando una int sin signo intermedia para contener el valor."}], "references": [{"url": "https://git.kernel.org/stable/c/21cd59fcb9952eb7505da2bdfc1eb9c619df3ff4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3ab8c5ed4f84fa20cd16794fe8dc31f633fbc70c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/517aedb365f2c94e2d7e0b908ac7127df76203a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6f2a8ca9a0a38589f52a7f0fb9425b9ba987ae7c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/9adefa7b9559d0f21034a5d5ec1b55840c9348b9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c72b7a474d3f445bf0c5bcf8ffed332c78eb28a1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e5536677da803ed54a29a446515c28dce7d3d574", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e5e06455760f2995b16a176033909347929d1128", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
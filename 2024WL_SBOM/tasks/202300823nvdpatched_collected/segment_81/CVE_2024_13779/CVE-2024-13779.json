{"cve_id": "CVE-2024-13779", "published_date": "2025-03-05T10:15:16.213", "last_modified_date": "2025-03-05T10:15:16.213", "descriptions": [{"lang": "en", "value": "The Hero Mega Menu - Responsive WordPress Menu Plugin plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the 'index' parameter in all versions up to, and including, 1.16.5 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Hero Mega Menu - Responsive WordPress Menu Plugin para WordPress es vulnerable a Cross-Site Scripting reflejado a través del parámetro 'index' en todas las versiones hasta la 1.16.5 incluida, debido a una depuración de entrada insuficiente y al escape de salida. Esto permite que atacantes no autenticados inyecten secuencias de comandos web arbitrarias en páginas que se ejecutan si logran engañar a un usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://codecanyon.net/item/hero-menu-responsive-wordpress-mega-menu-plugin/10324895", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/18d37650-057d-4cd1-bfeb-e40885d22566?source=cve", "source": "<EMAIL>", "tags": []}]}
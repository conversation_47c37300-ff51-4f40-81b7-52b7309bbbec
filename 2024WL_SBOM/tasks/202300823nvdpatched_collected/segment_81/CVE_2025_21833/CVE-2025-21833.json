{"cve_id": "CVE-2025-21833", "published_date": "2025-03-06T17:15:23.293", "last_modified_date": "2025-03-25T14:27:52.403", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\niommu/vt-d: Avoid use of NULL after WARN_ON_ONCE\n\nThere is a WARN_ON_ONCE to catch an unlikely situation when\ndomain_remove_dev_pasid can't find the `pasid`. In case it nevertheless\nhappens we must avoid using a NULL pointer."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: iommu/vt-d: evitar el uso de NULL después de WARN_ON_ONCE Existe un WARN_ON_ONCE para detectar una situación poco probable en la que domain_remove_dev_pasid no puede encontrar el `pasid`. En caso de que ocurra, debemos evitar el uso de un puntero NULL."}], "references": [{"url": "https://git.kernel.org/stable/c/60f030f7418d3f1d94f2fb207fe3080e1844630b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/df96876be3b064aefc493f760e0639765d13ed0d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
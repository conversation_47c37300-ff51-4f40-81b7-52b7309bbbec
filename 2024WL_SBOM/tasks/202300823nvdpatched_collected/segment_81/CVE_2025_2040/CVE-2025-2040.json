{"cve_id": "CVE-2025-2040", "published_date": "2025-03-06T20:15:38.920", "last_modified_date": "2025-07-07T18:29:28.303", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in zhijiantianya ruoyi-vue-pro 2.4.1. Affected by this vulnerability is an unknown functionality of the file /admin-api/bpm/model/deploy. The manipulation leads to improper neutralization of special elements used in a template engine. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en zhijiantianya ruoyi-vue-pro 2.4.1. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin-api/bpm/model/deploy. La manipulación conduce a la neutralización incorrecta de elementos especiales utilizados en un motor de plantillas. El ataque puede ejecutarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/ruoyi-vue-pro.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298783", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298783", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512574", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
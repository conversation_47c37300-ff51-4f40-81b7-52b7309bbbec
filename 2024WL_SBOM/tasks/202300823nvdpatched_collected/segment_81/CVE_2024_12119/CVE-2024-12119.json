{"cve_id": "CVE-2024-12119", "published_date": "2025-03-08T06:15:36.397", "last_modified_date": "2025-03-13T13:11:16.820", "descriptions": [{"lang": "en", "value": "The FooGallery – Responsive Photo Gallery, Image Viewer, Justified, Masonry & Carousel plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the default_gallery_title_size parameter in all versions up to, and including, 2.4.29 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with granted gallery and album creator roles, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento FooGallery – Responsive Photo Gallery, Image Viewer, Justified, Masonry &amp; Carousel para WordPress es vulnerable a cross site scripting almacenado a través del parámetro default_gallery_title_size en todas las versiones hasta la 2.4.29 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes autenticados, con roles de creador de galerías y álbumes otorgados, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://github.com/fooplugins/foogallery/blob/master/extensions/albums/album-default.php#L26", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/foogallery/tags/2.4.27/extensions/albums/album-default.php#L26", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2070c6e6-d830-4d1c-9408-5cb2254a00e5?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-27624", "published_date": "2025-03-05T23:15:14.197", "last_modified_date": "2025-06-24T00:45:20.613", "descriptions": [{"lang": "en", "value": "A cross-site request forgery (CSRF) vulnerability in Jenkins 2.499 and earlier, LTS 2.492.1 and earlier allows attackers to have users toggle their collapsed/expanded status of sidepanel widgets (e.g., Build Queue and Build Executor Status widgets)."}, {"lang": "es", "value": "Una vulnerabilidad de Cross Site Request Forgery (CSRF) en Jenkins 2.499 y anteriores, LTS 2.492.1 y anteriores permite a los atacantes hacer que los usuarios alternen el estado contraído/expandido de los widgets del panel lateral (por ejemplo, los widgets Cola de compilación y Estado del ejecutor de compilación)."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-03-05/#SECURITY-3498", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}
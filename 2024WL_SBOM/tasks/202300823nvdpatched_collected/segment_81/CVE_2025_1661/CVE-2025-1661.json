{"cve_id": "CVE-2025-1661", "published_date": "2025-03-11T04:15:24.803", "last_modified_date": "2025-03-19T20:48:03.360", "descriptions": [{"lang": "en", "value": "The HUSKY – Products Filter Professional for WooCommerce plugin for WordPress is vulnerable to Local File Inclusion in all versions up to, and including, ******* via the 'template' parameter of the woof_text_search AJAX action. This makes it possible for unauthenticated attackers to include and execute arbitrary files on the server, allowing the execution of any PHP code in those files. This can be used to bypass access controls, obtain sensitive data, or achieve code execution in cases where images and other “safe” file types can be uploaded and included."}, {"lang": "es", "value": "El complemento HUSKY – Products Filter Professional para WooCommerce para WordPress es vulnerable a la inclusión de archivos locales en todas las versiones hasta la ******* incluida, a través del parámetro 'template' de la acción AJAX woof_text_search. Esto permite que atacantes no autenticados incluyan y ejecuten archivos arbitrarios en el servidor, lo que permite la ejecución de cualquier código PHP en esos archivos. Esto se puede utilizar para eludir los controles de acceso, obtener datos confidenciales o lograr la ejecución de código en casos en los que se puedan cargar e incluir imágenes y otros tipos de archivos \"seguros\"."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/woocommerce-products-filter/trunk/ext/by_text/index.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3249621%40woocommerce-products-filter&new=3249621%40woocommerce-products-filter&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3253169%40woocommerce-products-filter&new=3253169%40woocommerce-products-filter&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9ae7b6fc-2120-4573-8b1b-d5422d435fa5?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
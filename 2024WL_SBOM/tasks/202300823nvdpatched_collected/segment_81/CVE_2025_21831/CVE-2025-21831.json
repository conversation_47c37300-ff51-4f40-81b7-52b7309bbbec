{"cve_id": "CVE-2025-21831", "published_date": "2025-03-06T17:15:23.060", "last_modified_date": "2025-03-06T17:15:23.060", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nPCI: Avoid putting some root ports into D3 on TUXEDO Sirius Gen1\n\ncommit 9d26d3a8f1b0 (\"PCI: Put PCIe ports into D3 during suspend\") sets the\npolicy that all PCIe ports are allowed to use D3.  When the system is\nsuspended if the port is not power manageable by the platform and won't be\nused for wakeup via a PME this sets up the policy for these ports to go\ninto D3hot.\n\nThis policy generally makes sense from an OSPM perspective but it leads to\nproblems with wakeup from suspend on the TUXEDO Sirius 16 Gen 1 with a\nspecific old BIOS. This manifests as a system hang.\n\nOn the affected Device + BIOS combination, add a quirk for the root port of\nthe problematic controller to ensure that these root ports are not put into\nD3hot at suspend.\n\nThis patch is based on\n\n  https://lore.kernel.org/linux-pci/<EMAIL>\n\nbut with the added condition both in the documentation and in the code to\napply only to the TUXEDO Sirius 16 Gen 1 with a specific old BIOS and only\nthe affected root ports."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: PCI: evitar poner algunos puertos raíz en D3 en TUXEDO Sirius Gen1 commit 9d26d3a8f1b0 (\"PCI: poner puertos PCIe en D3 durante la suspensión\") establece la política de que todos los puertos PCIe pueden usar D3. Cuando el sistema está suspendido, si la plataforma no puede administrar la energía del puerto y no se usará para la activación a través de un PME, esto configura la política para que estos puertos entren en D3hot. Esta política generalmente tiene sentido desde una perspectiva OSPM, pero genera problemas con la activación desde la suspensión en TUXEDO Sirius 16 Gen 1 con un BIOS antiguo específico. Esto se manifiesta como un bloqueo del sistema. En la combinación de dispositivo + BIOS afectada, agregue una peculiaridad para el puerto raíz del controlador problemático para garantizar que estos puertos raíz no se coloquen en D3hot en la suspensión. Este parche está basado en https://lore.kernel.org/linux-pci/<EMAIL> pero con la condición adicional tanto en la documentación como en el código de aplicarse solo a TUXEDO Sirius 16 Gen 1 con un BIOS antiguo específico y solo los puertos raíz afectados."}], "references": [{"url": "https://git.kernel.org/stable/c/5ee3dd6e59b834e4d66e8b16fc684749ee40a257", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8852e056e297df1d8635ee7504e780d3184e45d0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a78dfe50fffe6058afed2bb04c50c2c9a16664ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b1049f2d68693c80a576c4578d96774a68df2bad", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
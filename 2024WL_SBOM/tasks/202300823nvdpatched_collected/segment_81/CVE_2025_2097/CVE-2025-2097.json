{"cve_id": "CVE-2025-2097", "published_date": "2025-03-07T23:15:16.263", "last_modified_date": "2025-04-03T15:28:41.053", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in TOTOLINK EX1800T 9.1.0cu.2112_B20220316. This issue affects the function setRptWizardCfg of the file /cgi-bin/cstecgi.cgi. The manipulation of the argument loginpass leads to stack-based buffer overflow. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en TOTOLINK EX1800T 9.1.0cu.2112_B20220316. Este problema afecta a la función setRptWizardCfg del archivo /cgi-bin/cstecgi.cgi. La manipulación del argumento loginpass provoca un desbordamiento del búfer basado en la pila. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/kn0sky/cve/blob/main/TOTOLINK%20EX1800T/Stack-based%20Buffer%20Overflow%2001%20setRptWizardCfg-_loginpass.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298955", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298955", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.515326", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}
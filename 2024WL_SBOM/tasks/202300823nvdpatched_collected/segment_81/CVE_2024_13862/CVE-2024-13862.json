{"cve_id": "CVE-2024-13862", "published_date": "2025-03-11T06:15:26.303", "last_modified_date": "2025-05-21T19:33:15.200", "descriptions": [{"lang": "en", "value": "The S3Bubble Media Streaming (AWS|Elementor|YouTube|Vimeo Functionality) WordPress plugin through 8.0 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin"}, {"lang": "es", "value": "El complemento S3Bubble Media Streaming (AWS|Elementor|YouTube|Vimeo Functionality) de WordPress hasta la versión 8.0 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios elevados, como administradores."}], "references": [{"url": "https://wpscan.com/vulnerability/7692b768-a33f-45a2-90f1-1f4258493979/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
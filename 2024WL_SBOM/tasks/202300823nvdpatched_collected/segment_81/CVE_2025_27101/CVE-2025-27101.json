{"cve_id": "CVE-2025-27101", "published_date": "2025-03-11T22:15:13.390", "last_modified_date": "2025-03-12T14:15:16.203", "descriptions": [{"lang": "en", "value": "Opal is OBiBa’s core database application for biobanks or epidemiological studies. Prior to version 5.1.1, when copying any parent directory to a folder in the /temp/ directory, all files in that parent directory are copied, including files which the user should not have access to. All users of the application are impacted, as this is exploitable by any user to reveal all files in the opal filesystem. This also means that low-privilege users such as DataShield users can retrieve the files of other users. Version 5.1.1 contains a patch for the issue."}, {"lang": "es", "value": "Opal es la aplicación principal de OBiBa para biobancos o estudios epidemiológicos. Antes de la versión 5.1.1, al copiar cualquier directorio principal a una carpeta del directorio /temp/, se copiaban todos los archivos de dicho directorio, incluidos aquellos a los que el usuario no debería tener acceso. Todos los usuarios de la aplicación se ven afectados, ya que cualquier usuario puede explotar esta vulnerabilidad para revelar todos los archivos del sistema de archivos Opal. Esto también significa que usuarios con pocos privilegios, como los de DataShield, pueden recuperar los archivos de otros usuarios. La versión 5.1.1 incluye un parche para este problema."}], "references": [{"url": "https://github.com/obiba/opal/commit/fca7dc9c8348064741b2e8b2c31b66660a935743", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/obiba/opal/security/advisories/GHSA-rxmx-gqjj-vhv8", "source": "<EMAIL>", "tags": []}]}
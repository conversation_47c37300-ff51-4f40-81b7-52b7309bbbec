{"cve_id": "CVE-2025-25294", "published_date": "2025-03-06T19:15:27.267", "last_modified_date": "2025-03-06T19:15:27.267", "descriptions": [{"lang": "en", "value": "Envoy Gateway is an open source project for managing Envoy Proxy as a standalone or Kubernetes-based application gateway. In all Envoy Gateway versions prior to 1.2.7 and 1.3.1 a default Envoy Proxy access log configuration is used. This format is vulnerable to log injection attacks. If the attacker uses a specially crafted user-agent which performs json injection, then he could add and overwrite fields to the access log. This vulnerability is fixed in 1.3.1 and 1.2.7. One can overwrite the old text based default format with JSON formatter by modifying the \"EnvoyProxy.spec.telemetry.accessLog\" setting."}, {"lang": "es", "value": "Envoy Gateway es un proyecto de código abierto para administrar Envoy Proxy como una puerta de enlace de aplicaciones independiente o basada en Kubernetes. En todas las versiones de Envoy Gateway anteriores a 1.2.7 y 1.3.1 se utiliza una configuración de registro de acceso de Envoy Proxy predeterminada. Este formato es vulnerable a ataques de inyección de registros. Si el atacante utiliza un agente de usuario especialmente manipulado que realiza una inyección JSON, podría agregar y sobrescribir campos en el registro de acceso. Esta vulnerabilidad se solucionó en 1.3.1 y 1.2.7. Se puede sobrescribir el antiguo formato predeterminado basado en texto con el formateador JSON modificando la configuración \"EnvoyProxy.spec.telemetry.accessLog\"."}], "references": [{"url": "https://github.com/envoyproxy/gateway/commit/8f48f5199cf1bbb9a8ac0695c5171bfef6c9198a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/envoyproxy/gateway/security/advisories/GHSA-mf24-chxh-hmvj", "source": "<EMAIL>", "tags": []}]}
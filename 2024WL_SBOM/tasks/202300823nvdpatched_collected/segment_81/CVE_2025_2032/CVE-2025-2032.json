{"cve_id": "CVE-2025-2032", "published_date": "2025-03-06T17:15:24.297", "last_modified_date": "2025-05-12T20:43:29.347", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in ChestnutCMS 1.5.2. This vulnerability affects the function renameFile of the file /cms/file/rename. The manipulation of the argument rename leads to path traversal. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en ChestnutCMS 1.5.2. Esta vulnerabilidad afecta a la función renameFile del archivo /cms/file/rename. La manipulación del argumento rename provoca un path traversal. La vulnerabilidad se ha hecho pública y puede utilizarse."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/7", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.298774", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298774", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512030", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
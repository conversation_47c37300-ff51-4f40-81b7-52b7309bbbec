{"cve_id": "CVE-2024-53694", "published_date": "2025-03-07T17:15:20.103", "last_modified_date": "2025-03-07T17:15:20.103", "descriptions": [{"lang": "en", "value": "A time-of-check time-of-use (TOCTOU) race condition vulnerability has been reported to affect several product versions. If exploited, the vulnerability could allow local attackers who have gained user access to gain access to otherwise unauthorized resources.\n\nWe have already fixed the vulnerability in the following versions:\nQVPN Device Client for Mac 2.2.5 and later\nQsync for Mac 5.1.3 and later\nQfinder Pro Mac 7.11.1 and later"}, {"lang": "es", "value": "Se ha informado de una vulnerabilidad de condición de ejecución de tiempo de uso y tiempo de verificación (TOCTOU) que afecta a varias versiones del producto. Si se explota, la vulnerabilidad podría permitir que los atacantes locales que hayan obtenido acceso de usuario obtengan acceso a recursos que de otro modo no estarían autorizados. Ya hemos corregido la vulnerabilidad en las siguientes versiones: QVPN Device Client para Mac 2.2.5 y posteriores Qsync para Mac 5.1.3 y posteriores Qfinder Pro Mac 7.11.1 y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-24-51", "source": "<EMAIL>", "tags": []}]}
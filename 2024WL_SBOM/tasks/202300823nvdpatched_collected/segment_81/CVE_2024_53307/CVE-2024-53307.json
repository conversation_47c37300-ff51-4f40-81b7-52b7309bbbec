{"cve_id": "CVE-2024-53307", "published_date": "2025-03-10T18:15:29.373", "last_modified_date": "2025-06-23T20:08:13.233", "descriptions": [{"lang": "en", "value": "A reflected cross-site scripting (XSS) vulnerability in the /mw/ endpoint of Evisions MAPS v6.10.2.267 allows attackers to execute arbitrary code in the context of a user's browser via injecting a crafted payload."}, {"lang": "es", "value": "Una vulnerabilidad de cross-site scripting (XSS) reflejad en el endpoint /mw/ de Evisions MAPS v6.10.2.267 permite a los atacantes ejecutar código arbitrario en el contexto del navegador de un usuario mediante la inyección de un payload manipulado."}], "references": [{"url": "https://gist.github.com/Xib3rR4dAr/bf754848f1cd77162f79226144b04648", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://webhelp.evisions.com/releaseguides/maps/default.htm#6.11/6.11%20Release%20Notes.htm?TocPath=MAPS%25206.11%2520Release%2520Guide%257C_____3", "source": "<EMAIL>", "tags": ["Release Notes"]}]}
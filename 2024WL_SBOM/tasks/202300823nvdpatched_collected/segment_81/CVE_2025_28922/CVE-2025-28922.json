{"cve_id": "CVE-2025-28922", "published_date": "2025-03-11T21:15:49.930", "last_modified_date": "2025-03-11T21:15:49.930", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Terence D. Go To Top allows Stored XSS. This issue affects Go To Top: from n/a through 0.0.8."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en Terence <PERSON> To Top permite XSS almacenado. Este problema afecta a Go To Top desde n/d hasta la versión 0.0.8."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/go-to-top/vulnerability/wordpress-go-to-top-plugin-0-0-8-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
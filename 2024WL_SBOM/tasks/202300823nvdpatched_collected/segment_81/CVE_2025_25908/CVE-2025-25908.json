{"cve_id": "CVE-2025-25908", "published_date": "2025-03-10T22:15:27.150", "last_modified_date": "2025-06-23T20:13:31.600", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in tianti v2.3 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the coverImageURL parameter at /article/ajax/save."}, {"lang": "es", "value": "Una vulnerabilidad de cross-site scripting (XSS) almacenado en tianti v2.3 permite a los atacantes ejecutar scripts web o HTML arbitrarios mediante la inyección de un payload manipulado en el parámetro coverImageURL en /article/ajax/save."}], "references": [{"url": "https://github.com/xujeff/tianti/issues/40", "source": "<EMAIL>", "tags": ["Exploit"]}]}
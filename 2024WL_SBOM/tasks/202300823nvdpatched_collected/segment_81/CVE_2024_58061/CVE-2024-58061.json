{"cve_id": "CVE-2024-58061", "published_date": "2025-03-06T16:15:52.390", "last_modified_date": "2025-03-06T16:15:52.390", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: mac80211: prohibit deactivating all links\n\nIn the internal API this calls this is a WARN_ON, but that\nshould remain since internally we want to know about bugs\nthat may cause this. Prevent deactivating all links in the\ndebugfs write directly."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: mac80211: prohíbe desactivar todos los enlaces En la API interna, esto es un WARN_ON, pero eso debería permanecer ya que internamente queremos saber sobre los errores que pueden causar esto. Evite desactivar todos los enlaces en la escritura debugfs directamente."}], "references": [{"url": "https://git.kernel.org/stable/c/18100796c11dfdea9101fdc95d2428b2093477ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/270ad6776e7cf1be3b769e0447070f9d0e8269db", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7553477cbfd784b128297f9ed43751688415bbaa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d36e48a4d81c647df8a76cc58fd4d2442ba10744", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dfe9a043300261afe5eadc07b867a6810c4e999a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
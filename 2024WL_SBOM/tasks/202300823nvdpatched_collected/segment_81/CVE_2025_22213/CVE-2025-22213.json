{"cve_id": "CVE-2025-22213", "published_date": "2025-03-11T17:16:24.577", "last_modified_date": "2025-03-11T17:16:24.577", "descriptions": [{"lang": "en", "value": "Inadequate checks in the Media Manager allowed users with \"edit\" privileges to change file extension to arbitrary extension, including .php and other potentially executable extensions."}, {"lang": "es", "value": "Los controles inadecuados en el administrador de medios permitieron que los usuarios con privilegios de \"edición\" cambiaran la extensión del archivo a una extensión arbitraria, incluyendo .php y otras extensiones potencialmente ejecutables."}], "references": [{"url": "https://developer.joomla.org/security-centre/961-20250301-core-malicious-file-uploads-via-media-managere-malicious-file-uploads-via-media-manager.html", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27433", "published_date": "2025-03-11T01:15:36.607", "last_modified_date": "2025-03-11T01:15:36.607", "descriptions": [{"lang": "en", "value": "The Manage Bank Statements in SAP S/4HANA allows authenticated attacker to bypass certain functionality restrictions of the application and upload files to a reversed bank statement. This vulnerability has a low impact on the application's integrity, with no effect on confidentiality and availability of the application."}, {"lang": "es", "value": "La función de gestión de extractos bancarios en SAP S/4HANA permite a un atacante autenticado eludir ciertas restricciones de funcionalidad de la aplicación y cargar archivos en un extracto bancario invertido. Esta vulnerabilidad tiene un impacto bajo en la integridad de la aplicación y no afecta a la confidencialidad ni a la disponibilidad de la misma."}], "references": [{"url": "https://me.sap.com/notes/3565835", "source": "<EMAIL>", "tags": []}, {"url": "https://url.sap/sapsecuritypatchday", "source": "<EMAIL>", "tags": []}]}
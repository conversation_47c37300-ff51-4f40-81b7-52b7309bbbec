{"cve_id": "CVE-2024-13864", "published_date": "2025-03-11T06:15:26.407", "last_modified_date": "2025-05-06T15:05:46.537", "descriptions": [{"lang": "en", "value": "The Countdown Timer WordPress plugin through 1.0 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin"}, {"lang": "es", "value": "El complemento Countdown Timer de WordPress hasta la versión 1.0 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios altos, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/b95b32b6-218a-4d02-b294-ab13458006b2/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
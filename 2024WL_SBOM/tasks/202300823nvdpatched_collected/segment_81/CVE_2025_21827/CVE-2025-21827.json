{"cve_id": "CVE-2025-21827", "published_date": "2025-03-06T16:15:54.967", "last_modified_date": "2025-03-06T16:15:54.967", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nBluetooth: btusb: mediatek: Add locks for usb_driver_claim_interface()\n\nThe documentation for usb_driver_claim_interface() says that \"the\ndevice lock\" is needed when the function is called from places other\nthan probe(). This appears to be the lock for the USB interface\ndevice. The Mediatek btusb code gets called via this path:\n\n  Workqueue: hci0 hci_power_on [bluetooth]\n  Call trace:\n   usb_driver_claim_interface\n   btusb_mtk_claim_iso_intf\n   btusb_mtk_setup\n   hci_dev_open_sync\n   hci_power_on\n   process_scheduled_works\n   worker_thread\n   kthread\n\nWith the above call trace the device lock hasn't been claimed. Claim\nit.\n\nWithout this fix, we'd sometimes see the error \"Failed to claim iso\ninterface\". Sometimes we'd even see worse errors, like a NULL pointer\ndereference (where `intf->dev.driver` was NULL) with a trace like:\n\n  Call trace:\n   usb_suspend_both\n   usb_runtime_suspend\n   __rpm_callback\n   rpm_suspend\n   pm_runtime_work\n   process_scheduled_works\n\nBoth errors appear to be fixed with the proper locking."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: Bluetooth: btusb: mediatek: Add locks for usb_driver_claim_interface() The documentation for usb_driver_claim_interface() says that \"the device lock\" is needed when the function is called from places other than probe(). This appears to be the lock for the USB interface device. The Mediatek btusb code gets called via this path: Workqueue: hci0 hci_power_on [bluetooth] Call trace: usb_driver_claim_interface btusb_mtk_claim_iso_intf btusb_mtk_setup hci_dev_open_sync hci_power_on process_scheduled_works worker_thread kthread With the above call trace the device lock hasn't been claimed. Claim it. Without this fix, we'd sometimes see the error \"Failed to claim iso interface\". Sometimes we'd even see worse errors, like a NULL pointer dereference (where `intf-&gt;dev.driver` was NULL) with a trace like: Call trace: usb_suspend_both usb_runtime_suspend __rpm_callback rpm_suspend pm_runtime_work process_scheduled_works Ambos errores parecen solucionarse con el bloqueo adecuado."}], "references": [{"url": "https://git.kernel.org/stable/c/4194766ec8756f4f654d595ae49962acbac49490", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/930e1790b99e5839e1af69d2f7fd808f1fba2df9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e9087e828827e5a5c85e124ce77503f2b81c3491", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
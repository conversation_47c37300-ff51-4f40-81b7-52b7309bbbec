{"cve_id": "CVE-2025-2194", "published_date": "2025-03-11T14:15:27.753", "last_modified_date": "2025-04-09T20:46:03.463", "descriptions": [{"lang": "en", "value": "A vulnerability was found in MRCMS 3.1.2 and classified as problematic. This issue affects the function list of the file /admin/file/list.do of the component org.marker.mushroom.controller.FileController. The manipulation of the argument path leads to cross site scripting. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en MRCMS 3.1.2 y se clasificó como problemática. Este problema afecta la lista de funciones del archivo /admin/file/list.do del componente org.marker.mushroom.controller.FileController. La manipulación de la ruta de argumentos provoca ataques de cross site scripting. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299219", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299219", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511732", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
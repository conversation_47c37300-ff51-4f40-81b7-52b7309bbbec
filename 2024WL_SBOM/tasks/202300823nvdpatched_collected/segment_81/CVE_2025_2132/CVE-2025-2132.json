{"cve_id": "CVE-2025-2132", "published_date": "2025-03-09T23:15:34.580", "last_modified_date": "2025-03-11T20:25:40.670", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in ftcms 2.1. Affected is an unknown function of the file /admin/index.php/web/ajax_all_lists of the component Search. The manipulation of the argument name leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en ftcms 2.1. Se ve afectada una función desconocida del archivo /admin/index.php/web/ajax_all_lists del componente Search. La manipulación del nombre del argumento provoca una inyección SQL. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con anticipación sobre esta revelación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/ahieafe/zpp/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299052", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299052", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511614", "source": "<EMAIL>", "tags": ["VDB Entry"]}]}
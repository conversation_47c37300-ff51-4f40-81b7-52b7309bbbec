{"cve_id": "CVE-2025-28908", "published_date": "2025-03-11T21:15:48.230", "last_modified_date": "2025-03-11T21:15:48.230", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in pipdig pipDisqus allows Stored XSS. This issue affects pipDisqus: from n/a through 1.6."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en pipdig pipDisqus que permite XSS almacenado. Este problema afecta a pipDisqus desde n/d hasta la versión 1.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/pipdisqus/vulnerability/wordpress-pipdisqus-plugin-1-6-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27840", "published_date": "2025-03-08T20:15:36.027", "last_modified_date": "2025-03-12T14:58:54.130", "descriptions": [{"lang": "en", "value": "Espressif ESP32 chips allow 29 hidden HCI commands, such as 0xFC02 (Write memory)."}, {"lang": "es", "value": "Los chips Espressif ESP32 permiten 29 comandos HCI ocultos, como 0xFC02 (Escribir memoria)."}], "references": [{"url": "https://cheriot.org/auditing/backdoor/2025/03/09/no-esp32-style-backdoor.html", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Technical Description"]}, {"url": "https://darkmentor.com/blog/esp32_non-backdoor/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Technical Description"]}, {"url": "https://flyingpenguin.com/?p=67838", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Technical Description"]}, {"url": "https://github.com/TarlogicSecurity/Talks/blob/main/2025_RootedCon_BluetoothTools.pdf", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/em0gi/CVE-2025-27840", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/esphome/esphome/discussions/8382", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/orgs/espruino/discussions/7699", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://news.ycombinator.com/item?id=43301369", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://news.ycombinator.com/item?id=43308740", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://reg.rootedcon.com/cfp/schedule/talk/5", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.bleepingcomputer.com/news/security/undocumented-backdoor-found-in-bluetooth-chip-used-by-a-billion-devices/", "source": "<EMAIL>", "tags": ["Exploit", "Press/Media Coverage"]}, {"url": "https://www.bleepingcomputer.com/news/security/undocumented-commands-found-in-bluetooth-chip-used-by-a-billion-devices/", "source": "<EMAIL>", "tags": ["Exploit", "Press/Media Coverage"]}, {"url": "https://www.espressif.com/en/news/Response_ESP32_Bluetooth", "source": "<EMAIL>", "tags": ["Press/Media Coverage"]}, {"url": "https://www.tarlogic.com/news/backdoor-esp32-chip-infect-ot-devices/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://x.com/pascal_gujer/status/1898442439704158276", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
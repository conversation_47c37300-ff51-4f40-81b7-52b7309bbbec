{"cve_id": "CVE-2025-28857", "published_date": "2025-03-11T21:15:42.903", "last_modified_date": "2025-03-19T13:41:38.270", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in rankchecker Rankchecker.io Integration allows Stored XSS. This issue affects Rankchecker.io Integration: from n/a through 1.0.9."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en rankchecker Rankchecker.io Integration permite XSS almacenado. Este problema afecta a la integración de Rankchecker.io desde n/d hasta la versión 1.0.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/rankchecker-io-integration/vulnerability/wordpress-rankchecker-io-integration-plugin-1-0-9-csrf-to-stored-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
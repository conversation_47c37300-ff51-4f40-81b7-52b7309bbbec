{"cve_id": "CVE-2024-58071", "published_date": "2025-03-06T16:15:53.593", "last_modified_date": "2025-03-25T14:22:11.633", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nteam: prevent adding a device which is already a team device lower\n\nPrevent adding a device which is already a team device lower,\ne.g. adding veth0 if vlan1 was already added and veth0 is a lower of\nvlan1.\n\nThis is not useful in practice and can lead to recursive locking:\n\n$ ip link add veth0 type veth peer name veth1\n$ ip link set veth0 up\n$ ip link set veth1 up\n$ ip link add link veth0 name veth0.1 type vlan protocol 802.1Q id 1\n$ ip link add team0 type team\n$ ip link set veth0.1 down\n$ ip link set veth0.1 master team0\nteam0: Port device veth0.1 added\n$ ip link set veth0 down\n$ ip link set veth0 master team0\n\n============================================\nWARNING: possible recursive locking detected\n6.13.0-rc2-virtme-00441-ga14a429069bb #46 Not tainted\n--------------------------------------------\nip/7684 is trying to acquire lock:\nffff888016848e00 (team->team_lock_key){+.+.}-{4:4}, at: team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\n\nbut task is already holding lock:\nffff888016848e00 (team->team_lock_key){+.+.}-{4:4}, at: team_add_slave (drivers/net/team/team_core.c:1147 drivers/net/team/team_core.c:1977)\n\nother info that might help us debug this:\nPossible unsafe locking scenario:\n\nCPU0\n----\nlock(team->team_lock_key);\nlock(team->team_lock_key);\n\n*** DEADLOCK ***\n\nMay be due to missing lock nesting notation\n\n2 locks held by ip/7684:\n\nstack backtrace:\nCPU: 3 UID: 0 PID: 7684 Comm: ip Not tainted 6.13.0-rc2-virtme-00441-ga14a429069bb #46\nHardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS 1.16.3-debian-1.16.3-2 04/01/2014\nCall Trace:\n<TASK>\ndump_stack_lvl (lib/dump_stack.c:122)\nprint_deadlock_bug.cold (kernel/locking/lockdep.c:3040)\n__lock_acquire (kernel/locking/lockdep.c:3893 kernel/locking/lockdep.c:5226)\n? netlink_broadcast_filtered (net/netlink/af_netlink.c:1548)\nlock_acquire.part.0 (kernel/locking/lockdep.c:467 kernel/locking/lockdep.c:5851)\n? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\n? trace_lock_acquire (./include/trace/events/lock.h:24 (discriminator 2))\n? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\n? lock_acquire (kernel/locking/lockdep.c:5822)\n? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\n__mutex_lock (kernel/locking/mutex.c:587 kernel/locking/mutex.c:735)\n? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\n? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\n? fib_sync_up (net/ipv4/fib_semantics.c:2167)\n? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\nteam_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973)\nnotifier_call_chain (kernel/notifier.c:85)\ncall_netdevice_notifiers_info (net/core/dev.c:1996)\n__dev_notify_flags (net/core/dev.c:8993)\n? __dev_change_flags (net/core/dev.c:8975)\ndev_change_flags (net/core/dev.c:9027)\nvlan_device_event (net/8021q/vlan.c:85 net/8021q/vlan.c:470)\n? br_device_event (net/bridge/br.c:143)\nnotifier_call_chain (kernel/notifier.c:85)\ncall_netdevice_notifiers_info (net/core/dev.c:1996)\ndev_open (net/core/dev.c:1519 net/core/dev.c:1505)\nteam_add_slave (drivers/net/team/team_core.c:1219 drivers/net/team/team_core.c:1977)\n? __pfx_team_add_slave (drivers/net/team/team_core.c:1972)\ndo_set_master (net/core/rtnetlink.c:2917)\ndo_setlink.isra.0 (net/core/rtnetlink.c:3117)"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: equipo: evitar agregar un dispositivo que ya es un dispositivo de equipo inferior Evitar agregar un dispositivo que ya es un dispositivo de equipo inferior, por ejemplo, agregar veth0 si vlan1 ya se agregó y veth0 es un inferior de vlan1. Esto no es útil en la práctica y puede provocar un bloqueo recursivo: $ ip link add veth0 type veth peer name veth1 $ ip link set veth0 up $ ip link set veth1 up $ ip link add link veth0 name veth0.1 type vlan protocol 802.1Q id 1 $ ip link add team0 type team $ ip link set veth0.1 down $ ip link set veth0.1 master team0 team0: Port device veth0.1 added $ ip link set veth0 down $ ip link set veth0 master team0 ============================================ WARNING: possible recursive locking detected 6.13.0-rc2-virtme-00441-ga14a429069bb #46 Not tainted -------------------------------------------- ip/7684 is trying to acquire lock: ffff888016848e00 (team-&gt;team_lock_key){+.+.}-{4:4}, at: team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) but task is already holding lock: ffff888016848e00 (team-&gt;team_lock_key){+.+.}-{4:4}, at: team_add_slave (drivers/net/team/team_core.c:1147 drivers/net/team/team_core.c:1977) other info that might help us debug this: Possible unsafe locking scenario: CPU0 ---- lock(team-&gt;team_lock_key); lock(team-&gt;team_lock_key); *** DEADLOCK *** May be due to missing lock nesting notation 2 locks held by ip/7684: stack backtrace: CPU: 3 UID: 0 PID: 7684 Comm: ip Not tainted 6.13.0-rc2-virtme-00441-ga14a429069bb #46 Hardware name: QEMU Standard PC (i440FX + PIIX, 1996), BIOS 1.16.3-debian-1.16.3-2 04/01/2014 Call Trace:  dump_stack_lvl (lib/dump_stack.c:122) print_deadlock_bug.cold (kernel/locking/lockdep.c:3040) __lock_acquire (kernel/locking/lockdep.c:3893 kernel/locking/lockdep.c:5226) ? netlink_broadcast_filtered (net/netlink/af_netlink.c:1548) lock_acquire.part.0 (kernel/locking/lockdep.c:467 kernel/locking/lockdep.c:5851) ? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) ? trace_lock_acquire (./include/trace/events/lock.h:24 (discriminator 2)) ? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) ? lock_acquire (kernel/locking/lockdep.c:5822) ? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) __mutex_lock (kernel/locking/mutex.c:587 kernel/locking/mutex.c:735) ? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) ? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) ? fib_sync_up (net/ipv4/fib_semantics.c:2167) ? team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) team_device_event (drivers/net/team/team_core.c:2928 drivers/net/team/team_core.c:2951 drivers/net/team/team_core.c:2973) notifier_call_chain (kernel/notifier.c:85) call_netdevice_notifiers_info (net/core/dev.c:1996) __dev_notify_flags (net/core/dev.c:8993) ? __dev_change_flags (net/core/dev.c:8975) dev_change_flags (net/core/dev.c:9027) vlan_device_event (net/8021q/vlan.c:85 net/8021q/vlan.c:470) ? br_device_event (net/bridge/br.c:143) notifier_call_chain (kernel/notifier.c:85) call_netdevice_notifiers_info (net/core/dev.c:1996) dev_open (net/core/dev.c:1519 net/core/dev.c:1505) team_add_slave (drivers/net/team/team_core.c:1219 drivers/net/team/team_core.c:1977) ? __pfx_team_add_slave (drivers/net/team/team_core.c:1972) do_set_master (net/core/rtnetlink.c:2917) do_setlink.isra.0 (net/core/rtnetlink.c:3117) "}], "references": [{"url": "https://git.kernel.org/stable/c/0a7794b9ca78c8e7d001c583bf05736169de3f20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/184a564e6000b41582f160a5be9a9b5aabe22ac1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1bb06f919fa5bec77ad9b6002525c3dcc5c1fd6c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3fff5da4ca2164bb4d0f1e6cd33f6eb8a0e73e50", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/62ff1615815d565448c37cb8a7a2a076492ec471", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/adff6ac889e16d97abd1e4543f533221127e978a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bd099a2fa9be983ba0e90a57a59484fe9d520ba8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d9bce1310c0e2a55888e3e08c9f69d8377b3a377", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
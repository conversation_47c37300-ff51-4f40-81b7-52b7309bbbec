{"cve_id": "CVE-2025-21840", "published_date": "2025-03-07T09:15:17.033", "last_modified_date": "2025-03-07T09:15:17.033", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nthermal/netlink: Prevent userspace segmentation fault by adjusting UAPI header\n\nThe intel-lpmd tool [1], which uses the THERMAL_GENL_ATTR_CPU_CAPABILITY\nattribute to receive HFI events from kernel space, encounters a\nsegmentation fault after commit 1773572863c4 (\"thermal: netlink: Add the\ncommands and the events for the thresholds\").\n\nThe issue arises because the THERMAL_GENL_ATTR_CPU_CAPABILITY raw value\nwas changed while intel_lpmd still uses the old value.\n\nAlthough intel_lpmd can be updated to check the THERMAL_GENL_VERSION and\nuse the appropriate THERMAL_GENL_ATTR_CPU_CAPABILITY value, the commit\nitself is questionable.\n\nThe commit introduced a new element in the middle of enum thermal_genl_attr,\nwhich affects many existing attributes and introduces potential risks\nand unnecessary maintenance burdens for userspace thermal netlink event\nusers.\n\nSolve the issue by moving the newly introduced\nTHERMAL_GENL_ATTR_TZ_PREV_TEMP attribute to the end of the\nenum thermal_genl_attr. This ensures that all existing thermal generic\nnetlink attributes remain unaffected.\n\n[ rjw: Subject edits ]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: thermal/netlink: Prevenir un error de segmentación del espacio de usuario ajustando el encabezado UAPI La herramienta intel-lpmd [1], que utiliza el atributo THERMAL_GENL_ATTR_CPU_CAPABILITY para recibir eventos HFI desde el espacio del kernel, encuentra un error de segmentación después de el commit 1773572863c4 (\"thermal: netlink: Agregar los comandos y los eventos para los umbrales\"). El problema surge porque se cambió el valor sin procesar de THERMAL_GENL_ATTR_CPU_CAPABILITY mientras que intel_lpmd todavía usa el valor anterior. Aunque intel_lpmd se puede actualizar para verificar THERMAL_GENL_VERSION y usar el valor THERMAL_GENL_ATTR_CPU_CAPABILITY apropiado, el commit en sí es cuestionable. El commit introdujo un nuevo elemento en el medio de la enumeración thermal_genl_attr, que afecta a muchos atributos existentes e introduce riesgos potenciales y cargas de mantenimiento innecesarias para los usuarios del evento de enlace de red térmico del espacio de usuario. Resuelva el problema moviendo el atributo THERMAL_GENL_ATTR_TZ_PREV_TEMP recientemente introducido al final de la enumeración thermal_genl_attr. Esto garantiza que todos los atributos de enlace de red genéricos térmicos existentes permanezcan inalterados. [ rjw: Ediciones del sujeto ]"}], "references": [{"url": "https://git.kernel.org/stable/c/3a4ca365c51729143a2cab693cd40fe0bb585ef0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c195b9c6ab9c383d7aa3f4a65879b3ca90cb378b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
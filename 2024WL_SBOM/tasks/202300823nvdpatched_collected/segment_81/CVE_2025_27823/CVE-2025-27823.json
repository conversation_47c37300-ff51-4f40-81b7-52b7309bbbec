{"cve_id": "CVE-2025-27823", "published_date": "2025-03-07T22:15:38.073", "last_modified_date": "2025-03-07T22:15:38.073", "descriptions": [{"lang": "en", "value": "An issue was discovered in the Mail Disguise module before 1.x-1.0.5 for Backdrop CMS. It enables a website to obfuscate email addresses, and should prevent spambots from collecting them. The module doesn't sufficiently validate the data attribute value on links, potentially leading to a Cross Site Scripting (XSS) vulnerability. This is mitigated by the fact an attacker must be able to insert link (<a>) HTML elements containing data attributes into the page."}, {"lang": "es", "value": "Se descubrió un problema en el módulo Mail Disguise anterior a la versión 1.x-1.0.5 para Background CMS. Permite que un sitio web oculte direcciones de correo electrónico y debería evitar que los robots de spam las recopilen. El módulo no valida de forma suficiente el valor del atributo de datos en los enlaces, lo que puede provocar una vulnerabilidad de Cross Site Scripting (XSS). Esto se mitiga con el hecho de que un atacante debe poder insertar elementos HTML de enlace (<a rel=\"nofollow\">) que contengan atributos de datos en la página.</a>"}], "references": [{"url": "https://backdropcms.org/security/backdrop-sa-contrib-2025-007", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1944", "published_date": "2025-03-10T12:15:10.967", "last_modified_date": "2025-03-19T16:11:29.113", "descriptions": [{"lang": "en", "value": "picklescan before 0.0.23 is vulnerable to a ZIP archive manipulation attack that causes it to crash when attempting to extract and scan PyTorch model archives. By modifying the filename in the ZIP header while keeping the original filename in the directory listing, an attacker can make PickleScan raise a BadZipFile error. However, PyTorch's more forgiving ZIP implementation still allows the model to be loaded, enabling malicious payloads to bypass detection."}, {"lang": "es", "value": "Las versiones anteriores a la versión 0.0.23 de picklescan son vulnerables a un ataque de manipulación de archivos ZIP que provoca que se bloquee al intentar extraer y escanear archivos de modelos de PyTorch. Al modificar el nombre del archivo en el encabezado ZIP y mantener el nombre del archivo original en la lista de directorios, un atacante puede hacer que PickleScan genere un error BadZipFile. Sin embargo, la implementación ZIP más indulgente de PyTorch aún permite cargar el modelo, lo que permite que las cargas maliciosas eludan la detección."}], "references": [{"url": "https://github.com/mmaitre314/picklescan/commit/e58e45e0d9e091159c1554f9b04828bbb40b9781", "source": "103e4ec9-0a87-450b-af77-479448ddef11", "tags": ["Patch"]}, {"url": "https://github.com/mmaitre314/picklescan/security/advisories/GHSA-7q5r-7gvp-wc82", "source": "103e4ec9-0a87-450b-af77-479448ddef11", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://sites.google.com/sonatype.com/vulnerabilities/cve-2025-1944", "source": "103e4ec9-0a87-450b-af77-479448ddef11", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-1707", "published_date": "2025-03-11T22:15:12.583", "last_modified_date": "2025-03-11T22:15:12.583", "descriptions": [{"lang": "en", "value": "The Review Schema plugin for WordPress is vulnerable to Local File Inclusion in all versions up to, and including, 2.2.4 via post meta. This makes it possible for authenticated attackers, with contributor-level and above permissions, to include and execute arbitrary files on the server, allowing the execution of any PHP code in those files. This can be used to bypass access controls, obtain sensitive data, or achieve code execution in cases where php file type can be uploaded and included."}, {"lang": "es", "value": "El complemento Review Schema para WordPress es vulnerable a la inclusión local de archivos en todas las versiones hasta la 2.2.4 incluida, mediante metadatos de entrada. Esto permite a atacantes autenticados, con permisos de colaborador o superiores, incluir y ejecutar archivos arbitrarios en el servidor, permitiendo la ejecución de cualquier código PHP en dichos archivos. Esto puede utilizarse para eludir los controles de acceso, obtener datos confidenciales o ejecutar código cuando se permite subir e incluir archivos PHP."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/review-schema/tags/2.2.4/app/Shortcodes/ReviewSchema.php#L108", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3253799/review-schema", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9b4de243-d337-4f29-a766-bcafb3848d1c?source=cve", "source": "<EMAIL>", "tags": []}]}
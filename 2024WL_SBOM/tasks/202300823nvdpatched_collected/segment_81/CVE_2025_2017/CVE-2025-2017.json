{"cve_id": "CVE-2025-2017", "published_date": "2025-03-11T21:15:53.050", "last_modified_date": "2025-07-07T18:06:51.850", "descriptions": [{"lang": "en", "value": "Ashlar-Vellum Cobalt CO File Parsing Buffer Overflow Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Ashlar-Vellum Cobalt. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of CO files. The issue results from the lack of proper validation of the length of user-supplied data prior to copying it to a buffer. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-25240."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por desbordamiento del búfer en el análisis de archivos CO de Ashlar-Vellum Cobalt. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Ashlar-Vellum Cobalt. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos CO. El problema se debe a la falta de una validación adecuada de la longitud de los datos proporcionados por el usuario antes de copiarlos a un búfer. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Era ZDI-CAN-25240."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-121/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2024-56185", "published_date": "2025-03-10T19:15:39.097", "last_modified_date": "2025-06-27T16:21:27.090", "descriptions": [{"lang": "en", "value": "In ProtocolUnsolOnSSAdapter::GetServiceClass() of protocolcalladapter.cpp, there is a possible out-of-bounds read due to a missing bounds check. This could lead to local information disclosure with baseband firmware compromise required. User Interaction is not needed for exploitation."}, {"lang": "es", "value": "En ProtocolUnsolOnSSAdapter::GetServiceClass() de protocolcalladapter.cpp, existe una posible lectura fuera de los límites debido a una verificación de los límites faltante. Esto podría provocar la divulgación de información local y comprometer el firmware de banda base. No se necesita interacción del usuario para la explotación."}], "references": [{"url": "https://source.android.com/security/bulletin/pixel/2025-03-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
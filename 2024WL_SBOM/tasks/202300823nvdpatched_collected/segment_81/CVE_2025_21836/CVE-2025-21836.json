{"cve_id": "CVE-2025-21836", "published_date": "2025-03-07T09:15:16.600", "last_modified_date": "2025-03-07T09:15:16.600", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nio_uring/kbuf: reallocate buf lists on upgrade\n\nIORING_REGISTER_PBUF_R<PERSON> can reuse an old struct io_buffer_list if it\nwas created for legacy selected buffer and has been emptied. It violates\nthe requirement that most of the field should stay stable after publish.\nAlways reallocate it instead."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: io_uring/kbuf: reasignar listas de búferes en la actualización IORING_REGISTER_PBUF_RING puede reutilizar una estructura io_buffer_list antigua si se creó para un búfer seleccionado heredado y se vació. Viola el requisito de que la mayor parte del campo debe permanecer estable después de la publicación. Siempre reasignarlo en su lugar."}], "references": [{"url": "https://git.kernel.org/stable/c/146a185f6c05ee263db715f860620606303c4633", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2a5febbef40ce968e295a7aeaa5d5cbd9e3e5ad4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d0dc28dae836caf7645fef62a10befc624dd17b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8802766324e1f5d414a81ac43365c20142e85603", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-27497", "published_date": "2025-03-05T16:15:40.587", "last_modified_date": "2025-03-05T16:15:40.587", "descriptions": [{"lang": "en", "value": "OpenDJ is an LDAPv3 compliant directory service. OpenDJ prior to 4.9.3 contains a denial-of-service (DoS) vulnerability that causes the server to become unresponsive to all LDAP requests without crashing or restarting. This issue occurs when an alias loop exists in the LDAP database. If an ldapsearch request is executed with alias dereferencing set to \"always\" on this alias entry, the server stops responding to all future requests. Fortunately, the server can be restarted without data corruption. This vulnerability is fixed in 4.9.3."}, {"lang": "es", "value": "OpenDJ es un servicio de directorio compatible con LDAPv3. Las versiones anteriores a la 4.9.3 de OpenDJ contienen una vulnerabilidad de denegación de servicio (DoS) que hace que el servidor deje de responder a todas las solicitudes LDAP sin bloquearse ni reiniciarse. Este problema se produce cuando existe un bucle de alias en la base de datos LDAP. Si se ejecuta una solicitud ldapsearch con la desreferenciación de alias configurada en \"siempre\" en esta entrada de alias, el servidor deja de responder a todas las solicitudes futuras. Afortunadamente, el servidor se puede reiniciar sin que se produzcan daños en los datos. Esta vulnerabilidad se solucionó en la versión 4.9.3."}], "references": [{"url": "https://github.com/OpenIdentityPlatform/OpenDJ/commit/08aee4724608e4a32baa3c7d7499ec913a275aaf", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/OpenIdentityPlatform/OpenDJ/security/advisories/GHSA-93qr-h8pr-4593", "source": "<EMAIL>", "tags": []}]}
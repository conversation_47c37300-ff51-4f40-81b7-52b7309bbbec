{"cve_id": "CVE-2024-13868", "published_date": "2025-03-06T06:15:34.720", "last_modified_date": "2025-05-21T15:28:50.360", "descriptions": [{"lang": "en", "value": "The URL Shortener | Conversion Tracking  | AB Testing  | WooCommerce WordPress plugin through 9.0.2 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin."}, {"lang": "es", "value": "El complemento URL Shortener | Conversion Tracking | AB Testing | WooCommerce WordPress hasta la versión 9.0.2 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con altos privilegios, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/0bff1645-dd53-4416-a90f-7cf4a6b33c1a/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
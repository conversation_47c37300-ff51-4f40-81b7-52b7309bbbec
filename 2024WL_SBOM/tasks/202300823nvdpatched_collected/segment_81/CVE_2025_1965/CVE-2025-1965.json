{"cve_id": "CVE-2025-1965", "published_date": "2025-03-05T01:15:11.600", "last_modified_date": "2025-04-02T18:32:15.230", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in projectworlds Online Hotel Booking 1.0. Affected is an unknown function of the file /admin/login.php. The manipulation of the argument emailusername leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en ProjectWorlds Online Hotel Booking 1.0. Se trata de una función desconocida del archivo /admin/login.php. La manipulación del argumento emailusername provoca una inyección SQL. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede ser utilizado."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298566", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298566", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511473", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
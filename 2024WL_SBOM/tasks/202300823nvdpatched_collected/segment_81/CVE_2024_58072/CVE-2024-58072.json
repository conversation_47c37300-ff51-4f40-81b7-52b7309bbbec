{"cve_id": "CVE-2024-58072", "published_date": "2025-03-06T16:15:53.703", "last_modified_date": "2025-03-13T13:15:45.920", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: rtlwifi: remove unused check_buddy_priv\n\nCommit 2461c7d60f9f (\"rtlwifi: Update header file\") introduced a global\nlist of private data structures.\n\nLater on, commit 26634c4b1868 (\"rtlwifi Modify existing bits to match\nvendor version 2013.02.07\") started adding the private data to that list at\nprobe time and added a hook, check_buddy_priv to find the private data from\na similar device.\n\nHowever, that function was never used.\n\nBesides, though there is a lock for that list, it is never used. And when\nthe probe fails, the private data is never removed from the list. This\nwould cause a second probe to access freed memory.\n\nRemove the unused hook, structures and members, which will prevent the\npotential race condition on the list and its corruption during a second\nprobe when probe fails."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: rtlwifi: eliminar check_buddy_priv no utilizado el commit 2461c7d60f9f (\"rtlwifi: Actualizar archivo de encabezado\") introdujo una lista global de estructuras de datos privados. M<PERSON> tarde, el commit 26634c4b1868 (\"rtlwifi Modificar bits existentes para que coincidan con la versión del proveedor 2013.02.07\") comenzó a agregar los datos privados a esa lista en el momento del sondeo y agregó un gancho, check_buddy_priv para encontrar los datos privados de un dispositivo similar. Sin embargo, esa función nunca se usó. Además, aunque hay un bloqueo para esa lista, nunca se usa. Y cuando el sondeo falla, los datos privados nunca se eliminan de la lista. Esto haría que un segundo sondeo acceda a la memoria liberada. Elimine el gancho, las estructuras y los miembros no utilizados, lo que evitará la posible condición de ejecución en la lista y su corrupción durante un segundo sondeo cuando el sondeo falla."}], "references": [{"url": "https://git.kernel.org/stable/c/006e803af7408c3fc815b0654fc5ab43d34f0154", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1b9cbd8a9ae68b32099fbb03b2d5ffa0c5e0dcc9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1e39b0486cdb496cdfba3bc89886150e46acf6f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2fdac64c3c35858aa8ac5caa70b232e03456e120", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/465d01ef6962b82b1f0ad1f3e58b398dbd35c1c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/543e3e9f2e9e47ded774c74e680f28a0ca362aee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8e2fcc68fbaab3ad9f5671fee2be0956134b740a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f801e754efa21bd61b3cc15ec7565696165b272f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
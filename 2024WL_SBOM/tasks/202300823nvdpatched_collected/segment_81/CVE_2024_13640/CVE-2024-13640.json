{"cve_id": "CVE-2024-13640", "published_date": "2025-03-08T05:15:31.673", "last_modified_date": "2025-03-08T05:15:31.673", "descriptions": [{"lang": "en", "value": "The Print Invoice & Delivery Notes for WooCommerce plugin for WordPress is vulnerable to Sensitive Information Exposure in all versions up to, and including, 5.4.1 via the 'wcdn/invoice' directory. This makes it possible for unauthenticated attackers to extract sensitive data stored insecurely in the /wp-content/uploads/wcdn/invoice directory which can contain invoice files if an email attachment setting is enabled."}, {"lang": "es", "value": "El complemento Print Invoice &amp; Delivery Notes for WooCommerce para WordPress es vulnerable a la exposición de información confidencial en todas las versiones hasta la 5.4.1 incluida a través del directorio 'wcdn/invoice'. Esto permite que atacantes no autenticados extraigan datos confidenciales almacenados de forma insegura en el directorio /wp-content/uploads/wcdn/invoice, que puede contener archivos de facturas si se habilita la configuración de archivos adjuntos en correos electrónicos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/woocommerce-delivery-notes/trunk/includes/class-wcdn-theme.php#L56", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3250195/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/12ab3e54-a0b9-4420-ac90-f16e23688cca?source=cve", "source": "<EMAIL>", "tags": []}]}
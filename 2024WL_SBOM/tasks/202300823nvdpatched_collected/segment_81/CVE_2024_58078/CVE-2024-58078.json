{"cve_id": "CVE-2024-58078", "published_date": "2025-03-06T17:15:21.250", "last_modified_date": "2025-03-06T17:15:21.250", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmisc: misc_minor_alloc to use ida for all dynamic/misc dynamic minors\n\nmisc_minor_alloc was allocating id using ida for minor only in case of\nMISC_DYNAMIC_MINOR but misc_minor_free was always freeing ids\nusing ida_free causing a mismatch and following warn:\n> > WARNING: CPU: 0 PID: 159 at lib/idr.c:525 ida_free+0x3e0/0x41f\n> > ida_free called for id=127 which is not allocated.\n> > <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n...\n> > [<60941eb4>] ida_free+0x3e0/0x41f\n> > [<605ac993>] misc_minor_free+0x3e/0xbc\n> > [<605acb82>] misc_deregister+0x171/0x1b3\n\nmisc_minor_alloc is changed to allocate id from ida for all minors\nfalling in the range of dynamic/ misc dynamic minors"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: misc: misc_minor_alloc para usar ida para todos los menores dinámicos/misc dinámicos misc_minor_alloc asignaba id usando ida para menores solo en el caso de MISC_DYNAMIC_MINOR pero misc_minor_free siempre liberaba id usando ida_free, lo que causaba una falta de coincidencia y la siguiente advertencia: &gt; &gt; WARNING: CPU: 0 PID: 159 at lib/idr.c:525 ida_free+0x3e0/0x41f &gt; &gt; ida_free called for id=127 which is not allocated. &gt; &gt; &lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt;&lt; ... &gt; &gt; [&lt;60941eb4&gt;] ida_free+0x3e0/0x41f &gt; &gt; [&lt;605ac993&gt;] misc_minor_free+0x3e/0xbc &gt; &gt; [&lt;605acb82&gt;] misc_deregister+0x171/0x1b3 misc_minor_alloc Se cambió misc_minor_alloc para asignar id de ida para todos los menores que caen en el rango de menores dinámicos/misc dinámicos"}], "references": [{"url": "https://git.kernel.org/stable/c/3df72111c39f7e4c5029c9ff720b56ec2e05b764", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6635332d246d7db89b90e145f2bf937406cecaf0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6d04d2b554b14ae6c428a9c60b6c85f1e5c89f68", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8b4120b3e060e137eaa8dc76a1c40401088336e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-21835", "published_date": "2025-03-07T09:15:16.473", "last_modified_date": "2025-03-13T13:15:56.920", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: gadget: f_midi: fix MIDI Streaming descriptor lengths\n\nWhile the MIDI jacks are configured correctly, and the MIDIStreaming\nendpoint descriptors are filled with the correct information,\nbNumEmbMIDIJack and b<PERSON>ength are set incorrectly in these descriptors.\n\nThis does not matter when the numbers of in and out ports are equal, but\nwhen they differ the host will receive broken descriptors with\nuninitialized stack memory leaking into the descriptor for whichever\nvalue is smaller.\n\nThe precise meaning of \"in\" and \"out\" in the port counts is not clearly\ndefined and can be confusing.  But elsewhere the driver consistently\nuses this to match the USB meaning of IN and OUT viewed from the host,\nso that \"in\" ports send data to the host and \"out\" ports receive data\nfrom it."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: gadget: f_midi: fix MIDI Streaming descriptor lengths Mientras que los conectores MIDI están configurados correctamente, y los descriptores de endpoint MIDIStreaming están llenos con la información correcta, bNumEmbMIDIJack y bLength están configurados incorrectamente en estos descriptores. Esto no importa cuando los números de puertos de entrada y salida son iguales, pero cuando difieren, el host recibirá descriptores rotos con memoria de pila no inicializada que se filtra en el descriptor para el valor que sea menor. El significado preciso de \"entrada\" y \"salida\" en los recuentos de puertos no está claramente definido y puede ser confuso. Pero en otros lugares, el controlador usa esto constantemente para que coincida con el significado USB de IN y OUT visto desde el host, de modo que los puertos \"de entrada\" envían datos al host y los puertos \"de salida\" reciben datos de él."}], "references": [{"url": "https://git.kernel.org/stable/c/3a983390d14e8498f303fc5cb23ab7d696b815db", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6ae6dee9f005a2f3b739b85abb6f14a0935699e0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6b16761a928796e4b49e89a0b1ac284155172726", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9f36a89dcb78cb7e37f487b04a16396ac18c0636", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9f6860a9c11301b052225ca8825f8d2b1a5825bf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a2d0694e1f111379c1efdf439dadd3cfd959fe9d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d8e86700c8a8cf415e300a0921acd6a8f9b494f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/da1668997052ed1cb00322e1f3b63702615c9429", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2024-13580", "published_date": "2025-03-11T06:15:25.813", "last_modified_date": "2025-05-06T16:11:42.753", "descriptions": [{"lang": "en", "value": "The XV Random Quotes WordPress plugin through 1.40 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin reset them via a CSRF attack"}, {"lang": "es", "value": "El complemento XV Random Quotes WordPress hasta la versión 1.40 no tiene la verificación CSRF activada al actualizar su configuración, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión los restablezca a través de un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/48cffe03-adcf-4da2-a331-464ae511a805/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
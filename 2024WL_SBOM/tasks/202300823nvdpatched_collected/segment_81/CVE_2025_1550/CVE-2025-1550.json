{"cve_id": "CVE-2025-1550", "published_date": "2025-03-11T09:15:25.217", "last_modified_date": "2025-07-22T16:15:26.617", "descriptions": [{"lang": "en", "value": "The Keras Model.load_model function permits arbitrary code execution, even with safe_mode=True, through a manually constructed, malicious .keras archive. By altering the config.json file within the archive, an attacker can specify arbitrary Python modules and functions, along with their arguments, to be loaded and executed during model loading."}, {"lang": "es", "value": "La función Keras Model.load_model permite la ejecución de código arbitrario, incluso con safe_mode=True, a través de un archivo .keras malicioso creado manualmente. Al alterar el archivo config.json dentro del archivo, un atacante puede especificar módulos y funciones Python arbitrarios, junto con sus argumentos, para que se carguen y ejecuten durante la carga del modelo."}], "references": [{"url": "https://github.com/keras-team/keras/pull/20751", "source": "<EMAIL>", "tags": []}, {"url": "https://towerofhanoi.it/writeups/cve-2025-1550/", "source": "<EMAIL>", "tags": []}]}
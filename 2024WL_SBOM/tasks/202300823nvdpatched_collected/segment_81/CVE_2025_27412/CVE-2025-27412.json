{"cve_id": "CVE-2025-27412", "published_date": "2025-03-05T16:15:40.457", "last_modified_date": "2025-07-01T20:38:33.160", "descriptions": [{"lang": "en", "value": "REDAXO is a PHP-based CMS. In Redaxo from 5.0.0 through 5.18.2, the rex-api-result parameter is vulnerable to Reflected cross-site scripting (XSS) on the page of AddOns. This vulnerability is fixed in 5.18.3."}, {"lang": "es", "value": "REDAXO es un CMS basado en PHP. En Redaxo desde la versión 5.0.0 hasta la 5.18.2, el parámetro rex-api-result es vulnerable a ataques de Cross-Site Scripting (XSS) reflejado en la página de complementos. Esta vulnerabilidad se solucionó en la versión 5.18.3."}], "references": [{"url": "https://github.com/redaxo/redaxo/security/advisories/GHSA-8366-xmgf-334f", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
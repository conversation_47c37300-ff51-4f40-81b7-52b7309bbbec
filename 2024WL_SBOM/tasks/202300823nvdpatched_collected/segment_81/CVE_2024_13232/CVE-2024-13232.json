{"cve_id": "CVE-2024-13232", "published_date": "2025-03-05T10:15:14.197", "last_modified_date": "2025-03-05T10:15:14.197", "descriptions": [{"lang": "en", "value": "The WordPress Awesome Import & Export Plugin - Import & Export WordPress Data plugin for WordPress is vulnerable arbitrary SQL Execution and privilege escalation due to a missing capability check on the renderImport() function in all versions up to, and including, 4.1.1. This makes it possible for authenticated attackers, with Subscriber-level access and above, to execute arbitrary SQL statements that can leveraged to create a new administrative user account."}, {"lang": "es", "value": "El complemento WordPress Awesome Import &amp; Export Plugin - Import &amp; Export WordPress Data para WordPress es vulnerable a la ejecución de SQL arbitrario y a la escalada de privilegios debido a una verificación de capacidad faltante en la función renderImport() en todas las versiones hasta la 4.1.1 incluida. Esto permite que atacantes autenticados, con acceso de nivel de suscriptor y superior, ejecuten sentencias SQL arbitrarias que pueden aprovecharse para crear una nueva cuenta de usuario administrativo."}], "references": [{"url": "https://codecanyon.net/item/wordpress-awesome-import-export-plugin-v-24/********", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f24f0673-b5c8-4086-8795-692228a413af?source=cve", "source": "<EMAIL>", "tags": []}]}
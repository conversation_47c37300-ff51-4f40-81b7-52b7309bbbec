{"cve_id": "CVE-2025-0900", "published_date": "2025-03-11T21:15:41.887", "last_modified_date": "2025-07-07T18:23:40.633", "descriptions": [{"lang": "en", "value": "PDF-XChange Editor PDF File Parsing Out-Of-Bounds Read Information Disclosure Vulnerability. This vulnerability allows remote attackers to disclose sensitive information on affected installations of PDF-XChange Editor. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of PDF files. The issue results from the lack of proper validation of user-supplied data, which can result in a read past the end of an allocated object. An attacker can leverage this in conjunction with other vulnerabilities to execute arbitrary code in the context of the current process. Was ZDI-CAN-25368."}, {"lang": "es", "value": "Vulnerabilidad de divulgación de información de lectura fuera de los límites en el análisis de archivos PDF del editor PDF-XChange. Esta vulnerabilidad permite a atacantes remotos divulgar información confidencial en las instalaciones afectadas del editor PDF-XChange. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos PDF. El problema se debe a la falta de una validación adecuada de los datos proporcionados por el usuario, lo que puede provocar una lectura más allá del final de un objeto asignado. Un atacante puede aprovechar esto, junto con otras vulnerabilidades, para ejecutar código arbitrario en el contexto del proceso actual. Anteriormente, se denominaba ZDI-CAN-25368."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-086/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
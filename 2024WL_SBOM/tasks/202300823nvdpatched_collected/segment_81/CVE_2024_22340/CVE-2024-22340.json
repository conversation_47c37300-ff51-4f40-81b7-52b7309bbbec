{"cve_id": "CVE-2024-22340", "published_date": "2025-03-11T01:15:33.257", "last_modified_date": "2025-07-25T18:12:00.233", "descriptions": [{"lang": "en", "value": "IBM Common Cryptographic Architecture 7.0.0 through 7.5.51 \n\n\n\ncould allow a remote attacker to obtain sensitive information during the creation of ECDSA signatures to perform a timing-based attack."}, {"lang": "es", "value": "IBM Common Cryptographic Architecture 7.0.0 a 7.5.51 podría permitir que un atacante remoto obtenga información confidencial durante la creación de firmas ECDSA para realizar un ataque basado en tiempo."}], "references": [{"url": "https://www.ibm.com/support/pages/node/7185282", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
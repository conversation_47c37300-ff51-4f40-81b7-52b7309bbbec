{"cve_id": "CVE-2024-58060", "published_date": "2025-03-06T16:15:52.230", "last_modified_date": "2025-03-21T15:41:13.050", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: Reject struct_ops registration that uses module ptr and the module btf_id is missing\n\nThere is a UAF report in the bpf_struct_ops when CONFIG_MODULES=n.\nIn particular, the report is on tcp_congestion_ops that has\na \"struct module *owner\" member.\n\nFor struct_ops that has a \"struct module *owner\" member,\nit can be extended either by the regular kernel module or\nby the bpf_struct_ops. bpf_try_module_get() will be used\nto do the refcounting and different refcount is done\nbased on the owner pointer. When CONFIG_MODULES=n,\nthe btf_id of the \"struct module\" is missing:\n\nWARN: resolve_btfids: unresolved symbol module\n\nThus, the bpf_try_module_get() cannot do the correct refcounting.\n\nNot all subsystem's struct_ops requires the \"struct module *owner\" member.\ne.g. the recent sched_ext_ops.\n\nThis patch is to disable bpf_struct_ops registration if\nthe struct_ops has the \"struct module *\" member and the\n\"struct module\" btf_id is missing. The btf_type_is_fwd() helper\nis moved to the btf.h header file for this test.\n\nThis has happened since the beginning of bpf_struct_ops which has gone\nthrough many changes. The Fixes tag is set to a recent commit that this\npatch can apply cleanly. Considering CONFIG_MODULES=n is not\ncommon and the age of the issue, targeting for bpf-next also."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: Rechazar el registro de struct_ops que usa el módulo ptr y falta el módulo btf_id Hay un informe UAF en bpf_struct_ops cuando CONFIG_MODULES=n. En particular, el informe está en tcp_congestion_ops que tiene un miembro \"struct module *owner\". Para struct_ops que tiene un miembro \"struct module *owner\", puede extenderse mediante el módulo de kernel regular o mediante bpf_struct_ops. bpf_try_module_get() se usará para hacer el recuento de referencias y se realiza un recuento de referencias diferente según el puntero del propietario. Cuando CONFIG_MODULES=n, falta el btf_id del \"struct module\": WARN: resolve_btfids: módulo de símbolo sin resolver Por lo tanto, bpf_try_module_get() no puede hacer el recuento de referencias correcto. No todos los struct_ops de los subsistemas requieren el miembro \"struct module *owner\", por ejemplo, el reciente sched_ext_ops. Este parche sirve para deshabilitar el registro de bpf_struct_ops si struct_ops tiene el miembro \"struct module *\" y falta el btf_id \"struct module\". El asistente btf_type_is_fwd() se ha movido al archivo de encabezado btf.h para esta prueba. Esto ha sucedido desde el comienzo de bpf_struct_ops, que ha sufrido muchos cambios. La etiqueta Fixes se establece en una confirmación reciente que este parche puede aplicar sin problemas. Teniendo en cuenta que CONFIG_MODULES=n no es común y la antigüedad del problema, también se apunta a bpf-next."}], "references": [{"url": "https://git.kernel.org/stable/c/2324fb4e92092837ee278fdd8d60c48ee1a619ce", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/96ea081ed52bf077cad6d00153b6fba68e510767", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b777b14c2a4a4e2322daf8e8ffd42d2b88831b17", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
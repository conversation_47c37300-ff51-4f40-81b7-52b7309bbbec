{"cve_id": "CVE-2024-28607", "published_date": "2025-03-11T09:15:23.600", "last_modified_date": "2025-03-11T09:15:23.600", "descriptions": [{"lang": "en", "value": "The ip-utils package through 2.4.0 for Node.js might allow SSRF because some IP addresses (such as 0x7f.1) are improperly categorized as globally routable via a falsy isPrivate return value."}, {"lang": "es", "value": "El paquete ip-utils hasta la versión 2.4.0 para Node.js podría permitir SSRF porque algunas direcciones IP (como 0x7f.1) están categorizadas incorrectamente como enrutables globalmente a través de un valor de retorno isPrivate falso."}], "references": [{"url": "https://gist.github.com/aydinnyunus/4d71e7d9a433f3afc658724b903f4d23", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/librasean/IP-Utils/blob/4f88799f94f21efe6ea9135129ab2bbeb0c58edc/src/IsPrivate.ts#L4", "source": "<EMAIL>", "tags": []}]}
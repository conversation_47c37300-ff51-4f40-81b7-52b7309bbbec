{"cve_id": "CVE-2025-2208", "published_date": "2025-03-11T21:15:54.117", "last_modified_date": "2025-05-21T17:55:35.193", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in aitangbao springboot-manager 3.0. This issue affects some unknown processing of the file /sysFiles/upload of the component Filename Handler. The manipulation of the argument name leads to cross site scripting. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como problemática en aitangbao springboot-manager 3.0. Este problema afecta a un procesamiento desconocido del archivo /sysFiles/upload del componente Filename Handler. La manipulación del argumento name provoca ataques de cross site scripting. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/uglory-gll/javasec/blob/main/spring-manage.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.299279", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299279", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511738", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-28932", "published_date": "2025-03-11T21:15:51.150", "last_modified_date": "2025-03-11T21:15:51.150", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in BCS Website Solutions Insert Code allows Stored XSS. This issue affects Insert Code: from n/a through 2.4."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en BCS Website Solutions Insert Code permite XSS almacenado. Este problema afecta al código de inserción desde n/d hasta la versión 2.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/insert-code/vulnerability/wordpress-insert-code-plugin-2-4-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
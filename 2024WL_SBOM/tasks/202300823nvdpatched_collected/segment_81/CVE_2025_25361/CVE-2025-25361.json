{"cve_id": "CVE-2025-25361", "published_date": "2025-03-06T19:15:27.410", "last_modified_date": "2025-07-01T21:23:28.040", "descriptions": [{"lang": "en", "value": "An arbitrary file upload vulnerability in the component /cms/CmsWebFileAdminController.java of PublicCMS v4.0.202406 allows attackers to execute arbitrary code via uploading a crafted svg or xml file."}, {"lang": "es", "value": "Una vulnerabilidad de carga de archivos arbitrarios en el componente /cms/CmsWebFileAdminController.java de PublicCMS v4.0.202406 permite a los atacantes ejecutar código arbitrario mediante la carga de un archivo svg o xml manipulado."}], "references": [{"url": "https://github.com/c0rdXy/POC/blob/master/CVE/PublicCMS/XSS_02/XSS_02.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
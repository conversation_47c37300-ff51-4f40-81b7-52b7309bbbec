{"cve_id": "CVE-2024-51319", "published_date": "2025-03-11T15:15:42.313", "last_modified_date": "2025-05-28T18:18:25.813", "descriptions": [{"lang": "en", "value": "A local file include vulnerability in the /servlet/Report of Zucchetti Ad Hoc Infinity 2.4 allows an authenticated attacker to achieve Remote Code Execution by uploading a jsp web/reverse shell through /jsp/zimg_upload.jsp."}, {"lang": "es", "value": "Una vulnerabilidad de inclusión de archivo local en /servlet/Report de Zucchetti Ad Hoc Infinity 2.4 permite que un atacante autenticado logre una ejecución remota de código cargando un shell web/reverso jsp a través de /jsp/zimg_upload.jsp."}], "references": [{"url": "https://members.backbox.org/zucchetti-ad-hoc-infinity-multiple-vulnerabilities/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-58062", "published_date": "2025-03-06T16:15:52.490", "last_modified_date": "2025-03-25T14:36:55.653", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: iwlwifi: mvm: avoid NULL pointer dereference\n\nWhen iterating over the links of a vif, we need to make sure that the\npointer is valid (in other words - that the link exists) before\ndereferncing it.\nUse for_each_vif_active_link that also does the check."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: iwlwifi: mvm: evitar la desreferencia de puntero NULL Al iterar sobre los enlaces de un vif, debemos asegurarnos de que el puntero sea válido (en otras palabras, que el enlace exista) antes de desreferenciarlo. Utilice for_each_vif_active_link que también realiza la verificación."}], "references": [{"url": "https://git.kernel.org/stable/c/7f6fb4b7611eb6371c493c42fefad84a1742bcbb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/cf704a7624f99eb2ffca1a16c69183e85544a613", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fbb563ad5032a07ac83c746ce5c8de5f25b5ffd0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
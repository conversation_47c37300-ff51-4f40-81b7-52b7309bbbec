{"cve_id": "CVE-2024-58068", "published_date": "2025-03-06T16:15:53.257", "last_modified_date": "2025-03-25T14:47:07.467", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nOPP: fix dev_pm_opp_find_bw_*() when bandwidth table not initialized\n\nIf a driver calls dev_pm_opp_find_bw_ceil/floor() the retrieve bandwidth\nfrom the OPP table but the bandwidth table was not created because the\ninterconnect properties were missing in the OPP consumer node, the\nkernel will crash with:\n\nUnable to handle kernel NULL pointer dereference at virtual address 0000000000000004\n...\npc : _read_bw+0x8/0x10\nlr : _opp_table_find_key+0x9c/0x174\n...\nCall trace:\n  _read_bw+0x8/0x10 (P)\n  _opp_table_find_key+0x9c/0x174 (L)\n  _find_key+0x98/0x168\n  dev_pm_opp_find_bw_ceil+0x50/0x88\n...\n\nIn order to fix the crash, create an assert function to check\nif the bandwidth table was created before trying to get a\nbandwidth with _read_bw()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: OPP: se corrige dev_pm_opp_find_bw_*() cuando la tabla de ancho de banda no está inicializada Si un controlador llama a dev_pm_opp_find_bw_ceil/floor() para recuperar el ancho de banda de la tabla OPP pero la tabla de ancho de banda no se creó porque faltaban las propiedades de interconexión en el nodo consumidor de OPP, el kernel se bloqueará con: No se puede gestionar la desreferencia del puntero NULL del kernel en la dirección virtual 0000000000000004 ... pc : _read_bw+0x8/0x10 lr : _opp_table_find_key+0x9c/0x174 ... Rastreo de llamada: _read_bw+0x8/0x10 (P) _opp_table_find_key+0x9c/0x174 (L) _find_key+0x98/0x168 dev_pm_opp_find_bw_ceil+0x50/0x88 ... Para solucionar el fallo, cree una función de afirmación para verificar si la tabla de ancho de banda se creó antes de intentar obtener un ancho de banda con _read_bw()."}], "references": [{"url": "https://git.kernel.org/stable/c/5165486681dbd67b61b975c63125f2a5cb7f96d1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/84ff05c9bd577157baed711a4f0b41206593978b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/8532fd078d2a5286915d03bb0a0893ee1955acef", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b44b9bc7cab2967c3d6a791b1cd542c89fc07f0e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ff2def251849133be6076a7c2d427d8eb963c223", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
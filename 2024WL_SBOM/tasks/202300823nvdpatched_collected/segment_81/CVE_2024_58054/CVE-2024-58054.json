{"cve_id": "CVE-2024-58054", "published_date": "2025-03-06T16:15:51.600", "last_modified_date": "2025-03-06T16:15:51.600", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nstaging: media: max96712: fix kernel oops when removing module\n\nThe following kernel oops is thrown when trying to remove the max96712\nmodule:\n\nUnable to handle kernel paging request at virtual address 00007375746174db\nMem abort info:\n  ESR = 0x0000000096000004\n  EC = 0x25: DABT (current EL), IL = 32 bits\n  SET = 0, FnV = 0\n  EA = 0, S1PTW = 0\n  FSC = 0x04: level 0 translation fault\nData abort info:\n  ISV = 0, ISS = 0x00000004, ISS2 = 0x00000000\n  CM = 0, WnR = 0, TnD = 0, TagAccess = 0\n  GCS = 0, Overlay = 0, DirtyBit = 0, Xs = 0\nuser pgtable: 4k pages, 48-bit VAs, pgdp=000000010af89000\n[00007375746174db] pgd=0000000000000000, p4d=0000000000000000\nInternal error: Oops: 0000000096000004 [#1] PREEMPT SMP\nModules linked in: crct10dif_ce polyval_ce mxc_jpeg_encdec flexcan\n    snd_soc_fsl_sai snd_soc_fsl_asoc_card snd_soc_fsl_micfil dwc_mipi_csi2\n    imx_csi_formatter polyval_generic v4l2_jpeg imx_pcm_dma can_dev\n    snd_soc_imx_audmux snd_soc_wm8962 snd_soc_imx_card snd_soc_fsl_utils\n    max96712(C-) rpmsg_ctrl rpmsg_char pwm_fan fuse\n    [last unloaded: imx8_isi]\nCPU: 0 UID: 0 PID: 754 Comm: rmmod\n\t    Tainted: G         C    6.12.0-rc6-06364-g327fec852c31 #17\nTainted: [C]=CRAP\nHardware name: NXP i.MX95 19X19 board (DT)\npstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--)\npc : led_put+0x1c/0x40\nlr : v4l2_subdev_put_privacy_led+0x48/0x58\nsp : ffff80008699bbb0\nx29: ffff80008699bbb0 x28: ffff00008ac233c0 x27: 0000000000000000\nx26: 0000000000000000 x25: 0000000000000000 x24: 0000000000000000\nx23: ffff000080cf1170 x22: ffff00008b53bd00 x21: ffff8000822ad1c8\nx20: ffff000080ff5c00 x19: ffff00008b53be40 x18: 0000000000000000\nx17: 0000000000000000 x16: 0000000000000000 x15: 0000000000000000\nx14: 0000000000000004 x13: ffff0000800f8010 x12: 0000000000000000\nx11: ffff000082acf5c0 x10: ffff000082acf478 x9 : ffff0000800f8010\nx8 : 0101010101010101 x7 : 7f7f7f7f7f7f7f7f x6 : fefefeff6364626d\nx5 : 8080808000000000 x4 : 0000000000000020 x3 : 00000000553a3dc1\nx2 : ffff00008ac233c0 x1 : ffff00008ac233c0 x0 : ff00737574617473\nCall trace:\n led_put+0x1c/0x40\n v4l2_subdev_put_privacy_led+0x48/0x58\n v4l2_async_unregister_subdev+0x2c/0x1a4\n max96712_remove+0x1c/0x38 [max96712]\n i2c_device_remove+0x2c/0x9c\n device_remove+0x4c/0x80\n device_release_driver_internal+0x1cc/0x228\n driver_detach+0x4c/0x98\n bus_remove_driver+0x6c/0xbc\n driver_unregister+0x30/0x60\n i2c_del_driver+0x54/0x64\n max96712_i2c_driver_exit+0x18/0x1d0 [max96712]\n __arm64_sys_delete_module+0x1a4/0x290\n invoke_syscall+0x48/0x10c\n el0_svc_common.constprop.0+0xc0/0xe0\n do_el0_svc+0x1c/0x28\n el0_svc+0x34/0xd8\n el0t_64_sync_handler+0x120/0x12c\n el0t_64_sync+0x190/0x194\nCode: f9000bf3 aa0003f3 f9402800 f9402000 (f9403400)\n---[ end trace 0000000000000000 ]---\n\nThis happens because in v4l2_i2c_subdev_init(), the i2c_set_cliendata()\nis called again and the data is overwritten to point to sd, instead of\npriv. So, in remove(), the wrong pointer is passed to\nv4l2_async_unregister_subdev(), leading to a crash."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: staging: media: max96712: arregla el error del kernel al eliminar el módulo El siguiente error del kernel se produce al intentar eliminar el módulo max96712: No se puede gestionar la solicitud de paginación del kernel en la dirección virtual 00007375746174db Información de aborto de memoria: ESR = 0x0000000096000004 EC = 0x25: DABT (current EL), IL = 32 bits SET = 0, FnV = 0 EA = 0, S1PTW = 0 FSC = 0x04: level 0 translation fault Data abort info: ISV = 0, ISS = 0x00000004, ISS2 = 0x00000000 CM = 0, WnR = 0, TnD = 0, TagAccess = 0 GCS = 0, Overlay = 0, DirtyBit = 0, Xs = 0 user pgtable: 4k pages, 48-bit VAs, pgdp=000000010af89000 [00007375746174db] pgd=0000000000000000, p4d=0000000000000000 Internal error: Oops: 0000000096000004 [#1] PREEMPT SMP Modules linked in: crct10dif_ce polyval_ce mxc_jpeg_encdec flexcan snd_soc_fsl_sai snd_soc_fsl_asoc_card snd_soc_fsl_micfil dwc_mipi_csi2 imx_csi_formatter polyval_generic v4l2_jpeg imx_pcm_dma can_dev snd_soc_imx_audmux snd_soc_wm8962 snd_soc_imx_card snd_soc_fsl_utils max96712(C-) rpmsg_ctrl rpmsg_char pwm_fan fuse [last unloaded: imx8_isi] CPU: 0 UID: 0 PID: 754 Comm: rmmod Tainted: G C 6.12.0-rc6-06364-g327fec852c31 #17 Tainted: [C]=CRAP Hardware name: NXP i.MX95 19X19 board (DT) pstate: 60400009 (nZCv daif +PAN -UAO -TCO -DIT -SSBS BTYPE=--) pc : led_put+0x1c/0x40 lr : v4l2_subdev_put_privacy_led+0x48/0x58 sp : ffff80008699bbb0 x29: ffff80008699bbb0 x28: ffff00008ac233c0 x27: 0000000000000000 x26: 0000000000000000 x25: 0000000000000000 x24: 0000000000000000 x23: ffff000080cf1170 x22: ffff00008b53bd00 x21: ffff8000822ad1c8 x20: ffff000080ff5c00 x19: ffff00008b53be40 x18: 0000000000000000 x17: 0000000000000000 x16: 0000000000000000 x15: 0000000000000000 x14: 0000000000000004 x13: ffff0000800f8010 x12: 0000000000000000 x11: ffff000082acf5c0 x10: ffff000082acf478 x9 : ffff0000800f8010 x8 : 0101010101010101 x7 : 7f7f7f7f7f7f7f7f x6 : fefefeff6364626d x5 : 8080808000000000 x4 : 0000000000000020 x3 : 00000000553a3dc1 x2 : ffff00008ac233c0 x1 : ffff00008ac233c0 x0 : ff00737574617473 Call trace: led_put+0x1c/0x40 v4l2_subdev_put_privacy_led+0x48/0x58 v4l2_async_unregister_subdev+0x2c/0x1a4 max96712_remove+0x1c/0x38 [max96712] i2c_device_remove+0x2c/0x9c device_remove+0x4c/0x80 device_release_driver_internal+0x1cc/0x228 driver_detach+0x4c/0x98 bus_remove_driver+0x6c/0xbc driver_unregister+0x30/0x60 i2c_del_driver+0x54/0x64 max96712_i2c_driver_exit+0x18/0x1d0 [max96712] __arm64_sys_delete_module+0x1a4/0x290 invoke_syscall+0x48/0x10c el0_svc_common.constprop.0+0xc0/0xe0 do_el0_svc+0x1c/0x28 el0_svc+0x34/0xd8 el0t_64_sync_handler+0x120/0x12c el0t_64_sync+0x190/0x194 Code: f9000bf3 aa0003f3 f9402800 f9402000 (f9403400) ---[ end trace 0000000000000000 ]---  Esto sucede porque en v4l2_i2c_subdev_init(), se vuelve a llamar a i2c_set_cliendata() y los datos se sobrescriben para apuntar a sd, en lugar de priv. Entonces, en remove(), se pasa el puntero incorrecto a v4l2_async_unregister_subdev(), lo que genera un bloqueo."}], "references": [{"url": "https://git.kernel.org/stable/c/1556b9149b81cc549c13f5e56e81e89404d8a666", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/278a98f6d8a7bbe1110433b057333536e4490edf", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3311c5395e7322298b659b8addc704b39fb3a59c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dfde3d63afbaae664c4d36e53cfb4045d5374561", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ee1b5046d5cd892a0754ab982aeaaad3702083a5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
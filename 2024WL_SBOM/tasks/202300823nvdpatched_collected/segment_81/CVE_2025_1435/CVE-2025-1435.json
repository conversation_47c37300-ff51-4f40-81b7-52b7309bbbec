{"cve_id": "CVE-2025-1435", "published_date": "2025-03-05T09:15:10.267", "last_modified_date": "2025-03-05T09:15:10.267", "descriptions": [{"lang": "en", "value": "The bbPress plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 2.6.11. This is due to missing or incorrect nonce validation on the bbp_user_add_role_on_register() function. This makes it possible for unauthenticated attackers to elevate their privileges to that of a bb<PERSON>ress Keymaster via a forged request granted they can trick a site administrator into performing an action such as clicking on a link. Rather than implementing a nonce check to provide protection against this vulnerability, which would break functionality, the plugin no longer makes it possible to select a role during registration."}, {"lang": "es", "value": "El complemento bbPress para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 2.6.11 incluida. Esto se debe a una validación de nonce incorrecta o faltante en la función bbp_user_add_role_on_register(). Esto permite que atacantes no autenticados eleven sus privilegios a los de un Keymaster de bbPress a través de una solicitud falsificada, siempre que puedan engañar a un administrador del sitio para que realice una acción como hacer clic en un enlace. En lugar de implementar una verificación de nonce para brindar protección contra esta vulnerabilidad, que interrumpiría la funcionalidad, el complemento ya no permite seleccionar un rol durante el registro."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/bbpress/trunk/includes/users/signups.php#L151", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3247345/bbpress/branches/2.6/includes/users/capabilities.php", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3247345%40bbpress&new=3247345%40bbpress&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2d776d94-8c81-4e88-bae3-946824a75c09?source=cve", "source": "<EMAIL>", "tags": []}]}
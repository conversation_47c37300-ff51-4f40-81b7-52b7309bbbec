{"cve_id": "CVE-2025-0660", "published_date": "2025-03-10T21:15:40.110", "last_modified_date": "2025-03-10T21:15:40.110", "descriptions": [{"lang": "en", "value": "Concrete CMS versions 9.0.0 through 9.3.9 are affected by a stored XSS in Folder Function.The \"Add Folder\" functionality lacks input sanitization, allowing a rogue admin to inject XSS payloads as folder names.  The Concrete CMS security team gave this vulnerability a CVSS 4.0 Score of 4.8 with vector: CVSS:4.0/AV:N/AC:L/AT:N/PR:H/UI:P/VC:L/VI:N/VA:N/SC:L/SI:N/SA:N. Versions below 9 are not affected. Thanks, <PERSON><PERSON> for reporting."}, {"lang": "es", "value": "Las versiones 9.0.0 a 9.3.9 de Concrete CMS se ven afectadas por un XSS almacenado en la función de carpeta. La funcionalidad \"Agregar carpeta\" carece de depuración de entrada, lo que permite que un administrador malintencionado inyecte payloads XSS como nombres de carpeta. El equipo de seguridad de Concrete CMS le dio a esta vulnerabilidad una puntuación CVSS 4.0 de 4.8 con el vector: CVSS:4.0/AV:N/AC:L/AT:N/PR:H/UI:P/VC:L/VI:N/VA:N/SC:L/SI:N/SA:N. Las versiones anteriores a la 9 no se ven afectadas. Gracias, <PERSON><PERSON>, por informar."}], "references": [{"url": "https://documentation.concretecms.org/9-x/developers/introduction/version-history/940-release-notes", "source": "ff5b8ace-8b95-4078-9743-eac1ca5451de", "tags": []}, {"url": "https://github.com/concretecms/bedrock/pull/370", "source": "ff5b8ace-8b95-4078-9743-eac1ca5451de", "tags": []}, {"url": "https://github.com/concretecms/concretecms/pull/12454", "source": "ff5b8ace-8b95-4078-9743-eac1ca5451de", "tags": []}]}
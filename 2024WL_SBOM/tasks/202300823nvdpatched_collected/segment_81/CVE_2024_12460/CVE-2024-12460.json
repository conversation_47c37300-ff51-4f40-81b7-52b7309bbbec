{"cve_id": "CVE-2024-12460", "published_date": "2025-03-08T03:15:35.530", "last_modified_date": "2025-03-08T03:15:35.530", "descriptions": [{"lang": "en", "value": "The Years Since – Timeless Texts plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'years-since' shortcode in all versions up to, and including, 1.4.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Years Since – Timeless Texts para WordPress es vulnerable a cross site scripting almacenado a través del código abreviado \"years-since\" del complemento en todas las versiones hasta la 1.4.1 incluida, debido a una depuración de entrada y al escape de salida insuficiente en los atributos proporcionados por el usuario. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en las páginas que se ejecutarán siempre que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/years-since/trunk/alar-years-since.php#L132", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/years-since", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2f1cb71a-aabb-4ba1-93b4-24070aaa582b?source=cve", "source": "<EMAIL>", "tags": []}]}
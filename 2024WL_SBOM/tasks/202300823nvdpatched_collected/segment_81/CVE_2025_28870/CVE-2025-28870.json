{"cve_id": "CVE-2025-28870", "published_date": "2025-03-11T21:15:44.440", "last_modified_date": "2025-03-18T20:29:41.383", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in amocrm amoCRM WebForm allows DOM-Based XSS. This issue affects amoCRM WebForm: from n/a through 1.1."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en amocrm amoCRM WebForm permite XSS basado en DOM. Este problema afecta al formulario web de amoCRM desde n/d hasta la versión 1.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/amocrm-webform/vulnerability/wordpress-amocrm-webform-plugin-1-1-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
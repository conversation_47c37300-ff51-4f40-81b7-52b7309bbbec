{"cve_id": "CVE-2025-2033", "published_date": "2025-03-06T17:15:24.490", "last_modified_date": "2025-05-13T20:58:31.063", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in code-projects Blood Bank Management System 1.0. Affected is an unknown function of the file /user_dashboard/view_donor.php. The manipulation of the argument donor_id leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en code-projects Blood Bank Management System 1.0. Se ve afectada una función desconocida del archivo /user_dashboard/view_donor.php. La manipulación del argumento donor_id provoca una inyección SQL. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/intercpt/XSS1/blob/main/SQL.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298776", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298776", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512164", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-2177", "published_date": "2025-03-11T08:15:12.297", "last_modified_date": "2025-03-11T08:15:12.297", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in libzvbi up to 0.2.43. This vulnerability affects the function vbi_search_new of the file src/search.c. The manipulation of the argument pat_len leads to integer overflow. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. Upgrading to version 0.2.44 is able to address this issue. The patch is identified as ca1672134b3e2962cd392212c73f44f8f4cb489f. It is recommended to upgrade the affected component. The code maintainer was informed beforehand about the issues. She reacted very fast and highly professional."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en libzvbi hasta la versión 0.2.43. Esta vulnerabilidad afecta a la función vbi_search_new del archivo src/search.c. La manipulación del argumento pat_len provoca un desbordamiento de enteros. El ataque puede iniciarse de forma remota. El exploit se ha hecho público y puede utilizarse. La actualización a la versión 0.2.44 puede solucionar este problema. El parche se identifica como ca1672134b3e2962cd392212c73f44f8f4cb489f. Se recomienda actualizar el componente afectado. La responsable del código fue informada de antemano sobre los problemas. Reaccionó muy rápido y con gran profesionalidad."}], "references": [{"url": "https://github.com/zapping-vbi/zvbi/commit/ca1672134b3e2962cd392212c73f44f8f4cb489f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/releases/tag/v0.2.44", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/security/advisories/GHSA-g7cg-7gw9-v8cf", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299206", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299206", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.512803", "source": "<EMAIL>", "tags": []}]}
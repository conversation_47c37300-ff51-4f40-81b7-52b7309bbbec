{"cve_id": "CVE-2024-13897", "published_date": "2025-03-06T09:15:25.787", "last_modified_date": "2025-03-06T09:15:25.787", "descriptions": [{"lang": "en", "value": "The Moving Media Library plugin for WordPress is vulnerable to arbitrary file deletion due to insufficient file path validation in the generate_json_page function in all versions up to, and including, 1.22. This makes it possible for authenticated attackers, with Administrator-level access and above, to delete arbitrary files on the server, which can easily lead to remote code execution when the right file is deleted (such as wp-config.php)."}, {"lang": "es", "value": "El complemento Moving Media Library para WordPress es vulnerable a la eliminación arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función generate_json_page en todas las versiones hasta la 1.22 incluida. Esto permite que atacantes autenticados, con acceso de nivel de administrador o superior, eliminen archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código cuando se elimina el archivo correcto (como wp-config.php)."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/moving-media-library/trunk/lib/class-movingmedialibraryadmin.php#L166", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3244709/moving-media-library/trunk/lib/class-movingmedialibraryadmin.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/815ce00b-3753-4c38-8a30-5242a5841734?source=cve", "source": "<EMAIL>", "tags": []}]}
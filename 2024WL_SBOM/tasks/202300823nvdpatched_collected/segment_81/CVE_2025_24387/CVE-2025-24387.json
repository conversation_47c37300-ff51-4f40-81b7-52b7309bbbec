{"cve_id": "CVE-2025-24387", "published_date": "2025-03-10T10:15:14.360", "last_modified_date": "2025-03-24T14:11:20.387", "descriptions": [{"lang": "en", "value": "A vulnerability in OTRS Application Server allows session hijacking due to missing attributes for sensitive \ncookie settings in HTTPS sessions. A request to an OTRS endpoint from a possible malicious web site, would send the authentication cookie, performing an unwanted read operation.\n \n\nThis issue affects:\n\n  *  OTRS 7.0.X\n  *  OTRS 8.0.X\n  *  OTRS 2023.X\n  *  OTRS 2024.X\n  *  OTRS 2025.x"}, {"lang": "es", "value": "Una vulnerabilidad en OTRS Application Server permite el secuestro de sesiones debido a la falta de atributos para configuraciones de cookies confidenciales en sesiones HTTPS. Una solicitud a un endpoint de OTRS desde un posible sitio web malicioso enviaría la cookie de autenticación y realizaría una operación de lectura no deseada. Este problema afecta a: * OTRS 7.0.X * OTRS 8.0.X * OTRS 2023.X * OTRS 2024.X * OTRS 2025.x"}], "references": [{"url": "https://otrs.com/release-notes/otrs-security-advisory-2025-05/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2025-25306", "published_date": "2025-03-10T19:15:40.230", "last_modified_date": "2025-03-10T19:15:40.230", "descriptions": [{"lang": "en", "value": "Misskey is an open source, federated social media platform. The patch for CVE-2024-52591 did not sufficiently validate the relation between the `id` and `url` fields of ActivityPub objects. An attacker can forge an object where they claim authority in the `url` field even if the specific ActivityPub object type require authority in the `id` field. Version 2025.2.1 addresses the issue."}, {"lang": "es", "value": "Misskey es una plataforma de redes sociales federada de código abierto. El parche para CVE-2024-52591 no validó de manera suficiente la relación entre los campos `id` y `url` de los objetos ActivityPub. Un atacante puede falsificar un objeto en el que reclame autoridad en el campo `url` incluso si el tipo de objeto ActivityPub específico requiere autoridad en el campo `id`. La versión 2025.2.1 soluciona el problema."}], "references": [{"url": "https://github.com/misskey-dev/misskey/releases/tag/2025.2.1", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/misskey-dev/misskey/security/advisories/GHSA-6w2c-vf6f-xf26", "source": "<EMAIL>", "tags": []}]}
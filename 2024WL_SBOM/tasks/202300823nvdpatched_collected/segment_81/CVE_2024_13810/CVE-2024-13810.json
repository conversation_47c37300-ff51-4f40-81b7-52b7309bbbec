{"cve_id": "CVE-2024-13810", "published_date": "2025-03-05T10:15:17.503", "last_modified_date": "2025-03-05T10:15:17.503", "descriptions": [{"lang": "en", "value": "The Zass - WooCommerce Theme for Handmade Artists and Artisans theme for WordPress is vulnerable to unauthorized access due to a missing capability check on the 'zass_import_zass' AJAX actions in all versions up to, and including, ********. This makes it possible for authenticated attackers, with Subscriber-level access and above, to import demo content and overwrite the site."}, {"lang": "es", "value": "El tema Zass - WooCommerce Theme for Handmade Artists and Artisans para WordPress es vulnerable al acceso no autorizado debido a una verificación de capacidad faltante en las acciones AJAX 'zass_import_zass' en todas las versiones hasta la ******** incluida. Esto permite que atacantes autenticados, con acceso de nivel de suscriptor y superior, importen contenido de demostración y sobrescriban el sitio."}], "references": [{"url": "https://themeforest.net/item/zass-wordpress-woocommerce-theme/19614113", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d85e54c2-dff6-42e6-8123-767438f9c5f1?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2114", "published_date": "2025-03-09T05:15:30.270", "last_modified_date": "2025-03-09T05:15:30.270", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in Shenzhen Sixun Software Sixun Shanghui Group Business Management System 7. This issue affects some unknown processing of the file /WebPages/Adm/OperatorStop.asp of the component Reset Password Interface. The manipulation of the argument OperId leads to improper authorization. The attack may be initiated remotely. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en Shenzhen Sixun Software Sixun Shanghui Group Business Management System 7. Este problema afecta a algunos procesos desconocidos del archivo /WebPages/Adm/OperatorStop.asp del componente Reset Password Interface. La manipulación del argumento OperId conduce a una autorización incorrecta. El ataque puede iniciarse de forma remota. Es un ataque de complejidad bastante alta. Se sabe que la explotación es difícil. La explotación se ha divulgado al público y puede utilizarse. Se contactó al proveedor con anticipación sobre esta divulgación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/zhangbuneng/an-arbitrary-user-password-reset-vulnerability-in-the-Sixun-Shanghui-7-Group/issues/1#issue-2877317082", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299009", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299009", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.506591", "source": "<EMAIL>", "tags": []}]}
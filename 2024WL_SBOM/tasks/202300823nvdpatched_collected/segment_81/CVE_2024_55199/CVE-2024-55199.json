{"cve_id": "CVE-2024-55199", "published_date": "2025-03-10T18:15:29.757", "last_modified_date": "2025-06-23T20:10:31.250", "descriptions": [{"lang": "en", "value": "A Stored Cross Site Scripting (XSS) vulnerability in Celk Sistemas Celk Saude v.********* allows a remote attacker to store JavaScript code inside a PDF file through the file upload feature. When the file is rendered, the injected code is executed on the user's browser."}, {"lang": "es", "value": "Una vulnerabilidad de tipo Cross Site Scripting (XSS) almacenado en Celk Sistemas Celk Saude v.********* permite a un atacante remoto almacenar código JavaScript dentro de un archivo PDF a través de la función de carga de archivos. Cuando se procesa el archivo, el código inyectado se ejecuta en el navegador del usuario."}], "references": [{"url": "https://github.com/gabriel-bri/vulnerability-research/tree/main/CVE-2024-55199", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://portswigger.net/web-security/cross-site-scripting/stored", "source": "<EMAIL>", "tags": ["Technical Description"]}]}
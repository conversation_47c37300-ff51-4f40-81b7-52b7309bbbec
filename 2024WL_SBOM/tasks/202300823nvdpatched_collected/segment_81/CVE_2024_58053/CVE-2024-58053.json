{"cve_id": "CVE-2024-58053", "published_date": "2025-03-06T16:15:51.490", "last_modified_date": "2025-03-06T16:15:51.490", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nrxrpc: Fix handling of received connection abort\n\nFix the handling of a connection abort that we've received.  Though the\nabort is at the connection level, it needs propagating to the calls on that\nconnection.  Whilst the propagation bit is performed, the calls aren't then\nwoken up to go and process their termination, and as no further input is\nforthcoming, they just hang.\n\nAlso add some tracing for the logging of connection aborts."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: rxrpc: Se ha corregido la gestión de la interrupción de la conexión recibida Se ha corregido la gestión de una interrupción de la conexión que hemos recibido. Aunque la interrupción se produce a nivel de conexión, es necesario propagarla a las llamadas de esa conexión. Mientras se realiza el bit de propagación, las llamadas no se activan para procesar su finalización y, como no se recibe ninguna otra entrada, simplemente se cuelgan. También se ha añadido algún seguimiento para el registro de las interrupciones de la conexión."}], "references": [{"url": "https://git.kernel.org/stable/c/0e56ebde245e4799ce74d38419426f2a80d39950", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5842ce7b120c65624052a8da04460d35b26caac0", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/96d1d927c4d03ee9dcee7640bca70b74e63504fc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9c6702260557c0183d8417c79a37777a3d3e58e8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
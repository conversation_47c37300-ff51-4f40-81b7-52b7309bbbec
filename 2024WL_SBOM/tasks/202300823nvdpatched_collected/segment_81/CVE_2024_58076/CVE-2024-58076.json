{"cve_id": "CVE-2024-58076", "published_date": "2025-03-06T17:15:20.997", "last_modified_date": "2025-03-25T14:26:52.030", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nclk: qcom: gcc-sm6350: Add missing parent_map for two clocks\n\nIf a clk_rcg2 has a parent, it should also have parent_map defined,\notherwise we'll get a NULL pointer dereference when calling clk_set_rate\nlike the following:\n\n  [    3.388105] Call trace:\n  [    3.390664]  qcom_find_src_index+0x3c/0x70 (P)\n  [    3.395301]  qcom_find_src_index+0x1c/0x70 (L)\n  [    3.399934]  _freq_tbl_determine_rate+0x48/0x100\n  [    3.404753]  clk_rcg2_determine_rate+0x1c/0x28\n  [    3.409387]  clk_core_determine_round_nolock+0x58/0xe4\n  [    3.421414]  clk_core_round_rate_nolock+0x48/0xfc\n  [    3.432974]  clk_core_round_rate_nolock+0xd0/0xfc\n  [    3.444483]  clk_core_set_rate_nolock+0x8c/0x300\n  [    3.455886]  clk_set_rate+0x38/0x14c\n\nAdd the parent_map property for two clocks where it's missing and also\nun-inline the parent_data as well to keep the matching parent_map and\nparent_data together."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad:  clk: qcom: gcc-sm6350: Add missing parent_map for two clocks If a clk_rcg2 has a parent, it should also have parent_map defined, otherwise we'll get a NULL pointer dereference when calling clk_set_rate like the following: [ 3.388105] Call trace: [ 3.390664] qcom_find_src_index+0x3c/0x70 (P) [ 3.395301] qcom_find_src_index+0x1c/0x70 (L) [ 3.399934] _freq_tbl_determine_rate+0x48/0x100 [ 3.404753] clk_rcg2_determine_rate+0x1c/0x28 [ 3.409387] clk_core_determine_round_nolock+0x58/0xe4 [ 3.421414] clk_core_round_rate_nolock+0x48/0xfc [ 3.432974] clk_core_round_rate_nolock+0xd0/0xfc [ 3.444483] clk_core_set_rate_nolock+0x8c/0x300 [ 3.455886] clk_set_rate+0x38/0x14c Agregue la propiedad parent_map para dos relojes donde falta y también desvincule parent_data para mantener juntos los parent_map y parent_data coincidentes."}], "references": [{"url": "https://git.kernel.org/stable/c/08b77ed7cfaac62bba51ac7a0487409ec9fcbc84", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/175af15551ed5aa6af16ff97aff75cfffb42da21", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/39336edd14a59dc086fb19957655e0f340bb28e8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3e567032233a240b903dc11c9f18eeb3faa10ffa", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/96fe1a7ee477d701cfc98ab9d3c730c35d966861", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b6fe13566bf5676b1e3b72d2a06d875733e93ee6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
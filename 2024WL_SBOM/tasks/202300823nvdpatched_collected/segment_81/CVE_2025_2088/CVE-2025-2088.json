{"cve_id": "CVE-2025-2088", "published_date": "2025-03-07T15:15:14.833", "last_modified_date": "2025-03-13T15:53:14.107", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Pre-School Enrollment System up to 1.0. Affected is an unknown function of the file /admin/profile.php. The manipulation of the argument fullname/emailid/mobileNumber leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Pre-School Enrollment System hasta la versión 1.0. Se trata de una función desconocida del archivo /admin/profile.php. La manipulación del argumento fullname/emailid/mobileNumber provoca una inyección SQL. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/SECWG/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298902", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298902", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514974", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
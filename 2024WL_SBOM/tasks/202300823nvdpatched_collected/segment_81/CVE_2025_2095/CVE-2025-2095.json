{"cve_id": "CVE-2025-2095", "published_date": "2025-03-07T22:15:39.023", "last_modified_date": "2025-04-03T15:29:25.480", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in TOTOLINK EX1800T 9.1.0cu.2112_B20220316. This affects the function setDmzCfg of the file /cgi-bin/cstecgi.cgi. The manipulation of the argument ip leads to os command injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en TOTOLINK EX1800T 9.1.0cu.2112_B20220316. Afecta a la función setDmzCfg del archivo /cgi-bin/cstecgi.cgi. La manipulación del argumento ip provoca la inyección de comandos os. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/kn0sky/cve/blob/main/TOTOLINK%20EX1800T/OS%20Command%20Injection%2003%20setDmzCfg-_ip.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298953", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298953", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.515321", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}
{"cve_id": "CVE-2024-13778", "published_date": "2025-03-05T10:15:15.720", "last_modified_date": "2025-03-05T10:15:15.720", "descriptions": [{"lang": "en", "value": "The Hero Mega Menu - Responsive WordPress Menu Plugin plugin for WordPress is vulnerable to SQL Injection via several functions in all versions up to, and including, 1.16.5 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Hero Mega Menu - Responsive WordPress Menu Plugin para WordPress es vulnerable a la inyección SQL a través de varias funciones en todas las versiones hasta la 1.16.5 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel de suscriptor y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/hero-menu-responsive-wordpress-mega-menu-plugin/10324895", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5bdf04e6-6d9d-41a3-ac54-1a95f4617ea4?source=cve", "source": "<EMAIL>", "tags": []}]}
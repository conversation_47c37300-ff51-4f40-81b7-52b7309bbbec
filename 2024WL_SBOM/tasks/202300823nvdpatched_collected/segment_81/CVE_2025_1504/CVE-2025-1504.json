{"cve_id": "CVE-2025-1504", "published_date": "2025-03-08T03:15:37.393", "last_modified_date": "2025-03-24T18:27:10.747", "descriptions": [{"lang": "en", "value": "The Post Lockdown plugin for WordPress is vulnerable to Information Exposure in all versions up to, and including, 4.0.2 via the 'pl_autocomplete' AJAX action due to insufficient restrictions on which posts can be included. This makes it possible for authenticated attackers, with Subscriber-level access and above, to extract data from password protected, private, or draft posts that they should not have access to."}, {"lang": "es", "value": "El complemento Post Lockdown para WordPress es vulnerable a la exposición de información en todas las versiones hasta la 4.0.2 incluida a través de la acción AJAX 'pl_autocomplete' debido a restricciones insuficientes sobre qué publicaciones se pueden incluir. Esto hace posible que atacantes autenticados, con acceso de nivel de suscriptor y superior, extraigan datos de publicaciones protegidas con contraseña, privadas o borradores a las que no deberían tener acceso."}], "references": [{"url": "https://wordpress.org/plugins/post-lockdown/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/447cef6f-fa2e-4087-946d-6e0214830ea9?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
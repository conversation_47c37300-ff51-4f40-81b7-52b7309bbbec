{"cve_id": "CVE-2025-21834", "published_date": "2025-03-06T17:15:23.397", "last_modified_date": "2025-03-06T17:15:23.397", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nseccomp: passthrough uretprobe systemcall without filtering\n\nWhen attaching uretprobes to processes running inside docker, the attached\nprocess is segfaulted when encountering the retprobe.\n\nThe reason is that now that uretprobe is a system call the default seccomp\nfilters in docker block it as they only allow a specific set of known\nsyscalls. This is true for other userspace applications which use seccomp\nto control their syscall surface.\n\nSince uretprobe is a \"kernel implementation detail\" system call which is\nnot used by userspace application code directly, it is impractical and\nthere's very little point in forcing all userspace applications to\nexplicitly allow it in order to avoid crashing tracked processes.\n\nPass this systemcall through seccomp without depending on configuration.\n\nNote: uretprobe is currently only x86_64 and isn't expected to ever be\nsupported in i386.\n\n[kees: minimized changes for easier backporting, tweaked commit log]"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: seccomp: pasar a través de la llamada al sistema uretprobe sin filtrar Al adjuntar uretprobes a procesos que se ejecutan dentro de docker, el proceso adjunto sufre un error de segmentación al encontrar retprobe. La razón es que ahora que uretprobe es una llamada al sistema, los filtros seccomp predeterminados en docker lo bloquean ya que solo permiten un conjunto específico de llamadas al sistema conocidas. Esto es cierto para otras aplicaciones de espacio de usuario que usan seccomp para controlar su superficie de llamada al sistema. Dado que uretprobe es una llamada al sistema de \"detalle de implementación del kernel\" que no es utilizada directamente por el código de la aplicación de espacio de usuario, no es práctica y no tiene mucho sentido forzar a todas las aplicaciones de espacio de usuario a permitirla explícitamente para evitar que se bloqueen los procesos rastreados. Pase esta llamada al sistema a través de seccomp sin depender de la configuración. Nota: uretprobe actualmente solo es x86_64 y no se espera que sea compatible con i386. [kees: se minimizaron los cambios para facilitar la adaptación, se modificó el registro de confirmaciones]"}], "references": [{"url": "https://git.kernel.org/stable/c/5a262628f4cf2437d863fe41f9d427177b87664c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cf6cb56ef24410fb5308f9655087f1eddf4452e6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fa80018aa5be10c35e9fa896b7b4061a8dce3eed", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
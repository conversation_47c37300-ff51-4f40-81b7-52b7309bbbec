{"cve_id": "CVE-2024-13827", "published_date": "2025-03-05T09:15:09.390", "last_modified_date": "2025-03-05T09:15:09.390", "descriptions": [{"lang": "en", "value": "The Razorpay Subscription Button Elementor Plugin plugin for WordPress is vulnerable to Reflected Cross-Site Scripting due to the use of add_query_arg() and remove_query_arg() functions without appropriate escaping on the URL in all versions up to, and including, 1.0.3. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Razorpay Subscription Button Elementor Plugin para WordPress es vulnerable a Cross-Site Scripting reflejado debido al uso de las funciones add_query_arg() y remove_query_arg() sin el escape adecuado en la URL en todas las versiones hasta la 1.0.3 incluida. Esto permite que atacantes no autenticados inyecten secuencias de comandos web arbitrarias en páginas que se ejecutan si logran engañar a un usuario para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/razorpay-subscription-button-elementor/tags/1.0.3/includes/rzp-payment-buttons.php#L78", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/razorpay-subscription-button-elementor/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a8cdde8d-db43-4702-81c3-ea2d867baa8d?source=cve", "source": "<EMAIL>", "tags": []}]}
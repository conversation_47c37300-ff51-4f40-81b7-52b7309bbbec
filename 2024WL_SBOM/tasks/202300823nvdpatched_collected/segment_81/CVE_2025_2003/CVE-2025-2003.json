{"cve_id": "CVE-2025-2003", "published_date": "2025-03-05T19:15:39.630", "last_modified_date": "2025-03-28T16:22:53.547", "descriptions": [{"lang": "en", "value": "Incorrect authorization in PAM vaults in Devolutions Server 2024.3.12 and earlier allows an authenticated user to bypass the 'add in root' permission."}, {"lang": "es", "value": "La autorización incorrecta en las bóvedas PAM en Devolutions Server 2024.3.12 y versiones anteriores permite que un usuario autenticado omita el permiso \"agregar en la raíz\"."}], "references": [{"url": "https://devolutions.net/security/advisories/DEVO-2025-0003/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
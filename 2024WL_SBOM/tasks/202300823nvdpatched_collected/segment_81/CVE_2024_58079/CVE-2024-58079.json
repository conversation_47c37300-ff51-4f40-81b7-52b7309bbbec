{"cve_id": "CVE-2024-58079", "published_date": "2025-03-06T17:15:21.360", "last_modified_date": "2025-03-13T13:15:46.133", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: uvcvideo: Fix crash during unbind if gpio unit is in use\n\nWe used the wrong device for the device managed functions. We used the\nusb device, when we should be using the interface device.\n\nIf we unbind the driver from the usb interface, the cleanup functions\nare never called. In our case, the IRQ is never disabled.\n\nIf an IRQ is triggered, it will try to access memory sections that are\nalready free, causing an OOPS.\n\nWe cannot use the function devm_request_threaded_irq here. The devm_*\nclean functions may be called after the main structure is released by\nuvc_delete.\n\nLuckily this bug has small impact, as it is only affected by devices\nwith gpio units and the user has to unbind the device, a disconnect will\nnot trigger this error."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: uvcvideo: Se corrige el fallo durante la desvinculación si la unidad gpio está en uso. Usamos el dispositivo incorrecto para las funciones administradas por el dispositivo. Usamos el dispositivo usb, cuando deberíamos estar usando el dispositivo de interfaz. Si desvinculamos el controlador de la interfaz usb, las funciones de limpieza nunca se llaman. En nuestro caso, la IRQ nunca se deshabilita. Si se activa una IRQ, intentará acceder a secciones de memoria que ya están libres, lo que provocará un OOPS. No podemos usar la función devm_request_threaded_irq aquí. Las funciones de limpieza devm_* pueden llamarse después de que uvc_delete libere la estructura principal. Afortunadamente, este error tiene un impacto pequeño, ya que solo se ve afectado por dispositivos con unidades gpio y el usuario tiene que desvincular el dispositivo, una desconexión no activará este error."}], "references": [{"url": "https://git.kernel.org/stable/c/0b5e0445bc8384c18bd35cb9fe87f6258c6271d9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/0fdd7cc593385e46e92e180b71e264fc9c195298", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3c00e94d00ca079bef7906d6f39d1091bccfedd3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5d2e65cbe53d0141ed095cf31c2dcf3d8668c11d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a9ea1a3d88b7947ce8cadb2afceee7a54872bbc5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d2eac8b14ac690aa73052aa6d4ba69005715367e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
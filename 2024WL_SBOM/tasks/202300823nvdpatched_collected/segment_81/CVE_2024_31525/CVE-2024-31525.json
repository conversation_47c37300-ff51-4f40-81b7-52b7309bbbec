{"cve_id": "CVE-2024-31525", "published_date": "2025-03-05T19:15:37.340", "last_modified_date": "2025-03-06T15:15:15.607", "descriptions": [{"lang": "en", "value": "Peppermint Ticket Management 0.4.6 is vulnerable to Incorrect Access Control. A regular registered user is able to elevate his privileges to admin and gain complete access to the system as the authorization mechanism is not validated on the server side and only on the client side. This can result, for example, in creating a new admin user in the system which enables persistent access for the attacker as an administrator."}, {"lang": "es", "value": "Peppermint Ticket Management 0.4.6 es vulnerable a un control de acceso incorrecto. Un usuario registrado normal puede elevar sus privilegios a administrador y obtener acceso completo al sistema, ya que el mecanismo de autorización no está validado en el lado del servidor, sino solo en el lado del cliente. Esto puede dar como resultado, por ejemplo, la creación de un nuevo usuario administrador en el sistema que permite el acceso persistente para el atacante como administrador."}], "references": [{"url": "https://cwe.mitre.org/data/definitions/285.html", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Peppermint-Lab/peppermint/issues/258", "source": "<EMAIL>", "tags": []}]}
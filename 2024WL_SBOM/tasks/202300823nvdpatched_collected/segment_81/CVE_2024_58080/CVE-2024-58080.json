{"cve_id": "CVE-2024-58080", "published_date": "2025-03-06T17:15:21.473", "last_modified_date": "2025-03-25T14:27:18.737", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nclk: qcom: dispcc-sm6350: Add missing parent_map for a clock\n\nIf a clk_rcg2 has a parent, it should also have parent_map defined,\notherwise we'll get a NULL pointer dereference when calling clk_set_rate\nlike the following:\n\n  [    3.388105] Call trace:\n  [    3.390664]  qcom_find_src_index+0x3c/0x70 (P)\n  [    3.395301]  qcom_find_src_index+0x1c/0x70 (L)\n  [    3.399934]  _freq_tbl_determine_rate+0x48/0x100\n  [    3.404753]  clk_rcg2_determine_rate+0x1c/0x28\n  [    3.409387]  clk_core_determine_round_nolock+0x58/0xe4\n  [    3.421414]  clk_core_round_rate_nolock+0x48/0xfc\n  [    3.432974]  clk_core_round_rate_nolock+0xd0/0xfc\n  [    3.444483]  clk_core_set_rate_nolock+0x8c/0x300\n  [    3.455886]  clk_set_rate+0x38/0x14c\n\nAdd the parent_map property for the clock where it's missing and also\nun-inline the parent_data as well to keep the matching parent_map and\nparent_data together."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: clk: qcom: dispcc-sm6350: Add missing parent_map for a clock If a clk_rcg2 has a parent, it should also have parent_map defined, otherwise we'll get a NULL pointer dereference when calling clk_set_rate like the following: [ 3.388105] Call trace: [ 3.390664] qcom_find_src_index+0x3c/0x70 (P) [ 3.395301] qcom_find_src_index+0x1c/0x70 (L) [ 3.399934] _freq_tbl_determine_rate+0x48/0x100 [ 3.404753] clk_rcg2_determine_rate+0x1c/0x28 [ 3.409387] clk_core_determine_round_nolock+0x58/0xe4 [ 3.421414] clk_core_round_rate_nolock+0x48/0xfc [ 3.432974] clk_core_round_rate_nolock+0xd0/0xfc [ 3.444483] clk_core_set_rate_nolock+0x8c/0x300 [ 3.455886] clk_set_rate+0x38/0x14c Agregue la propiedad parent_map para el reloj donde falta y también desvincule parent_data para mantener juntos los parent_map y parent_data coincidentes."}], "references": [{"url": "https://git.kernel.org/stable/c/2dba8d5d423fa5f6f3a687aa6e0da5808f69091b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3ad28517385e2821e8e43388d6a0b3e1ba0bc3ab", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3daca9050857220726732ad9d4a8512069386f46", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a1f15808adfd77268eac7fefce5378ad9fedbfba", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d4cdb196f182d2fbe336c968228be00d8c3fed05", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
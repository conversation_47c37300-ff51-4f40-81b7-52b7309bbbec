{"cve_id": "CVE-2025-2124", "published_date": "2025-03-09T16:15:11.533", "last_modified_date": "2025-03-09T16:15:11.533", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, was found in Control iD RH iD *********. This affects an unknown part of the file /v2/customerdb/person.svc/change_password of the component API Handler. The manipulation of the argument message leads to cross site scripting. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en Control iD RH iD *********. Afecta a una parte desconocida del archivo /v2/customerdb/person.svc/change_password del componente API Handler. La manipulación del argumento message provoca ataques de cross site scripting. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con anticipación sobre esta revelación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/yago3008/cves", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299037", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299037", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.509845", "source": "<EMAIL>", "tags": []}]}
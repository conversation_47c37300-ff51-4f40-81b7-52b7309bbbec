{"cve_id": "CVE-2024-13805", "published_date": "2025-03-07T10:15:16.123", "last_modified_date": "2025-05-21T18:36:01.783", "descriptions": [{"lang": "en", "value": "The Advanced File Manager — Ultimate WordPress File Manager and Document Library Plugin plugin for WordPress is vulnerable to Stored Cross-Site Scripting via SVG File uploads in all versions up to, and including, 5.2.14 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Subscriber-level access and above, and granted permissions by an Administrator, to inject arbitrary web scripts in pages that will execute whenever a user accesses the SVG file."}, {"lang": "es", "value": "El complemento Advanced File Manager — Ultimate WordPress File Manager and Document Library Plugin para WordPress es vulnerable a Cross-Site Scripting almacenado a través de cargas de archivos SVG en todas las versiones hasta la 5.2.14 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes autenticados, con acceso de nivel de suscriptor o superior, y con permisos otorgados por un administrador, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán siempre que un usuario acceda al archivo SVG."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/file-manager-advanced/trunk/application/class_fma_connector.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3249482/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0fc6cc1b-7d49-48cd-9bce-d37c6dcfece9?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
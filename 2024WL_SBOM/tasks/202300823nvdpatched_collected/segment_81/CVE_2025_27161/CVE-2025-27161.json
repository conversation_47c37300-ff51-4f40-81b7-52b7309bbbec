{"cve_id": "CVE-2025-27161", "published_date": "2025-03-11T18:15:33.993", "last_modified_date": "2025-04-28T16:48:42.960", "descriptions": [{"lang": "en", "value": "Acrobat Reader versions 24.001.30225, 20.005.30748, 25.001.20428 and earlier are affected by an out-of-bounds read vulnerability when parsing a crafted file, which could result in a read past the end of an allocated memory structure. An attacker could leverage this vulnerability to execute code in the context of the current user. Exploitation of this issue requires user interaction in that a victim must open a malicious file."}, {"lang": "es", "value": "Las versiones 24.001.30225, 20.005.30748, 25.001.20428 y anteriores de Acrobat Reader se ven afectadas por una vulnerabilidad de lectura fuera de los límites al analizar un archivo manipulado, lo que podría resultar en una lectura más allá del final de la estructura de memoria asignada. Un atacante podría aprovechar esta vulnerabilidad para ejecutar código en el contexto del usuario actual. Para explotar este problema, la víctima debe abrir un archivo malicioso."}], "references": [{"url": "https://helpx.adobe.com/security/products/acrobat/apsb25-14.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2025-28926", "published_date": "2025-03-11T21:15:50.397", "last_modified_date": "2025-03-11T21:15:50.397", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in popeating Post Read Time allows Stored XSS. This issue affects Post Read Time: from n/a through 1.2.6."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en popeating Post Read Time permite XSS almacenado. Este problema afecta a Tiempo de lectura posterior desde n/d hasta la versión 1.2.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/post-read-time/vulnerability/wordpress-post-read-time-plugin-1-2-6-stored-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
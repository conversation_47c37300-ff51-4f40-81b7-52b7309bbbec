{"cve_id": "CVE-2025-28900", "published_date": "2025-03-11T21:15:47.313", "last_modified_date": "2025-03-11T21:15:47.313", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in webgarb TabGarb Pro allows Stored XSS. This issue affects TabGarb Pro: from n/a through 2.6."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en webgarb TabGarb Pro que permite XSS almacenado. Este problema afecta a TabGarb Pro desde la versión n/d hasta la 2.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/tabgarb/vulnerability/wordpress-tabgarb-pro-plugin-2-6-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
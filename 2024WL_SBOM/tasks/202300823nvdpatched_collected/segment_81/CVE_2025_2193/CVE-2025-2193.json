{"cve_id": "CVE-2025-2193", "published_date": "2025-03-11T13:15:43.997", "last_modified_date": "2025-04-09T20:48:27.550", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in MRCMS 3.1.2 and classified as critical. This vulnerability affects the function delete of the file /admin/file/delete.do of the component org.marker.mushroom.controller.FileController. The manipulation of the argument path/name leads to path traversal. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en MRCMS 3.1.2, clasificada como crítica. Esta vulnerabilidad afecta a la función de eliminación del archivo /admin/file/delete.do del componente org.marker.mushroom.controller.FileController. La manipulación del argumento path/name provoca un path traversal. El ataque puede iniciarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299218", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299218", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511724", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
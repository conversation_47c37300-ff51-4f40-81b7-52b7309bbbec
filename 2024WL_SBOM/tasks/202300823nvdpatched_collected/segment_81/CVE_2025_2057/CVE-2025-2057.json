{"cve_id": "CVE-2025-2057", "published_date": "2025-03-07T02:15:38.237", "last_modified_date": "2025-05-08T18:49:31.633", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Emergency Ambulance Hiring Portal 1.0. Affected is an unknown function of the file /admin/about-us.php. The manipulation of the argument pagedes leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Emergency Ambulance Hiring Portal 1.0. Se trata de una función desconocida del archivo /admin/about-us.php. La manipulación del argumento pagedes provoca una inyección SQL. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/12T40910/CVE/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298812", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298812", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514461", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
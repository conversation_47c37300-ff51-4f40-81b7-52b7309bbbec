{"cve_id": "CVE-2025-21826", "published_date": "2025-03-06T16:15:54.863", "last_modified_date": "2025-03-13T13:15:56.677", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nnetfilter: nf_tables: reject mismatching sum of field_len with set key length\n\nThe field length description provides the length of each separated key\nfield in the concatenation, each field gets rounded up to 32-bits to\ncalculate the pipapo rule width from pipapo_init(). The set key length\nprovides the total size of the key aligned to 32-bits.\n\nRegister-based arithmetics still allows for combining mismatching set\nkey length and field length description, eg. set key length 10 and field\ndescription [ 5, 4 ] leading to pipapo width of 12."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: netfilter: nf_tables: rechaza la suma no coincidente de field_len con la longitud de la clave establecida La descripción de la longitud del campo proporciona la longitud de cada campo de clave separado en la concatenación, cada campo se redondea a 32 bits para calcular el ancho de la regla pipapo desde pipapo_init(). La longitud de la clave establecida proporciona el tamaño total de la clave alineada a 32 bits. La aritmética basada en registros aún permite combinar la longitud de la clave establecida y la descripción de la longitud del campo no coincidentes, p. ej., la longitud de la clave establecida 10 y la descripción del campo [ 5, 4 ], lo que lleva a un ancho de pipapo de 12."}], "references": [{"url": "https://git.kernel.org/stable/c/1b9335a8000fb70742f7db10af314104b6ace220", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2ac254343d3cf228ae0738b2615fedf85d000752", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/49b7182b97bafbd5645414aff054b4a65d05823d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5083a7ae45003456c253e981b30a43f71230b4a3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6b467c8feac759f4c5c86d708beca2aa2b29584f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/82e491e085719068179ff6a5466b7387cc4bbf32", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/ab50d0eff4a939d20c37721fd9766347efcdb6f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
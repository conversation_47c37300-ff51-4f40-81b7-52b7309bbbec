{"cve_id": "CVE-2024-13320", "published_date": "2025-03-07T07:15:22.963", "last_modified_date": "2025-03-07T07:15:22.963", "descriptions": [{"lang": "en", "value": "The CURCY - WooCommerce Multi Currency - Currency Switcher plugin for WordPress is vulnerable to SQL Injection via the 'wc_filter_price_meta[where]' parameter in all versions up to, and including, 2.3.6 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento CURCY - WooCommerce Multi Currency - Currency Switcher para WordPress es vulnerable a la inyección SQL a través del parámetro 'wc_filter_price_meta[where]' en todas las versiones hasta la 2.3.6 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto hace posible que atacantes no autenticados agreguen consultas SQL adicionales a consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/woocommerce-multi-currency/20948446", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6d359a5c-db11-416e-a329-c3ed67b1a925?source=cve", "source": "<EMAIL>", "tags": []}]}
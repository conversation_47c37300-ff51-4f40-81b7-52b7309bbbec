{"cve_id": "CVE-2024-45324", "published_date": "2025-03-11T15:15:41.743", "last_modified_date": "2025-07-24T19:06:14.957", "descriptions": [{"lang": "en", "value": "A use of externally-controlled format string vulnerability [CWE-134] in FortiOS version 7.4.0 through 7.4.4, version 7.2.0 through 7.2.9, version 7.0.0 through 7.0.15 and before 6.4.15, FortiProxy version 7.4.0 through 7.4.6, version 7.2.0 through 7.2.12 and before 7.0.19, FortiPAM version 1.4.0 through 1.4.2 and before 1.3.1, FortiSRA version 1.4.0 through 1.4.2 and before 1.3.1 and FortiWeb version 7.4.0 through 7.4.5, version 7.2.0 through 7.2.10 and before 7.0.10 allows a privileged attacker to execute unauthorized code or commands via specially crafted HTTP or HTTPS commands."}, {"lang": "es", "value": "Una vulnerabilidad de uso de cadena de formato controlada externamente [CWE-134] en FortiOS versión 7.4.0 a 7.4.4, versión 7.2.0 a 7.2.9, versión 7.0.0 a 7.0.15 y anteriores a 6.4.15, FortiProxy versión 7.4.0 a 7.4.6, versión 7.2.0 a 7.2.12 y anteriores a 7.0.19, FortiPAM versión 1.4.0 a 1.4.2 y anteriores a 1.3.1, FortiSRA versión 1.4.0 a 1.4.2 y anteriores a 1.3.1 y FortiWeb versión 7.4.0 a 7.4.5, versión 7.2.0 a 7.2.10 y anteriores a 7.0.10 permite a un atacante privilegiado ejecutar código o comandos no autorizados a través de comandos HTTP o HTTPS especialmente manipulados."}], "references": [{"url": "https://fortiguard.fortinet.com/psirt/FG-IR-24-325", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2025-28919", "published_date": "2025-03-11T21:15:49.623", "last_modified_date": "2025-03-11T21:15:49.623", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Shellbot Easy Image Display allows Stored XSS. This issue affects Easy Image Display: from n/a through 1.2.5."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en Shellbot Easy Image Display permite XSS almacenado. Este problema afecta a Easy Image Display desde n/d hasta la versión 1.2.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/easy-image-display/vulnerability/wordpress-easy-image-display-plugin-1-2-5-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-58065", "published_date": "2025-03-06T16:15:52.830", "last_modified_date": "2025-03-25T14:37:52.280", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nclk: mmp: pxa1908-apbc: Fix NULL vs IS_ERR() check\n\nThe devm_kzalloc() function returns NULL on error, not error pointers.\nFix the check."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: clk: mmp: pxa1908-apbc: Se ha corregido la comprobación NULL frente a IS_ERR() La función devm_kzalloc() devuelve NULL en caso de error, no punteros de error. Corrija la comprobación."}], "references": [{"url": "https://git.kernel.org/stable/c/6628f7f88de5f65f01adef5a63c707cb49d0fddb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e5ca5d7b4d7c29246d957dc45d63610584ae3a54", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
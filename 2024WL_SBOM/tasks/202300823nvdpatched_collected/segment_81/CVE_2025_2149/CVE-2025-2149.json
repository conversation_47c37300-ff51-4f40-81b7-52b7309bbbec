{"cve_id": "CVE-2025-2149", "published_date": "2025-03-10T13:15:36.290", "last_modified_date": "2025-06-23T18:44:57.017", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PyTorch 2.6.0+cu124. It has been rated as problematic. Affected by this issue is the function nnq_Sigmoid of the component Quantized Sigmoid Module. The manipulation of the argument scale/zero_point leads to improper initialization. The attack needs to be approached locally. The complexity of an attack is rather high. The exploitation is known to be difficult. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PyTorch 2.6.0+cu124. Se ha calificado como problemática. La función nnq_Sigmoid del componente Quantized Sigmoid Module está afectada por este problema. La manipulación del argumento scale/zero_point provoca una inicialización incorrecta. El ataque debe abordarse localmente. Es un ataque de complejidad bastante alta. Se sabe que su explotación es difícil. La explotación se ha hecho pública y puede utilizarse."}], "references": [{"url": "https://github.com/pytorch/pytorch/issues/147818", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/pytorch/pytorch/issues/147818#issue-2877301660", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299060", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299060", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.506563", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry", "Exploit"]}]}
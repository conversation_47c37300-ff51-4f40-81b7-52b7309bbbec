{"cve_id": "CVE-2025-21843", "published_date": "2025-03-07T09:15:17.343", "last_modified_date": "2025-03-13T15:14:57.800", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/panthor: avoid garbage value in panthor_ioctl_dev_query()\n\n'priorities_info' is uninitialized, and the uninitialized value is copied\nto user object when calling PANTHOR_UOBJ_SET(). Using memset to initialize\n'priorities_info' to avoid this garbage value problem."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/panthor: evitar que el valor basura en panthor_ioctl_dev_query() 'priorities_info' no esté inicializado y que el valor no inicializado se copie al objeto de usuario al llamar a PANTHOR_UOBJ_SET(). Se utiliza memset para inicializar 'priorities_info' para evitar este problema de valor basura."}], "references": [{"url": "https://git.kernel.org/stable/c/3b32b7f638fe61e9d29290960172f4e360e38233", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/64b95bbc08bacf3e4b05c8604e6a4fec43bb712a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
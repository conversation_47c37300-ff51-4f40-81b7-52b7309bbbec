{"cve_id": "CVE-2024-58066", "published_date": "2025-03-06T16:15:52.997", "last_modified_date": "2025-03-25T14:43:37.903", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nclk: mmp: pxa1908-apbcp: Fix a NULL vs IS_ERR() check\n\nThe devm_kzalloc() function doesn't return error pointers, it returns\nNULL on error.  Update the check to match."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: clk: mmp: pxa1908-apbcp: Se corrige una comprobación de NULL frente a IS_ERR() La función devm_kzalloc() no devuelve punteros de error, devuelve NULL en caso de error. Actualice la comprobación para que coincida."}], "references": [{"url": "https://git.kernel.org/stable/c/2b3a36fb572caf9fb72f158be328395b1c938bf7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/3acea81be689b77b3ceac6ff345ff0366734d967", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
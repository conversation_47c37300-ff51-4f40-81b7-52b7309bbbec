{"cve_id": "CVE-2025-2051", "published_date": "2025-03-07T01:15:12.963", "last_modified_date": "2025-05-08T18:59:52.633", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Apartment Visitors Management System 1.0 and classified as critical. This vulnerability affects unknown code of the file /search-visitor.php. The manipulation of the argument searchdata leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Apartment Visitors Management System 1.0 y se ha clasificado como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /search-visitor.php. La manipulación del argumento searchdata conduce a una inyección SQL. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/guttlefish/vul/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298804", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298804", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514191", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
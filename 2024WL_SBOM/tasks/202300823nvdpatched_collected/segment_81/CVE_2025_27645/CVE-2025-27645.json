{"cve_id": "CVE-2025-27645", "published_date": "2025-03-05T06:15:36.117", "last_modified_date": "2025-04-15T22:15:21.337", "descriptions": [{"lang": "en", "value": "Vasion Print (formerly PrinterLogic) before Virtual Appliance Host 22.0.933 Application 20.0.2368 allows Insecure Extension Installation by Trusting HTTP Permission Methods on the Server Side V-2024-005."}, {"lang": "es", "value": "Vasion Print (anteriormente PrinterLogic) anterior a Virtual Appliance Host 22.0.933 La aplicación 20.0.2368 permite la instalación de extensiones inseguras al confiar en los métodos de permisos HTTP en el lado del servidor V-2024-005."}], "references": [{"url": "https://help.printerlogic.com/saas/Print/Security/Security-Bulletins.htm", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://pierrekim.github.io/blog/2025-04-08-vasion-printerlogic-83-vulnerabilities.html", "source": "<EMAIL>", "tags": []}]}
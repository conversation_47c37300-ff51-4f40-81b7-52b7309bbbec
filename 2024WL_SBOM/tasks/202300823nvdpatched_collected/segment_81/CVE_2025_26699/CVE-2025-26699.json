{"cve_id": "CVE-2025-26699", "published_date": "2025-03-06T19:15:27.683", "last_modified_date": "2025-03-19T20:15:19.543", "descriptions": [{"lang": "en", "value": "An issue was discovered in Django 5.1 before 5.1.7, 5.0 before 5.0.13, and 4.2 before 4.2.20. The django.utils.text.wrap() method and wordwrap template filter are subject to a potential denial-of-service attack when used with very long strings."}, {"lang": "es", "value": "Se descubrió un problema en Django 5.1 anterior a 5.1.7, 5.0 anterior a 5.0.13 y 4.2 anterior a 4.2.20. El método django.utils.text.wrap() y el filtro de plantilla wordwrap están sujetos a un posible ataque de denegación de servicio cuando se utilizan con cadenas muy largas. "}], "references": [{"url": "https://docs.djangoproject.com/en/dev/releases/security/", "source": "<EMAIL>", "tags": []}, {"url": "https://groups.google.com/g/django-announce", "source": "<EMAIL>", "tags": []}, {"url": "https://www.djangoproject.com/weblog/2025/mar/06/security-releases/", "source": "<EMAIL>", "tags": []}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/06/12", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2025/03/msg00012.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
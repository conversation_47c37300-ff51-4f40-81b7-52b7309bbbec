{"cve_id": "CVE-2025-27597", "published_date": "2025-03-07T16:15:39.893", "last_modified_date": "2025-03-07T18:15:48.163", "descriptions": [{"lang": "en", "value": "Vue I18n is the internationalization plugin for Vue.js. @intlify/message-resolver and @intlify/vue-i18n-core are vulnerable to Prototype Pollution through the entry function: handleFlatJson. An attacker can supply a payload with Object.prototype setter to introduce or modify properties within the global prototype chain, causing denial of service (DoS) a the minimum consequence. Moreover, the consequences of this vulnerability can escalate to other injection-based attacks, depending on how the library integrates within the application. For instance, if the polluted property propagates to sensitive Node.js APIs (e.g., exec, eval), it could enable an attacker to execute arbitrary commands within the application's context."}, {"lang": "es", "value": "Vue I18n es el complemento de internacionalización para Vue.js. @intlify/message-resolver y @intlify/vue-i18n-core son vulnerables a la contaminación de prototipos a través de la función de entrada: handleFlatJson. Un atacante puede proporcionar un payload con el definidor Object.prototype para introducir o modificar propiedades dentro de la cadena de prototipos global, lo que provoca una denegación de servicio (DoS) como consecuencia mínima. Además, las consecuencias de esta vulnerabilidad pueden escalar a otros ataques basados en inyección, según cómo se integre la biblioteca dentro de la aplicación. Por ejemplo, si la propiedad contaminada se propaga a las API de Node.js sensibles (por ejemplo, exec, eval), podría permitir que un atacante ejecute comandos arbitrarios dentro del contexto de la aplicación."}], "references": [{"url": "https://github.com/intlify/vue-i18n/commit/4bb6eacda7fc2cde5687549afa0efb27ca40862a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/intlify/vue-i18n/security/advisories/GHSA-p2ph-7g93-hw3m", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-56192", "published_date": "2025-03-10T21:15:40.007", "last_modified_date": "2025-06-27T16:12:02.787", "descriptions": [{"lang": "en", "value": "In wl_notify_gscan_event of wl_cfgscan.c, there is a possible out of bounds write due to a missing bounds check. This could lead to local escalation of privilege with no additional execution privileges needed. User interaction is not needed for exploitation."}, {"lang": "es", "value": "En wl_notify_gscan_event de wl_cfgscan.c, existe una posible escritura fuera de los límites debido a una verificación de los límites faltante. Esto podría provocar una escalada local de privilegios sin necesidad de permisos de ejecución adicionales. No se necesita interacción del usuario para la explotación."}], "references": [{"url": "https://source.android.com/docs/security/bulletin/pixel-watch/2025/2025-03-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
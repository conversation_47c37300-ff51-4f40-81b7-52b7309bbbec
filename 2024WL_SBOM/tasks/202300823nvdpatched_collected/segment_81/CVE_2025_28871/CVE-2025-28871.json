{"cve_id": "CVE-2025-28871", "published_date": "2025-03-11T21:15:44.600", "last_modified_date": "2025-03-17T18:45:11.073", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in jwpegram Block Spam By Math Reloaded allows Stored XSS. This issue affects Block Spam By Math Reloaded: from n/a through 2.2.4."}, {"lang": "es", "value": "La vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en jwpegram, Block Spam de Math Reloaded, permite XSS almacenado. Este problema afecta a Block Spam de Math Reloaded desde n/d hasta la versión 2.2.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/block-spam-by-math-reloaded/vulnerability/wordpress-block-spam-by-math-reloaded-plugin-2-2-4-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
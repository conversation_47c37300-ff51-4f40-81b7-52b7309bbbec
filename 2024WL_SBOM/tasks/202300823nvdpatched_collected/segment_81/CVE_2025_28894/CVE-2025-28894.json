{"cve_id": "CVE-2025-28894", "published_date": "2025-03-11T21:15:46.723", "last_modified_date": "2025-03-11T21:15:46.723", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in frucomerci List of Posts from each Category plugin for WordPress allows Stored XSS. This issue affects List of Posts from each Category plugin for WordPress: from n/a through 2.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en el complemento frucomerci List of Posts from each Category para WordPress permite XSS almacenado. Este problema afecta al plugin \"Lista de entradas de cada categoría\" para WordPress desde la versión n/d hasta la 2.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/list-posts-by-category/vulnerability/wordpress-list-of-posts-from-each-category-plugin-for-wordpress-plugin-2-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1287", "published_date": "2025-03-08T09:15:31.590", "last_modified_date": "2025-03-24T18:19:22.993", "descriptions": [{"lang": "en", "value": "The The Plus Addons for Elementor – Elementor Addons, Page Templates, Widgets, Mega Menu, WooCommerce plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Countdown, Syntax Highlighter, and Page Scroll widgets in all versions up to, and including, 6.2.2 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento The Plus Addons for Elementor – <PERSON><PERSON><PERSON>, Page Templates, Widgets, Mega Menu, WooCommerce para WordPress es vulnerable a cross site scripting almacenado a través de los widgets Countdown, Syntax Highlighter y Page Scroll en todas las versiones hasta la 6.2.2 incluida debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/the-plus-addons-for-elementor-page-builder/tags/6.2.0/modules/widgets/tp_countdown.php#L1868", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/the-plus-addons-for-elementor-page-builder/tags/6.2.0/modules/widgets/tp_page_scroll.php#L1015", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/the-plus-addons-for-elementor-page-builder/tags/6.2.0/modules/widgets/tp_syntax_highlighter.php#L1043", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3252092/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fbf86da7-621d-4fb7-ba16-d132db5b602a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
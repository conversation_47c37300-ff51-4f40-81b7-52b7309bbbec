{"cve_id": "CVE-2025-27363", "published_date": "2025-03-11T14:15:25.427", "last_modified_date": "2025-05-07T16:00:55.577", "descriptions": [{"lang": "en", "value": "An out of bounds write exists in FreeType versions 2.13.0 and below (newer versions of FreeType are not vulnerable) when attempting to parse font subglyph structures related to TrueType GX and variable font files. The vulnerable code assigns a signed short value to an unsigned long and then adds a static value causing it to wrap around and allocate too small of a heap buffer. The code then writes up to 6 signed long integers out of bounds relative to this buffer. This may result in arbitrary code execution. This vulnerability may have been exploited in the wild."}, {"lang": "es", "value": "Existe una escritura fuera de los límites en las versiones 2.13.0 y anteriores de FreeType al intentar analizar estructuras de subglifos de fuentes relacionadas con archivos de fuentes TrueType GX y variables. El código vulnerable asigna un valor short con signo a un long sin signo y luego añade un valor estático, lo que provoca un bucle y asigna un búfer de montón demasiado pequeño. El código escribe entonces hasta 6 enteros long con signo fuera de los límites en relación con este búfer. Esto puede provocar la ejecución de código arbitrario. Esta vulnerabilidad podría haber sido explotada in situ."}], "references": [{"url": "https://www.facebook.com/security/advisories/cve-2025-27363", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/13/1", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/13/11", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/13/12", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/13/2", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/13/3", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/13/8", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/14/1", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/14/2", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/14/3", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/14/4", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/05/06/3", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "https://lists.debian.org/debian-lts-announce/2025/03/msg00030.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List"]}, {"url": "https://source.android.com/docs/security/bulletin/2025-05-01", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Third Party Advisory"]}]}
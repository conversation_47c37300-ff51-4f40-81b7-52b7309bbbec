{"cve_id": "CVE-2025-1008", "published_date": "2025-03-05T09:15:10.110", "last_modified_date": "2025-03-05T09:15:10.110", "descriptions": [{"lang": "en", "value": "The Recently Purchased Products For Woo plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘view’ parameter in all versions up to, and including, 1.1.3 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Recently Purchased Products For Woo para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del parámetro \"view\" en todas las versiones hasta la 1.1.3 incluida, debido a una depuración de entrada insuficiente y al escape de salida. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/recently-purchased-products-for-woo/tags/1.1.3/includes/class-rppw-public.php#L160", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/recently-purchased-products-for-woo/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c9ebcd32-90c1-419c-a67c-6fe41ee9fab1?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2176", "published_date": "2025-03-11T08:15:12.090", "last_modified_date": "2025-03-11T08:15:12.090", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in libzvbi up to 0.2.43. This affects the function vbi_capture_sim_load_caption of the file src/io-sim.c. The manipulation leads to integer overflow. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. Upgrading to version 0.2.44 is able to address this issue. The identifier of the patch is ca1672134b3e2962cd392212c73f44f8f4cb489f. It is recommended to upgrade the affected component. The code maintainer was informed beforehand about the issues. She reacted very fast and highly professional."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en libzvbi hasta la versión 0.2.43. Afecta a la función vbi_capture_sim_load_caption del archivo src/io-sim.c. La manipulación provoca un desbordamiento de enteros. Es posible iniciar el ataque de forma remota. El exploit se ha hecho público y puede utilizarse. La actualización a la versión 0.2.44 puede solucionar este problema. El identificador del parche es ca1672134b3e2962cd392212c73f44f8f4cb489f. Se recomienda actualizar el componente afectado. La responsable del código fue informada de antemano sobre los problemas. Reaccionó muy rápido y con gran profesionalidad."}], "references": [{"url": "https://github.com/zapping-vbi/zvbi/commit/ca1672134b3e2962cd392212c73f44f8f4cb489f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/releases/tag/v0.2.44", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/security/advisories/GHSA-g7cg-7gw9-v8cf", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299205", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299205", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.512802", "source": "<EMAIL>", "tags": []}]}
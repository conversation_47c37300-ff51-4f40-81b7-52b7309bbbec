{"cve_id": "CVE-2025-1945", "published_date": "2025-03-10T12:15:12.450", "last_modified_date": "2025-03-19T16:14:37.237", "descriptions": [{"lang": "en", "value": "picklescan before 0.0.23 fails to detect malicious pickle files inside PyTorch model archives when certain ZIP file flag bits are modified. By flipping specific bits in the ZIP file headers, an attacker can embed malicious pickle files that remain undetected by PickleScan while still being successfully loaded by PyTorch's torch.load(). This can lead to arbitrary code execution when loading a compromised model."}, {"lang": "es", "value": "Las versiones anteriores a la versión 0.0.23 de picklescan no detectan archivos pickle maliciosos dentro de los archivos de modelos de PyTorch cuando se modifican ciertos bits de indicadores de archivos ZIP. Al invertir bits específicos en los encabezados de archivos ZIP, un atacante puede incrustar archivos pickle maliciosos que PickleScan no detecta, pero que se cargan correctamente con la función Torch.load() de PyTorch. Esto puede provocar la ejecución de código arbitrario al cargar un modelo comprometido."}], "references": [{"url": "https://github.com/mmaitre314/picklescan/commit/e58e45e0d9e091159c1554f9b04828bbb40b9781", "source": "103e4ec9-0a87-450b-af77-479448ddef11", "tags": ["Patch"]}, {"url": "https://github.com/mmaitre314/picklescan/security/advisories/GHSA-w8jq-xcqf-f792", "source": "103e4ec9-0a87-450b-af77-479448ddef11", "tags": ["Exploit", "Vendor Advisory"]}, {"url": "https://sites.google.com/sonatype.com/vulnerabilities/cve-2025-1945", "source": "103e4ec9-0a87-450b-af77-479448ddef11", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2025-1696", "published_date": "2025-03-06T12:15:36.293", "last_modified_date": "2025-03-06T12:15:36.293", "descriptions": [{"lang": "en", "value": "A vulnerability exists in Docker Desktop prior to version 4.39.0 that could lead to the unintentional disclosure of sensitive information via application logs. In affected versions, proxy configuration data—potentially including sensitive details—was written to log files in clear text whenever an HTTP GET request was made through a proxy. An attacker with read access to these logs could obtain the proxy information and leverage it for further attacks or unauthorized access. Starting with version 4.39.0, Docker Desktop no longer logs the proxy string, thereby mitigating this risk."}, {"lang": "es", "value": "Existe una vulnerabilidad en Docker Desktop anterior a la versión 4.39.0 que podría provocar la divulgación involuntaria de información confidencial a través de los registros de la aplicación. En las versiones afectadas, los datos de configuración del proxy (que podrían incluir detalles confidenciales) se escribían en archivos de registro en texto plano cada vez que se realizaba una solicitud HTTP GET a través de un proxy. Un atacante con acceso de lectura a estos registros podría obtener la información del proxy y aprovecharla para realizar más ataques o para obtener acceso no autorizado. A partir de la versión 4.39.0, Docker Desktop ya no registra la cadena de proxy, lo que mitiga este riesgo."}], "references": [{"url": "https://docs.docker.com/desktop/settings-and-maintenance/settings/#proxies", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.docker.com/desktop/troubleshoot-and-support/troubleshoot/#check-the-logs", "source": "<EMAIL>", "tags": []}]}
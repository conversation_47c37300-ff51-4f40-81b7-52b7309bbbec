{"cve_id": "CVE-2025-28861", "published_date": "2025-03-11T21:15:43.337", "last_modified_date": "2025-03-19T14:08:16.920", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in bhzad WP jQuery Persian Datepicker allows Stored XSS. This issue affects WP jQuery Persian Datepicker: from n/a through 0.1.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en bhzad WP jQuery Persian Datepicker permite XSS almacenado. Este problema afecta a WP jQuery Persian Datepicker desde n/d hasta la versión 0.1.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wpjqp-datepicker/vulnerability/wordpress-wp-jquery-persian-datepicker-plugin-0-1-0-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
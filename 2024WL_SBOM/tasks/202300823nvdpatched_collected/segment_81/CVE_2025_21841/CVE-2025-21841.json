{"cve_id": "CVE-2025-21841", "published_date": "2025-03-07T09:15:17.137", "last_modified_date": "2025-03-07T09:15:17.137", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncpufreq/amd-pstate: Fix cpufreq_policy ref counting\n\namd_pstate_update_limits() takes a cpufreq_policy reference but doesn't\ndecrement the refcount in one of the exit paths, fix that."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: cpufreq/amd-pstate: Se corrige el recuento de referencias de cpufreq_policy. amd_pstate_update_limits() toma una referencia cpufreq_policy pero no disminuye el recuento de referencias en una de las rutas de salida. Corrija eso."}], "references": [{"url": "https://git.kernel.org/stable/c/28e4c515cf644c621800bd97841757fd49891ba4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/3ace20038e19f23fe73259513f1f08d4bf1a3c83", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/56e6976793c0fcf1638aa534242408ab4e4ca705", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
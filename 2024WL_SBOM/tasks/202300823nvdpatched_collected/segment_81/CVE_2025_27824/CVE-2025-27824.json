{"cve_id": "CVE-2025-27824", "published_date": "2025-03-07T22:15:38.220", "last_modified_date": "2025-03-07T22:15:38.220", "descriptions": [{"lang": "en", "value": "An XSS issue was discovered in the Link iframe formatter module before 1.x-1.1.1 for Backdrop CMS. It doesn't sufficiently sanitize input before displaying results to the screen. This vulnerability is mitigated by the fact that an attacker must have the ability to create content containing an iFrame field."}, {"lang": "es", "value": "Se descubrió un problema de XSS en el módulo formateador de iframe de enlaces anterior a la versión 1.x-1.1.1 para Background CMS. No depura lo suficiente la entrada antes de mostrar los resultados en la pantalla. Esta vulnerabilidad se mitiga por el hecho de que un atacante debe tener la capacidad de crear contenido que contenga un campo iFrame."}], "references": [{"url": "https://backdropcms.org/security/backdrop-sa-contrib-2025-003", "source": "<EMAIL>", "tags": []}]}
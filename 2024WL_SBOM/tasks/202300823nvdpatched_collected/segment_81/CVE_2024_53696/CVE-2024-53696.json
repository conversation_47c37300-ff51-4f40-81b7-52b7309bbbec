{"cve_id": "CVE-2024-53696", "published_date": "2025-03-07T17:15:20.390", "last_modified_date": "2025-03-07T17:15:20.390", "descriptions": [{"lang": "en", "value": "A server-side request forgery (SSRF) vulnerability has been reported to affect QuLog Center. If exploited, the vulnerability could allow remote attackers who have gained administrator access to read application data.\n\nWe have already fixed the vulnerability in the following versions:\nQuLog Center 1.7.0.829 ( 2024/10/01 ) and later\nQuLog Center 1.8.0.888 ( 2024/10/15 ) and later\nQTS 4.5.4.2957 build 20241119 and later\nQuTS hero h4.5.4.2956 build 20241119 and later"}, {"lang": "es", "value": "Se ha informado de una vulnerabilidad de server-side request forgery (SSRF) que afecta a QuLog Center. Si se explota, la vulnerabilidad podría permitir a atacantes remotos que hayan obtenido acceso de administrador leer datos de la aplicación. Ya hemos corregido la vulnerabilidad en las siguientes versiones: QuLog Center 1.7.0.829 (2024/10/01) y posteriores QuLog Center 1.8.0.888 (2024/10/15) y posteriores QTS 4.5.4.2957 compilación 20241119 y posteriores QuTS hero h4.5.4.2956 compilación 20241119 y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-24-53", "source": "<EMAIL>", "tags": []}]}
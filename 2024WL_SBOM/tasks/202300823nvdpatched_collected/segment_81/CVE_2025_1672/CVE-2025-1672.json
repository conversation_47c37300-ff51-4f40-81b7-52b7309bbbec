{"cve_id": "CVE-2025-1672", "published_date": "2025-03-06T10:15:11.897", "last_modified_date": "2025-03-06T10:15:11.897", "descriptions": [{"lang": "en", "value": "The Notibar – Notification Bar for WordPress plugin for WordPress is vulnerable to Stored Cross-Site Scripting via admin settings in all versions up to, and including, 2.1.5 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled."}, {"lang": "es", "value": "El complemento Notibar – Notification Bar for WordPress para WordPress es vulnerable a Cross-Site Scripting almacenados a través de la configuración de administrador en todas las versiones hasta la 2.1.5 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes autenticados, con permisos de nivel de administrador y superiores, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. Esto solo afecta a instalaciones multisitio e instalaciones en las que se ha deshabilitado unfiltered_html."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3246799/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/notibar/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9985627d-9ba4-4a5b-94fb-06bcc769acfd?source=cve", "source": "<EMAIL>", "tags": []}]}
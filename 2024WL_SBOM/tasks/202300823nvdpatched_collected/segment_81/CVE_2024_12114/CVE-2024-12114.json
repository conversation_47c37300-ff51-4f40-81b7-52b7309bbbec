{"cve_id": "CVE-2024-12114", "published_date": "2025-03-08T06:15:35.103", "last_modified_date": "2025-03-12T16:24:29.630", "descriptions": [{"lang": "en", "value": "The FooGallery – Responsive Photo Gallery, Image Viewer, Justified, Masonry & Carousel plugin for WordPress is vulnerable to Insecure Direct Object Reference in all versions up to, and including, 2.4.29 via the foogallery_attachment_modal_save AJAX action due to missing validation on a user controlled key (img_id). This makes it possible for authenticated attackers, with granted access and above, to update arbitrary post and page content. This requires the Gallery Creator Role setting to be a value lower than 'Editor' for there to be any real impact."}, {"lang": "es", "value": "El complemento FooGallery – Responsive Photo Gallery, Image Viewer, Justified, Masonry &amp; Carousel para WordPress es vulnerable a la referencia directa a objetos inseguros en todas las versiones hasta la 2.4.29 incluida a través de la acción AJAX foogallery_attachment_modal_save debido a la falta de validación en una clave controlada por el usuario (img_id). Esto hace posible que atacantes autenticados, con acceso otorgado o superior, actualicen contenido arbitrario de publicaciones y páginas. Esto requiere que la configuración del rol de creador de la galería sea un valor inferior a \"Editor\" para que haya un impacto real."}], "references": [{"url": "https://github.com/fooplugins/foogallery/blob/master/includes/admin/class-gallery-attachment-modal.php#L242", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3250684/foogallery/tags/2.4.30/includes/admin/class-gallery-attachment-modal.php?old=3229839&old_path=foogallery%2Ftags%2F2.4.29%2Fincludes%2Fadmin%2Fclass-gallery-attachment-modal.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f4fe3ad9-247f-4e5d-8c79-0970afaa7729?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
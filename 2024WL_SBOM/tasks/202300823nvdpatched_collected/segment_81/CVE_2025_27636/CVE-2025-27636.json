{"cve_id": "CVE-2025-27636", "published_date": "2025-03-09T13:15:34.403", "last_modified_date": "2025-06-23T18:54:52.400", "descriptions": [{"lang": "en", "value": "Bypass/Injection vulnerability in Apache Camel components under particular conditions.\n\nThis issue affects Apache Camel: from 4.10.0 through <= 4.10.1, from 4.8.0 through <= 4.8.4, from 3.10.0 through <= 3.22.3.\n\nUsers are recommended to upgrade to version 4.10.2 for 4.10.x LTS, 4.8.5 for 4.8.x LTS and 3.22.4 for 3.x releases.\n\n\n\nThis vulnerability is present in Camel's default incoming header filter, that allows an attacker to include Camel specific\n\nheaders that for some Camel components can alter the behaviours such as the camel-bean component, to call another method\n\non the bean, than was coded in the application. In the camel-jms component, then a malicious header can be used to send\n\nthe message to another queue (on the same broker) than was coded in the application. This could also be seen by using the camel-exec component\n\n\n\n\nThe attacker would need to inject custom headers, such as HTTP protocols. So if you have Camel applications that are\n\ndirectly connected to the internet via HTTP, then an attacker could include malicious HTTP headers in the HTTP requests\n\nthat are send to the Camel application.\n\n\n\n\nAll the known Camel HTTP component such as camel-servlet, camel-jetty, camel-undertow, camel-platform-http, and camel-netty-http would be vulnerable out of the box.\n\nIn these conditions an attacker could be able to forge a Camel header name and make the bean component invoking other methods in the same bean.\n\nIn terms of usage of the default header filter strategy the list of components using that is: \n\n\n  *  camel-activemq\n  *  camel-activemq6\n  *  camel-amqp\n  *  camel-aws2-sqs\n  *  camel-azure-servicebus\n  *  camel-cxf-rest\n  *  camel-cxf-soap\n  *  camel-http\n  *  camel-jetty\n  *  camel-jms\n  *  camel-kafka\n  *  camel-knative\n  *  camel-mail\n  *  camel-nats\n  *  camel-netty-http\n  *  camel-platform-http\n  *  camel-rest\n  *  camel-sjms\n  *  camel-spring-rabbitmq\n  *  camel-stomp\n  *  camel-tahu\n  *  camel-undertow\n  *  camel-xmpp\n\n\n\n\n\n\nThe vulnerability arises due to a bug in the default filtering mechanism that only blocks headers starting with \"Camel\", \"camel\", or \"org.apache.camel.\". \n\n\nMitigation: You can easily work around this in your Camel applications by removing the headers in your Camel routes. There are many ways of doing this, also globally or per route. This means you could use the removeHeaders EIP, to filter out anything like \"cAmel, cAMEL\" etc, or in general everything not starting with \"Camel\", \"camel\" or \"org.apache.camel.\"."}, {"lang": "es", "value": "Vulnerabilidad de bypass/inyección en el componente Apache Camel-Bean en determinadas condiciones. Este problema afecta a Apache Camel: desde la versión 4.10.0 hasta la &lt;= 4.10.1, desde la versión 4.8.0 hasta la &lt;= 4.8.4, desde la versión 3.10.0 hasta la &lt;= 3.22.3. Se recomienda a los usuarios que actualicen a la versión 4.10.2 para 4.10.x LTS, 4.8.5 para 4.8.x LTS y 3.22.4 para las versiones 3.x. Esta vulnerabilidad solo está presente en la siguiente situación. El usuario está utilizando uno de los siguientes servidores HTTP a través de uno de los siguientes componentes Camel * camel-servlet * camel-jetty * camel-undertow * camel-platform-http * camel-netty-http y en la ruta, el intercambio se enrutará a un productor de camel-bean. Por lo tanto, SOLO el componente camel-bean está afectado. En particular: * La invocación del bean (solo se ve afectada si usas cualquiera de los anteriores junto con el componente camel-bean). * El bean que se puede llamar tiene más de 1 método implementado. En estas condiciones, un atacante podría falsificar un nombre de encabezado de Camel y hacer que el componente bean invoque otros métodos en el mismo bean. La vulnerabilidad surge debido a un error en el mecanismo de filtrado predeterminado que solo bloquea los encabezados que comienzan con \"Camel\", \"camel\" u \"org.apache.camel\". Mitigación: puedes solucionar esto fácilmente en tus aplicaciones Camel eliminando los encabezados en tus rutas Camel. Hay muchas formas de hacer esto, también globalmente o por ruta. Esto significa que puedes usar el EIP removeHeaders para filtrar cualquier cosa como \"cAmel, cAMEL\", etc., o en general todo lo que no comience con \"Camel\", \"camel\" u \"org.apache.camel\"."}], "references": [{"url": "https://camel.apache.org/security/CVE-2025-27636.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://issues.apache.org/jira/browse/CAMEL-21828", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://lists.apache.org/thread/l3zcg3vts88bmc7w8172wkgw610y693z", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/09/1", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://camel.apache.org/security/CVE-2025-27636.txt.asc", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit"]}, {"url": "https://github.com/akamai/CVE-2025-27636-Apache-Camel-PoC/blob/main/src/main/java/com/example/camel/VulnerableCamel.java", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Product"]}]}
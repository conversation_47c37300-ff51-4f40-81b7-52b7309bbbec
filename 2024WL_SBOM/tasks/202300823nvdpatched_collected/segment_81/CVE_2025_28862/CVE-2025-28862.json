{"cve_id": "CVE-2025-28862", "published_date": "2025-03-11T21:15:43.490", "last_modified_date": "2025-03-19T14:13:16.610", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Venugopal Comment Date and Gravatar remover allows Cross Site Request Forgery. This issue affects Comment Date and Gravatar remover: from n/a through 1.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Venugopal Comment Date and Gravatar remover permite Cross-Site Request Forgery. Este problema afecta al eliminador de comentarios, fechas y Gravatar desde n/d hasta la versión 1.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/remove-date-and-gravatar-under-comment/vulnerability/wordpress-comment-date-and-gravatar-remover-plugin-1-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2024-58074", "published_date": "2025-03-06T16:15:53.943", "last_modified_date": "2025-03-06T16:15:53.943", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/i915: Grab intel_display from the encoder to avoid potential oopsies\n\n<PERSON>rab the intel_display from 'encoder' rather than 'state'\nin the encoder hooks to avoid the massive footgun that is\nintel_sanitize_encoder(), which passes NULL as the 'state'\nargument to encoder .disable() and .post_disable().\n\nTODO: figure out how to actually fix intel_sanitize_encoder()..."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/i915: Tomar intel_display del codificador para evitar posibles errores <PERSON><PERSON> intel_display de 'encoder' en lugar de 'state' en los ganchos del codificador para evitar el enorme problema que es intel_sanitize_encoder(), que pasa NULL como el argumento 'state' a .disable() y .post_disable() del codificador. TODO: averiguar cómo solucionar realmente intel_sanitize_encoder()..."}], "references": [{"url": "https://git.kernel.org/stable/c/1885401569f24eb35c631bcc4e6543360dbe9292", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/dc3806d9eb66d0105f8d55d462d4ef681d9eac59", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-27509", "published_date": "2025-03-06T19:15:27.973", "last_modified_date": "2025-03-06T19:15:27.973", "descriptions": [{"lang": "en", "value": "fleetdm/fleet is an open source device management, built on osquery. In vulnerable versions of Fleet, an attacker could craft a specially-formed SAML response to forge authentication assertions, provision a new administrative user account if Just-In-Time (JIT) provisioning is enabled, or create new accounts tied to forged assertions if f MDM enrollment is enabled. This vulnerability is fixed in 4.64.2, 4.63.2, 4.62.4, and 4.58.1."}, {"lang": "es", "value": "fleedm/fleet es un sistema de gestión de dispositivos de código abierto, basado en osquery. En versiones vulnerables de Fleet, un atacante podría crear una respuesta SAML especialmente manipulada para falsificar las afirmaciones de autenticación, proporcionar una nueva cuenta de usuario administrativo si el aprovisionamiento Just-In-Time (JIT) está habilitado o crear nuevas cuentas vinculadas a afirmaciones falsificadas si la inscripción a MDM está habilitada. Esta vulnerabilidad se ha corregido en 4.64.2, 4.63.2, 4.62.4 y 4.58.1."}], "references": [{"url": "https://github.com/fleetdm/fleet/commit/718c95e47ad010ad6b8ceb3f3460e921fbfc53bb", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/fleetdm/fleet/security/advisories/GHSA-52jx-g6m5-h735", "source": "<EMAIL>", "tags": []}]}
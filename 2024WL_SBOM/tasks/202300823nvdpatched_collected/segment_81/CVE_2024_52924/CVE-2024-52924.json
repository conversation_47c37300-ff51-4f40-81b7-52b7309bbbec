{"cve_id": "CVE-2024-52924", "published_date": "2025-03-06T18:15:41.320", "last_modified_date": "2025-07-01T15:01:58.440", "descriptions": [{"lang": "en", "value": "An issue was discovered in NRMM in Samsung Mobile Processor, Wearable Processor, and Modem Exynos 9820, 9825, 980, 990, 850, 1080, 2100, 1280, 2200, 1330, 1380, 1480, 2400, 9110, W920, W930, W1000, Modem 5123, Modem 5300, and Modem 5400. Lack of boundary check during the decoding of Registration Accept messages can lead to out-of-bounds writes on the stack"}, {"lang": "es", "value": "Se descubrió un problema en NRMM en Samsung Mobile Processor, Wearable Processor y Modem Exynos 9820, 9825, 980, 990, 850, 1080, 2100, 1280, 2200, 1330, 1380, 1480, 2400, 9110, W920, W930, W1000, los módems 5123, 5300 y 5400. La falta de verificación de los límites durante la decodificación de los mensajes de aceptación de registro puede provocar escrituras fuera de los límites en la pila."}], "references": [{"url": "https://semiconductor.samsung.com/support/quality-support/product-security-updates/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
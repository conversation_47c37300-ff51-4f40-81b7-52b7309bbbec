{"cve_id": "CVE-2025-2195", "published_date": "2025-03-11T14:15:27.937", "last_modified_date": "2025-04-09T20:45:17.083", "descriptions": [{"lang": "en", "value": "A vulnerability was found in MRCMS 3.1.2. It has been classified as problematic. Affected is the function rename of the file /admin/file/rename.do of the component org.marker.mushroom.controller.FileController. The manipulation of the argument name/path leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en MRCMS 3.1.2. Se ha clasificado como problemática. La función \"rename\" del archivo /admin/file/rename.do del componente org.marker.mushroom.controller.FileController se ve afectada. La manipulación del argumento name/path provoca ataques de cross site scripting. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299220", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299220", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511733", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
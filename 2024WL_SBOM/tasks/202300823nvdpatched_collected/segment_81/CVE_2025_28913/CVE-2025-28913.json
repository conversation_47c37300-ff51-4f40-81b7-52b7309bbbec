{"cve_id": "CVE-2025-28913", "published_date": "2025-03-11T21:15:49.010", "last_modified_date": "2025-03-11T21:15:49.010", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Aftab Ali Muni WP Add Active Class To Menu Item allows Cross Site Request Forgery. This issue affects WP Add Active Class To Menu Item: from n/a through 1.0."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Aftab Ali Muni WP Add Active Class To Menu Item permite Cross-Site Request Forgery. Este problema afecta a WP Add Active Class To Menu Item desde n/d hasta la versión 1.0."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-add-active-class-to-menu-item/vulnerability/wordpress-wp-add-active-class-to-menu-item-plugin-1-0-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
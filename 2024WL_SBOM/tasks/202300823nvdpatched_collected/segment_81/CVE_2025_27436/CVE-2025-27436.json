{"cve_id": "CVE-2025-27436", "published_date": "2025-03-11T01:15:36.920", "last_modified_date": "2025-03-11T01:15:36.920", "descriptions": [{"lang": "en", "value": "The Manage Bank Statements in SAP S/4HANA does not perform required access control checks for an authenticated user to confirm whether a request to interact with a resource is legitimate, allowing the attacker to delete the attachment of a posted bank statement. This leads to a low impact on integrity, with no impact on the confidentiality of the data or the availability of the application."}, {"lang": "es", "value": "La función de gestión de extractos bancarios en SAP S/4HANA no realiza las comprobaciones de control de acceso necesarias para que un usuario autenticado confirme si una solicitud de interacción con un recurso es legítima, lo que permite al atacante eliminar el archivo adjunto de un extracto bancario publicado. Esto genera un impacto bajo en la integridad, sin impacto en la confidencialidad de los datos o la disponibilidad de la aplicación."}], "references": [{"url": "https://me.sap.com/notes/3565835", "source": "<EMAIL>", "tags": []}, {"url": "https://url.sap/sapsecuritypatchday", "source": "<EMAIL>", "tags": []}]}
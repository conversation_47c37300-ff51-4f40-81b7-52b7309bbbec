{"cve_id": "CVE-2024-42733", "published_date": "2025-03-07T21:15:17.080", "last_modified_date": "2025-06-23T19:40:09.470", "descriptions": [{"lang": "en", "value": "An issue in Docmosis Tornado v.2.9.7 and before allows a remote attacker to execute arbitrary code via a crafted script to the UNC path input"}, {"lang": "es", "value": "Un problema en Docmosis Tornado v.2.9.7 y anteriores permite que un atacante remoto ejecute código arbitrario a través de un script manipulado específicamente para la entrada de la ruta UNC."}], "references": [{"url": "https://github.com/Docmosis/tornado-docker/issues/14", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/Marsman1996/pocs/blob/master/redox/CVE-2024-57492/README.md", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}
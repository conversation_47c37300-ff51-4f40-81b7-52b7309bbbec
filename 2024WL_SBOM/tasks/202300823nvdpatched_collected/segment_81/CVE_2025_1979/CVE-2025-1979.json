{"cve_id": "CVE-2025-1979", "published_date": "2025-03-06T05:15:16.213", "last_modified_date": "2025-03-06T16:15:54.187", "descriptions": [{"lang": "en", "value": "Versions of the package ray before 2.43.0 are vulnerable to Insertion of Sensitive Information into Log File where the redis password is being logged in the standard logging. If the redis password is passed as an argument, it will be logged and could potentially leak the password.\r\rThis is only exploitable if:\r\r1) Logging is enabled;\r\r2) Redis is using password authentication;\r\r3) Those logs are accessible to an attacker, who can reach that redis instance.\r\r**Note:**\r\rIt is recommended that anyone who is running in this configuration should update to the latest version of Ray, then rotate their redis password."}, {"lang": "es", "value": "Las versiones del paquete ray anteriores a la 2.43.0 son vulnerables a la inserción de información confidencial en el archivo de registro donde se registra la contraseña de Redis en el registro estándar. Si la contraseña de Redis se pasa como argumento, se registrará y podría filtrarse la contraseña. Esto solo se puede explotar si: 1) el registro está habilitado; 2) Redis está utilizando la autenticación de contraseña; 3) esos registros son accesibles para un atacante, que puede llegar a esa instancia de Redis. **Nota:** Se recomienda que cualquier persona que esté ejecutando esta configuración actualice a la última versión de Ray y luego rote su contraseña de Redis."}], "references": [{"url": "https://github.com/ray-project/ray/commit/64a2e4010522d60b90c389634f24df77b603d85d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ray-project/ray/issues/50266", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ray-project/ray/pull/50409", "source": "<EMAIL>", "tags": []}, {"url": "https://security.snyk.io/vuln/SNYK-PYTHON-RAY-8745212", "source": "<EMAIL>", "tags": []}]}
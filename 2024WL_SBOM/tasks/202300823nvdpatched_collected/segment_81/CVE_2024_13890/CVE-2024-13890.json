{"cve_id": "CVE-2024-13890", "published_date": "2025-03-08T03:15:36.920", "last_modified_date": "2025-03-12T16:48:41.693", "descriptions": [{"lang": "en", "value": "The Allow PHP Execute plugin for WordPress is vulnerable to PHP Code Injection in all versions up to, and including, 1.0. This is due to allowing PHP code to be entered by all users for whom unfiltered HTML is allowed. This makes it possible for authenticated attackers, with Editor-level access and above, to inject PHP code into posts and pages."}, {"lang": "es", "value": "El complemento Allow PHP Execute para WordPress es vulnerable a la inyección de código PHP en todas las versiones hasta la 1.0 incluida. Esto se debe a que permite que todos los usuarios a los que se les permite HTML sin filtrar ingresen código PHP. Esto hace posible que atacantes autenticados, con acceso de nivel de editor o superior, inyecten código PHP en publicaciones y páginas."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/allow-php-execute/trunk/allow-php-execute.php#L10", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/412c39e9-9378-4c2c-817c-8d37f156af6e?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
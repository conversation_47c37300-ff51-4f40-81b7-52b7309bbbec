{"cve_id": "CVE-2025-27513", "published_date": "2025-03-05T19:15:39.337", "last_modified_date": "2025-03-05T19:15:39.337", "descriptions": [{"lang": "en", "value": "OpenTelemetry dotnet is a dotnet telemetry framework. A vulnerability in OpenTelemetry.Api package 1.10.0 to 1.11.1 could cause a Denial of Service (DoS) when a tracestate and traceparent header is received. Even if an application does not explicitly use trace context propagation, receiving these headers can still trigger high CPU usage. This issue impacts any application accessible over the web or backend services that process HTTP requests containing a tracestate header. Application may experience excessive resource consumption, leading to increased latency, degraded performance, or downtime. This vulnerability is fixed in 1.11.2."}, {"lang": "es", "value": "OpenTelemetry dotnet es un framework de telemetría de dotnet. Una vulnerabilidad en el paquete OpenTelemetry.Api 1.10.0 a 1.11.1 podría provocar una denegación de servicio (DoS) cuando se recibe un encabezado tracestate y traceparent. Incluso si una aplicación no utiliza explícitamente la propagación del contexto de seguimiento, la recepción de estos encabezados puede provocar un alto uso de la CPU. Este problema afecta a cualquier aplicación accesible a través de la web o servicios de backend que procesen solicitudes HTTP que contengan un encabezado tracestate. La aplicación puede experimentar un consumo excesivo de recursos, lo que genera mayor latencia, rendimiento degradado o tiempo de inactividad. Esta vulnerabilidad se solucionó en 1.11.2."}], "references": [{"url": "https://github.com/open-telemetry/opentelemetry-dotnet/commit/1b555c1201413f2f55f2cd3c4ba03ef4b615b6b5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/open-telemetry/opentelemetry-dotnet/security/advisories/GHSA-8785-wc3w-h8q6", "source": "<EMAIL>", "tags": []}]}
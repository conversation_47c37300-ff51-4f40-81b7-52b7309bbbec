{"cve_id": "CVE-2025-2065", "published_date": "2025-03-07T04:15:10.447", "last_modified_date": "2025-05-14T16:15:06.770", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in projectworlds Life Insurance Management System 1.0. This affects an unknown part of the file /editAgent.php. The manipulation of the argument agent_id leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en projectworlds Life Insurance Management System 1.0. Afecta a una parte desconocida del archivo /editAgent.php. La manipulación del argumento agent_id provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298821", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298821", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514758", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2024-13919", "published_date": "2025-03-10T10:15:13.890", "last_modified_date": "2025-03-24T14:14:53.337", "descriptions": [{"lang": "en", "value": "The Laravel framework versions between 11.9.0 and 11.35.1 are susceptible to reflected cross-site scripting due to an improper encoding of route parameters in the debug-mode error page."}, {"lang": "es", "value": "Las versiones del framework Laravel entre 11.9.0 y 11.35.1 son susceptibles a cross-site scripting reflejado debido a una codificación incorrecta de los parámetros de ruta en la página de error del modo de depuración."}], "references": [{"url": "https://github.com/laravel/framework/pull/53869", "source": "1e3a9e0f-5156-4bf8-b8a3-cc311bfc0f4a", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/laravel/framework/releases/tag/v11.36.0", "source": "1e3a9e0f-5156-4bf8-b8a3-cc311bfc0f4a", "tags": ["Release Notes"]}, {"url": "https://github.com/sbaresearch/advisories/tree/public/2024/SBA-ADV-20241209-02_<PERSON><PERSON>_Reflected_XSS_via_Route_Parameter_in_Debug-Mode_Error_Page", "source": "1e3a9e0f-5156-4bf8-b8a3-cc311bfc0f4a", "tags": ["Exploit"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/10/4", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory", "Exploit"]}]}
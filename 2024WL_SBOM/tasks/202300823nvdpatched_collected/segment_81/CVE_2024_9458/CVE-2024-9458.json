{"cve_id": "CVE-2024-9458", "published_date": "2025-03-07T10:15:16.497", "last_modified_date": "2025-05-07T16:50:52.970", "descriptions": [{"lang": "en", "value": "The Reservit Hotel WordPress plugin before 3.0 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Reservit Hotel de WordPress anterior a la versión 3.0 no depura ni escapa a algunas de sus configuraciones, lo que podría permitir que usuarios con privilegios elevados, como el administrador, realicen ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/1157d6ae-af8b-4508-97e9-b9e86f612550/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
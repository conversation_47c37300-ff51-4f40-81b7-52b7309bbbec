{"cve_id": "CVE-2025-27615", "published_date": "2025-03-10T19:15:40.917", "last_modified_date": "2025-03-10T19:15:40.917", "descriptions": [{"lang": "en", "value": "umatiGateway is software for connecting OPC Unified Architecture servers with an MQTT broker utilizing JSON messages. The user interface may possibly be publicly accessible with umatiGateway's provided docker-compose file. With this access, the configuration can be viewed and altered. Commit 5d81a3412bc0051754a3095d89a06d6d743f2b16 uses `127.0.0.1:8080:8080` to limit access to the local network. For those who are unable to use this proposed patch, a firewall on Port 8080 may block remote access, but the workaround may not be perfect because Docker may also bypass a firewall by its iptable based rules for port forwarding."}, {"lang": "es", "value": "umatiGateway es un software para conectar servidores de arquitectura unificada OPC con un agente MQTT utilizando mensajes JSON. La interfaz de usuario puede ser de acceso público con el archivo docker-compose proporcionado por umatiGateway. Con este acceso, la configuración puede verse y modificarse. El commit 5d81a3412bc0051754a3095d89a06d6d743f2b16 utiliza `127.0.0.1:8080:8080` para limitar el acceso a la red local. Para aquellos que no pueden usar este parche propuesto, un firewall en el puerto 8080 puede bloquear el acceso remoto, pero el workaround puede no ser perfecta porque Docker también puede eludir un firewall mediante sus reglas basadas en iptable para el reenvío de puertos."}], "references": [{"url": "https://github.com/umati/umatiGateway/blob/abe73096a17307327f0d6dc0ed4db1fb93464521/README.md?plain=1#L34-L35", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/umati/umatiGateway/commit/5d81a3412bc0051754a3095d89a06d6d743f2b16", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/umati/umatiGateway/pull/101", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/umati/umatiGateway/security/advisories/GHSA-qf9w-x9qx-2mq7", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-0956", "published_date": "2025-03-05T10:15:19.480", "last_modified_date": "2025-03-05T10:15:19.480", "descriptions": [{"lang": "en", "value": "The WooCommerce Recover Abandoned Cart plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 24.3.0 via deserialization of untrusted input from the 'raccookie_guest_email' cookie. This makes it possible for unauthenticated attackers to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present."}, {"lang": "es", "value": "El complemento WooCommerce Recover Abandoned Cart para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 24.3.0 incluida, a través de la deserialización de la entrada no confiable de la cookie 'raccookie_guest_email'. Esto hace posible que atacantes no autenticados inyecten un objeto PHP. No hay ninguna cadena POP conocida presente en el software vulnerable, lo que significa que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si hay una cadena POP presente a través de un complemento o tema adicional instalado en el sistema de destino, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código según la cadena POP presente."}], "references": [{"url": "https://codecanyon.net/item/woocommerce-recover-abandoned-cart/7715167", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/067c974c-b3bb-4f06-8f7c-3963112c40d2?source=cve", "source": "<EMAIL>", "tags": []}]}
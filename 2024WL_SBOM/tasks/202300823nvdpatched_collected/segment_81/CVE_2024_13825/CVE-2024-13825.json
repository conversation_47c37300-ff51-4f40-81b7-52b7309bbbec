{"cve_id": "CVE-2024-13825", "published_date": "2025-03-08T06:15:36.560", "last_modified_date": "2025-05-06T16:17:38.650", "descriptions": [{"lang": "en", "value": "The Email Keep WordPress plugin through 1.1 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin."}, {"lang": "es", "value": "El complemento Email Keep de WordPress hasta la versión 1.1 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios altos, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/368474a0-550d-49f8-855d-b2010f8b91b5/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
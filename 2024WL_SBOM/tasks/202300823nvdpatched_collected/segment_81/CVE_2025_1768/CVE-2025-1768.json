{"cve_id": "CVE-2025-1768", "published_date": "2025-03-07T11:15:15.653", "last_modified_date": "2025-04-09T20:52:13.823", "descriptions": [{"lang": "en", "value": "The SEO Plugin by <PERSON><PERSON><PERSON>ly SEO plugin for WordPress is vulnerable to blind SQL Injection via the 'search' parameter in all versions up to, and including, 12.4.05 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento SEO de Squirrly para WordPress es vulnerable a la inyección SQL a ciegas a través del parámetro 'search' en todas las versiones hasta la 12.4.05 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel de suscriptor y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/controllers/Assistant.php?rev=3207037#L55", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/controllers/Audits.php?rev=3207037#L86", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/controllers/BulkSeo.php?rev=3207037#L148", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/controllers/FocusPages.php?rev=3207037#L107", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/controllers/Onboarding.php?rev=3207037#L62", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/controllers/Post.php?rev=3207037#L480", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/models/Snippet.php?rev=3207037#L118", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/squirrly-seo/trunk/models/Snippet.php?rev=3207037#L96", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3248412/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3250395/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/squirrly-seo/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1a23ee5c-275f-4d51-8199-1cc2b0086f73?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
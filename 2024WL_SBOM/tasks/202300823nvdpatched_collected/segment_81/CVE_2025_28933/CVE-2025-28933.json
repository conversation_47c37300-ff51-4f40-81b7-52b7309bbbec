{"cve_id": "CVE-2025-28933", "published_date": "2025-03-11T21:15:51.290", "last_modified_date": "2025-03-11T21:15:51.290", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in maxfoundry MaxA/B allows Stored XSS. This issue affects MaxA/B: from n/a through 2.2.2."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en maxfoundry MaxA/B que permite XSS almacenado. Este problema afecta a MaxA/B desde n/d hasta la versión 2.2.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/maxab/vulnerability/wordpress-maxa-b-plugin-2-2-2-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
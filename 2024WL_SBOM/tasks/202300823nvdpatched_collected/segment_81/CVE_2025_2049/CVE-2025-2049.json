{"cve_id": "CVE-2025-2049", "published_date": "2025-03-06T23:15:12.523", "last_modified_date": "2025-04-03T13:33:00.487", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic has been found in code-projects Blood Bank System 1.0. Affected is an unknown function of the file AB+.php. The manipulation of the argument Bloodname leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en code-projects Blood Bank System 1.0. Se ve afectada una función desconocida del archivo AB+.php. La manipulación del argumento Bloodname provoca ataques de cross site scripting. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/ABC-YOLO/cve/blob/main/xss45.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298800", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298800", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514089", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-28902", "published_date": "2025-03-11T21:15:47.627", "last_modified_date": "2025-03-11T21:15:47.627", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Benjamin Pick Contact Form 7 Select Box Editor <PERSON><PERSON> allows Cross Site Request Forgery. This issue affects Contact Form 7 Select Box Editor But<PERSON>: from n/a through 0.6."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Benjamin Pick Contact Form 7 Select Box Editor Button permite Cross-Site Request Forgery. Este problema afecta al botón del editor de selección de Contact Form 7 desde n/d hasta la versión 0.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/contact-form-7-select-box-editor-button/vulnerability/wordpress-contact-form-7-select-box-editor-button-plugin-0-6-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-58056", "published_date": "2025-03-06T16:15:51.837", "last_modified_date": "2025-03-06T16:15:51.837", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nremoteproc: core: Fix ida_free call while not allocated\n\nIn the rproc_alloc() function, on error, put_device(&rproc->dev) is\ncalled, leading to the call of the rproc_type_release() function.\nAn error can occurs before ida_alloc is called.\n\nIn such case in rproc_type_release(), the condition (rproc->index >= 0) is\ntrue as rproc->index has been  initialized to 0.\nida_free() is called reporting a warning:\n[    4.181906] WARNING: CPU: 1 PID: 24 at lib/idr.c:525 ida_free+0x100/0x164\n[    4.186378] stm32-display-dsi 5a000000.dsi: Fixed dependency cycle(s) with /soc/dsi@5a000000/panel@0\n[    4.188854] ida_free called for id=0 which is not allocated.\n[    4.198256] mipi-dsi 5a000000.dsi.0: Fixed dependency cycle(s) with /soc/dsi@5a000000\n[    4.203556] Modules linked in: panel_orisetech_otm8009a dw_mipi_dsi_stm(+) gpu_sched dw_mipi_dsi stm32_rproc stm32_crc32 stm32_ipcc(+) optee(+)\n[    4.224307] CPU: 1 UID: 0 PID: 24 Comm: kworker/u10:0 Not tainted 6.12.0 #442\n[    4.231481] Hardware name: STM32 (Device Tree Support)\n[    4.236627] Workqueue: events_unbound deferred_probe_work_func\n[    4.242504] Call trace:\n[    4.242522]  unwind_backtrace from show_stack+0x10/0x14\n[    4.250218]  show_stack from dump_stack_lvl+0x50/0x64\n[    4.255274]  dump_stack_lvl from __warn+0x80/0x12c\n[    4.260134]  __warn from warn_slowpath_fmt+0x114/0x188\n[    4.265199]  warn_slowpath_fmt from ida_free+0x100/0x164\n[    4.270565]  ida_free from rproc_type_release+0x38/0x60\n[    4.275832]  rproc_type_release from device_release+0x30/0xa0\n[    4.281601]  device_release from kobject_put+0xc4/0x294\n[    4.286762]  kobject_put from rproc_alloc.part.0+0x208/0x28c\n[    4.292430]  rproc_alloc.part.0 from devm_rproc_alloc+0x80/0xc4\n[    4.298393]  devm_rproc_alloc from stm32_rproc_probe+0xd0/0x844 [stm32_rproc]\n[    4.305575]  stm32_rproc_probe [stm32_rproc] from platform_probe+0x5c/0xbc\n\nCalling ida_alloc earlier in rproc_alloc ensures that the rproc->index is\nproperly set."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: remoteproc: core: Se corrige en ida_free call while not allocated In the rproc_alloc() function, on error, put_device(&amp;rproc-&gt;dev) is called, leading to the call of the rproc_type_release() function. An error can occurs before ida_alloc is called. In such case in rproc_type_release(), the condition (rproc-&gt;index &gt;= 0) is true as rproc-&gt;index has been initialized to 0. ida_free() is called reporting a warning: [ 4.181906] WARNING: CPU: 1 PID: 24 at lib/idr.c:525 ida_free+0x100/0x164 [ 4.186378] stm32-display-dsi 5a000000.dsi: Fixed dependency cycle(s) with /soc/dsi@5a000000/panel@0 [ 4.188854] ida_free called for id=0 which is not allocated. [ 4.198256] mipi-dsi 5a000000.dsi.0: Fixed dependency cycle(s) with /soc/dsi@5a000000 [ 4.203556] Modules linked in: panel_orisetech_otm8009a dw_mipi_dsi_stm(+) gpu_sched dw_mipi_dsi stm32_rproc stm32_crc32 stm32_ipcc(+) optee(+) [ 4.224307] CPU: 1 UID: 0 PID: 24 Comm: kworker/u10:0 Not tainted 6.12.0 #442 [ 4.231481] Hardware name: STM32 (Device Tree Support) [ 4.236627] Workqueue: events_unbound deferred_probe_work_func [ 4.242504] Call trace: [ 4.242522] unwind_backtrace from show_stack+0x10/0x14 [ 4.250218] show_stack from dump_stack_lvl+0x50/0x64 [ 4.255274] dump_stack_lvl from __warn+0x80/0x12c [ 4.260134] __warn from warn_slowpath_fmt+0x114/0x188 [ 4.265199] warn_slowpath_fmt from ida_free+0x100/0x164 [ 4.270565] ida_free from rproc_type_release+0x38/0x60 [ 4.275832] rproc_type_release from device_release+0x30/0xa0 [ 4.281601] device_release from kobject_put+0xc4/0x294 [ 4.286762] kobject_put from rproc_alloc.part.0+0x208/0x28c [ 4.292430] rproc_alloc.part.0 from devm_rproc_alloc+0x80/0xc4 [ 4.298393] devm_rproc_alloc from stm32_rproc_probe+0xd0/0x844 [stm32_rproc] [ 4.305575] stm32_rproc_probe [stm32_rproc] from platform_probe+0x5c/0xbc Llamar a ida_alloc antes en rproc_alloc garantiza que rproc-&gt;index está configurado correctamente."}], "references": [{"url": "https://git.kernel.org/stable/c/2cf54928e7e32362215c69b68a6a53d110323bf3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7378aeb664e5ebc396950b36a1f2dedf5aabec20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b32d60a852bb3952886625d0c3b1c9a88c3ceb7c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e9efd9fa4679803fe23188d7b47119cf7bc2de6f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f2013d19b7704cd723ab42664b8d9408ea8cc77c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
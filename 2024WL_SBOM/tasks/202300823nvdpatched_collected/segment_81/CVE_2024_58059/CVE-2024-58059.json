{"cve_id": "CVE-2024-58059", "published_date": "2025-03-06T16:15:52.140", "last_modified_date": "2025-03-25T14:35:53.557", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: uvcvideo: Fix deadlock during uvc_probe\n\nIf uvc_probe() fails, it can end up calling uvc_status_unregister() before\nuvc_status_init() is called.\n\nFix this by checking if dev->status is NULL or not in\nuvc_status_unregister()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: uvcvideo: Se solucionó el bloqueo durante uvc_probe Si uvc_probe() falla, puede terminar llamando a uvc_status_unregister() antes de que se llame a uvc_status_init(). Solucione esto verificando si dev-&gt;status es NULL o no en uvc_status_unregister()."}], "references": [{"url": "https://git.kernel.org/stable/c/a67f75c2b5ecf534eab416ce16c11fe780c4f8f6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/db577ededf3a18b39567fc1a6209f12a0c4a3c52", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
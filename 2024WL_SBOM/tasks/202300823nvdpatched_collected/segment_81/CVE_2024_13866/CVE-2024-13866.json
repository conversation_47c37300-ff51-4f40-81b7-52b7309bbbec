{"cve_id": "CVE-2024-13866", "published_date": "2025-03-05T09:15:09.587", "last_modified_date": "2025-03-05T09:15:09.587", "descriptions": [{"lang": "en", "value": "The Simple Notification plugin for WordPress is vulnerable to Stored Cross-Site Scripting in all versions up to, and including, 1.3 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level access, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled."}, {"lang": "es", "value": "El complemento Simple Notification para WordPress es vulnerable a Cross-Site Scripting Almacenado en todas las versiones hasta la 1.3 incluida, debido a una depuración de entrada insuficiente y al escape de salida. Esto permite que atacantes autenticados, con acceso de nivel de administrador, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. Esto solo afecta a instalaciones multisitio e instalaciones en las que se ha deshabilitado unfiltered_html."}], "references": [{"url": "https://wordpress.org/plugins/simple-notification/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e814f798-5ebc-4bea-838f-d0a803f9bdbc?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-54085", "published_date": "2025-03-11T14:15:22.893", "last_modified_date": "2025-06-27T16:57:49.563", "descriptions": [{"lang": "en", "value": "AMI’s SPx contains\na vulnerability in the BMC where an Attacker may bypass authentication remotely through the Redfish Host Interface. A successful exploitation\nof this vulnerability may lead to a loss of confidentiality, integrity, and/or\navailability."}, {"lang": "es", "value": "El SPx de AMI contiene una vulnerabilidad en el BMC que permite a un atacante eludir la autenticación remotamente a través de la interfaz de host de Redfish. Una explotación exitosa de esta vulnerabilidad puede provocar la pérdida de confidencialidad, integridad o disponibilidad."}], "references": [{"url": "https://go.ami.com/hubfs/Security%20Advisories/2025/AMI-SA-2025003.pdf", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://arstechnica.com/security/2025/06/active-exploitation-of-ami-management-tool-imperils-thousands-of-servers/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Press/Media Coverage", "Third Party Advisory"]}, {"url": "https://eclypsium.com/blog/bmc-vulnerability-cve-2024-05485-cisa-known-exploited-vulnerabilities/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Press/Media Coverage", "Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20250328-0003/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Third Party Advisory"]}, {"url": "https://www.bleepingcomputer.com/news/security/cisa-ami-megarac-bug-that-lets-hackers-brick-servers-now-actively-exploited/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Press/Media Coverage", "Third Party Advisory"]}, {"url": "https://www.networkworld.com/article/4013368/ami-megarac-authentication-bypass-flaw-is-being-exploitated-cisa-warns.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Press/Media Coverage", "Third Party Advisory"]}, {"url": "https://nvd.nist.gov/vuln/detail/CVE-2024-54085", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["US Government Resource"]}]}
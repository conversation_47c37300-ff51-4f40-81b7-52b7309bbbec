{"cve_id": "CVE-2025-2020", "published_date": "2025-03-11T21:15:53.477", "last_modified_date": "2025-07-07T18:06:35.070", "descriptions": [{"lang": "en", "value": "Ashlar-Vellum Cobalt VC6 File Parsing Out-Of-Bounds Write Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Ashlar-Vellum Cobalt. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of VC6 files. The issue results from the lack of proper validation of user-supplied data, which can result in a write past the end of an allocated buffer. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-25254."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código por escritura fuera de los límites en el análisis de archivos VC6 de Ashlar-Vellum Cobalt. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Ashlar-Vellum Cobalt. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos VC6. El problema se debe a la falta de una validación adecuada de los datos proporcionados por el usuario, lo que puede provocar una escritura más allá del límite del búfer asignado. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Anteriormente, se denominó ZDI-CAN-25254."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-124/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
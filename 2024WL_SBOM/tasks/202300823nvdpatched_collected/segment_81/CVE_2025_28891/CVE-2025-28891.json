{"cve_id": "CVE-2025-28891", "published_date": "2025-03-11T21:15:46.420", "last_modified_date": "2025-03-11T21:15:46.420", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in jazzigor price-calc allows Stored XSS. This issue affects price-calc: from n/a through 0.6.3."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en jazzigor price-calc permite XSS almacenado. Este problema afecta a price-calc desde n/d hasta la versión 0.6.3."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/price-calc/vulnerability/wordpress-price-calc-plugin-0-6-3-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27257", "published_date": "2025-03-10T09:15:11.613", "last_modified_date": "2025-03-12T12:15:15.187", "descriptions": [{"lang": "en", "value": "Insufficient Verification of Data Authenticity vulnerability in GE Vernova UR IED family devices allows an authenticated user to install a modified firmware.\nThe firmware signature verification is enforced only on the client-side dedicated software Enervista UR Setup, allowing the integration check to be bypassed."}, {"lang": "es", "value": "La vulnerabilidad de verificación insuficiente de la autenticidad de los datos en los dispositivos de la familia GE Vernova UR IED permite que un usuario autenticado instale un firmware modificado. La verificación de la firma del firmware se aplica únicamente en el software dedicado del lado del cliente Enervista UR Setup, lo que permite omitir la comprobación de integración."}], "references": [{"url": "https://www.gevernova.com/grid-solutions/app/DownloadFile.aspx?prod=urfamily&type=21&file=76", "source": "<EMAIL>", "tags": []}, {"url": "https://www.nozominetworks.com/labs/vulnerability-advisories-cve-2025-27257", "source": "<EMAIL>", "tags": []}]}
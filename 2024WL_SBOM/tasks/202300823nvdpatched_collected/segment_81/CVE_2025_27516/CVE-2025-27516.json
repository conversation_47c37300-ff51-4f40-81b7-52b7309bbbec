{"cve_id": "CVE-2025-27516", "published_date": "2025-03-05T21:15:20.073", "last_modified_date": "2025-05-01T01:15:53.237", "descriptions": [{"lang": "en", "value": "Jinja is an extensible templating engine. Prior to 3.1.6, an oversight in how the Jinja sandboxed environment interacts with the |attr filter allows an attacker that controls the content of a template to execute arbitrary Python code. To exploit the vulnerability, an attacker needs to control the content of a template. Whether that is the case depends on the type of application using Jinja. This vulnerability impacts users of applications which execute untrusted templates. <PERSON><PERSON>'s sandbox does catch calls to str.format and ensures they don't escape the sandbox. However, it's possible to use the |attr filter to get a reference to a string's plain format method, bypassing the sandbox. After the fix, the |attr filter no longer bypasses the environment's attribute lookup. This vulnerability is fixed in 3.1.6."}, {"lang": "es", "value": "Jinja es un motor de plantillas extensible. Antes de la versión 3.1.6, un descuido en la forma en que el entorno aislado de Jinja interactúa con el filtro |attr permite que un atacante que controla el contenido de una plantilla ejecute código Python arbitrario. Para explotar la vulnerabilidad, un atacante debe controlar el contenido de una plantilla. Si ese es el caso depende del tipo de aplicación que utilice Jinja. Esta vulnerabilidad afecta a los usuarios de aplicaciones que ejecutan plantillas no confiables. El entorno aislado de Jinja detecta las llamadas a str.format y garantiza que no escapen del entorno aislado. Sin embargo, es posible utilizar el filtro |attr para obtener una referencia al método de formato simple de una cadena, sin pasar por el entorno aislado. Después de la corrección, el filtro |attr ya no pasa por alto la búsqueda de atributos del entorno. Esta vulnerabilidad se corrigió en la versión 3.1.6."}], "references": [{"url": "https://github.com/pallets/jinja/commit/90457bbf33b8662926ae65cdde4c4c32e756e403", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pallets/jinja/security/advisories/GHSA-cpwx-vrp4-4pq7", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.debian.org/debian-lts-announce/2025/04/msg00045.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}
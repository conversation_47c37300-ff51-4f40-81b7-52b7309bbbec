{"cve_id": "CVE-2025-2112", "published_date": "2025-03-08T22:15:36.443", "last_modified_date": "2025-06-27T16:25:36.963", "descriptions": [{"lang": "en", "value": "A vulnerability was found in user-xiangpeng yaoqishan up to a47fec4a31cbd13698c592dfdc938c8824dd25e4. It has been declared as critical. Affected by this vulnerability is the function getMediaLisByFilter of the file cn/javaex/yaoqishan/service/media_info/MediaInfoService.java. The manipulation of the argument typeId leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en el usuario-xiangpeng yaoqi<PERSON> hasta a47fec4a31cbd13698c592dfdc938c8824dd25e4. Se ha declarado como crítica. Esta vulnerabilidad afecta a la función getMediaLisByFilter del archivo cn/javaex/yaoqishan/service/media_info/MediaInfoService.java. La manipulación del argumento typeId conduce a una inyección SQL. El ataque se puede ejecutar de forma remota. El exploit se ha divulgado al público y puede utilizarse. Este producto adopta el enfoque de lanzamientos continuos para proporcionar una entrega continua. Por lo tanto, los detalles de las versiones afectadas y actualizadas no están disponibles. Se contactó al proveedor con anticipación sobre esta divulgación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/xiaolian-11/code_demo/blob/main/yaoqishan-sql.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.299005", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299005", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.506085", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
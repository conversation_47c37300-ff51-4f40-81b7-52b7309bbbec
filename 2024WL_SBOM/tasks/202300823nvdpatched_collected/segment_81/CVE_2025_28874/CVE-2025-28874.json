{"cve_id": "CVE-2025-28874", "published_date": "2025-03-11T21:15:44.907", "last_modified_date": "2025-04-09T13:17:09.637", "descriptions": [{"lang": "en", "value": "Authorization Bypass Through User-Controlled Key vulnerability in shanebp BP Email Assign Templates allows Exploiting Incorrectly Configured Access Control Security Levels. This issue affects BP Email Assign Templates: from n/a through 1.6."}, {"lang": "es", "value": "La vulnerabilidad de omisión de autorización mediante clave controlada por el usuario en shanebp BP Email Assign Templates permite explotar niveles de seguridad de control de acceso configurados incorrectamente. Este problema afecta a las plantillas de asignación de correo electrónico de BP desde n/d hasta la versión 1.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/bp-email-assign-templates/vulnerability/wordpress-bp-email-assign-templates-by-shanebp-plugin-1-6-arbitrary-content-deletion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
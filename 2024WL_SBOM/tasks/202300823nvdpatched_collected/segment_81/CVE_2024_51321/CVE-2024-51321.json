{"cve_id": "CVE-2024-51321", "published_date": "2025-03-11T15:15:42.570", "last_modified_date": "2025-05-28T14:48:50.617", "descriptions": [{"lang": "en", "value": "In Zucchetti Ad Hoc Infinity 2.4, an improper check on the m_cURL parameter allows an attacker to redirect the victim to an attacker-controlled website after the authentication."}, {"lang": "es", "value": "En Zucchetti Ad Hoc Infinity 2.4, una verificación incorrecta del parámetro m_cURL permite a un atacante redirigir a la víctima a un sitio web controlado por el atacante después de la autenticación."}], "references": [{"url": "https://members.backbox.org/zucchetti-ad-hoc-infinity-multiple-vulnerabilities/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
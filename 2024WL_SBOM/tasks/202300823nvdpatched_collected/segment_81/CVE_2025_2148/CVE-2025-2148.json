{"cve_id": "CVE-2025-2148", "published_date": "2025-03-10T12:15:12.617", "last_modified_date": "2025-06-23T18:47:34.860", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PyTorch 2.6.0+cu124. It has been declared as critical. Affected by this vulnerability is the function torch.ops.profiler._call_end_callbacks_on_jit_fut of the component Tuple Handler. The manipulation of the argument None leads to memory corruption. The attack can be launched remotely. The complexity of an attack is rather high. The exploitation appears to be difficult."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PyTorch 2.6.0+cu124. Se ha declarado como crítica. Esta vulnerabilidad afecta a la función Torch.ops.profiler._call_end_callbacks_on_jit_fut del componente Tuple Handler. La manipulación del argumento None provoca la corrupción de la memoria. El ataque se puede lanzar de forma remota. Es un ataque de complejidad bastante alta. Parece difícil de explotar."}], "references": [{"url": "https://github.com/pytorch/pytorch/issues/147722", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299059", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299059", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.505959", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
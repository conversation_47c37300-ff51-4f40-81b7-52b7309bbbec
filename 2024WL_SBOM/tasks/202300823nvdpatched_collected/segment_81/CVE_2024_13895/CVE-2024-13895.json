{"cve_id": "CVE-2024-13895", "published_date": "2025-03-08T03:15:37.073", "last_modified_date": "2025-03-12T16:44:26.183", "descriptions": [{"lang": "en", "value": "The The Code Snippets CPT plugin for WordPress is vulnerable to arbitrary shortcode execution in all versions up to, and including, 2.1.0. This is due to the software allowing users to execute an action that does not properly validate a value before running do_shortcode. This makes it possible for authenticated attackers, with Subscriber-level access and above, to execute arbitrary shortcodes."}, {"lang": "es", "value": "El complemento CPT The Code Snippets para WordPress es vulnerable a la ejecución arbitraria de códigos cortos en todas las versiones hasta la 2.1.0 incluida. Esto se debe a que el software permite a los usuarios ejecutar una acción que no valida correctamente un valor antes de ejecutar do_shortcode. Esto hace posible que atacantes autenticados, con acceso de nivel de suscriptor y superior, ejecuten códigos cortos arbitrarios."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/code-snippets-cpt/trunk/lib/CodeSnippitButton.php#L201", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/65f521f4-1968-4c43-a3f0-b0f81632d7aa?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-28860", "published_date": "2025-03-11T21:15:43.193", "last_modified_date": "2025-03-19T14:06:00.333", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in PPDPurveyor Google News Editors Picks Feed Generator allows Stored XSS. This issue affects Google News Editors Picks Feed Generator: from n/a through 2.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en PPDPurveyor Google News Editors Picks Feed Generator permite XSS almacenado. Este problema afecta al generador de feeds de Selecciones del editor de Google News: desde n/d hasta la versión 2.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/google-news-editors-picks-news-feeds/vulnerability/wordpress-google-news-editors-picks-feed-generator-plugin-2-1-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
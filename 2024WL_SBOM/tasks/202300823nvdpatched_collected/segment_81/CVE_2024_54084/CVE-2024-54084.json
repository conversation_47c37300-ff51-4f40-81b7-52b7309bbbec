{"cve_id": "CVE-2024-54084", "published_date": "2025-03-11T14:15:22.730", "last_modified_date": "2025-03-11T14:15:22.730", "descriptions": [{"lang": "en", "value": "APTIOV contains a vulnerability in BIOS where an attacker may cause a Time-of-check Time-of-use (TOCTOU) Race Condition by local means. Successful exploitation of this vulnerability may lead to arbitrary code execution."}, {"lang": "es", "value": "APTIOV contiene una vulnerabilidad en la BIOS que permite a un atacante provocar una condición de ejecución de tiempo de verificación y tiempo de uso (TOCTOU) localmente. La explotación exitosa de esta vulnerabilidad puede provocar la ejecución de código arbitrario."}], "references": [{"url": "https://go.ami.com/hubfs/Security%20Advisories/2025/AMI-SA-2025003.pdf", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2054", "published_date": "2025-03-07T02:15:38.060", "last_modified_date": "2025-05-21T18:49:28.633", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Blood Bank Management System 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /admin/edit_state.php. The manipulation of the argument state_id leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en code-projects Blood Bank Management System 1.0. Se ha declarado como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/edit_state.php. La manipulación del argumento state_id conduce a una inyección SQL. El ataque se puede ejecutar de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/intercpt/XSS1/blob/main/SQL6.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298807", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298807", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514346", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
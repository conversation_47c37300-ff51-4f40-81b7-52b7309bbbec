{"cve_id": "CVE-2025-21829", "published_date": "2025-03-06T17:15:22.820", "last_modified_date": "2025-03-06T17:15:22.820", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nRDMA/rxe: Fix the warning \"__rxe_cleanup+0x12c/0x170 [rdma_rxe]\"\n\nThe Call Trace is as below:\n\"\n  <TASK>\n  ? show_regs.cold+0x1a/0x1f\n  ? __rxe_cleanup+0x12c/0x170 [rdma_rxe]\n  ? __warn+0x84/0xd0\n  ? __rxe_cleanup+0x12c/0x170 [rdma_rxe]\n  ? report_bug+0x105/0x180\n  ? handle_bug+0x46/0x80\n  ? exc_invalid_op+0x19/0x70\n  ? asm_exc_invalid_op+0x1b/0x20\n  ? __rxe_cleanup+0x12c/0x170 [rdma_rxe]\n  ? __rxe_cleanup+0x124/0x170 [rdma_rxe]\n  rxe_destroy_qp.cold+0x24/0x29 [rdma_rxe]\n  ib_destroy_qp_user+0x118/0x190 [ib_core]\n  rdma_destroy_qp.cold+0x43/0x5e [rdma_cm]\n  rtrs_cq_qp_destroy.cold+0x1d/0x2b [rtrs_core]\n  rtrs_srv_close_work.cold+0x1b/0x31 [rtrs_server]\n  process_one_work+0x21d/0x3f0\n  worker_thread+0x4a/0x3c0\n  ? process_one_work+0x3f0/0x3f0\n  kthread+0xf0/0x120\n  ? kthread_complete_and_exit+0x20/0x20\n  ret_from_fork+0x22/0x30\n  </TASK>\n\"\nWhen too many rdma resources are allocated, rxe needs more time to\nhandle these rdma resources. Sometimes with the current timeout, rxe\ncan not release the rdma resources correctly.\n\nCompared with other rdma drivers, a bigger timeout is used."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: RDMA/rxe: Se corrige la advertencia \"__rxe_cleanup+0x12c/0x170 [rdma_rxe]\" El seguimiento de llamadas es el siguiente: \"  ? show_regs.cold+0x1a/0x1f ? __rxe_cleanup+0x12c/0x170 [rdma_rxe] ? __warn+0x84/0xd0 ? __rxe_cleanup+0x12c/0x170 [rdma_rxe] ? report_bug+0x105/0x180 ? handle_bug+0x46/0x80 ? exc_invalid_op+0x19/0x70 ? asm_exc_invalid_op+0x1b/0x20 ? __rxe_cleanup+0x12c/0x170 [rdma_rxe] ? __rxe_cleanup+0x124/0x170 [rdma_rxe] rxe_destroy_qp.cold+0x24/0x29 [rdma_rxe] ib_destroy_qp_user+0x118/0x190 [ib_core] rdma_destroy_qp.cold+0x43/0x5e [rdma_cm] rtrs_cq_qp_destroy.cold+0x1d/0x2b [rtrs_core] rtrs_srv_close_work.cold+0x1b/0x31 [rtrs_server] process_one_work+0x21d/0x3f0 worker_thread+0x4a/0x3c0 ? process_one_work+0x3f0/0x3f0 kthread+0xf0/0x120 ? kthread_complete_and_exit+0x20/0x20 ret_from_fork+0x22/0x30  \"Cuando se asignan demasiados recursos rdma, rxe necesita más tiempo para gestionar estos recursos rdma. A veces, con el tiempo de espera actual, rxe no puede liberar los recursos rdma correctamente. En comparación con otros controladores rdma, se utiliza un tiempo de espera mayor."}], "references": [{"url": "https://git.kernel.org/stable/c/45e567800492088bc52c9abac35524b4d332a8f8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/720653309dd31c8a927ef5d87964578ad544980f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7a2de8126ed3801f2396720e10a03cd546a3cea1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a7d15eaecf0d6e13226db629ae2401c8c02683e5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/edc4ef0e0154096d6c0cf5e06af6fc330dbad9d1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
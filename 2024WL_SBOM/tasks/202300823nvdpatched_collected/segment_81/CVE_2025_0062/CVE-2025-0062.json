{"cve_id": "CVE-2025-0062", "published_date": "2025-03-11T01:15:33.740", "last_modified_date": "2025-03-11T01:15:33.740", "descriptions": [{"lang": "en", "value": "SAP BusinessObjects Business Intelligence Platform allows an attacker to inject JavaScript code in Web Intelligence reports. This code is then executed in the victim's browser each time the vulnerable page is visited by the victim. On successful exploitation, an attacker could cause limited impact on confidentiality and integrity within the scope of victim�s browser. There is no impact on availability. This vulnerability occurs only when script/html execution is enabled by the administrator in Central Management Console."}, {"lang": "es", "value": "SAP BusinessObjects Business Intelligence Platform permite a un atacante inyectar código <PERSON>Script en los informes de Web Intelligence. Este código se ejecuta en el navegador de la víctima cada vez que esta visita la página vulnerable. Si se logra explotar esta vulnerabilidad, un atacante podría causar un impacto limitado en la confidencialidad e integridad dentro del alcance del navegador de la víctima. No hay impacto en la disponibilidad. Esta vulnerabilidad ocurre solo cuando el administrador habilita la ejecución de scripts/html en la consola de administración central."}], "references": [{"url": "https://me.sap.com/notes/3557459", "source": "<EMAIL>", "tags": []}, {"url": "https://url.sap/sapsecuritypatchday", "source": "<EMAIL>", "tags": []}]}
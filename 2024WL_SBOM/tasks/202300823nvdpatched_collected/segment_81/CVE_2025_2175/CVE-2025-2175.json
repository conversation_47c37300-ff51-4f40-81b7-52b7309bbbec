{"cve_id": "CVE-2025-2175", "published_date": "2025-03-11T07:15:37.440", "last_modified_date": "2025-03-11T07:15:37.440", "descriptions": [{"lang": "en", "value": "A vulnerability was found in libzvbi up to 0.2.43. It has been rated as problematic. Affected by this issue is the function _vbi_strndup_iconv. The manipulation leads to integer overflow. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. Upgrading to version 0.2.44 is able to address this issue. It is recommended to upgrade the affected component. The code maintainer was informed beforehand about the issues. She reacted very fast and highly professional."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en libzvbi hasta la versión 0.2.43. Se ha calificado como problemática. La función _vbi_strndup_iconv está afectada por este problema. La manipulación provoca un desbordamiento de enteros. El ataque puede ejecutarse de forma remota. El exploit se ha hecho público y puede utilizarse. La actualización a la versión 0.2.44 puede solucionar este problema. Se recomienda actualizar el componente afectado. La responsable del código fue informada de antemano sobre los problemas. Reaccionó muy rápido y con gran profesionalidad."}], "references": [{"url": "https://github.com/zapping-vbi/zvbi/releases/tag/v0.2.44", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/security/advisories/GHSA-g7cg-7gw9-v8cf", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299204", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299204", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.512801", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-58075", "published_date": "2025-03-06T16:15:54.040", "last_modified_date": "2025-03-06T16:15:54.040", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ncrypto: tegra - do not transfer req when tegra init fails\n\nThe tegra_cmac_init or tegra_sha_init function may return an error when\nmemory is exhausted. It should not transfer the request when they return\nan error."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: crypto: tegra - no transferir solicitud cuando falla la inicialización de tegra La función tegra_cmac_init o tegra_sha_init puede devolver un error cuando se agota la memoria. No debería transferir la solicitud cuando devuelven un error."}], "references": [{"url": "https://git.kernel.org/stable/c/15589bda46830695a3261518bb7627afac61f519", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/1dbc270f9df7f0ae1e591323431869059cee1b7d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/5eaa7c916e1ec4b122a1c3a8a20e692d9d9e174e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
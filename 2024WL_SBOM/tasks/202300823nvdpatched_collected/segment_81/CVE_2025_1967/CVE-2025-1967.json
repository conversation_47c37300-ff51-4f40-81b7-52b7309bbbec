{"cve_id": "CVE-2025-1967", "published_date": "2025-03-05T02:15:36.320", "last_modified_date": "2025-04-03T13:32:47.553", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in code-projects Blood Bank Management System 1.0. Affected by this issue is some unknown functionality of the file /user_dashboard/donor.php. The manipulation of the argument name leads to cross site scripting. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en code-projects Blood Bank Management System 1.0. Este problema afecta a algunas funciones desconocidas del archivo /user_dashboard/donor.php. La manipulación del nombre del argumento provoca cross site scripting. El ataque puede ejecutarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/intercpt/XSS1/blob/main/XSS.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.298568", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298568", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512163", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
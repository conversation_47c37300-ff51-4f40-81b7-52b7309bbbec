{"cve_id": "CVE-2025-2233", "published_date": "2025-03-11T23:15:38.610", "last_modified_date": "2025-03-11T23:15:38.610", "descriptions": [{"lang": "en", "value": "Samsung SmartThings Improper Verification of Cryptographic Signature Authentication Bypass Vulnerability. This vulnerability allows network-adjacent attackers to bypass authentication on affected installations of Samsung SmartThings. Authentication is not required to exploit this vulnerability.\n\nThe specific flaw exists within the Hub Local API service, which listens on TCP port 8766 by default. The issue results from the lack of proper verification of a cryptographic signature. An attacker can leverage this vulnerability to bypass authentication on the system. Was ZDI-CAN-25615."}, {"lang": "es", "value": "Vulnerabilidad de omisión de autenticación de firma criptográfica y verificación incorrecta en Samsung SmartThings. Esta vulnerabilidad permite a atacantes adyacentes a la red omitir la autenticación en las instalaciones afectadas de Samsung SmartThings. No se requiere autenticación para explotar esta vulnerabilidad. La falla específica se encuentra en el servicio Hub Local API, que escucha en el puerto TCP 8766 por defecto. El problema se debe a la falta de verificación adecuada de una firma criptográfica. Un atacante puede aprovechar esta vulnerabilidad para omitir la autenticación en el sistema. Anteriormente, se denominó ZDI-CAN-25615."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-127/", "source": "<EMAIL>", "tags": []}]}
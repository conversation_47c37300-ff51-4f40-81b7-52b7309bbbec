{"cve_id": "CVE-2025-25940", "published_date": "2025-03-10T16:15:13.520", "last_modified_date": "2025-06-23T20:05:39.933", "descriptions": [{"lang": "en", "value": "VisiCut 2.1 allows code execution via Insecure XML Deserialization in the loadPlfFile method of VisicutModel.java."}, {"lang": "es", "value": "VisiCut 2.1 permite la ejecución de código a través de la deserialización de XML insegura en el método loadPlfFile de VisicutModel.java."}], "references": [{"url": "https://github.com/t-oster/VisiCut", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://royblume.github.io/CVE-2025-25940/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
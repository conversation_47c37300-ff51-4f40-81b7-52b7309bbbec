{"cve_id": "CVE-2025-20206", "published_date": "2025-03-05T17:15:14.450", "last_modified_date": "2025-07-22T18:04:12.333", "descriptions": [{"lang": "en", "value": "A vulnerability in the interprocess communication (IPC) channel of Cisco Secure Client for Windows could allow an authenticated, local attacker to perform a DLL hijacking attack on an affected device if the Secure Firewall Posture Engine, formerly HostScan, is installed on Cisco Secure Client.\r\n\r\nThis vulnerability is due to insufficient validation of resources that are loaded by the application at run time. An attacker could exploit this vulnerability by sending a crafted IPC message to a specific Cisco Secure Client process. A successful exploit could allow the attacker to execute arbitrary code on the affected machine with SYSTEM privileges. To exploit this vulnerability, the attacker must have valid user credentials on the Windows system."}, {"lang": "es", "value": "Una vulnerabilidad en el canal de comunicación entre procesos (IPC) de Cisco Secure Client para Windows podría permitir que un atacante local autenticado realice un ataque de secuestro de DLL en un dispositivo afectado si Secure Firewall Posture Engine, anteriormente HostScan, está instalado en Cisco Secure Client. Esta vulnerabilidad se debe a una validación insuficiente de los recursos que carga la aplicación en tiempo de ejecución. Un atacante podría aprovechar esta vulnerabilidad enviando un mensaje IPC manipulado a un proceso específico de Cisco Secure Client. Una explotación exitosa podría permitir al atacante ejecutar código arbitrario en la máquina afectada con privilegios de SYSTEM. Para aprovechar esta vulnerabilidad, el atacante debe tener credenciales de usuario válidas en el sistema Windows."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-secure-dll-injection-AOyzEqSg", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2025-0990", "published_date": "2025-03-05T09:15:09.947", "last_modified_date": "2025-03-05T09:15:09.947", "descriptions": [{"lang": "en", "value": "The I Am Gloria plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.1.4. This is due to missing or incorrect nonce validation on the iamgloria23_gloria_settings_page function. This makes it possible for unauthenticated attackers to reset the tenant ID via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento I Am Gloria para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.1.4 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la función iamgloria23_gloria_settings_page. Esto permite que atacantes no autenticados restablezcan el ID del inquilino a través de una solicitud falsificada, siempre que puedan engañar a un administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://wordpress.org/plugins/gloria-assistant-by-webtronic-labs/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/33fd44dc-b4f8-4429-8dcd-5161602bb318?source=cve", "source": "<EMAIL>", "tags": []}]}
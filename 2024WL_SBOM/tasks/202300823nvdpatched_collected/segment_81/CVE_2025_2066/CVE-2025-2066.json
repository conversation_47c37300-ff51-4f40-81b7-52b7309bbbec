{"cve_id": "CVE-2025-2066", "published_date": "2025-03-07T05:15:17.293", "last_modified_date": "2025-05-14T16:14:57.687", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in projectworlds Life Insurance Management System 1.0 and classified as critical. This vulnerability affects unknown code of the file /updateAgent.php. The manipulation of the argument agent_id leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en projectworlds Life Insurance Management System 1.0 y se ha clasificado como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /updateAgent.php. La manipulación del argumento agent_id provoca una inyección SQL. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado. "}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298822", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298822", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514759", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
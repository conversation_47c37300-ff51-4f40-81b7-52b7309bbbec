{"cve_id": "CVE-2025-2052", "published_date": "2025-03-07T01:15:13.173", "last_modified_date": "2025-05-08T18:59:37.250", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Apartment Visitors Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /forgot-password.php. The manipulation of the argument contactno leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Apartment Visitors Management System 1.0 y se ha clasificado como crítica. Este problema afecta a algunos procesos desconocidos del archivo /forgot-password.php. La manipulación del argumento contactno provoca una inyección SQL. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/guttlefish/vul/issues/10", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298805", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298805", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514218", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
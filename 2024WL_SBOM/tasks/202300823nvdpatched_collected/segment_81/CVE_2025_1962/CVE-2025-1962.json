{"cve_id": "CVE-2025-1962", "published_date": "2025-03-05T00:15:37.540", "last_modified_date": "2025-04-02T18:24:43.310", "descriptions": [{"lang": "en", "value": "A vulnerability was found in projectworlds Online Hotel Booking 1.0. It has been classified as critical. This affects an unknown part of the file /admin/addroom.php. The manipulation of the argument roomname leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en projectworlds Online Hotel Booking 1.0. Se ha clasificado como crítica. Afecta a una parte desconocida del archivo /admin/addroom.php. La manipulación del argumento roomname provoca una inyección SQL. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298563", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298563", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511426", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry", "Exploit"]}]}
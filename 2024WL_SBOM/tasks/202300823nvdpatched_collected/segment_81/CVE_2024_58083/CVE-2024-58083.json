{"cve_id": "CVE-2024-58083", "published_date": "2025-03-06T17:15:21.773", "last_modified_date": "2025-04-01T18:30:59.790", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: Explicitly verify target vCP<PERSON> is online in kvm_get_vcpu()\n\nExplicitly verify the target vCPU is fully online _prior_ to clamping the\nindex in kvm_get_vcpu().  If the index is \"bad\", the nospec clamping will\ngenerate '0', i.e. KVM will return vCPU0 instead of NULL.\n\nIn practice, the bug is unlikely to cause problems, as it will only come\ninto play if userspace or the guest is buggy or misbehaving, e.g. KVM may\nsend interrupts to vCPU0 instead of dropping them on the floor.\n\nHowever, returning vCPU0 when it shouldn't exist per online_vcpus is\nproblematic now that KVM uses an xarray for the vCPUs array, as KVM needs\nto insert into the xarray before publishing the vCPU to userspace (see\ncommit c5b077549136 (\"KVM: Convert the kvm->vcpus array to a xarray\")),\ni.e. before vCPU creation is guaranteed to succeed.\n\nAs a result, incorrectly providing access to vCPU0 will trigger a\nuse-after-free if vCPU0 is dereferenced and kvm_vm_ioctl_create_vcpu()\nbails out of vCPU creation due to an error and frees vCPU0.  Commit\nafb2acb2e3a3 (\"KVM: Fix vcpu_array[0] races\") papered over that issue, but\nin doing so introduced an unsolvable teardown conundrum.  Preventing\naccesses to vCPU0 before it's fully online will allow reverting commit\nafb2acb2e3a3, without re-introducing the vcpu_array[0] UAF race."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: verificar explícitamente que la vCPU de destino esté en línea en kvm_get_vcpu() Verifique explícitamente que la vCPU de destino esté completamente en línea _antes_ de fijar el índice en kvm_get_vcpu(). Si el índice es \"malo\", la fijación nospec generará '0', es decir, KVM devolverá vCPU0 en lugar de NULL. En la práctica, es poco probable que el error cause problemas, ya que solo entrará en juego si el espacio de usuario o el invitado tienen errores o se comportan mal, por ejemplo, KVM puede enviar interrupciones a vCPU0 en lugar de dejarlas en el suelo. Sin embargo, devolver vCPU0 cuando no debería existir según online_vcpus es problemático ahora que KVM usa una matriz x para la matriz vCPUs, ya que KVM necesita insertar en la matriz x antes de publicar la vCPU en el espacio de usuario (consulte el commit c5b077549136 (\"KVM: Convertir la matriz kvm-&gt;vcpus en una matriz x\")), es decir, antes de que se garantice que la creación de la vCPU sea exitosa. Como resultado, proporcionar acceso incorrectamente a vCPU0 activará un uso después de liberación si se desreferencia a vCPU0 y kvm_vm_ioctl_create_vcpu() abandona la creación de la vCPU debido a un error y libera vCPU0. El commit afb2acb2e3a3 (\"KVM: Reparar las ejecuciones vcpu_array[0]\") disimuló ese problema, pero al hacerlo introdujo un enigma de desmontaje irresoluble. Evitar accesos a vCPU0 antes de que esté completamente en línea permitirá revertir el commit afb2acb2e3a3, sin volver a introducir la ejecución UAF vcpu_array[0]. "}], "references": [{"url": "https://git.kernel.org/stable/c/09d50ccf0b2d739db4a485b08afe7520a4402a63", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/125da53b3c0c9d7f58353aea0076e9efd6498ba7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/1e7381f3617d14b3c11da80ff5f8a93ab14cfc46", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/5cce2ed69b00e022b5cdf0c49c82986abd2941a8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7c4899239d0f70f88ac42665b3da51678d122480", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ca8da90ed1432ff3d000de4f1e2275d4e7d21b96", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/d817e510662fd1c9797952408d94806f97a5fffd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f2f805ada63b536bc192458a7098388286568ad4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
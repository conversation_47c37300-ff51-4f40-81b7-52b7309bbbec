{"cve_id": "CVE-2025-27676", "published_date": "2025-03-05T06:15:40.553", "last_modified_date": "2025-04-23T16:15:42.983", "descriptions": [{"lang": "en", "value": "Vasion Print (formerly PrinterLogic) before Virtual Appliance Host 22.0.843 Application 20.0.1923 allows Cross-Site Scripting in Reports V-2023-002."}, {"lang": "es", "value": "Vasion Print (anteriormente PrinterLogic) anterior a Virtual Appliance Host 22.0.843 La aplicación 20.0.1923 permite cross site scripting en los informes V-2023-002."}], "references": [{"url": "https://help.printerlogic.com/saas/Print/Security/Security-Bulletins.htm", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://pierrekim.github.io/blog/2025-04-08-vasion-printerlogic-83-vulnerabilities.html", "source": "<EMAIL>", "tags": []}, {"url": "https://pierrekim.github.io/blog/2025-04-08-vasion-printerlogic-83-vulnerabilities.html#va-xss-03", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": []}]}
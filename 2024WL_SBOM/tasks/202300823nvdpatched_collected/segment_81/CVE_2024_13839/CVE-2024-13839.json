{"cve_id": "CVE-2024-13839", "published_date": "2025-03-05T10:15:18.443", "last_modified_date": "2025-03-05T10:15:18.443", "descriptions": [{"lang": "en", "value": "The Staff Directory Plugin: Company Directory plugin for WordPress is vulnerable to Reflected Cross-Site Scripting due to the use of add_query_arg without appropriate escaping on the URL in all versions up to, and including, 4.3. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Staff Directory Plugin: Company Directory para WordPress es vulnerable a Cross-Site Scripting reflejado debido al uso de add_query_arg sin el escape adecuado en la URL en todas las versiones hasta la 4.3 incluida. Esto permite que atacantes no autenticados inyecten secuencias de comandos web arbitrarias en páginas que se ejecutan si logran engañar a un usuario para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/staff-directory-pro/trunk/include/tgmpa/init.php#L99", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/staff-directory-pro/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/*************-441a-b51d-2d09968492b5?source=cve", "source": "<EMAIL>", "tags": []}]}
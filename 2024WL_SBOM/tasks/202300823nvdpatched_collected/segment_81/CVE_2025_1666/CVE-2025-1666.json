{"cve_id": "CVE-2025-1666", "published_date": "2025-03-06T12:15:36.117", "last_modified_date": "2025-03-06T12:15:36.117", "descriptions": [{"lang": "en", "value": "The Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics plugin for WordPress is vulnerable to unauthorized modification of data due to a missing capability check on the send_uninstall_survey() function in all versions up to, and including, 4.4.1. This makes it possible for authenticated attackers, with Subscriber-level access and above, to submit the uninstall survey on behalf of a website."}, {"lang": "es", "value": "El complemento Cookie banner para WordPress – Cookiebot CMP de Usercentrics para WordPress es vulnerable a la modificación no autorizada de datos debido a una falta de comprobación de capacidad en la función send_uninstall_survey() en todas las versiones hasta la 4.4.1 incluida. Esto hace posible que atacantes autenticados, con acceso de nivel de suscriptor y superior, envíen la encuesta de desinstalación en nombre de un sitio web."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/cookiebot/tags/4.4.1/src/lib/Cookiebot_Review.php#L135", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3251089%40cookiebot&new=3251089%40cookiebot&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d2e5fca6-363c-4875-9eb8-44e080d99650?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-28876", "published_date": "2025-03-11T21:15:45.200", "last_modified_date": "2025-04-09T13:12:20.693", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Skrill_Team Skrill Official allows Cross Site Request Forgery. This issue affects Skrill Official: from n/a through 1.0.65."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en Skrill_Team Skrill Official permite Cross-Site Request Forgery. Este problema afecta a Skrill Official desde n/d hasta la versión 1.0.65."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/official-skrill-woocommerce/vulnerability/wordpress-skrill-official-plugin-1-0-65-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-27598", "published_date": "2025-03-06T23:15:12.183", "last_modified_date": "2025-03-24T18:36:19.670", "descriptions": [{"lang": "en", "value": "ImageSharp is a 2D graphics API. An Out-of-bounds Write vulnerability has been found in the ImageSharp gif decoder, allowing attackers to cause a crash using a specially crafted gif. This can potentially lead to denial of service. The problem has been patched. All users are advised to upgrade to v3.1.7 or v2.1.10."}, {"lang": "es", "value": "ImageSharp es una API de gráficos 2D. Se ha encontrado una vulnerabilidad de escritura fuera de los límites en el decodificador de gifs de ImageSharp, que permite a los atacantes provocar un bloqueo utilizando un gif especialmente manipulado. Esto puede provocar una denegación de servicio. El problema ha sido corregido. Se recomienda a todos los usuarios que actualicen a la versión 3.1.7 o 2.1.10."}], "references": [{"url": "https://github.com/SixLabors/ImageSharp/issues/2859", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/SixLabors/ImageSharp/pull/2890", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/SixLabors/ImageSharp/security/advisories/GHSA-2cmq-823j-5qj8", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
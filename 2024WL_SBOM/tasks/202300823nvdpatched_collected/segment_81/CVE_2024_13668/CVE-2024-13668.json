{"cve_id": "CVE-2024-13668", "published_date": "2025-03-07T10:15:16.013", "last_modified_date": "2025-05-21T16:59:07.120", "descriptions": [{"lang": "en", "value": "The WordPress Activity O Meter WordPress plugin through 1.0 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admins."}, {"lang": "es", "value": "El complemento WordPress Activity O Meter de WordPress hasta la versión 1.0 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con altos privilegios, como los administradores."}], "references": [{"url": "https://wpscan.com/vulnerability/a7bfc094-b235-419d-882d-96b439651f65/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-58085", "published_date": "2025-03-06T17:15:21.993", "last_modified_date": "2025-03-13T13:15:46.373", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ntomoyo: don't emit warning in tomoyo_write_control()\n\nsyzbot is reporting too large allocation warning at tomoyo_write_control(),\nfor one can write a very very long line without new line character. To fix\nthis warning, I use __GFP_NOWARN rather than checking for K<PERSON>LLOC_MAX_SIZE,\nfor practically a valid line should be always shorter than 32KB where the\n\"too small to fail\" memory-allocation rule applies.\n\nOne might try to write a valid line that is longer than 32KB, but such\nrequest will likely fail with -ENOMEM. Therefore, I feel that separately\nreturning -EINVAL when a line is longer than KMALLOC_MAX_SIZE is redundant.\nThere is no need to distinguish over-32KB and over-KMALLOC_MAX_SIZE."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: tomoyo: no emitir advertencia en tomoyo_write_control() syzbot informa una advertencia de asignación demasiado grande en tomoyo_write_control(), ya que se puede escribir una línea muy larga sin un carácter de nueva línea. Para corregir esta advertencia, uso __GFP_NOWARN en lugar de verificar KMALLOC_MAX_SIZE, ya que prácticamente una línea válida siempre debe ser más corta que 32 KB donde se aplica la regla de asignación de memoria \"demasiado pequeña para fallar\". Uno podría intentar escribir una línea válida que sea más larga que 32 KB, pero tal solicitud probablemente fallará con -ENOMEM. Por lo tanto, creo que devolver por separado -EINVAL cuando una línea es más larga que KMALLOC_MAX_SIZE es redundante. No hay necesidad de distinguir entre más de 32 KB y más de KMALLOC_MAX_SIZE."}], "references": [{"url": "https://git.kernel.org/stable/c/3df7546fc03b8f004eee0b9e3256369f7d096685", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/414705c0303350d139b1dc18f329fe47dfb642dd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a01c200fa7eb59da4d2dbbb48b61f4a0d196c09f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b2bd5857a0d6973ebbcb4d9831ddcaebbd257be1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c67efabddc73171c7771d3ffe4ffa1e503ee533e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c9382f380e8d09209b8e5c0def0545852168be25", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f6b37b3e12de638753bce79a2858070b9c4a4ad3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fe1c021eb03dae0dc9dce55e81f77a60e419a27a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
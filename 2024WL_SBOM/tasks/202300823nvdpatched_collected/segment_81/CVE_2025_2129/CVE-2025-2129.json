{"cve_id": "CVE-2025-2129", "published_date": "2025-03-09T20:15:27.157", "last_modified_date": "2025-03-09T20:15:27.157", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Mage AI 0.9.75. It has been classified as problematic. This affects an unknown part. The manipulation leads to insecure default initialization of resource. It is possible to initiate the attack remotely. The complexity of an attack is rather high. The exploitability is told to be difficult. The exploit has been disclosed to the public and may be used. The real existence of this vulnerability is still doubted at the moment. After 7 months of repeated follow-ups by the researcher, Mage AI has decided to not accept this issue as a valid security vulnerability and has confirmed that they will not be addressing it."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en Mage AI 0.9.75. Se ha clasificado como problemática. Afecta a una parte desconocida. La manipulación provoca una inicialización predeterminada insegura del recurso. Es posible iniciar el ataque de forma remota. Es un ataque de complejidad bastante alta. Se dice que la explotabilidad es difícil. Se ha hecho público el exploit y puede que sea utilizado. La existencia real de esta vulnerabilidad todavía se duda en este momento. Después de 7 meses de seguimiento repetido por parte del investigador, Mage AI ha decidido no aceptar este problema como una vulnerabilidad de seguridad válida y ha confirmado que no lo abordarán."}], "references": [{"url": "https://github.com/zn9988/publications/blob/main/2.Mage-AI%20-%20Insecure%20Default%20Authentication%20Setup%20Leading%20to%20Zero-Click%20RCE/README.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299049", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299049", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.510690", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27792", "published_date": "2025-03-11T22:15:13.567", "last_modified_date": "2025-03-12T14:15:16.930", "descriptions": [{"lang": "en", "value": "Opal is OBiBa’s core database application for biobanks or epidemiological studies. Prior to version 5.1.1, the protections against cross-site request forgery (CSRF) were insufficient application-wide. The referrer header is checked, and if it is invalid, the server returns 403. However, the referrer header can be dropped from CSRF requests using `<meta name=\"referrer\" content=\"never\">`, effectively bypassing this protection. Version 5.1.1 contains a patch for the issue."}, {"lang": "es", "value": "Opal es la aplicación principal de base de datos de OBiBa para biobancos o estudios epidemiológicos. Antes de la versión 5.1.1, la protección contra cross-site request forgery (CSRF) era insuficiente en toda la aplicación. Se verifica el encabezado de referencia y, si no es válido, el servidor devuelve el error 403. Sin embargo, se puede omitir el encabezado de referencia de las solicitudes CSRF mediante ``, lo que permite eludir esta protección. La versión 5.1.1 incluye un parche para este problema."}], "references": [{"url": "https://github.com/obiba/opal/security/advisories/GHSA-27vw-29rq-c358", "source": "<EMAIL>", "tags": []}]}
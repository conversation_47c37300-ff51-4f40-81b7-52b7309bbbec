{"cve_id": "CVE-2025-1702", "published_date": "2025-03-05T12:15:35.420", "last_modified_date": "2025-03-05T12:15:35.420", "descriptions": [{"lang": "en", "value": "The Ultimate Member – User Profile, Registration, Login, Member Directory, Content Restriction & Membership Plugin plugin for WordPress is vulnerable to time-based SQL Injection via the 'search' parameter in all versions up to, and including, 2.10.0 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Ultimate Member – User Profile, Registration, Login, Member Directory, Content Restriction &amp; Membership Plugin para WordPress es vulnerable a la inyección SQL basada en tiempo a través del parámetro 'search' en todas las versiones hasta la 2.10.0 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto hace posible que atacantes no autenticados agreguen consultas SQL adicionales a consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://github.com/ultimatemember/ultimatemember/pull/1654/commits/74647d42cc8d63f5d4f687efcd0792c246c23039", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/ultimate-member/trunk/includes/core/class-member-directory.php#L1775", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/ultimate-member/trunk/includes/core/class-member-directory.php#L1863", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3249862/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/ultimate-member/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/34adbae5-d615-4f8d-a845-6741d897f06c?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-1481", "published_date": "2025-03-08T03:15:37.237", "last_modified_date": "2025-03-12T16:40:25.783", "descriptions": [{"lang": "en", "value": "The Shortcode Cleaner Lite plugin for WordPress is vulnerable to unauthorized access of data due to a missing capability check on the download_backup() function in all versions up to, and including, 1.0.9. This makes it possible for authenticated attackers, with Subscriber-level access and above, to export arbitrary options."}, {"lang": "es", "value": "El complemento Shortcode Cleaner Lite para WordPress es vulnerable al acceso no autorizado a los datos debido a una falta de verificación de capacidad en la función download_backup() en todas las versiones hasta la 1.0.9 incluida. Esto permite que atacantes autenticados, con acceso de nivel de suscriptor y superior, exporten opciones arbitrarias."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/shortcode-cleaner-lite/trunk/vendor/codestar/codestar/core/Module/Export.php#L53", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://wordpress.org/plugins/shortcode-cleaner-lite/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/15613da5-f900-4a33-8eec-6c9e52ed30fc?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-28937", "published_date": "2025-03-11T21:15:51.587", "last_modified_date": "2025-03-11T21:15:51.587", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in lavacode Lava Ajax Search allows Stored XSS. This issue affects Lava Ajax Search: from n/a through 1.1.9."}, {"lang": "es", "value": "Vulnerabilidad de neutralización incorrecta de la entrada durante la generación de páginas web ('Cross-site Scripting') en lavacode Lava Ajax Search permite XSS almacenado. Este problema afecta a Lava Ajax Search desde n/d hasta la versión 1.1.9."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/lava-ajax-search/vulnerability/wordpress-lava-ajax-search-plugin-1-1-9-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
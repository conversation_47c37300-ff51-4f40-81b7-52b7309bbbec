{"cve_id": "CVE-2025-27403", "published_date": "2025-03-11T15:15:45.570", "last_modified_date": "2025-03-11T15:15:45.570", "descriptions": [{"lang": "en", "value": "Ratify is a verification engine as a binary executable and on Kubernetes which enables verification of artifact security metadata and admits for deployment only those that comply with policies the user creates. In a Kubernetes environment, Ratify can be configured to authenticate to a private Azure Container Registry (ACR). The Azure workload identity and Azure managed identity authentication providers are configured in this setup. Users that configure a private ACR to be used with the Azure authentication providers may be impacted by a vulnerability that exists in versions prior to 1.2.3 and 1.3.2. Both Azure authentication providers attempt to exchange an Entra ID (EID) token for an ACR refresh token. However, Ratify’s Azure authentication providers did not verify that the target registry is an ACR. This could have led to the EID token being presented to a non-ACR registry during token exchange. EID tokens with ACR access can potentially be extracted and abused if a user workload contains an image reference to a malicious registry. As of versions 1.2.3 and 1.3.2, the Azure workload identity and Azure managed identity authentication providers are updated to add new validation prior to EID token exchange. Validation relies upon registry domain validation against a pre-configured list of well-known ACR endpoints. EID token exchange will be executed only if at least one of the configured well-known domain suffixes (wildcard support included) matches the registry domain of the image reference."}, {"lang": "es", "value": "Ratify es un motor de verificación como ejecutable binario en Kubernetes que permite la verificación de metadatos de seguridad de artefactos y admite para su implementación solo aquellos que cumplen con las políticas creadas por el usuario. En un entorno de Kubernetes, Ratify se puede configurar para autenticarse en un Azure Container Registry (ACR) privado. Los proveedores de autenticación de identidad de carga de trabajo de Azure y de identidad administrada de Azure se configuran en esta configuración. Los usuarios que configuren un ACR privado para su uso con los proveedores de autenticación de Azure pueden verse afectados por una vulnerabilidad existente en versiones anteriores a la 1.2.3 y la 1.3.2. Ambos proveedores de autenticación de Azure intentan intercambiar un token de Entra ID (EID) por un token de actualización de ACR. Sin embargo, los proveedores de autenticación de Azure de Ratify no verificaron que el registro de destino sea un ACR. Esto podría haber provocado que el token EID se presentara a un registro que no es ACR durante el intercambio de tokens. Los tokens EID con acceso a ACR pueden ser extraídos y utilizados de forma abusiva si la carga de trabajo de un usuario contiene una referencia de imagen a un registro malicioso. A partir de las versiones 1.2.3 y 1.3.2, los proveedores de autenticación de identidad de carga de trabajo de Azure y de identidad administrada de Azure se actualizan para añadir una nueva validación antes del intercambio de tokens EID. La validación se basa en la validación del dominio de registro con una lista preconfigurada de endpoints ACR conocidos. El intercambio de tokens EID solo se ejecutará si al menos uno de los sufijos de dominio conocidos configurados (incluido el soporte para comodines) coincide con el dominio de registro de la referencia de la imagen."}], "references": [{"url": "https://github.com/ratify-project/ratify/commit/0ec0c08490e3d672ae64b1a220c90d5484f1c93f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ratify-project/ratify/commit/84c7c48fa76bb9a1c9583635d1e90bc25b1a546c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ratify-project/ratify/security/advisories/GHSA-44f7-5fj5-h4px", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2116", "published_date": "2025-03-09T07:15:10.003", "last_modified_date": "2025-03-09T07:15:10.003", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Beijing Founder Electronics Founder Enjoys All-Media Acquisition and Editing System 3.0 and classified as problematic. Affected by this vulnerability is an unknown functionality of the file /newsedit/newsedit/xy/imageProxy.do of the component File Protocol Handler. The manipulation of the argument xyImgUrl leads to server-side request forgery. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en Beijing Founder Electronics Founder Enjoys All-Media Acquisition and Editing System 3.0 y se ha clasificado como problemática. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /newsedit/newsedit/xy/imageProxy.do del componente File Protocol Handler. La manipulación del argumento xyImgUrl conduce a server-side request forgery. El ataque se puede lanzar de forma remota. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con anticipación sobre esta revelación, pero no respondió de ninguna manera."}], "references": [{"url": "https://flowus.cn/share/a104e4fc-a8f7-48b1-8648-1a3e5f78b9bf?code=G8A6P3", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299011", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299011", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.503719", "source": "<EMAIL>", "tags": []}]}
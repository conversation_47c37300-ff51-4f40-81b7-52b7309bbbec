{"cve_id": "CVE-2025-1664", "published_date": "2025-03-08T12:15:36.010", "last_modified_date": "2025-03-11T21:25:56.700", "descriptions": [{"lang": "en", "value": "The Essential Blocks – Page Builder Gutenberg Blocks, Patterns & Templates plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Parallax slider in all versions up to, and including, 5.3.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Essential Blocks – Page Builder Gutenberg Blocks, Patterns &amp; Templates para WordPress es vulnerable a cross site scripting almacenado a través del control deslizante Parallax en todas las versiones hasta la 5.3.1 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3250957/essential-blocks/tags/5.3.2/assets/blocks/parallax-slider/frontend.js", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/6abfa01b-e2ec-412c-a17d-e8bd1f5ac228?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
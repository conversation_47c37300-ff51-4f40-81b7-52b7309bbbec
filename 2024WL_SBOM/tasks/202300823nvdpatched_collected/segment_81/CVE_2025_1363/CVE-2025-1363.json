{"cve_id": "CVE-2025-1363", "published_date": "2025-03-09T06:15:11.733", "last_modified_date": "2025-05-21T17:02:28.650", "descriptions": [{"lang": "en", "value": "The URL Shortener | Conversion Tracking  | AB Testing  | WooCommerce WordPress plugin through 9.0.2 does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento URL Shortener | Conversion Tracking | AB Testing | WooCommerce WordPress hasta la versión 9.0.2 no desinfecta ni escapa a algunas de sus configuraciones, lo que podría permitir que usuarios con privilegios elevados como el administrador realicen ataques de cross site scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/16b08e77-3562-4506-9b28-abd1b1128b0a/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
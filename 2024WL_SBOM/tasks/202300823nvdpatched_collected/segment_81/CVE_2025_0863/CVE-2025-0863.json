{"cve_id": "CVE-2025-0863", "published_date": "2025-03-07T08:15:40.827", "last_modified_date": "2025-03-07T08:15:40.827", "descriptions": [{"lang": "en", "value": "The Flexmls® IDX Plugin plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'idx_frame' shortcode in all versions up to, and including, 3.14.27 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Flexmls® IDX Plugin para WordPress es vulnerable a Cross-Site Scripting almacenado a través del código abreviado 'idx_frame' del complemento en todas las versiones hasta la 3.14.27 incluida, debido a una depuración de entrada y al escape de salida insuficiente en los atributos proporcionados por el usuario. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán siempre que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/flexmls-idx/tags/3.14.25/flexmls_connect.php#L92", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/flexmls-idx/tags/3.14.25/lib/base.php#L220", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3251292%40flexmls-idx&new=3251292%40flexmls-idx&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1c8e814b-3828-4b3f-a9ad-b3758ab9b109?source=cve", "source": "<EMAIL>", "tags": []}]}
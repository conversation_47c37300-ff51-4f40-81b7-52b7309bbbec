{"cve_id": "CVE-2025-0954", "published_date": "2025-03-05T10:15:19.130", "last_modified_date": "2025-03-05T10:15:19.130", "descriptions": [{"lang": "en", "value": "The WP Online Contract plugin for WordPress is vulnerable to unauthorized access due to a missing capability check on the json_import() and json_export() functions in all versions up to, and including, 5.1.4. This makes it possible for unauthenticated attackers to import and export the plugin's settings."}, {"lang": "es", "value": "El complemento WP Online Contract para WordPress es vulnerable al acceso no autorizado debido a una falta de verificación de capacidad en las funciones json_import() y json_export() en todas las versiones hasta la 5.1.4 incluida. Esto permite que atacantes no autenticados importen y exporten la configuración del complemento."}], "references": [{"url": "https://codecanyon.net/item/wp-online-contract/7698011", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/70f464ca-ff6c-4c2e-8b56-bf5e4bc6bd1f?source=cve", "source": "<EMAIL>", "tags": []}]}
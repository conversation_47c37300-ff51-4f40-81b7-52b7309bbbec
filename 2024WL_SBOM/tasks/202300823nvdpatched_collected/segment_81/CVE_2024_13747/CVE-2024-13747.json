{"cve_id": "CVE-2024-13747", "published_date": "2025-03-05T10:15:14.617", "last_modified_date": "2025-03-05T10:15:14.617", "descriptions": [{"lang": "en", "value": "The WooMail - WooCommerce Email Customizer plugin for WordPress is vulnerable to unauthorized loss of data due to a missing capability check on the 'template_delete_saved' function in all versions up to, and including, 3.0.34. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject SQL into an existing post deletion query."}, {"lang": "es", "value": "El complemento WooMail - WooCommerce Email Customizer para WordPress es vulnerable a la pérdida no autorizada de datos debido a una verificación de capacidad faltante en la función 'template_delete_saved' en todas las versiones hasta la 3.0.34 incluida. Esto hace posible que atacantes autenticados, con acceso de nivel de suscriptor y superior, inyecten SQL en una consulta de eliminación de publicaciones existente."}], "references": [{"url": "https://codecanyon.net/item/email-customizer-for-woocommerce-with-drag-drop-builder-woo-email-editor/22400984", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e74e1a7c-4fe6-4041-8c4c-13389dacb9db?source=cve", "source": "<EMAIL>", "tags": []}]}
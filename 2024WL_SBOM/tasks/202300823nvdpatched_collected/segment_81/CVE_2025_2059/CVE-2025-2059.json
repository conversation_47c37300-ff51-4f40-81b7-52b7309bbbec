{"cve_id": "CVE-2025-2059", "published_date": "2025-03-07T02:15:38.600", "last_modified_date": "2025-05-21T18:47:37.477", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Emergency Ambulance Hiring Portal 1.0 and classified as critical. Affected by this issue is some unknown functionality of the file /admin/booking-details.php. The manipulation of the argument ambulanceregnum leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Emergency Ambulance Hiring Portal 1.0 y se clasificó como crítica. Este problema afecta a algunas funciones desconocidas del archivo /admin/booking-details.php. La manipulación del argumento ambulanceregnum conduce a una inyección SQL. El ataque puede ejecutarse de forma remota. El exploit se ha revelado al público y puede utilizarse."}], "references": [{"url": "https://github.com/siznwaa/CVE/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298814", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298814", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514522", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
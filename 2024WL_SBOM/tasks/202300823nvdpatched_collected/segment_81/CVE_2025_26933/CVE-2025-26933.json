{"cve_id": "CVE-2025-26933", "published_date": "2025-03-10T15:15:37.997", "last_modified_date": "2025-03-10T15:15:37.997", "descriptions": [{"lang": "en", "value": "Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion') vulnerability in Nitin Prakash WC Place Order Without Payment allows PHP Local File Inclusion. This issue affects WC Place Order Without Payment: from n/a through 2.6.7."}, {"lang": "es", "value": "Vulnerabilidad de control inadecuado del nombre de archivo para la declaración Include/Require en el programa PHP ('Inclusión de archivo remoto PHP') en Nitin Prakash WC Place Order Without Payment permite la inclusión de archivos locales PHP. Este problema afecta a WC Place Order Without Payment: desde n/a hasta 2.6.7."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wc-place-order-without-payment/vulnerability/wordpress-place-order-without-payment-for-woocommerce-plugin-2-6-7-local-file-inclusion-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
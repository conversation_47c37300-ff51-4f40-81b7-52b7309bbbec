{"cve_id": "CVE-2025-1261", "published_date": "2025-03-08T02:15:34.147", "last_modified_date": "2025-03-24T18:32:02.397", "descriptions": [{"lang": "en", "value": "The HT Mega – Absolute Addons For Elementor plugin for WordPress is vulnerable to DOM-Based Stored Cross-Site Scripting via the plugin's Countdown widget in all versions up to, and including, 2.8.2 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. NOTE: This vulnerability exists due to an incomplete fix for CVE-2024-3307."}, {"lang": "es", "value": "El complemento HT Mega – Absolute Addons For Elementor para WordPress es vulnerable a cross site scripting almacenado basadas en DOM a través del widget Countdown del complemento en todas las versiones hasta la 2.8.2 incluida, debido a una depuración de entrada y al escape de salida insuficiente en los atributos proporcionados por el usuario. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. NOTA: Esta vulnerabilidad existe debido a una corrección incompleta de CVE-2024-3307."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3249106/ht-mega-for-elementor/tags/2.8.3/assets/js/htmega-widgets-active.js", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e553135d-88e0-4840-99ad-9514c2243b7d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2024-13904", "published_date": "2025-03-07T09:15:15.817", "last_modified_date": "2025-03-13T17:43:12.470", "descriptions": [{"lang": "en", "value": "The Platform.ly for WooCommerce plugin for WordPress is vulnerable to Blind Server-Side Request Forgery in all versions up to, and including, 1.1.6 via the 'hooks' function. This makes it possible for unauthenticated attackers to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Platform.ly para WooCommerce para WordPress es vulnerable a Blind Server-Side Request Forgery en todas las versiones hasta la 1.1.6 incluida a través de la función \"hooks\". Esto permite que atacantes no autenticados realicen solicitudes web a ubicaciones arbitrarias que se originan en la aplicación web y se pueden usar para consultar y modificar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/platformly-for-woocommerce/trunk/platformly-for-woocommerce.php#L167", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3249460", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/944e4c96-6ded-4483-9eaf-d976646f45ea?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
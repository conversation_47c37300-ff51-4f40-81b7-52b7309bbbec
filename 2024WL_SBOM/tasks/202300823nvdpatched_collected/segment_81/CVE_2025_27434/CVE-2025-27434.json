{"cve_id": "CVE-2025-27434", "published_date": "2025-03-11T01:15:36.760", "last_modified_date": "2025-03-11T01:15:36.760", "descriptions": [{"lang": "en", "value": "Due to insufficient input validation, SAP Commerce (Swagger UI) allows an unauthenticated attacker to inject the malicious code from remote sources, which can be leveraged by an attacker to execute a cross-site scripting (XSS) attack. This could lead to a high impact on the confidentiality, integrity, and availability of data in SAP Commerce."}, {"lang": "es", "value": "Debido a una validación de entrada insuficiente, SAP Commerce (Swagger UI) permite que un atacante no autenticado inyecte código malicioso desde fuentes remotas, que puede ser aprovechado por un atacante para ejecutar un ataque de cross-site scripting (XSS). Esto podría generar un gran impacto en la confidencialidad, integridad y disponibilidad de los datos en SAP Commerce."}], "references": [{"url": "https://me.sap.com/notes/3569602", "source": "<EMAIL>", "tags": []}, {"url": "https://url.sap/sapsecuritypatchday", "source": "<EMAIL>", "tags": []}]}
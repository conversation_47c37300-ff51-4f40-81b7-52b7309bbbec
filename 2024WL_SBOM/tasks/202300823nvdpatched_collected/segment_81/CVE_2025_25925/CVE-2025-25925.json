{"cve_id": "CVE-2025-25925", "published_date": "2025-03-11T20:15:16.917", "last_modified_date": "2025-05-21T19:27:00.397", "descriptions": [{"lang": "en", "value": "A stored cross-scripting (XSS) vulnerability in Openmrs v2.4.3 Build 0ff0ed allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the personName.middleName parameter at /openmrs/admin/patients/shortPatientForm.form."}, {"lang": "es", "value": "Una vulnerabilidad de cross-scripting (XSS) almacenado en Openmrs v2.4.3 Build 0ff0ed permite a los atacantes ejecutar scripts web o HTML arbitrarios mediante la inyección de un payload manipulado en el parámetro personName.middleName en /openmrs/admin/patients/shortPatientForm.form."}], "references": [{"url": "http://openmrs.com", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://github.com/johnchd/CVEs/blob/main/OpenMRS/CVE-2025-25925%20-%20P-XSS.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
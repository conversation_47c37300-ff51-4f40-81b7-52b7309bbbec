{"cve_id": "CVE-2025-2196", "published_date": "2025-03-11T14:15:28.123", "last_modified_date": "2025-04-10T10:53:23.660", "descriptions": [{"lang": "en", "value": "A vulnerability was found in MRCMS 3.1.2. It has been declared as problematic. Affected by this vulnerability is the function upload of the file /admin/file/upload.do of the component org.marker.mushroom.controller.FileController. The manipulation of the argument path leads to cross site scripting. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en MRCMS 3.1.2. Se ha declarado problemática. Esta vulnerabilidad afecta la función de carga del archivo /admin/file/upload.do del componente org.marker.mushroom.controller.FileController. La manipulación de la ruta de argumentos provoca ataques de cross site scripting. El ataque puede ejecutarse remotamente. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.299221", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299221", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511735", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
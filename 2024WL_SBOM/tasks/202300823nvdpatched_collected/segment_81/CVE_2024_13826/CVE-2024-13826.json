{"cve_id": "CVE-2024-13826", "published_date": "2025-03-08T06:15:36.657", "last_modified_date": "2025-05-06T16:16:19.067", "descriptions": [{"lang": "en", "value": "The Email Keep WordPress plugin through 1.1 does not have CSRF check in place when updating its settings, which could allow attackers to make a logged in admin change them via a CSRF attack"}, {"lang": "es", "value": "El complemento Email Keep WordPress hasta la versión 1.1 no tiene una verificación CSRF activada al actualizar sus configuraciones, lo que podría permitir a los atacantes hacer que un administrador que haya iniciado sesión las cambie mediante un ataque CSRF."}], "references": [{"url": "https://wpscan.com/vulnerability/56b143b6-e5db-4037-ab2a-4e4d0cb7a005/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
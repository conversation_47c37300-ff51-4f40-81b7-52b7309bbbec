{"cve_id": "CVE-2025-27910", "published_date": "2025-03-10T22:15:27.287", "last_modified_date": "2025-05-21T19:34:30.897", "descriptions": [{"lang": "en", "value": "tianti v2.3 was discovered to contain a Cross-Site Request Forgery (CSRF) via the component /user/ajax/upd/status. This vulnerability allows attackers to execute arbitrary operations via a crafted GET or POST request."}, {"lang": "es", "value": "Se descubrió que tianti v2.3 contenía Cross-Site Request Forgery (CSRF) a través del componente /user/ajax/upd/status. Esta vulnerabilidad permite a los atacantes ejecutar operaciones arbitrarias a través de una solicitud GET o POST manipulada."}], "references": [{"url": "https://github.com/xujeff/tianti/issues/39", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}]}
{"cve_id": "CVE-2025-1964", "published_date": "2025-03-05T01:15:10.020", "last_modified_date": "2025-05-15T20:43:05.607", "descriptions": [{"lang": "en", "value": "A vulnerability was found in projectworlds Online Hotel Booking 1.0. It has been rated as critical. This issue affects some unknown processing of the file /booknow.php?roomname=Duplex. The manipulation of the argument checkin leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en projectworlds Online Hotel Booking 1.0. Se ha calificado como crítica. Este problema afecta a algunos procesos desconocidos del archivo /booknow.php?roomname=Duplex. La manipulación del argumento checkin provoca una inyección SQL. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado. También pueden verse afectados otros parámetros."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298565", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298565", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.511471", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
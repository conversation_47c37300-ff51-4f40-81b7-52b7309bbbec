{"cve_id": "CVE-2025-25015", "published_date": "2025-03-05T10:15:20.160", "last_modified_date": "2025-04-02T17:15:45.240", "descriptions": [{"lang": "en", "value": "Prototype pollution in Kibana leads to arbitrary code execution via a crafted file upload and specifically crafted HTTP requests.\nIn Kibana versions >= 8.15.0 and < 8.17.1, this is exploitable by users with the Viewer role. In Kibana versions 8.17.1 and 8.17.2 , this is only exploitable by users that have roles that contain all the following privileges: fleet-all, integrations-all, actions:execute-advanced-connectors"}, {"lang": "es", "value": "La contaminación de prototipos en Kibana conduce a la ejecución de código arbitrario a través de una carga de archivo manipulada y solicitudes HTTP manipuladas específicamente. En las versiones de Kibana &gt;= 8.15.0 y &lt; 8.17.1, esto es explotable por los usuarios con el rol de Visor. En las versiones de Kibana 8.17.1 y 8.17.2, esto solo es explotable por los usuarios que tienen roles que contienen todos los siguientes privilegios: flee-all, integrations-all, shares:execute-advanced-connectors"}], "references": [{"url": "https://discuss.elastic.co/t/kibana-8-17-3-8-16-6-security-update-esa-2025-06/375441", "source": "<EMAIL>", "tags": []}]}
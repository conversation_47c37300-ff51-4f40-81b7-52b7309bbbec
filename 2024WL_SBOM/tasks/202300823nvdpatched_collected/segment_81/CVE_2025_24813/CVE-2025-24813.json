{"cve_id": "CVE-2025-24813", "published_date": "2025-03-10T17:15:35.067", "last_modified_date": "2025-07-23T18:10:23.830", "descriptions": [{"lang": "en", "value": "Path Equivalence: 'file.Name' (Internal Dot) leading to Remote Code Execution and/or Information disclosure and/or malicious content added to uploaded files via write enabled Default Servlet in Apache Tomcat.\n\nThis issue affects Apache Tomcat: from 11.0.0-M1 through 11.0.2, from 10.1.0-M1 through 10.1.34, from 9.0.0.M1 through 9.0.98.\n\nIf all of the following were true, a malicious user was able to view       security sensitive files and/or inject content into those files:\n- writes enabled for the default servlet (disabled by default)\n- support for partial PUT (enabled by default)\n- a target URL for security sensitive uploads that was a sub-directory of a target URL for public uploads\n- attacker knowledge of the names of security sensitive files being uploaded\n- the security sensitive files also being uploaded via partial PUT\n\nIf all of the following were true, a malicious user was able to       perform remote code execution:\n- writes enabled for the default servlet (disabled by default)\n- support for partial PUT (enabled by default)\n- application was using Tomcat's file based session persistence with the default storage location\n- application included a library that may be leveraged in a deserialization attack\n\nUsers are recommended to upgrade to version 11.0.3, 10.1.35 or 9.0.99, which fixes the issue."}, {"lang": "es", "value": "Equivalencia de ruta: 'file.Name' (punto interno) que conduce a la ejecución remota de código y/o divulgación de información y/o contenido malicioso agregado a los archivos cargados a través del servlet predeterminado habilitado para escritura en Apache Tomcat. Este problema afecta a Apache Tomcat: desde 11.0.0-M1 hasta 11.0.2, desde 10.1.0-M1 hasta 10.1.34, desde 9.0.0.M1 hasta 9.0.98. Si todo lo siguiente fuera cierto, un usuario malintencionado podría ver archivos sensibles de seguridad y/o inyectar contenido en esos archivos: - escrituras habilitadas para el servlet predeterminado (deshabilitado por defecto) - soporte para PUT parcial (habilitado por defecto) - una URL de destino para cargas sensibles de seguridad que era un subdirectorio de una URL de destino para cargas públicas - conocimiento del atacante de los nombres de los archivos sensibles de seguridad que se estaban cargando - los archivos sensibles de seguridad también se estaban cargando a través de PUT parcial Si todo lo siguiente fuera cierto, un usuario malintencionado podría realizar una ejecución remota de código: - escrituras habilitadas para el servlet predeterminado (deshabilitado por defecto) - soporte para PUT parcial (habilitado por defecto) - la aplicación estaba usando la persistencia de sesión basada en archivos de Tomcat con la ubicación de almacenamiento predeterminada - la aplicación incluía una biblioteca que se puede aprovechar en un ataque de deserialización Se recomienda a los usuarios actualizar a la versión 11.0.3, 10.1.35 o 9.0.98, que corrige el problema."}], "references": [{"url": "https://lists.apache.org/thread/j5fkjv2k477os90nczf2v9l61fb0kkgq", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "http://www.openwall.com/lists/oss-security/2025/03/10/5", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2025/04/msg00003.html", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20250321-0001/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Third Party Advisory"]}, {"url": "https://www.vicarius.io/vsociety/posts/cve-2025-24813-detect-apache-tomcat-rce", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Issue Tracking"]}, {"url": "https://www.vicarius.io/vsociety/posts/cve-2025-24813-mitigate-apache-tomcat-rce", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Issue Tracking"]}, {"url": "https://www.vicarius.io/vsociety/posts/cve-2025-24813-tomcat-detect-vulnerability", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Issue Tracking"]}, {"url": "https://www.vicarius.io/vsociety/posts/cve-2025-24813-tomcat-mitigation-vulnerability", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Issue Tracking"]}, {"url": "https://github.com/absholi7ly/POC-CVE-2025-24813/blob/main/README.md", "source": "134c704f-9b21-4f2e-91b3-4a467353bcc0", "tags": ["Exploit"]}]}
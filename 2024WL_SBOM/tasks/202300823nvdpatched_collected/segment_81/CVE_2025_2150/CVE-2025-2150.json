{"cve_id": "CVE-2025-2150", "published_date": "2025-03-10T08:15:11.917", "last_modified_date": "2025-03-24T14:06:07.687", "descriptions": [{"lang": "en", "value": "The C&Cm@il from HGiga has a Stored Cross-Site Scripting (XSS) vulnerability, allowing remote attackers with regular privileges to send emails containing malicious JavaScript code, which will be executed in the recipient's browser when they view the email."}, {"lang": "es", "value": "El C&amp;Cm@il de HGiga tiene una vulnerabilidad de Cross-Site Scripting (XSS) Almacenado, que permite a atacantes remotos con privilegios regulares enviar correos electrónicos que contienen código JavaScript malicioso, que se ejecutará en el navegador del destinatario cuando vea el correo electrónico."}], "references": [{"url": "https://www.twcert.org.tw/en/cp-139-10005-05e0f-2.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.twcert.org.tw/tw/cp-132-10004-99474-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-21832", "published_date": "2025-03-06T17:15:23.177", "last_modified_date": "2025-03-06T17:15:23.177", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nblock: don't revert iter for -EIOCBQUEUED\n\nblkdev_read_iter() has a few odd checks, like gating the position and\ncount adjustment on whether or not the result is bigger-than-or-equal to\nzero (where bigger than makes more sense), and not checking the return\nvalue of blkdev_direct_IO() before doing an iov_iter_revert(). The\nlatter can lead to attempting to revert with a negative value, which\nwhen passed to iov_iter_revert() as an unsigned value will lead to\nthrowing a WARN_ON() because unroll is bigger than MAX_RW_COUNT.\n\nBe sane and don't revert for -EIOCBQUEUED, like what is done in other\nspots."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bloque: no revertir iter para -EIOCBQUEUED blkdev_read_iter() tiene algunas comprobaciones extrañas, como bloquear el ajuste de posición y conteo en función de si el resultado es o no mayor o igual a cero (donde mayor que tiene más sentido), y no verificar el valor de retorno de blkdev_direct_IO() antes de hacer un iov_iter_revert(). Esto último puede llevar a intentar revertir con un valor negativo, que cuando se pasa a iov_iter_revert() como un valor sin signo conducirá a lanzar un WARN_ON() porque unroll es mayor que MAX_RW_COUNT. Sea sensato y no revierta para -EIOCBQUEUED, como lo que se hace en otros lugares."}], "references": [{"url": "https://git.kernel.org/stable/c/68f16d3034a06661245ecd22f0d586a8b4e7c473", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/6c26619effb1b4cb7d20b4e666ab8f71f6a53ccb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/84671b0630ccb46ae9f1f99a45c7d63ffcd6a474", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a58f136bad29f9ae721a29d98c042fddbee22f77", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/b13ee668e8280ca5b07f8ce2846b9957a8a10853", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
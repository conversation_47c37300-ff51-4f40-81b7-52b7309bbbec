{"cve_id": "CVE-2024-13471", "published_date": "2025-03-05T12:15:35.110", "last_modified_date": "2025-03-05T12:15:35.110", "descriptions": [{"lang": "en", "value": "The DesignThemes Core Features plugin for WordPress is vulnerable to unauthorized access of data due to a missing capability check on the dt_process_imported_file function in all versions up to, and including, 4.7. This makes it possible for unauthenticated attackers to read arbitrary files on the underlying operating system."}, {"lang": "es", "value": "El complemento DesignThemes Core Features para WordPress es vulnerable al acceso no autorizado a los datos debido a una falta de verificación de capacidad en la función dt_process_imported_file en todas las versiones hasta la 4.7 incluida. Esto hace posible que atacantes no autenticados lean archivos arbitrarios en el sistema operativo subyacente."}], "references": [{"url": "https://themeforest.net/item/lms-learning-management-system-education-lms-wordpress-theme/7867581", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1729d0de-1f5f-4349-b592-5841d01ed33a?source=cve", "source": "<EMAIL>", "tags": []}]}
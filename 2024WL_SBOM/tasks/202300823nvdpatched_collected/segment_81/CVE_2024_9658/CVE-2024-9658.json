{"cve_id": "CVE-2024-9658", "published_date": "2025-03-07T09:15:15.993", "last_modified_date": "2025-03-13T14:58:42.383", "descriptions": [{"lang": "en", "value": "The School Management System for Wordpress plugin for WordPress is vulnerable to privilege escalation via account takeover in all versions up to, and including, 93.0.0. This is due to the plugin not properly validating a user's identity prior to updating their details like email and password through the mj_smgt_update_user() and mj_smgt_add_admission() functions, along with a local file inclusion vulnerability. This makes it possible for authenticated attackers, with student-level access and above, to change arbitrary user's email addresses and passwords, including administrators, and leverage that to gain access to their account. This was escalated four months ago after no response to our initial outreach, yet it still vulnerable."}, {"lang": "es", "value": "El complemento School Management System for Wordpress para WordPress es vulnerable a la escalada de privilegios mediante la apropiación de cuentas en todas las versiones hasta la 93.0.0 incluida. Esto se debe a que el complemento no valida correctamente la identidad de un usuario antes de actualizar sus datos, como el correo electrónico y la contraseña, a través de las funciones mj_smgt_update_user() y mj_smgt_add_admission(), junto con una vulnerabilidad de inclusión de archivos locales. Esto hace posible que los atacantes autenticados, con acceso a nivel de estudiante y superior, cambien las direcciones de correo electrónico y las contraseñas de usuarios arbitrarios, incluidos los administradores, y aprovechen eso para obtener acceso a su cuenta. Esto se intensificó hace cuatro meses después de que no recibimos respuesta a nuestro contacto inicial, pero aún es vulnerable."}], "references": [{"url": "https://codecanyon.net/item/school-management-system-for-wordpress/11470032", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b5fd7bca-7754-4f83-8e51-5278e6e8cc78?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2025-27610", "published_date": "2025-03-10T23:15:35.073", "last_modified_date": "2025-03-10T23:15:35.073", "descriptions": [{"lang": "en", "value": "Rack provides an interface for developing web applications in Ruby. Prior to versions 2.2.13, 3.0.14, and 3.1.12, `Rack::Static` can serve files under the specified `root:` even if `urls:` are provided, which may expose other files under the specified `root:` unexpectedly. The vulnerability occurs because `Rack::Static` does not properly sanitize user-supplied paths before serving files. Specifically, encoded path traversal sequences are not correctly validated, allowing attackers to access files outside the designated static file directory. By exploiting this vulnerability, an attacker can gain access to all files under the specified `root:` directory, provided they are able to determine then path of the file. Versions 2.2.13, 3.0.14, and 3.1.12 contain a patch for the issue. Other mitigations include removing usage of `Rack::Static`, or ensuring that `root:` points at a directory path which only contains files which should be accessed publicly. It is likely that a CDN or similar static file server would also mitigate the issue."}, {"lang": "es", "value": "Rack proporciona una interfaz para desarrollar aplicaciones web en Ruby. Antes de las versiones 2.2.13, 3.0.14 y 3.1.12, `Rack::Static` puede servir archivos bajo el `root:` especificado incluso si se proporcionan `urls:`, lo que puede exponer otros archivos bajo el `root:` especificado de forma inesperada. La vulnerabilidad se produce porque `Rack::Static` no depura correctamente las rutas proporcionadas por el usuario antes de servir los archivos. En concreto, las secuencias de path traversal codificadas no se validan correctamente, lo que permite a los atacantes acceder a archivos fuera del directorio de archivos estáticos designado. Al explotar esta vulnerabilidad, un atacante puede obtener acceso a todos los archivos bajo el directorio `root:` especificado, siempre que pueda determinar la ruta del archivo. Las versiones 2.2.13, 3.0.14 y 3.1.12 contienen un parche para el problema. Otras medidas de mitigación incluyen eliminar el uso de `Rack::St<PERSON>` o garantizar que `root:` apunte a una ruta de directorio que solo contenga archivos a los que se debe acceder de forma pública. Es probable que una CDN o un servidor de archivos estáticos similar también mitigue el problema."}], "references": [{"url": "https://github.com/rack/rack/commit/50caab74fa01ee8f5dbdee7bb2782126d20c6583", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/rack/rack/security/advisories/GHSA-7wqh-767x-r66v", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27255", "published_date": "2025-03-10T09:15:11.323", "last_modified_date": "2025-03-12T12:15:14.907", "descriptions": [{"lang": "en", "value": "Use of Hard-coded Credentials vulnerability in GE Vernova EnerVista UR Setup allows Privilege Escalation. The local user database is encrypted using an hardcoded password retrievable by an attacker analyzing the application code."}, {"lang": "es", "value": "La vulnerabilidad de uso de credenciales codificadas de forma rígida en GE Vernova EnerVista UR Setup permite la escalada de privilegios. La base de datos de usuarios local está cifrada mediante una contraseña codificada de forma rígida que un atacante puede recuperar analizando el código de la aplicación."}], "references": [{"url": "https://www.gevernova.com/grid-solutions/app/DownloadFile.aspx?prod=urfamily&type=21&file=76", "source": "<EMAIL>", "tags": []}, {"url": "https://www.nozominetworks.com/labs/vulnerability-advisories-cve-2025-27255", "source": "<EMAIL>", "tags": []}]}
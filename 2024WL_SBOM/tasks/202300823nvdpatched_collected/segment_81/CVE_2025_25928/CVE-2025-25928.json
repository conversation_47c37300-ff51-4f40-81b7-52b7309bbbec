{"cve_id": "CVE-2025-25928", "published_date": "2025-03-11T20:15:17.247", "last_modified_date": "2025-07-07T18:14:16.003", "descriptions": [{"lang": "en", "value": "A Cross-Site Request Forgery (CSRF) in the component /admin/users/user.form of Openmrs 2.4.3 Build 0ff0ed allows attackers to execute arbitrary operations via a crafted request. In this case, an attacker could elevate a low-privileged account to an administrative role by leveraging the CSRF vulnerability at the /admin/users/user.form endpoint."}, {"lang": "es", "value": "Cross-Site Request Forgery (CSRF) en el componente /admin/users/user.form de Openmrs 2.4.3 Build 0ff0ed permite a los atacantes ejecutar operaciones arbitrarias a través de una solicitud GET manipulada."}], "references": [{"url": "https://github.com/johnchd/CVEs/blob/main/OpenMRS/CVE-2025-25928%20-%20CSRF%20PrivEsc.md", "source": "<EMAIL>", "tags": ["Exploit"]}]}
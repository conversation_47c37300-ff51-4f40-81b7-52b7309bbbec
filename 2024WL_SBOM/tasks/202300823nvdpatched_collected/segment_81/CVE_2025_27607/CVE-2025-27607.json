{"cve_id": "CVE-2025-27607", "published_date": "2025-03-07T17:15:22.433", "last_modified_date": "2025-07-01T16:22:57.830", "descriptions": [{"lang": "en", "value": "Python JSON Logger is a JSON Formatter for Python Logging. Between 30 December 2024 and 4 March 2025 Python JSON Logger was vulnerable to RCE through a missing dependency. This occurred because msgspec-python313-pre was deleted by the owner leaving the name open to being claimed by a third party. If the package was claimed, it would allow them RCE on any Python JSON Logger user who installed the development dependencies on Python 3.13 (e.g. pip install python-json-logger[dev]). This issue has been resolved with 3.3.0."}, {"lang": "es", "value": "Python JSON Logger es un formateador JSON para el registro de Python. Entre el 30 de diciembre de 2024 y el 4 de marzo de 2025, Python JSON Logger fue vulnerable a RCE debido a una dependencia faltante. Esto ocurrió porque el propietario eliminó msgspec-python313-pre, lo que dejó el nombre abierto a que un tercero lo reclamara. Si se reclamaba el paquete, les permitiría realizar RCE en cualquier usuario de Python JSON Logger que instalara las dependencias de desarrollo en Python 3.13 (por ejemplo, pip install python-json-logger[dev]). Este problema se ha resuelto con la versión 3.3.0."}], "references": [{"url": "https://github.com/nhairs/python-json-logger/commit/2548e3a2e3cedf6bef3ee7c60c55b7c02d1af11a", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/nhairs/python-json-logger/commit/e7761e56edb980cfab0165e32469d5fd017a5d72", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/nhairs/python-json-logger/security/advisories/GHSA-wmxh-pxcx-9w24", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}
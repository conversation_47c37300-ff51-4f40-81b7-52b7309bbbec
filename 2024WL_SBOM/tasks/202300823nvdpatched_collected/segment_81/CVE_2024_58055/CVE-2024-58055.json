{"cve_id": "CVE-2024-58055", "published_date": "2025-03-06T16:15:51.730", "last_modified_date": "2025-03-25T14:34:54.660", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: gadget: f_tcm: Don't free command immediately\n\nDon't prematurely free the command. Wait for the status completion of\nthe sense status. It can be freed then. Otherwise we will double-free\nthe command."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: gadget: f_tcm: No libere el comando inmediatamente No libere el comando de forma prematura. Espere a que se complete el estado de detección. En ese momento, se puede liberar. De lo contrario, liberaremos el comando dos veces."}], "references": [{"url": "https://git.kernel.org/stable/c/16907219ad6763f401700e1b57b2da4f3e07f047", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/38229c35a6d7875697dfb293356407330cfcd23e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7cb72dc08ed8da60fd6d1f6adf13bf0e6ee0f694", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/929b69810eec132b284ffd19047a85d961df9e4d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/bbb7f49839b57d66ccaf7b5752d9b63d3031dd0a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c225d006a31949d673e646d585d9569bc28feeb9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e6693595bd1b55af62d057a4136a89d5c2ddf0e9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/f0c33e7d387ccbb6870e73a43c558fefede06614", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
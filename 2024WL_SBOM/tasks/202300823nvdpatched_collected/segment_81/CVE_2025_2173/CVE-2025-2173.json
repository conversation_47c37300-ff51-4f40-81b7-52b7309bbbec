{"cve_id": "CVE-2025-2173", "published_date": "2025-03-11T07:15:36.057", "last_modified_date": "2025-03-11T07:15:36.057", "descriptions": [{"lang": "en", "value": "A vulnerability was found in libzvbi up to 0.2.43. It has been classified as problematic. Affected is the function vbi_strndup_iconv_ucs2 of the file src/conv.c. The manipulation of the argument src_length leads to uninitialized pointer. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Upgrading to version 0.2.44 is able to address this issue. The patch is identified as 8def647eea27f7fd7ad33ff79c2d6d3e39948dce. It is recommended to upgrade the affected component. The code maintainer was informed beforehand about the issues. She reacted very fast and highly professional."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en libzvbi hasta la versión 0.2.43. Se ha clasificado como problemática. La función vbi_strndup_iconv_ucs2 del archivo src/conv.c está afectada. La manipulación del argumento src_length da lugar a un puntero no inicializado. Es posible lanzar el ataque de forma remota. El exploit se ha hecho público y puede utilizarse. La actualización a la versión 0.2.44 puede solucionar este problema. El parche se identifica como 8def647eea27f7fd7ad33ff79c2d6d3e39948dce. Se recomienda actualizar el componente afectado. La responsable del código fue informada de antemano sobre los problemas. Reaccionó muy rápido y con gran profesionalidad."}], "references": [{"url": "https://github.com/zapping-vbi/zvbi/commit/8def647eea27f7fd7ad33ff79c2d6d3e39948dce", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/releases/tag/v0.2.44", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/security/advisories/GHSA-g7cg-7gw9-v8cf", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299202", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299202", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.512798", "source": "<EMAIL>", "tags": []}]}
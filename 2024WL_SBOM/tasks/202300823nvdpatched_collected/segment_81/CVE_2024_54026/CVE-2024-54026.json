{"cve_id": "CVE-2024-54026", "published_date": "2025-03-11T15:15:43.307", "last_modified_date": "2025-07-24T18:46:17.740", "descriptions": [{"lang": "en", "value": "An improper neutralization of special elements used in an sql command ('sql injection') in Fortinet FortiSandbox Cloud version 23.4, FortiSandbox at least 4.4.0 through 4.4.6 and 4.2.0 through 4.2.7 and 4.0.0 through 4.0.5 and 3.2.0 through 3.2.4 and 3.1.0 through 3.1.5 and 3.0.0 through 3.0.7 allows attacker to execute unauthorized code or commands via specifically crafted HTTP requests."}, {"lang": "es", "value": "Una neutralización incorrecta de elementos especiales utilizados en un comando sql ('sql injection') en Fortinet FortiSandbox Cloud versión 23.4, FortiSandbox al menos 4.4.0 a 4.4.6 y 4.2.0 a 4.2.7 y 4.0.0 a 4.0.5 y 3.2.0 a 3.2.4 y 3.1.0 a 3.1.5 y 3.0.0 a 3.0.7 permite a un atacante ejecutar código o comandos no autorizados a través de solicitudes HTTP específicamente manipuladas."}], "references": [{"url": "https://fortiguard.fortinet.com/psirt/FG-IR-24-353", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
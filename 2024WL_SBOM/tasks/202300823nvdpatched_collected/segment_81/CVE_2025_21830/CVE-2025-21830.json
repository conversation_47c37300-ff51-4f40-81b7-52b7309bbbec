{"cve_id": "CVE-2025-21830", "published_date": "2025-03-06T17:15:22.943", "last_modified_date": "2025-03-13T13:15:56.810", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nlandlock: Handle weird files\n\nA corrupted filesystem (e.g. bcachefs) might return weird files.\nInstead of throwing a warning and allowing access to such file, treat\nthem as regular files."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: landlock: manejar archivos extraños Un sistema de archivos dañado (por ejemplo, bcachefs) podría devolver archivos extraños. En lugar de lanzar una advertencia y permitir el acceso a dichos archivos, trátelos como archivos normales."}], "references": [{"url": "https://git.kernel.org/stable/c/0fde195a373ab1267e60baa9e1a703a97e7464cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2569e65d2eb6ac1afe6cb6dfae476afee8b6771a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/39bb3d56f1c351e76bb18895d0e73796e653d5c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/49440290a0935f428a1e43a5ac8dc275a647ff80", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/7d6121228959ddf44a4b9b6a177384ac7854e2f9", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a1fccf6b72b56343dd4f2d96b008147f9951eebd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
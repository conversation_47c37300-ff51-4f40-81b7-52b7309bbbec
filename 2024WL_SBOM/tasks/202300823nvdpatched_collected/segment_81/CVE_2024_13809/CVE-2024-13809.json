{"cve_id": "CVE-2024-13809", "published_date": "2025-03-05T10:15:17.313", "last_modified_date": "2025-03-05T10:15:17.313", "descriptions": [{"lang": "en", "value": "The Hero Slider - WordPress Slider Plugin plugin for WordPress is vulnerable to SQL Injection via several parameters in all versions up to, and including, 1.3.5 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Hero Slider - WordPress Slider Plugin para WordPress es vulnerable a la inyección SQL a través de varios parámetros en todas las versiones hasta la 1.3.5 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel de suscriptor y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/hero-slider-wordpress-slider-plugin/13067813", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a692d9c4-66e0-4461-ad13-65e1446106c5?source=cve", "source": "<EMAIL>", "tags": []}]}
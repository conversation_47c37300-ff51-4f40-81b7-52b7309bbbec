{"cve_id": "CVE-2025-27519", "published_date": "2025-03-07T16:15:39.623", "last_modified_date": "2025-03-07T16:15:39.623", "descriptions": [{"lang": "en", "value": "Cognita is a RAG (Retrieval Augmented Generation) Framework for building modular, open source applications for production by TrueFoundry. A path traversal issue exists at /v1/internal/upload-to-local-directory which is enabled when the Local env variable is set to true, such as when Cognita is setup using Docker. Because the docker environment sets up the backend uvicorn server with auto reload enabled, when an attacker overwrites the /app/backend/__init__.py file, the file will automatically be reloaded and executed. This allows an attacker to get remote code execution in the context of the Docker container. This vulnerability is fixed in commit a78bd065e05a1b30a53a3386cc02e08c317d2243."}, {"lang": "es", "value": "Cognita es un framework RAG (Retrieval Augmented Generation) para crear aplicaciones modulares de código abierto para producción de TrueFoundry. Existe un problema de path traversal en /v1/internal/upload-to-local-directory que se habilita cuando la variable de entorno Local se establece en true, como cuando Cognita se configura con Docker. Debido a que el entorno de Docker configura el servidor backend uvicorn con la recarga automática habilitada, cuando un atacante sobrescribe el archivo /app/backend/__init__.py, el archivo se recargará y ejecutará automáticamente. Esto permite que un atacante obtenga la ejecución remota de código en el contexto del contenedor de Docker. Esta vulnerabilidad se corrige en el commit a78bd065e05a1b30a53a3386cc02e08c317d2243."}], "references": [{"url": "https://github.com/truefoundry/cognita/commit/a78bd065e05a1b30a53a3386cc02e08c317d2243", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/truefoundry/cognita/pull/393", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-193_GHSL-2024-194_Cognita/", "source": "<EMAIL>", "tags": []}]}
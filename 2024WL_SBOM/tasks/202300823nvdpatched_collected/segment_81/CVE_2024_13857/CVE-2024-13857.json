{"cve_id": "CVE-2024-13857", "published_date": "2025-03-07T10:15:16.293", "last_modified_date": "2025-03-07T10:15:16.293", "descriptions": [{"lang": "en", "value": "The WPGet API – Connect to any external REST API plugin for WordPress is vulnerable to Server-Side Request Forgery in all versions up to, and including, 2.2.10. This makes it possible for authenticated attackers, with Administrator-level access and above, to make web requests to arbitrary locations originating from the web application which can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento WPGet API – Connect to any external REST API para WordPress es vulnerable a Server-Side Request Forgery en todas las versiones hasta la 2.2.10 incluida. Esto permite que atacantes autenticados, con acceso de nivel de administrador o superior, realicen solicitudes web a ubicaciones arbitrarias que se originan en la aplicación web y que se pueden usar para consultar y modificar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3251647/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/wpgetapi/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/cd2a8e7b-6fca-49f3-ba6d-bdaa418f611a?source=cve", "source": "<EMAIL>", "tags": []}]}
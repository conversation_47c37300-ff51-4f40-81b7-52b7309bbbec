{"cve_id": "CVE-2025-2067", "published_date": "2025-03-07T05:15:17.527", "last_modified_date": "2025-05-14T16:14:48.690", "descriptions": [{"lang": "en", "value": "A vulnerability was found in projectworlds Life Insurance Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /search.php. The manipulation of the argument key leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en projectworlds Life Insurance Management System 1.0 y se ha clasificado como crítica. Este problema afecta a algunos procesos desconocidos del archivo /search.php. La manipulación de la clave del argumento provoca una inyección SQL. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/9", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298823", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298823", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514762", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
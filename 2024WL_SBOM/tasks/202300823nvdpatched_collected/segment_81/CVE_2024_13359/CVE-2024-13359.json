{"cve_id": "CVE-2024-13359", "published_date": "2025-03-08T10:15:09.977", "last_modified_date": "2025-03-13T15:15:40.237", "descriptions": [{"lang": "en", "value": "The Product Input Fields for WooCommerce plugin for WordPress is vulnerable to arbitrary file uploads due to insufficient file type validation in the add_product_input_fields_to_order_item_meta() function in all versions up to, and including, 1.12.0. This may make it possible for unauthenticated attackers to upload arbitrary files on the affected site's server which may make remote code execution possible. Please note that by default the plugin is only vulnerable to a double extension file upload attack, unless an administrators leaves the accepted file extensions field blank which can make .php file uploads possible. Please note 1.12.2 was mistakenly marked as patched while 1.12.1 was marked as vulnerable for a short period of time, this is not the case and 1.12.1 is fully patched."}, {"lang": "es", "value": "El complemento Product Input Fields for WooCommerce para WordPress es vulnerable a la carga de archivos arbitrarios debido a una validación insuficiente del tipo de archivo en la función add_product_input_fields_to_order_item_meta() en todas las versiones hasta la 1.12.1 incluida. Esto puede permitir que atacantes no autenticados carguen archivos arbitrarios en el servidor del sitio afectado, lo que puede hacer posible la ejecución remota de código. Tenga en cuenta que, de forma predeterminada, el complemento solo es vulnerable a un ataque de carga de archivos de doble extensión, a menos que un administrador deje en blanco el campo de extensiones de archivo aceptadas, lo que puede hacer posible la carga de archivos .php."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/product-input-fields-for-woocommerce/tags/-1.8.2/includes/class-alg-wc-pif-main.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3234567%40product-input-fields-for-woocommerce&new=3234567%40product-input-fields-for-woocommerce&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3250201%40product-input-fields-for-woocommerce&new=3250201%40product-input-fields-for-woocommerce&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a9c08f2e-bffd-40a6-89f3-559cb34f4395?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
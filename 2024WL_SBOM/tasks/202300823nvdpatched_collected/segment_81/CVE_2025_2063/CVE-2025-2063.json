{"cve_id": "CVE-2025-2063", "published_date": "2025-03-07T04:15:10.060", "last_modified_date": "2025-05-14T16:15:16.297", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in projectworlds Life Insurance Management System 1.0. Affected by this vulnerability is an unknown functionality of the file /deleteNominee.php. The manipulation of the argument nominee_id leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en projectworlds Life Insurance Management System 1.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /deleteNominee.php. La manipulación del argumento nominee_id provoca una inyección SQL. El ataque se puede ejecutar de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298819", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298819", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514749", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-1383", "published_date": "2025-03-06T12:15:35.937", "last_modified_date": "2025-03-19T20:47:28.020", "descriptions": [{"lang": "en", "value": "The Podlove Podcast Publisher plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 4.2.2. This is due to missing or incorrect nonce validation on the ajax_transcript_delete() function. This makes it possible for unauthenticated attackers to delete arbitrary episode transcripts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Podlove Podcast Publisher para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 4.2.2 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la función ajax_transcript_delete(). Esto permite que atacantes no autenticados eliminen transcripciones de episodios arbitrarios a través de una solicitud falsificada, siempre que puedan engañar a un administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/podlove-podcasting-plugin-for-wordpress/tags/4.2.0/lib/modules/transcripts/transcripts.php#L223", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3246867/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/podlove-podcasting-plugin-for-wordpress/#developers", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/00a95ae7-3c58-4e5e-aaef-c04d1dacf27f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
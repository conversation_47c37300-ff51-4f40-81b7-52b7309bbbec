{"cve_id": "CVE-2024-58052", "published_date": "2025-03-06T16:15:51.367", "last_modified_date": "2025-03-25T15:00:52.263", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/amdgpu: Fix potential NULL pointer dereference in atomctrl_get_smc_sclk_range_table\n\nThe function atomctrl_get_smc_sclk_range_table() does not check the return\nvalue of smu_atom_get_data_table(). If smu_atom_get_data_table() fails to\nretrieve SMU_Info table, it returns NULL which is later dereferenced.\n\nFound by Linux Verification Center (linuxtesting.org) with SVACE.\n\nIn practice this should never happen as this code only gets called\non polaris chips and the vbios data table will always be present on\nthose chips."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/amdgpu: Arreglar posible desreferencia de puntero NULL en atomctrl_get_smc_sclk_range_table La función atomctrl_get_smc_sclk_range_table() no comprueba el valor de retorno de smu_atom_get_data_table(). Si smu_atom_get_data_table() no puede recuperar la tabla SMU_Info, devuelve NULL que luego se desreferencia. Encontrado por Linux Verification Center (linuxtesting.org) con SVACE. En la práctica, esto nunca debería suceder ya que este código solo se llama en chips polaris y la tabla de datos vbios siempre estará presente en esos chips."}], "references": [{"url": "https://git.kernel.org/stable/c/0b97cd8a61b2b40fd73cf92a4bb2256462d22adb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/2396bc91935c6da0588ce07850d07897974bd350", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/357445e28ff004d7f10967aa93ddb4bffa5c3688", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/396350adf0e5ad4bf05f01e4d79bfb82f0f6c41a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6a30634a2e0f1dd3c6b39fd0f114c32893a9907a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/a713ba7167c2d74c477dd7764dbbdbe3199f17f4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ae522ad211ec4b72eaf742b25f24b0a406afcba1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/c47066ed7c8f3b320ef87fa6217a2b8b24e127cc", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
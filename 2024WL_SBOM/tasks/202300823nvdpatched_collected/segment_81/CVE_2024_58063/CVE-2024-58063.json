{"cve_id": "CVE-2024-58063", "published_date": "2025-03-06T16:15:52.583", "last_modified_date": "2025-03-25T14:37:12.963", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: rtlwifi: fix memory leaks and invalid access at probe error path\n\nDeinitialize at reverse order when probe fails.\n\nWhen init_sw_vars fails, rtl_deinit_core should not be called, specially\nnow that it destroys the rtl_wq workqueue.\n\nAnd call rtl_pci_deinit and deinit_sw_vars, otherwise, memory will be\nleaked.\n\nRemove pci_set_drvdata call as it will already be cleaned up by the core\ndriver code and could lead to memory leaks too. cf. commit 8d450935ae7f\n(\"wireless: rtlwifi: remove unnecessary pci_set_drvdata()\") and\ncommit 3d86b93064c7 (\"rtlwifi: Fix PCI probe error path orphaned memory\")."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: rtlwifi: corrige fugas de memoria y acceso no válido en la ruta de error de la sonda Desinicializar en orden inverso cuando la sonda falla. Cuando init_sw_vars falla, no se debe llamar a rtl_deinit_core, especialmente ahora que destruye la cola de trabajo rtl_wq. Y llame a rtl_pci_deinit y deinit_sw_vars, de lo contrario, se perderá memoria. Elimine la llamada a pci_set_drvdata ya que ya estará limpiada por el código del controlador central y también podría provocar fugas de memoria. cf. commit 8d450935ae7f (\"wireless: rtlwifi: eliminar pci_set_drvdata() innecesario\") y commit 3d86b93064c7 (\"rtlwifi: corregir la memoria huérfana en la ruta de error de la sonda PCI\")."}], "references": [{"url": "https://git.kernel.org/stable/c/32acebca0a51f5e372536bfdc0d7d332ab749013", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/455e0f40b5352186a9095f2135d5c89255e7c39a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/624cea89a0865a2bc3e00182a6b0f954a94328b4", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/6b76bab5c257463302c9e97f5d84d524457468eb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/85b67b4c4a0f8a6fb20cf4ef7684ff2b0cf559df", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/b96371339fd9cac90f5ee4ac17ee5c4cbbdfa6f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e7ceefbfd8d447abc8aca8ab993a942803522c06", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/ee0b0d7baa8a6d42c7988f6e50c8f164cdf3fa47", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
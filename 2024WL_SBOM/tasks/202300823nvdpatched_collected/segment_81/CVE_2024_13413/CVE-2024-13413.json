{"cve_id": "CVE-2024-13413", "published_date": "2025-03-11T05:15:37.083", "last_modified_date": "2025-03-11T05:15:37.083", "descriptions": [{"lang": "en", "value": "The ProductDyno plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the ‘res’ parameter in all versions up to, and including, 1.0.24 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts into pages that execute if they can successfully trick a user into performing an action such as clicking on a link. This vulnerability is potentially a duplicate of CVE-2025-22320."}, {"lang": "es", "value": "El complemento ProductDyno para WordPress es vulnerable a Cross-Site Scripting reflejado a través del parámetro 'res' en todas las versiones hasta la 1.0.24 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes no autenticados inyecten secuencias de comandos web arbitrarias en páginas que se ejecutan si logran engañar a un usuario para que realice una acción, como hacer clic en un enlace. Esta vulnerabilidad es potencialmente un duplicado de CVE-2025-22320."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/productdyno/trunk/admin/partials/productdyno-admin-display.php#L81", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3251678/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/productdyno/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fdc1289a-abd1-43db-89b7-3e81878a0f9a?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-58084", "published_date": "2025-03-06T17:15:21.890", "last_modified_date": "2025-03-24T18:30:09.320", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nfirmware: qcom: scm: Fix missing read barrier in qcom_scm_get_tzmem_pool()\n\nCommit 2e4955167ec5 (\"firmware: qcom: scm: Fix __scm and waitq\ncompletion variable initialization\") introduced a write barrier in probe\nfunction to store global '__scm' variable.  We all known barriers are\npaired (see memory-barriers.txt: \"Note that write barriers should\nnormally be paired with read or address-dependency barriers\"), therefore\naccessing it from concurrent contexts requires read barrier.  Previous\ncommit added such barrier in qcom_scm_is_available(), so let's use that\ndirectly.\n\nLack of this read barrier can result in fetching stale '__scm' variable\nvalue, NULL, and dereferencing it.\n\nNote that barrier in qcom_scm_is_available() satisfies here the control\ndependency."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: firmware: qcom: scm: Fix missing read barrier in qcom_scm_get_tzmem_pool() el commit 2e4955167ec5 (\"firmware: qcom: scm: Fix __scm and waitq ending variable initialization\") introdujo una barrera de escritura en la función de sondeo para almacenar la variable global '__scm'. Todos sabemos que las barreras están emparejadas (consulte memory-barriers.txt: \"Note que las barreras de escritura normalmente deben emparejarse con barreras de lectura o de dependencia de dirección\"), por lo tanto, acceder a ellas desde contextos concurrentes requiere una barrera de lectura. Una confirmación anterior agregó dicha barrera en qcom_scm_is_available(), así que usémosla directamente. La falta de esta barrera de lectura puede resultar en la obtención del valor de la variable '__scm' obsoleto, NULL, y su desreferenciación. Tenga en cuenta que la barrera en qcom_scm_is_available() satisface aquí la dependencia de control."}], "references": [{"url": "https://git.kernel.org/stable/c/b628510397b5cafa1f5d3e848a28affd1c635302", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e03db7c1255ebabba5e1a447754faeb138de15a2", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/fee921e3c641f64185abee83f9a6e65f0b380682", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2024-13906", "published_date": "2025-03-07T08:15:37.467", "last_modified_date": "2025-03-07T08:15:37.467", "descriptions": [{"lang": "en", "value": "The Gallery by BestWebSoft – Customizable Image and Photo Galleries for WordPress plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 4.7.3 via deserialization of untrusted input in the 'import_gallery_from_csv' function. This makes it possible for authenticated attackers, with Administrator-level access and above, to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present."}, {"lang": "es", "value": "El complemento The Gallery by BestWebSoft – Customizable Image and Photo Galleries for WordPress para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 4.7.3 incluida, a través de la deserialización de entradas no confiables en la función 'import_gallery_from_csv'. Esto hace posible que atacantes autenticados, con acceso de nivel de administrador y superior, inyecten un objeto PHP. No hay ninguna cadena POP conocida presente en el software vulnerable, lo que significa que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si hay una cadena POP presente a través de un complemento o tema adicional instalado en el sistema de destino, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código según la cadena POP presente."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/gallery-plugin/tags/4.7.3/gallery-plugin.php#L292", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3249573/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/76c5559d-f9dd-43cf-8c8e-07188b4edf7f?source=cve", "source": "<EMAIL>", "tags": []}]}
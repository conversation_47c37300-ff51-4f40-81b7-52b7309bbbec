{"cve_id": "CVE-2025-2034", "published_date": "2025-03-06T18:15:43.230", "last_modified_date": "2025-05-07T16:39:30.977", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Pre-School Enrollment System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /admin/edit-class.php?cid=1. The manipulation of the argument classname/capacity/classtiming leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Pre-School Enrollment System 1.0 y se ha clasificado como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/edit-class.php?cid=1. La manipulación del argumento classname/capacity conduce a una inyección SQL. El ataque se puede ejecutar de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/wangCCTV/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298777", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298777", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512292", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
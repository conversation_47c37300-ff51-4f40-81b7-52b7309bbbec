{"cve_id": "CVE-2024-13552", "published_date": "2025-03-07T10:15:14.493", "last_modified_date": "2025-03-07T10:15:14.493", "descriptions": [{"lang": "en", "value": "The SupportCandy – Helpdesk & Customer Support Ticket System plugin for WordPress is vulnerable to Insecure Direct Object Reference in all versions up to, and including, 3.3.0 via file upload due to missing validation on a user controlled key. This makes it possible for authenticated attackers to download attachments for support tickets that don't belong to them. If an admin enables tickets for guests, this can be exploited by unauthenticated attackers."}, {"lang": "es", "value": "El complemento SupportCandy – Helpdesk &amp; Customer Support Ticket System para WordPress es vulnerable a una referencia directa a objetos insegura en todas las versiones hasta la 3.3.0 incluida a través de la carga de archivos debido a la falta de validación en una clave controlada por el usuario. Esto hace posible que atacantes autenticados descarguen archivos adjuntos para tickets de soporte que no les pertenecen. Si un administrador habilita tickets para invitados, esto puede ser explotado por atacantes no autenticados."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/supportcandy/trunk/includes/admin/tickets/class-wpsc-new-ticket.php#L395", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3235142/supportcandy/trunk?old=3188306&old_path=%2Fsupportcandy%2Ftrunk", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/13f87248-cc0b-4351-b79d-6efc5190b021?source=cve", "source": "<EMAIL>", "tags": []}]}
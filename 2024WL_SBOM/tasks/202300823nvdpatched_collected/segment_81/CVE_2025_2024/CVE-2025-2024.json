{"cve_id": "CVE-2025-2024", "published_date": "2025-03-07T20:15:39.023", "last_modified_date": "2025-03-07T20:15:39.023", "descriptions": [{"lang": "en", "value": "Trimble SketchUp SKP File Parsing Uninitialized Variable Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Trimble SketchUp. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of SKP files. The issue results from the lack of proper initialization of memory prior to accessing it. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-25210."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código en variables no inicializadas al analizar archivos SKP de Trimble SketchUp. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Trimble SketchUp. Se requiere la interacción del usuario para explotar esta vulnerabilidad, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica existe en el análisis de archivos SKP. El problema es el resultado de la falta de inicialización adecuada de la memoria antes de acceder a ella. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Era ZDI-CAN-25210."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-111/", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-24985", "published_date": "2025-03-11T17:16:34.303", "last_modified_date": "2025-05-29T16:31:56.417", "descriptions": [{"lang": "en", "value": "Integer overflow or wraparound in Windows Fast FAT Driver allows an unauthorized attacker to execute code locally."}, {"lang": "es", "value": "El desbordamiento de enteros o el envoltura en el controlador Fast FAT de Windows permite que un atacante no autorizado ejecute código localmente."}], "references": [{"url": "https://msrc.microsoft.com/update-guide/vulnerability/CVE-2025-24985", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://www.vicarius.io/vsociety/posts/cve-2025-24985-integer-overflow-vulnerability-in-microsoft-windows-fast-fat-driver-detection-script", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Exploit"]}, {"url": "https://www.vicarius.io/vsociety/posts/cve-2025-24985-integer-overflow-vulnerability-in-microsoft-windows-fast-fat-driver-mitigation-script", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": ["Mitigation"]}]}
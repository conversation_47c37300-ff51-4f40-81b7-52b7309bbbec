{"cve_id": "CVE-2024-51320", "published_date": "2025-03-11T15:15:42.447", "last_modified_date": "2025-05-28T14:50:57.723", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerability in Zucchetti Ad Hoc Infinity 2.4 allows an authenticated attacker to achieve Remote Code Execution via the /servlet/gsdm_fsave_htmltmp, /servlet/gsdm_btlk_openfile components"}, {"lang": "es", "value": "La vulnerabilidad de cross site scripting en Zucchetti Ad Hoc Infinity 2.4 permite que un atacante autenticado logre la ejecución remota de código a través de los componentes /servlet/gsdm_fsave_htmltmp y /servlet/gsdm_btlk_openfile."}], "references": [{"url": "https://members.backbox.org/zucchetti-ad-hoc-infinity-multiple-vulnerabilities/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
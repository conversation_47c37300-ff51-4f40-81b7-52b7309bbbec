{"cve_id": "CVE-2024-12609", "published_date": "2025-03-07T09:15:14.860", "last_modified_date": "2025-07-07T18:19:17.110", "descriptions": [{"lang": "en", "value": "The School Management System for Wordpress plugin for WordPress is vulnerable to SQL Injection via the 'view-attendance' page in all versions up to, and including, 92.0.0 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query in the mj_smgt_view_student_attendance() function.  This makes it possible for authenticated attackers, with Student-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento School Management System for Wordpress para WordPress es vulnerable a la inyección SQL a través de la página \"view-attendance\" en todas las versiones hasta la 92.0.0 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente en la función mj_smgt_view_student_attendance(). Esto hace posible que los atacantes autenticados, con acceso de nivel de estudiante y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/school-management-system-for-wordpress/11470032", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a8790df5-7228-4854-870c-1e6d3d0cfbaa?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
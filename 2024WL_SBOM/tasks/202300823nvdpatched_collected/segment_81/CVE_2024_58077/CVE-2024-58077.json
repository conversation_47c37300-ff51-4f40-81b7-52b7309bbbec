{"cve_id": "CVE-2024-58077", "published_date": "2025-03-06T17:15:21.123", "last_modified_date": "2025-06-19T13:15:51.193", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nASoC: soc-pcm: don't use soc_pcm_ret() on .prepare callback\n\ncommit 1f5664351410 (\"ASoC: lower \"no backend DAIs enabled for ... Port\"\nlog severity\") ignores -EINVAL error message on common soc_pcm_ret().\nIt is used from many functions, ignoring -EINVAL is over-kill.\n\nThe reason why -EINVAL was ignored was it really should only be used\nupon invalid parameters coming from userspace and in that case we don't\nwant to log an error since we do not want to give userspace a way to do\na denial-of-service attack on the syslog / diskspace.\n\nSo don't use soc_pcm_ret() on .prepare callback is better idea."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ASoC: soc-pcm: no usar soc_pcm_ret() en la devolución de llamada .prepare commit 1f5664351410 (\"ASoC: menor severidad del registro \"no backend DAIs enabled for ... Port\") ignora el mensaje de error -EINVAL en soc_pcm_ret() común. Se usa desde muchas funciones, ignorar -EINVAL es exagerado. La razón por la que se ignoró -EINVAL fue que realmente solo debería usarse en parámetros no válidos que provengan del espacio de usuario y, en ese caso, no queremos registrar un error ya que no queremos darle al espacio de usuario una forma de realizar un ataque de denegación de servicio en el syslog / espacio de disco. Por lo tanto, no usar soc_pcm_ret() en la devolución de llamada .prepare es una mejor idea."}], "references": [{"url": "https://git.kernel.org/stable/c/301c26a018acb94dd537a4418cefa0f654500c6f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/79b8c7c93beb4f5882c9ee5b9ba73354fa4bc9ee", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/8ec4e8c8e142933eaa8e1ed87168831069250e4e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/90778f31efdf44622065ebbe8d228284104bd26f", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
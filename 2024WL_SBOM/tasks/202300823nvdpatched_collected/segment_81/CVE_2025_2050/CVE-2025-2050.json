{"cve_id": "CVE-2025-2050", "published_date": "2025-03-07T00:15:35.243", "last_modified_date": "2025-05-28T15:04:53.617", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul User Registration & Login and User Management System 3.3. Affected by this vulnerability is an unknown functionality of the file /login.php. The manipulation of the argument email leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul User Registration &amp; Login and User Management System 3.3. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /login.php. La manipulación del argumento email conduce a una inyección SQL. El ataque puede ejecutarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/guttlefish/vul/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298801", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298801", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514115", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
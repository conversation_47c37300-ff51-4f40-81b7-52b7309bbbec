{"cve_id": "CVE-2025-2039", "published_date": "2025-03-06T20:15:38.753", "last_modified_date": "2025-05-13T20:57:18.117", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in code-projects Blood Bank Management System 1.0. Affected is an unknown function of the file /admin/delete_members.php. The manipulation of the argument member_id leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en code-projects Blood Bank Management System 1.0. Se ve afectada una función desconocida del archivo /admin/delete_members.php. La manipulación del argumento member_id provoca una inyección SQL. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede ser utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/intercpt/XSS1/blob/main/SQL4.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298782", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298782", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512564", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
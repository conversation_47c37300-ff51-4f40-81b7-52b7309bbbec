{"cve_id": "CVE-2025-1475", "published_date": "2025-03-07T07:15:23.343", "last_modified_date": "2025-03-07T07:15:23.343", "descriptions": [{"lang": "en", "value": "The WPCOM Member plugin for WordPress is vulnerable to authentication bypass in all versions up to, and including, 1.7.5. This is due to insufficient verification on the 'user_phone' parameter when logging in. This makes it possible for unauthenticated attackers to log in as any existing user on the site, such as an administrator, if SMS login is enabled."}, {"lang": "es", "value": "El complemento WPCOM Member para WordPress es vulnerable a la omisión de la autenticación en todas las versiones hasta la 1.7.5 incluida. Esto se debe a una verificación insuficiente en el parámetro 'user_phone' al iniciar sesión. Esto hace posible que atacantes no autenticados inicien sesión como cualquier usuario existente en el sitio, como un administrador, si el inicio de sesión por SMS está habilitado."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wpcom-member/tags/1.7.1/includes/form-validation.php#L110", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3248208/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/05178bf3-3040-41aa-ba43-779376d30298?source=cve", "source": "<EMAIL>", "tags": []}]}
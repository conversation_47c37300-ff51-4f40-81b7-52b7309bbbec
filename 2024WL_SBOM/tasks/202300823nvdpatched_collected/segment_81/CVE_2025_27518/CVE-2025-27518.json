{"cve_id": "CVE-2025-27518", "published_date": "2025-03-07T16:15:39.187", "last_modified_date": "2025-03-07T16:15:39.187", "descriptions": [{"lang": "en", "value": "Cognita is a RAG (Retrieval Augmented Generation) Framework for building modular, open source applications for production by TrueFoundry. An insecure CORS configuration in the Cognita backend server allows arbitrary websites to send cross site requests to the application. This vulnerability is fixed in commit 75079c3d3cf376381489b9a82ee46c69024e1a15."}, {"lang": "es", "value": "Cognita es un framework RAG (Retrieval Augmented Generation) para crear aplicaciones modulares de código abierto para producción de TrueFoundry. Una configuración CORS insegura en el servidor backend de Cognita permite que sitios web arbitrarios envíen solicitudes entre sitios a la aplicación. Esta vulnerabilidad se corrigió en la confirmación 75079c3d3cf376381489b9a82ee46c69024e1a15."}], "references": [{"url": "https://github.com/truefoundry/cognita/commit/75079c3d3cf376381489b9a82ee46c69024e1a15", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/truefoundry/cognita/pull/424", "source": "<EMAIL>", "tags": []}, {"url": "https://securitylab.github.com/advisories/GHSL-2024-193_GHSL-2024-194_Cognita/", "source": "<EMAIL>", "tags": []}]}
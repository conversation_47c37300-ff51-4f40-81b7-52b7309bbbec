{"cve_id": "CVE-2025-27508", "published_date": "2025-03-05T22:15:35.867", "last_modified_date": "2025-03-07T18:15:48.033", "descriptions": [{"lang": "en", "value": "Emissary is a P2P based data-driven workflow engine. The ChecksumCalculator class within allows for hashing and checksum generation, but it includes or defaults to algorithms that are no longer recommended for secure cryptographic use cases (e.g., SHA-1, CRC32, and SSDEEP). These algorithms, while possibly valid for certain non-security-critical tasks, can expose users to security risks if used in scenarios where strong cryptographic guarantees are required. This issue is fixed in 8.24.0."}, {"lang": "es", "value": "Emissary es un motor de flujo de trabajo basado en datos P2P. La clase ChecksumCalculator permite la generación de sumas de comprobación y hash, pero incluye o utiliza de forma predeterminada algoritmos que ya no se recomiendan para casos de uso criptográfico seguro (por ejemplo, SHA-1, CRC32 y SSDEEP). Estos algoritmos, si bien pueden ser válidos para ciertas tareas no críticas para la seguridad, pueden exponer a los usuarios a riesgos de seguridad si se utilizan en escenarios en los que se requieren garantías criptográficas sólidas. Este problema se solucionó en 8.24.0."}], "references": [{"url": "https://github.com/NationalSecurityAgency/emissary/commit/da3a81a8977577597ff2a944820a5ae4e9762368", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/NationalSecurityAgency/emissary/security/advisories/GHSA-hw43-fcmm-3m5g", "source": "<EMAIL>", "tags": []}]}
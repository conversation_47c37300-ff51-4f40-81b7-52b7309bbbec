{"cve_id": "CVE-2025-2037", "published_date": "2025-03-06T19:15:28.540", "last_modified_date": "2025-05-13T20:58:11.033", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Blood Bank Management System 1.0. It has been declared as critical. This vulnerability affects unknown code of the file /user_dashboard/delete_requester.php. The manipulation of the argument requester_id leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en code-projects Blood Bank Management System 1.0. Se ha declarado como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /user_dashboard/delete_requester.php. La manipulación del argumento requester_id conduce a una inyección SQL. El ataque puede iniciarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/intercpt/XSS1/blob/main/SQL1.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298780", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298780", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512550", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
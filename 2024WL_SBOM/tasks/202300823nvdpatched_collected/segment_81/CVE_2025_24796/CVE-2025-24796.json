{"cve_id": "CVE-2025-24796", "published_date": "2025-03-06T19:15:26.953", "last_modified_date": "2025-03-06T19:15:26.953", "descriptions": [{"lang": "en", "value": "Collabora Online is a collaborative online office suite based on LibreOffice. Macro support is disabled by default in Collabora Online, but can be enabled by an administrator. Collabora Online typically hosts each document instance within a jail and is allowed to download content from locations controlled by the net.lok_allow configuration option, which by default include the private IP ranges to enable access to the local network. If enabled, macros were allowed run executable binaries. By combining an ability to host executables, typically in the local network, in an allowed accessible location, with a macro enabled Collabora Online, it was then possible to install arbitrary binaries within the jail and execute them. These executables are restricted to the same jail file system and user as the document instance but can be used to bypass the additional limits on what network hosts are accessible and provide more flexibility as a platform for further attempts. This is issue is fixed in **********, 23.05.19, 22.05.25 and later macros."}, {"lang": "es", "value": "Collabora Online es una suite de oficina colaborativa en línea basada en LibreOffice. El soporte de macros está deshabilitado de manera predeterminada en Collabora Online, pero puede ser habilitado por un administrador. Collabora Online normalmente aloja cada instancia de documento dentro de una cárcel y se le permite descargar contenido desde ubicaciones controladas por la opción de configuración net.lok_allow, que de manera predeterminada incluye los rangos de IP privados para habilitar el acceso a la red local. Si se habilitaba, se permitía que las macros ejecutaran binarios ejecutables. Al combinar la capacidad de alojar ejecutables, generalmente en la red local, en una ubicación accesible permitida, con un Collabora Online con macros habilitadas, fue posible instalar binarios arbitrarios dentro de la cárcel y ejecutarlos. Estos ejecutables están restringidos al mismo sistema de archivos de la cárcel y al mismo usuario que la instancia del documento, pero se pueden usar para eludir los límites adicionales sobre qué hosts de red son accesibles y brindar más flexibilidad como plataforma para intentos posteriores. Este problema se solucionó en las macros **********, 23.05.19, 22.05.25 y posteriores."}], "references": [{"url": "https://github.com/CollaboraOnline/online/security/advisories/GHSA-4jjq-vgqp-qw45", "source": "<EMAIL>", "tags": []}]}
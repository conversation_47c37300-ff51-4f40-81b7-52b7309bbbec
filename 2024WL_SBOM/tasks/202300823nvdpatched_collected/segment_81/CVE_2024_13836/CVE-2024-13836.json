{"cve_id": "CVE-2024-13836", "published_date": "2025-03-11T06:15:26.070", "last_modified_date": "2025-05-06T16:06:04.827", "descriptions": [{"lang": "en", "value": "The WP Login Control WordPress plugin through 2.0.0 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin."}, {"lang": "es", "value": "El complemento WP Login Control de WordPress hasta la versión 2.0.0 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un error de Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios altos, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/26c2026a-1490-4a0f-9d1d-54ee43c69f22/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
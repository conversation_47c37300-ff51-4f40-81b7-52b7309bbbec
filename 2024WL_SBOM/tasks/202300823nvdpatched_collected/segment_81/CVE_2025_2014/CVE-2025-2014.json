{"cve_id": "CVE-2025-2014", "published_date": "2025-03-11T21:15:52.613", "last_modified_date": "2025-07-07T18:07:22.490", "descriptions": [{"lang": "en", "value": "Ashlar-Vellum Cobalt VS File Parsing Use of Uninitialized Variable Remote Code Execution Vulnerability. This vulnerability allows remote attackers to execute arbitrary code on affected installations of Ashlar-Vellum Cobalt. User interaction is required to exploit this vulnerability in that the target must visit a malicious page or open a malicious file.\n\nThe specific flaw exists within the parsing of VS files. The issue results from the lack of proper initialization of memory prior to accessing it. An attacker can leverage this vulnerability to execute code in the context of the current process. Was ZDI-CAN-25235."}, {"lang": "es", "value": "Vulnerabilidad de ejecución remota de código en el análisis de archivos VS mediante el uso de variables no inicializadas de Ashlar-Vellum Cobalt. Esta vulnerabilidad permite a atacantes remotos ejecutar código arbitrario en las instalaciones afectadas de Ashlar-Vellum Cobalt. Para explotar esta vulnerabilidad, se requiere la interacción del usuario, ya que el objetivo debe visitar una página maliciosa o abrir un archivo malicioso. La falla específica se encuentra en el análisis de archivos VS. El problema se debe a la falta de inicialización correcta de la memoria antes de acceder a ella. Un atacante puede aprovechar esta vulnerabilidad para ejecutar código en el contexto del proceso actual. Anteriormente, se denominaba ZDI-CAN-25235."}], "references": [{"url": "https://www.zerodayinitiative.com/advisories/ZDI-25-115/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
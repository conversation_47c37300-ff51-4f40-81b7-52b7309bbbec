{"cve_id": "CVE-2025-21839", "published_date": "2025-03-07T09:15:16.923", "last_modified_date": "2025-05-09T08:15:19.130", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nKVM: x86: Load DR6 with guest value only before entering .vcpu_run() loop\n\nMove the conditional loading of hardware DR6 with the guest's DR6 value\nout of the core .vcpu_run() loop to fix a bug where KVM can load hardware\nwith a stale vcpu->arch.dr6.\n\nWhen the guest accesses a DR and host userspace isn't debugging the guest,\nKVM disables DR interception and loads the guest's values into hardware on\nVM-Enter and saves them on VM-Exit.  This allows the guest to access DRs\nat will, e.g. so that a sequence of DR accesses to configure a breakpoint\nonly generates one VM-Exit.\n\nFor DR0-DR3, the logic/behavior is identical between VMX and SVM, and also\nidentical between KVM_DEBUGREG_BP_ENABLED (userspace debugging the guest)\nand KVM_DEBUGREG_WONT_EXIT (guest using DRs), and so KVM handles loading\nDR0-DR3 in common code, _outside_ of the core kvm_x86_ops.vcpu_run() loop.\n\nBut for DR6, the guest's value doesn't need to be loaded into hardware for\nKVM_DEBUGREG_BP_ENABLED, and SVM provides a dedicated VMCB field whereas\nVMX requires software to manually load the guest value, and so loading the\nguest's value into DR6 is handled by {svm,vmx}_vcpu_run(), i.e. is done\n_inside_ the core run loop.\n\nUnfortunately, saving the guest values on VM-Exit is initiated by common\nx86, again outside of the core run loop.  If the guest modifies DR6 (in\nhardware, when DR interception is disabled), and then the next VM-Exit is\na fastpath VM-Exit, KVM will reload hardware DR6 with vcpu->arch.dr6 and\nclobber the guest's actual value.\n\nThe bug shows up primarily with nested VMX because KVM handles the VMX\npreemption timer in the fastpath, and the window between hardware DR6\nbeing modified (in guest context) and DR6 being read by guest software is\norders of magnitude larger in a nested setup.  E.g. in non-nested, the\nVMX preemption timer would need to fire precisely between #DB injection\nand the #DB handler's read of DR6, whereas with a KVM-on-KVM setup, the\nwindow where hardware DR6 is \"dirty\" extends all the way from L1 writing\nDR6 to VMRESUME (in L1).\n\n    L1's view:\n    ==========\n    <L1 disables DR interception>\n           CPU 0/KVM-7289    [023] d....  2925.640961: kvm_entry: vcpu 0\n A:  L1 Writes DR6\n           CPU 0/KVM-7289    [023] d....  2925.640963: <hack>: Set DRs, DR6 = 0xffff0ff1\n\n B:        CPU 0/KVM-7289    [023] d....  2925.640967: kvm_exit: vcpu 0 reason EXTERNAL_INTERRUPT intr_info 0x800000ec\n\n D: L1 reads DR6, arch.dr6 = 0\n           CPU 0/KVM-7289    [023] d....  2925.640969: <hack>: Sync DRs, DR6 = 0xffff0ff0\n\n           CPU 0/KVM-7289    [023] d....  2925.640976: kvm_entry: vcpu 0\n    L2 reads DR6, L1 disables DR interception\n           CPU 0/KVM-7289    [023] d....  2925.640980: kvm_exit: vcpu 0 reason DR_ACCESS info1 0x0000000000000216\n           CPU 0/KVM-7289    [023] d....  2925.640983: kvm_entry: vcpu 0\n\n           CPU 0/KVM-7289    [023] d....  2925.640983: <hack>: Set DRs, DR6 = 0xffff0ff0\n\n    L2 detects failure\n           CPU 0/KVM-7289    [023] d....  2925.640987: kvm_exit: vcpu 0 reason HLT\n    L1 reads DR6 (confirms failure)\n           CPU 0/KVM-7289    [023] d....  2925.640990: <hack>: Sync DRs, DR6 = 0xffff0ff0\n\n    L0's view:\n    ==========\n    L2 reads DR6, arch.dr6 = 0\n          CPU 23/KVM-5046    [001] d....  3410.005610: kvm_exit: vcpu 23 reason DR_ACCESS info1 0x0000000000000216\n          CPU 23/KVM-5046    [001] .....  3410.005610: kvm_nested_vmexit: vcpu 23 reason DR_ACCESS info1 0x0000000000000216\n\n    L2 => L1 nested VM-Exit\n          CPU 23/KVM-5046    [001] .....  3410.005610: kvm_nested_vmexit_inject: reason: DR_ACCESS ext_inf1: 0x0000000000000216\n\n          CPU 23/KVM-5046    [001] d....  3410.005610: kvm_entry: vcpu 23\n          CPU 23/KVM-5046    [001] d....  3410.005611: kvm_exit: vcpu 23 reason VMREAD\n          CPU 23/KVM-5046    [001] d....  3410.005611: kvm_entry: vcpu 23\n          CPU 23/KVM-5046    [001] d....  3410.\n---truncated---"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: KVM: x86: Cargar DR6 con valor de invitado solo antes de ingresar al bucle .vcpu_run() Mueva la carga condicional de DR6 de hardware con el valor DR6 del invitado fuera del bucle .vcpu_run() del núcleo para corregir un error en el que KVM puede cargar hardware con un vcpu-&gt;arch.dr6 obsoleto. Cuando el invitado accede a un DR y el espacio de usuario del host no está depurando el invitado, KVM deshabilita la intercepción de DR y carga los valores del invitado en el hardware en VM-Enter y los guarda en VM-Exit. Esto permite que el invitado acceda a los DR a voluntad, por ejemplo, para que una secuencia de accesos a DR para configurar un punto de interrupción solo genere una VM-Exit. Para DR0-DR3, la lógica/comportamiento es idéntico entre VMX y SVM, y también idéntico entre KVM_DEBUGREG_BP_ENABLED (depuración del invitado en el espacio de usuario) y KVM_DEBUGREG_WONT_EXIT (invitado que utiliza DR), y por lo tanto KVM gestiona la carga de DR0-DR3 en código común, _fuera_ del bucle kvm_x86_ops.vcpu_run() central. Pero para DR6, el valor del invitado no necesita cargarse en el hardware para KVM_DEBUGREG_BP_ENABLED, y SVM proporciona un campo VMCB dedicado, mientras que VMX requiere software para cargar manualmente el valor del invitado, y por lo tanto, la carga del valor del invitado en DR6 es gestionada por {svm,vmx}_vcpu_run(), es decir, se realiza _dentro_ del bucle de ejecución central. Desafortunadamente, guardar los valores del invitado en VM-Exit lo inicia el x86 común, nuevamente fuera del bucle de ejecución central. Si el invitado modifica DR6 (en hardware, cuando la intercepción de DR está deshabilitada), y luego la siguiente salida de VM es una salida de VM de ruta rápida, KVM volverá a cargar el DR6 de hardware con vcpu-&gt;arch.dr6 y destruirá el valor real del invitado. El error aparece principalmente con VMX anidado porque KVM maneja el temporizador de preempción de VMX en la ruta rápida, y la ventana entre la modificación del DR6 de hardware (en el contexto del invitado) y la lectura del DR6 por parte del software del invitado es órdenes de magnitud más grande en una configuración anidada. Por ejemplo, en una configuración no anidada, el temporizador de preempción de VMX debería activarse precisamente entre la inyección de #DB y la lectura de DR6 por parte del controlador #DB, mientras que con una configuración KVM sobre KVM, la ventana donde el DR6 de hardware está \"sucio\" se extiende desde L1 escribiendo DR6 a VMRESUME (en L1). Vista de L1: ==========  CPU 0/KVM-7289 [023] d.... 2925.640961: kvm_entry: vcpu 0 A: L1 escribe DR6 CPU 0/KVM-7289 [023] d.... 2925.640963: : Establecer DR, DR6 = 0xffff0ff1 B: CPU 0/KVM-7289 [023] d.... 2925.640967: kvm_exit: vcpu 0 motivo EXTERNAL_INTERRUPT intr_info 0x800000ec D: L1 lee DR6, arch.dr6 = 0 CPU 0/KVM-7289 [023] d.... 2925.640969: : Sincronizar DR, DR6 = 0xffff0ff0 CPU 0/KVM-7289 [023] d.... 2925.640976: kvm_entry: vcpu 0 L2 lee DR6, L1 deshabilita la intercepción de DR CPU 0/KVM-7289 [023] d.... 2925.640980: kvm_exit: vcpu 0 motivo DR_ACCESS info1 0x0000000000000216 CPU 0/KVM-7289 [023] d.... 2925.640983: kvm_entry: vcpu 0 CPU 0/KVM-7289 [023] d.... 2925.640983: : Establecer DRs, DR6 = 0xffff0ff0 L2 detecta falla CPU 0/KVM-7289 [023] d.... 2925.640987: kvm_exit: vcpu 0 motivo HLT L1 lee DR6 (confirma falla) CPU 0/KVM-7289 [023] d.... 2925.640990: : Sincronizar DRs, DR6 = 0xffff0ff0 Vista de L0: ========== L2 lee DR6, arch.dr6 = 0 CPU 23/KVM-5046 [001] d.... 3410.005610: kvm_exit: vcpu 23 motivo DR_ACCESS info1 0x0000000000000216 CPU 23/KVM-5046 [001] ..... 3410.005610: kvm_nested_vmexit: vcpu 23 motivo DR_ACCESS info1 0x0000000000000216 L2 =&gt; L1 anidada VM-Salir CPU 23/KVM-5046 [001] ..... 3410.005610: kvm_nested_vmexit_inject: motivo: DR_ACCESS ext_inf1: 0x000000000000216 CPU 23/KVM-5046 [001] d.... 3410.005610: kvm_entry: vcpu 23 CPU 23/KVM-5046 [001] d.... 3410.005611: ---truncado---"}], "references": [{"url": "https://git.kernel.org/stable/c/4eb063de686bfcdfd03a8c801d1bbe87d2d5eb55", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/93eeb6df1605b3a24f38afdba7ab903ba6b64133", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/9efb2b99b96c86664bbdbdd2cdb354ac9627eb20", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a1723e9c53fe6431415be19302a56543daf503f5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c2fee09fc167c74a64adb08656cb993ea475197e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/d456de38d9eb753a4e9fde053c18d4ef8e485339", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
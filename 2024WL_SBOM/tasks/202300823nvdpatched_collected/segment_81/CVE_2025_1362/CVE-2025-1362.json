{"cve_id": "CVE-2025-1362", "published_date": "2025-03-09T06:15:10.323", "last_modified_date": "2025-05-21T16:59:38.977", "descriptions": [{"lang": "en", "value": "The URL Shortener | Conversion Tracking  | AB Testing  | WooCommerce WordPress plugin through 9.0.2 does not have CSRF checks in some bulk actions, which could allow attackers to make logged in admins perform unwanted actions, such as deleting customers via CSRF attacks"}, {"lang": "es", "value": "El complemento URL Shortener | Conversion Tracking | AB Testing | WooCommerce WordPress hasta la versión 9.0.2 no tiene comprobaciones CSRF en algunas acciones masivas, lo que podría permitir a los atacantes hacer que los administradores que hayan iniciado sesión realicen acciones no deseadas, como eliminar clientes a través de ataques CSRF"}], "references": [{"url": "https://wpscan.com/vulnerability/035cc502-a514-440f-8808-5655c8c915e2/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
{"cve_id": "CVE-2024-58081", "published_date": "2025-03-06T17:15:21.577", "last_modified_date": "2025-03-25T14:27:38.820", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nclk: mmp2: call pm_genpd_init() only after genpd.name is set\n\nSetting the genpd's struct device's name with dev_set_name() is\nhappening within pm_genpd_init(). If it remains NULL, things can blow up\nlater, such as when crafting the devfs hierarchy for the power domain:\n\n  Unable to handle kernel NULL pointer dereference at virtual address 00000000 when read\n  ...\n  Call trace:\n   strlen from start_creating+0x90/0x138\n   start_creating from debugfs_create_dir+0x20/0x178\n   debugfs_create_dir from genpd_debug_add.part.0+0x4c/0x144\n   genpd_debug_add.part.0 from genpd_debug_init+0x74/0x90\n   genpd_debug_init from do_one_initcall+0x5c/0x244\n   do_one_initcall from kernel_init_freeable+0x19c/0x1f4\n   kernel_init_freeable from kernel_init+0x1c/0x12c\n   kernel_init from ret_from_fork+0x14/0x28\n\nBisecting tracks this crash back to commit 899f44531fe6 (\"pmdomain: core:\nAdd GENPD_FLAG_DEV_NAME_FW flag\"), which exchanges use of genpd->name\nwith dev_name(&genpd->dev) in genpd_debug_add.part()."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: clk: mmp2: llamar a pm_genpd_init() solo después de que se establece genpd.name La configuración del nombre del dispositivo de la estructura genpd con dev_set_name() ocurre dentro de pm_genpd_init(). Si permanece NULL, las cosas pueden explotar más tarde, como al crear la jerarquía devfs para el dominio de energía: No se puede gestionar la desreferencia del puntero NULL del kernel en la dirección virtual 00000000 cuando se lee ... Rastreo de llamadas: start_creating+0x90/0x138 start_creating from debugfs_create_dir+0x20/0x178 debugfs_create_dir from genpd_debug_add.part.0+0x4c/0x144 genpd_debug_add.part.0 from genpd_debug_init+0x74/0x90 genpd_debug_init from do_one_initcall+0x5c/0x244 do_one_initcall from kernel_init_freeable+0x19c/0x1f4 kernel_init_freeable from kernel_init+0x1c/0x12c kernel_init from ret_from_fork+0x14/0x28 Bisecting rastrea este fallo hasta el commit 899f44531fe6 (\"pmdomain: core: Agregar bandera GENPD_FLAG_DEV_NAME_FW\"), que intercambia el uso de genpd-&gt;name con dev_name(&amp;genpd-&gt;dev) en genpd_debug_add.part()."}], "references": [{"url": "https://git.kernel.org/stable/c/763517124e27b07fa300b486d7d13c5d563a215e", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/e24b15d4704dcb73920c3d18a6157abd18df08c1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/eca01d5911fb34218d10a58d8d9534b758c8fd0a", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
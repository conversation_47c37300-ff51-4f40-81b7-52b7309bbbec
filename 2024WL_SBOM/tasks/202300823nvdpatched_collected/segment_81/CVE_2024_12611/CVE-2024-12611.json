{"cve_id": "CVE-2024-12611", "published_date": "2025-03-07T09:15:15.177", "last_modified_date": "2025-07-07T18:16:49.047", "descriptions": [{"lang": "en", "value": "The School Management System for Wordpress plugin for WordPress is vulnerable to Reflected Cross-Site Scripting via the 'title' parameter in all versions up to, and including, 93.0.0 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that execute if they can successfully trick a user into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento School Management System for Wordpress para WordPress es vulnerable a Cross-Site Scripting reflejado a través del parámetro 'title' en todas las versiones hasta la 93.0.0 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes no autenticados inyecten secuencias de comandos web arbitrarias en páginas que se ejecutan si logran engañar a un usuario para que realice una acción, como hacer clic en un enlace."}], "references": [{"url": "https://codecanyon.net/item/school-management-system-for-wordpress/11470032", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/45ada7a4-466b-4e73-8869-e1178e4fc67a?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
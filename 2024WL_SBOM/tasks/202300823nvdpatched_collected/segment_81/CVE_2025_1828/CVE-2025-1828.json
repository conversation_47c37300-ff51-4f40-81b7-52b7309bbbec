{"cve_id": "CVE-2025-1828", "published_date": "2025-03-11T00:15:11.060", "last_modified_date": "2025-06-27T16:11:22.767", "descriptions": [{"lang": "en", "value": "Crypt::Random Perl package 1.05 through 1.55 may use rand() function, which is not cryptographically strong, for cryptographic functions.\n\nIf the Provider is not specified and /dev/urandom or an Entropy Gathering Daemon (egd) service is not available Crypt::Random will default to use the insecure Crypt::Random::rand provider.\n\nIn particular, Windows versions of perl will encounter this issue by default."}, {"lang": "es", "value": "El paquete Crypt::Random Perl 1.05 a 1.55 puede utilizar la función rand(), que no es criptográficamente segura, para funciones criptográficas. Crypt::Random::rand 1.05 a 1.55 utiliza la función rand(). Si no se especifica el proveedor y /dev/urandom o un servicio Entropy Gathering Daemon (egd) no está disponible, Crypt::Random utilizará de forma predeterminada el proveedor inseguro Crypt::Random::rand. En particular, las versiones de Windows de Perl encontrarán este problema de forma predeterminada."}], "references": [{"url": "https://github.com/perl-Crypt-OpenPGP/Crypt-Random/commit/1f8b29e9e89d8d083fd025152e76ec918136cc05", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Patch"]}, {"url": "https://github.com/perl-Crypt-OpenPGP/Crypt-Random/pull/1", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://perldoc.perl.org/functions/rand", "source": "9b29abf9-4ab0-4765-b253-1875cd9b441e", "tags": ["Patch"]}]}
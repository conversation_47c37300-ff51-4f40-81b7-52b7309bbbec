{"cve_id": "CVE-2025-25929", "published_date": "2025-03-11T20:15:17.377", "last_modified_date": "2025-07-07T18:16:37.203", "descriptions": [{"lang": "en", "value": "A reflected cross-site scripting (XSS) vulnerability in the component /legacyui/quickReportServlet of Openmrs 2.4.3 Build 0ff0ed allows attackers to execute arbitrary JavaScript in the context of a user's browser via a crafted payload injected into the reportType parameter."}, {"lang": "es", "value": "Una vulnerabilidad de cross site scripting (XSS) reflejado en el componente /legacyui/quickReportServlet de Openmrs 2.4.3 Build 0ff0ed permite a los atacantes ejecutar JavaScript arbitrario en el contexto del navegador de un usuario a través de un payload manipulado e inyectado en el parámetro reportType."}], "references": [{"url": "https://github.com/johnchd/CVEs/blob/main/OpenMRS/CVE-2025-25929%20-%20R-XSS.md", "source": "<EMAIL>", "tags": ["Exploit"]}]}
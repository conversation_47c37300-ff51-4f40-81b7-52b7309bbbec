{"cve_id": "CVE-2025-2152", "published_date": "2025-03-10T14:15:26.543", "last_modified_date": "2025-03-13T18:15:14.183", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in Open Asset Import Library Assimp 5.4.3. This issue affects the function Assimp::BaseImporter::ConvertToUTF8 of the file BaseImporter.cpp of the component File Handler. The manipulation leads to heap-based buffer overflow. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad, que se ha clasificado como crítica, en Open Asset Import Library Assimp 5.4.3. Este problema afecta a la función Assimp::BaseImporter::ConvertToUTF8 del archivo BaseImporter.cpp del componente File Handler. La manipulación provoca un desbordamiento del búfer basado en el montón. El ataque puede iniciarse de forma remota. El exploit se ha hecho público y puede utilizarse."}], "references": [{"url": "https://github.com/assimp/assimp/issues/6027", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://github.com/assimp/assimp/issues/6027#issue-2877629241", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.299063", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.299063", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.510818", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
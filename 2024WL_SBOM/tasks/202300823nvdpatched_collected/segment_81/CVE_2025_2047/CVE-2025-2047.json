{"cve_id": "CVE-2025-2047", "published_date": "2025-03-06T23:15:12.343", "last_modified_date": "2025-04-03T13:32:55.893", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Art Gallery Management System 1.0. It has been classified as problematic. This affects an unknown part of the file /search.php. The manipulation of the argument search leads to cross site scripting. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Art Gallery Management System 1.0. Se ha clasificado como problemática. Afecta a una parte desconocida del archivo /search.php. La manipulación del argumento search provoca ataques de cross site scripting. Es posible iniciar el ataque de forma remota. La vulnerabilidad se ha hecho pública y puede utilizarse."}], "references": [{"url": "https://github.com/chenyihao-cyber/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298797", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298797", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514015", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2025-2064", "published_date": "2025-03-07T04:15:10.247", "last_modified_date": "2025-05-14T16:15:12.957", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in projectworlds Life Insurance Management System 1.0. Affected by this issue is some unknown functionality of the file /deletePayment.php. The manipulation of the argument recipt_no leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en projectworlds Life Insurance Management System 1.0. Este problema afecta a algunas funciones desconocidas del archivo /deletePayment.php. La manipulación del argumento recipt_no provoca una inyección SQL. El ataque puede ejecutarse de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ubfbuz3/cve/issues/6", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298820", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298820", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514751", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
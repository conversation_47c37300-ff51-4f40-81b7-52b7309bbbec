{"cve_id": "CVE-2025-27515", "published_date": "2025-03-05T19:15:39.483", "last_modified_date": "2025-03-05T19:15:39.483", "descriptions": [{"lang": "en", "value": "Laravel is a web application framework. When using wildcard validation to validate a given file or image field (`files.*`), a user-crafted malicious request could potentially bypass the validation rules. This vulnerability is fixed in 11.44.1 and 12.1.1."}, {"lang": "es", "value": "Laravel es un framework de trabajo para aplicaciones web. Al utilizar la validación con comodines para validar un campo de archivo o imagen determinado (`files.*`), una solicitud maliciosa manipulada por el usuario podría eludir las reglas de validación. Esta vulnerabilidad se ha corregido en las versiones 11.44.1 y 12.1.1."}], "references": [{"url": "https://github.com/laravel/framework/commit/2d133034fefddfb047838f4caca3687a3ba811a5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/laravel/framework/security/advisories/GHSA-78fx-h6xr-vch4", "source": "<EMAIL>", "tags": []}]}
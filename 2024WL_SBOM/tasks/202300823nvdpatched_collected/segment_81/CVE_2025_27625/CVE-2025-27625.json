{"cve_id": "CVE-2025-27625", "published_date": "2025-03-05T23:15:14.297", "last_modified_date": "2025-06-24T00:42:16.010", "descriptions": [{"lang": "en", "value": "In Jenkins 2.499 and earlier, LTS 2.492.1 and earlier, redirects starting with backslash (`\\`) characters are considered safe, allowing attackers to perform phishing attacks by having users go to a Jenkins URL that will forward them to a different site, because browsers interpret these characters as part of scheme-relative redirects."}, {"lang": "es", "value": "En Jenkins 2.499 y anteriores, LTS 2.492.1 y anteriores, las redirecciones que comienzan con caracteres de barra invertida (`\\`) se consideran seguras, lo que permite a los atacantes realizar ataques de phishing haciendo que los usuarios accedan a una URL de Jenkins que los reenviará a un sitio diferente, porque los navegadores interpretan estos caracteres como parte de redirecciones relativas al esquema."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-03-05/#SECURITY-3501", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}
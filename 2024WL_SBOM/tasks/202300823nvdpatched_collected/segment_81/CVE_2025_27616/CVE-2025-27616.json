{"cve_id": "CVE-2025-27616", "published_date": "2025-03-10T19:15:41.080", "last_modified_date": "2025-03-10T19:15:41.080", "descriptions": [{"lang": "en", "value": "Vela is a Pipeline Automation (CI/CD) framework built on Linux container technology written in Golang. Prior to versions 0.25.3 and 0.26.3, by spoofing a webhook payload with a specific set of headers and body data, an attacker could transfer ownership of a repository and its repo level secrets to a separate repository. These secrets could be exfiltrated by follow up builds to the repository. Users with an enabled repository with access to repo level CI secrets in Vela are vulnerable to the exploit, and any user with access to the CI instance and the linked source control manager can perform the exploit. Versions 0.25.3 and 0.26.3 fix the issue. No known workarounds are available."}, {"lang": "es", "value": "Vela es un framework de automatización de canalización (CI/CD) creado con tecnología de contenedores Linux escrito en Golang. Antes de las versiones 0.25.3 y 0.26.3, al falsificar el payload de un webhook con un conjunto específico de encabezados y datos de cuerpo, un atacante podía transferir la propiedad de un repositorio y sus secretos de nivel de repositorio a un repositorio separado. Estos secretos podrían ser exfiltrados por compilaciones de seguimiento al repositorio. Los usuarios con un repositorio habilitado con acceso a secretos de CI de nivel de repositorio en Vela son afectados por la vulnerabilidad, y cualquier usuario con acceso a la instancia de CI y al administrador de control de código fuente vinculado puede realizar la vulnerabilidad. Las versiones 0.25.3 y 0.26.3 solucionan el problema. No hay workarounds conocidos disponibles."}], "references": [{"url": "https://github.com/go-vela/server/commit/257886e5a3eea518548387885894e239668584f5", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/go-vela/server/commit/67c1892e2464dc54b8d2588815dfb7819222500b", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/go-vela/server/releases/tag/v0.25.3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/go-vela/server/releases/tag/v0.26.3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/go-vela/server/security/advisories/GHSA-9m63-33q3-xq5x", "source": "<EMAIL>", "tags": []}]}
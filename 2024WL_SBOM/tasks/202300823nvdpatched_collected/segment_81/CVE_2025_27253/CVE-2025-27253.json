{"cve_id": "CVE-2025-27253", "published_date": "2025-03-10T09:15:10.897", "last_modified_date": "2025-03-12T11:15:41.127", "descriptions": [{"lang": "en", "value": "An improper input validation in GE Vernova UR IED family devices from version 7.0 up to 8.60 allows an attacker to provide input that enstablishes a TCP connection through a port forwarding. The lack of the IP address and port validation may allow the attacker to bypass firewall rules or to send malicious traffic in the network"}, {"lang": "es", "value": "Una validación de entrada incorrecta en los dispositivos de la familia GE Vernova UR IED de la versión 7.0 a la 8.60 permite a un atacante proporcionar una entrada que establezca una conexión TCP a través de un reenvío de puerto. La falta de validación de la dirección IP y del puerto puede permitir al atacante eludir las reglas del firewall o enviar tráfico malicioso en la red."}], "references": [{"url": "https://www.gevernova.com/grid-solutions/app/DownloadFile.aspx?prod=urfamily&type=21&file=76", "source": "<EMAIL>", "tags": []}, {"url": "https://www.nozominetworks.com/labs/vulnerability-advisories-cve-2025-27253", "source": "<EMAIL>", "tags": []}]}
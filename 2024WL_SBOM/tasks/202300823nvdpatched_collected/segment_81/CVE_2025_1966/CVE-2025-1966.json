{"cve_id": "CVE-2025-1966", "published_date": "2025-03-05T02:15:36.157", "last_modified_date": "2025-04-02T18:22:05.807", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Pre-School Enrollment System 1.0. Affected by this vulnerability is an unknown functionality of the file /admin/index.php. The manipulation of the argument username leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en PHPGurukul Pre-School Enrollment System 1.0. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/index.php. La manipulación del argumento username provoca una inyección SQL. El ataque se puede lanzar de forma remota. El exploit se ha hecho público y puede utilizarse."}], "references": [{"url": "https://github.com/SECWG/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298567", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298567", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512039", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2024-13757", "published_date": "2025-03-05T10:15:14.923", "last_modified_date": "2025-05-26T01:47:50.477", "descriptions": [{"lang": "en", "value": "The Master Slider – Responsive Touch Slider plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's ms_layer shortcode in all versions up to, and including, 3.10.6 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Master Slider – Responsive Touch Slider para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del código corto ms_layer del complemento en todas las versiones hasta la 3.10.6 incluida, debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/master-slider/trunk/includes/msp-shortcodes.php#L815", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://wordpress.org/plugins/master-slider/#developers", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/26a7fb51-f40d-46b8-9f52-495716032a1b?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2024-58073", "published_date": "2025-03-06T16:15:53.833", "last_modified_date": "2025-03-25T14:26:28.240", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/msm/dpu: check dpu_plane_atomic_print_state() for valid sspp\n\nSimilar to the r_pipe sspp protect, add a check to protect\nthe pipe state prints to avoid NULL ptr dereference for cases when\nthe state is dumped without a corresponding atomic_check() where the\npipe->sspp is assigned.\n\nPatchwork: https://patchwork.freedesktop.org/patch/628404/"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/msm/dpu: comprobar dpu_plane_atomic_print_state() para sspp válidos De manera similar a la protección r_pipe sspp, agregue una verificación para proteger las impresiones del estado de la tubería para evitar la desreferencia de ptr NULL para los casos en que el estado se vuelca sin un atomic_check() correspondiente donde se asigna pipe-&gt;sspp. Patchwork: https://patchwork.freedesktop.org/patch/628404/"}], "references": [{"url": "https://git.kernel.org/stable/c/008af2074e4b91d34440102501b710c235a3b245", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/789384eb1437aed94155dc0eac8a8a6ba1baf578", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
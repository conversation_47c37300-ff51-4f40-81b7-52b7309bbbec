{"cve_id": "CVE-2024-33501", "published_date": "2025-03-11T15:15:41.550", "last_modified_date": "2025-07-24T19:05:16.203", "descriptions": [{"lang": "en", "value": "Two improper neutralization of special elements used in an SQL Command ('SQL Injection') vulnerability [CWE-89] in Fortinet FortiAnalyzer version 7.4.0 through 7.4.2 and before 7.2.5, FortiManager version 7.4.0 through 7.4.2 and before 7.2.5 and FortiAnalyzer-BigData version 7.4.0 and before 7.2.7 allows a privileged attacker to execute unauthorized code or commands via specifically crafted CLI requests."}, {"lang": "es", "value": "Dos vulnerabilidades de neutralización incorrecta de elementos especiales utilizadas en un comando SQL ('SQL Injection') [CWE-89] en Fortinet FortiAnalyzer versión 7.4.0 a 7.4.2 y anteriores a 7.2.5, FortiManager versión 7.4.0 a 7.4.2 y anteriores a 7.2.5 y FortiAnalyzer-BigData versión 7.4.0 y anteriores a 7.2.7 permiten que un atacante privilegiado ejecute código o comandos no autorizados a través de solicitudes CLI específicamente manipuladas."}], "references": [{"url": "https://fortiguard.fortinet.com/psirt/FG-IR-24-130", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
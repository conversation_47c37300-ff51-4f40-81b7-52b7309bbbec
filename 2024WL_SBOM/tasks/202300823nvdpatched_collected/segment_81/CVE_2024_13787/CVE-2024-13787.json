{"cve_id": "CVE-2024-13787", "published_date": "2025-03-05T10:15:16.810", "last_modified_date": "2025-03-05T10:15:16.810", "descriptions": [{"lang": "en", "value": "The VEDA - MultiPurpose WordPress Theme theme for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 4.2 via deserialization of untrusted input in the 'veda_backup_and_restore_action' function. This makes it possible for authenticated attackers, with Subscriber-level access and above, to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present."}, {"lang": "es", "value": "El tema VEDA - MultiPurpose WordPress Theme para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 4.2 incluida, a través de la deserialización de entradas no confiables en la función 'veda_backup_and_restore_action'. Esto hace posible que atacantes autenticados, con acceso de nivel de suscriptor y superior, inyecten un objeto PHP. No hay ninguna cadena POP conocida presente en el software vulnerable, lo que significa que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si hay una cadena POP presente a través de un complemento o tema adicional instalado en el sistema de destino, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código según la cadena POP presente."}], "references": [{"url": "https://themeforest.net/item/veda-multipurpose-theme/15860489", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d0966138-b28b-4c03-a2cf-b51c5f478276?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-13781", "published_date": "2025-03-07T09:15:15.660", "last_modified_date": "2025-03-13T17:45:04.640", "descriptions": [{"lang": "en", "value": "The Hero Maps Premium plugin for WordPress is vulnerable to SQL Injection via several AJAX actions in all versions up to, and including, 2.3.9 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Hero Maps Premium para WordPress es vulnerable a la inyección SQL a través de varias acciones AJAX en todas las versiones hasta la 2.3.9 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel de suscriptor y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/hero-maps-premium-responsive-google-maps-plugin/12577151", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/8f394209-df80-491f-b700-cc06e54ea676?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
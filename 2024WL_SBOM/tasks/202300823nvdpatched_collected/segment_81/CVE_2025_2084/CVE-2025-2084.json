{"cve_id": "CVE-2025-2084", "published_date": "2025-03-07T12:15:34.260", "last_modified_date": "2025-03-12T17:20:12.987", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Human Metapneumovirus Testing Management System 1.0. It has been classified as problematic. Affected is an unknown function of the file /search-report.php of the component Search Report Page. The manipulation leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Human Metapneumovirus Testing Management System 1.0. Se ha clasificado como problemática. Se ve afectada una función desconocida del archivo /search-report.php del componente Search Report Page. La manipulación conduce a cross site scripting. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/sorcha-l/cve/blob/main/Human%20Metapneumovirus%20(HMPV)%20%E2%80%93%20Testing%20Management%20System%20%20XSS%20in%20search-report.php.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298896", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298896", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514804", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2024-57492", "published_date": "2025-03-10T14:15:24.387", "last_modified_date": "2025-03-24T18:58:00.923", "descriptions": [{"lang": "en", "value": "An issue in redoxOS relibc before commit 98aa4ea5 allows a local attacker to cause a denial of service via the round_up_to_page funciton."}, {"lang": "es", "value": "Un problema en redoxOS relibc antes del commit 98aa4ea5 permite que un atacante local provoque una denegación de servicio a través de la función round_up_to_page."}], "references": [{"url": "https://github.com/Marsman1996/pocs/blob/master/redox/CVE-2024-57492/README.md", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}, {"url": "https://gitlab.redox-os.org/redox-os/relibc/-/issues/200", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://gitlab.redox-os.org/redox-os/relibc/-/merge_requests/569", "source": "<EMAIL>", "tags": ["Exploit"]}]}
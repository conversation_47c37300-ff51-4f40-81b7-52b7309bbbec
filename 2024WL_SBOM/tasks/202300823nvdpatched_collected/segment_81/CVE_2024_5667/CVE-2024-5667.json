{"cve_id": "CVE-2024-5667", "published_date": "2025-03-05T10:15:18.910", "last_modified_date": "2025-03-05T10:15:18.910", "descriptions": [{"lang": "en", "value": "Multiple plugins for WordPress are vulnerable to Stored Cross-Site Scripting via the plugin's bundled Featherlight.js JavaScript library (versions 1.7.13 to 1.7.14) in various versions due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "Varios complementos para WordPress son vulnerables a Cross-Site Scripting Almacenado a través de la librería de JavaScript Featherlight.js incluida en el complemento (versiones 1.7.13 a 1.7.14) en varias versiones debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-featherlight/trunk/js/wpFeatherlight.pkgd.js", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3137531/responsive-lightbox", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/44b173da-a6b9-424c-95a1-a87a9b8ee4af?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-28883", "published_date": "2025-03-11T21:15:45.803", "last_modified_date": "2025-03-11T21:15:45.803", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Martin WP Compare Tables allows Stored XSS. This issue affects WP Compare Tables: from n/a through 1.0.5."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en Martin WP Compare Tables permite XSS almacenado. Este problema afecta a WP Compare Tables desde n/d hasta la versión 1.0.5."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wp-compare-tables/vulnerability/wordpress-wp-compare-tables-plugin-1-0-5-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-26659", "published_date": "2025-03-11T01:15:35.683", "last_modified_date": "2025-03-11T01:15:35.683", "descriptions": [{"lang": "en", "value": "SAP NetWeaver Application Server ABAP does not sufficiently encode user-controlled inputs, leading to DOM-basedCross-Site Scripting (XSS) vulnerability. This allows an attacker with no privileges, to craft a malicious web message that exploits WEBGUI functionality. On successful exploitation, the malicious JavaScript payload executes in the scope of victim�s browser potentially compromising their data and/or manipulating browser content. This leads to a limited impact on confidentiality and integrity. There is no impact on availability"}, {"lang": "es", "value": "SAP NetWeaver Application Server ABAP no codifica lo suficiente las entradas controladas por el usuario, lo que genera una vulnerabilidad de cross-site scripting (XSS) basada en DOM. Esto permite a un atacante sin privilegios manipular un mensaje web malicioso que explota la funcionalidad de WEBGUI. Si la explotación es exitosa, el payload malicioso de JavaScript se ejecuta en el ámbito del navegador de la víctima, lo que puede comprometer sus datos o manipular el contenido del navegador. Esto genera un impacto limitado en la confidencialidad y la integridad. No hay impacto en la disponibilidad."}], "references": [{"url": "https://me.sap.com/notes/3552824", "source": "<EMAIL>", "tags": []}, {"url": "https://url.sap/sapsecuritypatchday", "source": "<EMAIL>", "tags": []}]}
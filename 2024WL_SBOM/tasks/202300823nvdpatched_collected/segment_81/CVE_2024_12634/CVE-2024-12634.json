{"cve_id": "CVE-2024-12634", "published_date": "2025-03-07T11:15:13.850", "last_modified_date": "2025-03-07T11:15:13.850", "descriptions": [{"lang": "en", "value": "The Related Posts, Inline Related Posts, Contextual Related Posts, Related Content By PickPlugins plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including 2.0.59. This is due to missing nonce validation on a function. This makes it possible for unauthenticated attackers to inject malicious web scripts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Related Posts, Inline Related Posts, Contextual Related Posts, Related Content de PickPlugins para WordPress es vulnerable a cross-site request forgery en todas las versiones hasta la 2.0.59 incluida. Esto se debe a la falta de validación de nonce en una función. Esto hace posible que atacantes no autenticados inyecten scripts web maliciosos a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/related-post/tags/2.0.58b/includes/menu/settings.php#L129", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3251482/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/related-post/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/29d22612-8e0d-4275-b370-9729352c951e?source=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-21838", "published_date": "2025-03-07T09:15:16.810", "last_modified_date": "2025-03-07T18:15:47.713", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nusb: gadget: core: flush gadget workqueue after device removal\n\ndevice_del() can lead to new work being scheduled in gadget->work\nworkqueue. This is observed, for example, with the dwc3 driver with the\nfollowing call stack:\n  device_del()\n    gadget_unbind_driver()\n      usb_gadget_disconnect_locked()\n        dwc3_gadget_pullup()\n\t  dwc3_gadget_soft_disconnect()\n\t    usb_gadget_set_state()\n\t      schedule_work(&gadget->work)\n\nMove flush_work() after device_del() to ensure the workqueue is cleaned\nup."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: usb: gadget: core: vaciar la cola de trabajo de gadget después de la eliminación del dispositivo device_del() puede provocar que se programe un nuevo trabajo en la cola de trabajo gadget-&gt;work. Esto se observa, por ejemplo, con el controlador dwc3 con la siguiente pila de llamadas: device_del() gadget_unbind_driver() usb_gadget_disconnect_locked() dwc3_gadget_pullup() dwc3_gadget_soft_disconnect() usb_gadget_set_state() schedule_work(&amp;gadget-&gt;work) Mueva flush_work() después de device_del() para garantizar que se limpie la cola de trabajo."}], "references": [{"url": "https://git.kernel.org/stable/c/399a45e5237ca14037120b1b895bd38a3b4492ea", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/859cb45aefa6de823b2fa7f229fe6d9562c9f3b7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/97695b5a1b5467a4f91194db12160f56da445dfe", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e3bc1a9a67ce33a2e761e6e7b7c2afc6cb9b7266", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f894448f3904d7ad66fecef8f01fe0172629e091", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-27517", "published_date": "2025-03-05T20:16:05.960", "last_modified_date": "2025-03-05T20:16:05.960", "descriptions": [{"lang": "en", "value": "Volt is an elegantly crafted functional API for Livewire. Malicious, user-crafted request payloads could potentially lead to remote code execution within Volt components. This vulnerability is fixed in 1.7.0."}, {"lang": "es", "value": "Volt es una API funcional manipulada con elegancia para Livewire. Los payloads de solicitudes maliciosas manipuladas por el usuario podrían provocar la ejecución remota de código dentro de los componentes de Volt. Esta vulnerabilidad se solucionó en la versión 1.7.0."}], "references": [{"url": "https://github.com/livewire/volt/security/advisories/GHSA-v69f-5jxm-hwvv", "source": "<EMAIL>", "tags": []}]}
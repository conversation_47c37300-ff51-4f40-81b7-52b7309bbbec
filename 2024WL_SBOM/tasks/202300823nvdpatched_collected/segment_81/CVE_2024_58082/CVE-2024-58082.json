{"cve_id": "CVE-2024-58082", "published_date": "2025-03-06T17:15:21.677", "last_modified_date": "2025-03-06T17:15:21.677", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nmedia: nuvoton: Fix an error check in npcm_video_ece_init()\n\nWhen function of_find_device_by_node() fails, it returns NULL instead of\nan error code. So the corresponding error check logic should be modified\nto check whether the return value is NULL and set the error code to be\nreturned as -ENODEV."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: media: nuvoton: Se corrige una comprobación de errores en npcm_video_ece_init(). Cuando la función of_find_device_by_node() falla, devuelve NULL en lugar de un código de error. <PERSON>r lo tanto, se debe modificar la lógica de comprobación de errores correspondiente para verificar si el valor de retorno es NULL y establecer el código de error que se devolverá como -ENODEV."}], "references": [{"url": "https://git.kernel.org/stable/c/bdd823b9d068284e1d998b962cfef29236365df3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c36b830754ae1dd1db41c27f57b29267878f9702", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/c4b7779abc6633677e6edb79e2809f4f61fde157", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
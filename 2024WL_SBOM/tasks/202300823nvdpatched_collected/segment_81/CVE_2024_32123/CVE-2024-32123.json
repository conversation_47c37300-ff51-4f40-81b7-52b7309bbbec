{"cve_id": "CVE-2024-32123", "published_date": "2025-03-11T15:15:41.353", "last_modified_date": "2025-07-24T19:05:03.987", "descriptions": [{"lang": "en", "value": "Multiple improper neutralization of special elements used in an os command ('os command injection') in Fortinet FortiManager, FortiAnalyzer versions 7.4.0 through 7.4.2\r\n7.2.0 through 7.2.5 and 7.0.0 through 7.0.12 and 6.4.0 through 6.4.14 and 6.2.0 through 6.2.12 and 6.0.0 through 6.0.12 and 5.6.0 through 5.6.11 and 5.4.0 through 5.4.7 and 5.2.0 through 5.2.10 and 5.0.0 through 5.0.12 and 4.3.4 through 4.3.8 allows attacker to execute unauthorized code or commands via crafted CLI requests."}, {"lang": "es", "value": "La neutralización incorrecta múltiple de elementos especiales utilizados en un comando del sistema operativo ('inyección de comando del sistema operativo') en Fortinet FortiManager, FortiAnalyzer versiones 7.4.0 a 7.4.2, 7.2.0 a 7.2.5 y 7.0.0 a 7.0.12 y 6.4.0 a 6.4.14 y 6.2.0 a 6.2.12 y 6.0.0 a 6.0.12 y 5.6.0 a 5.6.11 y 5.4.0 a 5.4.7 y 5.2.0 a 5.2.10 y 5.0.0 a 5.0.12 y 4.3.4 a 4.3.8 permite a los atacantes ejecutar código o comandos no autorizados a través de solicitudes CLI manipuladas."}], "references": [{"url": "https://fortiguard.com/psirt/FG-IR-24-124", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2024-13853", "published_date": "2025-03-11T06:15:26.177", "last_modified_date": "2025-05-21T19:33:57.973", "descriptions": [{"lang": "en", "value": "The SEO Tools WordPress plugin through 4.0.7 does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin"}, {"lang": "es", "value": "El complemento SEO Tools WordPress hasta la versión 4.0.7 no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios elevados, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/52991dd9-41f7-4cf8-b8c9-56dd4e62bf0c/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}
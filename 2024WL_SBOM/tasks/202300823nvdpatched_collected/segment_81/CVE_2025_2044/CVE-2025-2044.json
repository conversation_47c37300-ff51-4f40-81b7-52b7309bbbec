{"cve_id": "CVE-2025-2044", "published_date": "2025-03-06T22:15:35.957", "last_modified_date": "2025-05-13T20:57:02.520", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Blood Bank Management System 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /admin/delete_bloodGroup.php. The manipulation of the argument blood_id leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en code-projects Blood Bank Management System 1.0. Se ha declarado como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/delete_bloodGroup.php. La manipulación del argumento blood_id conduce a una inyección SQL. El ataque se puede ejecutar de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/intercpt/XSS1/blob/main/SQL5.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298789", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298789", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.513653", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
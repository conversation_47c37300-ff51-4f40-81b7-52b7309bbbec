{"cve_id": "CVE-2025-28867", "published_date": "2025-03-11T21:15:44.127", "last_modified_date": "2025-03-18T20:43:40.300", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in stesvis Frontpage category filter allows Cross Site Request Forgery. This issue affects Frontpage category filter: from n/a through 1.0.2."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en stesvis Frontpage category filter permite Cross-Site Request Forgery. Este problema afecta al filtro de categorías de Frontpage desde n/d hasta la versión 1.0.2."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/frontpage-category-filter/vulnerability/wordpress-frontpage-category-filter-plugin-1-0-2-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
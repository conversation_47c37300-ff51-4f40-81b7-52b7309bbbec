{"cve_id": "CVE-2025-27506", "published_date": "2025-03-06T19:15:27.833", "last_modified_date": "2025-03-06T19:15:27.833", "descriptions": [{"lang": "en", "value": "NocoDB is software for building databases as spreadsheets. The API endpoint related to the password reset function is vulnerable to Reflected Cross-Site-Scripting. The endpoint /api/v1/db/auth/password/reset/:tokenId is vulnerable to Reflected Cross-Site-Scripting. The flaw occurs due to implementation of the client-side template engine ejs, specifically on file resetPassword.ts where the template is using the insecure function “<%-“, which is rendered by the function renderPasswordReset. This vulnerability is fixed in 0.258.0."}, {"lang": "es", "value": "NocoDB es un software para crear bases de datos como hojas de cálculo. El endpoint de la API relacionado con la función de restablecimiento de contraseña es vulnerable a Cross-Site-Scripting Reflejado. El endpoint /api/v1/db/auth/password/reset/:tokenId es vulnerable a Cross-Site-Scripting Reflejado. La falla ocurre debido a la implementación del motor de plantilla del lado del cliente ejs, específicamente en el archivo resetPassword.ts donde la plantilla usa la función insegura “&lt;%-“, que es representada por la función renderPasswordReset. Esta vulnerabilidad se corrigió en 0.258.0."}], "references": [{"url": "https://github.com/nocodb/nocodb/blob/ba5a191b33259d984fc92df225f7d82ede2ddb56/packages/nocodb/src/modules/auth/auth.controller.ts#L251", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nocodb/nocodb/blob/ba5a191b33259d984fc92df225f7d82ede2ddb56/packages/nocodb/src/modules/auth/ui/auth/resetPassword.ts#L71", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nocodb/nocodb/commit/ea821edb133e621e26183ae65c8ff9ee5d6f2723", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/nocodb/nocodb/security/advisories/GHSA-wf6c-hrhf-86cw", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-53700", "published_date": "2025-03-07T17:15:20.957", "last_modified_date": "2025-03-07T17:15:20.957", "descriptions": [{"lang": "en", "value": "A command injection vulnerability has been reported to affect QHora. If exploited, the vulnerability could allow remote attackers who have gained administrator access to execute arbitrary commands.\n\nWe have already fixed the vulnerability in the following version:\nQuRouter ********* and later"}, {"lang": "es", "value": "Se ha informado de una vulnerabilidad de inyección de comandos que afecta a QHora. Si se explota, la vulnerabilidad podría permitir que atacantes remotos que hayan obtenido acceso de administrador ejecuten comandos arbitrarios. Ya hemos corregido la vulnerabilidad en la siguiente versión: QuRouter ********* y posteriores"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-25-07", "source": "<EMAIL>", "tags": []}]}
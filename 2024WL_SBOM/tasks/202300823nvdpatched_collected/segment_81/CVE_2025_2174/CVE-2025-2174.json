{"cve_id": "CVE-2025-2174", "published_date": "2025-03-11T07:15:36.887", "last_modified_date": "2025-03-11T07:15:36.887", "descriptions": [{"lang": "en", "value": "A vulnerability was found in libzvbi up to 0.2.43. It has been declared as problematic. Affected by this vulnerability is the function vbi_strndup_iconv_ucs2 of the file src/conv.c. The manipulation of the argument src_length leads to integer overflow. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Upgrading to version 0.2.44 is able to address this issue. The patch is named ca1672134b3e2962cd392212c73f44f8f4cb489f. It is recommended to upgrade the affected component. The code maintainer was informed beforehand about the issues. She reacted very fast and highly professional."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en libzvbi hasta la versión 0.2.43. Se ha declarado como problemática. La función vbi_strndup_iconv_ucs2 del archivo src/conv.c está afectada por esta vulnerabilidad. La manipulación del argumento src_length provoca un desbordamiento de enteros. El ataque se puede ejecutar de forma remota. El exploit se ha hecho público y se puede utilizar. La actualización a la versión 0.2.44 puede solucionar este problema. El parche se llama ca1672134b3e2962cd392212c73f44f8f4cb489f. Se recomienda actualizar el componente afectado. La responsable del código fue informada de antemano sobre los problemas. Reaccionó muy rápido y con gran profesionalidad."}], "references": [{"url": "https://github.com/zapping-vbi/zvbi/commit/ca1672134b3e2962cd392212c73f44f8f4cb489f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/releases/tag/v0.2.44", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zapping-vbi/zvbi/security/advisories/GHSA-g7cg-7gw9-v8cf", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.299203", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.299203", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.512800", "source": "<EMAIL>", "tags": []}]}
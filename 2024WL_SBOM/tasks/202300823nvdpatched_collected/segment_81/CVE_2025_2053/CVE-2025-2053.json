{"cve_id": "CVE-2025-2053", "published_date": "2025-03-07T01:15:13.373", "last_modified_date": "2025-05-08T18:49:50.887", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Apartment Visitors Management System 1.0. It has been classified as critical. Affected is an unknown function of the file /visitor-detail.php. The manipulation of the argument editid leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Apartment Visitors Management System 1.0. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /visitor-detail.php. La manipulación del argumento editid provoca una inyección SQL. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/guttlefish/vul/issues/11", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298806", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298806", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514234", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
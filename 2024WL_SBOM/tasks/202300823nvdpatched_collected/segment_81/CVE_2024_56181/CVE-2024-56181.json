{"cve_id": "CVE-2024-56181", "published_date": "2025-03-11T10:15:15.597", "last_modified_date": "2025-06-10T16:15:35.567", "descriptions": [{"lang": "en", "value": "A vulnerability has been identified in SIMATIC Field PG M5 (All versions), SIMATIC IPC BX-21A (All versions < V31.01.07), SIMATIC IPC BX-32A (All versions < V29.01.07), SIMATIC IPC BX-39A (All versions < V29.01.07), SIMATIC IPC BX-59A (All versions < V32.01.04), SIMATIC IPC PX-32A (All versions < V29.01.07), SIMATIC IPC PX-39A (All versions < V29.01.07), SIMATIC IPC PX-39A PRO (All versions < V29.01.07), SIMATIC IPC RC-543A (All versions), SIMATIC IPC RC-543B (All versions), SIMATIC IPC RW-543A (All versions), SIMATIC IPC RW-543B (All versions), SIMATIC IPC127E (All versions), SIMATIC IPC227E (All versions), SIMATIC IPC227G (All versions), SIMATIC IPC277E (All versions), SIMATIC IPC277G (All versions), SIMATIC IPC277G PRO (All versions), SIMATIC IPC3000 SMART V3 (All versions), SIMATIC IPC327G (All versions), SIMATIC IPC347G (All versions), SIMATIC IPC377G (All versions), SIMATIC IPC427E (All versions), SIMATIC IPC477E (All versions), SIMATIC IPC477E PRO (All versions), SIMATIC IPC527G (All versions), SIMATIC IPC627E (All versions < V25.02.15), SIMATIC IPC647E (All versions < V25.02.15), SIMATIC IPC677E (All versions < V25.02.15), SIMATIC IPC847E (All versions < V25.02.15), SIMATIC ITP1000 (All versions). The affected devices have insufficient protection mechanism for the EFI(Extensible Firmware Interface) variables stored on the device. This could allow an authenticated attacker to alter the secure boot configuration without proper authorization by directly communicate with the flash controller."}, {"lang": "es", "value": "Se ha identificado una vulnerabilidad en SIMATIC Field PG M5 (todas las versiones), SIMATIC IPC BX-21A (todas las versiones &lt; V31.01.07), SIMATIC IPC BX-32A (todas las versiones &lt; V29.01.07), SIMATIC IPC BX-39A (todas las versiones &lt; V29.01.07), SIMATIC IPC BX-59A (todas las versiones &lt; V32.01.04), SIMATIC IPC PX-32A (todas las versiones &lt; V29.01.07), SIMATIC IPC PX-39A (todas las versiones &lt; V29.01.07), SIMATIC IPC PX-39A PRO (todas las versiones &lt; V29.01.07), SIMATIC IPC RC-543B (todas las versiones), SIMATIC IPC RW-543A (todas las versiones), SIMATIC IPC127E (todas las versiones), SIMATIC IPC227E (todas las versiones), SIMATIC IPC227G (todas las versiones), SIMATIC IPC277E (todas las versiones), SIMATIC IPC277G (todas las versiones), SIMATIC IPC277G PRO (todas las versiones), SIMATIC IPC3000 SMART V3 (todas las versiones), SIMATIC IPC327G (todas las versiones), SIMATIC IPC347G (todas las versiones), SIMATIC IPC377G (todas las versiones), SIMATIC IPC427E (todas las versiones), SIMATIC IPC477E (todas las versiones), SIMATIC IPC477E PRO (todas las versiones), SIMATIC IPC527G (todas las versiones), SIMATIC IPC627E (todas las versiones &lt; V25.02.15), SIMATIC IPC647E (todas las versiones &lt; V25.02.15), SIMATIC IPC677E (todas las versiones &lt; V25.02.15), SIMATIC IPC847E (todas las versiones &lt; V25.02.15), SIMATIC ITP1000 (todas las versiones Los dispositivos afectados no cuentan con un mecanismo de protección suficiente para las variables EFI (interfaz de firmware extensible) almacenadas en el dispositivo. Esto podría permitir que un atacante autenticado altere la configuración de arranque seguro sin la debida autorización comunicándose directamente con el controlador flash."}], "references": [{"url": "https://cert-portal.siemens.com/productcert/html/ssa-216014.html", "source": "<EMAIL>", "tags": []}]}
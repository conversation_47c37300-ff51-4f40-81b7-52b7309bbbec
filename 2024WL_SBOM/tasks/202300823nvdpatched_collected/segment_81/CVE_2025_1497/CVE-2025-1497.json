{"cve_id": "CVE-2025-1497", "published_date": "2025-03-10T14:15:24.723", "last_modified_date": "2025-03-24T18:46:13.367", "descriptions": [{"lang": "en", "value": "A vulnerability, that could result in Remote Code Execution (RCE), has been found in PlotAI. Lack of validation of LLM-generated output allows attacker to execute arbitrary Python code.\n<PERSON>endor commented out vulnerable line, further usage of the software requires uncommenting it and thus accepting the risk. The vendor does not plan to release a patch to fix this vulnerability."}, {"lang": "es", "value": "Se ha descubierto una vulnerabilidad en PlotAI que podría provocar la ejecución remota de código (RCE). La falta de validación de la salida generada por LLM permite al atacante ejecutar código Python arbitrario. El proveedor ha comentado la línea vulnerable; para seguir utilizando el software es necesario descomentarla y, por lo tanto, aceptar el riesgo. El proveedor no tiene previsto publicar un parche para solucionar esta vulnerabilidad."}], "references": [{"url": "https://cert.pl/en/posts/2025/03/CVE-2025-1497", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://cert.pl/posts/2025/03/CVE-2025-1497", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/mljar/plotai", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/mljar/plotai/commit/bdcfb13484f0b85703a4c1ddfd71cb21840e7fde", "source": "<EMAIL>", "tags": ["Patch"]}]}
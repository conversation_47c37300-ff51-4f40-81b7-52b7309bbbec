{"cve_id": "CVE-2025-28886", "published_date": "2025-03-11T21:15:46.113", "last_modified_date": "2025-03-11T21:15:46.113", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in xjb REST API TO MiniProgram allows Cross Site Request Forgery. This issue affects REST API TO MiniProgram: from n/a through 4.7.1."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en xjb REST API TO MiniProgram permite Cross-Site Request Forgery. Este problema afecta a la API REST de xjb para MiniProgram desde n/d hasta la versión 4.7.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/rest-api-to-miniprogram/vulnerability/wordpress-rest-api-to-miniprogram-plugin-4-7-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
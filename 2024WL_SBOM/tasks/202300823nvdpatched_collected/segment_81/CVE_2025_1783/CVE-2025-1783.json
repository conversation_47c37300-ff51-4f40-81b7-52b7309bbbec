{"cve_id": "CVE-2025-1783", "published_date": "2025-03-08T10:15:11.647", "last_modified_date": "2025-03-24T18:10:03.760", "descriptions": [{"lang": "en", "value": "The Gallery Styles plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Gallery Block in all versions up to, and including, 1.3.4 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Gallery Styles para WordPress es vulnerable a cross site scripting almacenado a través del bloque Gallery en todas las versiones hasta la 1.3.4 incluida, debido a una depuración de entrada y al escape de salida insuficiente. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/gallery-styles/tags/1.3.4/gallery-styles.php#L34", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3251908/gallery-styles/trunk/gallery-styles.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c9443e36-648c-4984-8b06-28e9da959e26?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
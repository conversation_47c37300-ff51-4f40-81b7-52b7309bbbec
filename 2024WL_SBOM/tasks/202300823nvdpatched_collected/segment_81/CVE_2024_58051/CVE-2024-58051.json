{"cve_id": "CVE-2024-58051", "published_date": "2025-03-06T16:15:51.247", "last_modified_date": "2025-03-13T13:15:45.027", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nipmi: ipmb: Add check devm_kasprintf() returned value\n\ndevm_kasprintf() can return a NULL pointer on failure but this\nreturned value is not checked."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: ipmi: ipmb: Agregar verificación del valor devuelto por devm_kasprintf() devm_kasprintf() puede devolver un puntero NULL en caso de error, pero este valor devuelto no se verifica."}], "references": [{"url": "https://git.kernel.org/stable/c/1a8a17c5ce9cb5a82797602bff9819ac732d2ff5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/2378bd0b264ad3a1f76bd957caf33ee0c7945351", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/312a6445036d692bc5665307eeafa4508c33c4b5", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/4c9caf86d04dcb10e9fd8cd9db8eb79b5bfcc4d8", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/a63284d415d4d114abd8be6e66a9558f3ca0702d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/caac520350546e736894d14e051b64a9edb3600c", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/e529fbcf1f35f5fc3c839df7f06c3e3d02579715", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb288ab33fd87579789cb331209ff09e988ff4f7", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
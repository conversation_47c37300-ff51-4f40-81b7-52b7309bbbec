{"cve_id": "CVE-2025-2031", "published_date": "2025-03-06T16:15:55.727", "last_modified_date": "2025-05-12T20:56:38.933", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in ChestnutCMS up to 1.5.2. This affects the function uploadFile of the file /dev-api/cms/file/upload. The manipulation of the argument file leads to unrestricted upload. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en ChestnutCMS hasta la versión 1.5.2. Afecta a la función uploadFile del archivo /dev-api/cms/file/upload. La manipulación del argumento file provoca una carga sin restricciones. Es posible iniciar el ataque de forma remota. El exploit ha sido divulgado al público y puede ser utilizado."}], "references": [{"url": "https://github.com/IceFoxH/VULN/issues/6", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.298773", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298773", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.512029", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
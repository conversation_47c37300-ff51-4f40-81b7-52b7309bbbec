{"cve_id": "CVE-2024-58067", "published_date": "2025-03-06T16:15:53.150", "last_modified_date": "2025-03-25T14:44:00.237", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nclk: mmp: pxa1908-mpmu: Fix a NULL vs IS_ERR() check\n\nThe devm_kzalloc() function returns NULL on error, not error pointers.\nUpdate the check to match."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: clk: mmp: pxa1908-mpmu: Se corrige una comprobación de NULL frente a IS_ERR() La función devm_kzalloc() devuelve NULL en caso de error, no punteros de error. Actualice la comprobación para que coincida."}], "references": [{"url": "https://git.kernel.org/stable/c/0869a7b2afdfcdd2beb0a5fb683119bcf39c0e9d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}, {"url": "https://git.kernel.org/stable/c/7def56f841af22e07977e193eea002e085facbdb", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": ["Patch"]}]}
{"cve_id": "CVE-2025-27623", "published_date": "2025-03-05T23:15:14.097", "last_modified_date": "2025-06-24T00:46:38.817", "descriptions": [{"lang": "en", "value": "Jenkins 2.499 and earlier, LTS 2.492.1 and earlier does not redact encrypted values of secrets when accessing `config.xml` of views via REST API or CLI, allowing attackers with View/Read permission to view encrypted values of secrets."}, {"lang": "es", "value": "Jenkins 2.499 y anteriores, LTS 2.492.1 y anteriores no redactan los valores cifrados de los secretos al acceder a `config.xml` de las vistas a través de la API REST o CLI, lo que permite a los atacantes con permiso de Ver/Leer ver los valores cifrados de los secretos."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-03-05/#SECURITY-3496", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}
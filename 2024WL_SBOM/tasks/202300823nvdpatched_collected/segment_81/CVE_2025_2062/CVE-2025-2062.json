{"cve_id": "CVE-2025-2062", "published_date": "2025-03-07T04:15:08.933", "last_modified_date": "2025-05-14T16:15:19.497", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in projectworlds Life Insurance Management System 1.0. Affected is an unknown function of the file /clientStatus.php. The manipulation of the argument client_id leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en projectworlds Life Insurance Management System 1.0. Se ve afectada una función desconocida del archivo /clientStatus.php. La manipulación del argumento client_id provoca una inyección SQL. Es posible lanzar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/akjedfha/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.298818", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298818", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514722", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
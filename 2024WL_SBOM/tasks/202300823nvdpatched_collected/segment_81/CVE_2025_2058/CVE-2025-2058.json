{"cve_id": "CVE-2025-2058", "published_date": "2025-03-07T02:15:38.413", "last_modified_date": "2025-05-08T18:49:08.910", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Emergency Ambulance Hiring Portal 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /admin/search.php. The manipulation of the argument searchdata leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Emergency Ambulance Hiring Portal 1.0 y se ha clasificado como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/search.php. La manipulación del argumento searchdata conduce a una inyección SQL. El ataque se puede ejecutar de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/12T40910/CVE/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298813", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298813", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.514462", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
{"cve_id": "CVE-2024-12809", "published_date": "2025-03-07T07:15:21.380", "last_modified_date": "2025-03-07T07:15:21.380", "descriptions": [{"lang": "en", "value": "The Wishlist plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'wishlist_button' shortcode in all versions up to, and including, 1.0.43 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Wishlist para WordPress es vulnerable a cross site scripting almacenados a través del código abreviado 'wishlist_button' del complemento en todas las versiones hasta la 1.0.43 incluida, debido a una depuración de entrada y al escape de salida insuficiente en los atributos proporcionados por el usuario. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en las páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. "}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wishlist/trunk/includes/classes/class-shortcodes.php?rev=3215801#L223", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3242533%40wishlist&new=3242533%40wishlist&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3251476%40wishlist&new=3251476%40wishlist&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1b902d46-ff27-486f-836d-f55a8048f08c?source=cve", "source": "<EMAIL>", "tags": []}]}
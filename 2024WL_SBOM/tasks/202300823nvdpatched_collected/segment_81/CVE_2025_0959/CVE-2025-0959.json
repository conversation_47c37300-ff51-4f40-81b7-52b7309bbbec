{"cve_id": "CVE-2025-0959", "published_date": "2025-03-07T09:15:16.153", "last_modified_date": "2025-03-13T14:59:44.297", "descriptions": [{"lang": "en", "value": "The Eventer - WordPress Event & Booking Manager Plugin plugin for WordPress is vulnerable to SQL Injection via the reg_id parameter in all versions up to, and including, ******* due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Eventer - WordPress Event &amp; Booking Manager Plugin para WordPress es vulnerable a la inyección SQL a través del parámetro reg_id en todas las versiones hasta la ******* incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel de suscriptor y superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/eventer-wordpress-event-manager-plugin/20972534", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/bd42c89e-57db-458f-910c-404a5615f280?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
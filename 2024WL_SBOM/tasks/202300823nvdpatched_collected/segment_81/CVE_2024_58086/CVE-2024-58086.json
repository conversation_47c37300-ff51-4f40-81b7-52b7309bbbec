{"cve_id": "CVE-2024-58086", "published_date": "2025-03-06T17:15:22.117", "last_modified_date": "2025-03-13T13:15:46.490", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\ndrm/v3d: Stop active perfmon if it is being destroyed\n\nIf the active performance monitor (`v3d->active_perfmon`) is being\ndestroyed, stop it first. Currently, the active perfmon is not\nstopped during destruction, leaving the `v3d->active_perfmon` pointer\nstale. This can lead to undefined behavior and instability.\n\nThis patch ensures that the active perfmon is stopped before being\ndestroyed, aligning with the behavior introduced in commit\n7d1fd3638ee3 (\"drm/v3d: Stop the active perfmon before being destroyed\")."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: drm/v3d: Detener el monitor de rendimiento activo si se está destruyendo Si se está destruyendo el monitor de rendimiento activo (`v3d-&gt;active_perfmon`), de<PERSON><PERSON>lo primero. Actualmente, el monitor de rendimiento activo no se detiene durante la destrucción, lo que deja obsoleto el puntero `v3d-&gt;active_perfmon`. Esto puede provocar un comportamiento indefinido e inestabilidad. Este parche garantiza que el monitor de rendimiento activo se detenga antes de destruirse, en consonancia con el comportamiento introducido en el commit 7d1fd3638ee3 (\"drm/v3d: Detener el monitor de rendimiento activo antes de destruirse\")."}], "references": [{"url": "https://git.kernel.org/stable/c/1c5673a2c8926adbb61f340c779b28e18188a8cd", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/21f1435b1e6b012a07c42f36b206d2b66fc8f13b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/22e19c8c5f6b709f4ae40227392a30d57bac187d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/95036d4c01167568166108d42c2b0e9f8dbd7d2b", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/eb0e0eca0eab93f310c6c37b8564049366704691", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/f8805b12f477bd964e2820a87921c7b58cc2dee3", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
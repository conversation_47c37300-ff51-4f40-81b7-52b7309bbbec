{"cve_id": "CVE-2025-25748", "published_date": "2025-03-11T18:15:32.810", "last_modified_date": "2025-03-21T21:15:36.553", "descriptions": [{"lang": "en", "value": "A CSRF vulnerability in the gestione_utenti.php endpoint of HotelDruid 3.0.7 allows attackers to perform unauthorized actions (e.g., modifying user passwords) on behalf of authenticated users by exploiting the lack of origin or referrer validation and the absence of CSRF tokens. NOTE: this is disputed because there is an id_sessione CSRF token."}, {"lang": "es", "value": "Una vulnerabilidad CSRF en el endpoint gestione_utenti.php de HotelDruid 3.0.7 permite a los atacantes realizar acciones no autorizadas (por ejemplo, modificar las contraseñas de los usuarios) en nombre de usuarios autenticados explotando la falta de validación del origen o del referente y la ausencia de tokens CSRF."}], "references": [{"url": "https://www.huyvo.net/post/cve-2025-25748-cross-site-request-forgery-csrf-vulnerability-in-hoteldruid-3-0-7", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-2093", "published_date": "2025-03-07T22:15:38.680", "last_modified_date": "2025-04-03T13:33:14.790", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Library Management System 3.0. It has been declared as problematic. Affected by this vulnerability is an unknown functionality of the file /change-password.php. The manipulation of the argument email/phone number leads to weak password recovery. The attack can be launched remotely. The complexity of an attack is rather high. The exploitation appears to be difficult. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Online Library Management System 3.0. Se ha declarado como problemática. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /change-password.php. La manipulación del argumento email/phone number conduce a una recuperación de contraseña débil. El ataque se puede lanzar de forma remota. Es un ataque de complejidad bastante alta. Parece difícil de explotar. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/SECWG/cve/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.298951", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.298951", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.515207", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}
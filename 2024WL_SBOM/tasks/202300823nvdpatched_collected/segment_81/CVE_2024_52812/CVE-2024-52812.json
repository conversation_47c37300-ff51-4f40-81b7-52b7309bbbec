{"cve_id": "CVE-2024-52812", "published_date": "2025-03-10T18:15:29.207", "last_modified_date": "2025-03-10T18:15:29.207", "descriptions": [{"lang": "en", "value": "LF Edge eKuiper is an internet-of-things data analytics and stream processing engine. Prior to version 2.0.8, auser with rights to modify the service (e.g. kuiperUser role) can inject a cross-site scripting payload into the rule `id` parameter. Then, after any user with access to this service (e.g. admin) tries make any modifications with the rule (update, run, stop, delete), a payload acts in the victim's browser. Version 2.0.8 fixes the issue."}, {"lang": "es", "value": "LF Edge eKuiper es un motor de procesamiento de flujo y análisis de datos de Internet de las cosas. Antes de la versión 2.0.8, un usuario con derechos para modificar el servicio (por ejemplo, el rol kuiperUser) puede inyectar un payload de cross-site scripting en el parámetro `id` de la regla. <PERSON><PERSON>, después de que cualquier usuario con acceso a este servicio (por ejemplo, administrador) intente realizar modificaciones con la regla (actualizar, ejecutar, detener, eliminar), un payload actúa en el navegador de la víctima. La versión 2.0.8 soluciona el problema."}], "references": [{"url": "https://github.com/lf-edge/ekuiper/blob/dbce32d5a195cf1de949b3a6a4e29f0df0f3330d/internal/server/rest.go#L681", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/blob/dbce32d5a195cf1de949b3a6a4e29f0df0f3330d/internal/server/rest.go#L716", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/blob/dbce32d5a195cf1de949b3a6a4e29f0df0f3330d/internal/server/rest.go#L735", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/blob/dbce32d5a195cf1de949b3a6a4e29f0df0f3330d/internal/server/rest.go#L794", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/blob/dbce32d5a195cf1de949b3a6a4e29f0df0f3330d/internal/server/rest.go#L809", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/blob/dbce32d5a195cf1de949b3a6a4e29f0df0f3330d/internal/server/rest.go#L824", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/releases/tag/v2.0.8", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/lf-edge/ekuiper/security/advisories/GHSA-6hrw-x7pr-4mp8", "source": "<EMAIL>", "tags": []}]}
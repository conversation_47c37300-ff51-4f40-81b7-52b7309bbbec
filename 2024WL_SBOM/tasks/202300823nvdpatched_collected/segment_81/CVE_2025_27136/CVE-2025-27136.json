{"cve_id": "CVE-2025-27136", "published_date": "2025-03-10T19:15:40.770", "last_modified_date": "2025-03-10T19:15:40.770", "descriptions": [{"lang": "en", "value": "LocalS3 is an Amazon S3 mock service for testing and local development. Prior to version 1.21, the LocalS3 service's bucket creation endpoint is vulnerable to XML External Entity (XXE) injection. When processing the CreateBucketConfiguration XML document during bucket creation, the service's XML parser is configured to resolve external entities. This allows an attacker to declare an external entity that references an internal URL, which the server will then attempt to fetch when parsing the XML. The vulnerability specifically occurs in the location constraint processing, where the XML parser resolves external entities without proper validation or restrictions. When the external entity is resolved, the server makes an HTTP request to the specified URL and includes the response content in the parsed XML document. This vulnerability can be exploited to perform server-side request forgery (SSRF) attacks, allowing an attacker to make requests to internal services or resources that should not be accessible from external networks. The server will include the responses from these internal requests in the resulting bucket configuration, effectively leaking sensitive information. The attacker only needs to be able to send HTTP requests to the LocalS3 service to exploit this vulnerability."}, {"lang": "es", "value": "LocalS3 es un servicio simulado de Amazon S3 para pruebas y desarrollo local. Antes de la versión 1.21, el endpoint de creación de buckets del servicio LocalS3 es vulnerable a la inyección de entidades externas XML (XXE). Al procesar el documento XML CreateBucketConfiguration durante la creación de buckets, el analizador XML del servicio se configura para resolver entidades externas. Esto permite que un atacante declare una entidad externa que hace referencia a una URL interna, que el servidor intentará obtener al analizar el XML. La vulnerabilidad se produce específicamente en el procesamiento de restricciones de ubicación, donde el analizador XML resuelve entidades externas sin la validación o las restricciones adecuadas. Cuando se resuelve la entidad externa, el servidor realiza una solicitud HTTP a la URL especificada e incluye el contenido de la respuesta en el documento XML analizado. Esta vulnerabilidad se puede explotar para realizar ataques de server-side request forgery (SSRF), lo que permite que un atacante realice solicitudes a servicios o recursos internos que no deberían ser accesibles desde redes externas. El servidor incluirá las respuestas de estas solicitudes internas en la configuración de bucket resultante, lo que filtra información confidencial de manera efectiva. El atacante sólo necesita poder enviar solicitudes HTTP al servicio LocalS3 para explotar esta vulnerabilidad."}], "references": [{"url": "https://github.com/Robothy/local-s3/commit/d6ed756ceb30c1eb9d4263321ac683d734f8836f", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Robothy/local-s3/security/advisories/GHSA-g6wm-2v64-wq36", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-22603", "published_date": "2025-03-10T19:15:39.970", "last_modified_date": "2025-03-10T19:15:39.970", "descriptions": [{"lang": "en", "value": "AutoGPT is a platform that allows users to create, deploy, and manage continuous artificial intelligence agents that automate complex workflows. Versions prior to autogpt-platform-beta-v0.4.2 contains a server-side request forgery (SSRF) vulnerability inside component (or block) `Send Web Request`. The root cause is  that IPV6 address is not restricted or filtered, which allows attackers to perform a server side request forgery to visit an IPV6 service. autogpt-platform-beta-v0.4.2 fixes the issue."}, {"lang": "es", "value": "AutoGPT es una plataforma que permite a los usuarios crear, implementar y administrar agentes de inteligencia artificial continuos que automatizan flujos de trabajo complejos. Las versiones anteriores a autogpt-platform-beta-v0.4.2 contienen una vulnerabilidad de server-side request forgery (SSRF) dentro del componente (o bloque) `Send Web Request`. La causa principal es que la dirección IPV6 no está restringida ni filtrada, lo que permite a los atacantes realizar server side request forgery para visitar un servicio IPV6. autogpt-platform-beta-v0.4.2 soluciona el problema."}], "references": [{"url": "https://boatneck-faucet-cba.notion.site/SSRF-of-AutoGPT-153b650a4d88804d923ad65a015a7d61", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Significant-Gravitas/AutoGPT/blob/2121ffd06b26a438706bf642372cc46d81c94ddc/autogpt_platform/backend/backend/util/request.py#L11", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Significant-Gravitas/AutoGPT/commit/26214e1b2c6777e0fae866642b23420adaadd6c4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Significant-Gravitas/AutoGPT/security/advisories/GHSA-4c8v-hwxc-2356", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2025-27789", "published_date": "2025-03-11T20:15:18.330", "last_modified_date": "2025-03-11T20:15:18.330", "descriptions": [{"lang": "en", "value": "<PERSON>l is a compiler for writing next generation JavaScript. When using versions of Babel prior to 7.26.10 and 8.0.0-alpha.17 to compile regular expression named capturing groups, <PERSON><PERSON> will generate a polyfill for the `.replace` method that has quadratic complexity on some specific replacement pattern strings (i.e. the second argument passed to `.replace`). Generated code is vulnerable if all the following conditions are true: Using Babel to compile regular expression named capturing groups, using the `.replace` method on a regular expression that contains named capturing groups, and the code using untrusted strings as the second argument of `.replace`. This problem has been fixed in `@babel/helpers` and `@babel/runtime` 7.26.10 and 8.0.0-alpha.17. It's likely that individual users do not directly depend on `@babel/helpers`, and instead depend on `@babel/core` (which itself depends on `@babel/helpers`). Upgrading to `@babel/core` 7.26.10 is not required, but it guarantees use of a new enough `@babel/helpers` version. Note that just updating Babel dependencies is not enough; one will also need to re-compile the code. No known workarounds are available."}, {"lang": "es", "value": "Babel es un compilador para escribir JavaScript de nueva generación. Al usar versiones de Babel anteriores a la 7.26.10 y 8.0.0-alpha.17 para compilar grupos de captura con nombre de expresiones regulares, Babel generará un polyfill para el método `.replace` con complejidad cuadrática en algunas cadenas de patrones de reemplazo específicas (es decir, el segundo argumento pasado a `.replace`). El código generado es vulnerable si se cumplen todas las siguientes condiciones: usar Babel para compilar grupos de captura con nombre de expresiones regulares, usar el método `.replace` en una expresión regular que contenga grupos de captura con nombre y usar cadenas no confiables como segundo argumento de `.replace`. Este problema se ha corregido en `@babel/helpers` y `@babel/runtime` 7.26.10 y 8.0.0-alpha.17. Es probable que los usuarios individuales no dependan directamente de `@babel/helpers`, sino de `@babel/core` (que a su vez depende de `@babel/helpers`). No es necesario actualizar a `@babel/core` 7.26.10, pero garantiza el uso de una versión suficientemente nueva de `@babel/helpers`. Tenga en cuenta que actualizar las dependencias de Babel no es suficiente; también será necesario recompilar el código. No se conocen workarounds."}], "references": [{"url": "https://github.com/babel/babel/pull/17173", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/babel/babel/security/advisories/GHSA-968p-4wvh-cqc8", "source": "<EMAIL>", "tags": []}]}
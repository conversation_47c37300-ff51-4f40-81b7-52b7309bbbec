{"cve_id": "CVE-2025-27912", "published_date": "2025-03-11T08:15:11.727", "last_modified_date": "2025-03-11T08:15:11.727", "descriptions": [{"lang": "en", "value": "An issue was discovered in Datalust Seq before 2024.3.13545. Missing Content-Type validation can lead to CSRF when (1) Entra ID or OpenID Connect authentication is in use and a user visits a compromised/malicious site, or (2) when username/password or Active Directory authentication is in use and a user visits a compromised/malicious site under the same effective top-level domain as the Seq server. Exploitation of the vulnerability allows the attacker to conduct impersonation attacks and perform actions in Seq on behalf of the targeted user."}, {"lang": "es", "value": "Se descubrió un problema en Datalust Seq antes de 2024.3.13545. La falta de validación de tipo de contenido puede generar CSRF cuando (1) se utiliza la autenticación de Entra ID o OpenID Connect y un usuario visita un sitio comprometido o malicioso, o (2) cuando se utiliza la autenticación de nombre de usuario/contraseña o Active Directory y un usuario visita un sitio comprometido o malicioso bajo el mismo dominio de nivel superior efectivo que el servidor Seq. La explotación de la vulnerabilidad permite al atacante realizar ataques de suplantación de identidad y realizar acciones en Seq en nombre del usuario objetivo."}], "references": [{"url": "https://datalust.co/seq", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/datalust/seq-tickets/issues/2366", "source": "<EMAIL>", "tags": []}]}
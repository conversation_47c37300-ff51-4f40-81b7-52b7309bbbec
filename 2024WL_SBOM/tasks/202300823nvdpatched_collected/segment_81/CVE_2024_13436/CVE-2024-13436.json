{"cve_id": "CVE-2024-13436", "published_date": "2025-03-11T04:15:24.357", "last_modified_date": "2025-03-11T04:15:24.357", "descriptions": [{"lang": "en", "value": "The Appsero Helper plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.3.2. This is due to missing or incorrect nonce validation on the 'appsero_helper' page. This makes it possible for unauthenticated attackers to update settings and inject malicious web scripts via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Appsero Helper para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.3.2 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la página 'appsero_helper'. Esto permite que atacantes no autenticados actualicen configuraciones e inyecten scripts web maliciosos a través de una solicitud falsificada, siempre que puedan engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3253119%40appsero-helper&new=3253119%40appsero-helper&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c0d0c31e-1641-48e6-bd3e-47d8afb1b3b8?source=cve", "source": "<EMAIL>", "tags": []}]}
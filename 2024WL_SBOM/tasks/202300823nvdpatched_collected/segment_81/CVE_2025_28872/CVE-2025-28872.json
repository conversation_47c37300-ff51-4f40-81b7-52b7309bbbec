{"cve_id": "CVE-2025-28872", "published_date": "2025-03-11T21:15:44.757", "last_modified_date": "2025-04-09T14:04:59.747", "descriptions": [{"lang": "en", "value": "Missing Authorization vulnerability in jwpegram Block Spam By Math Reloaded allows Accessing Functionality Not Properly Constrained by ACLs. This issue affects Block Spam By Math Reloaded: from n/a through 2.2.4."}, {"lang": "es", "value": "La vulnerabilidad de falta de autorización en jwpegram Block Spam By Math Reloaded permite acceder a funcionalidades no restringidas correctamente por las ACL. Este problema afecta a \"Block Spam By Math Reloaded\" desde n/d hasta la versión 2.2.4."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/block-spam-by-math-reloaded/vulnerability/wordpress-block-spam-by-math-reloaded-plugin-2-2-4-broken-access-control-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
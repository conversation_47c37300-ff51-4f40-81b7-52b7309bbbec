{"cve_id": "CVE-2025-21825", "published_date": "2025-03-06T16:15:54.753", "last_modified_date": "2025-03-06T16:15:54.753", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nbpf: Cancel the running bpf_timer through kworker for PREEMPT_RT\n\nDuring the update procedure, when overwrite element in a pre-allocated\nhtab, the freeing of old_element is protected by the bucket lock. The\nreason why the bucket lock is necessary is that the old_element has\nalready been stashed in htab->extra_elems after alloc_htab_elem()\nreturns. If freeing the old_element after the bucket lock is unlocked,\nthe stashed element may be reused by concurrent update procedure and the\nfreeing of old_element will run concurrently with the reuse of the\nold_element. However, the invocation of check_and_free_fields() may\nacquire a spin-lock which violates the lockdep rule because its caller\nhas already held a raw-spin-lock (bucket lock). The following warning\nwill be reported when such race happens:\n\n  BUG: scheduling while atomic: test_progs/676/0x00000003\n  3 locks held by test_progs/676:\n  #0: ffffffff864b0240 (rcu_read_lock_trace){....}-{0:0}, at: bpf_prog_test_run_syscall+0x2c0/0x830\n  #1: ffff88810e961188 (&htab->lockdep_key){....}-{2:2}, at: htab_map_update_elem+0x306/0x1500\n  #2: ffff8881f4eac1b8 (&base->softirq_expiry_lock){....}-{2:2}, at: hrtimer_cancel_wait_running+0xe9/0x1b0\n  Modules linked in: bpf_testmod(O)\n  Preemption disabled at:\n  [<ffffffff817837a3>] htab_map_update_elem+0x293/0x1500\n  CPU: 0 UID: 0 PID: 676 Comm: test_progs Tainted: G ... 6.12.0+ #11\n  Tainted: [W]=WARN, [O]=OOT_MODULE\n  Hardware name: QEMU Standard PC (i440FX + PIIX, 1996)...\n  Call Trace:\n  <TASK>\n  dump_stack_lvl+0x57/0x70\n  dump_stack+0x10/0x20\n  __schedule_bug+0x120/0x170\n  __schedule+0x300c/0x4800\n  schedule_rtlock+0x37/0x60\n  rtlock_slowlock_locked+0x6d9/0x54c0\n  rt_spin_lock+0x168/0x230\n  hrtimer_cancel_wait_running+0xe9/0x1b0\n  hrtimer_cancel+0x24/0x30\n  bpf_timer_delete_work+0x1d/0x40\n  bpf_timer_cancel_and_free+0x5e/0x80\n  bpf_obj_free_fields+0x262/0x4a0\n  check_and_free_fields+0x1d0/0x280\n  htab_map_update_elem+0x7fc/0x1500\n  bpf_prog_9f90bc20768e0cb9_overwrite_cb+0x3f/0x43\n  bpf_prog_ea601c4649694dbd_overwrite_timer+0x5d/0x7e\n  bpf_prog_test_run_syscall+0x322/0x830\n  __sys_bpf+0x135d/0x3ca0\n  __x64_sys_bpf+0x75/0xb0\n  x64_sys_call+0x1b5/0xa10\n  do_syscall_64+0x3b/0xc0\n  entry_SYSCALL_64_after_hwframe+0x4b/0x53\n  ...\n  </TASK>\n\nIt seems feasible to break the reuse and refill of per-cpu extra_elems\ninto two independent parts: reuse the per-cpu extra_elems with bucket\nlock being held and refill the old_element as per-cpu extra_elems after\nthe bucket lock is unlocked. However, it will make the concurrent\noverwrite procedures on the same CPU return unexpected -E2BIG error when\nthe map is full.\n\nTherefore, the patch fixes the lock problem by breaking the cancelling\nof bpf_timer into two steps for PREEMPT_RT:\n1) use hrtimer_try_to_cancel() and check its return value\n2) if the timer is running, use hrtimer_cancel() through a kworker to\n   cancel it again\nConsidering that the current implementation of hrtimer_cancel() will try\nto acquire a being held softirq_expiry_lock when the current timer is\nrunning, these steps above are reasonable. However, it also has\ndownside. When the timer is running, the cancelling of the timer is\ndelayed when releasing the last map uref. The delay is also fixable\n(e.g., break the cancelling of bpf timer into two parts: one part in\nlocked scope, another one in unlocked scope), it can be revised later if\nnecessary.\n\nIt is a bit hard to decide the right fix tag. One reason is that the\nproblem depends on PREEMPT_RT which is enabled in v6.12. Considering the\nsoftirq_expiry_lock lock exists since v5.4 and bpf_timer is introduced\nin v5.15, the bpf_timer commit is used in the fixes tag and an extra\ndepends-on tag is added to state the dependency on PREEMPT_RT.\n\nDepends-on: v6.12+ with PREEMPT_RT enabled"}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: bpf: Cancelar la ejecución de bpf_timer a través de kworker para PREEMPT_RT Durante el procedimiento de actualización, cuando se sobrescribe un elemento en un htab preasignado, la liberación de old_element está protegida por el bloqueo del depósito. La razón por la que el bloqueo del depósito es necesario es que old_element ya se ha almacenado en htab-&gt;extra_elems después de que alloc_htab_elem() regrese. Si se libera old_element después de que se desbloquea el bloqueo del depósito, el elemento almacenado puede reutilizarse mediante un procedimiento de actualización concurrente y la liberación de old_element se ejecutará simultáneamente con la reutilización de old_element. Sin embargo, la invocación de check_and_free_fields() puede adquirir un bloqueo de giro que viola la regla lockdep porque su llamador ya ha mantenido un bloqueo de giro sin procesar (bloqueo del depósito). Se informará la siguiente advertencia cuando ocurra dicha ejecución: UG: scheduling while atomic: test_progs/676/0x00000003 3 locks held by test_progs/676: #0: ffffffff864b0240 (rcu_read_lock_trace){....}-{0:0}, at: bpf_prog_test_run_syscall+0x2c0/0x830 #1: ffff88810e961188 (&amp;htab-&gt;lockdep_key){....}-{2:2}, at: htab_map_update_elem+0x306/0x1500 #2: ffff8881f4eac1b8 (&amp;base-&gt;softirq_expiry_lock){....}-{2:2}, at: hrtimer_cancel_wait_running+0xe9/0x1b0 Modules linked in: bpf_testmod(O) Preemption disabled at: [] htab_map_update_elem+0x293/0x1500 CPU: 0 UID: 0 PID: 676 Comm: test_progs Tainted: G ... 6.12.0+ #11 Tainted: [W]=WARN, [O]=OOT_MODULE Hardware name: QEMU Standard PC (i440FX + PIIX, 1996)... Call Trace:  dump_stack_lvl+0x57/0x70 dump_stack+0x10/0x20 __schedule_bug+0x120/0x170 __schedule+0x300c/0x4800 schedule_rtlock+0x37/0x60 rtlock_slowlock_locked+0x6d9/0x54c0 rt_spin_lock+0x168/0x230 hrtimer_cancel_wait_running+0xe9/0x1b0 hrtimer_cancel+0x24/0x30 bpf_timer_delete_work+0x1d/0x40 bpf_timer_cancel_and_free+0x5e/0x80 bpf_obj_free_fields+0x262/0x4a0 check_and_free_fields+0x1d0/0x280 htab_map_update_elem+0x7fc/0x1500 bpf_prog_9f90bc20768e0cb9_overwrite_cb+0x3f/0x43 bpf_prog_ea601c4649694dbd_overwrite_timer+0x5d/0x7e bpf_prog_test_run_syscall+0x322/0x830 __sys_bpf+0x135d/0x3ca0 __x64_sys_bpf+0x75/0xb0 x64_sys_call+0x1b5/0xa10 do_syscall_64+0x3b/0xc0 entry_SYSCALL_64_after_hwframe+0x4b/0x53 ...  Parece factible dividir la reutilización y el rellenado de extra_elems por CPU en dos partes independientes: reutilizar los extra_elems por CPU con el bloqueo del depósito mantenido y rellenar el old_element como extra_elems por CPU después de que se desbloquee el bloqueo del depósito. Sin embargo, hará que los procedimientos de sobrescritura concurrentes en la misma CPU devuelvan un error inesperado -E2BIG cuando el mapa esté lleno. Por lo tanto, el parche corrige el problema de bloqueo dividiendo la cancelación de bpf_timer en dos pasos para PREEMPT_RT: 1) use hrtimer_try_to_cancel() y verifique su valor de retorno 2) si el temporizador se está ejecutando, use hrtimer_cancel() a través de un kworker para cancelarlo nuevamente Considerando que la implementación actual de hrtimer_cancel() intentará adquirir un softirq_expiry_lock retenido cuando el temporizador actual se esté ejecutando, estos pasos anteriores son razonables. Sin embargo, también tiene desventajas. Cuando el temporizador se está ejecutando, la cancelación del temporizador se retrasa al liberar el último uref del mapa. El retraso también se puede corregir (por ejemplo, dividir la cancelación del temporizador bpf en dos partes: una parte en el ámbito bloqueado, otra en el ámbito desbloqueado), se puede revisar más tarde si es necesario. Es un poco difícil decidir la etiqueta de corrección correcta. Una razón es que el problema depende de PREEMPT_RT, que está habilitado en la versión v6.12. Teniendo en cuenta que el bloqueo softirq_expiry_lock existe desde la versión v5.4 y que bpf_timer se introdujo en la versión v5.15 --- truncado --- "}], "references": [{"url": "https://git.kernel.org/stable/c/33e47d9573075342a41783a55c8c67bc71246fc1", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/58f038e6d209d2dd862fcf5de55407855856794d", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/fbeda3d939ca10063aafa7a77cc0f409d82cda88", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
{"cve_id": "CVE-2025-26910", "published_date": "2025-03-10T15:15:37.660", "last_modified_date": "2025-06-27T17:39:43.517", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in Iqonic Design WPBookit allows Stored XSS. This issue affects WPBookit: from n/a through 1.0.1."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Request Forgery (CSRF) en Iqonic Design WPBookit permite XSS almacenado. Este problema afecta a WPBookit: desde n/a hasta 1.0.1."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/wpbookit/vulnerability/wordpress-wpbookit-plugin-1-0-1-cross-site-request-forgery-csrf-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
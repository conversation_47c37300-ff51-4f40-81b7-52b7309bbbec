{"cve_id": "CVE-2024-13924", "published_date": "2025-03-08T13:15:12.160", "last_modified_date": "2025-03-12T16:23:39.567", "descriptions": [{"lang": "en", "value": "The Starter Templates by FancyWP plugin for WordPress is vulnerable to Blind Server-Side Request Forgery in all versions up to, and including, 2.0.0 via the 'http_request_host_is_external' filter. This makes it possible for unauthenticated attackers to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Starter Templates de FancyWP para WordPress es vulnerable a Blind Server-Side Request Forgery en todas las versiones hasta la 2.0.0 incluida a través del filtro \"http_request_host_is_external\". Esto permite que atacantes no autenticados realicen solicitudes web a ubicaciones arbitrarias que se originan en la aplicación web y se pueden usar para consultar y modificar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/starter-templates/trunk/classess/class-export.php#L3", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9355b100-08a9-4640-a91b-e56ba1ab9b07?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
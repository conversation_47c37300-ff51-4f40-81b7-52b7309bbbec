{"cve_id": "CVE-2025-21828", "published_date": "2025-03-06T16:15:55.070", "last_modified_date": "2025-03-06T16:15:55.070", "descriptions": [{"lang": "en", "value": "In the Linux kernel, the following vulnerability has been resolved:\n\nwifi: mac80211: don't flush non-uploaded STAs\n\nIf STA state is pre-moved to AUTHORIZED (such as in IBSS\nscenarios) and insertion fails, the station is freed.\nIn this case, the driver never knew about the station,\nso trying to flush it is unexpected and may crash.\n\nCheck if the sta was uploaded to the driver before and\nfix this."}, {"lang": "es", "value": "En el kernel de Linux, se ha resuelto la siguiente vulnerabilidad: wifi: mac80211: no vacíe las STA no cargadas Si el estado de la STA se mueve previamente a AUTORIZADO (como en los escenarios de IBSS) y la inserción falla, la estación se libera. En este caso, el controlador nunca supo de la estación, por lo que intentar vaciarla es inesperado y puede bloquearse. Verifique si la estación se cargó al controlador antes y solucione este problema."}], "references": [{"url": "https://git.kernel.org/stable/c/9efb5531271fa7ebae993b2a33a705d9947c7ce6", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/aa3ce3f8fafa0b8fb062f28024855ea8cb3f3450", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cd10b7fcb95a6a86c67adc54304c59a578ab16af", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}, {"url": "https://git.kernel.org/stable/c/cf21ef3d430847ba864bbc9b2774fffcc03ce321", "source": "416baaa9-dc9f-4396-8d5f-8c081fb06d67", "tags": []}]}
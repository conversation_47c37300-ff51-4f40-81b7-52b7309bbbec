{"cve_id": "CVE-2025-28892", "published_date": "2025-03-11T21:15:46.570", "last_modified_date": "2025-03-11T21:15:46.570", "descriptions": [{"lang": "en", "value": "Cross-Site Request Forgery (CSRF) vulnerability in a2rocklobster FTP Sync allows Stored XSS. This issue affects FTP Sync: from n/a through 1.1.6."}, {"lang": "es", "value": "La vulnerabilidad de Cross-Site Request Forgery (CSRF) en a2rocklobster FTP Sync permite XSS almacenado. Este problema afecta a la sincronización FTP desde la versión n/d hasta la 1.1.6."}], "references": [{"url": "https://patchstack.com/database/wordpress/plugin/ftp-sync/vulnerability/wordpress-ftp-sync-plugin-1-1-6-csrf-to-stored-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}
{"cve_id": "CVE-2024-13844", "published_date": "2025-03-08T06:15:36.760", "last_modified_date": "2025-03-13T13:10:31.303", "descriptions": [{"lang": "en", "value": "The Post SMTP plugin for WordPress is vulnerable to generic SQL Injection via the ‘columns’ parameter in all versions up to, and including, 3.1.2 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query. This makes it possible for authenticated attackers, with Administrator-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Post SMTP para WordPress es vulnerable a la inyección SQL genérica a través del parámetro \"columns\" en todas las versiones hasta la 3.1.2 incluida, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación suficiente en la consulta SQL existente. Esto permite que los atacantes autenticados, con acceso de nivel de administrador o superior, agreguen consultas SQL adicionales a las consultas ya existentes que se pueden usar para extraer información confidencial de la base de datos."}], "references": [{"url": "https://gist.github.com/nhienit2010/d4692062f54b89e16aa068a0ef142cf6#file-postmanemailquerylog-php-L314", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3249371/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/post-smtp/#developers", "source": "<EMAIL>", "tags": ["Product", "Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0540f70d-009a-4776-8717-f096e30a11d3?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
{"cve_id": "CVE-2019-25222", "published_date": "2025-03-15T07:15:33.523", "last_modified_date": "2025-03-21T11:08:00.220", "descriptions": [{"lang": "en", "value": "The Thumbnail carousel slider plugin for WordPress is vulnerable to SQL Injection via the 'id' parameter in all versions up to, and including, 1.0.4 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Thumbnail carousel slider para WordPress es vulnerable a la inyección SQL a través del parámetro 'id' en todas las versiones hasta la 1.0.4 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas ya existentes, que pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wp-responsive-thumbnail-slider/tags/1.0.4/wp-responsive-images-thumbnail-slider.php#L1326", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/wp-responsive-thumbnail-slider/tags/1.0.5/wp-responsive-images-thumbnail-slider.php", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://wordpress.org/plugins/wp-responsive-thumbnail-slider", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f6023483-3fa5-4b85-9422-7d395abcfbd8?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}
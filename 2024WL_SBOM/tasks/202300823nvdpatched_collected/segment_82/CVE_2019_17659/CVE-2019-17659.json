{"cve_id": "CVE-2019-17659", "published_date": "2025-03-17T14:15:16.360", "last_modified_date": "2025-07-15T16:48:48.100", "descriptions": [{"lang": "en", "value": "A use of hard-coded cryptographic key vulnerability in FortiSIEM version 5.2.6 may allow a remote unauthenticated attacker to obtain SSH access to the supervisor as the restricted user \"tunneluser\" by leveraging knowledge of the private key from another installation or a firmware image."}, {"lang": "es", "value": "Una vulnerabilidad en el uso de una clave criptográfica codificada en FortiSIEM versión 5.2.6 puede permitir que un atacante remoto no autenticado obtenga acceso SSH al supervisor como el usuario restringido \"tunneluser\" aprovechando el conocimiento de la clave privada de otra instalación o una imagen de firmware."}], "references": [{"url": "https://fortiguard.fortinet.com/psirt/FG-IR-19-296", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
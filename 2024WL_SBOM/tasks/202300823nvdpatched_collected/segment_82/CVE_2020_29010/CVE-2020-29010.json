{"cve_id": "CVE-2020-29010", "published_date": "2025-03-17T14:15:16.730", "last_modified_date": "2025-07-24T20:15:46.223", "descriptions": [{"lang": "en", "value": "An exposure of sensitive information to an unauthorized actor vulnerability in FortiOS version 6.2.4 and below, version 6.0.10 and belowmay allow remote authenticated actors to read the SSL VPN events log entries of users in other VDOMs by  executing \"get vpn ssl monitor\" from the CLI. The sensitive data includes usernames, user groups, and IP address."}, {"lang": "es", "value": "La exposición de información confidencial a una vulnerabilidad de un agente no autorizado en FortiOS versión 6.2.4 y anteriores, y versión 6.0.10 y anteriores, podría permitir que agentes autenticados remotamente lean las entradas del registro de eventos de SSL VPN de usuarios en otros VDOM mediante la ejecución de \"get vpn ssl monitor\" desde la CLI. Los datos confidenciales incluyen nombres de usuario, grupos de usuarios y direcciones IP."}], "references": [{"url": "https://fortiguard.fortinet.com/psirt/FG-IR-20-103", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}
{"cve_id": "CVE-2020-36843", "published_date": "2025-03-13T06:15:34.043", "last_modified_date": "2025-03-13T06:15:34.043", "descriptions": [{"lang": "en", "value": "The implementation of EdDSA in EdDSA-Java (aka ed25519-java) through 0.3.0 exhibits signature malleability and does not satisfy the SUF-CMA (Strong Existential Unforgeability under Chosen Message Attacks) property. This allows attackers to create new valid signatures different from previous signatures for a known message."}, {"lang": "es", "value": "La implementación de EdDSA en EdDSA-Java (también conocido como ed25519-java) hasta la versión 0.3.0 presenta maleabilidad de firmas y no cumple con la propiedad SUF-CMA (Fuerte Infalsificación Existencial ante Ataques de Mensajes Elegidos). Esto permite a los atacantes crear nuevas firmas válidas, diferentes de las firmas previas, para un mensaje conocido."}], "references": [{"url": "https://eprint.iacr.org/2020/1244", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/str4d/ed25519-java/issues/82#issue-727629226", "source": "<EMAIL>", "tags": []}]}